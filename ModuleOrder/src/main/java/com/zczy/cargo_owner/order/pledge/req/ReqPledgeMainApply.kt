package com.zczy.cargo_owner.order.pledge.req

import com.zczy.comm.http.entity.BaseNewRequest
import com.zczy.comm.http.entity.BaseRsp
import com.zczy.comm.http.entity.ResultData

/**
 *  user: ssp
 *  time: 2020/6/18 9:54
 *  desc: 押金索要申请
 */
class ReqPledgeMainApply(
    var orderId: String = "", //订单id 多个订单用 , , ,分隔开
    var claimReason: String = "", //申请理由
    var consignorMoney: String = "", //索要金额
) : BaseNewRequest<BaseRsp<ResultData>>("oms-app/consignor/deposit/depositApply")

fun ReqPledgeMainApply.getOrderIdStr(list: MutableList<RspPledgeMain>): String {
    var string = ""
    list.forEachIndexed { index, rspPledgeMain ->
        string += if (index == list.size - 1) {
            rspPledgeMain.orderId
        } else {
            rspPledgeMain.orderId + ","
        }
    }
    return string
}