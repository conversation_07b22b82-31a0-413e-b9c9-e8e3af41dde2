package com.zczy.cargo_owner.order.express.req

import com.zczy.comm.http.entity.BaseNewRequest
import com.zczy.comm.http.entity.BaseRsp
import com.zczy.comm.http.entity.ResultData

/**
 *  user: ssp
 *  time: 2020/11/3 14:55
 *  desc: 回单签收 原因
 */
class ReqOrderExpressReason(
        var signReason: String? = null,
        var orderId: String? = "",
        var orderIds: String? = ""
) : BaseNewRequest<BaseRsp<ResultData>>("oms-app/order/express/consignorBeforeSignExpress") {

}

data class RxBusDoSign(var success: Boolean = false)
