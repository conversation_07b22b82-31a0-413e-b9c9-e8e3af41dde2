package com.zczy.cargo_owner.order.reminder.widget

import android.content.Context
import android.graphics.Color
import androidx.constraintlayout.widget.ConstraintLayout
import android.util.AttributeSet
import android.util.Log
import android.view.LayoutInflater
import com.zczy.cargo_owner.order.R
import com.zczy.comm.utils.ex.YYYY_MM_DD
import com.zczy.comm.utils.ex.YYYY_MM_DD_HH_MM
import com.zczy.comm.utils.ex.getFormatTime
import com.zczy.comm.utils.ex.toCalendar
import com.zczy.comm.utils.json.toJson
import com.zczy.comm.widget.pickerview.StartEndTopBar
import com.zczy.comm.widget.pickerview.picker.BasePicker
import com.zczy.comm.widget.pickerview.picker.TimePicker
import com.zczy.comm.widget.pickerview.widget.DefaultCenterDecoration
import com.zczy.comm.widget.pickerview.widget.PickerView
import kotlinx.android.synthetic.main.order_returned_confirm_filter_time_layout.view.*
import java.util.*

/**
 *  user: ssp
 *  time: 2021/7/15 15:35
 *  desc: 时间选择
 */

class TransportReminderFilterTimeView :
    ConstraintLayout {

    var listener: Listener? = null

    constructor(context: Context) : super(context)

    constructor(context: Context, attrs: AttributeSet?) : super(context, attrs)

    constructor(context: Context, attrs: AttributeSet?, defStyleAttr: Int) : super(
        context,
        attrs,
        defStyleAttr
    )

    init {
        setBackgroundColor(Color.WHITE)
        LayoutInflater.from(context)
            .inflate(R.layout.transport_reminder_filter_time_layout, this)
        initView()
    }

    private fun initView() {

        setOnClickListener {
            val startTime = tvStartTime.text.toString().toCalendar(YYYY_MM_DD_HH_MM)?.timeInMillis
                ?: Calendar.getInstance().let {
                    it.add(Calendar.HOUR, 1)
                    it.add(Calendar.MINUTE, 30)
                    it.timeInMillis
                }
            showStartEnd(context, startTime) { d1, d2 ->
                listener?.onClickTime(
                    d1.getFormatTime(YYYY_MM_DD_HH_MM),
                    d2.getFormatTime(YYYY_MM_DD_HH_MM)
                )
            }
        }
    }

    fun setTime(startTime: String, endTime: String) {
        if (startTime.isEmpty() || endTime.isEmpty()) {
            tvStartTime.text = ""
            tvEndTime.text = ""
        } else {
            tvStartTime.text = startTime
            tvEndTime.text = endTime
        }
    }

    fun setWarning(warning: Boolean) {
        if (warning) {
            setBackgroundResource(R.color.red_warning)
        } else {
            setBackgroundResource(R.color.white)
        }
    }

    interface Listener {
        fun onClickTime(startTime: String, endTime: String)
    }

    fun showStartEnd(context: Context, selectedDate: Long?, onTimeSelect: (Date?, Date?) -> Unit) {
        PickerView.sOutTextSize = 17
        PickerView.sCenterTextSize = 18
        PickerView.sCenterColor = Color.BLACK
        PickerView.sOutColor = Color.parseColor("#A3A3A3")
        PickerView.sDefaultVisibleItemCount = 5
        BasePicker.sDefaultPickerBackgroundColor = Color.WHITE
        DefaultCenterDecoration.sDefaultLineColor = Color.TRANSPARENT

        val now = selectedDate ?: Calendar.getInstance().timeInMillis

        val mTimePicker = TimePicker.Builder(context,
            TimePicker.TYPE_MIXED_DATE or TimePicker.TYPE_HOUR or TimePicker.TYPE_MINUTE,
            TimePicker.OnTimeSelectListener { _, date -> Log.e("XXXX", date.toJson()) })
            // 设置选中时间
            .setSelectedDate(now)

            // 设置 Formatter
            .setFormatter(object : TimePicker.DefaultFormatter() {
                // 自定义Formatter显示去年，今年，明年
                override fun format(
                    picker: TimePicker,
                    type: Int,
                    position: Int,
                    num: Long
                ): CharSequence {
                    return when (type) {
                        TimePicker.TYPE_MIXED_DATE -> {
                            val calendar = Calendar.getInstance()
                            calendar.timeInMillis = num
                            calendar.time.getFormatTime(YYYY_MM_DD)
                        }
                        else -> super.format(picker, type, position, num)
                    }
                }
            }).create()

        var start: Date? = null
        var end: Date? = null

        val topBar = StartEndTopBar(mTimePicker.rootLayout)
        topBar.setDividerHeight(0f).setDividerColor(Color.parseColor("#eeeeee"))
        topBar.btnNext.setOnClickListener {
            if (topBar.btnStart.isSelected) {
                topBar.btnNext.text = "确定"
                topBar.btnStart.isSelected = false
                topBar.btnEnd.isSelected = true
                start = mTimePicker.selectedDates
                val calendar = start?.toCalendar() ?: Calendar.getInstance()
                calendar.add(Calendar.HOUR, 3)
                mTimePicker.setSelectedDate(calendar.timeInMillis)

            } else {
                end = mTimePicker.selectedDates
                onTimeSelect(start, end)
                mTimePicker.onCancel()
            }
        }
        mTimePicker.topBar = topBar
        mTimePicker.setOnPickerChooseListener(object : BasePicker.OnPickerChooseListener {
            override fun onConfirm(): Boolean = false

            override fun onCancel() {
            }
        })

        mTimePicker.show()
    }
}