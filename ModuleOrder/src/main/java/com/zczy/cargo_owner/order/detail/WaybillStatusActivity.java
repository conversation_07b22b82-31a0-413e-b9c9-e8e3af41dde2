package com.zczy.cargo_owner.order.detail;

import android.content.Context;
import android.content.Intent;
import android.os.Bundle;
import androidx.annotation.Nullable;
import androidx.core.content.ContextCompat;

import com.sfh.lib.ui.AbstractLifecycleActivity;
import com.zczy.cargo_owner.order.R;
import com.zczy.comm.ui.UtilStatus;

/***
 * 运单状态
 */
public class WaybillStatusActivity extends AbstractLifecycleActivity {

    public static final String EXTRA_ORDER_ID_STRING = "extra_order_id_string";

    public static void start(Context context, String orderId) {
        Intent intent = new Intent(context, WaybillStatusActivity.class);
        intent.putExtra(EXTRA_ORDER_ID_STRING, orderId);
        context.startActivity(intent);
    }

    @Override
    protected void onCreate(@Nullable Bundle savedInstanceState) {

        super.onCreate(savedInstanceState);
        setContentView(R.layout.order_waybill_status_activity);
        UtilStatus.initStatus(this, ContextCompat.getColor(this, R.color.comm_title_bg));
        initView();
    }

    private void initView() {

        String orderId = getIntent().getStringExtra(EXTRA_ORDER_ID_STRING);
        getSupportFragmentManager().beginTransaction().add(R.id.frame_layout,WaybillDetailFragment.start(orderId)).commitAllowingStateLoss();
//        FragmentTransaction fragmentTransaction = getSupportFragmentManager().beginTransaction();
//        fragmentTransaction.add(R.id.frame_layout, WaybillStatusFragment.start(orderId));
//        fragmentTransaction.commit();
    }
}