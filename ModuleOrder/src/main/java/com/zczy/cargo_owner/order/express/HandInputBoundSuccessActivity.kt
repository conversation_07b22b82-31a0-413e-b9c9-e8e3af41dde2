package com.zczy.cargo_owner.order.express

import android.content.Context
import android.content.Intent
import android.graphics.Color
import android.os.Bundle
import android.text.TextUtils
import android.view.View
import android.widget.Toast
import com.google.gson.Gson
import com.huawei.hms.hmsscankit.ScanUtil
import com.huawei.hms.ml.scan.HmsScan
import com.sfh.lib.mvvm.annotation.LiveDataMatch
import com.zczy.cargo_owner.order.R
import com.zczy.cargo_owner.order.express.adapter.BoundSuccessAdapter
import com.zczy.cargo_owner.order.express.model.OrderExpressMainModel
import com.zczy.cargo_owner.order.express.req.ListData
import com.zczy.cargo_owner.order.express.req.RespEnsureExpressSign
import com.zczy.cargo_owner.order.express.scan.HWScanActivity
import com.zczy.comm.CommServer
import com.zczy.comm.scan.RxCodeResult
import com.zczy.comm.ui.BaseActivity
import com.zczy.comm.utils.ex.setVisible
import com.zczy.comm.utils.json.toJson
import com.zczy.comm.widget.pulltorefresh.CommEmptyView
import kotlinx.android.synthetic.main.activity_hand_input_success.*

class HandInputBoundSuccessActivity : BaseActivity<OrderExpressMainModel>() {
    var listData: MutableList<ListData>? = null
    var data: RespEnsureExpressSign? = RespEnsureExpressSign()

    companion object {
        @JvmStatic
        fun start(context: Context?, data: RespEnsureExpressSign) {
            if (context == null) return
            val intent = Intent(context, HandInputBoundSuccessActivity::class.java)
            val bundle = Bundle()
            bundle.putSerializable("data", data)
            context.startActivity(intent)
        }

        @JvmStatic
        fun start(context: Context?, jsonStr: String) {
            if (context == null) return
            val intent = Intent(context, HandInputBoundSuccessActivity::class.java)
            intent.putExtra("jsonStr", jsonStr)
            context.startActivity(intent)
        }
    }

    override fun getLayout(): Int = R.layout.activity_hand_input_success

    override fun bindView(bundle: Bundle?) {
//        val jsonStr = intent.getStringExtra("jsonStr")
//
//        val listType = object : TypeToken<MutableList<ListData>>() {}.type
//        listData = if (TextUtils.isEmpty(jsonStr)) {
//            Gson().fromJson(
//                "[]",
//                listType
//            )
//        } else {
//            Gson().fromJson(
//                jsonStr,
//                listType
//            )
//        }

//        data = intent.extras.getSerializable("data") as RespEnsureExpressSign

        val jsonStr = intent.getStringExtra("jsonStr")
        data = Gson().fromJson(
            jsonStr,
            RespEnsureExpressSign::class.java
        )

        val boundSuccessAdapter = BoundSuccessAdapter()
        swipeRefreshMoreLayout.apply {
            setAdapter(boundSuccessAdapter, false)
            addItemDecorationSize(30)
            setEmptyView(CommEmptyView.creatorDef(this@HandInputBoundSuccessActivity))
            isRefreshEnabled = false
        }
        boundSuccessAdapter.setNewData(data!!.list)

        if (TextUtils.isEmpty(data?.returnMsg)) {
            tv_error_msg.visibility = View.GONE
        } else {
            tv_error_msg.visibility = View.VISIBLE
            tv_error_msg.text = data?.returnMsg
        }

        if (data?.type == "0") {
            iv_scan_tag.setBackgroundResource(R.drawable.icon_failed)
            tv_scan_tag.text = ""
            tv_scan_tag.setTextColor(Color.parseColor("#FF602E"))
            tv_scan_next.text = "重新扫描"
        } else {
            iv_scan_tag.setBackgroundResource(R.drawable.icon_success)
            tv_scan_tag.text = "签收成功！"
            tv_scan_tag.setTextColor(Color.parseColor("#61B24D"))
            tv_scan_next.text = "扫描下一个"
        }

        tv_scan_next.setOnClickListener {
            val intent = Intent(this, HWScanActivity::class.java)
            this.startActivityForResult(intent, 10000)
        }
    }

    override fun initData() {

    }

    override fun onNewIntent(intent: Intent?) {
        super.onNewIntent(intent)

        val jsonStr = intent?.getStringExtra("jsonStr")
        data = Gson().fromJson(
            jsonStr,
            RespEnsureExpressSign::class.java
        )

        val boundSuccessAdapter = BoundSuccessAdapter()
        swipeRefreshMoreLayout.apply {
            setAdapter(boundSuccessAdapter, false)
            addItemDecorationSize(30)
            setEmptyView(CommEmptyView.creatorDef(this@HandInputBoundSuccessActivity))
            isRefreshEnabled = false
        }
        boundSuccessAdapter.setNewData(data!!.list)

        if (TextUtils.isEmpty(data?.returnMsg)) {
            tv_error_msg.visibility = View.GONE
        } else {
            tv_error_msg.visibility = View.VISIBLE
            tv_error_msg.text = data?.returnMsg
        }

        if (data?.type == "0") {
            iv_scan_tag.setBackgroundResource(R.drawable.icon_failed)
            tv_scan_tag.text = "签收异常！"
            tv_scan_tag.setVisible(false)
            tv_scan_tag.setTextColor(Color.parseColor("#FF602E"))
        } else {
            iv_scan_tag.setBackgroundResource(R.drawable.icon_success)
            tv_scan_tag.text = "签收成功！"
            tv_scan_tag.setVisible(true)
            tv_scan_tag.setTextColor(Color.parseColor("#61B24D"))
        }

        tv_scan_next.setOnClickListener {
            val intent = Intent(this, HWScanActivity::class.java)
            this.startActivityForResult(intent, 10000)
        }
    }

    override fun onActivityResult(requestCode: Int, resultCode: Int, data: Intent?) {
        super.onActivityResult(requestCode, resultCode, data)
        if (resultCode != RESULT_OK || data == null) {
            finish()
            return
        }
        if (requestCode == 10000) {
            val hmsScanResult: HmsScan? = data.getParcelableExtra(ScanUtil.RESULT)
            if (hmsScanResult != null) {
                //展示解码结果
                val result = RxCodeResult()
                result.result = hmsScanResult.getOriginalValue()
                handleDecode(result)
            }
        }
    }

    private fun handleDecode(result: RxCodeResult?) {
        val login = CommServer.getUserServer().login
        if (login == null) {
            Toast.makeText(this, "请先登录", Toast.LENGTH_SHORT).show()
            return
        }
        if (result == null || TextUtils.isEmpty(result.result)) {
            showDialogToast("扫描识别失败,重新扫描!")
            return
        }
        val data = TextUtils.split(result.result, ";")

        getViewModel(OrderExpressMainModel::class.java).ensureExpressSign(result.result)
    }

    @LiveDataMatch
    open fun onEnsureExpressSignSuccess(data: RespEnsureExpressSign) {
        HandInputBoundSuccessActivity.start(this, data.toJson())
    }
}