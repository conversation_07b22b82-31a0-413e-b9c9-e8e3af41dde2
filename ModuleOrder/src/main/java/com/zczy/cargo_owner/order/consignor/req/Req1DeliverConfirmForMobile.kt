package com.zczy.cargo_owner.order.consignor.req

import android.text.TextUtils
import com.zczy.comm.http.entity.BaseNewRequest
import com.zczy.comm.http.entity.BaseRsp
import com.zczy.comm.http.entity.PageList

/** 功能描述:
 * 1.手机端接口：根据条件查询发货单确认列表
 * http://wiki.zczy56.com/pages/viewpage.action?pageId=16089091
 * <AUTHOR>
 * @company 中储智运科技股份有限公司
 * @copyright （版权）中储南京智慧物流科技有限公司所有
 * @date 2019/4/9
 */
data class Req1DeliverConfirmForMobile(
    // 搜索框查询条件 运单号后6位／货物名称/承运方
    var search: String = "",
    // 查询状态条件  0-未确认  1-已确认  不传为查全部（搜索框）
    var advanceState: String = "",
    var nowPage: Int = 1,
    var pageSize: Int = 10,
    /** sort 0 降序  1 升序 */
    var sort: String = "0"
) : BaseNewRequest<BaseRsp<PageList<Rsp1QueryConsignorOrderStateOfMobile>>>
    ("oms-app/order/consignor/deliverConfirmForMobile")

data class Rsp1QueryConsignorOrderStateOfMobile(
    var orderId: String = "", //	订单id
    var orderModel: String = "", // 订单类型	订单类型：0 抢单,1 议价
    var detailId: String = "", // 订单详细id
    var advanceState: String = "", // 预付款状态	预付款状态：0:未审核，1:已审核，2:货主打回
    var plateNumber: String = "", //	车牌号
    var despatchCity: String = "", // 启运地市
    var despatchDis: String = "", // 启运地区
    var deliverCity: String = "", // 目的地市
    var deliverDis: String = "", // 目的地区
    var driverMobile: String = "", // 司机手机号
    var driverUserName: String = "", // 司机姓名
    var advanceRatio: String = "", // 预付款比例
    var consignorAdvanceMoney: String = "", // 货主预付款金额
    var deliverWeightStr: String = "", // 确认发货重量
    var pbConsignorMoneyStr: String = "", //	确认发货金额
    var pbConsignorUnitMoney: String = "", //	发布货主单价(含税)
    var repulseReason: String = "", // 打回原因
    var cargoCategory: String = "", // 货物类别：1：重货,2 泡货
    var orderCarpoolingId: String? = "", // 零担拼车单号
    // 后台财务-韦泗林 要求 删除 2019-06-18
    // var hugeOrderFlag: String = "", // 是否是批量货：0 否 1 是
    var deliverImageList: List<DeliverImageList> = emptyList() // 发货单图片列表
) {
    data class DeliverImageList(
        var id: String = "", //	图片主键Id	图片主键Id
        var imageUrl: String = "", //	图片url	图片url
        var createdTime: String = "" //	创建时间	创建时间
    )
}

fun Rsp1QueryConsignorOrderStateOfMobile.showOrderId(): String {
    return if (TextUtils.isEmpty(orderCarpoolingId)) {
        orderId
    } else ({
        orderCarpoolingId
    }).toString()
}

fun Rsp1QueryConsignorOrderStateOfMobile.formatStartAddress(): String {
    val sb = StringBuilder()
    sb.append(despatchCity)
    if (sb.isNotEmpty()) {
        sb.append(" ")
    }
    sb.append(despatchDis)
    return sb.toString()
}

fun Rsp1QueryConsignorOrderStateOfMobile.formatEndAddress(): String {
    val sb = StringBuilder()
    sb.append(deliverCity)
    if (sb.isNotEmpty()) {
        sb.append(" ")
    }
    sb.append(deliverDis)
    return sb.toString()
}

fun Rsp1QueryConsignorOrderStateOfMobile.formatDeliverWeight(): String {
    val s = if (cargoCategory == "2") "方" else "吨"
    return "$deliverWeightStr$s"
}

fun Rsp1QueryConsignorOrderStateOfMobile.formatDriverInfo(): String {
    return when {
        driverUserName.isEmpty() && plateNumber.isEmpty() -> {
            ""
        }
        driverUserName.isEmpty() -> {
            val num: String = if (plateNumber.length > 3) {
                plateNumber.substring(0, 2) + " " + plateNumber.substring(2)
            } else {
                plateNumber
            }
            "车牌号：$num"
        }
        plateNumber.isEmpty() -> {
            "承运方：$driverUserName"
        }
        else -> {
            val num: String = if (plateNumber.length > 3) {
                plateNumber.substring(0, 2) + " " + plateNumber.substring(2)
            } else {
                plateNumber
            }
            "承运方：$driverUserName | 车牌号：$num"
        }
    }
}