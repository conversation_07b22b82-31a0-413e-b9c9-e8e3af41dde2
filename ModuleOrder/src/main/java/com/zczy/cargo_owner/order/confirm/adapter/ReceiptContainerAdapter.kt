package com.zczy.cargo_owner.order.confirm.adapter

import androidx.constraintlayout.widget.ConstraintLayout
import android.util.TypedValue
import android.view.ViewGroup
import com.chad.library.adapter.base.BaseQuickAdapter
import com.chad.library.adapter.base.BaseViewHolder
import com.zczy.cargo_owner.order.R
import com.zczy.cargo_owner.order.confirm.bean.ContainerNoJsonArray
import com.zczy.comm.utils.dp2px
import com.zczy.comm.widget.inputv2.InputViewEdit

/**
 * 功能描述: 结算信息 集装箱
 * <AUTHOR>
 * @date 2022/3/8-18:11
 */

class ReceiptContainerAdapter(var canEdit: Boolean = false) :
    BaseQuickAdapter<ContainerNoJsonArray, BaseViewHolder>(R.layout.order_returned_container_item) {
    override fun onCreateViewHolder(parent: ViewGroup, viewType: Int): BaseViewHolder {
        return super.onCreateViewHolder(parent, viewType).apply {
            getView<InputViewEdit>(R.id.inputContainerListNo).setListener(object :
                InputViewEdit.Listener() {
                override fun onTextChanged(viewId: Int, view: InputViewEdit, s: String) {
                    val tag = view.tag
                    if (tag is ContainerNoJsonArray) {
                        tag.containerListNo = s
                    }
                }
            })
        }
    }

    override fun convert(helper: BaseViewHolder?, item: ContainerNoJsonArray) {
        helper?.apply {
            val view = getView<InputViewEdit>(R.id.inputContainerListNo)
            view.content = item.containerListNo
            view.tag = item
            view.tvTitle.setTextSize(TypedValue.COMPLEX_UNIT_SP, 16F)
            view.editText.setTextSize(TypedValue.COMPLEX_UNIT_SP, 13F)
            view.editText.setBackgroundResource(R.drawable.base_btn_grey_ee_solid_4radius_shape)
            val layoutParams = ConstraintLayout.LayoutParams(
                ConstraintLayout.LayoutParams.WRAP_CONTENT,
                ConstraintLayout.LayoutParams.WRAP_CONTENT
            )
            layoutParams.topToTop = ConstraintLayout.LayoutParams.PARENT_ID
            layoutParams.endToEnd = ConstraintLayout.LayoutParams.PARENT_ID
            layoutParams.marginEnd = dp2px(14f)
            layoutParams.matchConstraintMinWidth = dp2px(80f)
            view.editText.layoutParams = layoutParams
            view.editText.setPadding(dp2px(12f), dp2px(2f), dp2px(12f), dp2px(2f))
            view.setTitle2("")
            view.isEnabled = canEdit
        }
    }
}