package com.zczy.cargo_owner.order.overdue

import android.annotation.SuppressLint
import android.content.Context
import android.content.Intent
import android.os.Bundle
import android.text.InputFilter
import android.text.InputFilter.LengthFilter
import android.text.InputType
import android.view.View
import androidx.recyclerview.widget.LinearLayoutManager
import com.jakewharton.rxbinding2.widget.RxTextView
import com.sfh.lib.event.RxBusEventManager
import com.sfh.lib.mvvm.annotation.LiveDataMatch
import com.sfh.lib.rx.ui.UtilRxView
import com.sfh.lib.ui.dialog.DialogBuilder
import com.zczy.cargo_owner.order.R
import com.zczy.cargo_owner.order.overdue.adapter.OrderOverdueAppealAdapter
import com.zczy.cargo_owner.order.overdue.model.OrderOverdueFragmentModel
import com.zczy.cargo_owner.order.overdue.req.*
import com.zczy.comm.data.entity.EImage
import com.zczy.comm.ui.BaseActivity
import com.zczy.comm.utils.ex.setVisible
import com.zczy.comm.utils.ex.toCommaString
import com.zczy.comm.utils.imageselector.ImageSelector
import com.zczy.comm.utils.toJsonArray
import com.zczy.comm.widget.EditTextCloseView
import com.zczy.comm.widget.dialog.ChooseDialogV1
import com.zczy.comm.widget.inputv2.InputViewClick
import io.reactivex.android.schedulers.AndroidSchedulers
import kotlinx.android.synthetic.main.overdue_order_appeal_activity.*
import java.io.File


/**
 * 功能描述: 逾期申诉
 * <AUTHOR>
 * @date 2023/3/23-20:46
 */

class OrderOverdueAppealActivity : BaseActivity<OrderOverdueFragmentModel>() {

    private val detail by lazy { intent.getBooleanExtra(APPEAL_DETAIL, false) }
    private val dataList by lazy { intent.getStringExtra(EXTRA_DATA)?.toJsonArray(OrderOverDueItem::class.java) ?: mutableListOf() }
    private val mAdapter = OrderOverdueAppealAdapter()

    companion object {

        private const val APPEAL_DETAIL = "APPEAL_DETAIL"
        private const val EXTRA_DATA = "extra_data"

        @JvmStatic
        fun jumpPage(
            context: Context?,
            detail: Boolean,
            extraData: String
        ) {
            val intent = Intent(context, OrderOverdueAppealActivity::class.java)
            intent.putExtra(APPEAL_DETAIL, detail)
            intent.putExtra(EXTRA_DATA, extraData)
            context?.startActivity(intent)
        }
    }

    override fun getLayout(): Int {
        return R.layout.overdue_order_appeal_activity
    }

    @SuppressLint("SetTextI18n")
    override fun bindView(bundle: Bundle?) {
        recyclerView.apply {
            layoutManager = LinearLayoutManager(this@OrderOverdueAppealActivity)
            adapter = mAdapter
        }
        if (detail) {
            //申诉详情
            appToolbar.setTitle("申诉详情")
            tv_tips.visibility = View.GONE
            inputView3.isEnabled = false
            image_select_view.canSelect = false
            val editText = inputView3.editText
            if (editText is EditTextCloseView) {
                editText.setCompoundDrawables(editText.getCompoundDrawables()[0], editText.getCompoundDrawables()[1], null, editText.getCompoundDrawables()[3])
            }
            inputView1.setArrowVisible(false)
            inputView2.setArrowVisible(false)
            edit_code.isEnabled = false
            ll_btn.setVisible(false)
            tv_code_size.setVisible(false)
            viewModel?.queryAppealDetail(
                req = ReqQueryAppealDetail(
                    orderId = dataList.toCommaString { it.orderId }
                )
            )
        } else {
            //逾期申诉
            appToolbar.setTitle("逾期限制挂单申诉")
            tv_tips.visibility = View.VISIBLE
            image_select_view.maxCount = 5
            inputView1.setListener(object : InputViewClick.Listener() {
                override fun onClick(viewId: Int, view: InputViewClick, content: String) {
                    ChooseDialogV1.instance(listOf("等待结算", "等待违约"))
                        .setTitle("请选择处理方式")
                        .setChoose(view.content)
                        .setClick { s, _ ->
                            view.content = s
                        }
                        .show(this@OrderOverdueAppealActivity)
                }
            })
            inputView2.setListener(object : InputViewClick.Listener() {
                override fun onClick(viewId: Int, view: InputViewClick, content: String) {
                    ChooseDialogV1.instance(listOf("货损货差", "存在纠纷"))
                        .setTitle("请选择原因")
                        .setChoose(view.content)
                        .setClick { s, _ ->
                            view.content = s
                        }
                        .show(this@OrderOverdueAppealActivity)
                }
            })
            inputView3.isEnabled = true
            inputView3.setInputType(InputType.TYPE_CLASS_NUMBER)

            val emojiFilter = InputFilter { source, start, end, _, _, _ ->
                for (i in start until end) {
                    if (Character.getType(source[i]) == Character.SURROGATE.toInt()) {
                        return@InputFilter ""
                    }
                }
                null
            }
            edit_code.isEnabled = true
            edit_code.filters = arrayOf(LengthFilter(200), emojiFilter)
            val disposable = RxTextView
                .textChanges(edit_code)
                .observeOn(AndroidSchedulers.mainThread())
                .subscribe { charSequence ->
                    tv_code_size.text = "(${charSequence.length}/200)"
                }
            this.putDisposable(disposable)
            ll_btn.setVisible(true)
            UtilRxView.clicks(tvCancel, 1500) {
                finish()
            }
            UtilRxView.clicks(tvBtn, 1500) {
                val dialogBuilder = DialogBuilder()
                dialogBuilder.title = "温馨提示"
                dialogBuilder.message = "在提交申诉时，请仔细核对并确认您所描述的情况完全属实，并确保上传的申诉证明材料真实有效。平台将对此进行备案处理，并保留追究责任的权利。如后期出现任何不实情况，您将自行承担相应责任。请务必诚信申诉，感谢您的配合。"
                dialogBuilder.setOkListener { _, _ ->
                    if (doAppealOverdue()) return@setOkListener
                }
                showDialog(dialogBuilder)
            }
        }
        mAdapter.setNewData(dataList)
    }

    private fun doAppealOverdue(): Boolean {
        val req = ReqAppealOverdueOrders()
        req.orderIds = dataList.toCommaString { it.orderId }
        when (inputView1.content) {
            "" -> {
                showToast("请选择处理方式!")
                return true
            }

            "等待结算" -> {
                req.appealType = "1"
            }

            "等待违约" -> {
                req.appealType = "2"
            }
        }
        when (inputView2.content) {
            "" -> {
                showToast("请选择原因!")
                return true
            }

            "货损货差" -> {
                req.appealReason = "1"
            }

            "存在纠纷" -> {
                req.appealReason = "2"
            }
        }
        if (inputView3.content.isEmpty()) {
            showToast("申诉有效时长不能为空!")
            return true
        }
        if (inputView3.content.toInt() < 1) {
            showToast("申诉有效时长最少不能低于1天!")
            return true
        }
        req.appealValidityDay = inputView3.content
        val text = edit_code.text.toString()
        if (text.isNotEmpty()) {
            req.appealRemark = text
        }
        val imgList = image_select_view.imgList
        if (imgList.isEmpty()) {
            showToast("图片必传!")
            return true
        }
        req.appealFiles = imgList.toCommaString { it.imageId }
        viewModel?.appealOverdueOrders(
            req = req
        )
        return false
    }

    override fun initData() {

    }

    @LiveDataMatch(tag = "上传文件成功")
    open fun onFileSuccess(tag: File, url: String) {
        image_select_view.setSelectData(url)
    }

    @LiveDataMatch(tag = "上传文件失败")
    open fun onFileFailure(tag: File, error: String) {
        showToast(error)
    }

    public override fun onActivityResult(requestCode: Int, resultCode: Int, data: Intent?) {
        super.onActivityResult(requestCode, resultCode, data)
        if (resultCode == RESULT_OK) {
            val strings = ImageSelector.obtainPathResult(data)
            viewModel?.upFile(strings)
        }
    }

    @LiveDataMatch
    open fun appealOverdueOrdersSuccess() {
        RxBusEventManager.postEvent("逾期申请成功")
        finish()
    }

    @LiveDataMatch
    open fun queryAppealDetailSuccess(data: RspQueryAppealDetailV1?) {
        data?.data?.apply {
            inputView1.content = when (appealType) {
                "1" -> {
                    "等待结算"
                }

                "2" -> {
                    "等待违约"
                }

                else -> {
                    ""
                }
            }
            inputView2.content = when (appealReason) {
                "1" -> {
                    "货损货差"
                }

                "2" -> {
                    "存在纠纷"
                }

                else -> {
                    ""
                }
            }
            inputView3.content = appealValidityDay ?: ""
            edit_code.setText(appealRemark ?: "")
            val e = arrayListOf<EImage>()
            files.forEach {
                e.add(
                    EImage(
                        imageId = it.pictureUrl
                    )
                )
            }
            image_select_view.imgList = e
        }
    }
}