package com.zczy.cargo_owner.order.settlement

import android.content.Context
import android.content.Intent
import android.os.Bundle
import com.zczy.cargo_owner.order.R
import com.zczy.comm.ui.BaseActivity
import kotlinx.android.synthetic.main.order_confirm_settlement_success_activity.*

/**
 * @description
 * <AUTHOR> 韩正亚
 * @company 中储智运科技股份有限公司
 * @copyright （版权）中储南京智慧物流科技有限公司所有
 * @date 2019/4/24
 */

class ConfirmSettlementSuccessActivity : BaseActivity<SettlementApplicationModel>() {
    override fun getLayout(): Int {
        return R.layout.order_confirm_settlement_success_activity
    }

    override fun bindView(bundle: Bundle?) {
        btnConfirmSettlementGotIt.setOnClickListener {
            finish()
        }
    }

    override fun initData() {

    }

    companion object {
        @JvmStatic
        fun start(context: Context?) {
            if (context == null) return
            val intent = Intent(context, ConfirmSettlementSuccessActivity::class.java)
            context.startActivity(intent)
        }
    }
}