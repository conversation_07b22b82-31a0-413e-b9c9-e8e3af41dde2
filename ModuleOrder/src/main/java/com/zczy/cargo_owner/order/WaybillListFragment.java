package com.zczy.cargo_owner.order;

import static com.zczy.cargo_owner.order.confirm.ReturnedOrderConfirmListFragment.OPERATION_CONFIG;
import static com.zczy.cargo_owner.order.confirm.ReturnedOrderConfirmListFragment.RETURNED_ORDER_QUERY_TYPE;
import static com.zczy.cargo_owner.order.confirm.ReturnedOrderConfirmListFragment.TYPE_NOT_CONFIRM;
import static com.zczy.cargo_owner.order.entity.EWaybill.TYPE_DELIVERMINE;

import android.annotation.SuppressLint;
import android.app.Activity;
import android.content.Intent;
import android.graphics.Color;
import android.graphics.drawable.GradientDrawable;
import android.os.Bundle;
import android.text.Html;
import android.text.SpannableString;
import android.text.SpannableStringBuilder;
import android.text.Spanned;
import android.text.TextUtils;
import android.text.style.ForegroundColorSpan;
import android.view.Gravity;
import android.view.View;
import android.widget.CheckBox;
import android.widget.EditText;
import android.widget.ImageView;
import android.widget.TextView;

import androidx.constraintlayout.widget.ConstraintLayout;

import com.chad.library.adapter.base.BaseQuickAdapter;
import com.sfh.lib.event.RxBusEvent;
import com.sfh.lib.event.RxBusEventManager;
import com.sfh.lib.mvvm.annotation.LiveDataMatch;
import com.sfh.lib.mvvm.service.BaseViewModel;
import com.sfh.lib.ui.AbstractLifecycleFragment;
import com.sfh.lib.ui.dialog.DialogBuilder;
import com.sfh.lib.utils.UtilTool;
import com.zczy.cargo_owner.libcomm.DeliverProvider;
import com.zczy.cargo_owner.libcomm.IDeliverProvider;
import com.zczy.cargo_owner.libcomm.event.EventNewGoodsSuccess;
import com.zczy.cargo_owner.libcomm.event.EventRefreshWaybillList;
import com.zczy.cargo_owner.libcomm.event.order.EventWaybillException;
import com.zczy.cargo_owner.order.change.OrderChangeHandleActivity;
import com.zczy.cargo_owner.order.change.OrderChangeScopeInfoActivity;
import com.zczy.cargo_owner.order.change.ReceiptAddressChangeActivity;
import com.zczy.cargo_owner.order.change.deliverinfo.ui.OrderChangeDeliverInfoActivity;
import com.zczy.cargo_owner.order.change.manage.req.RxBusReFreshOrderList;
import com.zczy.cargo_owner.order.confirm.ReturnedOrderConfirmListActivity;
import com.zczy.cargo_owner.order.confirm.bean.OperationConfig;
import com.zczy.cargo_owner.order.confirm.v2.SingleReturnedOrderConfirmActivityV2;
import com.zczy.cargo_owner.order.confirm.v2.SingleReturnedOrderConfirmActivityV3;
import com.zczy.cargo_owner.order.detail.WaybillContractDetailActivity;
import com.zczy.cargo_owner.order.detail.WaybillDetailStatueActivity;
import com.zczy.cargo_owner.order.dialog.OrderEstimatedShippingTimeDialog;
import com.zczy.cargo_owner.order.dialog.OrderPayBillDialog;
import com.zczy.cargo_owner.order.dialog.OrderPayBillTypeDialog;
import com.zczy.cargo_owner.order.entity.EBusinessEntity;
import com.zczy.cargo_owner.order.entity.EJiDongOrderCode;
import com.zczy.cargo_owner.order.entity.EJiDongOrderCodeHandle;
import com.zczy.cargo_owner.order.entity.EOrderListExceptionDtoList;
import com.zczy.cargo_owner.order.entity.EWaybill;
import com.zczy.cargo_owner.order.entity.JdSearchBean;
import com.zczy.cargo_owner.order.entity.WaybillExceptionInfo;
import com.zczy.cargo_owner.order.model.ReqDeleteBatchOrder;
import com.zczy.cargo_owner.order.model.ReqFreightPayment;
import com.zczy.cargo_owner.order.model.ReqPayCheck;
import com.zczy.cargo_owner.order.model.ReqTmsConsignorConfirmPay;
import com.zczy.cargo_owner.order.model.ReqTmsConsignorOfflinePayment;
import com.zczy.cargo_owner.order.model.RespConsignorConfirmPayMoney;
import com.zczy.cargo_owner.order.model.RespQueryDispatchMobile;
import com.zczy.cargo_owner.order.model.RspQueryChangeState;
import com.zczy.cargo_owner.order.model.RspTmsConsignorConfirmPay;
import com.zczy.cargo_owner.order.model.RxBusModifyOrder;
import com.zczy.cargo_owner.order.model.RxChangeCancel;
import com.zczy.cargo_owner.order.model.WayBillModelV1;
import com.zczy.cargo_owner.order.model.WaybillModel;
import com.zczy.cargo_owner.order.qrcode.SeniorOrderWaybillQRCodeActivity;
import com.zczy.cargo_owner.order.settlement.SettlementApplicationListActivity;
import com.zczy.cargo_owner.order.settlement.view.SettlementFilterDialog;
import com.zczy.cargo_owner.order.transport.WaybillTrackingActivityV1;
import com.zczy.cargo_owner.order.violate.OrderViolateAddActivity;
import com.zczy.cargo_owner.order.violate.OrderViolateAddTMSActivity;
import com.zczy.cargo_owner.order.violate.OrderViolateListActivity;
import com.zczy.comm.CommServer;
import com.zczy.comm.data.entity.ELogin;
import com.zczy.comm.http.entity.BaseRsp;
import com.zczy.comm.http.entity.PageList;
import com.zczy.comm.pay.PayDialog;
import com.zczy.comm.pluginserver.AMainServer;
import com.zczy.comm.utils.Md5Util;
import com.zczy.comm.utils.PhoneUtil;
import com.zczy.comm.utils.ShapeUtil;
import com.zczy.comm.utils.ex.ViewUtil;
import com.zczy.comm.utils.json.JsonUtil;
import com.zczy.comm.widget.dialog.MenuDialogV1;
import com.zczy.comm.widget.pulltorefresh.CommEmptyView;
import com.zczy.comm.widget.pulltorefresh.OnLoadingListener;
import com.zczy.comm.widget.pulltorefresh.SwipeRefreshMoreLayout;
import com.zczy.lib_zstatistics.sdk.ZStatistics;
import com.zczy.lib_zstatistics.sdk.base.EventKeyType;
import com.zczy.lib_zstatistics.sdk.base.EventType;
import com.zczy.lib_zstatistics.sdk.base.OnAddBusinessKV;

import org.jetbrains.annotations.NotNull;

import java.util.ArrayList;
import java.util.Calendar;
import java.util.HashMap;
import java.util.List;
import java.util.Map;
import java.util.Objects;

/**
 * 功能描述:承运人【4待装货，5待卸货,7已完成，1全部】运单列表
 *
 * <AUTHOR> 孙飞虎
 * @company 中储智运科技股份有限公司
 * @copyright （版权）中储南京智慧物流科技有限公司所有
 * @date 2018/12/17
 */
public class WaybillListFragment extends AbstractLifecycleFragment<WaybillModel>
        implements BaseQuickAdapter.OnItemChildClickListener,
        BaseQuickAdapter.OnItemClickListener,
        View.OnClickListener,
        OnRefreshList {

    private EWaybill mData;
    private static final int REQUEST_NORMAL_CHOOSE_V1 = 0x33;
    private static final int REQUEST_BATCH_CHOOSE_V1 = 0x34;

    private OnRefreshList onRefreshListCallBack;

    /***
     *
     * @param type {@link EWaybill}
     * @return
     */
    public static WaybillListFragment newInstance(String type, OnRefreshList onRefreshListCallBack) {

        Bundle args = new Bundle();
        args.putString(QUERY_TYPE, type);
        WaybillListFragment fragment = new WaybillListFragment();
        fragment.onRefreshListCallBack = onRefreshListCallBack;
        fragment.setArguments(args);
        return fragment;
    }

    /***
     *
     * @param type {@link EWaybill}
     * @return
     */
    public static WaybillListFragment newInstance(String type, String title) {

        Bundle args = new Bundle();
        args.putString(QUERY_TYPE, type);
        args.putString(QUERY_TITLE, title);
        args.putBoolean("search", true);
        WaybillListFragment fragment = new WaybillListFragment();
        fragment.setArguments(args);
        return fragment;
    }

    public static WaybillListFragment newInstance(String type, JdSearchBean jdSearchBean) {

        Bundle args = new Bundle();
        args.putString(QUERY_TYPE, type);
        args.putSerializable(QUERY_BEAN, jdSearchBean);
        args.putBoolean("search", true);
        WaybillListFragment fragment = new WaybillListFragment();
        fragment.setArguments(args);
        return fragment;
    }

    public static final String QUERY_TYPE = "queryType";
    public static final String QUERY_TITLE = "queryTitle";
    public static final String QUERY_INIT_DATA = "query_init_data";
    // 冀东搜索条件
    public static final String QUERY_BEAN = "queryBean";

    public SwipeRefreshMoreLayout mSwipeRefreshMoreLayout;
    private ELogin mLogin = CommServer.getUserServer().getLogin();
    /***
     * 运单类型
     */
    public String queryType;
    /**
     * 搜索内容
     */
    public String title;
    /**
     * 冀东搜索内容
     */
    public JdSearchBean jdSearchBean;

    public TextView tv_entity;

    public TextView tv_select;
    //业务主体
    public String businessEntity = "";
    //是否零担
    public String tvNilStr = "";

    // 时间段 1近三个月运单  2019 2019已完结运单 2018 2018已完结运单 2017 2017已完结运单 2016 2016已完结运单 2015 2015已完结运单
    protected String periodFlag = "";

    //默认全部
    protected String selectFaly = "";
    /**
     * ZCZY-8777 冀东客户数字供应链解决方案需求
     */
    private ConstraintLayout rl_all_select;
    private CheckBox cb_all_select;
    private TextView tv_setting;
    private TextView tvDeleteBatch;
    private TextView tvGoBatchOrderManage;

    private List<EWaybill> selectList;
    private WaybillAdapter waybillAdapter;
    private SelectJiDongOrderCodeDialog selectJiDongOrderCodeDialog;
    private String orderId;
    // 是否为冀东账号
    private boolean isJiDongAccount;
    //冀东--自定义编号dialog是否点击设置
    private boolean jdDialogUpdateTag;
    private EWaybill waybill;

    @Override
    public int getLayout() {

        return R.layout.order_waybill_refresh_list_fragment;
    }

    @Override
    public void initData(View view) {
        orderId = "";
        rl_all_select = view.findViewById(R.id.rl_all_select);
        tvGoBatchOrderManage = view.findViewById(R.id.tvGoBatchOrderManage);
        cb_all_select = view.findViewById(R.id.cb_all_select);
        tvDeleteBatch = view.findViewById(R.id.tvDeleteBatch);
        tv_setting = view.findViewById(R.id.tv_setting);
        tv_setting.setBackground(ShapeUtil.INSTANCE.creatGradientShape(
                getResources().getColor(R.color.color_EAEFFE),
                getResources().getColor(R.color.color_F6FAFF),
                GradientDrawable.Orientation.BL_TR,
                16f)
        );
        selectList = new ArrayList<>();
        tvDeleteBatch.setOnClickListener(this);
        cb_all_select.setOnClickListener(this);
        tv_setting.setOnClickListener(this);
        tvGoBatchOrderManage.setOnClickListener(this);
        //8777，判断是否为冀东账号
        getViewModel().querySingleDictConfigV1();
        waybillAdapter = new WaybillAdapter(getContext(), (view1, title, data, position) -> {
            this.waybill = data;
            if (data.isTmsOrder()) {
                //极速好货TMS
                this.tmsOnClickItem(data, title);
            } else {
                if (TextUtils.equals(TYPE_DELIVERMINE, data.getOrderQueryType())) {
                    //我的发布
                    onItemDelivermine(data, title);
                } else if (TextUtils.equals(EWaybill.TYPE_DELAYSHIPMENT, data.getOrderQueryType())) {
                    //待发货
                    onItemDelayShipment(data, title);
                } else if (TextUtils.equals(EWaybill.TYPE_UNLOAD, data.getOrderQueryType())) {
                    //运输中
                    onItemUnload(data, title);
                } else {
                    //已完成
                    onItemComplete(data, title);
                }
            }

        });
        this.mSwipeRefreshMoreLayout = view.findViewById(R.id.swipeRefreshMoreLayout);
        this.mSwipeRefreshMoreLayout.setAdapter(waybillAdapter, true);
        this.mSwipeRefreshMoreLayout.setEmptyView(CommEmptyView.creatorDef(getContext()));
        this.mSwipeRefreshMoreLayout.setOnLoadListener(new OnLoadingListener() {
            @Override
            public void onRefreshUI(int nowPage) {
                selectList.clear();
                waybillAdapter.unSelectAll();
                cb_all_select.setChecked(false);
                rl_all_select.setVisibility(View.GONE);
                showSelectNum();
                //激活一次生命监听防止遗漏 部分机型出现无响应问题
                activateLifecycleEvent();
                if (isJiDongAccount) {
                    getViewModel().queryPageWaybill(queryType, nowPage, jdSearchBean, businessEntity, "", periodFlag, selectFaly, tvNilStr);
                } else {
                    getViewModel().queryPageWaybill(queryType, nowPage, jdSearchBean, title, businessEntity, "", periodFlag, selectFaly, tvNilStr);
                }
                if (onRefreshListCallBack != null) {
                    onRefreshListCallBack.onRefresh();
                }
            }

            @Override
            public void onLoadMoreUI(int nowPage) {
//                selectList.clear();
//                waybillAdapter.unSelectAll();
//                showSelectNum();
                cb_all_select.setChecked(false);
//                rl_all_select.setVisibility(View.GONE);
                //激活一次生命监听防止遗漏 部分机型出现无响应问题
                activateLifecycleEvent();
                if (isJiDongAccount) {
                    getViewModel().queryPageWaybill(queryType, nowPage, jdSearchBean, businessEntity, waybillAdapter.getData().get(waybillAdapter.getData().size() - 1).getCreatedTimeSear(), periodFlag, selectFaly, tvNilStr);
                } else {
                    getViewModel().queryPageWaybill(queryType, nowPage, jdSearchBean, title, businessEntity, waybillAdapter.getData().get(waybillAdapter.getData().size() - 1).getCreatedTimeSear(), periodFlag, selectFaly, tvNilStr);
                }
            }
        });
//        this.mSwipeRefreshMoreLayout.addItemDecorationSize(15);
        this.mSwipeRefreshMoreLayout.addOnItemListener(this);
        this.mSwipeRefreshMoreLayout.addOnItemChildClickListener(this);
        Bundle data = getArguments();
        boolean donotInitData = false;
        if (data != null) {
            queryType = data.getString(QUERY_TYPE);
            title = data.getString(QUERY_TITLE);
            donotInitData = data.getBoolean(QUERY_INIT_DATA);
            jdSearchBean = (JdSearchBean) data.getSerializable(QUERY_BEAN);
        }
        if (!donotInitData) {
            //刷新数据
            this.mSwipeRefreshMoreLayout.onAutoRefresh();
        }

        this.tv_entity = view.findViewById(R.id.tv_entity);
        TextView tvNil = view.findViewById(R.id.tvNil);
        tvNil.setOnClickListener(v -> {
            ArrayList<String> item = new ArrayList<>();
            item.add("全部");
            item.add("是");
            item.add("否");
            new SettlementFilterDialog(getActivity(), item, new SettlementFilterDialog.ItemOnClick() {
                @SuppressLint("SetTextI18n")
                @Override
                public void onSelectedText(String str, int position) {
                    tvNil.setText("是否零担：" + str);
                    tvNilStr = str;
                    mSwipeRefreshMoreLayout.onAutoRefresh();
                }

                @Override
                public void dismiss() {

                }
            }).show(tv_entity);
        });
        TextView tv_date = view.findViewById(R.id.tv_date);
        this.selectTime(tv_date);

        this.tv_select = view.findViewById(R.id.tv_select);

        if (data.getBoolean("search", false)) {
            //只有搜索页显示时间选项，不显示业务主体选择,筛选项
            tv_date.setVisibility(View.VISIBLE);
            tv_entity.setVisibility(View.GONE);
            tv_select.setVisibility(View.GONE);
            tvGoBatchOrderManage.setVisibility(View.GONE);
        } else {
            //除搜索之外，不显示时间选项
            tv_date.setVisibility(View.VISIBLE);
            if (TextUtils.equals(queryType, TYPE_DELIVERMINE)) {
                tvGoBatchOrderManage.setVisibility(View.VISIBLE);
            } else {
                tvGoBatchOrderManage.setVisibility(View.GONE);
            }

            View view_left = view.findViewById(R.id.view_left);
            View view_right = view.findViewById(R.id.view_right);

            if (TextUtils.equals(queryType, EWaybill.TYPE_COMPLETE)) {
                //只有已完成运单列表,显示筛选项
                tv_select.setVisibility(View.VISIBLE);
                //让“业务主体”按钮，在左边
                view_left.setVisibility(View.GONE);
                view_right.setVisibility(View.VISIBLE);
            } else {
                tv_select.setVisibility(View.GONE);
                //让“业务主体”按钮，在右边
                view_left.setVisibility(View.VISIBLE);
                view_right.setVisibility(View.GONE);
            }
            tv_select.setOnClickListener(v -> {
                //选择运单状态
                SelectWaybillStatusDialog.show(getContext(), tv_select.getText().toString(), (text, type) -> {
                    selectFaly = type;
                    tv_select.setText(text);
                    mSwipeRefreshMoreLayout.onAutoRefresh();
                }, v);
            });
            this.tv_entity.setOnClickListener(v -> getViewModel().queryEntity(true));
            //查询业务主体，控制业务主体显示不显示
            getViewModel().queryEntity(false);
        }

    }

    private void tmsOnClickItem(EWaybill data, String title) {
        mData = data;
        switch (title) {
            case "取消发布": {
                DialogBuilder dialogBuilder = new DialogBuilder()
                        .setMessage("确认取消发布吗？").setOkListener((DialogBuilder.DialogInterface dialog, int which) -> {
                            dialog.dismiss();
                            getViewModel().cancelOrder(data.getOrderId());
                        });
                showDialog(dialogBuilder);
                break;
            }
            case "修改运价": {
                getViewModel().modifyOrder(getActivity(), data.getOrderId());
                break;
            }
            case "处理变更": {
                OrderChangeHandleActivity.start(getContext(), data.getOrderId());
                break;
            }
            case "变更信息": {
                getViewModel().queryChangeState(getActivity(), data);
                break;
            }
            case "签订协议": {
                WaybillContractDetailActivity.start(getContext(), data.getOrderId(), true, true, false);
                break;
            }
            case "确认收货": {
                SingleReturnedOrderConfirmActivityV3.jumpPage(getActivity(), data.getOrderId());
                break;
            }
            case "支付运费": {
                if (TextUtils.equals(data.getInvoiceType(), "2")) {
                    //不开票
                    new OrderPayBillTypeDialog().setOnPayBlock(payType -> {
                        if (TextUtils.equals(payType, "1")) {
                            DialogBuilder dialogBuilder = new DialogBuilder();
                            dialogBuilder.setMessage("确认后，运单完结线上不再展示支付运费入口，司机会收到已完成运费支付的提示，若后续司机反馈未收到运费，核实后将会影响您再平台后续发单及其他权益，请谨慎操作");
                            dialogBuilder.setTitle("提示");
                            dialogBuilder.setHideCancel(false);
                            dialogBuilder.setOKTextListener("确定", (dialogInterface, i) -> {
                                dialogInterface.dismiss();
                                // 立即支付
                                getViewModel(WaybillModel.class).execute(false, new ReqTmsConsignorOfflinePayment(mData.getOrderId()), rsp -> {
                                    if (rsp.success()) {
                                        mSwipeRefreshMoreLayout.onAutoRefresh();
                                    } else {
                                        showDialogToast(rsp.getMsg());
                                    }
                                });
                            });
                            showDialog(dialogBuilder);
                        } else {
                            getViewModel(WaybillModel.class).consignorConfirmPay(getActivity(), data.getOrderId());
                        }
                        return null;
                    });
                } else {
                    getViewModel(WaybillModel.class).consignorConfirmPay(getActivity(), data.getOrderId());
                }
                break;
            }
            case "再来一单": {
                // TMS 再来一单
                IDeliverProvider deliver = DeliverProvider.deliver;
                if (deliver != null) {
                    deliver.openDeliverDraftsEditActivityV1(
                            WaybillListFragment.this,
                            data.getOrderId(),
                            data.getSpecifyFlag(),
                            0x101,
                            data.getGoodsSource()
                    );
                }
                break;
            }
            case "申请开票": {
                AMainServer.getPluginServer().openInvoiceCenterActivity(getActivity());
                break;
            }
            case "违约申请": {
                // 违约申请   非指定 && 预付 违约提示 检查
                this.getViewModel().checkNospecify(data);
                break;
            }
            case "处理违约": {
                //违约列表
                OrderViolateListActivity.startContentUI(getActivity(), data.getOrderId(), data.getSpecifyFlag(), data.getGoodsSource());
                break;
            }
            case "预估运输时间": {
                // 预估运输时间
                new OrderEstimatedShippingTimeDialog(data.getOrderId(), getViewModel(BaseViewModel.class)).show(WaybillListFragment.this);
                break;
            }
        }
    }

    private void selectTime(TextView tvTime) {
        this.periodFlag = "1";
        final List<String> items = new ArrayList<>(7);
        //1近三个月运单  2019 2019已完结运单 2018 2018已完结运单 2017 2017已完结运单 2016 2016已完结运单 2015 2015已完结运单
        items.add("近三个月");
        Calendar calendar = Calendar.getInstance();
        for (int i = calendar.get(Calendar.YEAR); i >= 2015; i--) {
            items.add(String.valueOf(i));
        }
        tvTime.setOnClickListener((View v) -> MenuDialogV1.instance(items).setTitle("请选择").setClick((o, index) -> {
            periodFlag = index == 0 ? "1" : items.get(index);
            tvTime.setText(index == 0 ? items.get(index) : items.get(index) + "年");
            mSwipeRefreshMoreLayout.onAutoRefresh();
            return null;

        }).show(getActivity()));
    }

    @LiveDataMatch
    public void showBusinessEntity(List<EBusinessEntity> list, boolean show) {
        if (list == null || list.size() <= 1) {
            tv_entity.setVisibility(View.GONE);
            return;
        }
        tv_entity.setVisibility(View.VISIBLE);
        if (!show) {
            //第一次判断显示
            return;
        }
        List<String> item = new ArrayList<>(list.size());
        for (EBusinessEntity entity : list) {
            item.add(entity.getUpSubsidiaryShortName());
        }

        new SettlementFilterDialog(getActivity(), item, new SettlementFilterDialog.ItemOnClick() {
            @Override
            public void onSelectedText(String selectedStr, int position) {
                tv_entity.setText(selectedStr);
                businessEntity = list.get(position).getConsignorSubsidiaryId();
                mSwipeRefreshMoreLayout.onAutoRefresh();
            }

            @Override
            public void dismiss() {

            }
        }).show(tv_entity);
    }

    @LiveDataMatch(tag = "刷新运单列表")
    public void onWaybillPageList(PageList<EWaybill> data) {
        this.mSwipeRefreshMoreLayout.onRefreshCompale(data);
        if (TextUtils.equals(EWaybill.TYPE_COMPLETE, queryType) && tv_select != null) {
            //全部
            String content = tv_select.getText().toString();
            if (content.contains(" ")) {
                content = content.split(" ")[0];
            }
            if (data != null && data.getTotalSize() > 0) {
                content = content + " (" + data.getTotalSize() + ")";
            }
            tv_select.setTextColor(Color.BLUE);
            tv_select.setText(content);
        }
    }

    @Override
    public void onItemClick(BaseQuickAdapter adapter, View view, int position) {
        // 进入运单详情界面
        final EWaybill data = (EWaybill) adapter.getItem(position);
        this.onOpenWaybillDetail(data);
    }

    @Override
    public void onItemChildClick(BaseQuickAdapter adapter, View view, int position) {

        final EWaybill data = (EWaybill) adapter.getItem(position);
        int id = view.getId();
        if (view.getId() == R.id.tvPresent2) {
            //查看预挂单原因
            DialogBuilder dialogBuilder = new DialogBuilder();
            assert data != null;
            dialogBuilder.setMessage(data.getOrderPresetDto().getPresetRejectReason());
            dialogBuilder.setTitle("提示");
            dialogBuilder.setHideCancel(true);
            dialogBuilder.setOKTextListener("我知道了", (dialogInterface, i) -> dialogInterface.dismiss());
            showDialog(dialogBuilder);
        } else if (view.getId() == R.id.tv_toast) {
            assert data != null;
            if (TextUtils.equals(TYPE_DELIVERMINE, data.getOrderQueryType())) {
                //我的发布
                if (!TextUtils.isEmpty(data.getFailReason())) {
                    DialogBuilder dialogBuilder = new DialogBuilder();
                    dialogBuilder.setMessage(data.getFailReason());
                    dialogBuilder.setTitle("提示");
                    dialogBuilder.setHideCancel(true);
                    dialogBuilder.setOKTextListener("我知道了", (dialogInterface, i) -> dialogInterface.dismiss());
                    showDialog(dialogBuilder);
                } else if (TextUtils.equals("45", data.getOrderCurrentStateId()) || TextUtils.equals("26", data.getOrderCurrentStateId())) {
                    //挂价中
                    DialogBuilder dialogBuilder = new DialogBuilder();
                    dialogBuilder.setMessage("您的运单信息正在人工确认中，预计10分钟内审核完毕，请耐心等待");
                    dialogBuilder.setTitle("提示");
                    dialogBuilder.setHideCancel(true);
                    dialogBuilder.setOKTextListener("我知道了", (dialogInterface, i) -> dialogInterface.dismiss());
                    showDialog(dialogBuilder);
                }
            } else {
                if (TextUtils.equals("16", data.getOrderCurrentStateId())) {
                    ReturnedOrderConfirmListActivity.start(getContext(), 0, "");
                } else if (TextUtils.equals("28", data.getOrderCurrentStateId())) {
                    //待结算
                    SettlementApplicationListActivity.start(getContext(), 0, "");
                } else if (TextUtils.equals("45", data.getOrderCurrentStateId()) || TextUtils.equals("26", data.getOrderCurrentStateId())) {
                    //挂价中
                    DialogBuilder dialogBuilder = new DialogBuilder();
                    dialogBuilder.setMessage("您的运单信息正在人工确认中，预计10分钟内审核完毕，请耐心等待");
                    dialogBuilder.setTitle("提示");
                    dialogBuilder.setHideCancel(true);
                    dialogBuilder.setOKTextListener("我知道了", (dialogInterface, i) -> dialogInterface.dismiss());
                    showDialog(dialogBuilder);
                }
            }

        } else if (id == R.id.tv_copy || id == R.id.tv_order) {
            //复制order
            UtilTool.setCopyText(getContext(), "运单号", data.getOrderId());
            showToast("复制成功");
        } else if (id == R.id.tv_copy_n) {
            UtilTool.setCopyText(getContext(), "车牌号码", data.getPlateNumber());
            showToast("复制车牌号码成功");
        } else if (id == R.id.tv_handle_exception) {
            if (data.isTmsOrder()) {
                // TMS极速货源 运单运单异常处理
                EOrderListExceptionDtoList exceptionDtoList = data.getOrderListExceptionDtoList().get(0);
                //异常类型 1 违约 2 挂价驳回  3 变更 4 过期取消
                if (TextUtils.equals("1", exceptionDtoList.getExceptionType())) {
                    OrderViolateListActivity.startContentUI(getActivity(), data.getOrderId(), data.getSpecifyFlag(), data.getGoodsSource());
                }

            } else {
                RxBusEventManager.postEvent(new EventWaybillException("jump"));
            }

        } else if (id == R.id.tv_see_details) {
            WaybillExceptionInfo exceptionInfo = data.getExceptionInfo();
            if (exceptionInfo != null) {
                String exceptionName = exceptionInfo.getExceptionName();
                DialogBuilder dialogBuilder = new DialogBuilder();
                dialogBuilder.setMessage(exceptionName);
                dialogBuilder.setOkListener((dialogInterface, i) -> {
                    dialogInterface.dismiss();
                });
                showDialog(dialogBuilder);
            }
        } else if (id == R.id.btn_qr) {
            SeniorOrderWaybillQRCodeActivity.start(getActivity(), data.getOrderId());
        } else if (id == R.id.cbChosenReturnedOrder) {
            // 左上角选择按钮
            ImageView ivSelect = view.findViewById(R.id.cbChosenReturnedOrder);
            selectItem(ivSelect, data, adapter, position);
            adapter.notifyItemChanged(position);
        }
        //适配器显示与点击事件 WLHY-6239 需求给优化了
//        else if (id == R.id.tvSpecialAmount) {
//            // 特惠运力
//            IDeliverProvider deliver = DeliverProvider.deliver;
//            if (deliver != null) {
//                deliver.openDeliverNormalChooseActivityV1(
//                        this,
//                        data.getOrderId(),
//                        false,
//                        REQUEST_NORMAL_CHOOSE_V1
//                );
//            }
//
//            ZStatistics.onEvent("com.zczy.cargo_owner.order.WaybillListFragment", "tvSpecialAmount", new OnAddBusinessKV() {
//                @Override
//                public void put(Map<String, Object> map) throws Exception {
//                    Map vaule = new HashMap();
//                    vaule.put("orderId", orderId);
//                    map.put("", vaule);
//                }
//            });
//        }

    }

    private void selectItem(ImageView ivSelect, EWaybill data, BaseQuickAdapter adapter,
                            int position) {
        if (ivSelect.isSelected()) {
            data.setSelected(false);
            selectList.remove(data);
        } else {
            selectList.add(data);
            data.setSelected(true);
        }
        if (selectList.size() >= 1) {
            rl_all_select.setVisibility(View.VISIBLE);
            if (TextUtils.equals(queryType, TYPE_DELIVERMINE)) {
                //我的发布
                ViewUtil.setVisible(tvDeleteBatch, true);
            } else {
                ViewUtil.setVisible(tvDeleteBatch, false);
            }
            showSelectNum();
        } else {
            rl_all_select.setVisibility(View.GONE);
        }
        cb_all_select.setChecked(selectList.size() == adapter.getData().size());
    }

    private void showSelectNum() {
        SpannableStringBuilder ssb = new SpannableStringBuilder();
        SpannableString ssp1 = new SpannableString("全选(已选");
        SpannableString ssp2 = new SpannableString(selectList.size() + "条");
        ssp2.setSpan(new ForegroundColorSpan(Color.parseColor("#FA6400")), 0, ssp2.length(), Spanned.SPAN_EXCLUSIVE_EXCLUSIVE);
        SpannableString ssp3 = new SpannableString(")");
        ssb.append(ssp1).append(ssp2).append(ssp3);
        cb_all_select.setText(ssb);
        orderId = "";
        for (int i = 0; i < selectList.size(); i++) {
            orderId += selectList.get(i).getOrderId() + ",";
        }
    }

    private void onOpenWaybillDetail(EWaybill data) {

        //  打开运单详情
        WaybillDetailStatueActivity.start(getContext(), data.getOrderId());
    }


    /***
     * 我的发布
     */
    private void onItemDelivermine(EWaybill data, String title) {
        mData = data;
        switch (title) {
            case "删除": {
                // 删除
                DialogBuilder dialogBuilder = new DialogBuilder()
                        .setMessage("确认删除该运单吗？").setOkListener((DialogBuilder.DialogInterface dialog, int which) -> {
                            dialog.dismiss();
                            getViewModel().deleteConsignorOrder(data.getOrderId());
                        });
                showDialog(dialogBuilder);
                break;
            }

            case "重新发布": {
                // 重新发布
                getViewModel(WaybillModel.class).republishOrder(data.getOrderId());
                break;
            }

            case "再来一单": {
                // 再来一单
                IDeliverProvider deliver = DeliverProvider.deliver;
                if (deliver != null) {
                    deliver.openDeliverDraftsEditActivity(
                            WaybillListFragment.this,
                            data.getOrderId(),
                            data.getSpecifyFlag(),
                            0x101,
                            data.getGoodsSource()
                    );
                }
                postViewEvent("tv_age_waybill", data.getOrderId());
                break;
            }

            case "取消发布": {
                // 取消发布
                DialogBuilder dialogBuilder = new DialogBuilder()
                        .setMessage("确认取消发布吗？").setOkListener((DialogBuilder.DialogInterface dialog, int which) -> {
                            dialog.dismiss();
                            getViewModel().cancelConsignorOrder(data.getOrderId());
                        });
                showDialog(dialogBuilder);
                postViewEvent("tv_cancle_waybill", data.getOrderId());
                break;
            }

            case "选择承运方": {
                //选择承运方
                IDeliverProvider deliver = DeliverProvider.deliver;
                if (deliver != null) {
                    deliver.openDeliverNormalChooseActivity(
                            WaybillListFragment.this,
                            JsonUtil.toJson(data),
                            0x100);
                }
                break;
            }

            case "变更信息": {
                //变更信息
                OrderChangeDeliverInfoActivity.start(getActivity(), data.getOrderId(), data.getOrderQueryType());
                postViewEvent("tv_change_waybill", data.getOrderId());
                break;
            }

            case "请平台找车": {
                //请平台找车
                if (data.isHelpMatchVehicleFlag()) {
                    getViewModel().queryDispatchMobile(data.getOrderId());
                } else {
                    DialogBuilder dialogBuilder = new DialogBuilder();
                    dialogBuilder.setTitle("提示");
                    dialogBuilder.setMessage("平台正在帮忙找车，无需重复操作，请耐心等待。");
                    dialogBuilder.setHideCancel(true);
                    dialogBuilder.setOKText("我知道了");
                    dialogBuilder.setGravity(Gravity.LEFT);
                    dialogBuilder.setOkListener((dialog, which) -> dialog.dismiss());
                    showDialog(dialogBuilder);
                    return;
                }
                break;
            }

            case "设置自定义编号": {
                //设置自定义编号
                orderId = data.getOrderId();
                if (isJiDongAccount) {
                    getViewModel().queryJiDongOrderCode(data.getOrderId(), periodFlag);
                } else {
                    showDialog(1, false);
                }
                break;
            }
            case "发货单上传": {
                WaybillShipmentsActivity.start(getContext(), data.getOrderId());
                break;
            }
            case "合同补签": {
                WaybillContractDetailActivity.start(getActivity(), data.getOrderId(), true);
                break;
            }
            case "去回单": {
                //回单确认
                ReturnedOrderConfirmListActivity.start(getContext(), 0, "");
                break;
            }
            case "去结算": {
                //结算申请
                SettlementApplicationListActivity.start(getContext(), 0, "");
                break;
            }
            case "修改运价": {
                // 修改运价
                WayBillModifyPriceActivity.jumpPage(getContext(), data.getOrderId());
                break;
            }
            case "押回单地址变更": {
                // 押回单地址变更
                ReceiptAddressChangeActivity.start(WaybillListFragment.this, data.getOrderId());
            }
            case "预估运输时间": {
                // 预估运输时间
                new OrderEstimatedShippingTimeDialog(data.getOrderId(), getViewModel(BaseViewModel.class)).show(WaybillListFragment.this);
            }
            case "变更装卸货范围": {
                //  变更装卸货范围
                OrderChangeScopeInfoActivity.start(getContext(), data.getOrderId(), "1");
            }
            default: {

            }
        }
    }

    @Override
    public void onActivityResult(int requestCode, int resultCode, Intent data) {
        super.onActivityResult(requestCode, resultCode, data);
        if ((requestCode == 0x100 || requestCode == 0x101 || requestCode == 0x27) && resultCode == Activity.RESULT_OK) {
            this.mSwipeRefreshMoreLayout.onAutoRefresh();
        }
    }

    @RxBusEvent(from = "发新货成功")
    public void onEventNewGoodsSuccess(EventNewGoodsSuccess data) {
        this.mSwipeRefreshMoreLayout.onAutoRefresh();
    }

    /***
     * 待发货
     * @param data
     */
    private void onItemDelayShipment(EWaybill data, String title) {
        switch (title) {
            case "变更信息": {
                // 变更信息
                OrderChangeDeliverInfoActivity.start(getContext(), data.getOrderId(), data.getOrderQueryType());
                break;
            }

            case "违约申请": {
                // 违约申请 非指定 && 预付 违约提示 检查
                this.getViewModel().checkNospecify(data);
                break;
            }

            case "联系司机": {
                // 联系司机
                PhoneUtil.callPhone(getContext(), data.getDriverMobile());
                break;
            }

            case "查看定位": {
                // 联系司机
                WaybillTrackingActivityV1.startUI(getContext(), data.getOrderId(), data.getPlateNumber());
                break;
            }

            case "设置自定义编号": {
                //设置自定义编号
                orderId = data.getOrderId();
                if (isJiDongAccount) {
                    getViewModel().queryJiDongOrderCode(data.getOrderId(), periodFlag);
                } else {
                    showDialog(1, false);
                }
                break;
            }
            case "发货单上传": {
                WaybillShipmentsActivity.start(getContext(), data.getOrderId());
                break;
            }
            case "合同补签": {
                WaybillContractDetailActivity.start(getActivity(), data.getOrderId(), true);
                break;
            }
            case "去回单": {
                //回单确认
                ReturnedOrderConfirmListActivity.start(getContext(), 0, "");
                break;
            }
            case "去结算": {
                //结算申请
                SettlementApplicationListActivity.start(getContext(), 0, "");
                break;
            }
            case "修改运价": {
                // 修改运价
                WayBillModifyPriceActivity.jumpPage(getContext(), data.getOrderId());
                break;
            }
            case "押回单地址变更": {
                // 押回单地址变更
                ReceiptAddressChangeActivity.start(WaybillListFragment.this, data.getOrderId());
                break;
            }
            case "预估运输时间": {
                // 预估运输时间
                new OrderEstimatedShippingTimeDialog(data.getOrderId(), getViewModel(BaseViewModel.class)).show(WaybillListFragment.this);
                break;
            }
            case "变更装卸货范围": {
                //  变更装卸货范围
                OrderChangeScopeInfoActivity.start(getContext(), data.getOrderId(), "1");
            }
            default: {

            }
        }
    }

    /***
     * 运输中
     * @param data
     */
    private void onItemUnload(EWaybill data, String title) {
        switch (title) {
            case "变更信息": {
                // 变更信息
                OrderChangeDeliverInfoActivity.start(getContext(), data.getOrderId(), data.getOrderQueryType());
                break;
            }

            case "联系司机": {
                // 联系司机
                PhoneUtil.callPhone(getContext(), data.getDriverMobile());
                break;
            }

            case "违约申请": {
                // 违约申请   非指定 && 预付 违约提示 检查
                this.getViewModel().checkNospecify(data);
                break;
            }

            case "在途跟踪": {
                // 在途跟踪
                WaybillTrackingActivityV1.startUI(getContext(), data.getOrderId(), data.getPlateNumber());
                break;
            }

            case "设置自定义编号": {
                //设置自定义编号
                orderId = data.getOrderId();
                if (isJiDongAccount) {
                    getViewModel().queryJiDongOrderCode(data.getOrderId(), periodFlag);
                } else {
                    showDialog(1, false);
                }
                break;
            }
            case "发货单上传": {
                WaybillShipmentsActivity.start(getContext(), data.getOrderId());
                break;
            }
            case "合同补签": {
                WaybillContractDetailActivity.start(getActivity(), data.getOrderId(), true);
                break;
            }
            case "去回单": {
                //回单确认
                ReturnedOrderConfirmListActivity.start(getContext(), 0, "");
                break;
            }
            case "去结算": {
                //结算申请
                SettlementApplicationListActivity.start(getContext(), 0, "");
                break;
            }
            case "修改运价": {
                // 修改运价
                WayBillModifyPriceActivity.jumpPage(getContext(), data.getOrderId());
                break;
            }
            case "押回单地址变更": {
                // 押回单地址变更
                ReceiptAddressChangeActivity.start(WaybillListFragment.this, data.getOrderId());
                break;
            }
            case "预估运输时间": {
                // 预估运输时间
                new OrderEstimatedShippingTimeDialog(data.getOrderId(), getViewModel(BaseViewModel.class)).show(WaybillListFragment.this);
                break;
            }
            case "变更装卸货范围": {
                //  变更装卸货范围
                OrderChangeScopeInfoActivity.start(getContext(), data.getOrderId(), "1");
            }
            default: {

            }
        }
    }

    /***
     * 已完成
     * @param data
     */
    private void onItemComplete(EWaybill data, String title) {
        switch (title) {
            case "变更信息": {
                // 变更信息
                OrderChangeDeliverInfoActivity.start(getContext(), data.getOrderId(), data.getOrderQueryType());
                break;
            }

            case "违约申请": {
                // 违约申请   非指定 && 预付 违约提示 检查
                this.getViewModel().checkNospecify(data);
                break;
            }

            case "评价": {
                //  评价
                AMainServer pluginServer = AMainServer.getPluginServer();
                if (pluginServer != null) {
                    pluginServer.jumpEvaluate(getContext(), data.getOrderId());
                }
                break;
            }

            case "查看评价": {
                AMainServer pluginServer = AMainServer.getPluginServer();
                if (pluginServer != null) {
                    //查看评价
                    pluginServer.jumpEvaluateDetails(getContext(), data.getOrderId());
                }
                break;
            }

            case "联系司机": {
                //联系司机
                PhoneUtil.callPhone(getContext(), data.getDriverMobile());
                break;
            }

            case "轨迹回放": {
                // 6.轨迹回放
                WaybillTrackingActivityV1.startUI(getContext(), data.getOrderId(), data.getPlateNumber());
                break;
            }

            case "设置自定义编号": {
                //设置自定义编号
                orderId = data.getOrderId();
                if (isJiDongAccount) {
                    getViewModel().queryJiDongOrderCode(data.getOrderId(), periodFlag);
                } else {
                    showDialog(1, false);
                }
                break;
            }
            case "合同补签": {
                WaybillContractDetailActivity.start(getActivity(), data.getOrderId(), true);
                break;
            }
            case "去回单": {
                //回单确认
                if (TextUtils.equals(data.getBackStatus(), "3")) {
                    OperationConfig operationConfig = new OperationConfig();
                    operationConfig.setShowItemBottomButton(View.GONE);
                    operationConfig.setShowAllSelectOrderLayout(View.VISIBLE);
                    operationConfig.setShowWaitingForProcessingButton(View.VISIBLE);
                    operationConfig.setShowRejectedButton(View.VISIBLE);
                    operationConfig.setShowApplyBreachOfContractBtn(View.VISIBLE);
                    operationConfig.setHoverItemBottomButton(true);
                    startSingleReturnedOrderConfirmActivityV2(data.getOrderId(), data.getDetailId(), TYPE_NOT_CONFIRM, operationConfig);
                } else {
                    ReturnedOrderConfirmListActivity.start(getContext(), 0, "");
                }
                break;
            }
            case "去结算": {
                //结算申请
                SettlementApplicationListActivity.start(getContext(), 0, "");
                break;
            }
            case "修改运价": {
                // 修改运价
                WayBillModifyPriceActivity.jumpPage(getContext(), data.getOrderId());
                break;
            }
            case "押回单地址变更": {
                // 押回单地址变更
                ReceiptAddressChangeActivity.start(WaybillListFragment.this, data.getOrderId());
                break;
            }
            case "预估运输时间": {
                // 预估运输时间
                new OrderEstimatedShippingTimeDialog(data.getOrderId(), getViewModel(BaseViewModel.class)).show(WaybillListFragment.this);
                break;
            }
            case "变更装卸货范围": {
                //  变更装卸货范围
                OrderChangeScopeInfoActivity.start(getContext(), data.getOrderId(), "1");
            }
            default: {

            }
        }
    }

    private void startSingleReturnedOrderConfirmActivityV2(String orderId, String detailId, String queryType, OperationConfig operationConfig) {
        Intent intent = new Intent(getActivity(), SingleReturnedOrderConfirmActivityV2.class);
        intent.putExtra(SingleReturnedOrderConfirmActivityV2.ORDER_ID, orderId);
        intent.putExtra(SingleReturnedOrderConfirmActivityV2.DETAIL_ID, detailId);
        intent.putExtra(RETURNED_ORDER_QUERY_TYPE, queryType);
        intent.putExtra(OPERATION_CONFIG, operationConfig);
        startActivityForResult(intent, 0x27);
    }

    @RxBusEvent(from = "修改运价")
    public void onModifyPriceSuccess(RxBusModifyOrder data) {
        if (data.getSuccess()) {
            mSwipeRefreshMoreLayout.onAutoRefresh();
        }
    }

    @RxBusEvent(from = "刷新列表")
    public void onNeedRefresh(RxBusReFreshOrderList data) {
        if (data.getSuccess()) {
            mSwipeRefreshMoreLayout.onAutoRefresh();
        }
    }

    @LiveDataMatch(tag = "违约申请检查")
    public void onViolateSuccess(boolean isExitSize, EWaybill data) {
        if (isExitSize) {
            //违约列表
            OrderViolateListActivity.startContentUI(getActivity(), data.getOrderId(), data.getSpecifyFlag(), data.getGoodsSource());
        } else {
            if (data.isTmsOrder()) {
                OrderViolateAddTMSActivity.startContentUI(getActivity(), data.getOrderId(), false, data.getSpecifyFlag(), data.getGoodsSource(), 1001);
            } else {
                //新增违约
                OrderViolateAddActivity.startContentUI(getActivity(), data.getOrderId(), false, data.getSpecifyFlag(), data.getGoodsSource(), 1001);
            }
        }
    }

    @LiveDataMatch
    public void queryDispatchMobile(RespQueryDispatchMobile data) {
        // 请平台找车
        DialogBuilder dialogBuilder = new DialogBuilder();
        View inflater = getActivity().getLayoutInflater().inflate(R.layout.custom_phone_dialog, null);
        TextView tvContent = inflater.findViewById(R.id.tvContent);
        EditText et_phone = inflater.findViewById(R.id.et_phone);
        et_phone.setText(data.getDelistConsultMobile());
        dialogBuilder.setTitle("提示");
        tvContent.setOnClickListener(new View.OnClickListener() {
            @Override
            public void onClick(View v) {
                PhoneUtil.callPhoneSoon(getActivity(), data.getDispatchMobile());
            }
        });
        tvContent.setText(Html.fromHtml("请在下方留下您的联系方式，您的<br />运单专属调度人员" + "<font color='#5086FC'> " + data.getDispatchMobile() + "</font>" + "会<br />" + "与您联系。"));
        //添加调度企业微信
        View ly_weixin = inflater.findViewById(R.id.ly_weixin);
        ly_weixin.setVisibility(TextUtils.isEmpty(data.getActivityId()) ? View.GONE : View.VISIBLE);
        ly_weixin.setOnClickListener(new View.OnClickListener() {
            @Override
            public void onClick(View v) {
                // 打开微信 后需唤起微信小程序的页面打开页面
                String path = "/pages/index/index?urlIdData=ims_ent_wechat_activity," + data.getActivityId() + "&form=Android&type=Cargo";
                AMainServer.getPluginServer().openWeiXinChat(getContext(), path);
            }
        });
        dialogBuilder.setView(inflater);
        dialogBuilder.setCancelable(false);
        dialogBuilder.setHideCancel(false);
        dialogBuilder.setCancelTextListener("取消", (dialogInterface, i) -> dialogInterface.dismiss());
        dialogBuilder.setOKTextListener("确认", (dialogInterface, i) -> {
            if (TextUtils.isEmpty(et_phone.getText().toString().trim())) {
                showToast("请输入联系方式");
                return;
            }
            getViewModel().helpMatchVehicle(mData.getOrderId(), data.getDispatchMobile(), et_phone.getText().toString().trim(), data.getDispatchId());
            dialogInterface.dismiss();
        });
        showDialog(dialogBuilder);

    }

    @LiveDataMatch
    public void queryConsignorConfirmPayMoney(RespConsignorConfirmPayMoney respConsignorConfirmPayMoney) {
        new OrderPayBillDialog(respConsignorConfirmPayMoney.getMoney())
                .setPayBlock((orderPayBillDialog, view) -> {
                    // 立即支付
                    getViewModel(WaybillModel.class).execute(false, new ReqTmsConsignorConfirmPay(mData.getOrderId()), rsp -> {
                        if (rsp.success()) {
                            RspTmsConsignorConfirmPay data = rsp.getData();
                            orderPay(data);
                        } else {
                            showDialogToast(rsp.getMsg());
                        }
                    });
                    return null;
                }).show(WaybillListFragment.this);
    }

    private void orderPay(RspTmsConsignorConfirmPay data) {
        if (data != null) {
            ReqPayCheck reqPayCheck = new ReqPayCheck();
            reqPayCheck.setApplyBatchNo(data.getModel());
            reqPayCheck.setConsignorSettleMoney(data.getConsignorSettleMoney());
            reqPayCheck.setCarrierSettleMoney(data.getCarrierSettleMoney());
            reqPayCheck.setCheckMoney(data.getConsignorSettleMoney());
            reqPayCheck.setSubsidiaryId(data.getSubsidiaryId());
            getViewModel(WayBillModelV1.class).payCheck(reqPayCheck, getContext(), WaybillListFragment.this);
        }
    }

    @LiveDataMatch
    public void payCheckSuccess(ReqPayCheck reqPayCheck) {
        new PayDialog(WaybillListFragment.this.getContext(), reqPayCheck.getConsignorSettleMoney(), (keyPwd, dialog) -> {
            ReqFreightPayment reqFreightPayment = new ReqFreightPayment();
            reqFreightPayment.setPassword(Md5Util.mmd5(keyPwd));
            reqFreightPayment.setApplyBatchNo(reqPayCheck.getApplyBatchNo());
            reqFreightPayment.setConsignorSettleMoney(reqPayCheck.getConsignorSettleMoney());
            reqFreightPayment.setCarrierSettleMoney(reqPayCheck.getCarrierSettleMoney());
            reqFreightPayment.setOrderId(mData.getOrderId());
            ELogin login = CommServer.getUserServer().getLogin();
            reqFreightPayment.setConsignorUserId(login.getUserId());
            reqFreightPayment.setConsignorCustomerId(login.getCustomerId());
            getViewModel(WayBillModelV1.class).freightPayment(reqFreightPayment, (dealType, errorMsg) -> {
                requireActivity().runOnUiThread(() -> {
                    if (TextUtils.equals("1", dealType)) {
                        //支付成功
                        mSwipeRefreshMoreLayout.onAutoRefresh();
                        dialog.dismiss();
                    } else if (TextUtils.equals("1", dealType)) {
                        //多次输入锁定
                        dialog.showLockMsg(errorMsg);
                    } else {
                        dialog.dismiss();
                    }
                });
                return null;
            });
        }).show(mRoot);
    }

    @LiveDataMatch
    public void helpMatchVehicleSuccess() {
        showToast("操作成功");
        mSwipeRefreshMoreLayout.onAutoRefresh();
    }

    public void search(@NotNull String key) {
        this.title = key;
        this.mSwipeRefreshMoreLayout.onAutoRefresh();
    }


    @LiveDataMatch(tag = "冀东--修改自定义编号")
    public void onEditSelfCommentSuccess(String msg) {
        showToast(msg);
        if (selectJiDongOrderCodeDialog != null) {
            selectJiDongOrderCodeDialog.dismiss();
        }
        this.mSwipeRefreshMoreLayout.onAutoRefresh();

    }


    @LiveDataMatch(tag = "冀东--批量修改自定义编号")
    public void onBatchEditSelfCommentSuccess(String msg) {
        showToast(msg);
        if (selectJiDongOrderCodeDialog != null) {
            selectJiDongOrderCodeDialog.dismiss();
        }
        this.mSwipeRefreshMoreLayout.onAutoRefresh();
    }

    @RxBusEvent(from = "冀东--修改其他自定义编号")
    public void onRxUserInfoSuccess(EJiDongOrderCodeHandle msg) {
        if (TextUtils.equals(msg.getHandel(), "refresh")) {
            this.mSwipeRefreshMoreLayout.onAutoRefresh();
        }
    }

    @LiveDataMatch
    public void onCancelConsignorOrder() {
        this.mSwipeRefreshMoreLayout.onAutoRefresh();
    }

    @LiveDataMatch
    public void onCancelConsignorOrderTms() {
        this.mSwipeRefreshMoreLayout.onAutoRefresh();
    }

    @LiveDataMatch
    public void onCancelViolateSuccess(String orderId) {
        // 取消运单进入违约申请
        OrderViolateListActivity.startContentUI(getActivity(), orderId, waybill.getSpecifyFlag(), waybill.getGoodsSource(), true);
    }

    @RxBusEvent(from = "刷新 运单 数据")
    public void onEventRefreshWaybillList(EventRefreshWaybillList data) {
        if (queryType.equals(data.getQueryType()) || TextUtils.equals("WaybillDetailFragment", data.getQueryType())) {
            this.mSwipeRefreshMoreLayout.onAutoRefresh();
        }
    }

    @LiveDataMatch
    public void onDeleteSuccess() {
        this.mSwipeRefreshMoreLayout.onAutoRefresh();
    }

    @LiveDataMatch(tag = "冀东--查询采购订单号")
    public void queryJiDongOrderCodeSuccess(PageList<EJiDongOrderCode> data) {
        showDialog(data, false);
    }

    @LiveDataMatch(tag = "冀东--查询采购订单号--批量")
    public void queryJiDongOrderCodeBatchSuccess(PageList<EJiDongOrderCode> data) {
        showDialog(data, true);
    }

    @LiveDataMatch(tag = "冀东账号判断")
    public void showJiDongMenu(boolean show) {
        isJiDongAccount = show;
        waybillAdapter.setJiDongFlag(show);
    }

    @LiveDataMatch(tag = "批量删除运单")
    public void deleteBatchOrderSuccess(String msg) {
        showToast(msg);
        this.mSwipeRefreshMoreLayout.onAutoRefresh();
    }

    /**
     * 冀东--选择自定义编号dialog
     *
     * @param data
     * @param b2
     */
    private void showDialog(PageList<EJiDongOrderCode> data, boolean b2) {
        EJiDongOrderCode eJiDongOrderCode = new EJiDongOrderCode();
        eJiDongOrderCode.setOrderCode("其他");
        data.getRootArray().add(eJiDongOrderCode);
        selectJiDongOrderCodeDialog = new SelectJiDongOrderCodeDialog();
        if (!jdDialogUpdateTag) {
            selectJiDongOrderCodeDialog.setListener(new SelectJiDongOrderCodeDialog.SelectJiDongOrderCodeDialogListener() {
                @Override
                public void selectItem(String posStr, boolean isBatch) {
                    if (containsEmoji(posStr)) {
                        showDialogToast("请不要输入表情符号！");
                        return;
                    }
                    if (isBatch) {
                        //批量设置
                        boolean b = orderId.endsWith(",");
                        if (b) {
                            orderId = orderId.substring(0, orderId.length() - 1);
                        }
                        getViewModel().batchEditSelfComment(orderId, posStr, periodFlag);
                    } else {
                        //单个设置
                        getViewModel().editSelfComment(orderId, posStr);
                    }
                }

                @Override
                public void noSelect() {
                    showDialogToast("请选择自定义编号");
                }

                @Override
                public void isUpdate(boolean b) {
                    if (b) {
                        jdDialogUpdateTag = b;
                        getViewModel().queryJiDongOrderCode(orderId, periodFlag);
                    }
                }
            }).setData(data, orderId, 1, b2, periodFlag).show(getActivity());
        } else {
            showToast("更新成功!");
            jdDialogUpdateTag = false;
            selectJiDongOrderCodeDialog.setData(data, orderId, 1, b2, periodFlag);
        }
    }

    /**
     * 冀东--选择自定义编号dialog
     *
     * @param size
     * @param b2
     */
    private void showDialog(int size, boolean b2) {
        WayBillCustomNumberingDialog.instance(b2, size, (content, isBatch) -> {
                    if (containsEmoji(content)) {
                        showDialogToast("请不要输入表情符号！");
                        return;
                    }
                    if (isBatch) {
                        //批量设置
                        boolean b = orderId.endsWith(",");
                        if (b) {
                            orderId = orderId.substring(0, orderId.length() - 1);
                        }
                        Objects.requireNonNull(getViewModel()).batchEditSelfComment(orderId, content, periodFlag);
                    } else {
                        //单个设置
                        Objects.requireNonNull(getViewModel()).editSelfComment(orderId, content);
                    }
                }
        ).show(this);
    }

    /**
     * 判断字符串中是否含有表情
     *
     * @param source
     * @return
     */
    public boolean containsEmoji(String source) {
        int len = source.length();
        boolean isEmoji = false;
        for (int i = 0; i < len; i++) {
            char hs = source.charAt(i);
            if (0xd800 <= hs && hs <= 0xdbff) {
                if (source.length() > 1) {
                    char ls = source.charAt(i + 1);
                    int uc = ((hs - 0xd800) * 0x400) + (ls - 0xdc00) + 0x10000;
                    if (0x1d000 <= uc && uc <= 0x1f77f) {
                        return true;
                    }
                }
            } else {
                // non surrogate
                if (0x2100 <= hs && hs <= 0x27ff && hs != 0x263b) {
                    return true;
                } else if (0x2B05 <= hs && hs <= 0x2b07) {
                    return true;
                } else if (0x2934 <= hs && hs <= 0x2935) {
                    return true;
                } else if (0x3297 <= hs && hs <= 0x3299) {
                    return true;
                } else if (hs == 0xa9 || hs == 0xae || hs == 0x303d
                        || hs == 0x3030 || hs == 0x2b55 || hs == 0x2b1c
                        || hs == 0x2b1b || hs == 0x2b50 || hs == 0x231a) {
                    return true;
                }
                if (!isEmoji && source.length() > 1 && i < source.length() - 1) {
                    char ls = source.charAt(i + 1);
                    if (ls == 0x20e3) {
                        return true;
                    }
                }
            }
        }
        return isEmoji;
    }

    @Override
    public void onClick(View v) {
        int id = v.getId();
        if (id == R.id.tvGoBatchOrderManage) {
            //进入批量货管理
            IDeliverProvider deliver = DeliverProvider.deliver;
            if (deliver != null) {
                deliver.openDeliverBatchManageActivity(WaybillListFragment.this);
            }
        } else if (id == R.id.cb_all_select) {
            if (cb_all_select.isChecked()) {
                waybillAdapter.selectAll();
                selectList.clear();
                selectList.addAll(waybillAdapter.getData());
                showSelectNum();
            } else {
                waybillAdapter.unSelectAll();
                selectList.clear();
                showSelectNum();
                rl_all_select.setVisibility(View.GONE);
            }
        } else if (id == R.id.tv_setting) {
            if (isJiDongAccount) {
                boolean b = orderId.endsWith(",");
                if (b) {
                    orderId = orderId.substring(0, orderId.length() - 1);
                }
                getViewModel().queryJiDongOrderCodeBatch(orderId, periodFlag);
            } else {
                showDialog(selectList.size(), true);
            }
        } else if (id == R.id.tvDeleteBatch) {
            //批量删除
            ReqDeleteBatchOrder req = new ReqDeleteBatchOrder();
            List<String> list = new ArrayList<>();
            List<String> list1 = new ArrayList<>();
            if (selectList == null || selectList.size() == 0) {
                showToast("请选择运单");
                return;
            }
            for (int i = 0; i < selectList.size(); i++) {
                EWaybill eWaybill = selectList.get(i);
                if (eWaybill.getButtons().isDelete()) {
                    list.add(eWaybill.getOrderId());
                }
                list1.add(eWaybill.getOrderId());
            }
            req.setOrderIds(list1);
            DialogBuilder dialogBuilder = new DialogBuilder();
            dialogBuilder.setTitle("提示");
            dialogBuilder.setMessage("是否批量删除" + list.size() + "条运单?\n删除后的运单可在pc端回收站查看");
            dialogBuilder.setOkListener((dialog, which) -> {
                Objects.requireNonNull(getViewModel()).deleteBatchOrder(req);
                dialog.dismiss();
            });
            showDialog(dialogBuilder);
        }
    }

    private void postViewEvent(String tableId, String orderId) {
        ZStatistics.onEvent(this.getClass().getName(), this.getClass().getSimpleName() + "#" + tableId, new OnAddBusinessKV() {
            @Override
            public void put(Map<String, Object> map) throws Exception {
                Map<String, String> requestParam = new HashMap<>(1);
                requestParam.put("orderId", orderId);
                map.put("requestParam", requestParam);
                map.put(EventKeyType.EVENT.value(), EventType.ON_VIEW.value());
            }
        });
    }

    @Override
    public void onRefresh() {
        if (this.mSwipeRefreshMoreLayout != null) {
            this.mSwipeRefreshMoreLayout.onAutoRefresh();
        }
    }

    @LiveDataMatch
    public void onQueryChangeState(BaseRsp<RspQueryChangeState> res) {
        OrderChangeDeliverInfoActivity.start(getContext(), mData.getOrderId(), mData.getOrderCurrentStateId(), true);
    }

    @RxBusEvent(from = "tms取消变更")
    public void onEvent(RxChangeCancel event) {
        this.mSwipeRefreshMoreLayout.onAutoRefresh();
    }
}
