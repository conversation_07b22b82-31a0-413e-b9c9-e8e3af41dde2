package com.zczy.cargo_owner.order.billing.req

import com.zczy.comm.http.entity.BaseNewRequest
import com.zczy.comm.http.entity.BaseRsp
import com.zczy.comm.http.entity.ResultData

/**
 * 功能描述:  结算申请提交到待审核接口-app
 * <AUTHOR>
 * @date 2022/6/29-14:45
 */
class ReqSettleApplyAuditReturn(
    var detailIds: String? = null
) : BaseNewRequest<BaseRsp<ResultData>>("oms-app/settle/apply/consignor/settleApplyAuditReturn")