package com.zczy.cargo_owner.order.transport.req

import com.zczy.cargo_owner.order.transport.bean.PathInTransitBean
import com.zczy.comm.http.entity.BaseNewRequest
import com.zczy.comm.http.entity.BaseRsp
import com.zczy.comm.http.entity.PageList

/** 功能描述:
 *
 * <AUTHOR> 贾发展
 * @company 中储智运科技股份有限公司
 * @copyright （版权）中储南京智慧物流科技有限公司所有
 * @date 2019/6/20
 */

class ReqPathInTransitPage(var pageSize: Int = 10,
                           var nowPage: Int = 1,
                           var orderId: String = ""
) : BaseNewRequest<BaseRsp<PageList<PathInTransitBean>>>("oms-app/order/consignor/pathInTransitPage")