package com.zczy.cargo_owner.order.express.req

import com.zczy.comm.http.entity.BaseNewRequest
import com.zczy.comm.http.entity.BaseRsp
import com.zczy.comm.http.entity.ResultData
import java.io.Serializable


data class ReqEnsureExpressSign(
    var poundOrderNum: String = ""
) : BaseNewRequest<BaseRsp<RespEnsureExpressSign>>("oms-app/order/express/consignorPoundSign")

data class RespEnsureExpressSign(
    val type: String? = null,
    val returnMsg: String? = null,
    val list: MutableList<ListData>? = null
) : ResultData(), Serializable

data class ListData(
    val type: Int? = null,  //0：签收失败，1：签收成功
    val poundOrderNum: String? = null,  //磅单号
    val errorMsg: String? = null,   //	失败信息
    val errCode: String? = null     //1:重复添加
) : Serializable