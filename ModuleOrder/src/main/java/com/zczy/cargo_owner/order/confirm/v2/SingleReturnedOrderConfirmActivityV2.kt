package com.zczy.cargo_owner.order.confirm.v2

import android.annotation.SuppressLint
import android.app.Activity
import android.content.Intent
import android.graphics.Color
import android.os.Bundle
import android.os.Handler
import android.text.Editable
import android.text.InputFilter
import android.text.TextUtils
import android.text.TextWatcher
import android.util.TypedValue
import android.view.View
import android.view.ViewGroup
import android.widget.Button
import android.widget.EditText
import android.widget.ImageView
import android.widget.LinearLayout
import android.widget.RadioButton
import android.widget.RadioGroup
import android.widget.RelativeLayout
import android.widget.TextView
import android.widget.Toast
import androidx.constraintlayout.widget.ConstraintLayout
import androidx.core.content.ContextCompat
import androidx.drawerlayout.widget.DrawerLayout
import androidx.recyclerview.widget.LinearLayoutManager
import androidx.recyclerview.widget.RecyclerView
import com.sfh.lib.event.RxBusEvent
import com.sfh.lib.event.RxBusEventManager
import com.sfh.lib.mvvm.annotation.LiveDataMatch
import com.sfh.lib.mvvm.service.BaseViewModel
import com.sfh.lib.rx.ui.UtilRxView
import com.sfh.lib.ui.dialog.DialogBuilder
import com.sfh.lib.utils.UtilTool
import com.zczy.cargo_owner.libcomm.config.HttpConfig
import com.zczy.cargo_owner.libcomm.utils.showWipeTypeV3
import com.zczy.cargo_owner.libcomm.utils.toDoubleRoundDownString
import com.zczy.cargo_owner.libcomm.widget.ImageSelectViewV1
import com.zczy.cargo_owner.order.R
import com.zczy.cargo_owner.order.confirm.AddAndDeductMoneyActivity
import com.zczy.cargo_owner.order.confirm.ReqGetVideoPath
import com.zczy.cargo_owner.order.confirm.RespGetVideoPath
import com.zczy.cargo_owner.order.confirm.ReturnedOrderConfirmListFragment
import com.zczy.cargo_owner.order.confirm.ReturnedOrderConfirmModel
import com.zczy.cargo_owner.order.confirm.ReturnedOrderConfirmSuccessActivity
import com.zczy.cargo_owner.order.confirm.ReturnedOrderDeniedReasonActivity
import com.zczy.cargo_owner.order.confirm.UploadReturnedOrderPhotoActivity
import com.zczy.cargo_owner.order.confirm.UploadReturnedOrderPhotoActivityV1
import com.zczy.cargo_owner.order.confirm.UploadReturnedOrderPhotoActivityV2
import com.zczy.cargo_owner.order.confirm.adapter.ReceiptContainerAdapter
import com.zczy.cargo_owner.order.confirm.adapter.UploadReturnedOrderPhotoAdapterV1
import com.zczy.cargo_owner.order.confirm.bean.AgreeAdjustDetailBean
import com.zczy.cargo_owner.order.confirm.bean.AgreeAdjustDetailReq
import com.zczy.cargo_owner.order.confirm.bean.AgreeAdjustReq
import com.zczy.cargo_owner.order.confirm.bean.ExceptionConfigTypeEnum
import com.zczy.cargo_owner.order.confirm.bean.OperationConfig
import com.zczy.cargo_owner.order.confirm.bean.QueryLoadStateByDetailIdsRes
import com.zczy.cargo_owner.order.confirm.bean.ReqQueryConsignorRepulseTag
import com.zczy.cargo_owner.order.confirm.bean.ReqQueryReceiptLossRiseDetailInfoByAppChangeRuleId
import com.zczy.cargo_owner.order.confirm.bean.ReqReturnQueryReceiptLossRiseRule
import com.zczy.cargo_owner.order.confirm.bean.ReturnedOrderBatchConfirmRes
import com.zczy.cargo_owner.order.confirm.bean.ReturnedOrderDetailBean
import com.zczy.cargo_owner.order.confirm.bean.ReturnedOrderDetailBeanV1
import com.zczy.cargo_owner.order.confirm.bean.ReturnedOrderDetailReq
import com.zczy.cargo_owner.order.confirm.bean.ReturnedOrderUpdatePhotoReq
import com.zczy.cargo_owner.order.confirm.bean.RspAddAndDeductMoney
import com.zczy.cargo_owner.order.confirm.bean.RspQueryReceiptLossRiseDetailInfoByAppChangeRuleId
import com.zczy.cargo_owner.order.confirm.bean.RspReturnQueryReceiptLossRiseRule
import com.zczy.cargo_owner.order.confirm.bean.RxAgreeAdjust
import com.zczy.cargo_owner.order.confirm.bean.RxBusUploadImgSuccess
import com.zczy.cargo_owner.order.confirm.bean.RxBusUploadImgSuccessV1
import com.zczy.cargo_owner.order.confirm.bean.RxBusUploadImgSuccessV2
import com.zczy.cargo_owner.order.confirm.bean.SingleReturnedOrderConfirmMsgReq
import com.zczy.cargo_owner.order.confirm.bean.SingleReturnedOrderConfirmMsgRes
import com.zczy.cargo_owner.order.confirm.bean.SingleReturnedOrderConfirmReq
import com.zczy.cargo_owner.order.confirm.bean.WaitingProcessingReq
import com.zczy.cargo_owner.order.confirm.bean.WatermarkPic
import com.zczy.cargo_owner.order.confirm.bean.differenceV1
import com.zczy.cargo_owner.order.confirm.bean.getDeliverCargoCategoryStr
import com.zczy.cargo_owner.order.confirm.bean.getDeliverCargoCategoryStrV1
import com.zczy.cargo_owner.order.confirm.bean.getDeliverWeightStr
import com.zczy.cargo_owner.order.confirm.bean.getReturnMoneyTime
import com.zczy.cargo_owner.order.confirm.bean.getSettleRate
import com.zczy.cargo_owner.order.confirm.bean.isShowCheckMonth
import com.zczy.cargo_owner.order.confirm.bean.showCarrierDeliverRemark
import com.zczy.cargo_owner.order.confirm.bean.showCarrierReceiveRemark
import com.zczy.cargo_owner.order.confirm.bean.showContainerNum
import com.zczy.cargo_owner.order.confirm.bean.showDeliverTime
import com.zczy.cargo_owner.order.confirm.bean.showEditV1
import com.zczy.cargo_owner.order.confirm.bean.showEditV2
import com.zczy.cargo_owner.order.confirm.bean.showReceiveTime
import com.zczy.cargo_owner.order.confirm.bean.showReturnMoneyTime
import com.zczy.cargo_owner.order.confirm.bean.showRule
import com.zczy.cargo_owner.order.confirm.bean.showVideo1
import com.zczy.cargo_owner.order.confirm.bean.showVideo2
import com.zczy.cargo_owner.order.confirm.computeMoney
import com.zczy.cargo_owner.order.confirm.dialog.AgreeAdjustDetailDialog
import com.zczy.cargo_owner.order.confirm.dialog.OrderDeductionDeployDialogs
import com.zczy.cargo_owner.order.confirm.dialog.OrderEditRecordDialog
import com.zczy.cargo_owner.order.confirm.dialog.ReturnOrderReasonChooseDialog
import com.zczy.cargo_owner.order.confirm.dialog.ReturnedOrderConfirmDialog
import com.zczy.cargo_owner.order.confirm.widget.InputViewCheckV4
import com.zczy.cargo_owner.order.confirm.widget.ReturnReseonsDialog
import com.zczy.cargo_owner.order.confirm.widget.SmallKuiDunCalcView
import com.zczy.cargo_owner.order.confirm.widget.computeMoney1
import com.zczy.cargo_owner.order.confirm.widget.computeMoney2
import com.zczy.cargo_owner.order.confirm.widget.getMoneyRateText
import com.zczy.cargo_owner.order.confirm.widget.getTurnMoney
import com.zczy.cargo_owner.order.confirm.widget.roundTo2DecimalPlaces
import com.zczy.cargo_owner.order.confirm.widget.roundTo2DecimalPlacesV1
import com.zczy.cargo_owner.order.model.ReqDeductionDeploy
import com.zczy.cargo_owner.order.model.ReqExceptionTypeByOrderId
import com.zczy.cargo_owner.order.model.ReqOrderExceptionNotToReceiptFlag
import com.zczy.cargo_owner.order.model.RspConsignorSmartDeficitTonDefaultRuleList
import com.zczy.cargo_owner.order.model.RspExceptionTypeByOrderId
import com.zczy.cargo_owner.order.violate.OrderViolateAddActivity
import com.zczy.comm.CommServer
import com.zczy.comm.SpannableHepler
import com.zczy.comm.data.OrderPriceEnum
import com.zczy.comm.data.entity.EImage
import com.zczy.comm.data.getOrderPriceList
import com.zczy.comm.http.entity.BaseRsp
import com.zczy.comm.http.entity.ResultData
import com.zczy.comm.pluginserver.AMainServer
import com.zczy.comm.ui.BaseActivity
import com.zczy.comm.utils.NumUtil
import com.zczy.comm.utils.dp2px
import com.zczy.comm.utils.ex.getFormatTime
import com.zczy.comm.utils.ex.isNull
import com.zczy.comm.utils.ex.isTrue
import com.zczy.comm.utils.ex.setVisible
import com.zczy.comm.utils.ex.toDoubleRoundDownString
import com.zczy.comm.utils.ex.yes
import com.zczy.comm.utils.imageselector.ImageSelectView
import com.zczy.comm.utils.json.toJson
import com.zczy.comm.videoplayer.VideoPlayActivity
import com.zczy.comm.widget.dialog.ChooseDialogV1
import com.zczy.comm.widget.inputv2.InputViewCheckV2
import com.zczy.comm.widget.inputv2.InputViewClick
import com.zczy.comm.widget.itemdecoration.SpaceItemDecoration
import com.zczy.comm.widget.pickerview.CustomTopBar
import com.zczy.comm.widget.pickerview.picker.BasePicker
import com.zczy.comm.widget.pickerview.picker.TimePicker
import com.zczy.comm.widget.pickerview.widget.DefaultCenterDecoration
import com.zczy.comm.widget.pickerview.widget.PickerView
import com.zczy.comm.x5.X5WebActivity
import kotlinx.android.synthetic.main.order_confirm_returned_info_layout.isexaminePicUrlArr
import kotlinx.android.synthetic.main.order_confirm_returned_info_layout.isexaminePicUrlArrImage
import kotlinx.android.synthetic.main.order_confirm_returned_info_layout.tvexaminePicUrlArr
import kotlinx.android.synthetic.main.order_returned_settlement_info_layout.confirmJiaKouInfo
import kotlinx.android.synthetic.main.order_returned_settlement_info_layout.tvJiaKouKuanXiang
import kotlinx.android.synthetic.main.order_returned_settlement_info_layout.tvTaxIncludedPrice
import kotlinx.android.synthetic.main.order_returned_settlement_info_layout.tvTaxNotIncludedPrice
import kotlinx.android.synthetic.main.order_returned_settlement_info_layout.view.recyclerViewContainer
import kotlinx.android.synthetic.main.order_returned_single_confirm_activity_v2.btnAgreeAdjust
import kotlinx.android.synthetic.main.order_returned_single_confirm_activity_v2.btnReject
import kotlinx.android.synthetic.main.order_returned_single_confirm_activity_v2.btnSingleConfirm
import kotlinx.android.synthetic.main.order_returned_single_confirm_activity_v2.btnUploadPhoto
import kotlinx.android.synthetic.main.order_returned_single_confirm_activity_v2.btnWaitingProcessing
import kotlinx.android.synthetic.main.order_returned_single_confirm_activity_v2.clOrderCustomNumber
import kotlinx.android.synthetic.main.order_returned_single_confirm_activity_v2.cl_isBackUp
import kotlinx.android.synthetic.main.order_returned_single_confirm_activity_v2.dclInfo
import kotlinx.android.synthetic.main.order_returned_single_confirm_activity_v2.deniedInfo
import kotlinx.android.synthetic.main.order_returned_single_confirm_activity_v2.drawerLayout
import kotlinx.android.synthetic.main.order_returned_single_confirm_activity_v2.edit_code
import kotlinx.android.synthetic.main.order_returned_single_confirm_activity_v2.endDrawer
import kotlinx.android.synthetic.main.order_returned_single_confirm_activity_v2.layoutBtn
import kotlinx.android.synthetic.main.order_returned_single_confirm_activity_v2.layoutConfirmBillingInfo
import kotlinx.android.synthetic.main.order_returned_single_confirm_activity_v2.layoutConfirmBillingInfoHzImg
import kotlinx.android.synthetic.main.order_returned_single_confirm_activity_v2.layoutConfirmDeliveryInfo
import kotlinx.android.synthetic.main.order_returned_single_confirm_activity_v2.layoutConfirmReturnedInfo
import kotlinx.android.synthetic.main.order_returned_single_confirm_activity_v2.orderCustomNumber1
import kotlinx.android.synthetic.main.order_returned_single_confirm_activity_v2.platformInfo
import kotlinx.android.synthetic.main.order_returned_single_confirm_activity_v2.returned_order_info_view
import kotlinx.android.synthetic.main.order_returned_single_confirm_activity_v2.rp_evidence_img
import kotlinx.android.synthetic.main.order_returned_single_confirm_activity_v2.tvWeightExceptionMsg
import kotlinx.android.synthetic.main.order_returned_single_confirm_activity_v2.tv_code_size
import kotlinx.android.synthetic.main.order_returned_single_confirm_activity_v2.tv_code_size_v1
import kotlinx.android.synthetic.main.order_returned_single_confirm_activity_v2.view_code
import java.math.BigDecimal
import kotlin.math.absoluteValue

/**
 *描述：单个回单确认页面
 *auth:宋双朋
 *time:2024/6/11 9:59
 */
@SuppressLint("SetTextI18n")
open class SingleReturnedOrderConfirmActivityV2 : BaseActivity<ReturnedOrderConfirmModel>(), AgreeAdjustDetailDialog.AgreeAdjustCallback {

    private lateinit var orderId: String
    private lateinit var detailId: String //明细id
    private lateinit var queryType: String
    private var backorderreconsiderstate: String? = ""
    private var lastUptTime: String? = null
    private var consignorSmartDeficitTonUseFlag = ""  //货主是否同意使用

    // 发货信息
    private lateinit var tvDeliveryNum: TextView
    private lateinit var isDeliveryRecyclerViewCargo: RecyclerView
    private lateinit var tvShippingModification: TextView
    private lateinit var tvDocumentPhotoV2: TextView//货主上传单据照片
    private lateinit var tvCarrierAlreadyEdited: TextView//司机是否修改
    private lateinit var isvDeliveryImageRlV2: RelativeLayout//单据照片
    private lateinit var isvDeliveryImageV2: ImageSelectView//单据照片
    private lateinit var isvDeliveryImage: ImageSelectView//单据照片
    private lateinit var isvDeliveryTime: TextView//发货信息-上传时间
    private lateinit var isvDeliveryImageRl: RelativeLayout//单据照片
    private lateinit var tvDocumentPhoto: TextView//单据照片
    private lateinit var isvDeliveryImageV1: ImageSelectView//运单照片
    private lateinit var isvDeliveryRemark: TextView//发货备注
    private lateinit var isvDeliveryImageRlV1: RelativeLayout//运单照片
    private lateinit var tvDocumentPhotoV1: TextView//运单照片
    private lateinit var inputReceiptContainer: InputViewClick//实际发货 集装箱
    private lateinit var iv_video_guide: ImageView
    private lateinit var tvDocumentPhotoV3: TextView//发货挂车车牌照片
    private lateinit var isvDeliveryImageRlV3: RelativeLayout//发货挂车车牌照片
    private lateinit var isvDeliveryImageV3: ImageSelectView//发货挂车车牌照片
    private lateinit var orderReturnAiDocuments: TextView//ai发货单据号
    private lateinit var inputAiOrderNum: EditText//确认的发货单据号
    private lateinit var inputAiOrderNumWarning: TextView
    private lateinit var viewAiOrder: LinearLayout


    // 回单信息
    private lateinit var tvReceiptNum: TextView
    private lateinit var isvReturnImage: ImageSelectView
    private lateinit var inputReceiptInformationContainer: InputViewClick//实际收货 集装箱
    private lateinit var tvReceiptDocumentPhoto: TextView
    private lateinit var tvReceiptModification: TextView
    private lateinit var tvReceiptCarrierAlreadyEdited: TextView
    private lateinit var tvReceiptDocumentPhotoV2: TextView
    private lateinit var isReceiptReturnImageRlV2: RelativeLayout
    private lateinit var isReceiptReturnImageV2: ImageSelectView
    private lateinit var isvReturnImageV1: ImageSelectView
    private lateinit var isvReturnVideoV1: ImageSelectViewV1
    private lateinit var isvDeliveryVideoV1: ImageSelectViewV1
    private lateinit var isvReturnRemark: TextView//收货备注
    private lateinit var ivReceiptDocumentPhotoSwitch: TextView
    private lateinit var ivDocumentPhotoSwitch: TextView
    private lateinit var isvReturnImageRlV1: RelativeLayout
    private lateinit var isvReturnImageRl: RelativeLayout
    private lateinit var isvReturnTime: TextView
    private lateinit var jmyl_warning: ConstraintLayout
    private lateinit var tv_warning_jmyl: TextView
    private lateinit var tvReceiptDocumentPhotoV1: TextView
    private lateinit var inputOutStageTime: InputViewClick
    private lateinit var clOutStageTime: ConstraintLayout
    private lateinit var isReceiptRecyclerViewCargo: RecyclerView // 收货货物明细
    private lateinit var tvReceiptDocumentPhotoV3: TextView//卸货挂车车牌照片
    private lateinit var isvReceiptReturnImageRlV3: RelativeLayout//卸货挂车车牌照片
    private lateinit var isvReceiptReturnImageV3: ImageSelectView//卸货挂车车牌照片

    //拍照卸货图片
    private lateinit var rl_clockin: RelativeLayout
    private lateinit var image_clockin: ImageSelectView

    //打回原因
    private lateinit var tvReturnReason: TextView

    //待处理原因
    private lateinit var tvdclReason: TextView

    // 结算信息
    private lateinit var etConfirmSettlementTonnage: EditText
    private lateinit var orderSettleTypeUnitEditText: EditText
    private lateinit var orderSettleType: TextView
    private lateinit var orderSettleTypeUnit: TextView
    private lateinit var etConfirmSettlementPriceIncludedTax: EditText
    private lateinit var etConfirmSettlementPriceNotIncludedTax: EditText
    private lateinit var etConsignorNoRateUnitShowMoney: EditText
    private lateinit var tvCheckMonth: TextView
    private lateinit var cl_order_textview12_2: ConstraintLayout
    private lateinit var orderSettlementBasis: TextView
    private lateinit var tvLossTonnageCalc: TextView
    private lateinit var tvExpiryDateStr: TextView
    private lateinit var tvExpiryDate: TextView
    private lateinit var tvJiaKouKuanXiangUnit: TextView
    private lateinit var recyclerViewContainer: RecyclerView
    private var smallLabel: SmallKuiDunCalcView? = null
    private val mReceiptContainerAdapter: ReceiptContainerAdapter = ReceiptContainerAdapter()

    // 抹零选项
    private lateinit var inputViewWipeZero: InputViewCheckV2
    private lateinit var inputViewWipeZeroValue: InputViewClick
    private lateinit var llViewWipeZero: LinearLayout
    private lateinit var clWipeZero: ConstraintLayout
    private lateinit var viewWipeMoney1: TextView // 抹零后金额 (含税)
    private lateinit var viewWipeMoney2: TextView // 抹零后金额 (承运方预估到手价)

    // ************** 亏吨计算器  *****************//
    /**
     *  发货
     */
    private lateinit var editShippingTonnage: EditText

    /**
     *  收货
     */
    private lateinit var editReceiptTonnage: EditText

    /**
     *  磅差 = 发货 - 收货
     */
    private lateinit var tvPoundDifference: TextView

    /**
     *  允许亏吨率 = 允许亏吨量 / 发货
     */
    private lateinit var editAllowableDeficitRate: EditText

    /**
     *  允许亏吨量 = 允许亏吨率 * 发货
     */
    private lateinit var editAllowableLoss: EditText

    /**
     *  超出亏吨量 = if ((磅差 - 允许亏吨量) < 0) 0 else (磅差 - 允许亏吨量)
     */
    private lateinit var tvExceedingTheAmountOfLoss: TextView

    /**
     *  允许亏吨扣款系数 默认为0
     */
    private lateinit var editAllowDeductibleDeductionFactor: EditText

    /**
     *  允许亏吨扣款 = 允许亏吨扣款系数 * 允许亏吨量
     */
    private lateinit var editAllowDeductibleDeductions: EditText

    /**
     *  超出亏吨扣款系数（货值单价），后台返回
     */
    private lateinit var editExceedingTheDeductibleDeductionUnit: EditText

    /**
     *  超出亏吨扣款 = 超出亏吨扣款系数 * 超出亏吨量
     */
    private lateinit var editExceedingTheDeductibleDeduction: EditText

    private lateinit var tvPriceIncludeTax: TextView
    private lateinit var tvPriceNoTax: TextView
    private lateinit var radioGroup: RadioGroup
    private lateinit var rbIncludeTax: RadioButton
    private lateinit var rbNoIncludeTax: RadioButton
    private lateinit var btnReset: Button
    private lateinit var btnCloseDrawer: Button

    //亏吨后结算价格含税
    private var mTaxIncludedPrice = ""
    private var mReturnedOrderDetailBean: ReturnedOrderDetailBean? = null
    private var addAndDeductMoneyList: MutableList<RspAddAndDeductMoney> = mutableListOf()

    //平台审核信息
    private lateinit var tvVerifyResult: TextView
    private lateinit var tvDeal: TextView
    private lateinit var tvSuggestionResult: TextView
    private lateinit var clSuggestion: ConstraintLayout
    private lateinit var tvAbnormalTrajectoryResult: TextView
    private lateinit var tvAbnormalTrajectoryDeal: TextView
    private lateinit var clAbnormalTrajectory: ConstraintLayout

    //货主回单信息
    private lateinit var isvReturnImageHz: ImageSelectViewV1
    private lateinit var order_hz_img_up: TextView
    private lateinit var order_hz_img_textview: TextView
    private lateinit var order_hz_divider: View

    //回单确认修改弹窗
    private lateinit var ll_edit_record: LinearLayout

    private var mReleaseLossTonData = ""
    private val handler: Handler = Handler()
    private val delayRun = Runnable {
        queryRuleId()
    }
    private val mUploadReturnedOrderPhotoAdapterV11 = UploadReturnedOrderPhotoAdapterV1()
    private val mUploadReturnedOrderPhotoAdapterV12 = UploadReturnedOrderPhotoAdapterV1()
    private var mReturnedOrderDetailBeanV1 = ReturnedOrderDetailBeanV1() // 抹零价格主体
    override fun getLayout(): Int {
        return R.layout.order_returned_single_confirm_activity_v2
    }

    override fun bindView(bundle: Bundle?) {
        btnReject.setOnClickListener {
            // 打回
            reject()
        }
        btnWaitingProcessing.setOnClickListener {
            ReturnReseonsDialog(this) { dialog, yesNo, reson ->
                dialog?.dismiss()
                if (yesNo) {
                    //调用上传原因
                    viewModel?.submitWaitingProcessing(
                        WaitingProcessingReq(
                            detailId,
                            reson.toString()
                        )
                    )
                } else {
                    viewModel?.submitWaitingProcessing(WaitingProcessingReq(detailId, ""))
                }
            }.show()
        }
        btnSingleConfirm.setOnClickListener {
            val b1 =
                mReturnedOrderDetailBean?.releaseLossTonData?.consignorSmartDeficitTonFlag?.isTrue
                    ?: false
            val replace11 = tvReceiptNum.text.toString().trim()
                .replace(mReturnedOrderDetailBean?.getDeliverCargoCategoryStr() ?: "", "")
            val replace21 = tvDeliveryNum.text.toString().trim()
                .replace(mReturnedOrderDetailBean?.getDeliverCargoCategoryStr() ?: "", "")
            val b2 = !TextUtils.equals(replace11, replace21)
            val b3 = smallLabel?.getCheck() ?: false
            val b4 = TextUtils.isEmpty(smallLabel?.getRuleItem())
            if (b1 && b2 && b3 && b4) {
                val dialogBuilder = DialogBuilder()
                dialogBuilder.message = "请选择亏涨吨规则!"
                dialogBuilder.title = "提示"
                dialogBuilder.isHideCancel = true
                dialogBuilder.setOKText("知道了")
                showDialog(dialogBuilder)
                return@setOnClickListener
            }
            val msg = mReturnedOrderDetailBean.isShowCheckMonth(etConfirmSettlementPriceNotIncludedTax.text.toString().trim())
            if (msg.isNotEmpty()) {
                val dialogBuilder = DialogBuilder()
                dialogBuilder.title = "提示"
                dialogBuilder.message = msg
                dialogBuilder.isHideCancel = false
                dialogBuilder.setOKText("确定")
                dialogBuilder.setOkListener { dialog, _ ->
                    dialog.dismiss()
                    viewModel?.queryLoadStateByDetailIds(
                        detailId = detailId,
                        consignorSmartDeficitTonUseRateType = smallLabel?.getConsignorSmartDeficitTonUseRateType(),
                        consignorSmartDeficitTonUseFlag = consignorSmartDeficitTonUseFlag,
                        consignorSmartDeficitTonCargoUnitMoney = mReturnedOrderDetailBean?.cargoUnitMoney
                    )
                }
                showDialog(dialogBuilder)
            } else {
                viewModel?.queryLoadStateByDetailIds(
                    detailId = detailId,
                    consignorSmartDeficitTonUseRateType = smallLabel?.getConsignorSmartDeficitTonUseRateType(),
                    consignorSmartDeficitTonUseFlag = consignorSmartDeficitTonUseFlag,
                    consignorSmartDeficitTonCargoUnitMoney = mReturnedOrderDetailBean?.cargoUnitMoney
                )
            }
        }
        btnAgreeAdjust.setOnClickListener {
            // 同意调整
            viewModel?.agreeAdjustDetail(AgreeAdjustDetailReq(detailId, "2"))
        }
        //发货信息
        tvDeliveryNum = layoutConfirmDeliveryInfo.findViewById(R.id.tvDeliveryNum)
        isDeliveryRecyclerViewCargo =
            layoutConfirmDeliveryInfo.findViewById(R.id.isDeliveryRecyclerViewCargo)
        mUploadReturnedOrderPhotoAdapterV11.canEdit = false
        isDeliveryRecyclerViewCargo.apply {
            layoutManager = LinearLayoutManager(this@SingleReturnedOrderConfirmActivityV2)
            adapter = mUploadReturnedOrderPhotoAdapterV11
        }
        tvShippingModification = layoutConfirmDeliveryInfo.findViewById(R.id.tvShippingModification)
        isvDeliveryImageV2 = layoutConfirmDeliveryInfo.findViewById(R.id.isvDeliveryImageV2)
        isvDeliveryImageRlV2 = layoutConfirmDeliveryInfo.findViewById(R.id.isvDeliveryImageRlV2)
        tvDocumentPhotoV2 = layoutConfirmDeliveryInfo.findViewById(R.id.tvDocumentPhotoV2)
        tvCarrierAlreadyEdited = layoutConfirmDeliveryInfo.findViewById(R.id.tvCarrierAlreadyEdited)
        isvDeliveryImage = layoutConfirmDeliveryInfo.findViewById(R.id.isvDeliveryImage)
        isvDeliveryTime = layoutConfirmDeliveryInfo.findViewById(R.id.isvDeliveryTime)
        orderReturnAiDocuments = layoutConfirmDeliveryInfo.findViewById(R.id.orderReturnAiDocuments)
        inputAiOrderNum = layoutConfirmDeliveryInfo.findViewById(R.id.inputAiOrderNum)
        inputAiOrderNum.filters = arrayOf(InputFilter.LengthFilter(30))
        UtilRxView.afterTextChangeEvents(inputAiOrderNum, 800) { result ->
            inputAiOrderNumWarning.setVisible(result.isEmpty())
        }
        inputAiOrderNumWarning = layoutConfirmDeliveryInfo.findViewById(R.id.inputAiOrderNumWarning)
        viewAiOrder = layoutConfirmDeliveryInfo.findViewById(R.id.viewAiOrder)
        isvDeliveryImageRl = layoutConfirmDeliveryInfo.findViewById(R.id.isvDeliveryImageRl)
        tvDocumentPhoto = layoutConfirmDeliveryInfo.findViewById(R.id.tvDocumentPhoto)
        ivDocumentPhotoSwitch = layoutConfirmDeliveryInfo.findViewById(R.id.ivDocumentPhotoSwitch)
        ivReceiptDocumentPhotoSwitch = layoutConfirmReturnedInfo.findViewById(R.id.ivReceiptDocumentPhotoSwitch)
        isvDeliveryImageV1 = layoutConfirmDeliveryInfo.findViewById(R.id.isvDeliveryImageV1)
        isvDeliveryRemark = layoutConfirmDeliveryInfo.findViewById(R.id.isvDeliveryRemark)
        isvDeliveryVideoV1 = layoutConfirmDeliveryInfo.findViewById(R.id.isvDeliveryVideoV1)
        isvDeliveryVideoV1.isVideo = true
        isvDeliveryImageRlV1 = layoutConfirmDeliveryInfo.findViewById(R.id.isvDeliveryImageRlV1)
        tvDocumentPhotoV1 = layoutConfirmDeliveryInfo.findViewById(R.id.tvDocumentPhotoV1)
        tvDocumentPhotoV3 = layoutConfirmDeliveryInfo.findViewById(R.id.tvDocumentPhotoV3)
        isvDeliveryImageRlV3 = layoutConfirmDeliveryInfo.findViewById(R.id.isvDeliveryImageRlV3)
        isvDeliveryImageV3 = layoutConfirmDeliveryInfo.findViewById(R.id.isvDeliveryImageV3)



        inputReceiptContainer = layoutConfirmDeliveryInfo.findViewById(R.id.inputReceiptContainer)
        inputReceiptContainer.setListener(object : InputViewClick.Listener() {
            override fun onClick(viewId: Int, view: InputViewClick, content: String) {
                // 实际发货集装箱
                ReturnedOrderContainerListActivityV1.jumpUi(
                    context = this@SingleReturnedOrderConfirmActivityV2,
                    orderId = orderId
                )
            }
        })
        ivReceiptDocumentPhotoSwitch.setOnClickListener {
            val driverReceiveProofPicJsonArr =
                mReturnedOrderDetailBean?.driverReceiveProofPicJsonArr ?: emptyList()
            val driverReceivePhotoPicJsonArr =
                mReturnedOrderDetailBean?.driverReceivePhotoPicJsonArr ?: emptyList()
            when (ivReceiptDocumentPhotoSwitch.text) {
                "切换原图" -> {
                    //带水印图片 切换成原图
                    driverReceiveProofPicJsonArr.mapNotNull {
                        it.picOrgUrl.takeIf { s -> s.isNotEmpty() }
                            ?.let { url -> EImage(imageId = url) }
                    }.let { list ->
                        isvReturnImage.imgList = ArrayList(list)
                    }
                    driverReceivePhotoPicJsonArr.mapNotNull {
                        it.picOrgUrl.takeIf { s -> s.isNotEmpty() }
                            ?.let { url -> EImage(imageId = url) }
                    }.let { list ->
                        isvReturnImageV1.imgList = ArrayList(list)
                    }
                    ivReceiptDocumentPhotoSwitch.text = "切换默认"
                }

                "切换默认" -> {
                    //原图 切换成带水印图片
                    driverReceiveProofPicJsonArr.mapNotNull {
                        it.picUrl.takeIf { s -> s.isNotEmpty() }
                            ?.let { url -> EImage(imageId = url) }
                    }.let { list ->
                        isvReturnImage.imgList = ArrayList(list)
                    }
                    driverReceivePhotoPicJsonArr.mapNotNull {
                        it.picUrl.takeIf { s -> s.isNotEmpty() }
                            ?.let { url -> EImage(imageId = url) }
                    }.let { list ->
                        isvReturnImageV1.imgList = ArrayList(list)
                    }
                    ivReceiptDocumentPhotoSwitch.text = "切换原图"
                }
            }
        }
        ivDocumentPhotoSwitch.setOnClickListener {
            val driverDeliverProofPicJsonArr =
                mReturnedOrderDetailBean?.driverDeliverProofPicJsonArr ?: emptyList()
            val driverDeliverPhotoPicJsonArr =
                mReturnedOrderDetailBean?.driverDeliverPhotoPicJsonArr ?: emptyList()
            when (ivDocumentPhotoSwitch.text) {
                "切换原图" -> {
                    //带水印图片 切换成原图
                    driverDeliverProofPicJsonArr.mapNotNull {
                        it.picOrgUrl.takeIf { s -> s.isNotEmpty() }
                            ?.let { url -> EImage(imageId = url) }
                    }.let { list ->
                        isvDeliveryImage.imgList = ArrayList(list)
                    }
                    driverDeliverPhotoPicJsonArr.mapNotNull {
                        it.picOrgUrl.takeIf { s -> s.isNotEmpty() }
                            ?.let { url -> EImage(imageId = url) }
                    }.let { list ->
                        isvDeliveryImageV1.imgList = ArrayList(list)
                    }
                    ivDocumentPhotoSwitch.text = "切换默认"
                }

                "切换默认" -> {
                    //原图 切换成带水印图片
                    driverDeliverProofPicJsonArr.mapNotNull {
                        it.picUrl.takeIf { s -> s.isNotEmpty() }
                            ?.let { url -> EImage(imageId = url) }
                    }.let { list ->
                        isvDeliveryImage.imgList = ArrayList(list)
                    }
                    driverDeliverPhotoPicJsonArr.mapNotNull {
                        it.picUrl.takeIf { s -> s.isNotEmpty() }
                            ?.let { url -> EImage(imageId = url) }
                    }.let { list ->
                        isvDeliveryImageV1.imgList = ArrayList(list)
                    }
                    ivDocumentPhotoSwitch.text = "切换原图"
                }
            }
        }
        //实际发货
        inputReceiptContainer.tvTitle.apply {
            setTextColor(
                ContextCompat.getColor(
                    this@SingleReturnedOrderConfirmActivityV2,
                    R.color.color_666
                )
            )
            setTextSize(TypedValue.COMPLEX_UNIT_SP, 13F)
        }
        inputReceiptContainer.tvContent.apply {
            setTextColor(
                ContextCompat.getColor(
                    this@SingleReturnedOrderConfirmActivityV2,
                    R.color.text_33
                )
            )
            setTextSize(TypedValue.COMPLEX_UNIT_SP, 13F)
        }
        iv_video_guide = findViewById(R.id.iv_video_guide)

        val login = CommServer.getUserServer().login
        if (null != login) {
            val reqGetVideoPath = ReqGetVideoPath()
            reqGetVideoPath.showPosition = 3
            when {
                login.relation.isCarrier -> {
                    reqGetVideoPath.targetScope = 2
                }

                login.relation.isBoss -> {
                    reqGetVideoPath.targetScope = 10
                }

                else -> {
                    reqGetVideoPath.targetScope = 3
                }
            }
            viewModel?.getVideoPath(reqGetVideoPath)
        }

        iv_video_guide.visibility = View.GONE

        //平台审核信息
        tvVerifyResult = platformInfo.findViewById(R.id.tv_verify_result)
        tvDeal = platformInfo.findViewById(R.id.tv_deal)
        tvDeal.setOnClickListener {
            // 平台审核结果 去处理
            viewModel?.queryExceptionOrderExamineInfoByOrderId(
                ReqExceptionTypeByOrderId(
                    orderId,
                    "2"
                )
            )
        }
        tvSuggestionResult = platformInfo.findViewById(R.id.tv_suggestion_result)
        clSuggestion = platformInfo.findViewById(R.id.cl_suggestion)
        tvAbnormalTrajectoryResult = platformInfo.findViewById(R.id.tv_abnormal_trajectory_result)
        tvAbnormalTrajectoryDeal = platformInfo.findViewById(R.id.tv_abnormal_trajectory_deal)
        clAbnormalTrajectory = platformInfo.findViewById(R.id.cl_abnormal_trajectory)
        tvAbnormalTrajectoryDeal.setOnClickListener {
            // 轨迹异常 去处理
            viewModel?.queryExceptionOrderExamineInfoByOrderId(
                ReqExceptionTypeByOrderId(
                    orderId,
                    "1"
                )
            )
        }
        //回单信息
        tvReceiptDocumentPhoto = layoutConfirmReturnedInfo.findViewById(R.id.tvReceiptDocumentPhoto)
        tvReceiptModification = layoutConfirmReturnedInfo.findViewById(R.id.tvReceiptModification)
        tvReceiptCarrierAlreadyEdited = layoutConfirmReturnedInfo.findViewById(R.id.tvReceiptCarrierAlreadyEdited)
        tvReceiptDocumentPhotoV2 = layoutConfirmReturnedInfo.findViewById(R.id.tvReceiptDocumentPhotoV2)
        isReceiptReturnImageV2 = layoutConfirmReturnedInfo.findViewById(R.id.isReceiptReturnImageV2)
        isReceiptReturnImageRlV2 = layoutConfirmReturnedInfo.findViewById(R.id.isReceiptReturnImageRlV2)
        tvReceiptNum = layoutConfirmReturnedInfo.findViewById(R.id.tvReceiptNum)
        isvReturnImage = layoutConfirmReturnedInfo.findViewById(R.id.isvReturnImage)
        inputReceiptInformationContainer = layoutConfirmReturnedInfo.findViewById(R.id.inputReceiptInformationContainer)
        inputReceiptInformationContainer.setListener(
            object : InputViewClick.Listener() {
                override fun onClick(viewId: Int, view: InputViewClick, content: String) {
                    // 实际收货集装箱
                    ReturnedOrderContainerListActivityV2.jumpUi(
                        context = this@SingleReturnedOrderConfirmActivityV2,
                        orderId = orderId
                    )
                }
            })
        //实际收货
        inputReceiptInformationContainer.tvTitle.apply {
            setTextColor(
                ContextCompat.getColor(
                    this@SingleReturnedOrderConfirmActivityV2,
                    R.color.color_666
                )
            )
            setTextSize(TypedValue.COMPLEX_UNIT_SP, 13F)
        }
        inputReceiptInformationContainer.tvContent.apply {
            setTextColor(
                ContextCompat.getColor(
                    this@SingleReturnedOrderConfirmActivityV2,
                    R.color.text_33
                )
            )
            setTextSize(TypedValue.COMPLEX_UNIT_SP, 13F)
        }
        isvReturnImage.isNestedScrollingEnabled = false
        isvReturnImage.setHasFixedSize(true)
        isexaminePicUrlArrImage.isNestedScrollingEnabled = false
        isexaminePicUrlArrImage.setHasFixedSize(true)
        tvReceiptDocumentPhotoV1 = layoutConfirmReturnedInfo.findViewById(R.id.tvReceiptDocumentPhotoV1)
        isvReturnImageV1 = layoutConfirmReturnedInfo.findViewById(R.id.isvReturnImageV1)
        isvReturnRemark = layoutConfirmReturnedInfo.findViewById(R.id.isvReturnRemark)
        isvReturnVideoV1 = layoutConfirmReturnedInfo.findViewById(R.id.isvReturnVideoV1)
        isvReturnVideoV1.isVideo = true
        isvReturnImageRlV1 = layoutConfirmReturnedInfo.findViewById(R.id.isvReturnImageRlV1)
        isvReturnImageRl = layoutConfirmReturnedInfo.findViewById(R.id.isvReturnImageRl)
        isvReturnTime = layoutConfirmReturnedInfo.findViewById(R.id.isvReturnTime)
        tv_warning_jmyl = layoutConfirmReturnedInfo.findViewById(R.id.tv_warning_jmyl)
        jmyl_warning = layoutConfirmReturnedInfo.findViewById(R.id.jmyl_warning)
        isvReturnImageV1.isNestedScrollingEnabled = false
        isvReturnImageV1.setHasFixedSize(true)
        //卸货图片
        rl_clockin = layoutConfirmReturnedInfo.findViewById(R.id.rl_clockin)
        image_clockin = layoutConfirmReturnedInfo.findViewById(R.id.image_clockin)
        image_clockin.isNestedScrollingEnabled = false
        image_clockin.setHasFixedSize(true)

        tvReceiptDocumentPhotoV3 = layoutConfirmReturnedInfo.findViewById(R.id.tvReceiptDocumentPhotoV3)
        isvReceiptReturnImageRlV3 = layoutConfirmReturnedInfo.findViewById(R.id.isvReceiptReturnImageRlV3)
        isvReceiptReturnImageV3 = layoutConfirmReturnedInfo.findViewById(R.id.isvReceiptReturnImageV3)
        isvReceiptReturnImageV3.isNestedScrollingEnabled = false
        isvReceiptReturnImageV3.setHasFixedSize(true)

        isReceiptRecyclerViewCargo = layoutConfirmReturnedInfo.findViewById(R.id.isReceiptRecyclerViewCargo)
        mUploadReturnedOrderPhotoAdapterV12.canEdit = false
        isReceiptRecyclerViewCargo.apply {
            layoutManager = LinearLayoutManager(this@SingleReturnedOrderConfirmActivityV2)
            adapter = mUploadReturnedOrderPhotoAdapterV12
        }
        //ZCZY-7729 冀东定制化需求
        clOutStageTime = layoutConfirmReturnedInfo.findViewById(R.id.clOutStageTime)
        inputOutStageTime = layoutConfirmReturnedInfo.findViewById(R.id.inputOutStageTime)
        inputOutStageTime.let {
            it.tvTitle.apply {
                setTextColor(
                    ContextCompat.getColor(
                        this@SingleReturnedOrderConfirmActivityV2,
                        R.color.color_666
                    )
                )
                setTextSize(TypedValue.COMPLEX_UNIT_SP, 13F)
            }
            it.tvContent.apply {
                setTextColor(
                    ContextCompat.getColor(
                        this@SingleReturnedOrderConfirmActivityV2,
                        R.color.text_33
                    )
                )
                setTextSize(TypedValue.COMPLEX_UNIT_SP, 13F)
            }
            it.setListener(object : InputViewClick.Listener() {
                override fun onClick(viewId: Int, view: InputViewClick, content: String) {
                    showSelectTime()
                }
            })
        }
        //打回原因
        tvReturnReason = deniedInfo.findViewById(R.id.tvDeniedReason)
        //待处理原因
        tvdclReason = dclInfo.findViewById(R.id.tvdclReason)
        //结算信息
        etConfirmSettlementTonnage = layoutConfirmBillingInfo.findViewById(R.id.etConfirmSettlementTonnage)
        orderSettleTypeUnitEditText = layoutConfirmBillingInfo.findViewById(R.id.orderSettleTypeUnitEditText)
        orderSettleType = layoutConfirmBillingInfo.findViewById(R.id.orderSettleType)
        orderSettleTypeUnit = layoutConfirmBillingInfo.findViewById(R.id.orderSettleTypeUnit)
        UtilTool.setEditTextInputSize(etConfirmSettlementTonnage, 4)
        UtilTool.setEditTextInputSize(orderSettleTypeUnitEditText, 3)
        etConfirmSettlementPriceIncludedTax = layoutConfirmBillingInfo.findViewById(R.id.etConfirmSettlementPriceIncludedTax)
        etConfirmSettlementPriceNotIncludedTax = layoutConfirmBillingInfo.findViewById(R.id.etConfirmSettlementPriceNotIncludedTax)
        //回单修改记录
        ll_edit_record = layoutConfirmBillingInfo.findViewById(R.id.ll_edit_record)
        mReturnedOrderDetailBean?.receiptMoneySnapShotObj?.let { data ->
            ll_edit_record.visibility = View.VISIBLE
            ll_edit_record.setOnClickListener {
                OrderEditRecordDialog(this).show(data)
            }
        }
        etConfirmSettlementPriceNotIncludedTax.filters =
            arrayOf<InputFilter>(DecimalDigitsInputFilter(7, 2))
        cl_order_textview12_2 = layoutConfirmBillingInfo.findViewById(R.id.cl_order_textview12_2)
        tvCheckMonth = layoutConfirmBillingInfo.findViewById(R.id.tvCheckMonth)
        etConsignorNoRateUnitShowMoney = layoutConfirmBillingInfo.findViewById(R.id.etConsignorNoRateUnitShowMoney)
        etConsignorNoRateUnitShowMoney.filters = arrayOf<InputFilter>(DecimalDigitsInputFilter(7, 2))
        orderSettlementBasis = layoutConfirmBillingInfo.findViewById(R.id.orderSettlementBasis)
        tvLossTonnageCalc = layoutConfirmBillingInfo.findViewById(R.id.tvLossTonnageCalc)
        tvExpiryDateStr = layoutConfirmBillingInfo.findViewById(R.id.tvExpiryDateStr)
        tvExpiryDate = layoutConfirmBillingInfo.findViewById(R.id.tvExpiryDate)
        tvJiaKouKuanXiangUnit = layoutConfirmBillingInfo.findViewById(R.id.tvJiaKouKuanXiangUnit)
        recyclerViewContainer = layoutConfirmBillingInfo.findViewById(R.id.recyclerViewContainer)
        smallLabel = layoutConfirmBillingInfo.findViewById(R.id.smallLabel)
        //抹零规则
        llViewWipeZero = layoutConfirmBillingInfo.findViewById(R.id.llViewWipeZero)
        inputViewWipeZero = layoutConfirmBillingInfo.findViewById(R.id.inputViewWipeZero)
        inputViewWipeZeroValue = layoutConfirmBillingInfo.findViewById(R.id.inputViewWipeZeroValue)
        inputViewWipeZero.tvTitle.textSize = 14F
        inputViewWipeZero.tvLeft.textSize = 14F
        inputViewWipeZero.tvRight.textSize = 14F
        inputViewWipeZeroValue.tvContent.textSize = 14F
        inputViewWipeZeroValue.tvTitle.textSize = 14F
        clWipeZero = layoutConfirmBillingInfo.findViewById(R.id.clWipeZero)
        viewWipeMoney1 = layoutConfirmBillingInfo.findViewById(R.id.viewWipeMoney1)
        viewWipeMoney2 = layoutConfirmBillingInfo.findViewById(R.id.viewWipeMoney2)
        inputViewWipeZeroValue.setListener(object : InputViewClick.Listener() {
            override fun onClick(viewId: Int, view: InputViewClick, content: String) {
                ChooseDialogV1.instance(getOrderPriceList())
                    .setFlatMap { title ?: "" }
                    .setClick { s, _ ->
                        view.content = s.title ?: ""
                        mReturnedOrderDetailBeanV1.mOrderPriceBean = s
                        computeViewWipeMoney1()
                    }
                    .show(this@SingleReturnedOrderConfirmActivityV2)
            }
        })
        inputViewWipeZero.setListener(object : InputViewCheckV2.Listener() {
            override fun onClickCheck(
                viewId: Int,
                view: InputViewCheckV2,
                check: Int,
                radioStr: String
            ): Boolean {
                when (check) {
                    InputViewCheckV4.LEFT -> {
                        clWipeZero.setVisible(true)
                        inputViewWipeZeroValue.setVisible(true)
                    }

                    InputViewCheckV4.RIGHT -> {
                        clWipeZero.setVisible(false)
                        inputViewWipeZeroValue.setVisible(false)
                    }
                }
                return true
            }
        })
        recyclerViewContainer.apply {
            layoutManager = LinearLayoutManager(this@SingleReturnedOrderConfirmActivityV2)
            addItemDecoration(SpaceItemDecoration(dp2px(0f)))
            mReceiptContainerAdapter.bindToRecyclerView(recyclerViewContainer)
            adapter = mReceiptContainerAdapter
        }
        //加扣款项
        tvJiaKouKuanXiang.setOnClickListener {
            val check = smallLabel?.getRulePrice() ?: false
            if (check) {
                //使用亏涨吨结算价格(承运方预估到手价)
                AddAndDeductMoneyActivity.jumpPage(
                    activity = this@SingleReturnedOrderConfirmActivityV2,
                    consignorNoRateMoney = smallLabel?.getTaxNotIncludedPrice(),
                    rate = mReturnedOrderDetailBean.getSettleRate().toString(),
                    extraData = addAndDeductMoneyList.toJson(),
                    requestCode = AddAndDeductMoneyActivity.ADD_AND_DEDUCT_MONEY_CODE
                )
            } else {
                AddAndDeductMoneyActivity.jumpPage(
                    activity = this@SingleReturnedOrderConfirmActivityV2,
                    consignorNoRateMoney = etConfirmSettlementPriceNotIncludedTax.text.toString(),
                    rate = mReturnedOrderDetailBean.getSettleRate().toString(),
                    extraData = addAndDeductMoneyList.toJson(),
                    requestCode = AddAndDeductMoneyActivity.ADD_AND_DEDUCT_MONEY_CODE
                )
            }
        }
        // 亏吨计算器
        editShippingTonnage = endDrawer.findViewById(R.id.editShippingTonnage)
        editReceiptTonnage = endDrawer.findViewById(R.id.editReceiptTonnage)
        tvPoundDifference = endDrawer.findViewById(R.id.tvPoundDifference)
        editAllowableDeficitRate = endDrawer.findViewById(R.id.editAllowableDeficitRate)
        editAllowableLoss = endDrawer.findViewById(R.id.editAllowableLoss)
        tvExceedingTheAmountOfLoss = endDrawer.findViewById(R.id.tvExceedingTheAmountOfLoss)
        editAllowDeductibleDeductionFactor =
            endDrawer.findViewById(R.id.editAllowDeductibleDeductionFactor)
        editAllowDeductibleDeductions = endDrawer.findViewById(R.id.editAllowDeductibleDeductions)
        editExceedingTheDeductibleDeductionUnit =
            endDrawer.findViewById(R.id.editExceedingTheDeductibleDeductionUnit)
        editExceedingTheDeductibleDeduction =
            endDrawer.findViewById(R.id.editExceedingTheDeductibleDeduction)
        tvPriceIncludeTax = endDrawer.findViewById(R.id.tvPriceIncludeTax)
        tvPriceNoTax = endDrawer.findViewById(R.id.tvPriceNoIncludeTax)
        radioGroup = endDrawer.findViewById(R.id.radioGroup)
        rbIncludeTax = endDrawer.findViewById(R.id.rbIncludeTax)
        rbNoIncludeTax = endDrawer.findViewById(R.id.rbNoIncludeTax)
        btnReset = endDrawer.findViewById(R.id.btnReset)
        btnCloseDrawer = endDrawer.findViewById(R.id.btnCloseDrawer)
        //货主回单信息
        isvReturnImageHz = layoutConfirmBillingInfoHzImg.findViewById(R.id.isvReturnImageHz)
        isvReturnImageHz.canSelect = false
        isvReturnImageHz.canDelete = true
        isvReturnImageHz.deleteListener =
            object : ImageSelectViewV1.OnDeleteListener() {
                override fun onDeletePhoto(deleteUrl: String) {
                    //删除图片监听
                    val returnedOrderUpdatePhotoReq = ReturnedOrderUpdatePhotoReq()
                    returnedOrderUpdatePhotoReq.orderId = orderId
                    returnedOrderUpdatePhotoReq.photoId = deleteUrl
                    viewModel?.updatePhoto(returnedOrderUpdatePhotoReq)
                }
            }
        order_hz_divider = layoutConfirmBillingInfoHzImg.findViewById(R.id.order_hz_divider)
        order_hz_img_textview =
            layoutConfirmBillingInfoHzImg.findViewById(R.id.order_hz_img_textview)
        order_hz_img_up = layoutConfirmBillingInfoHzImg.findViewById(R.id.order_hz_img_up)
        order_hz_img_up.setOnClickListener {
            // 上传回单照片
            val intent = Intent(
                this@SingleReturnedOrderConfirmActivityV2,
                UploadReturnedOrderPhotoActivity::class.java
            )
            intent.putExtra(UploadReturnedOrderPhotoActivity.DETAIL_ID, detailId)
            intent.putExtra(UploadReturnedOrderPhotoActivity.ORDER_ID, orderId)
            var imageSize = isvReturnImageHz.imgList.size
            isvReturnImageHz.imgList.forEach { image ->
                when (image.pictureType) {
                    "13" -> {
                        imageSize -= 1
                    }
                }
            }
            intent.putExtra(UploadReturnedOrderPhotoActivity.UPLOAD_PHOTO_SIZE, imageSize)
            startActivityForResult(intent, UPLOAD_PHOTO)
        }
        tvLossTonnageCalc.setOnClickListener {
            drawerLayout.openDrawer(endDrawer)
        }
        initCodeView()
        initCodeViewV1()
    }

    override fun initData() {
        orderId = intent.getStringExtra(ORDER_ID) ?: ""
        detailId = intent.getStringExtra(DETAIL_ID) ?: ""
        backorderreconsiderstate = intent.getStringExtra(BACK_ORDER_RECONSIDER_STATE)
        queryType =
            intent.getStringExtra(ReturnedOrderConfirmListFragment.RETURNED_ORDER_QUERY_TYPE) ?: ""
        lastUptTime = intent.getStringExtra(ReturnedOrderConfirmListFragment.LAST_UPT_TIME)
        val operationConfig: OperationConfig? =
            intent.getParcelableExtra(ReturnedOrderConfirmListFragment.OPERATION_CONFIG)
        btnUploadPhoto.setVisible(false)
        bindClickEvent(btnUploadPhoto)
        when (queryType) {
            ReturnedOrderConfirmListFragment.TYPE_TO_BE_UPLOADED -> {
                btnUploadPhoto.setVisible(true)
                btnWaitingProcessing.setVisible(false)
                btnReject.setVisible(false)
                btnSingleConfirm.setVisible(false)
                mReceiptContainerAdapter.canEdit = false
                remarkEnable(false)
            }

            ReturnedOrderConfirmListFragment.TYPE_NOT_CONFIRM -> {
                btnWaitingProcessing.visibility = operationConfig?.showWaitingForProcessingButton
                    ?: View.GONE
                btnReject.visibility = operationConfig?.showRejectedButton ?: View.GONE
                btnSingleConfirm.visibility = View.VISIBLE
                mReceiptContainerAdapter.canEdit = true
                remarkEnable(true)
            }

            ReturnedOrderConfirmListFragment.TYPE_WAITING_PROCESSING -> {
                btnWaitingProcessing.visibility = View.GONE
                val btnSingleConfirmLayoutParams = btnSingleConfirm.layoutParams
                btnSingleConfirmLayoutParams.width = dp2px(106f)
                btnSingleConfirm.layoutParams = btnSingleConfirmLayoutParams
                btnReject.visibility = operationConfig?.showRejectedButton ?: View.GONE
                val btnRejectLayoutParams = btnReject.layoutParams
                btnRejectLayoutParams.width = dp2px(106f)
                btnReject.layoutParams = btnRejectLayoutParams
                mReceiptContainerAdapter.canEdit = true
                remarkEnable(true)
            }

            ReturnedOrderConfirmListFragment.TYPE_CONFIRMED -> {
                btnWaitingProcessing.visibility = View.GONE
                btnReject.visibility = View.GONE
                val layoutParams = btnSingleConfirm.layoutParams
                layoutParams.width = ViewGroup.LayoutParams.MATCH_PARENT
                btnSingleConfirm.layoutParams = layoutParams
                mReceiptContainerAdapter.canEdit = true
                remarkEnable(true)
                btnSingleConfirm.text = "重新确认"
            }

            ReturnedOrderConfirmListFragment.TYPE_NEGOTIATING -> {
                when (backorderreconsiderstate) {
                    "4" -> {
                        btnWaitingProcessing.visibility = View.GONE
                        btnReject.visibility = View.GONE
                        btnSingleConfirm.visibility = View.GONE
                        view_code.setVisible(false)
                        btnAgreeAdjust.visibility = View.VISIBLE
                    }

                    else -> {
                        btnAgreeAdjust.visibility = View.GONE
                        layoutBtn.visibility = View.GONE
                    }
                }
                mReceiptContainerAdapter.canEdit = false
                remarkEnable(false)
            }

            else -> {
                layoutBtn.visibility = View.GONE
                mReceiptContainerAdapter.canEdit = false
                remarkEnable(false)
            }
        }
        refreshReturnedOrderDetail()
    }

    override fun onSingleClick(v: View) {
        super.onSingleClick(v)
        when (v.id) {
            R.id.btnUploadPhoto -> {
                //回单上传
                UploadReturnedOrderPhotoActivityV2.jumpPage(
                    context = this@SingleReturnedOrderConfirmActivityV2,
                    orderId = orderId
                )
            }
        }
    }

    private fun remarkEnable(edit: Boolean) {
        view_code.setVisible(true)
        edit_code.isEnabled = edit
    }

    private fun reject() {
        getViewModel(BaseViewModel::class.java).execute(ReqQueryConsignorRepulseTag(orderId = orderId)) {
            if (it.success()) {
                val list = it.data?.list
                if (list.isNullOrEmpty()) {
                    showDialogToast("未获取到数据")
                    return@execute
                }
                val returnOrderReasonChooseDialog = ReturnOrderReasonChooseDialog(list = list)
                returnOrderReasonChooseDialog.onToastBlock = { toastMsg ->
                    showDialogToast(toastMsg)
                }
                returnOrderReasonChooseDialog.mClickBlock = { choose ->
                    when (choose?.exceptionConfigType) {
                        ExceptionConfigTypeEnum.问题反馈.value,
                        ExceptionConfigTypeEnum.回单打回.value -> {
                            rejected(orderId = orderId, detailId = detailId, remarks = choose.remarks, exceptionConfigType = choose.exceptionConfigType)
                        }

                        ExceptionConfigTypeEnum.违约申请.value -> {
                            OrderViolateAddActivity.startContentUI(
                                context = this@SingleReturnedOrderConfirmActivityV2,
                                orderId = orderId,
                                label = choose.remarks,
                                checkStop = false,
                                requestCode = 0x55
                            )
                        }
                    }
                }
                returnOrderReasonChooseDialog.show(this@SingleReturnedOrderConfirmActivityV2)
            } else {
                showDialogToast(it.msg)
            }
        }
    }

    // 打回
    private fun rejected(orderId: String?, detailId: String?, remarks: String?, exceptionConfigType: String?) {
        ReturnedOrderDeniedReasonActivity.start(
            context = this@SingleReturnedOrderConfirmActivityV2,
            orderId = orderId,
            detailId = detailId,
            remarks = remarks,
            exceptionConfigType = exceptionConfigType,
        )
    }

    @LiveDataMatch
    open fun updatePhotoSuccess() {
        if (isvReturnImageHz.imgList.size >= 5) {
            order_hz_img_up.setVisible(false)
        } else {
            order_hz_img_up.setVisible(true)
        }
    }

    override fun onActivityResult(requestCode: Int, resultCode: Int, intent: Intent?) {
        super.onActivityResult(requestCode, resultCode, intent)
        if (resultCode == Activity.RESULT_OK) {
            when (requestCode) {
                AddAndDeductMoneyActivity.ADD_AND_DEDUCT_MONEY_CODE -> {
                    addAndDeductMoneyList = AddAndDeductMoneyActivity.obtainData(intent)
                    val check = smallLabel?.getRulePrice() ?: false
                    if (check) {
                        calc(
                            priceDoesNotIncludeTax = smallLabel?.getTaxNotIncludedPrice() ?: "0.00",
                            rate = mReturnedOrderDetailBean.getSettleRate().toString()
                        )
                    } else {
                        calc(
                            priceDoesNotIncludeTax = etConfirmSettlementPriceNotIncludedTax.text.toString(),
                            rate = mReturnedOrderDetailBean.getSettleRate().toString()
                        )
                    }
                }

                0x01 -> {
                    RxBusEventManager.postEvent(RxAgreeAdjust(true))
                    finish()
                }

                UPLOAD_PHOTO_V1 -> {
                    //发货
                    val cargoList = UploadReturnedOrderPhotoActivityV1.obtainDataV3(intent)
                    mReturnedOrderDetailBean?.deliverCargoArray = cargoList
                    mUploadReturnedOrderPhotoAdapterV11.setNewData(cargoList)
                    val list = UploadReturnedOrderPhotoActivityV1.obtainDataV1(
                        UploadReturnedOrderPhotoActivityV1.UPLOAD_PHOTO_LIST_V1,
                        intent
                    )
                    val imgList = arrayListOf<EImage>()
                    list?.forEach {
                        imgList.add(EImage(imageId = it))
                    }
                    isvDeliveryImageV2.imgList = imgList

                    val weight = UploadReturnedOrderPhotoActivityV1.obtainDataV2(
                        UploadReturnedOrderPhotoActivityV1.WEIGHT_V1,
                        intent
                    )
                    if (imgList.size <= 0) {
                        tvDocumentPhotoV2.setVisible(false)
                        isvDeliveryImageV2.setVisible(false)
                        isvDeliveryImageRlV2.setVisible(false)
                    } else {
                        tvDocumentPhotoV2.setVisible(true)
                        isvDeliveryImageV2.setVisible(true)
                        isvDeliveryImageRlV2.setVisible(true)
                    }
                    tvDeliveryNum.text =
                        weight + mReturnedOrderDetailBean?.getDeliverCargoCategoryStr()
                    calculateDifferenceRatio("1")
                    queryRuleId()
                    val replace1 = tvReceiptNum.text.toString().trim()
                        .replace(mReturnedOrderDetailBean?.getDeliverCargoCategoryStr() ?: "吨", "")
                    val replace2 = tvDeliveryNum.text.toString().trim()
                        .replace(mReturnedOrderDetailBean?.getDeliverCargoCategoryStr() ?: "吨", "")
                    viewModel?.queryReceiptLossRiseRule(
                        req = ReqReturnQueryReceiptLossRiseRule(
                            orderId = orderId,
                            receiveWeight = replace1,
                            deliverWeight = replace2,
                            consignorCheckMoney = etConfirmSettlementPriceIncludedTax.text.toString()
                                .trim(),
                            configType = mReleaseLossTonData.ifEmpty {
                                if (replace2.toDouble() > replace1.toDouble()) {
                                    "1"
                                } else {
                                    "2"
                                }
                            }
                        ),
                        block = {
                            runOnUiThread {
                                if (it.isNullOrEmpty()) {
                                    smallLabel?.setVisible(false)
                                }
                            }
                            false
                        }
                    )
                }

                UPLOAD_PHOTO_V2 -> {
                    //收货
                    val cargoList = UploadReturnedOrderPhotoActivityV1.obtainDataV3(intent)
                    mReturnedOrderDetailBean?.receiveCargoArray = cargoList
                    mUploadReturnedOrderPhotoAdapterV12.setNewData(cargoList)
                    val list = UploadReturnedOrderPhotoActivityV1.obtainDataV1(
                        UploadReturnedOrderPhotoActivityV1.UPLOAD_PHOTO_LIST_V2,
                        intent
                    )
                    val imgList = arrayListOf<EImage>()
                    list?.forEach {
                        imgList.add(EImage(imageId = it))
                    }
                    isReceiptReturnImageV2.imgList = imgList
                    val weight = UploadReturnedOrderPhotoActivityV1.obtainDataV2(
                        UploadReturnedOrderPhotoActivityV1.WEIGHT_V2,
                        intent
                    )
                    if (imgList.size <= 0) {
                        tvReceiptDocumentPhotoV2.setVisible(false)
                        isReceiptReturnImageV2.setVisible(false)
                        isReceiptReturnImageRlV2.setVisible(false)
                    } else {
                        tvReceiptDocumentPhotoV2.setVisible(true)
                        isReceiptReturnImageV2.setVisible(true)
                        isReceiptReturnImageRlV2.setVisible(true)
                    }
                    tvReceiptNum.text =
                        weight + mReturnedOrderDetailBean?.getDeliverCargoCategoryStr()
                    calculateDifferenceRatio("2")
                    queryRuleId()
                    val replace1 = tvReceiptNum.text.toString().trim()
                        .replace(mReturnedOrderDetailBean?.getDeliverCargoCategoryStr() ?: "吨", "")
                    val replace2 = tvDeliveryNum.text.toString().trim()
                        .replace(mReturnedOrderDetailBean?.getDeliverCargoCategoryStr() ?: "吨", "")
                    viewModel?.queryReceiptLossRiseRule(
                        req = ReqReturnQueryReceiptLossRiseRule(
                            orderId = orderId,
                            receiveWeight = replace1,
                            deliverWeight = replace2,
                            consignorCheckMoney = etConfirmSettlementPriceIncludedTax.text.toString()
                                .trim(),
                            configType = mReleaseLossTonData.ifEmpty {
                                if (replace2.toDouble() > replace1.toDouble()) {
                                    "1"
                                } else {
                                    "2"
                                }
                            }
                        ),
                        block = {
                            runOnUiThread {
                                if (it.isNullOrEmpty()) {
                                    smallLabel?.setVisible(false)
                                }
                            }
                            false
                        }
                    )
                }
            }
        }
    }

    private fun calculateSettleBasisType() {

        val etConfirmSettlementTonnageWeight = etConfirmSettlementTonnage.text
        if (mReturnedOrderDetailBean?.settleBasisType?.isTrue == true) {
            //结算依据 1 确认发货吨位结算,2 确认收货吨位结算
            val tvDeliveryNumWeight = tvDeliveryNum.text.toString().trim()
                .replace(mReturnedOrderDetailBean?.getDeliverCargoCategoryStr() ?: "吨", "")
            if (!TextUtils.equals(etConfirmSettlementTonnageWeight, tvDeliveryNumWeight)) {
                etConfirmSettlementTonnage.setText(tvDeliveryNumWeight)
                confirmSettlementTonnage(etConfirmSettlementTonnage.text.toString())
            }
        } else {
            val tvReceiptNumWeight = tvReceiptNum.text.toString().trim()
                .replace(mReturnedOrderDetailBean?.getDeliverCargoCategoryStr() ?: "吨", "")
            if (!TextUtils.equals(etConfirmSettlementTonnageWeight, tvReceiptNumWeight)) {
                etConfirmSettlementTonnage.setText(tvReceiptNumWeight)
                confirmSettlementTonnage(etConfirmSettlementTonnage.text.toString())
            }
        }
    }

    private fun queryRuleId() {
        //查询亏涨吨
        var replace1 = tvReceiptNum.text.toString().trim()
            .replace(mReturnedOrderDetailBean?.getDeliverCargoCategoryStr() ?: "吨", "")
        if (TextUtils.isEmpty(replace1)) {
            replace1 = "0.000"
        }
        var replace2 = tvDeliveryNum.text.toString().trim()
            .replace(mReturnedOrderDetailBean?.getDeliverCargoCategoryStr() ?: "吨", "")
        if (TextUtils.isEmpty(replace2)) {
            replace2 = "0.000"
        }
        val sub = NumUtil.sub(replace1.toDouble(), replace2.toDouble())
        if (mReturnedOrderDetailBean?.releaseLossTonData?.consignorSmartDeficitTonFlag.isTrue) {
            val mLossRiseType: String
            when {
                sub == 0.00 -> {
                    smallLabel?.setVisible(false)
                    calc(
                        priceDoesNotIncludeTax = etConfirmSettlementPriceNotIncludedTax.text.toString(),
                        rate = mReturnedOrderDetailBean.getSettleRate().toString()
                    )
                }

                sub < 0 -> {
                    //亏吨
                    smallLabel?.setVisible(true)
                    mLossRiseType = "1"
                    if (!TextUtils.equals(
                            mLossRiseType,
                            <EMAIL>?.releaseLossTonData?.releaseLossTonType
                        )
                    ) {
                        smallLabel?.setRuleVisibleV1()
                        calc(
                            priceDoesNotIncludeTax = etConfirmSettlementPriceNotIncludedTax.text.toString(),
                            rate = mReturnedOrderDetailBean.getSettleRate().toString()
                        )
                        return
                    }
                    viewModel?.queryReceiptLossRiseDetailInfoByAppChangeRuleId(
                        req = ReqQueryReceiptLossRiseDetailInfoByAppChangeRuleId(
                            ruleId = smallLabel?.getRuleItem(),
                            orderId = orderId,
                            receiveWeight = replace1,
                            deliverWeight = replace2,
                            consignorRateMoney = etConfirmSettlementPriceIncludedTax.text.toString(),
                            consignorSmartDeficitTonUseRateType = smallLabel?.getConsignorSmartDeficitTonUseRateType()
                        )
                    )
                }

                sub > 0 -> {
                    //涨吨
                    smallLabel?.setVisible(true)
                    mLossRiseType = "2"
                    if (!TextUtils.equals(
                            mLossRiseType,
                            <EMAIL>?.releaseLossTonData?.releaseLossTonType
                        )
                    ) {
                        smallLabel?.setRuleVisibleV1()
                        calc(
                            priceDoesNotIncludeTax = etConfirmSettlementPriceNotIncludedTax.text.toString(),
                            rate = mReturnedOrderDetailBean.getSettleRate().toString()
                        )
                        return
                    }
                    viewModel?.queryReceiptLossRiseDetailInfoByAppChangeRuleId(
                        req = ReqQueryReceiptLossRiseDetailInfoByAppChangeRuleId(
                            ruleId = smallLabel?.getRuleItem(),
                            orderId = orderId,
                            receiveWeight = replace1,
                            deliverWeight = replace2,
                            consignorRateMoney = etConfirmSettlementPriceIncludedTax.text.toString(),
                            consignorSmartDeficitTonUseRateType = smallLabel?.getConsignorSmartDeficitTonUseRateType()
                        )
                    )
                }
            }
        }
    }

    private fun calculateDifferenceRatio(editType: String) {
        var replace1 = tvReceiptNum.text.toString().trim()
            .replace(mReturnedOrderDetailBean?.getDeliverCargoCategoryStr() ?: "吨", "")
        if (TextUtils.isEmpty(replace1)) {
            replace1 = "0.000"
        }
        var replace2 = tvDeliveryNum.text.toString().trim()
            .replace(mReturnedOrderDetailBean?.getDeliverCargoCategoryStr() ?: "吨", "")
        if (TextUtils.isEmpty(replace2)) {
            replace2 = "0.000"
        }
        val sub = NumUtil.sub(replace1.toDouble(), replace2.toDouble())
        val a = mReturnedOrderDetailBean?.supportReceiptSmallerFlag.isTrue//开通小型亏涨吨
        val b = TextUtils.equals("4", mReturnedOrderDetailBean?.settleBasisType)//按理计重量结算
        val c = TextUtils.equals("2", editType) // 修改收货吨位
        if (b && c) {
            mUploadReturnedOrderPhotoAdapterV12.settleBasisType =
                mReturnedOrderDetailBean?.settleBasisType
            etConfirmSettlementTonnage.setText(
                mUploadReturnedOrderPhotoAdapterV12.computeAllWeightV1().toString()
                    .toDoubleRoundDownString(4)
            )
        } else if (b && editType.isTrue) {
            mUploadReturnedOrderPhotoAdapterV11.settleBasisType =
                mReturnedOrderDetailBean?.settleBasisType
            etConfirmSettlementTonnage.setText(
                mUploadReturnedOrderPhotoAdapterV11.computeAllWeightV1().toString()
                    .toDoubleRoundDownString(4)
            )
            confirmSettlementTonnage(etConfirmSettlementTonnage.text.toString())
        } else {
            if (mReturnedOrderDetailBean?.supportReceiptSmallerFlag.isTrue) {
                val etConfirmSettlementTonnageWeight = etConfirmSettlementTonnage.text
                when {
                    sub == 0.00 -> {
                        orderSettlementBasis.text = "按收货磅单结算"

                        val tvReceiptNumWeight = tvReceiptNum.text.toString().trim()
                            .replace(
                                mReturnedOrderDetailBean?.getDeliverCargoCategoryStr() ?: "吨",
                                ""
                            )
                        if (!TextUtils.equals(
                                etConfirmSettlementTonnageWeight,
                                tvReceiptNumWeight
                            )
                        ) {
                            etConfirmSettlementTonnage.setText(tvReceiptNumWeight)
                            confirmSettlementTonnage(etConfirmSettlementTonnage.text.toString())
                        }
                    }

                    sub < 0.00 -> {
                        orderSettlementBasis.text = "按收货磅单结算"
                        val tvReceiptNumWeight = tvReceiptNum.text.toString().trim()
                            .replace(
                                mReturnedOrderDetailBean?.getDeliverCargoCategoryStr() ?: "吨",
                                ""
                            )
                        if (!TextUtils.equals(
                                etConfirmSettlementTonnageWeight,
                                tvReceiptNumWeight
                            )
                        ) {
                            etConfirmSettlementTonnage.setText(tvReceiptNumWeight)
                            confirmSettlementTonnage(etConfirmSettlementTonnage.text.toString())
                        }
                    }

                    else -> {
                        orderSettlementBasis.text = "按发货磅单结算"
                        val tvDeliveryNumWeight = tvDeliveryNum.text.toString().trim()
                            .replace(
                                mReturnedOrderDetailBean?.getDeliverCargoCategoryStr() ?: "吨",
                                ""
                            )
                        if (!TextUtils.equals(
                                etConfirmSettlementTonnageWeight,
                                tvDeliveryNumWeight
                            )
                        ) {
                            etConfirmSettlementTonnage.setText(tvDeliveryNumWeight)
                            confirmSettlementTonnage(etConfirmSettlementTonnage.text.toString())
                        }
                    }
                }
            } else {
                calculateSettleBasisType()
            }
        }
        val div = NumUtil.div(sub, replace2.toDouble(), 4)
        val mul = NumUtil.mul(div, 100.00)
        tv_warning_jmyl.text = "差额：${
            sub.toString()
                .toDoubleRoundDownString(3) + mReturnedOrderDetailBean?.getDeliverCargoCategoryStr()
        } | ${mul.toString().toDoubleRoundDownString(2)}" + "%"
    }

    @LiveDataMatch
    open fun ongetVideoPathSuccess(respGetVideoPath: RespGetVideoPath?) {
        if (respGetVideoPath?.data != null && !TextUtils.isEmpty(respGetVideoPath.data.content)) {
            iv_video_guide.visibility = View.VISIBLE

            iv_video_guide.setOnClickListener { // 播放视频
                val intent = Intent(this@SingleReturnedOrderConfirmActivityV2, VideoPlayActivity::class.java)
                intent.putExtra("videoUri", respGetVideoPath.data.content)
                startActivity(intent)
            }
        } else {
            iv_video_guide.visibility = View.GONE
        }
    }

    @LiveDataMatch(tag = "上传回单照片接口")
    open fun uploadReturnedOrderPhotoSuccess(msg: String?) {
        showToast(msg)
        refreshReturnedOrderDetail()
    }

    private fun refreshReturnedOrderDetail() {
        viewModel?.returnedOrderDetail(ReturnedOrderDetailReq(orderId, detailId))
    }

    private fun refreshRetrunedOrderDetailHzPic() {
        viewModel?.returnedOrderDetailHzPic(ReturnedOrderDetailReq(orderId, detailId))
    }

    private fun refreshRetrunedOrderDetailHzPicV1() {
        viewModel?.returnedOrderDetailHzPicV1(ReturnedOrderDetailReq(orderId, detailId))
    }

    @LiveDataMatch
    open fun exceptionOrderExamineInfoSuccess(mReturnedOrderDetailBean: RspExceptionTypeByOrderId?) {
        mReturnedOrderDetailBean?.let {
            val pluginServer = AMainServer.getPluginServer()
            pluginServer?.openWaybillProveInfoSubmitActivity(
                this@SingleReturnedOrderConfirmActivityV2,
                mReturnedOrderDetailBean.monitorId,
                0x01
            )
        }
    }

    @LiveDataMatch
    open fun agreeAdjustDetailSuccess(mReturnedOrderDetailBean: AgreeAdjustDetailBean) {
        val ft = supportFragmentManager?.beginTransaction()
        val prev = supportFragmentManager?.findFragmentByTag("dialog")
        if (prev != null) {
            ft?.remove(prev)
        }
        ft?.addToBackStack(null)
        val agreeAdjustDialog = AgreeAdjustDetailDialog.instance(mReturnedOrderDetailBean, this)
        if (ft != null) {
            agreeAdjustDialog.show(ft, "dialog")
        }
    }

    override fun agreeAdjust(consignorUserId: String?, detailId: String?) {
        viewModel?.agreeAdjust(AgreeAdjustReq(detailId, consignorUserId))
    }

    @LiveDataMatch
    open fun agreeAdjustSuccess(rsp: String) {
        showToast(rsp)
        RxBusEventManager.postEvent(RxAgreeAdjust(true))
        finish()
    }

    @LiveDataMatch
    open fun queryLoadStateByDetailIdsSuccess(res: QueryLoadStateByDetailIdsRes?) {
        res?.let { it ->
            //1 无提示直接回单确认 2:存在有未审核的，无法回单确认 3:需要用户点击确定才可回单确认
            when (it.loadState) {
                "1" -> {
                    viewModel?.queryOrderExceptionNotToReceiptFlag(
                        ReqOrderExceptionNotToReceiptFlag(
                            detailId = detailId
                        )
                    )
                }

                "2" -> {
                    it.msg?.let {
                        showToast(it)
                    }
                }

                "3" -> {
                    val dialogBuilder = DialogBuilder()
                        .setMessage(it.msg)
                        .setOkListener { dialog, _ ->
                            dialog.dismiss()
                            viewModel?.queryOrderExceptionNotToReceiptFlag(
                                ReqOrderExceptionNotToReceiptFlag(detailId = detailId)
                            )
                        }
                    showDialog(dialogBuilder)
                }

                else -> {
                }
            }
        }
    }

    private fun confirmReturnedOrder() {
        val latestSlipLoad = etConfirmSettlementTonnage.text.toString().trim()
        if (latestSlipLoad.isEmpty()) {
            showToast("请输入结算吨位")
            return
        }
        viewModel?.getSingleReturnedOrderMsg(
            SingleReturnedOrderConfirmMsgReq(
                orderId = orderId,
                detailId = detailId,
                consignorRateMoney = mReturnedOrderDetailBean?.consignorRateMoney,
                consignorNoRateMoney = mReturnedOrderDetailBean?.consignorNoRateMoney,
                slipLoad = latestSlipLoad,
                deductionDetail = addAndDeductMoneyList.toJson(),
                consignorSmartDeficitTonUseRateType = smallLabel?.getConsignorSmartDeficitTonUseRateType(),
                consignorSmartDeficitTonUseFlag = smallLabel?.getConsignorSmartDeficitTonUseFlag(),
                releaseLossConsignorRateMoney = smallLabel?.getTaxIncludedPrice(),
                lastUptTime = lastUptTime,
                lossRiseMoney = smallLabel?.getLossRiseMoney(),
                deliverWeight = tvDeliveryNum.text.toString().trim()
                    .replace(mReturnedOrderDetailBean?.getDeliverCargoCategoryStr() ?: "吨", ""),
                receiveWeight = tvReceiptNum.text.toString().trim()
                    .replace(mReturnedOrderDetailBean?.getDeliverCargoCategoryStr() ?: "吨", ""),
                ignoreSmallChangeType = mReturnedOrderDetailBeanV1.mOrderPriceBean.value
            )
        )
    }


    private fun dialogYes(remark: String) {
        val latestSlipLoad = etConfirmSettlementTonnage.text.toString().trim()
        if (latestSlipLoad.isEmpty()) {
            showToast("请输入结算吨位")
            return
        }
        val deliverReceiptNumber = inputAiOrderNum.text.toString().trim()
        if (mReturnedOrderDetailBean?.showDeliverReceiptFlag.isTrue && deliverReceiptNumber.isEmpty()) {
            showToast("请输入确认发货单据号")
            inputAiOrderNumWarning.setVisible(true)
            return
        }
        when (mReturnedOrderDetailBean?.goodsSource) {
            "1" -> {
                checkAll().yes {
                    viewModel?.singleConfirmReturnedOrder(
                        SingleReturnedOrderConfirmReq(
                            orderId = orderId,
                            consignorPricUnitMoney = when (mReturnedOrderDetailBean?.freightType) {
                                "1" -> {
                                    orderSettleTypeUnitEditText.text.toString()
                                }

                                else -> {
                                    null
                                }
                            },
                            detailId = detailId,
                            consignorRateMoney = mReturnedOrderDetailBean?.consignorRateMoney,
                            consignorNoRateMoney = mReturnedOrderDetailBean?.consignorNoRateMoney,
                            slipLoad = latestSlipLoad,
                            deductionDetail = addAndDeductMoneyList?.toJson(),
                            consignorSmartDeficitTonUseRateType = smallLabel?.getConsignorSmartDeficitTonUseRateType(),
                            lossRiseMoney = smallLabel?.getLossRiseMoney(),
                            consignorSmartDeficitTonUseFlag = smallLabel?.getConsignorSmartDeficitTonUseFlag(),
                            releaseLossConsignorRateMoney = smallLabel?.getTaxIncludedPrice(),
                            releaseLossRuleId = smallLabel?.getRuleItem(),
                            overLossRuleName = smallLabel?.getRuleName(),
                            selfComment = orderCustomNumber1.text.toString().trim(),
                            updateSelfComment = "1",
                            consigorExplain = remark,
                            lastUptTime = lastUptTime,
                            consignorReceiptRemark = edit_code.text.toString().trim(),
                            outTime = inputOutStageTime.content.trim(),
                            containerNoJsonArray = when (mReturnedOrderDetailBean?.goodsSource) {
                                "1" -> {
                                    mReceiptContainerAdapter.data.toMutableList()
                                }

                                else -> {
                                    null
                                }
                            },
                            deliverWeight = tvDeliveryNum.text.toString().trim()
                                .replace(
                                    mReturnedOrderDetailBean?.getDeliverCargoCategoryStr() ?: "吨",
                                    ""
                                ),
                            consignorNeedSendDeliverPicArr = isvDeliveryImageV2.imgList.map {
                                WatermarkPic(
                                    picUrl = it.imageId
                                )
                            }.toMutableList(),
                            receiveWeight = tvReceiptNum.text.toString().trim()
                                .replace(
                                    mReturnedOrderDetailBean?.getDeliverCargoCategoryStr() ?: "吨",
                                    ""
                                ),
                            consignorNeedSendReceiptPicArr = isReceiptReturnImageV2.imgList.map {
                                WatermarkPic(
                                    picUrl = it.imageId
                                )
                            }.toMutableList(),
                            ignoreSmallChangeType = mReturnedOrderDetailBeanV1.mOrderPriceBean.value,
                            ignoreSmallChangeRateMoney = mReturnedOrderDetailBeanV1.ignoreSmallChangeRateMoney,
                            ignoreSmallChangeNoRateMoney = mReturnedOrderDetailBeanV1.ignoreSmallChangeNoRateMoney,
                            deliverCargoJsonList = mUploadReturnedOrderPhotoAdapterV11.data,
                            receiveCargoJsonList = mUploadReturnedOrderPhotoAdapterV12.data,
                            deductionNoRateMoney = tvTaxNotIncludedPrice.text.toString()
                                .replace("元", ""),
                            deductionRateMoney = tvTaxIncludedPrice.text.toString()
                                .replace("元", ""),
                            releaseLossConsignorNoRateMoney = smallLabel?.getTaxNotIncludedPrice()
                                ?: "0.00",
                            deliverReceiptNumber = deliverReceiptNumber,
                        )
                    )
                }
            }

            else -> {
                viewModel?.singleConfirmReturnedOrder(
                    SingleReturnedOrderConfirmReq(
                        orderId = orderId,
                        consignorPricUnitMoney = when (mReturnedOrderDetailBean?.freightType) {
                            "1" -> {
                                orderSettleTypeUnitEditText.text.toString()
                            }

                            else -> {
                                null
                            }
                        },
                        detailId = detailId,
                        consignorRateMoney = mReturnedOrderDetailBean?.consignorRateMoney,
                        consignorNoRateMoney = mReturnedOrderDetailBean?.consignorNoRateMoney,
                        slipLoad = latestSlipLoad,
                        deductionDetail = addAndDeductMoneyList?.toJson(),
                        consignorSmartDeficitTonUseRateType = smallLabel?.getConsignorSmartDeficitTonUseRateType(),
                        lossRiseMoney = smallLabel?.getLossRiseMoney(),
                        consignorSmartDeficitTonUseFlag = smallLabel?.getConsignorSmartDeficitTonUseFlag(),
                        releaseLossConsignorRateMoney = smallLabel?.getTaxIncludedPrice(),
                        releaseLossRuleId = smallLabel?.getRuleItem(),
                        overLossRuleName = smallLabel?.getRuleName(),
                        selfComment = orderCustomNumber1.text.toString().trim(),
                        updateSelfComment = "1",
                        consigorExplain = remark,
                        lastUptTime = lastUptTime,
                        consignorReceiptRemark = edit_code.text.toString().trim(),
                        outTime = inputOutStageTime.content.trim(),
                        containerNoJsonArray = when (mReturnedOrderDetailBean?.goodsSource) {
                            "1" -> {
                                mReceiptContainerAdapter.data.toMutableList()
                            }

                            else -> {
                                null
                            }
                        },
                        deliverWeight = tvDeliveryNum.text.toString().trim()
                            .replace(
                                mReturnedOrderDetailBean?.getDeliverCargoCategoryStr() ?: "吨",
                                ""
                            ),
                        consignorNeedSendDeliverPicArr = isvDeliveryImageV2.imgList.map {
                            WatermarkPic(
                                picUrl = it.imageId
                            )
                        }.toMutableList(),
                        receiveWeight = tvReceiptNum.text.toString().trim()
                            .replace(
                                mReturnedOrderDetailBean?.getDeliverCargoCategoryStr() ?: "吨",
                                ""
                            ),
                        consignorNeedSendReceiptPicArr = isReceiptReturnImageV2.imgList.map {
                            WatermarkPic(
                                picUrl = it.imageId
                            )
                        }.toMutableList(),
                        ignoreSmallChangeType = mReturnedOrderDetailBeanV1.mOrderPriceBean.value,
                        ignoreSmallChangeRateMoney = mReturnedOrderDetailBeanV1.ignoreSmallChangeRateMoney,
                        ignoreSmallChangeNoRateMoney = mReturnedOrderDetailBeanV1.ignoreSmallChangeNoRateMoney,
                        deliverCargoJsonList = mUploadReturnedOrderPhotoAdapterV11.data,
                        receiveCargoJsonList = mUploadReturnedOrderPhotoAdapterV12.data,
                        deductionNoRateMoney = tvTaxNotIncludedPrice.text.toString()
                            .replace("元", ""),
                        deductionRateMoney = tvTaxIncludedPrice.text.toString().replace("元", ""),
                        releaseLossConsignorNoRateMoney = smallLabel?.getTaxNotIncludedPrice()
                            ?: "0.00",
                        deliverReceiptNumber = deliverReceiptNumber,
                    )
                )
            }
        }

    }

    private fun checkAll(): Boolean {
        mReceiptContainerAdapter.data.forEachIndexed { index, item ->
            val empty = when {
                item.containerListNo.isEmpty() -> {
                    showDialogToast("箱号内容不能为空")
                    true
                }

                else -> false
            }
            empty.yes {
                recyclerViewContainer.scrollToPosition(index)
                return false
            }
        }
        return true
    }

    private var isYiJia = false

    @LiveDataMatch
    open fun orderExceptionNotToReceiptFlagSuccess(mReturnedOrderDetailBean: BaseRsp<ResultData>?) {
        //查询异常
        mReturnedOrderDetailBean?.apply {
            if (success()) {
                confirmReturnedOrder()
            } else {
                val dialogBuilder = DialogBuilder()
                dialogBuilder.setMessage(msg)
                    .setTitle("提示")
                    .setCancelTextListener("取消") { dialog, _ -> dialog?.dismiss() }
                    .setOKTextListener("继续") { dialog, _ ->
                        dialog?.dismiss()
                        confirmReturnedOrder()
                    }
                showDialog(dialogBuilder)

            }
        }

    }

    @LiveDataMatch
    open fun getSingleReturnedOrderMsgSuccess(msgRes: SingleReturnedOrderConfirmMsgRes) {
        msgRes.let {
            if (it.tips == "2") {
                isYiJia = true
            }
            ReturnedOrderConfirmDialog(msgRes = msgRes).setOkBlock { contentReason ->
                dialogYes(contentReason)
            }.show(this@SingleReturnedOrderConfirmActivityV2)
        }
    }

    @LiveDataMatch
    open fun returnedOrderDetailSuccess(mReturnedOrderDetailBean: ReturnedOrderDetailBean) {
        setUiData(mReturnedOrderDetailBean)
    }

    @LiveDataMatch
    open fun returnedOrderDetailHzPicSuccess(mReturnedOrderDetailBean: ReturnedOrderDetailBean) {
        setHuozuPicUi(mReturnedOrderDetailBean)
    }

    @LiveDataMatch
    open fun returnedOrderDetailHzPicSuccessV1(mReturnedOrderDetailBean: ReturnedOrderDetailBean) {
        setUpdateReceiptDeliverWeightAndPic(mReturnedOrderDetailBean)
    }

    @LiveDataMatch
    open fun batchConfirmReturnedOrderSuccess(res: BaseRsp<ReturnedOrderBatchConfirmRes>) {
        ReturnedOrderConfirmSuccessActivity.start(
            this, detailId,
            res.data?.data?.successCount ?: "",
            res.data?.showAutoSettle ?: "", false
        )
        RxBusEventManager.postEvent(RxAgreeAdjust(true))
        finish()
    }

    @LiveDataMatch
    open fun submitWaitingProcessingSuccess() {
        RxBusEventManager.postEvent(RxAgreeAdjust(true))
        finish()
    }

    private fun setHuozuPicUi(mReturnedOrderDetailBean: ReturnedOrderDetailBean) {
        if (TextUtils.isEmpty(mReturnedOrderDetailBean.backStatus) || TextUtils.equals(
                "0",
                mReturnedOrderDetailBean.backStatus
            ) || TextUtils.equals("3", mReturnedOrderDetailBean.backStatus)
        ) {
            order_hz_img_up.setVisible(true)
            order_hz_divider.setVisible(true)
            order_hz_img_textview.setVisible(true)
            mReturnedOrderDetailBean.consignorUploadPicJsonObjArr?.apply {
                var imageSize = size
                val list = arrayListOf<EImage>()
                forEach { upLoadPic ->
                    val eImage = EImage()
                    eImage.imageId = upLoadPic.pictureUrl
                    eImage.toastStr = upLoadPic.photoId
                    eImage.pictureType = upLoadPic.pictureType
                    list.add(eImage)
                    when (upLoadPic.pictureType) {
                        "13" -> {
                            imageSize -= 1
                        }
                    }
                }
                isvReturnImageHz.imgList = list
                if (imageSize >= 5) {
                    order_hz_img_up.setVisible(false)
                } else {
                    order_hz_img_up.setVisible(true)
                }
            }
        } else {
            order_hz_img_up.setVisible(false)
            order_hz_img_textview.setVisible(false)
            if (mReturnedOrderDetailBean.consignorUploadPicJsonObjArr.isNullOrEmpty()) {
                isvReturnImageHz.setVisible(false)
                order_hz_divider.setVisible(false)
            } else {
                //货主回单信息不为空
                order_hz_divider.setVisible(true)
                order_hz_img_textview.setVisible(true)
                mReturnedOrderDetailBean.consignorUploadPicJsonObjArr.apply {
                    val list = arrayListOf<EImage>()
                    forEach { upLoadPic ->
                        val eImage = EImage()
                        eImage.imageId = upLoadPic.pictureUrl
                        eImage.toastStr = upLoadPic.photoId
                        eImage.pictureType = upLoadPic.pictureType
                        list.add(eImage)
                    }
                    isvReturnImageHz.imgList = list
                }
            }
        }
    }

    private fun setUpdateReceiptDeliverWeightAndPic(mReturnedOrderDetailBean: ReturnedOrderDetailBean?) {
        mReturnedOrderDetailBean?.let {
            isvDeliveryImageV2.canSelect = false
            isReceiptReturnImageV2.canSelect = false
            if (it.showEditV1()) {
                //是否能够修改发货信息 默认为0，不能修改 0：不能修改 1：可修改
                tvShippingModification.setVisible(true)
                tvDocumentPhotoV2.setVisible(true)
                tvShippingModification.setOnClickListener {
                    UploadReturnedOrderPhotoActivityV1.jumpPage(
                        activity = this@SingleReturnedOrderConfirmActivityV2,
                        uploadType = false,
                        uploadPhoto = mReturnedOrderDetailBean.consignorDeliverPicUrlArr,
                        requestCode = UPLOAD_PHOTO_V1,
                        weight = tvDeliveryNum.text.toString().trim().replace(
                            mReturnedOrderDetailBean.getDeliverCargoCategoryStr(), ""
                        ),
                        weightUnit = mReturnedOrderDetailBean.getDeliverCargoCategoryStr(),
                        cargoList = mReturnedOrderDetailBean.deliverCargoArray?.toJson(),
                        settleBasisType = mReturnedOrderDetailBean.settleBasisType
                    )
                }
            } else {
                tvShippingModification.setVisible(false)
                tvDocumentPhotoV2.setVisible(false)
            }
            val consignorDeliverPicUrlArr = it.consignorDeliverPicUrlArr
            if (consignorDeliverPicUrlArr == null || consignorDeliverPicUrlArr.size <= 0) {
                tvDocumentPhotoV2.setVisible(false)
                isvDeliveryImageV2.setVisible(false)
                isvDeliveryImageRlV2.setVisible(false)
            } else {
                tvDocumentPhotoV2.setVisible(true)
                isvDeliveryImageV2.setVisible(true)
                isvDeliveryImageRlV2.setVisible(true)
                val list = arrayListOf<EImage>()
                consignorDeliverPicUrlArr.forEach { item ->
                    list.add(EImage(imageId = item.picUrl))
                }
                isvDeliveryImageV2.imgList = list
            }
            val consignorReveivePicUrlArr = it.consignorReveivePicUrlArr
            if (it.showEditV2()) {
                //是否能够修改收货信息 默认为0，不能修改 0：不能修改 1：可修改
                tvReceiptModification.setOnClickListener {
                    UploadReturnedOrderPhotoActivityV1.jumpPage(
                        activity = this@SingleReturnedOrderConfirmActivityV2,
                        uploadType = true,
                        uploadPhoto = mReturnedOrderDetailBean.consignorReveivePicUrlArr,
                        requestCode = UPLOAD_PHOTO_V2,
                        weight = tvReceiptNum.text.toString().trim()
                            .replace(mReturnedOrderDetailBean.getDeliverCargoCategoryStr(), ""),
                        weightUnit = mReturnedOrderDetailBean.getDeliverCargoCategoryStr(),
                        cargoList = mReturnedOrderDetailBean.receiveCargoArray?.toJson(),
                        settleBasisType = mReturnedOrderDetailBean.settleBasisType
                    )
                }
                tvReceiptModification.setVisible(true)
                tvReceiptDocumentPhotoV2.setVisible(true)
            } else {
                tvReceiptModification.setVisible(false)
                tvReceiptDocumentPhotoV2.setVisible(false)
            }
            if (consignorReveivePicUrlArr == null || consignorReveivePicUrlArr.size <= 0) {
                tvReceiptDocumentPhotoV2.setVisible(false)
                isReceiptReturnImageV2.setVisible(false)
                isReceiptReturnImageRlV2.setVisible(false)
            } else {
                tvReceiptDocumentPhotoV2.setVisible(true)
                isReceiptReturnImageV2.setVisible(true)
                isReceiptReturnImageRlV2.setVisible(true)
                val list = arrayListOf<EImage>()
                consignorReveivePicUrlArr.forEach { item ->
                    list.add(EImage(imageId = item.picUrl))
                }
                isReceiptReturnImageV2.imgList = list
            }
            when (it.driverUpdateDeliverWeightFlag) {
                "1" -> {
                    tvCarrierAlreadyEdited.setVisible(true)
                    tvCarrierAlreadyEdited.text = "司机已修改"
                }

                "3" -> {
                    tvCarrierAlreadyEdited.setVisible(true)
                    tvCarrierAlreadyEdited.text = "平台修改"
                }

                else -> {
                    tvCarrierAlreadyEdited.setVisible(false)
                }
            }
            when (it.receiveUpdateDeliverWeightFlag) {
                "1" -> {
                    tvReceiptCarrierAlreadyEdited.setVisible(true)
                    tvReceiptCarrierAlreadyEdited.text = "司机已修改"
                }

                "3" -> {
                    tvReceiptCarrierAlreadyEdited.setVisible(true)
                    tvReceiptCarrierAlreadyEdited.text = "平台修改"
                }

                else -> {
                    tvReceiptCarrierAlreadyEdited.setVisible(false)
                }
            }
        }
    }

    private fun setUiData(mReturnedOrderDetailBean: ReturnedOrderDetailBean) {
        this.mReturnedOrderDetailBean = mReturnedOrderDetailBean
        if (TextUtils.equals(queryType, ReturnedOrderConfirmListFragment.TYPE_NOT_CONFIRM)) {
            lastUptTime = mReturnedOrderDetailBean.lastUptTime
        }
        mReturnedOrderDetailBean.receiptMoneySnapShotObj?.let { data ->
            ll_edit_record.visibility = View.VISIBLE
            ll_edit_record.setOnClickListener {
                OrderEditRecordDialog(this).show(data)
            }
        }
        mReturnedOrderDetailBean.orderIgnoreSmallObj?.let { mOrderIgnoreSmallObj ->
            //抹零相关数据
            mReturnedOrderDetailBeanV1 = mOrderIgnoreSmallObj
            mReturnedOrderDetailBeanV1.mOrderPriceBean.title = mOrderIgnoreSmallObj.ignoreSmallChangeType?.showWipeTypeV3()
            mReturnedOrderDetailBeanV1.mOrderPriceBean.value = mOrderIgnoreSmallObj.ignoreSmallChangeType
        }
        inputViewWipeZero.setVisible(mReturnedOrderDetailBean.orderIgnoreSmallFlag.isTrue)
        mReturnedOrderDetailBeanV1.mOrderPriceBean.let { mOrderPriceBean ->
            inputViewWipeZeroValue.content = mOrderPriceBean.title ?: ""
        }
        when (mReturnedOrderDetailBeanV1.mOrderPriceBean.value) {
            OrderPriceEnum.含税价角分抹零.value,
            OrderPriceEnum.承运方预估到手价十元以下抹零.value,
            OrderPriceEnum.含税价十元以下抹零.value,
            OrderPriceEnum.承运方预估到手价角分抹零.value,
            OrderPriceEnum.含税价五元以下抹零.value,
            OrderPriceEnum.承运方预估到手价五元以下抹零.value,
                -> {
                //抹零规则
                inputViewWipeZero.check = InputViewCheckV2.LEFT
                val sb1 = SpannableHepler().append(
                    SpannableHepler.Txt(
                        mReturnedOrderDetailBean.orderIgnoreSmallObj?.ignoreSmallChangeRateMoney,
                        "#FF5E1C"
                    )
                )
                    .append(SpannableHepler.Txt("(含税)", "#333333"))
                    .builder()
                viewWipeMoney1.text = sb1
                val sb2 = SpannableHepler().append(
                    SpannableHepler.Txt(
                        mReturnedOrderDetailBean.orderIgnoreSmallObj?.ignoreSmallChangeNoRateMoney,
                        "#FF5E1C"
                    )
                )
                    .append(SpannableHepler.Txt("(承运方预估到手价)", "#333333"))
                    .builder()
                viewWipeMoney2.text = sb2
                clWipeZero.setVisible(true)
                inputViewWipeZeroValue.setVisible(true)
            }

            RspAddAndDeductMoney.TYPE_0 -> {
                inputViewWipeZero.check = InputViewCheckV2.RIGHT
                clWipeZero.setVisible(false)
                inputViewWipeZeroValue.setVisible(false)
            }

            else -> {
                //不抹零
                inputViewWipeZero.check = InputViewCheckV2.NONE
                clWipeZero.setVisible(false)
                inputViewWipeZeroValue.setVisible(false)
            }
        }
        if (mReturnedOrderDetailBean.settleApplyState.isTrue) {
            btnSingleConfirm.setVisible(false)
        }
        //承运方预估到手价单价
        if (mReturnedOrderDetailBean.consignorNoRateUnitShowMoneyFlag.isTrue) {
            cl_order_textview12_2.setVisible(true)
            etConsignorNoRateUnitShowMoney.setText(mReturnedOrderDetailBean.consignorNoRateUnitShowMoney)
        } else {
            cl_order_textview12_2.setVisible(false)
        }
        //发货信息-AI发货单据号
        viewAiOrder.setVisible(mReturnedOrderDetailBean.showDeliverReceiptFlag.isTrue)
        if (mReturnedOrderDetailBean.showDeliverReceiptFlag.isTrue) {
            //AI发货单据号
            orderReturnAiDocuments.text = "发货单据号：${mReturnedOrderDetailBean.deliverAiReceiptNumber ?: ""}"
            //确认的发货单据号
            inputAiOrderNum.setText(mReturnedOrderDetailBean.deliverReceiptNumber ?: "")
        }
        isvDeliveryTime.text = mReturnedOrderDetailBean.showDeliverTime()
        //发货信息-上传时间(确认发货时间)
        isvDeliveryTime.text = mReturnedOrderDetailBean.showDeliverTime()
        //回单信息-上传时间(确认收货时间)
        isvReturnTime.text = mReturnedOrderDetailBean.showReceiveTime()
        //发货货物明细
        mUploadReturnedOrderPhotoAdapterV11.canEdit = false
        mUploadReturnedOrderPhotoAdapterV11.str = UploadReturnedOrderPhotoAdapterV1.STR_1
        mUploadReturnedOrderPhotoAdapterV11.setNewData(mReturnedOrderDetailBean.deliverCargoArray)
        //收货货物明细
        mUploadReturnedOrderPhotoAdapterV12.canEdit = false
        mUploadReturnedOrderPhotoAdapterV12.str = UploadReturnedOrderPhotoAdapterV1.STR_2
        mUploadReturnedOrderPhotoAdapterV12.setNewData(mReturnedOrderDetailBean.receiveCargoArray)

        mReleaseLossTonData = mReturnedOrderDetailBean.releaseLossTonData?.releaseLossTonType ?: ""
        setUpdateReceiptDeliverWeightAndPic(mReturnedOrderDetailBean)
        initOrderCustomNumber(mReturnedOrderDetailBean.selfComment)
        initRemarker(mReturnedOrderDetailBean)
        // 运单信息
        returned_order_info_view.setOrderId(orderId)
        returned_order_info_view.setData(mReturnedOrderDetailBean)

        EditTextClearTextWatcher.clear(etConfirmSettlementPriceIncludedTax)
        EditTextClearTextWatcher.clear(etConfirmSettlementPriceNotIncludedTax)
        EditTextClearTextWatcher.clear(etConsignorNoRateUnitShowMoney)

        tvDeliveryNum.text = mReturnedOrderDetailBean.getDeliverWeightStr() + mReturnedOrderDetailBean.getDeliverCargoCategoryStr()
        tvJiaKouKuanXiangUnit.text = mReturnedOrderDetailBean.getDeliverCargoCategoryStr()
        etConfirmSettlementTonnage.setText(mReturnedOrderDetailBean.slipLoad)
        val includeTaxPrice = mReturnedOrderDetailBean.consignorRateMoney
        etConfirmSettlementPriceIncludedTax.setText(includeTaxPrice)
        val noTaxPrice = mReturnedOrderDetailBean.consignorNoRateMoney
        etConfirmSettlementPriceNotIncludedTax.setText(noTaxPrice)
        orderSettlementBasis.text = mReturnedOrderDetailBean.settleBasisTypeName
        //回款到期日
        tvExpiryDateStr.text = mReturnedOrderDetailBean.getReturnMoneyTime()
        tvExpiryDateStr.setVisible(mReturnedOrderDetailBean.showReturnMoneyTime())
        tvExpiryDate.setVisible(mReturnedOrderDetailBean.showReturnMoneyTime())
        ivDocumentPhotoSwitch.setVisible(mReturnedOrderDetailBean.deliverOrgPicFlag.isTrue)
        ivReceiptDocumentPhotoSwitch.setVisible(mReturnedOrderDetailBean.receiveOrgPicFlag.isTrue)
        //单据照片
        if (mReturnedOrderDetailBean.driverDeliverProofPicJsonArr.isNullOrEmpty()) {
            isvDeliveryImage.visibility = View.GONE
            isvDeliveryImageRl.visibility = View.GONE
            tvDocumentPhoto.visibility = View.GONE
        } else {
            tvDocumentPhoto.visibility = View.VISIBLE
            isvDeliveryImage.visibility = View.VISIBLE
            isvDeliveryImageRl.visibility = View.VISIBLE
            isvDeliveryImage.imgList =
                ArrayList(mReturnedOrderDetailBean.driverDeliverProofPicJsonArr.map { EImage(imageId = it.picUrl) })
        }
        jmyl_warning.setVisible(!TextUtils.isEmpty(mReturnedOrderDetailBean.differenceV1()))
        tv_warning_jmyl.text = mReturnedOrderDetailBean.differenceV1()
        //运单照片
        if (mReturnedOrderDetailBean.driverDeliverPhotoPicJsonArr.isNullOrEmpty()) {
            isvDeliveryImageV1.visibility = View.GONE
            isvDeliveryImageRlV1.visibility = View.GONE
            tvDocumentPhotoV1.visibility = View.GONE
        } else {
            tvDocumentPhotoV1.visibility = View.VISIBLE
            isvDeliveryImageV1.visibility = View.VISIBLE
            isvDeliveryImageRlV1.visibility = View.VISIBLE
            isvDeliveryImageV1.imgList =
                ArrayList(mReturnedOrderDetailBean.driverDeliverPhotoPicJsonArr.map { EImage(imageId = it.picUrl) })
        }
        //挂车车牌照片
        if (mReturnedOrderDetailBean.deliverTrailerPicList.isNullOrEmpty()) {
            isvDeliveryImageV3.visibility = View.GONE
            isvDeliveryImageRlV3.visibility = View.GONE
            tvDocumentPhotoV3.visibility = View.GONE
        } else {
            tvDocumentPhotoV3.visibility = View.VISIBLE
            isvDeliveryImageV3.visibility = View.VISIBLE
            isvDeliveryImageRlV3.visibility = View.VISIBLE
            isvDeliveryImageV3.imgList =
                ArrayList(mReturnedOrderDetailBean.deliverTrailerPicList.map { EImage(imageId = it.picUrl) })
        }
        //发货备注
        isvDeliveryRemark.text = mReturnedOrderDetailBean.showCarrierDeliverRemark()
        //收货备注
        isvReturnRemark.text = mReturnedOrderDetailBean.showCarrierReceiveRemark()
        //发货视频
        isvDeliveryVideoV1.imgList = mReturnedOrderDetailBean.showVideo1()
        isvDeliveryVideoV1.setVisible(mReturnedOrderDetailBean.showVideo1().isNotEmpty())
        //收货视频
        isvReturnVideoV1.imgList = mReturnedOrderDetailBean.showVideo2()
        isvReturnVideoV1.setVisible(mReturnedOrderDetailBean.showVideo2().isNotEmpty())
        val receiveWeight = mReturnedOrderDetailBean.receiveWeight
        tvReceiptNum.text = receiveWeight + mReturnedOrderDetailBean.getDeliverCargoCategoryStr()
        //ZCZY-7729 冀东定制化需求
        inputOutStageTime.content = mReturnedOrderDetailBean.outTime ?: ""
        if (TextUtils.equals("1", mReturnedOrderDetailBean.isShowOutTime)) {
            clOutStageTime.setVisible(true)
        } else {
            clOutStageTime.setVisible(false)
        }

        //回单信息
        if (TextUtils.isEmpty(mReturnedOrderDetailBean.backStatus) || TextUtils.equals(
                "0",
                mReturnedOrderDetailBean.backStatus
            ) || TextUtils.equals("3", mReturnedOrderDetailBean.backStatus)
        ) {
            order_hz_img_up.setVisible(true)
            order_hz_divider.setVisible(true)
            order_hz_img_textview.setVisible(true)
            mReturnedOrderDetailBean.consignorUploadPicJsonObjArr?.apply {
                var imageSize = size
                val list = arrayListOf<EImage>()
                forEach { upLoadPic ->
                    val eImage = EImage()
                    eImage.imageId = upLoadPic.pictureUrl
                    eImage.toastStr = upLoadPic.photoId
                    eImage.pictureType = upLoadPic.pictureType
                    list.add(eImage)
                    when (upLoadPic.pictureType) {
                        "13" -> {
                            imageSize -= 1
                        }
                    }
                }
                isvReturnImageHz.imgList = list
                if (imageSize >= 5) {
                    order_hz_img_up.setVisible(false)
                } else {
                    order_hz_img_up.setVisible(true)
                }
            }
        } else {
            order_hz_img_up.setVisible(false)
            order_hz_img_textview.setVisible(false)
            if (mReturnedOrderDetailBean.consignorUploadPicJsonObjArr.isNullOrEmpty()) {
                isvReturnImageHz.setVisible(false)
                order_hz_divider.setVisible(false)
            } else {
                //货主回单信息不为空
                order_hz_divider.setVisible(true)
                order_hz_img_textview.setVisible(true)
                mReturnedOrderDetailBean.consignorUploadPicJsonObjArr.apply {
                    val list = arrayListOf<EImage>()
                    forEach { upLoadPic ->
                        val eImage = EImage()
                        eImage.imageId = upLoadPic.pictureUrl
                        eImage.toastStr = upLoadPic.photoId
                        eImage.pictureType = upLoadPic.pictureType
                        list.add(eImage)
                    }
                    isvReturnImageHz.imgList = list
                }
            }
        }
        if (mReturnedOrderDetailBean.driverReceiveProofPicJsonArr.isNullOrEmpty()) {
            isvReturnImage.setVisible(false)
            isvReturnImageRl.setVisible(false)
            tvReceiptDocumentPhoto.setVisible(false)
        } else {
            tvReceiptDocumentPhoto.setVisible(true)
            isvReturnImage.setVisible(true)
            isvReturnImageRl.setVisible(true)
            isvReturnImage.imgList =
                ArrayList(mReturnedOrderDetailBean.driverReceiveProofPicJsonArr.map { EImage(imageId = it.picUrl) })
        }
        if (mReturnedOrderDetailBean.examinePicUrlArr.isNullOrEmpty()) {
            tvexaminePicUrlArr.setVisible(false)
            isexaminePicUrlArr.setVisible(false)
            isexaminePicUrlArrImage.setVisible(false)
        } else {
            tvexaminePicUrlArr.setVisible(true)
            isexaminePicUrlArr.setVisible(true)
            isexaminePicUrlArrImage.setVisible(true)
            isexaminePicUrlArrImage.canSelect = false
            isexaminePicUrlArrImage.imgList =
                ArrayList(mReturnedOrderDetailBean.examinePicUrlArr.map { EImage(imageId = it.picUrl) })
        }

        if (mReturnedOrderDetailBean.driverReceivePhotoPicJsonArr.isNullOrEmpty()) {
            isvReturnImageV1.setVisible(false)
            isvReturnImageRlV1.setVisible(false)
            tvReceiptDocumentPhotoV1.setVisible(false)
        } else {
            isvReturnImageV1.setVisible(true)
            isvReturnImageRlV1.setVisible(true)
            tvReceiptDocumentPhotoV1.setVisible(true)
            isvReturnImageV1.imgList =
                ArrayList(mReturnedOrderDetailBean.driverReceivePhotoPicJsonArr.map { EImage(imageId = it.picUrl) })
            ivReceiptDocumentPhotoSwitch.tag = true
        }

        // 卸货图片
        if (mReturnedOrderDetailBean.carrierReveivePicUrlArr.isNullOrEmpty()) {
            rl_clockin.setVisible(false)
        } else {
            rl_clockin.setVisible(true)
            image_clockin.imgList =
                ArrayList(mReturnedOrderDetailBean.carrierReveivePicUrlArr?.map { EImage(imageId = it.picUrl) })
        }
        // 挂车车牌合影
        if (mReturnedOrderDetailBean.receiveTrailerPicList.isNullOrEmpty()) {
            isvReceiptReturnImageV3.setVisible(false)
            isvReceiptReturnImageRlV3.setVisible(false)
            tvReceiptDocumentPhotoV3.setVisible(false)
        } else {
            isvReceiptReturnImageV3.setVisible(true)
            isvReceiptReturnImageRlV3.setVisible(true)
            tvReceiptDocumentPhotoV3.setVisible(true)
            isvReceiptReturnImageV3.imgList =
                ArrayList(mReturnedOrderDetailBean.receiveTrailerPicList.map { EImage(imageId = it.picUrl) })
        }

        //集装箱数据
        when (mReturnedOrderDetailBean.goodsSource) {
            "1" -> {
                inputReceiptContainer.setVisible(true)
                inputReceiptInformationContainer.setVisible(true)
                recyclerViewContainer.setVisible(true)
            }

            else -> {
                inputReceiptContainer.setVisible(false)
                inputReceiptInformationContainer.setVisible(false)
                recyclerViewContainer.setVisible(false)
            }
        }
        mReturnedOrderDetailBean.containerDataObj?.let { ob1 ->
            ob1.receiptContainerDataObj?.let { ob2 ->
                //实际发货
                inputReceiptContainer.tvContent.apply {
                    text = ob2.containerName + "*" + ob2.containerNoJsonArray.size + "箱"
                }
                //实际收货
                inputReceiptInformationContainer.tvContent.apply {
                    text = ob2.containerName + "*" + ob2.containerNoJsonArray.size + "箱"
                }
                //结算信息
                ob2.containerNoJsonArray.let { ob3 ->
                    if (ob3.size > 0) {
                        mReceiptContainerAdapter.setNewData(ob2.containerNoJsonArray)
                    }
                }
            }
        }

        //现场证据照片
        mReturnedOrderDetailBean.nativeEvidencePicJsonArr?.let { list ->
            rp_evidence_img.setImgData(ArrayList(list.map { EImage(imageId = it) }))
        }
        //结算金额（含税价）
        UtilRxView.afterTextChangeEvents(etConfirmSettlementPriceIncludedTax, 500) { content ->
            if (etConfirmSettlementPriceIncludedTax.isFocused) {
                val inContent = content.toString()
                //结算金额(含税价)
                <EMAIL>?.consignorRateMoney =
                    inContent
                //结算金额(含税价): 推算->承运方预估到手价(不含税价)
                val money1 = getMoneyRateText(
                    inContent,
                    <EMAIL>?.settleRate,
                    <EMAIL>?.consignorNoRateMoney
                )
                <EMAIL>?.consignorNoRateMoney =
                    money1
                //结算金额(含税价): 推算->承运方预估到手价单价
                val money2 = computeMoney2(
                    <EMAIL>?.consignorNoRateMoney,
                    <EMAIL>?.slipLoad
                )
                <EMAIL>?.consignorNoRateUnitShowMoney =
                    money2
                etConfirmSettlementPriceNotIncludedTax.setText(money1)
                etConsignorNoRateUnitShowMoney.setText(money2)
                handler.removeCallbacks(delayRun)
                //延迟1000ms，如果不再输入字符，则执行该线程的run方法
                handler.postDelayed(delayRun, 1000)
            }
        }

        //结算金额（含税价）
        etConfirmSettlementPriceNotIncludedTax.addTextChangedListener(object : TextWatcher {
            override fun beforeTextChanged(s: CharSequence?, start: Int, count: Int, after: Int) {}

            override fun onTextChanged(s: CharSequence?, start: Int, before: Int, count: Int) {
                if (!s.isNullOrEmpty()) {
                    try {
                        val value = s.toString().toDouble()
                        if (value > 9999999.99) {
                            etConfirmSettlementPriceNotIncludedTax.error = "整数只能输入7位"
                        }
                    } catch (e: NumberFormatException) {
                        e.printStackTrace()
                    }
                }
                tvCheckMonth.setVisible(mReturnedOrderDetailBean.isShowCheckMonth(s.toString()).isNotEmpty())
                tvCheckMonth.text = mReturnedOrderDetailBean.isShowCheckMonth(s.toString())
            }

            override fun afterTextChanged(s: Editable?) {
                if (etConfirmSettlementPriceNotIncludedTax.isFocused) {
                    val inContent = s.toString()
                    //承运方预估到手价(不含税价)
                    <EMAIL>?.consignorNoRateMoney =
                        inContent
                    //承运方预估到手价(不含税价): 返推算->结算金额(含税价)
                    val money1 = getTurnMoney(
                        inContent,
                        <EMAIL>?.settleRate,
                        <EMAIL>?.consignorRateMoney
                    )
                    <EMAIL>?.consignorRateMoney =
                        money1
                    //承运方预估到手价(不含税价): 推算->承运方预估到手价单价
                    val money2 = computeMoney2(
                        <EMAIL>?.consignorNoRateMoney,
                        <EMAIL>?.slipLoad
                    )
                    <EMAIL>?.consignorNoRateUnitShowMoney =
                        money2
                    etConfirmSettlementPriceIncludedTax.setText(money1)
                    etConsignorNoRateUnitShowMoney.setText(money2)
                    handler.removeCallbacks(delayRun)
                    //延迟1000ms，如果不再输入字符，则执行该线程的run方法
                    handler.postDelayed(delayRun, 1000)
                }
            }
        })

        etConsignorNoRateUnitShowMoney.addTextChangedListener(object : TextWatcher {
            override fun beforeTextChanged(s: CharSequence?, start: Int, count: Int, after: Int) {}

            override fun onTextChanged(s: CharSequence?, start: Int, before: Int, count: Int) {
                if (!s.isNullOrEmpty()) {
                    try {
                        val value = s.toString().toDouble()
                        if (value > 9999999.99) {
                            etConsignorNoRateUnitShowMoney.error = "整数只能输入7位"
                        }
                    } catch (e: NumberFormatException) {
                        e.printStackTrace()
                    }
                }
            }

            override fun afterTextChanged(s: Editable?) {
                if (etConsignorNoRateUnitShowMoney.isFocused) {
                    val inContent = s.toString()
                    //承运方预估到手价单价
                    <EMAIL>?.consignorNoRateUnitShowMoney =
                        inContent
                    //承运方预估到手价单价: 返推算->承运方预估到手价(不含税价)
                    val money1 = computeMoney1(
                        <EMAIL>?.consignorNoRateUnitShowMoney,
                        <EMAIL>?.slipLoad
                    )
                    <EMAIL>?.consignorNoRateMoney =
                        money1
                    //承运方预估到手价(不含税价): 返推算->结算金额(含税价)
                    val money2 = getTurnMoney(
                        <EMAIL>?.consignorNoRateMoney
                            ?: "",
                        <EMAIL>?.settleRate,
                        <EMAIL>?.consignorRateMoney
                    )
                    <EMAIL>?.consignorRateMoney =
                        money2
                    etConfirmSettlementPriceIncludedTax.setText(money1)
                    etConfirmSettlementPriceNotIncludedTax.setText(money2)
                    handler.removeCallbacks(delayRun)
                    //延迟1000ms，如果不再输入字符，则执行该线程的run方法
                    handler.postDelayed(delayRun, 1000)
                }
            }
        })

        val replace11 = tvReceiptNum.text.toString().trim()
            .replace(mReturnedOrderDetailBean.getDeliverCargoCategoryStr(), "")
        val replace21 = tvDeliveryNum.text.toString().trim()
            .replace(mReturnedOrderDetailBean.getDeliverCargoCategoryStr(), "")
        smallLabel?.setCallback(object : SmallKuiDunCalcView.SmallKuiDunCalcCallback {
            override fun agreeOrNotYiJu(isAgree: Boolean) {
                consignorSmartDeficitTonUseFlag = if (isAgree) {
                    RspAddAndDeductMoney.TYPE_1
                } else {
                    RspAddAndDeductMoney.TYPE_0
                }
                val check = smallLabel?.getRulePrice() ?: false
                if (check) {
                    calc(
                        priceDoesNotIncludeTax = smallLabel?.getTaxNotIncludedPrice() ?: "0.00",
                        rate = mReturnedOrderDetailBean.getSettleRate().toString()
                    )
                } else {
                    calc(
                        priceDoesNotIncludeTax = etConfirmSettlementPriceNotIncludedTax.text.toString(),
                        rate = mReturnedOrderDetailBean.getSettleRate().toString()
                    )
                }
            }

            override fun includeOrNotTax(isIncludeTax: Boolean) {

                val replace1 = tvReceiptNum.text.toString().trim()
                    .replace(
                        <EMAIL>?.getDeliverCargoCategoryStr()
                            ?: "", ""
                    )
                val replace2 = tvDeliveryNum.text.toString().trim()
                    .replace(
                        <EMAIL>?.getDeliverCargoCategoryStr()
                            ?: "", ""
                    )
                viewModel?.queryReceiptLossRiseDetailInfoByAppChangeRuleId(
                    req = ReqQueryReceiptLossRiseDetailInfoByAppChangeRuleId(
                        ruleId = smallLabel?.getRuleItem(),
                        orderId = orderId,
                        receiveWeight = replace1,
                        deliverWeight = replace2,
                        consignorRateMoney = etConfirmSettlementPriceIncludedTax.text.toString(),
                        consignorSmartDeficitTonUseRateType = smallLabel?.getConsignorSmartDeficitTonUseRateType()
                    )
                )
            }

            override fun showDialog(kuiOrChaoType: String, pos: Int) {
                when (kuiOrChaoType) {
                    "1" -> {
                        val dialog = DialogBuilder()
                        if (pos == 1) {
                            dialog.message = "亏吨，即发货吨位>收货吨位，亏吨数=发货吨位-收货吨位"
                        } else {
                            dialog.message =
                                "允许亏吨数=发货吨位*允许亏吨率;允许超吨数=发货吨位*允许超吨率"
                        }
                        dialog.isHideCancel = true
                        <EMAIL>(dialog)
                    }

                    "2" -> {
                        val dialog = DialogBuilder()
                        if (pos == 1) {
                            dialog.message = "超吨，即收货吨位>发货吨位，超吨数=收货吨位-发货吨位"
                        } else {
                            dialog.message =
                                "允许亏吨数=发货吨位*允许亏吨率;允许超吨数=发货吨位*允许超吨率"
                        }
                        dialog.isHideCancel = true
                        <EMAIL>(dialog)
                    }

                    else -> {
                    }
                }
            }

            override fun selectCalcDeploy(ruleId: String, ruleName: String) {
                //选择扣款配置
                viewModel?.selectCalcDeploy(ReqDeductionDeploy(ruleId = ruleId), ruleName)
            }

            override fun tvTaxIncludedPrice(taxIncludedPrice: String) {
                //亏吨后结算价格
                mTaxIncludedPrice = taxIncludedPrice
            }

            override fun onSelectRule(ruleId: String?) {
                // 亏涨吨详情
                X5WebActivity.startNoTitleContentUI(
                    this@SingleReturnedOrderConfirmActivityV2,
                    HttpConfig.getWebUrl() + "/form_h5/h5_inner/index.html?_t=${System.currentTimeMillis()}#/lossRiseRule?ruleId=${ruleId}&orderId=${orderId}"
                )
            }

            override fun onClickRule() {
                val replace1 = tvReceiptNum.text.toString().trim()
                    .replace(mReturnedOrderDetailBean.getDeliverCargoCategoryStr(), "")
                val replace2 = tvDeliveryNum.text.toString().trim()
                    .replace(mReturnedOrderDetailBean.getDeliverCargoCategoryStr(), "")
                viewModel?.queryReceiptLossRiseRule(
                    req = ReqReturnQueryReceiptLossRiseRule(
                        orderId = orderId,
                        receiveWeight = replace1,
                        deliverWeight = replace2,
                        consignorCheckMoney = etConfirmSettlementPriceIncludedTax.text.toString()
                            .trim(),
                        configType = mReleaseLossTonData.ifEmpty {
                            if (replace2.toDouble() > replace1.toDouble()) {
                                "1"
                            } else {
                                "2"
                            }
                        }
                    ))
            }

            override fun onLossRiseType(lossRiseType: String) {
                <EMAIL>?.releaseLossTonData?.releaseLossTonType =
                    lossRiseType
            }
        })
        smallLabel?.setVisible(false)
        if (mReturnedOrderDetailBean.releaseLossTonData?.consignorSmartDeficitTonFlag.isTrue && !TextUtils.equals(
                replace11,
                replace21
            )
        ) {
            consignorSmartDeficitTonUseFlag =
                mReturnedOrderDetailBean.releaseLossTonData?.consignorSmartDeficitTonFlag ?: ""
            if (mReturnedOrderDetailBean.showRule()) {
                smallLabel?.setData(mReturnedOrderDetailBean, orderId)
                smallLabel?.setVisible(true)
            } else {
                smallLabel?.setVisible(false)
            }
        }
        if (mReturnedOrderDetailBean.consignorDeductionFlag.isTrue) {
            confirmJiaKouInfo.setVisible(true)
            when (mReturnedOrderDetailBean.deductionType) {
                RspAddAndDeductMoney.TYPE_1 -> {
                    tvJiaKouKuanXiang.text = "加款"
                }

                RspAddAndDeductMoney.TYPE_2 -> {
                    tvJiaKouKuanXiang.text = "扣款"
                }

                else -> {
                    tvJiaKouKuanXiang.text = "无"
                }
            }
            tvTaxIncludedPrice.text = mReturnedOrderDetailBean.consignorDeductiorRateMoney
            tvTaxNotIncludedPrice.text = mReturnedOrderDetailBean.consignorDeductiorNoRateMoney
            addAndDeductMoneyList =
                mReturnedOrderDetailBean.consignorDeductionObj?.deductionDetailArray
                    ?: mutableListOf()
            val check = smallLabel?.getRulePrice() ?: false
            if (check) {
                calc(
                    priceDoesNotIncludeTax = smallLabel?.getTaxNotIncludedPrice() ?: "0.00",
                    rate = mReturnedOrderDetailBean.getSettleRate().toString()
                )
            } else {
                calc(
                    priceDoesNotIncludeTax = etConfirmSettlementPriceNotIncludedTax.text.toString(),
                    rate = mReturnedOrderDetailBean.getSettleRate().toString()
                )
            }
        } else {
            confirmJiaKouInfo.setVisible(false)
        }
        when (mReturnedOrderDetailBean.receiptEditSettleFlag) {
            "1" -> {
                //禁止修改
                etConfirmSettlementTonnage.isEnabled = false
                etConfirmSettlementPriceIncludedTax.isEnabled = false
                etConfirmSettlementPriceNotIncludedTax.isEnabled = false
            }

            else -> {
                etConfirmSettlementTonnage.isEnabled =
                    mReturnedOrderDetailBean.updateSettleWeightFlag.isTrue
                etConfirmSettlementPriceIncludedTax.isEnabled =
                    mReturnedOrderDetailBean.consignorRateMoneyUpdateFlag.isTrue
                etConfirmSettlementPriceNotIncludedTax.isEnabled = true
            }
        }
        UtilRxView.afterTextChangeEvents(etConfirmSettlementTonnage, 500) { content ->
            if (etConfirmSettlementTonnage.isFocused) {
                confirmSettlementTonnage(content.toString())
            }
        }
        if (mReturnedOrderDetailBean.freightType.isTrue) {
            orderSettleTypeUnit.text =
                "元/${mReturnedOrderDetailBean.getDeliverCargoCategoryStrV1()}"
            orderSettleType.text = "运费计价方式(含税单价)"
            //单价
            orderSettleTypeUnitEditText.setText(mReturnedOrderDetailBean.pbConsignorUnitMoney)
            when (mReturnedOrderDetailBean.receiptEditSettleFlag) {
                "1" -> {
                    //禁止修改
                    orderSettleTypeUnitEditText.isEnabled = false
                }

                else -> {
                    orderSettleTypeUnitEditText.isEnabled =
                        mReturnedOrderDetailBean.consignorRateMoneyUpdateFlag.isTrue
                }
            }
        } else {
            //包车价
            orderSettleTypeUnitEditText.setText(mReturnedOrderDetailBean.pbConsignorMoney)
            orderSettleType.text = "运费计价方式(含税包车价)"
            orderSettleTypeUnitEditText.isEnabled = false
            orderSettleTypeUnit.text = "元"
        }
        UtilRxView.afterTextChangeEvents(orderSettleTypeUnitEditText, 800) { m ->
            if (mReturnedOrderDetailBean.freightType.isTrue) {
                //运费计价方式（单价）
                val m1 = if (mReturnedOrderDetailBean.goodsSource.isTrue) {
                    //集装箱货源
                    mReturnedOrderDetailBean.showContainerNum()
                } else {
                    etConfirmSettlementTonnage.text.toString().toDoubleOrNull() ?: 0.00
                }
                val m2 = m.toString().toDoubleOrNull() ?: 0.00
                val money = NumUtil.mul(m1, m2).toString().toDoubleRoundDownString(2)
                <EMAIL>?.consignorRateMoney =
                    money
                etConfirmSettlementPriceIncludedTax.setText(money)
                //结算金额(含税价): 推算->承运方预估到手价(不含税价)
                val money1 = getMoneyRateText(
                    <EMAIL>?.consignorRateMoney,
                    <EMAIL>?.settleRate,
                    <EMAIL>?.consignorNoRateMoney
                )
                <EMAIL>?.consignorNoRateMoney =
                    money1
                //结算金额(含税价): 推算->承运方预估到手价单价
                val money2 = computeMoney2(
                    <EMAIL>?.consignorNoRateMoney,
                    <EMAIL>?.slipLoad
                )
                <EMAIL>?.consignorNoRateUnitShowMoney =
                    money2
                etConfirmSettlementPriceNotIncludedTax.setText(money1)
                etConsignorNoRateUnitShowMoney.setText(money2)
                calc(
                    priceDoesNotIncludeTax = etConfirmSettlementPriceNotIncludedTax.text.toString(),
                    rate = <EMAIL>?.getSettleRate()
                        .toString()
                )
            }
        }

        //是否展示备案卸货
        if (mReturnedOrderDetailBean.isBackUpReceive == "1") {
            cl_isBackUp.visibility = View.VISIBLE
        }
        // 初始化亏吨费计算器
        if (mReturnedOrderDetailBean.deficitCalculatorShowFlag == "1") {
            tvLossTonnageCalc.visibility = View.VISIBLE
            initCalc(
                mReturnedOrderDetailBean.getDeliverWeightStr(),
                receiveWeight,
                mReturnedOrderDetailBean.cargoUnitMoney,
                includeTaxPrice.toString(),
                noTaxPrice.toString()
            )

            btnCloseDrawer.setOnClickListener {
                drawerLayout.closeDrawer(endDrawer)
            }

            btnReset.setOnClickListener {
                resetCalc(
                    mReturnedOrderDetailBean.getDeliverWeightStr(),
                    receiveWeight,
                    mReturnedOrderDetailBean.cargoUnitMoney,
                    includeTaxPrice.toString(),
                    noTaxPrice.toString()
                )
            }
        } else {
            tvLossTonnageCalc.visibility = View.GONE
            drawerLayout.setDrawerLockMode(DrawerLayout.LOCK_MODE_LOCKED_CLOSED)
        }

        if (mReturnedOrderDetailBean.weightExceptionMsg.isNullOrBlank()) {
            tvWeightExceptionMsg.visibility = View.GONE
        } else {
            tvWeightExceptionMsg.visibility = View.VISIBLE
            tvWeightExceptionMsg.text = mReturnedOrderDetailBean.weightExceptionMsg
        }
        if (mReturnedOrderDetailBean.backRegectReason.isNullOrEmpty()) {
            deniedInfo.visibility = View.GONE
        } else {
            tvReturnReason.text = mReturnedOrderDetailBean.backRegectReason
        }

        if (mReturnedOrderDetailBean.dealReason.isNullOrEmpty()) {
            dclInfo.visibility = View.GONE
        } else {
            tvdclReason.text = mReturnedOrderDetailBean.dealReason
        }
        //平台审核信息

        when (mReturnedOrderDetailBean.orderReceiptCheckState) {
            // 平台审核结果  0:"未审核"1:"异常"2:"审核通过"
            "0" -> {
                tvVerifyResult.text = "待审核"
            }

            "1" -> {
                tvVerifyResult.text = "回单异常"
            }

            "2" -> {
                tvVerifyResult.text = "回单通过"
            }

            "3" -> {
                tvVerifyResult.text = "回单打回"
            }

            else -> {
                tvVerifyResult.text = ""
            }
        }

        when (mReturnedOrderDetailBean.orderReceiptToDealFlag) {
            //  0:不处理1:去处理
            "0" -> {
                tvDeal.visibility = View.GONE
            }

            "1" -> {
                tvDeal.visibility = View.VISIBLE
            }
        }
        clSuggestion.visibility = View.VISIBLE
        val orderReceiptItemReason1 = mReturnedOrderDetailBean.orderReceiptItemReason
        when (TextUtils.isEmpty(orderReceiptItemReason1)) {
            true -> {
                tvSuggestionResult.text = ""
            }

            else -> {
                tvSuggestionResult.text = orderReceiptItemReason1
            }

        }

        when (mReturnedOrderDetailBean.orderTraceExceptionFlag) {
            "1" -> {
                clAbnormalTrajectory.visibility = View.VISIBLE
                when (mReturnedOrderDetailBean.orderTraceExceptionToDealFlag) {
                    //轨迹异常去处理 0:不处理1:去处理
                    "1" -> {
                        tvAbnormalTrajectoryDeal.visibility = View.VISIBLE
                    }

                    else -> {
                        tvAbnormalTrajectoryDeal.visibility = View.GONE
                    }
                }
                val orderTraceExceptionItemReason =
                    mReturnedOrderDetailBean.orderTraceExceptionItemReason
                when (TextUtils.isEmpty(orderTraceExceptionItemReason)) {
                    true -> {
                        tvAbnormalTrajectoryResult.text = ""
                    }

                    else -> {
                        tvAbnormalTrajectoryResult.text = orderTraceExceptionItemReason
                    }
                }
            }

            else -> {
                clAbnormalTrajectory.visibility = View.GONE
            }
        }

        // 初始化时重新计算抹零金额，确保页面首次加载时显示正确的计算结果
        // 只有在抹零功能启用且选择了抹零规则时才进行计算
        if (inputViewWipeZero.visibility == View.VISIBLE && inputViewWipeZero.check == InputViewCheckV2.LEFT) {
            computeViewWipeMoney1()
        }
    }

    private fun computeViewWipeMoney1() {
        var money1: Double  // 结算金额(含税) 用于抹零最终计算
        var money2: Double  // 结算金额(承运方预估到手价) 用于抹零最终计算
        //计算抹零
        val check = smallLabel?.getRulePrice() ?: false
        if (check) {
            //亏涨吨
            money1 = smallLabel?.getTaxIncludedPrice()?.toDoubleOrNull() ?: 0.00
            money2 = smallLabel?.getTaxNotIncludedPrice()?.toDoubleOrNull() ?: 0.00
            //加扣款总金额
            val computeMoney = addAndDeductMoneyList.computeMoney()
            //加扣款
            money2 = NumUtil.sum(money2, computeMoney)
            if (computeMoney != 0.00) {
                money1 = NumUtil.mul(money2, NumUtil.sum(1.00, mReturnedOrderDetailBean.getSettleRate()))
            }
        } else {
            //未使用亏涨吨
            money1 = etConfirmSettlementPriceIncludedTax.text.toString().trim().toDoubleOrNull() ?: 0.00
            money2 = etConfirmSettlementPriceNotIncludedTax.text.toString().trim().toDoubleOrNull() ?: 0.00
            //加扣款总金额
            val computeMoney = addAndDeductMoneyList.computeMoney()
            //加扣款
            money2 = NumUtil.sum(money2, computeMoney)
            if (computeMoney != 0.00) {
                money1 = NumUtil.mul(money2, NumUtil.sum(1.00, mReturnedOrderDetailBean.getSettleRate()))
            }
        }

        //抹零金额相关计算
        when (mReturnedOrderDetailBeanV1.mOrderPriceBean.value) {
            OrderPriceEnum.含税价角分抹零.value -> {
                wipeMoney1(money1, money2)
            }

            OrderPriceEnum.承运方预估到手价十元以下抹零.value -> {
                wipeMoney2(money2, money1)
            }

            OrderPriceEnum.含税价十元以下抹零.value -> {
                wipeMoney3(money1, money2)
            }

            OrderPriceEnum.承运方预估到手价角分抹零.value -> {
                wipeMoney4(money2, money1)
            }

            OrderPriceEnum.含税价五元以下抹零.value -> {
                wipeMoney5(money1, money2)
            }

            OrderPriceEnum.承运方预估到手价五元以下抹零.value -> {
                wipeMoney6(money2, money1)
            }
        }
    }

    private fun wipeMoney6(money2: Double, money1: Double) {
        //5元以下抹零 抹零金额除以5 向下取整
        val mo1 = NumUtil.div(money2, 5.00, 2).roundTo2DecimalPlacesV1()
        val mo2 = NumUtil.mul(mo1, 5.00)
        mReturnedOrderDetailBeanV1.ignoreSmallChangeNoRateMoney = mo2.toDoubleRoundDownString(8)
        val sub = NumUtil.sub(money2, mo2)
        //推算结算价格（承运方预估到手价） 5元以下抹零
        val rate = NumUtil.sum(1.00, mReturnedOrderDetailBean.getSettleRate())
        if (sub != 0.00) {
            //抹零金额为0时不逆推
            mReturnedOrderDetailBeanV1.ignoreSmallChangeRateMoney = NumUtil.mul(mo2, rate).toDoubleRoundDownString(8)
        } else {
            mReturnedOrderDetailBeanV1.ignoreSmallChangeRateMoney = money1.toDoubleRoundDownString(2)
        }
        val sb = SpannableHepler()
            .append(SpannableHepler.Txt(mReturnedOrderDetailBeanV1.ignoreSmallChangeNoRateMoney.toDoubleRoundDownString(2), "#FF5E1C"))
            .append(SpannableHepler.Txt("(承运方预估到手价)", "#333333"))
            .builder()
        viewWipeMoney2.text = sb
        val sb1 = SpannableHepler()
            .append(SpannableHepler.Txt(mReturnedOrderDetailBeanV1.ignoreSmallChangeRateMoney.toDoubleRoundDownString(2), "#FF5E1C"))
            .append(SpannableHepler.Txt("(含税)", "#333333"))
            .builder()
        viewWipeMoney1.text = sb1
    }

    private fun wipeMoney5(money1: Double, money2: Double) {
        //5元以下抹零 抹零金额除以5 向下取整
        val mo1 = NumUtil.div(money1, 5.00, 2).roundTo2DecimalPlacesV1()
        val mo2 = NumUtil.mul(mo1, 5.00)
        mReturnedOrderDetailBeanV1.ignoreSmallChangeRateMoney = mo2.toDoubleRoundDownString(8)
        val sub = NumUtil.sub(money1, mo2)
        //推算结算价格（承运方预估到手价） 5元以下抹零
        val rate = NumUtil.sum(1.00, mReturnedOrderDetailBean.getSettleRate())
        if (sub != 0.00) {
            //抹零金额为0时不逆推
            mReturnedOrderDetailBeanV1.ignoreSmallChangeNoRateMoney = NumUtil.div(mo2, rate, 8).toDoubleRoundDownString(8)
        } else {
            mReturnedOrderDetailBeanV1.ignoreSmallChangeNoRateMoney = money2.toDoubleRoundDownString(2)
        }
        val sb = SpannableHepler()
            .append(SpannableHepler.Txt(mReturnedOrderDetailBeanV1.ignoreSmallChangeNoRateMoney.toDoubleRoundDownString(2), "#FF5E1C"))
            .append(SpannableHepler.Txt("(承运方预估到手价)", "#333333"))
            .builder()
        viewWipeMoney2.text = sb
        val sb1 = SpannableHepler()
            .append(SpannableHepler.Txt(mReturnedOrderDetailBeanV1.ignoreSmallChangeRateMoney.toDoubleRoundDownString(2), "#FF5E1C"))
            .append(SpannableHepler.Txt("(含税)", "#333333"))
            .builder()
        viewWipeMoney1.text = sb1
    }

    private fun wipeMoney4(money2: Double, money1: Double) {
        //计算结算价格(承运方预估到手价) 角分抹零金额
        mReturnedOrderDetailBeanV1.ignoreSmallChangeNoRateMoney = money2.toInt().toString()
        val sb = SpannableHepler()
            .append(SpannableHepler.Txt(mReturnedOrderDetailBeanV1.ignoreSmallChangeNoRateMoney, "#FF5E1C"))
            .append(SpannableHepler.Txt("(承运方预估到手价)", "#333333"))
            .builder()
        viewWipeMoney2.text = sb
        //推算结算价格（含税）
        val rate = NumUtil.sum(1.00, mReturnedOrderDetailBean.getSettleRate())
        val sub = if (money2 != money2.toInt().toDouble()) {
            NumUtil.mul(mReturnedOrderDetailBeanV1.ignoreSmallChangeNoRateMoney?.toDoubleOrNull() ?: 0.00, rate).roundTo2DecimalPlaces().toDoubleRoundDownString(2)
        } else {
            money1.toDoubleRoundDownString(2)
        }
        mReturnedOrderDetailBeanV1.ignoreSmallChangeRateMoney = sub
        val sb1 = SpannableHepler()
            .append(SpannableHepler.Txt(sub, "#FF5E1C"))
            .append(SpannableHepler.Txt("(含税)", "#333333"))
            .builder()
        viewWipeMoney1.text = sb1
    }

    private fun wipeMoney3(money1: Double, money2: Double) {
        //计算结算价格(含税价 10元以下抹零)
        val toInt1 = money1.toInt()
        if (toInt1 < 10) {
            //数值小于10 直接10元以下抹零
            mReturnedOrderDetailBeanV1.ignoreSmallChangeRateMoney = "0.00"
            val sb = SpannableHepler().append(SpannableHepler.Txt(mReturnedOrderDetailBeanV1.ignoreSmallChangeRateMoney, "#FF5E1C"))
                .append(SpannableHepler.Txt("(含税)", "#333333"))
                .builder()
            viewWipeMoney1.text = sb
        } else if (toInt1 > 10) {
            //大于10元取最后一位整数
            val m3 = toInt1.toString().substring(toInt1.toString().length - 1).toInt()
            mReturnedOrderDetailBeanV1.ignoreSmallChangeRateMoney = NumUtil.sub(toInt1.toDouble(), m3.toDouble()).roundTo2DecimalPlaces().toDoubleRoundDownString(2)
            val sb = SpannableHepler()
                .append(SpannableHepler.Txt(mReturnedOrderDetailBeanV1.ignoreSmallChangeRateMoney, "#FF5E1C"))
                .append(SpannableHepler.Txt("(含税)", "#333333"))
                .builder()
            viewWipeMoney1.text = sb
        } else {
            mReturnedOrderDetailBeanV1.ignoreSmallChangeRateMoney = toInt1.toString().toDoubleRoundDownString(2)
            val sb = SpannableHepler()
                .append(SpannableHepler.Txt(toInt1.toString(), "#FF5E1C"))
                .append(SpannableHepler.Txt("(含税)", "#333333"))
                .builder()
            viewWipeMoney1.text = sb
        }
        //推算承运方预估到手价
        val rate = NumUtil.sum(1.00, mReturnedOrderDetailBean.getSettleRate())
        val sub1 = mReturnedOrderDetailBeanV1.ignoreSmallChangeRateMoney?.toDoubleOrNull() ?: 0.00
        val sub = if (money1 != sub1) {
            NumUtil.div(sub1, rate, 2).roundTo2DecimalPlaces().toDoubleRoundDownString(2)
        } else {
            money2.toDoubleRoundDownString(2)
        }
        mReturnedOrderDetailBeanV1.ignoreSmallChangeNoRateMoney = sub
        val sb1 = SpannableHepler()
            .append(SpannableHepler.Txt(sub, "#FF5E1C"))
            .append(SpannableHepler.Txt("(承运方预估到手价)", "#333333"))
            .builder()
        viewWipeMoney2.text = sb1
    }

    private fun wipeMoney2(money2: Double, money1: Double) {
        //计算结算价格(承运方预估到手价 十元以下抹零)
        val toInt1 = money2.toInt() //十元以下抹零 直接去除小数点后数值
        if (toInt1 < 10) {
            //数值小于10 直接十元以下抹零
            mReturnedOrderDetailBeanV1.ignoreSmallChangeNoRateMoney = "0.00"
            val sb = SpannableHepler()
                .append(SpannableHepler.Txt(mReturnedOrderDetailBeanV1.ignoreSmallChangeNoRateMoney, "#FF5E1C"))
                .append(SpannableHepler.Txt("(承运方预估到手价)", "#333333"))
                .builder()
            viewWipeMoney2.text = sb
        } else if (toInt1 > 10) {
            //大于10元 取最后一位整数
            val m3 = toInt1.toString().substring(toInt1.toString().length - 1).toInt()
            val sub = NumUtil.sub(toInt1.toDouble(), m3.toDouble()).roundTo2DecimalPlaces().toDoubleRoundDownString(2)
            mReturnedOrderDetailBeanV1.ignoreSmallChangeNoRateMoney = sub
            val sb = SpannableHepler()
                .append(SpannableHepler.Txt(sub, "#FF5E1C"))
                .append(SpannableHepler.Txt("(承运方预估到手价)", "#333333"))
                .builder()
            viewWipeMoney2.text = sb
        } else {
            mReturnedOrderDetailBeanV1.ignoreSmallChangeNoRateMoney = toInt1.toString().toDoubleRoundDownString(2)
            val sb = SpannableHepler()
                .append(SpannableHepler.Txt(toInt1.toString(), "#FF5E1C"))
                .append(SpannableHepler.Txt("(承运方预估到手价)", "#333333"))
                .builder()
            viewWipeMoney2.text = sb
        }
        //推算结算价格（承运方预估到手价） 十元以下抹零
        val rate = NumUtil.sum(1.00, mReturnedOrderDetailBean.getSettleRate())
        val sub = if (money2 != (mReturnedOrderDetailBeanV1.ignoreSmallChangeNoRateMoney?.toDoubleOrNull() ?: 0.00)) {
            NumUtil.mul(mReturnedOrderDetailBeanV1.ignoreSmallChangeNoRateMoney?.toDoubleOrNull() ?: 0.00, rate).roundTo2DecimalPlaces().toDoubleRoundDownString(2)
        } else {
            money1.toDoubleRoundDownString(2)
        }
        mReturnedOrderDetailBeanV1.ignoreSmallChangeRateMoney = sub
        val sb1 = SpannableHepler()
            .append(SpannableHepler.Txt(sub, "#FF5E1C"))
            .append(SpannableHepler.Txt("(含税)", "#333333"))
            .builder()
        viewWipeMoney1.text = sb1
    }

    private fun wipeMoney1(money1: Double, money2: Double) {
        //计算结算价格(含税) 角分抹零金额
        mReturnedOrderDetailBeanV1.ignoreSmallChangeRateMoney = money1.toInt().toString()
        val sb = SpannableHepler()
            .append(SpannableHepler.Txt(mReturnedOrderDetailBeanV1.ignoreSmallChangeRateMoney, "#FF5E1C"))
            .append(SpannableHepler.Txt("(含税)", "#333333"))
            .builder()
        viewWipeMoney1.text = sb
        //推算结算价格（承运方预估到手价） 十元以下抹零
        val rate = NumUtil.sum(1.00, mReturnedOrderDetailBean.getSettleRate())
        val sub = if (money1 != money1.toInt().toDouble()) {
            NumUtil.div(mReturnedOrderDetailBeanV1.ignoreSmallChangeRateMoney?.toDoubleOrNull() ?: 0.00, rate, 2).roundTo2DecimalPlaces().toDoubleRoundDownString(2)
        } else {
            money2.toDoubleRoundDownString(2)
        }
        mReturnedOrderDetailBeanV1.ignoreSmallChangeNoRateMoney = sub
        val sb1 = SpannableHepler()
            .append(SpannableHepler.Txt(sub, "#FF5E1C"))
            .append(SpannableHepler.Txt("(承运方预估到手价)", "#333333"))
            .builder()
        viewWipeMoney2.text = sb1
    }

    private fun confirmSettlementTonnage(content: String) {
        //结算数量值变动 相关计算
        <EMAIL>?.slipLoad = content
        //单价直接丢给含税结算金额
        if ("1" == <EMAIL>?.freightType) {
            if ((orderSettleTypeUnitEditText.text.isEmpty() || content.isEmpty()) && !<EMAIL>?.goodsSource.isTrue) {
                <EMAIL>?.consignorRateMoney =
                    ""
                etConfirmSettlementPriceIncludedTax.setText("")
            } else {
                val s1 =
                    if (<EMAIL>?.goodsSource.isTrue) {
                        <EMAIL>?.showContainerNum()
                    } else {
                        content.toDoubleOrNull() ?: 0.0
                    }
                val s2 = orderSettleTypeUnitEditText.text.toString().toDoubleOrNull() ?: 0.0
                val s3 = 0.0
                //单价，竞价模式加保费
                if (<EMAIL>?.orderModel.isTrue) {
                    val money1 =
                        (NumUtil.mulEgnorNull(s1, s2) + s3).roundTo2DecimalPlaces().toString()
                    <EMAIL>?.consignorRateMoney =
                        money1
                    etConfirmSettlementPriceIncludedTax.setText(money1)
                } else {
                    val money1 = NumUtil.mulEgnorNull(s1, s2).roundTo2DecimalPlaces().toString()
                        .toDoubleRoundDownString(2)
                    <EMAIL>?.consignorRateMoney =
                        money1
                    etConfirmSettlementPriceIncludedTax.setText(money1)
                }
            }
            //结算金额(含税价): 推算->承运方预估到手价(不含税价)
            val money1 = getMoneyRateText(
                <EMAIL>?.consignorRateMoney,
                <EMAIL>?.settleRate,
                <EMAIL>?.consignorNoRateMoney
            )
            <EMAIL>?.consignorNoRateMoney =
                money1
            //结算金额(含税价): 推算->承运方预估到手价单价
            val money2 = computeMoney2(
                <EMAIL>?.consignorNoRateMoney,
                <EMAIL>?.slipLoad
            )
            <EMAIL>?.consignorNoRateUnitShowMoney =
                money2
            etConfirmSettlementPriceNotIncludedTax.setText(money1)
            etConsignorNoRateUnitShowMoney.setText(money2)
            calc(
                priceDoesNotIncludeTax = etConfirmSettlementPriceNotIncludedTax.text.toString(),
                rate = <EMAIL>?.getSettleRate()
                    .toString()
            )
        }
    }

    private fun initOrderCustomNumber(selfComment: String?) {
        /*不管该字段，进来就展示自定义编号，货主可以修改*/
//        if (TextUtils.isEmpty(selfComment)) {
//            clOrderCustomNumber.setVisible(false)
//        } else {
        clOrderCustomNumber.setVisible(true)
        orderCustomNumber1.setText(selfComment)
//        }
    }

    private fun initRemarker(mReturnedOrderDetailBean: ReturnedOrderDetailBean) {
        edit_code.setText(mReturnedOrderDetailBean.consignorReceiptRemark ?: "")
    }

    private fun initCodeView() {
        // 请输入备注
        edit_code.filters = arrayOf<InputFilter>(InputFilter.LengthFilter(150))
        edit_code.addTextChangedListener(object : TextWatcher {
            override fun afterTextChanged(s: Editable) {
                if (s.length > 150) {
                    return
                }
                tv_code_size.text = "${s.length}/150"
            }

            override fun beforeTextChanged(s: CharSequence?, start: Int, count: Int, after: Int) {
            }

            override fun onTextChanged(s: CharSequence?, start: Int, before: Int, count: Int) {
            }
        })
    }

    private fun initCodeViewV1() {
        // 请输入自定义编号
        orderCustomNumber1.filters = arrayOf<InputFilter>(InputFilter.LengthFilter(50))
        orderCustomNumber1.addTextChangedListener(object : TextWatcher {
            override fun afterTextChanged(s: Editable) {
                if (s.length > 50) {
                    return
                }
                tv_code_size_v1.text = "${s.length}/50"
            }

            override fun beforeTextChanged(s: CharSequence?, start: Int, count: Int, after: Int) {
            }

            override fun onTextChanged(s: CharSequence?, start: Int, before: Int, count: Int) {
            }
        })
    }

    // 初始化亏吨费计算器
    private fun initCalc(
        deliverWeight: String?,
        receiveWeight: String?,
        cargoUnitMoney: String?,
        includeTaxPrice: String,
        noTaxPrice: String
    ) {
        editShippingTonnage.addTextChangedListener(shippingTonnageTextWatcher)
        editReceiptTonnage.addTextChangedListener(receiptTonnageTextWatcher)
        editShippingTonnage.setText(deliverWeight)
        editReceiptTonnage.setText(receiveWeight)

        // 允许亏吨率
        editAllowableDeficitRate.setOnFocusChangeListener { _, hasFocus ->
            if (hasFocus) {
                editAllowableDeficitRate.addTextChangedListener(allowableDeficitRateTextWatcher)
                editAllowableLoss.removeTextChangedListener(allowableLossTextWatcher)
            } else {
                editAllowableDeficitRate.removeTextChangedListener(allowableDeficitRateTextWatcher)
            }
        }
        // 允许亏吨量
        editAllowableLoss.setOnFocusChangeListener { _, hasFocus ->
            if (hasFocus) {
                editAllowableLoss.addTextChangedListener(allowableLossTextWatcher)
                editAllowableDeficitRate.removeTextChangedListener(allowableDeficitRateTextWatcher)
            } else {
                editAllowableLoss.removeTextChangedListener(allowableLossTextWatcher)
            }
        }
        // 允许亏吨扣款系数
        editAllowDeductibleDeductionFactor.addTextChangedListener(
            allowDeductibleDeductionFactorTextWatcher
        )

        // 超出亏吨扣款系数 (货值单价)
        editExceedingTheDeductibleDeductionUnit.addTextChangedListener(
            exceedingTheDeductibleDeductionUnitTextWatcher()
        )
        editExceedingTheDeductibleDeductionUnit.setText(cargoUnitMoney)
        // 允许亏吨扣款
        editAllowDeductibleDeductions.addTextChangedListener(
            allowDeductibleDeductionsTextWatcher(
                includeTaxPrice
            )
        )
        // 超出亏吨扣款
        editExceedingTheDeductibleDeduction.addTextChangedListener(
            exceedingTheDeductibleDeductionTextWatcher(includeTaxPrice)
        )

        radioGroup.setOnCheckedChangeListener { _, checkedId ->
            when (checkedId) {
                R.id.rbIncludeTax -> {
                    val allowDeductibleDeductionsNum =
                        editAllowDeductibleDeductions.text.toString().toBigDecimalOrNull()
                    val exceedingTheDeductibleDeductionNum =
                        editExceedingTheDeductibleDeduction.text.toString().toBigDecimalOrNull()
                    val includeTaxPriceNum = includeTaxPrice.toBigDecimalOrNull()
                    if (allowDeductibleDeductionsNum != null && exceedingTheDeductibleDeductionNum != null && includeTaxPriceNum != null) {
                        calcDeductingTaxIncludedDamage(
                            includeTaxPriceNum,
                            allowDeductibleDeductionsNum,
                            exceedingTheDeductibleDeductionNum
                        )
                    }
                }

                R.id.rbNoIncludeTax -> {
                    val allowDeductibleDeductionsNum =
                        editAllowDeductibleDeductions.text.toString().toBigDecimalOrNull()
                    val exceedingTheDeductibleDeductionNum =
                        editExceedingTheDeductibleDeduction.text.toString().toBigDecimalOrNull()
                    val includeTaxPriceNum = includeTaxPrice.toBigDecimalOrNull()
                    if (allowDeductibleDeductionsNum != null && exceedingTheDeductibleDeductionNum != null && includeTaxPriceNum != null) {
                        calcDeductionOfTaxFreeGoods(
                            includeTaxPriceNum,
                            allowDeductibleDeductionsNum,
                            exceedingTheDeductibleDeductionNum
                        )
                    }
                }

                else -> {
                }
            }
        }
        rbIncludeTax.isChecked = true

        tvPriceIncludeTax.text = includeTaxPrice
        tvPriceNoTax.text = noTaxPrice
    }

    // 发货吨位
    private val shippingTonnageTextWatcher = object : TextWatcher {
        override fun afterTextChanged(s: Editable?) {
            s?.let { editable ->
                val toString = editable.toString()
                if (toString.isNotBlank()) {
                    val input = toString.toBigDecimalOrNull()
                    input?.let { inputNum ->
                        var a = editReceiptTonnage.text.toString()
                        if (TextUtils.isEmpty(a)) {
                            a = "0.00"
                        }
                        val poundDifferenceNum = inputNum.subtract(
                            a.toBigDecimal()
                        )
                        calcExceedingTheAmountOfLoss(poundDifferenceNum)
                        tvPoundDifference.text = String.format(
                            resources.getString(R.string.order_calc_price),
                            poundDifferenceNum.setScale(4, BigDecimal.ROUND_HALF_UP).toString()
                        )

                        if (allowableDeficitRateTextWatcher != null) {
                            editAllowableDeficitRate.removeTextChangedListener(
                                allowableDeficitRateTextWatcher
                            )
                        }
                        if (allowableLossTextWatcher != null) {
                            editAllowableLoss.removeTextChangedListener(allowableLossTextWatcher)
                        }

                        allowableDeficitRateTextWatcher = allowableDeficitRateTextWatcher(inputNum)
                        allowableLossTextWatcher = allowableLossTextWatcher(inputNum)
                    }
                }
            }
        }

        override fun beforeTextChanged(s: CharSequence?, start: Int, count: Int, after: Int) {}

        override fun onTextChanged(s: CharSequence?, start: Int, before: Int, count: Int) {}
    }

    // 收货吨位
    private val receiptTonnageTextWatcher = object : TextWatcher {
        override fun afterTextChanged(s: Editable?) {
            s?.let { editable ->
                val toString = editable.toString()
                if (toString.isNotEmpty()) {
                    val inputNum = toString.toBigDecimalOrNull()
                    inputNum?.let {
                        val poundDifferenceNum =
                            editShippingTonnage.text.toString().toBigDecimal() - it
                        calcExceedingTheAmountOfLoss(poundDifferenceNum)
                        tvPoundDifference.text = String.format(
                            resources.getString(R.string.order_calc_price),
                            poundDifferenceNum.setScale(4, BigDecimal.ROUND_HALF_UP).toString()
                        )
                    }
                }
            }
        }

        override fun beforeTextChanged(s: CharSequence?, start: Int, count: Int, after: Int) {}

        override fun onTextChanged(s: CharSequence?, start: Int, before: Int, count: Int) {}
    }

    /**
     * 允许亏吨率
     */
    private var allowableDeficitRateTextWatcher: TextWatcher? = null

    /**
     * 允许亏吨量
     */
    private var allowableLossTextWatcher: TextWatcher? = null

    private fun allowableDeficitRateTextWatcher(deliverWeightNum: BigDecimal): TextWatcher {
        return object : TextWatcher {
            override fun afterTextChanged(s: Editable?) {
                s?.let { editable ->
                    val inputRate = editable.toString()
                    if (inputRate.isNotEmpty()) {
                        val inputRateNum = inputRate.toBigDecimalOrNull()
                        inputRateNum?.let {
                            val allowableLoss =
                                it.divide(BigDecimal(THOUSAND), 3, BigDecimal.ROUND_HALF_UP)
                                    .multiply(deliverWeightNum)
                            editAllowableLoss.setText(
                                String.format(
                                    resources.getString(R.string.order_calc_price),
                                    allowableLoss.setScale(4, BigDecimal.ROUND_HALF_UP).toString()
                                )
                            )
                            calcDeductibleDeductions(allowableLoss)
                            calcExceedingTheAmountOfLoss(
                                tvPoundDifference.text.toString().toBigDecimal()
                            )
                        }
                    }
                }
            }

            override fun beforeTextChanged(s: CharSequence?, start: Int, count: Int, after: Int) {}

            override fun onTextChanged(s: CharSequence?, start: Int, before: Int, count: Int) {}
        }
    }

    private fun allowableLossTextWatcher(deliverWeightNum: BigDecimal): TextWatcher {
        return object : TextWatcher {
            override fun afterTextChanged(s: Editable?) {
                s?.let { editable ->
                    val inputLoss = editable.toString()
                    if (inputLoss.isNotEmpty()) {
                        val inputLossNum = inputLoss.toBigDecimal()
                        inputLossNum.let { input ->
                            if (deliverWeightNum == BigDecimal(ZERO)) {
                                Toast.makeText(
                                    this@SingleReturnedOrderConfirmActivityV2,
                                    "发货吨位不能为0",
                                    Toast.LENGTH_SHORT
                                ).show()
                            } else {
                                val allowableLoss =
                                    input.divide(deliverWeightNum, 3, BigDecimal.ROUND_HALF_UP)
                                        .multiply(BigDecimal(THOUSAND))
                                editAllowableDeficitRate.setText(
                                    String.format(
                                        resources.getString(R.string.order_calc_price),
                                        allowableLoss.setScale(4, BigDecimal.ROUND_HALF_UP)
                                            .toString()
                                    )
                                )
                            }
                            calcDeductibleDeductions(input)
                            calcExceedingTheAmountOfLoss(
                                tvPoundDifference.text.toString().toBigDecimal()
                            )
                        }
                    }
                }
            }

            override fun beforeTextChanged(s: CharSequence?, start: Int, count: Int, after: Int) {}

            override fun onTextChanged(s: CharSequence?, start: Int, before: Int, count: Int) {}
        }
    }

    /**
     * 计算 超出亏吨量
     */
    private fun calcExceedingTheAmountOfLoss(poundDifferenceNum: BigDecimal) {
        val allowableLoss = editAllowableLoss.text.toString()
        if (allowableLoss.isNotEmpty()) {
            val allowableLossNum = allowableLoss.toBigDecimal()
            val exceedingTheAmountOfLossNum = poundDifferenceNum - allowableLossNum
            if (exceedingTheAmountOfLossNum > BigDecimal(ZERO)) {
                tvExceedingTheAmountOfLoss.text = String.format(
                    resources.getString(R.string.order_calc_price),
                    exceedingTheAmountOfLossNum.setScale(4, BigDecimal.ROUND_HALF_UP).toString()
                )
                val exceedingTheDeductibleDeductionUnitNum =
                    editExceedingTheDeductibleDeductionUnit.text.toString().toBigDecimalOrNull()
                        ?: BigDecimal(ZERO)
                val allowDeductibleDeductionsNum =
                    exceedingTheAmountOfLossNum * exceedingTheDeductibleDeductionUnitNum
                editExceedingTheDeductibleDeduction.setText(
                    String.format(
                        resources.getString(R.string.order_calc_price),
                        allowDeductibleDeductionsNum.setScale(4, BigDecimal.ROUND_DOWN).toString()
                    )
                )
            } else {
                tvExceedingTheAmountOfLoss.text = ZERO_POINT_THREE_ZERO_STR
                editExceedingTheDeductibleDeduction.setText(ZERO_POINT_THREE_ZERO_STR)
            }
        }
    }

    /**
     * 计算 超出亏吨扣款
     */
    private fun calcDeductibleDeductions(allowableLoss: BigDecimal) {
        val factor = editAllowDeductibleDeductionFactor.text.toString()
        if (factor.isNotEmpty()) {
            val allowDeductibleDeductions = if (factor.toBigDecimal() == BigDecimal(ZERO)) {
                allowableLoss * BigDecimal(ZERO)
            } else {
                allowableLoss * factor.toBigDecimal()
            }
            editAllowDeductibleDeductions.setText(
                String.format(
                    resources.getString(R.string.order_calc_price),
                    allowDeductibleDeductions.setScale(4, BigDecimal.ROUND_DOWN).toString()
                )
            )
        }
    }

    // 允许亏吨扣款系数
    private val allowDeductibleDeductionFactorTextWatcher = object : TextWatcher {
        override fun afterTextChanged(s: Editable?) {
            s?.let { editable ->
                val toString = editable.toString()
                if (toString.isNotBlank()) {
                    val inputNum = toString.toBigDecimalOrNull()
                    inputNum?.let {
                        val allowDeductibleDeductionsNum =
                            editAllowableLoss.text.toString()
                                .toBigDecimal() * it
                        editAllowDeductibleDeductions.setText(
                            String.format(
                                resources.getString(R.string.order_calc_price),
                                allowDeductibleDeductionsNum.setScale(4, BigDecimal.ROUND_DOWN)
                                    .toString()
                            )
                        )
                    }
                }
            }
        }

        override fun beforeTextChanged(s: CharSequence?, start: Int, count: Int, after: Int) {}

        override fun onTextChanged(s: CharSequence?, start: Int, before: Int, count: Int) {}
    }

    // 超出亏吨扣款系数（货值单价）
    private fun exceedingTheDeductibleDeductionUnitTextWatcher(): TextWatcher {
        return object : TextWatcher {
            override fun afterTextChanged(s: Editable?) {
                s?.let { editable ->
                    val toString = editable.toString()
                    if (toString.isNotBlank()) {
                        val inputNum = toString.toBigDecimalOrNull()
                        inputNum?.let {
                            val allowDeductibleDeductionsNum =
                                tvExceedingTheAmountOfLoss.text.toString()
                                    .toDoubleRoundDownString(3).toBigDecimal() * it
                            editExceedingTheDeductibleDeduction.setText(
                                String.format(
                                    resources.getString(R.string.order_calc_price),
                                    allowDeductibleDeductionsNum.setScale(4, BigDecimal.ROUND_DOWN)
                                        .toString()
                                )
                            )
                        }
                    }
                }
            }

            override fun beforeTextChanged(s: CharSequence?, start: Int, count: Int, after: Int) {}

            override fun onTextChanged(s: CharSequence?, start: Int, before: Int, count: Int) {}
        }
    }

    // 允许亏吨扣款
    private fun allowDeductibleDeductionsTextWatcher(includeTaxPrice: String): TextWatcher {
        return object : TextWatcher {
            override fun afterTextChanged(s: Editable?) {
                s?.let { editable ->
                    val toString = editable.toString()
                    if (toString.isNotBlank()) {
                        val inputNum = toString.toBigDecimalOrNull()
                        inputNum?.let {
                            if (includeTaxPrice.isNotEmpty()) {
                                getWhichRbIsSelected(
                                    includeTaxPrice,
                                    it,
                                    editExceedingTheDeductibleDeduction.text.toString()
                                        .toBigDecimalOrNull()
                                        ?: BigDecimal(ZERO)
                                )
                            }
                        }
                    }
                }
            }

            override fun beforeTextChanged(s: CharSequence?, start: Int, count: Int, after: Int) {}

            override fun onTextChanged(s: CharSequence?, start: Int, before: Int, count: Int) {}
        }
    }

    // 超出亏吨扣款
    private fun exceedingTheDeductibleDeductionTextWatcher(includeTaxPrice: String): TextWatcher {
        return object : TextWatcher {
            override fun afterTextChanged(s: Editable?) {
                s?.let { editable ->
                    val toString = editable.toString()
                    if (toString.isNotBlank()) {
                        val inputNum = toString.toBigDecimalOrNull()
                        inputNum?.let {
                            getWhichRbIsSelected(
                                includeTaxPrice,
                                editAllowDeductibleDeductions.text.toString().toBigDecimalOrNull()
                                    ?: BigDecimal(ZERO),
                                it
                            )
                        }
                    }
                }
            }

            override fun beforeTextChanged(s: CharSequence?, start: Int, count: Int, after: Int) {}

            override fun onTextChanged(s: CharSequence?, start: Int, before: Int, count: Int) {}
        }
    }

    private fun getWhichRbIsSelected(
        includeTaxPrice: String,
        allowDeductibleDeductionsNum: BigDecimal,
        exceedingTheDeductibleDeductionNum: BigDecimal
    ) {
        val includeTaxPriceNum = includeTaxPrice.toBigDecimalOrNull()
        when (radioGroup.checkedRadioButtonId) {
            R.id.rbIncludeTax -> {
                calcDeductingTaxIncludedDamage(
                    includeTaxPriceNum
                        ?: BigDecimal(ZERO),
                    allowDeductibleDeductionsNum,
                    exceedingTheDeductibleDeductionNum
                )
            }

            R.id.rbNoIncludeTax -> {
                calcDeductionOfTaxFreeGoods(
                    includeTaxPriceNum
                        ?: BigDecimal(ZERO),
                    allowDeductibleDeductionsNum,
                    exceedingTheDeductibleDeductionNum
                )
            }

            else -> {
            }
        }
    }

    // 扣减含税货损
    private fun calcDeductingTaxIncludedDamage(
        includeTaxPriceNum: BigDecimal,
        allowDeductibleDeductionsNum: BigDecimal,
        exceedingTheDeductibleDeductionNum: BigDecimal
    ) {
        var settleRate = mReturnedOrderDetailBean.getSettleRate()
        if (settleRate == 0.00) {
            settleRate = 1.00
        }
        val realIncludeTaxPrice =
            includeTaxPriceNum - ((allowDeductibleDeductionsNum + exceedingTheDeductibleDeductionNum) * BigDecimal(
                settleRate
            ))
        val realNoTaxPrice = realIncludeTaxPrice / BigDecimal(settleRate)

        tvPriceIncludeTax.text = String.format(
            resources.getString(R.string.order_calc_price),
            realIncludeTaxPrice.setScale(2, BigDecimal.ROUND_HALF_UP).toString()
        )
        tvPriceNoTax.text = String.format(
            resources.getString(R.string.order_calc_price),
            realNoTaxPrice.setScale(2, BigDecimal.ROUND_HALF_UP).toString()
        )
    }

    // 扣减不含税货损
    private fun calcDeductionOfTaxFreeGoods(
        includeTaxPriceNum: BigDecimal,
        allowDeductibleDeductionsNum: BigDecimal,
        exceedingTheDeductibleDeductionNum: BigDecimal
    ) {
        var settleRate = mReturnedOrderDetailBean.getSettleRate()
        if (settleRate == 0.00) {
            settleRate = 1.00
        }
        val realIncludeTaxPrice =
            includeTaxPriceNum - (allowDeductibleDeductionsNum + exceedingTheDeductibleDeductionNum)
        val realNoTaxPrice = realIncludeTaxPrice / BigDecimal(settleRate)


        tvPriceIncludeTax.text = String.format(
            resources.getString(R.string.order_calc_price),
            realIncludeTaxPrice.setScale(2, BigDecimal.ROUND_HALF_UP).toString()
        )
        tvPriceNoTax.text = String.format(
            resources.getString(R.string.order_calc_price),
            realNoTaxPrice.setScale(2, BigDecimal.ROUND_HALF_UP).toString()
        )
    }

    // 重置亏吨费计算器
    private fun resetCalc(
        deliverWeight: String?,
        receiveWeight: String?,
        cargoUnitMoney: String?,
        includeTaxPrice: String,
        noTaxPrice: String
    ) {
        editShippingTonnage.setText(deliverWeight)
        editReceiptTonnage.setText(receiveWeight)
        editAllowableDeficitRate.setText("0")
        editAllowableLoss.setText(ZERO_POINT_THREE_ZERO_STR)
        editAllowDeductibleDeductionFactor.setText(ZERO_POINT_TWO_ZERO_STR)
        editExceedingTheDeductibleDeductionUnit.setText(cargoUnitMoney ?: ZERO_POINT_TWO_ZERO_STR)
    }

    @LiveDataMatch(tag = "回单确认修改收发货吨位查询亏涨吨规则")
    open fun queryReceiptLossRiseRuleSuccess(mReturnedOrderDetailBean: RspReturnQueryReceiptLossRiseRule?) {
        mReturnedOrderDetailBean?.let {
            if (it.ruleList.isNullOrEmpty()) {
                return
            }
            OrderDeductionDeployDialogs(
                context = this@SingleReturnedOrderConfirmActivityV2,
                orderId = orderId,
                listData = it.ruleList,
                clickListener = object : OrderDeductionDeployDialogs.OnClickSubmitListener {
                    override fun onClickSubmitListener(mReturnedOrderDetailBean: RspConsignorSmartDeficitTonDefaultRuleList?) {
                        smallLabel?.setRuleItem(mReturnedOrderDetailBean)
                        val replace1 = tvReceiptNum.text.toString().trim()
                            .replace(
                                <EMAIL>?.getDeliverCargoCategoryStr()
                                    ?: "", ""
                            )
                        val replace2 = tvDeliveryNum.text.toString().trim()
                            .replace(
                                <EMAIL>?.getDeliverCargoCategoryStr()
                                    ?: "", ""
                            )
                        viewModel?.queryReceiptLossRiseDetailInfoByAppChangeRuleId(
                            req = ReqQueryReceiptLossRiseDetailInfoByAppChangeRuleId(
                                ruleId = mReturnedOrderDetailBean?.ruleId,
                                orderId = orderId,
                                receiveWeight = replace1,
                                deliverWeight = replace2,
                                consignorRateMoney = etConfirmSettlementPriceIncludedTax.text.toString(),
                                consignorSmartDeficitTonUseRateType = smallLabel?.getConsignorSmartDeficitTonUseRateType()
                            )
                        )
                    }
                },
                selectData = null
            ).show(smallLabel)
        }
    }

    @LiveDataMatch(tag = "移动端切换亏涨吨id计算金额")
    open fun queryReceiptLossRiseDetailInfoByAppChangeRuleIdSuccess(mData: RspQueryReceiptLossRiseDetailInfoByAppChangeRuleId?) {
        mReleaseLossTonData = mData?.lossRiseType ?: ""
        smallLabel?.setRuleMoney(data = mData)
        //计算加扣款
        if (mData.isNull) {
            calc(
                priceDoesNotIncludeTax = etConfirmSettlementPriceNotIncludedTax.text.toString(),
                rate = mReturnedOrderDetailBean.getSettleRate().toString()
            )
        } else {
            calc(
                priceDoesNotIncludeTax = mData?.lossRiseConsignorNoRateMoney ?: "0.00",
                rate = mReturnedOrderDetailBean.getSettleRate().toString()
            )
        }
    }

    @LiveDataMatch(tag = "亏涨吨规则ruleId为空")
    open fun onRuleIdEmpty() {
        smallLabel?.setRuleVisible()
        calc(
            priceDoesNotIncludeTax = etConfirmSettlementPriceNotIncludedTax.text.toString(),
            rate = mReturnedOrderDetailBean.getSettleRate().toString()
        )
    }

    @RxBusEvent(from = "上传回单照片")
    open fun onRxUploadSuccess(mReturnedOrderDetailBean: RxBusUploadImgSuccess) {
        if (mReturnedOrderDetailBean.success) {
            refreshRetrunedOrderDetailHzPic()
        }
    }

    @RxBusEvent(from = "回单上传")
    open fun onRxUploadSuccessV1(mReturnedOrderDetailBean: RxBusUploadImgSuccessV2) {
        RxBusEventManager.postEvent(RxAgreeAdjust(true))
        finish()
    }

    @RxBusEvent(from = "修改发货收货数量")
    open fun onRxUploadSuccess(mReturnedOrderDetailBean: RxBusUploadImgSuccessV1) {
        if (mReturnedOrderDetailBean.success) {
            refreshRetrunedOrderDetailHzPicV1()
        }
    }

    companion object {
        const val ORDER_ID = "orderId"
        const val DETAIL_ID = "detailId"
        const val BACK_ORDER_RECONSIDER_STATE = "backorderreconsiderstate"
        const val ZERO_POINT_THREE_ZERO_STR = "0.000"
        const val ZERO_POINT_TWO_ZERO_STR = "0.00"
        const val ZERO = 0.000
        const val THOUSAND = 1000
        private const val UPLOAD_PHOTO = 0x1638
        private const val UPLOAD_PHOTO_V1 = 0x1938
        private const val UPLOAD_PHOTO_V2 = 0x1838
    }

    private fun calc(priceDoesNotIncludeTax: String, rate: String) {
        val inputTotalAmount = addAndDeductMoneyList.computeMoney()
        //计算加扣款
        val settleRated = rate.toDoubleOrNull() ?: 0.0
        val priceDoesNotIncludeTaxDouble = priceDoesNotIncludeTax.toDoubleOrNull() ?: 0.0
        val s7 = NumUtil.sumEgnorNull(priceDoesNotIncludeTaxDouble, inputTotalAmount)
        val s6 = settleRated + 1
        if (inputTotalAmount > 0.00) {
            //加款
            tvJiaKouKuanXiang.text =
                "加款${inputTotalAmount.absoluteValue.toDoubleRoundDownString(2)}元"
            tvTaxIncludedPrice.text =
                NumUtil.mulEgnorNull(s7, s6).roundTo2DecimalPlaces().toDoubleRoundDownString(2)
            tvTaxNotIncludedPrice.text = s7.roundTo2DecimalPlaces().toDoubleRoundDownString(2)
        } else if (inputTotalAmount < 0.00) {
            //减款
            tvJiaKouKuanXiang.text =
                "扣款${inputTotalAmount.absoluteValue.toDoubleRoundDownString(2)}元"
            tvTaxIncludedPrice.text =
                NumUtil.mulEgnorNull(s7, s6).roundTo2DecimalPlaces().toDoubleRoundDownString(2)
            tvTaxNotIncludedPrice.text = s7.roundTo2DecimalPlaces().toDoubleRoundDownString(2)
        } else {
            tvJiaKouKuanXiang.text = "加扣款0.00元"
            tvTaxIncludedPrice.text =
                <EMAIL>?.consignorRateMoney
            tvTaxNotIncludedPrice.text = s7.toDoubleRoundDownString(2)
        }
        //计算抹零结算价格
        computeViewWipeMoney1()
    }

    private fun showSelectTime() {
        PickerView.sOutTextSize = 17
        PickerView.sCenterTextSize = 18
        PickerView.sCenterColor = Color.BLACK
        PickerView.sOutColor = Color.parseColor("#A3A3A3")
        PickerView.sDefaultVisibleItemCount = 5
        BasePicker.sDefaultPickerBackgroundColor = Color.WHITE
        DefaultCenterDecoration.sDefaultLineColor = Color.TRANSPARENT
        val now = System.currentTimeMillis()
        val mTimePicker = TimePicker.Builder(
            this@SingleReturnedOrderConfirmActivityV2,
            TimePicker.TYPE_ALL
        ) { _, date ->
            inputOutStageTime.content = date.getFormatTime("yyyy-MM-dd HH:mm:00")
        }
            // 设置选中时间
            .setSelectedDate(now)
            .create()

        val topBar = CustomTopBar(mTimePicker.rootLayout)
        topBar.titleView.text = "选择出厂时间"
        topBar.setDividerHeight(0f).setDividerColor(Color.parseColor("#eeeeee"))
        mTimePicker.topBar = topBar
        mTimePicker.show()
    }

}