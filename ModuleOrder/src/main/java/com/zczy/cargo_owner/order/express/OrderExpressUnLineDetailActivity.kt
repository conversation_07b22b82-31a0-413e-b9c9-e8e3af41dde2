package com.zczy.cargo_owner.order.express

import android.app.Activity
import android.content.Intent
import android.os.Bundle
import android.text.TextUtils
import android.view.View
import android.widget.TextView
import com.sfh.lib.mvvm.annotation.LiveDataMatch
import com.zczy.cargo_owner.order.R
import com.zczy.cargo_owner.order.express.model.OrderExpressDetailModel
import com.zczy.cargo_owner.order.express.req.RspCarrierExpressData
import com.zczy.cargo_owner.order.express.req.RspConsignorExpressDetailForMobile
import com.zczy.cargo_owner.order.express.req.getSignStates
import com.zczy.comm.permission.PermissionCallBack
import com.zczy.comm.permission.PermissionUtil
import com.zczy.comm.ui.BaseActivity
import com.zczy.comm.utils.CommUtils
import com.zczy.comm.utils.PhoneUtil
import com.zczy.comm.utils.ex.setVisible
import com.zczy.comm.utils.json.toJson
import com.zczy.comm.utils.json.toJsonObject
import com.zczy.comm.widget.AppToolber
import kotlinx.android.synthetic.main.order_express_detail_activity.*

/**
 * PS: 快递详情 线下送达
 * Created by sdx on 2018/11/5.
 */
class OrderExpressUnLineDetailActivity : BaseActivity<OrderExpressDetailModel>() {

    private val expressData by lazy { obtainData(intent) }

    private val tvFreightOrderValue by lazy { findViewById<TextView>(R.id.tv_freight_order_value) }
    private val tvPlateNumberValue by lazy { findViewById<TextView>(R.id.tv_plate_number_value) }
    private val tvExpressCompanyValue by lazy { findViewById<TextView>(R.id.tv_express_company_value) }
    private val tv_states_company_value by lazy { findViewById<TextView>(R.id.tv_states_company_value) }
    private val tv_return_reason_detail by lazy { findViewById<TextView>(R.id.tv_return_reason_detail) }
    private val tv_sign_reason by lazy { findViewById<TextView>(R.id.tv_sign_reason) }
    private val tv_sign_reason_value by lazy { findViewById<TextView>(R.id.tv_sign_reason_value) }
    private val tv_return_reason by lazy { findViewById<TextView>(R.id.tv_return_reason) }
    private val appToolber by lazy { findViewById<AppToolber>(R.id.appToolber) }

    companion object {
        private const val EXTRA_EXPRESS_DATA = "extra_express_data"

        @JvmStatic
        fun start(activity: Activity, data: RspCarrierExpressData) {
            val intent = Intent(activity, OrderExpressUnLineDetailActivity::class.java)
            intent.putExtra(EXTRA_EXPRESS_DATA, data.toJson())
            activity.startActivity(intent)
        }

        fun obtainData(intent: Intent?): RspCarrierExpressData {
            return intent?.getStringExtra(EXTRA_EXPRESS_DATA)?.toJsonObject(RspCarrierExpressData::class.java)
                    ?: RspCarrierExpressData()
        }
    }

    override fun getLayout(): Int = R.layout.order_express_unline_detail_activity

    override fun bindView(bundle: Bundle?) {
        bindClickEvent(btn_copy)
        bindClickEvent(btn_call)
    }

    override fun initData() {
        tvFreightOrderValue.text = expressData.orderId
        tvPlateNumberValue.text = expressData.plateNumber
        viewModel?.getNetInfo(expressData.detailId, expressData.orderId)
    }

    override fun onSingleClick(v: View) {
        super.onSingleClick(v)
        when (v.id) {
            // 复制
            R.id.btn_copy -> {
                val isCopy = CommUtils.copyText(this@OrderExpressUnLineDetailActivity,
                        "运单号", tvFreightOrderValue.text.toString().trim())
                if (isCopy) {
                    showToast("复制成功")
                } else {
                    showToast("复制失败")
                }
            }
            R.id.btn_call -> {
//                PermissionUtil.call(this@OrderExpressUnLineDetailActivity, object : PermissionCallBack() {
//                    override fun onHasPermission() {
                        PhoneUtil.callPhone(this@OrderExpressUnLineDetailActivity, expressData.driverMobile)
//                    }
//                })
            }
        }
    }

    @LiveDataMatch
    open fun getNetInfoSuccess(data: RspConsignorExpressDetailForMobile) {
        when (data.signState) {
            "5" -> {
                appToolber.setTitle("签收详情")
            }
            else -> {
                appToolber.setTitle("线下送达")
            }
        }
        tv_states_company_value.text = data.getSignStates()
        if (!TextUtils.isEmpty(data.repulseReason)) {
            tv_return_reason.setVisible(true)
            tv_return_reason_detail.setVisible(true)
            tv_return_reason_detail.text = data.repulseReason
        }
        if (!TextUtils.isEmpty(data.signReason)) {
            tv_sign_reason.setVisible(true)
            tv_sign_reason_value.setVisible(true)
            tv_sign_reason_value.text = data.signReason
        }

        when (data.sendType) {
            "0" -> {
                tv_express_company.visibility = View.GONE
                tv_express_company_value.visibility = View.GONE
            }
            else -> {
                tv_express_company.visibility = View.VISIBLE
                tv_express_company_value.visibility = View.VISIBLE
            }
        }

    }
}
