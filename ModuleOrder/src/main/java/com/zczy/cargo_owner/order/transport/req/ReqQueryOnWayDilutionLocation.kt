package com.zczy.cargo_owner.order.transport.req

import com.zczy.comm.http.entity.BaseNewRequest
import com.zczy.comm.http.entity.BaseRsp
import com.zczy.comm.http.entity.ResultData

/**
 *  desc: 查询抽稀后地址信息接口
 *  user: 宋双朋
 *  time: 2025/6/4 14:37
 */
data class ReqQueryOnWayDilutionLocation(
    var orderId: String? = null,
    var isCheckBackUpLocation: String? = null, // 是否平台核实轨迹 非必传 0不勾选 1勾选
) : BaseNewRequest<BaseRsp<RspQueryOnWayDilutionLocationV1>>("oms-app/order/consignor/queryOnWayDilutionLocation")

class RspQueryOnWayDilutionLocationV1(
    val locationArr: MutableList<RspQueryOnWayDilutionLocationV2>? = null, // 抽稀后地址集合
) : ResultData()

class RspQueryOnWayDilutionLocationV2(
    var lat: String? = null,  // 纬度
    var lon: String? = null, // 经度
    var address: String? = null,  // 中文坐标
    var uploadTime: String? = null, // 坐标上传时间
)