package com.zczy.cargo_owner.order.confirm.flex

import android.content.Context
import android.content.Intent
import android.os.Bundle
import com.sfh.lib.mvvm.service.BaseViewModel
import com.zczy.cargo_owner.order.R
import com.zczy.cargo_owner.order.confirm.ReturnedOrderDeniedReasonActivity
import com.zczy.cargo_owner.order.flexbox.AlignItems
import com.zczy.cargo_owner.order.flexbox.FlexDirection
import com.zczy.cargo_owner.order.flexbox.FlexWrap
import com.zczy.cargo_owner.order.flexbox.FlexboxLayoutManager
import com.zczy.cargo_owner.order.flexbox.JustifyContent
import com.zczy.comm.ui.BaseActivity
import kotlinx.android.synthetic.main.flex_box_activity.rv_flex

/**
 *@Desc 测试
 *@User ssp
 *@Date 2023/9/18-15:08
 */
class FlexBoxActivity : BaseActivity<BaseViewModel>() {

    companion object {

        @JvmStatic
        fun start(context: Context?) {
            if (context == null) return
            val intent = Intent(context, FlexBoxActivity::class.java)
            context.startActivity(intent)
        }
    }

    override fun getLayout(): Int {
        return R.layout.flex_box_activity
    }

    override fun bindView(bundle: Bundle?) {
        initView()
    }

    override fun initData() {

    }

    private fun initView() {
        val flexboxLayoutManager = FlexboxLayoutManager(this)
        flexboxLayoutManager.flexWrap = FlexWrap.WRAP
        flexboxLayoutManager.flexDirection = FlexDirection.ROW
        flexboxLayoutManager.justifyContent = JustifyContent.FLEX_START
        flexboxLayoutManager.alignItems = AlignItems.FLEX_START
        val adapter = FlexAdapter()
        initData(adapter)
        rv_flex.adapter = adapter
        rv_flex.layoutManager = flexboxLayoutManager
    }

    private fun initData(adapter: FlexAdapter) {
        val lists = arrayListOf(
            "慕课网",
            "极客学院",
            "网易云课程",
            "腾讯课堂",
            "二维码",
            "视频转gif,gif优化等",
            "在线翻译",
            "麦子",
            "人工智能",
            "极客学院",
            "网易云课程",
            "腾讯课堂",
            "二维码",
            "视频转gif,gif优化等",
            "在线翻译",
            "麦子",
            "人工智能",
            "极客学院",
            "网易云课程",
            "腾讯课堂",
            "二维码",
            "视频转gif,gif优化等",
            "在线翻译",
            "麦子",
            "人工智能",
            "极客学院",
            "网易云课程",
            "腾讯课堂",
            "二维码",
            "视频转gif,gif优化等",
            "在线翻译",
            "麦子",
            "人工智能",
            "极客学院",
            "网易云课程",
            "腾讯课堂",
            "二维码",
            "视频转gif,gif优化等",
            "在线翻译",
            "麦子",
            "人工智能",
            "极客学院",
            "网易云课程",
            "腾讯课堂",
            "二维码",
            "视频转gif,gif优化等",
            "在线翻译",
            "麦子",
            "人工智能",
            "极客学院",
            "网易云课程",
            "腾讯课堂",
            "二维码",
            "视频转gif,gif优化等",
            "在线翻译",
            "麦子",
            "人工智能",
            "极客学院",
            "网易云课程",
            "腾讯课堂",
            "二维码",
            "视频转gif,gif优化等",
            "在线翻译",
            "麦子",
            "人工智能",
            "极客学院",
            "网易云课程",
            "腾讯课堂",
            "二维码",
            "视频转gif,gif优化等",
            "在线翻译",
            "麦子",
            "人工智能",
            "极客学院",
            "网易云课程",
            "腾讯课堂",
            "二维码",
            "视频转gif,gif优化等",
            "在线翻译",
            "麦子",
            "人工智能",
            "极客学院",
            "网易云课程",
            "腾讯课堂",
            "二维码",
            "视频转gif,gif优化等",
            "在线翻译",
            "麦子",
            "人工智能",
            "极客学院",
            "网易云课程",
            "腾讯课堂",
            "二维码",
            "视频转gif,gif优化等",
            "在线翻译",
            "麦子",
            "人工智能",
            "极客学院",
            "网易云课程",
            "腾讯课堂",
            "二维码",
            "视频转gif,gif优化等",
            "在线翻译",
            "麦子",
            "人工智能",
            "极客学院",
            "网易云课程",
            "腾讯课堂",
            "二维码",
            "视频转gif,gif优化等",
            "在线翻译",
            "麦子",
            "人工智能",
            "极客学院",
            "网易云课程",
            "腾讯课堂",
            "二维码",
            "视频转gif,gif优化等",
            "在线翻译",
            "麦子",
            "人工智能",
            "极客学院",
            "网易云课程",
            "腾讯课堂",
            "二维码",
            "视频转gif,gif优化等",
            "在线翻译",
            "麦子",
            "人工智能",
            "极客学院",
            "网易云课程",
            "腾讯课堂",
            "二维码",
            "视频转gif,gif优化等",
            "在线翻译",
            "麦子",
            "人工智能",
            "极客学院",
            "网易云课程",
            "腾讯课堂",
            "二维码",
            "视频转gif,gif优化等",
            "在线翻译",
            "麦子",
            "人工智能",
            "极客学院",
            "网易云课程",
            "腾讯课堂",
            "二维码",
            "视频转gif,gif优化等",
            "在线翻译",
            "麦子",
            "人工智能",
            "极客学院",
            "网易云课程",
            "腾讯课堂",
            "二维码",
            "视频转gif,gif优化等",
            "在线翻译",
            "麦子",
            "人工智能",
            "极客学院",
            "网易云课程",
            "腾讯课堂",
            "二维码",
            "视频转gif,gif优化等",
            "在线翻译",
            "麦子",
            "人工智能"
        )
        adapter.addAll(lists)
    }
}
