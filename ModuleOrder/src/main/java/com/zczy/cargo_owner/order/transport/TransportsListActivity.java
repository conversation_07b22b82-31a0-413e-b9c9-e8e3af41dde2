package com.zczy.cargo_owner.order.transport;

import android.content.Context;
import android.content.Intent;
import android.os.Bundle;
import androidx.annotation.Nullable;
import android.text.TextUtils;

import com.sfh.lib.exception.HandleException;
import com.sfh.lib.rx.IResult;
import com.zczy.cargo_owner.order.R;
import com.zczy.cargo_owner.order.transport.adapter.TransportListAdapter;
import com.zczy.cargo_owner.order.transport.bean.TransportWaybill;
import com.zczy.cargo_owner.order.transport.model.TransportModel;
import com.zczy.cargo_owner.order.transport.req.ReqTransPortWaybill;
import com.zczy.cargo_owner.order.transport.wight.CustomPopupWindow;
import com.zczy.comm.CommServer;
import com.zczy.comm.data.entity.ECity;
import com.zczy.comm.http.entity.PageList;
import com.zczy.comm.ui.BaseActivity;
import com.zczy.comm.utils.CommUtils;
import com.zczy.comm.widget.AppToolber;
import com.zczy.comm.widget.pulltorefresh.CommEmptyView;
import com.zczy.comm.widget.pulltorefresh.OnLoadingListener;
import com.zczy.comm.widget.pulltorefresh.SwipeRefreshMoreLayout;

import java.util.List;

import io.reactivex.disposables.Disposable;

import static com.zczy.comm.utils.ResUtil.dp2px;

/**
 * 功能描述: 运输列表
 *
 * <AUTHOR> 贾发展
 * @company 中储智运科技股份有限公司
 * @copyright （版权）中储南京智慧物流科技有限公司所有
 * @date 2019/3/12
 */
public class TransportsListActivity extends BaseActivity<TransportModel> implements OnLoadingListener {

    private SwipeRefreshMoreLayout refreshMoreLayout;
    private AppToolber appToolber;
    private CustomPopupWindow customPopupWindow;
    private Disposable disposable;
    private TransportListAdapter waybillAdapter;
    private String searchContent = "";
    private String despatchPro = "";
    private String despatchCity = "";

    public static void startUI(Context context, String searchContent,String despatchPro) {
        Intent intent = new Intent(context, TransportsListActivity.class);
        intent.putExtra("searchContent", searchContent);
        intent.putExtra("despatchPro", despatchPro);
        context.startActivity(intent);
    }

    @Override
    protected int getLayout() {
        return R.layout.transport_list_activity;
    }

    @Override
    protected void bindView(@Nullable Bundle bundle) {
        appToolber = findViewById(R.id.appToolber);
        refreshMoreLayout = findViewById(R.id.refresh_more_layout);
        waybillAdapter = new TransportListAdapter();
        refreshMoreLayout.setAdapter(waybillAdapter, true);
        refreshMoreLayout.addItemDecorationSize(dp2px(7));
        refreshMoreLayout.setEmptyView(CommEmptyView.creatorDef(this));
        refreshMoreLayout.setOnLoadListener(this);
        customPopupWindow = new CustomPopupWindow(TransportsListActivity.this);
        searchContent = getIntent().getStringExtra("searchContent");
        despatchPro = getIntent().getStringExtra("despatchPro");
        if (TextUtils.isEmpty(despatchPro)) {
            despatchPro = "";
        }
        refreshMoreLayout.onAutoRefresh();
    }

    @Override
    protected void initData() {
        disposable = CommServer.getCacheServer().getCity(new IResult<List<ECity>>() {
            @Override
            public void onSuccess(List<ECity> eCities) {
                customPopupWindow.setProvinceData(eCities);
            }

            @Override
            public void onFail(HandleException e) {
            }
        });
        initListener();
    }

    private void initListener() {
        appToolber.setRightOnClickListener(view -> {
            customPopupWindow.show(appToolber);
        });
        customPopupWindow.setCustomPopWindowsListener(new CustomPopupWindow.OnPopWindowClickListener() {
            @Override
            public void selectCity(ECity cityBean) {
                despatchCity = cityBean.getAreaName();
                refreshMoreLayout.onAutoRefresh();
            }

            @Override
            public void selectProvince(ECity cityBean) {
                despatchPro = cityBean.getAreaName();
                customPopupWindow.setCityData(cityBean.getChildList());
            }
        });
        waybillAdapter.setOnItemChildClickListener((adapter, view, position) -> {
            TransportWaybill item = (TransportWaybill) adapter.getItem(position);
            if (view.getId() == R.id.tv_copy) {
                // 复制运单号
                boolean isCopy = CommUtils.copyText(this, "运单号", item.getOrderId());
                if (isCopy) {
                    showToast("复制成功");
                } else {
                    showToast("复制失败");
                }
            }
        });
        waybillAdapter.setOnItemClickListener((adapter, view, position) -> {
            TransportWaybill item = (TransportWaybill) adapter.getItem(position);
            WaybillTrackingActivityV1.startUI(this, item.getOrderId(), item.getPlateNum());
        });
    }

    @Override
    public void onRefreshUI(int nowPage) {
        if (!TextUtils.isEmpty(despatchCity) && !TextUtils.isEmpty(despatchPro)) {
            searchContent = "";
        }
        ReqTransPortWaybill reqTransPortWaybill = new ReqTransPortWaybill();
        reqTransPortWaybill.setNowPage(nowPage);
        reqTransPortWaybill.setPageSize(10);
        reqTransPortWaybill.setSearchStr(searchContent);
        reqTransPortWaybill.setDespatchCity(despatchCity);
        reqTransPortWaybill.setDespatchPro(despatchPro);
        getViewModel().queryTransPortWaybill(reqTransPortWaybill);
    }

    @Override
    public void onLoadMoreUI(int nowPage) {
        if (!TextUtils.isEmpty(despatchCity) && !TextUtils.isEmpty(despatchPro)) {
            searchContent = "";
        }
        ReqTransPortWaybill reqTransPortWaybill = new ReqTransPortWaybill();
        reqTransPortWaybill.setNowPage(nowPage);
        reqTransPortWaybill.setPageSize(10);
        reqTransPortWaybill.setSearchStr(searchContent);
        reqTransPortWaybill.setDespatchCity(despatchCity);
        reqTransPortWaybill.setDespatchPro(despatchPro);
        getViewModel().queryTransPortWaybill(reqTransPortWaybill);
    }

    public void onQueryTransPortWaybillSuccess(PageList<TransportWaybill> pageList) {
        refreshMoreLayout.onRefreshCompale(pageList);
    }

    @Override
    protected void onDestroy() {
        super.onDestroy();
        if (disposable != null) {
            disposable.dispose();
        }
    }
}
