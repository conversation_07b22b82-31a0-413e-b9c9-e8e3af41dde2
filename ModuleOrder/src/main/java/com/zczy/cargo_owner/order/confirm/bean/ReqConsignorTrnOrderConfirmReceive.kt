package com.zczy.cargo_owner.order.confirm.bean

import com.zczy.comm.http.entity.BaseNewRequest
import com.zczy.comm.http.entity.BaseRsp

/**
 *  desc: 货主确认收货-极速好货
 *  user: ssp
 *  time: 2025/3/25 14:03
 */
class ReqConsignorTrnOrderConfirmReceive(
    var orderId: String? = null
) : BaseNewRequest<BaseRsp<RspQueryTrnSingleReceiptOrderData>>("oms-app/consignor/consignorConfirm/consignorTrnOrderConfirmReceive")

/**
 *  desc: 货主打回-极速好货
 *  user: ssp
 *  time: 2025/3/25 14:03
 */
class ReqConsignorTrnOrderReject(
    var orderId: String? = null
) : BaseNewRequest<BaseRsp<RspQueryTrnSingleReceiptOrderData>>("oms-app/consignor/consignorConfirm/consignorTrnOrderReject")

/**
 *  desc: 货主确认收货前置校验
 *  user: ssp
 *  time: 2025/3/25 14:03
 */
class ReqBeforeConsignorTrnOrderConfirmCheck(
    var orderId: String? = null
) : BaseNewRequest<BaseRsp<RspQueryTrnSingleReceiptOrderData>>("oms-app/consignor/consignorConfirm/beforeConsignorTrnOrderConfirmCheck")
