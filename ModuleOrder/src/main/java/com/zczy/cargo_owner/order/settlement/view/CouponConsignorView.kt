package com.zczy.cargo_owner.order.settlement.view

import android.content.Context
import android.graphics.Color
import androidx.core.content.ContextCompat
import android.text.SpannableString
import android.text.TextUtils
import android.text.style.ImageSpan
import android.util.AttributeSet
import android.widget.TextView
import com.sfh.lib.mvvm.service.BaseViewModel
import com.sfh.lib.rx.IResultSuccessNoFail
import com.zczy.cargo_owner.order.R
import com.zczy.cargo_owner.order.confirm.widget.CenterAlignImageSpan
import com.zczy.cargo_owner.order.settlement.bean.ReqQueryConsignorCouponCache
import com.zczy.cargo_owner.order.settlement.bean.RspConsignorCoupon
import com.zczy.comm.http.entity.BaseRsp
import com.zczy.comm.utils.dp2px
import com.zczy.comm.utils.ex.setVisible

/***
 * 可用优惠券，(优惠券)
 */
class CouponConsignorView:TextView {
    constructor(context: Context) : super(context)

    constructor(context: Context, attrs: AttributeSet?) : super(context, attrs)

    init {
        setBackgroundColor(Color.parseColor("#FEF6D9"))
        setTextColor(Color.parseColor("#666666"))
        setTextSize(12.0f)
        setPadding(dp2px(14.0f),dp2px(6.0f),dp2px(14.0f),dp2px(6.0f));
        setVisible(false)
    }

    fun start(vm:BaseViewModel?){

        vm?.execute(ReqQueryConsignorCouponCache(), object : IResultSuccessNoFail<BaseRsp<RspConsignorCoupon>> {
            override fun onSuccess(p0: BaseRsp<RspConsignorCoupon>) {
                if (p0.success() )
                {
                    p0.data?.let {
                        if (!it.dataListSize.isNullOrEmpty()){
                            setVisible(true)
                            val sp = SpannableString(String.format("您有%s张运费抵用券未使用，若运费满足用券规则可在申请结算时使用",it.dataListSize))
                            //获取一张图片
                            val drawable = ContextCompat.getDrawable(context, R.drawable.base_message_black_ic)
                            drawable?.setBounds(0, 0, drawable.minimumWidth, drawable.minimumHeight)
                            //居中对齐imageSpan
                            val imageSpan = CenterAlignImageSpan(drawable)
                            sp.setSpan(imageSpan, 0, 1, ImageSpan.ALIGN_BASELINE)
                            setText(sp)
                        }
                    }
                }
            }
        })
    }
}