package com.zczy.cargo_owner.order.confirm.v2;

import android.text.TextWatcher;
import android.widget.EditText;
import android.widget.TextView;

import java.lang.reflect.Field;
import java.util.ArrayList;

/**
 * <AUTHOR>
 * @description
 * @company 中储智运科技股份有限公司
 * @copyright （版权）中储南京智慧物流科技有限公司所有
 * @since 2021-10-12
 */
class EditTextClearTextWatcher {

    /***
     * 清空TextWatcher
     * @param editText
     */
    public static void clear(EditText editText) {
        try {
            Field mListenersField = TextView.class.getDeclaredField("mListeners");
            mListenersField.setAccessible(true);
            ArrayList list = (ArrayList) mListenersField.get(editText);
            list.clear();

        } catch (Exception e) {
           System.out.println(e.getLocalizedMessage());
        }
    }
}
