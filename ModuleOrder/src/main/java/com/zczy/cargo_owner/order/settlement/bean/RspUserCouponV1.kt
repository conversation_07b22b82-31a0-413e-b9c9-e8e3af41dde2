package com.zczy.cargo_owner.order.settlement.bean

/**
 *@Desc 优惠券
 *@User ssp
 *@Date 2023/8/21-19:48
 */
class RspUserCouponV1(
    var userCouponId: String = "",
    /** 优惠券类型名称*/
    var couponTypeName: String = "",
    var couponId: String = "",
    var haveUsed: String = "",
    /** 优惠券编码*/
    var couponCode: String = "",
    /** 是否支持叠加使用*/
    var haveAllowRepeat: String = "",
    /** 优惠券面额*/
    var couponUnitMoney: String = "",
    /** couponTypeId == 2 才有 抵用券折扣类型：0 抵扣比例 1 抵扣金额 2 随机折扣*/
    var discountType: String = "",
    /** 满多少可用*/
    var minMoney: String = "",
    /** 优惠券名称*/
    var couponName: String = "",
    /** 优惠券有效期*/
    var validityTime: String = "",
    var haveExpire: String = "",
    /** 优惠券用户唯一编码*/
    var couponRecordCode: String = "",
    var orderMoney: String = "",
    var couponType: String = "",
    /** 优惠券是否可以开始使用  #1可用 0不可用  未使用tab时展示用*/
    var isUse: String = "",
    /** 优惠券类型ID 1 增值卷 2 抵用卷 3 任务类型-满赠卷 4 任务类型-满减卷 5.油气优惠券6 :保证金 7：服务费*/
    var couponTypeId: String = "",
    /** 每张抵用券抵扣比例 */
    var discountRatio: String = "",
    /** 每张抵用券抵扣折扣（例：discountRatio = 75；discountRatioStr = 7.5（折）） */
    var discountRatioStr: String = "",
    var validityEndTime: String = "",
    var redemptionCode: String = "",
    var isDefault: String = "",
    // 金额上限
    var discountMoneyTop: String = "",
    var defaultFlag: String = "",
    // #门槛上限展示，app端用
    var dyqCouponName: String = "",
    /** 使用说明*/
    var instructions: String = "",
)