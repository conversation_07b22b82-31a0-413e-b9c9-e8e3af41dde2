package com.zczy.cargo_owner.order.express.model

import com.sfh.lib.exception.HandleException
import com.sfh.lib.mvvm.service.BaseViewModel
import com.sfh.lib.rx.IResult
import com.zczy.cargo_owner.order.express.req.ReqOrderExpressReason
import com.zczy.comm.http.entity.BaseRsp
import com.zczy.comm.http.entity.ResultData

/**
 *  user: ssp
 *  time: 2020/11/9 13:47
 *  desc: 回单原因
 */
class OrderExpressMainReasonModel : BaseViewModel() {

    fun doSign(signReason: String?, orderId: String?) {
        execute(true,
                ReqOrderExpressReason(signReason = signReason,
                        orderId = orderId),
                object : IResult<BaseRsp<ResultData>> {
                    override fun onSuccess(baseRsp: BaseRsp<ResultData>) {
                        if (baseRsp.success()) {
                            setValue("onDoSignSuccess")
                        } else {
                            showDialogToast(baseRsp.msg)
                        }
                    }

                    override fun onFail(e: HandleException) {
                        hideLoading()
                        showDialogToast(e.msg)
                    }
                })
    }

    fun doSignOrders(signReason: String?, orderIds: String?) {
        execute(true,
            ReqOrderExpressReason(signReason = signReason,
                orderIds = orderIds),
            object : IResult<BaseRsp<ResultData>> {
                override fun onSuccess(baseRsp: BaseRsp<ResultData>) {
                    if (baseRsp.success()) {
                        setValue("onDoSignSuccess")
                    } else {
                        showDialogToast(baseRsp.msg)
                    }
                }

                override fun onFail(e: HandleException) {
                    hideLoading()
                    showDialogToast(e.msg)
                }
            })
    }

}