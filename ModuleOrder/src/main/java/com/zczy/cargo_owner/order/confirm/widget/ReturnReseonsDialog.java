package com.zczy.cargo_owner.order.confirm.widget;

import android.app.AlertDialog;
import android.app.Dialog;
import android.content.Context;
import android.content.DialogInterface;
import android.graphics.Color;
import android.os.Bundle;
import android.text.Editable;
import android.text.SpannableString;
import android.text.SpannableStringBuilder;
import android.text.Spanned;
import android.text.TextUtils;
import android.text.TextWatcher;
import android.text.style.ForegroundColorSpan;
import android.view.View;
import android.widget.EditText;
import android.widget.ImageView;
import android.widget.TextView;

import com.zczy.cargo_owner.order.R;

/**
 * 功能描述: 议价提示对话框
 *
 * <AUTHOR> 孙飞虎
 * @company 中储智运科技股份有限公司
 * @copyright （版权）中储南京智慧物流科技有限公司所有
 * @date 2017/12/8
 */
public class ReturnReseonsDialog extends Dialog implements View.OnClickListener {

    private ImageView ivColse;

    private TextView tvNo;

    private TextView tvOk;
    private EditText edit_code;
    private TextView tv_code_size;

    public interface ICallback {

        void onClick(DialogInterface dialog, boolean yesNo, String reson);
    }


    ICallback callback;

    public ReturnReseonsDialog(Context context, ICallback callback) {

        super(context, R.style.UserBossDialog);
        this.callback = callback;
    }

    @Override
    protected void onCreate(Bundle savedInstanceState) {

        super.onCreate(savedInstanceState);
        this.setContentView(R.layout.order_waybill_bargain_money_dialog);
        tv_code_size = findViewById(R.id.tv_code_size);
        ivColse = findViewById(R.id.ivColse);
        edit_code = findViewById(R.id.edit_code);
        ivColse.setOnClickListener(this);
        tvNo = findViewById(R.id.tv_no);
        tvNo.setOnClickListener(this);
        findViewById(R.id.tv_ok).setOnClickListener(this);
        edit_code.addTextChangedListener(new TextWatcher() {
            @Override
            public void beforeTextChanged(CharSequence s, int start, int count, int after) {

            }

            @Override
            public void onTextChanged(CharSequence s, int start, int before, int count) {

            }

            @Override
            public void afterTextChanged(Editable s) {
                tv_code_size.setText(String.format("(%s/50)", s.length()));
            }
        });
    }


    @Override
    public void onClick(View v) {

        if (v.getId() == R.id.ivColse) {
            this.dismiss();
        } else if (v.getId() == R.id.tv_ok) {
            this.callback.onClick(this, true, edit_code.getText().toString().trim());
        } else if (v.getId() == R.id.tv_no) {
            this.callback.onClick(this, false, "");
        }
    }
}
