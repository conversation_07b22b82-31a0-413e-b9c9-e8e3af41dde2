package com.zczy.cargo_owner.order.confirm.bean

import com.zczy.comm.http.entity.BaseNewRequest
import com.zczy.comm.http.entity.BaseRsp
import com.zczy.comm.http.entity.ResultData

/*=============================================================================================
 * 功能描述:        查询回单确认搜索项配置
 *---------------------------------------------------------------------------------------------
 *  涉及业务 & 更新说明:                                                                         
 *                                                                                             
 *                                                                                             
 *-------------------------------------------------------------------------------------------- 
 *  <AUTHOR> 孙飞虎  on  2021/11/22                                                   
 *  @Company    中储智运科技股份有限公司
 *  @Copyright （版权）中储南京智慧物流科技有限公司所有                                                                                        *
 *=============================================================================================*/

class ReqqueryConsignorFrontReceiptAppSearch
    : BaseNewRequest<BaseRsp<AppSearchList>>("oms-app/order/receipt/queryConsignorFrontReceiptAppSearch")

data class AppSearchList(
    var appSearchList:List<SearchItem>
) : ResultData()

data class SearchItem(
    var searchName: String = "",
    var searchKey: String = ""
)