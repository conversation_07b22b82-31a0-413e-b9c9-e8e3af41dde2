package com.zczy.cargo_owner.order.settlement.bean

import com.zczy.comm.http.entity.BaseNewRequest
import com.zczy.comm.http.entity.BaseRsp
import com.zczy.comm.http.entity.PageList

/**
 *@Desc 结算申请批量查询详情接口
 *@User ssp
 *@Date 2023/8/3-14:50
 */
class ReqSettleAccountDetailDataByDetails(
    var detailIds: String? = null, //明细Id
    var periodFlag: String? = null, //年份
    var modeType: String? = null, //0网货专票 1代开专票
) : BaseNewRequest<BaseRsp<PageList<RspQuerySettleApplyDataByAllChoose>>>("oms-app/settle/apply/consignor/settleAccountDetailDataByDetails")