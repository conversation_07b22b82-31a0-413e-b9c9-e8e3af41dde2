package com.zczy.cargo_owner.order.violate.req

import android.text.TextUtils
import com.zczy.comm.http.entity.BaseNewRequest
import com.zczy.comm.http.entity.BaseRsp
import com.zczy.comm.http.entity.ResultData

/**
 * desc: 根据orderId查询违约方
 * user: 宋双朋
 * time: 2025/1/17 14:53
 */
class ReqQueryBreachUserName(var orderId: String) : BaseNewRequest<BaseRsp<EBreachUserName>>("/wo-app/order/toBreachAddV2")

class EBreachUserName(
    // 1可以显示，其他不显示(保障服务费)
    var isShowMoney: String? = null,
    /**** 对方用户违约信息 */
    var person: EViolateUser? = null,
    /*** 违约类型 对方 */
    var type: List<EViolateType>? = null,
    /*** 违约类型 自己 */
    var typeOwner: List<EViolateType>? = null,
    val isPersonalAccidentPolicyMoney: Boolean,
    var modeType:String? = null,//业务模式：0：网货专票 1：代开专票  2：不要票
) : ResultData()

class EViolateUser {
    /*** 违约用户ID */
    var breachUserId: String? = null

    /*** 违约用户名 */
    var breachUserName: String? = null

    /*** 违约用户类型 */
    var breachType: String? = null

    /***违约保费 */
    var breachPolicyMoney: String? = null

    /***承运人的违约保费 */
    var carrierPolicyMoney: String? = null

    //人身意外保障费用
    var personalAccidentPolicyMoney: String? = null
    //违约运费损失保障服务费
    var freightLossPolicyMoney: String? = null

    // 摘单6小时取消运单提示框 false-不需要弹框，true-需要弹框
    var personalAccidentPolicyMoneyFlag: Boolean = false

    val personalAccidentPolicyMoneyNumber: String
        get() = if (TextUtils.isEmpty(personalAccidentPolicyMoney)) "0" else personalAccidentPolicyMoney?:"0.00"
    val isPersonalAccidentPolicy: Boolean
        get() {
            return (personalAccidentPolicyMoney?.toDoubleOrNull() ?: 0.00) > 0.0f
        }
}

class EViolateType {
    var id: String? = null
    var value: String? = null
    var dictKey: String? = null
    var memo: String? = null

    override fun toString(): String {
        return value!!
    }
}