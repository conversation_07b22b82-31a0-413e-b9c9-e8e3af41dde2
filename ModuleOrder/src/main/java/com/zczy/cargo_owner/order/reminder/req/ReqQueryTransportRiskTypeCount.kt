package com.zczy.cargo_owner.order.reminder.req

import com.zczy.comm.http.entity.BaseNewRequest
import com.zczy.comm.http.entity.BaseRsp
import com.zczy.comm.http.entity.ResultData
import com.zczy.comm.utils.ex.isNull

/**
 *  desc: 运输风险 首页数量
 *  user: 宋双朋
 *  time: 2024/11/6 17:13
 */

class ReqQueryTransportRiskTypeCount : BaseNewRequest<BaseRsp<RspQueryTransportRiskTypeCount>>("/oms-app/risk/event/queryTransportRiskTypeCount")

class RspQueryTransportRiskTypeCount(
    val transportRisk: String? = null, // 总数量
    val transportRisk1: String? = null, // 运输事故
    val transportRisk2: String? = null, // 装卸货纠纷
    val transportRisk3: String? = null, // 无法联系上承运人
    val transportRisk4: String? = null, // 货损货差
    val transportRisk5: String? = null, // 途中修车
    val transportRisk6: String? = null, // 环保检查
    val transportRisk7: String? = null, // 自然灾害
) : ResultData()