package com.zczy.cargo_owner.order.settlement.bean

import com.zczy.comm.http.entity.BaseNewRequest
import com.zczy.comm.http.entity.BaseRsp
import com.zczy.comm.http.entity.ResultData

/**
 *  desc: 查询发单员
 *  user: 宋双朋
 *  time: 2024/12/18 10:13
 */
class ReqQuerySubUserList : BaseNewRequest<BaseRsp<RspQuerySubUserListV1>>("mms-app/member/querySubUserList")

class RspQuerySubUserListV1(
    val data: MutableList<RspQuerySubUserList>? = null,//发单员信息集合
) : ResultData()

class RspQuerySubUserList(
    val userNm: String? = null,//发单员名称
    val userId: String? = null,//发单员id
)