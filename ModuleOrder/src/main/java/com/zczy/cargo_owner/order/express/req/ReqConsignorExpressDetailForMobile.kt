package com.zczy.cargo_owner.order.express.req

import com.zczy.comm.http.entity.BaseNewRequest
import com.zczy.comm.http.entity.BaseRsp
import com.zczy.comm.http.entity.ResultData
import com.zczy.comm.utils.ex.*

/**
 * PS: 快递详情
 * Created by sdx on 2019/2/26.
 */
data class ReqConsignorExpressDetailForMobile(
    var orderId: String = "", // 订单ID
    var detailId: String = "" // 明细ID
) : BaseNewRequest<BaseRsp<RspConsignorExpressDetailForMobile>>("oms-app/order/express/consignorExpressDetailForMobile ")

data class RspConsignorExpressDetailForMobile(
    var orderId: String = "",
    var expressCompanyName: String = "",
    var sendType: String = "", // 寄件方式；1:快递送达 2:线下送达
    var state: String = "", //快递单当前状态0在途;1 揽收;2 疑难;3 签收;4 退签;5 派件;6 退回;7 转单;10 待清关;11 清关中;12 已清关;13 清关异常;14 拒签 ',
    var plateNumber: String = "",
    var expressNum: String = "",
    var signState: String = "", //快递签收状态
    var repulseReason: String = "", //打回原因
    var signReason: String = "", //回单签收原因
    var expressInfo: List<RspExpressDetailItem> = emptyList()
) : ResultData()

fun RspConsignorExpressDetailForMobile.showState(): String {
    return when (state) {
        "0" -> {
            "在途"
        }
        "1" -> {
            "揽收"
        }
        "2" -> {
            "疑难"
        }
        "3" -> {
            "签收"
        }
        "4" -> {
            "退签"
        }
        "5" -> {
            "派件"
        }
        "6" -> {
            "退回"
        }
        "7" -> {
            "转单"
        }
        "10" -> {
            "待清关"
        }
        "11" -> {
            "清关中"
        }
        "12" -> {
            "已清关"
        }
        "13" -> {
            "清关异常"
        }
        "14" -> {
            "拒签"
        }
        else -> {
            ""
        }
    }
}

data class RspExpressDetailItem(
    var ftime: String = "",
    var areaCode: String = "",
    var areaName: String = "",
    var context: String = "",
    var time: String = "",
    var status: String = ""
)

fun RspExpressDetailItem.formatTime(): String {
    return time.toCalendar(YYYY_MM_DD_HH_MM)?.getDefTime(HH_MM) ?: ""
}

fun RspExpressDetailItem.formatDate(): String {
    return time.toCalendar(YYYY_MM_DD_HH_MM)?.getDefTime(YYYY_MM_DD) ?: ""
}

fun RspConsignorExpressDetailForMobile.getSignStates(): String {

    var signStates = ""
    when (signState) {
        "0" -> {
            signStates = "未签收"

        }
        "1" -> {
            signStates = "已签收"
        }
        "3" -> {
            signStates = "待录入"
        }
        "4" -> {
            signStates = "已打回"
        }
        "5" -> {
            signStates = "已签收"
        }
        else -> {
            signStates = ""
        }
    }
    return signStates
}


