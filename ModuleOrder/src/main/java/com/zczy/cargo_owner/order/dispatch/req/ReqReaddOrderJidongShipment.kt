package com.zczy.cargo_owner.order.dispatch.req

import com.zczy.comm.http.entity.BaseNewRequest
import com.zczy.comm.http.entity.BaseRsp
import com.zczy.comm.http.entity.ResultData


/*=============================================================================================
 * 功能描述: 生成派车单，重新生成派车单
 *---------------------------------------------------------------------------------------------
 *  涉及业务 & 更新说明:
 *
 *
 *--------------------------------------------------------------------------------------------
 *  <AUTHOR>  on  2022/3/31
 *  @Company    中储智运科技股份有限公司
 *  @Copyright （版权）中储南京智慧物流科技有限公司所有                                                                                        *
 *=============================================================================================*/
class ReqReaddOrderJidongShipment(
        var orderId: String? = ""//订单号
): BaseNewRequest<BaseRsp<ResultData>>("oms-app/order/orderJidongShipment/readdOrderJidongShipment")
