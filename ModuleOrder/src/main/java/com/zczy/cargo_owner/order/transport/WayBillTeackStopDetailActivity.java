package com.zczy.cargo_owner.order.transport;

import android.content.Context;
import android.content.Intent;
import android.os.Bundle;
import androidx.annotation.Nullable;
import androidx.recyclerview.widget.LinearLayoutManager;
import androidx.recyclerview.widget.RecyclerView;

import com.sfh.lib.mvvm.annotation.LiveDataMatch;
import com.sfh.lib.ui.AbstractLifecycleActivity;
import com.zczy.cargo_owner.order.R;
import com.zczy.cargo_owner.order.transport.adapter.WayBillTeackStopDetail;
import com.zczy.cargo_owner.order.transport.model.TransportModel;
import com.zczy.cargo_owner.order.transport.model.req.ReqQueryTrajectoryStopLocation;
import com.zczy.cargo_owner.order.transport.model.req.RspQueryTrajectoryStopLocation;
import com.zczy.cargo_owner.order.transport.model.req.RspQueryTrajectoryStopLocationChildV1;

import java.util.List;

/*=============================================================================================
 * 功能描述:停留未行驶详情
 *---------------------------------------------------------------------------------------------
 *  涉及业务 & 更新说明:
 *
 *
 *--------------------------------------------------------------------------------------------
 *  <AUTHOR>  on  2022/3/17
 *  @Company    中储智运科技股份有限公司
 *  @Copyright （版权）中储南京智慧物流科技有限公司所有                                                                                        *
 *=============================================================================================*/
public class WayBillTeackStopDetailActivity extends AbstractLifecycleActivity<TransportModel> {
    private RecyclerView recycleview;
    private WayBillTeackStopDetail mAdapter;
    private String orderId;
    private String selectTime;
    List<RspQueryTrajectoryStopLocationChildV1> list;

    public static void start(Context context, String orderId, String selectTime) {
        Intent intent = new Intent(context, WayBillTeackStopDetailActivity.class);
        intent.putExtra("orderId", orderId);
        intent.putExtra("selectTime", selectTime);
        context.startActivity(intent);
    }

    @Override
    protected void onCreate(@Nullable Bundle savedInstanceState) {
        super.onCreate(savedInstanceState);
        setContentView(R.layout.way_bill_teack_stop_detail_activity);
        initView();
        initData();
    }

    private void initData() {
        getViewModel().queryTrajectoryStopLocation(new ReqQueryTrajectoryStopLocation(orderId, selectTime));
    }

    private void initView() {
        orderId = getIntent().getStringExtra("orderId");
        selectTime = getIntent().getStringExtra("selectTime");
        recycleview = findViewById(R.id.recycleview);
        recycleview.setLayoutManager(new LinearLayoutManager(this));
        mAdapter = new WayBillTeackStopDetail(this);
        recycleview.setAdapter(mAdapter);
    }

    @LiveDataMatch
    public void onQueryTrajectoryStopLocationSuccess(RspQueryTrajectoryStopLocation data) {
        mAdapter.setData(data.getTrajectoryStopList());
    }
}
