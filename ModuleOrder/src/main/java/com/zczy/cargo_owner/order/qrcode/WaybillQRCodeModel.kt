package com.zczy.cargo_owner.order.qrcode

import android.Manifest
import android.R.attr.path
import android.content.Context
import android.content.Intent
import android.graphics.Bitmap
import android.graphics.Bitmap.CompressFormat
import android.graphics.Canvas
import android.graphics.Color
import android.net.Uri
import android.os.Bundle
import android.os.Environment
import android.provider.MediaStore
import android.text.SpannableString
import android.text.SpannableStringBuilder
import android.text.Spanned
import android.text.style.AbsoluteSizeSpan
import android.text.style.ImageSpan
import android.view.View
import com.google.zxing.BarcodeFormat
import com.google.zxing.EncodeHintType
import com.google.zxing.MultiFormatWriter
import com.google.zxing.common.BitMatrix
import com.jaeger.library.StatusBarUtil
import com.sfh.lib.exception.HandleException
import com.sfh.lib.mvvm.service.BaseViewModel
import com.sfh.lib.rx.IResult
import com.sfh.lib.rx.IResultSuccess
import com.zczy.cargo_owner.order.R
import com.zczy.comm.http.entity.BaseRsp
import com.zczy.comm.permission.PermissionCallBack
import com.zczy.comm.permission.PermissionUtil
import com.zczy.comm.pluginserver.AMainServer
import com.zczy.comm.ui.BaseActivity
import com.zczy.comm.utils.dp2px
import com.zczy.comm.utils.getResDrawable
import com.zczy.comm.utils.json.toJsonObject
import com.zczy.comm.widget.span.MyImageSpan
import kotlinx.android.synthetic.main.order_waybill_qr_code_activity.*
import java.io.File
import java.io.FileNotFoundException
import java.io.FileOutputStream
import java.util.*


/**
 * PS:
 * <AUTHOR> by sdx on 2020/6/1.
 */
class WaybillQRCodeModel : BaseViewModel() {
    fun queryHugeOrderDetailQrcodeForApp(hugeOrderId: String) {
        execute(true,
                ReqQueryHugeOrderDetailQrcodeForApp(hugeOrderId = hugeOrderId)
        ) {
            if (it.success()) {
                setValue("onQueryHugeOrderDetailQrcodeForApp", it.data)
            } else {
                showDialogToast(it.msg)
            }
        }
    }

    fun querySeniorOrderDetailQrcodeForApp(hugeOrderId: String) {
        execute(true,
            ReqQuerySeniorOrderDetailQrcodeForApp(orderId = hugeOrderId)
        ) {
            if (it.success()) {
                setValue("onQuerySeniorOrderDetailQrcodeForAppSuccess", it.data)
            } else {
                showDialogToast(it.msg)
            }
        }
    }

    fun queryCommonOrderDetailQrcodeForApp(orderId: String) {
        execute(true,
            ReqQueryCommonOrderDetailQrcodeForApp(orderId = orderId)
        ) {
            if (it.success()) {
                setValue("onQueryCommonOrderDetailQrcodeForAppSuccess", it.data)
            } else {
                showDialogToast(it.msg)
            }
        }
    }
}