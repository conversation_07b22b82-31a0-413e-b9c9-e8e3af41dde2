package com.zczy.cargo_owner.order.entity;

/**
 * 功能描述:运单按钮
 *
 * <AUTHOR> 孙飞虎
 * @company 中储智运科技股份有限公司
 * @copyright （版权）中储南京智慧物流科技有限公司所有
 * @date 2019/1/2
 */
public class EWaybillButton {

    boolean showQrCode;
    /***取消发布*/
    boolean cancelRelease;
    /***重新发布*/
    boolean republish;
    /***删除*/
    boolean delete;
    /***选择承运方*/
    boolean chooseCarrier;
    /***在途跟踪*/
    boolean track;
    /***违约申请*/
    boolean breachApply;
    /***轨迹回放*/
    boolean backLook;
    /***评价*/
    boolean evaluate;
    /***查看评价*/
    boolean lookEvaluation;
    /**
     * 变更信息
     */
    boolean changeInfo;
    /***编辑*/
    boolean modify;
    /***发布*/
    boolean release;
    /***还原*/
    boolean restore;
    /***永久删除*/
    boolean completeDelete;
    /**
     * 联系司机(按钮)
     */
    boolean contactDriver;
    /**
     * 发布失败重新发布
     */
    boolean publishAgain;
    /*是否展示查看定位*/
    boolean lookLocation;
    boolean consignorNeedVehicle;
    /**
     * 发货单上传
     */
    boolean uploadAdvanceFile;

    //合同补签
    boolean signContract;
    boolean showEditCommentButton;//wlhy-980_货主端-运单自定义编号 编辑自定义编号按钮 true展示
    boolean goSettleApply;//true展示，跳转到货主结算申请列表未申请tab页
    boolean goBackOrder;//true展示，跳转到货主回单确认列表未确认tab页
    boolean consignorModifyOrderPrice;//货主修改未成交运单价格按钮
    boolean receiptAddressModify;//押回单地址变更按钮
    boolean estimatedTimeButton;//预估运输时间
    boolean changeLoadUnloadRange;//变更装卸货范围

    public boolean isChangeLoadUnloadRange() {
        return changeLoadUnloadRange;
    }

    public boolean isEstimatedTimeButton() {
        return estimatedTimeButton;
    }

    public boolean isReceiptAddressModify() {
        return receiptAddressModify;
    }

    public boolean isConsignorModifyOrderPrice() {
        return consignorModifyOrderPrice;
    }

    public boolean isGoSettleApply() {
        return goSettleApply;
    }

    public boolean isGoBackOrder() {
        return goBackOrder;
    }

    public boolean isShowEditCommentButton() {
        return showEditCommentButton;
    }

    public void setShowEditCommentButton(boolean showEditCommentButton) {
        this.showEditCommentButton = showEditCommentButton;
    }

    public boolean isSignContract() {
        return signContract;
    }

    public boolean isUploadAdvanceFile() {
        return uploadAdvanceFile;
    }

    public boolean isLookLocation() {
        return lookLocation;
    }

    public void setLookLocation(boolean lookLocation) {
        this.lookLocation = lookLocation;
    }

    public boolean isPublishAgain() {
        return publishAgain;
    }

    public boolean isContactDriver() {
        return contactDriver;
    }

    public boolean isChangeInfo() {
        return changeInfo;
    }

    public boolean isCancelRelease() {
        return cancelRelease;
    }

    public boolean isRepublish() {
        return republish;
    }

    public boolean isModify() {
        return modify;
    }

    public boolean isRelease() {
        return release;
    }

    public boolean isRestore() {
        return restore;
    }

    public boolean isCompleteDelete() {
        return completeDelete;
    }

    public boolean isDelete() {

        return delete;
    }

    public boolean isChooseCarrier() {

        return chooseCarrier;
    }

    public boolean isTrack() {

        return track;
    }

    public boolean isBreachApply() {

        return breachApply;
    }

    public boolean isBackLook() {

        return backLook;
    }

    public boolean isEvaluate() {

        return evaluate;
    }

    public boolean isLookEvaluation() {

        return lookEvaluation;
    }

    public boolean isShowQrCode() {
        return showQrCode;
    }

    public boolean isConsignorNeedVehicle() {
        return consignorNeedVehicle;
    }

    public void setConsignorNeedVehicle(boolean consignorNeedVehicle) {
        this.consignorNeedVehicle = consignorNeedVehicle;
    }
}
