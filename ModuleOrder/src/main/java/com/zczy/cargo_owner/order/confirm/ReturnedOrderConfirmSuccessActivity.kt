package com.zczy.cargo_owner.order.confirm

import android.content.Context
import android.content.Intent
import android.os.Bundle
import android.text.TextUtils
import android.view.View
import android.widget.Toast
import com.sfh.lib.mvvm.annotation.LiveDataMatch
import com.sfh.lib.ui.dialog.DialogBuilder
import com.zczy.cargo_owner.order.R
import com.zczy.cargo_owner.order.confirm.bean.AutoApplySettlementReq
import com.zczy.cargo_owner.order.confirm.bean.AutoApplySettlementRes
import com.zczy.cargo_owner.order.confirm.bean.CheckOrderCanUsePresetRewardRsp
import com.zczy.cargo_owner.order.confirm.bean.ReqCheckOrderCanUsePresetReward
import com.zczy.cargo_owner.order.settlement.SettlementApplicationListActivity
import com.zczy.cargo_owner.order.settlement.bean.FindSettleAccountMoneyIsEnoughReq
import com.zczy.cargo_owner.order.settlement.bean.FindSettleAccountMoneyIsEnoughRsp
import com.zczy.comm.CommServer
import com.zczy.comm.ui.BaseActivity
import com.zczy.comm.utils.ex.isTrue
import com.zczy.comm.utils.ex.toCommaString
import kotlinx.android.synthetic.main.order_returned_confirm_success_activity.*

/**
 * @description 回单确认成功
 * <AUTHOR> 韩正亚
 * @company 中储智运科技股份有限公司
 * @copyright （版权）中储南京智慧物流科技有限公司所有
 * @date 2019/4/15
 */

open class ReturnedOrderConfirmSuccessActivity : BaseActivity<ReturnedOrderConfirmModel>() {
    private lateinit var autoDetailIds: String
    private lateinit var successCount: String
    private lateinit var showAutoSettle: String
    private var isBatch = false

    override fun getLayout(): Int {
        return R.layout.order_returned_confirm_success_activity
    }

    override fun bindView(bundle: Bundle?) {
        btnReturnedOrderConfirmSuccessGotIt.setOnClickListener {
            finish()
        }

        btnAutoSettlementApply.setOnClickListener {
            viewModel?.checkOrderCanUsePresetRewardReq(
                ReqCheckOrderCanUsePresetReward(
                    detailIds = autoDetailIds
                )
            )
        }
        btnToSettlementApply.setOnClickListener {
            SettlementApplicationListActivity.start(this@ReturnedOrderConfirmSuccessActivity, 1, "");
        }
    }

    override fun initData() {
        autoDetailIds = intent.getStringExtra(AUTO_DETAIL_IDS) ?: ""
        successCount = intent.getStringExtra(SUCCESS_COUNT) ?: ""
        showAutoSettle = intent.getStringExtra(SHOW_AUTOSETTLE) ?: ""
        isBatch = intent.getBooleanExtra(IS_BATCH, false)

        if (showAutoSettle.isTrue) {
            btnAutoSettlementApply.text = "自动结算申请"
            btnAutoSettlementApply.visibility = View.VISIBLE
            btnReturnedOrderConfirmSuccessGotIt.text = "不，谢谢"
        } else {
            btnAutoSettlementApply.visibility = View.GONE
        }
    }

    @LiveDataMatch
    open fun autoApplySettlementSuccess(res: AutoApplySettlementRes) {
        val count = res.data?.failedCount?.toDouble();
        if (count != null && count > 0) {
            val dialog = DialogBuilder()
                .setHideCancel(true)
                .setCancelable(false)
                .setTitle("提示")
                .setOkListener { dialogInterface, _ ->
                    dialogInterface.dismiss()
                    finish()
                }
                .setOKText("知道了")
                .setMessage("请求成功，提交结算申请成功${res.data?.succedCount ?: "0"}单，失败${res.data?.failedCount ?: "0"}单！  \n 失败原因:${res.data?.showMsg ?: ""}")
            showDialog(dialog)
        } else {
            val dialog = DialogBuilder()
                .setHideCancel(true)
                .setCancelable(false)
                .setTitle("提示")
                .setOkListener { dialogInterface, _ ->
                    dialogInterface.dismiss()
                    finish()
                }
                .setOKText("知道了")
                .setMessage("请求成功，提交结算申请成功${res.data?.succedCount ?: "0"}单，失败${res.data?.failedCount ?: "0"}单！")
            showDialog(dialog)
        }
    }

    @LiveDataMatch
    open fun onMoneyEnough(data: CheckOrderCanUsePresetRewardRsp) {
        if (!TextUtils.isEmpty(data.showMsgCount)) {
            val showMsgCountInt = data.showMsgCount?.toIntOrNull() ?: 0
            if (showMsgCountInt > 0) {
                val dialogBuilder = DialogBuilder()
                dialogBuilder.message = data.showMsg
                dialogBuilder.title = "提示"
                dialogBuilder.isHideCancel = true
                dialogBuilder.setOKTextColor("我知道了", R.color.text_blue)
                dialogBuilder.setOkListener { dialogInterface, _ ->
                    dialogInterface.dismiss()
                    viewModel?.autoApplySettlement(
                        AutoApplySettlementReq(
                            autoDetailIds,
                            autoDetailIds,
                            CommServer.getUserServer().login.userId,
                            CommServer.getUserServer().login.childId,
                            CommServer.getUserServer().login.userName
                        )
                    )
                }
                showDialog(dialogBuilder)
            }else{
                viewModel?.autoApplySettlement(
                    AutoApplySettlementReq(
                        autoDetailIds,
                        autoDetailIds,
                        CommServer.getUserServer().login.userId,
                        CommServer.getUserServer().login.childId,
                        CommServer.getUserServer().login.userName
                    )
                )
            }
        }else{
            viewModel?.autoApplySettlement(
                AutoApplySettlementReq(
                    autoDetailIds,
                    autoDetailIds,
                    CommServer.getUserServer().login.userId,
                    CommServer.getUserServer().login.childId,
                    CommServer.getUserServer().login.userName
                )
            )
        }
    }

    @LiveDataMatch
    open fun autoApplySettlementFail(res: String) {
        val dialog = DialogBuilder()
            .setHideCancel(true)
            .setCancelable(false)
            .setTitle("提示")
            .setOkListener { dialogInterface, _ ->
                dialogInterface.dismiss()
                finish()
            }
            .setOKText("知道了")
            .setMessage(res)
        showDialog(dialog)
    }

    companion object {
        private const val AUTO_DETAIL_IDS = "autoDetailIds"
        private const val SUCCESS_COUNT = "successCount"
        private const val SHOW_AUTOSETTLE = "showAutoSettle"
        private const val IS_BATCH = "IS_BATCH"

        @JvmStatic
        fun start(
            context: Context?,
            autoDetailIds: String,
            successCount: String,
            showAutoSettle: String,
            isBatch: Boolean = false
        ) {
            if (context == null) return
            val intent = Intent(context, ReturnedOrderConfirmSuccessActivity::class.java)
            intent.putExtra(AUTO_DETAIL_IDS, autoDetailIds)
            intent.putExtra(SUCCESS_COUNT, successCount)
            intent.putExtra(SHOW_AUTOSETTLE, showAutoSettle)
            intent.putExtra(IS_BATCH, isBatch)
            context.startActivity(intent)
        }
    }
}