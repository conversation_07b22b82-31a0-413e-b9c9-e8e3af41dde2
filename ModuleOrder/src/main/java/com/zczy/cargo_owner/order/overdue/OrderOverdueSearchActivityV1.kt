package com.zczy.cargo_owner.order.overdue

import android.content.Context
import android.content.Intent
import android.os.Bundle
import android.text.TextUtils
import android.widget.TextView
import com.sfh.lib.mvvm.annotation.LiveDataMatch
import com.sfh.lib.mvvm.service.BaseViewModel
import com.zczy.cargo_owner.order.R
import com.zczy.cargo_owner.order.confirm.ReturnedOrderConfirmListActivity
import com.zczy.cargo_owner.order.detail.WaybillDetailStatueActivity
import com.zczy.cargo_owner.order.overdue.adapter.OrderOverdueUndoAdapter
import com.zczy.cargo_owner.order.overdue.model.OrderOverdueFragmentModel
import com.zczy.cargo_owner.order.overdue.req.OrderOverDueItem
import com.zczy.cargo_owner.order.settlement.SettlementApplicationListActivity
import com.zczy.cargo_owner.order.violate.OrderViolateListActivity
import com.zczy.comm.http.entity.PageList
import com.zczy.comm.ui.BaseActivity
import com.zczy.comm.utils.dp2px
import com.zczy.comm.utils.toJson
import com.zczy.comm.widget.pulltorefresh.CommEmptyView
import kotlinx.android.synthetic.main.order_overdue_search_activity.*

/**
 * PS: 逾期运单管理
 * Created by sdx on 2019-06-24.
 */
class OrderOverdueSearchActivityV1 : BaseActivity<BaseViewModel>() {

    private val mAdapter = OrderOverdueUndoAdapter()
    private val remarks by lazy { intent.getStringExtra(REMARKS) }
    private val limitPublishFlag by lazy { intent.getStringExtra("limitPublishFlag") }

    companion object {
        const val REMARKS = "remarks"

        @JvmStatic
        fun start(context: Context?, remarks: String, limitPublishFlag: String) {
            context ?: return
            val intent = Intent(context, OrderOverdueSearchActivityV1::class.java)
            intent.putExtra("remarks", remarks)
            intent.putExtra("limitPublishFlag", limitPublishFlag)
            context.startActivity(intent)
        }
    }

    override fun getLayout(): Int = R.layout.order_overdue_search_activity

    override fun bindView(bundle: Bundle?) {
        val emptyView = CommEmptyView.creatorDef(this@OrderOverdueSearchActivityV1)
        swipe_refresh_more_layout.apply {
            setAdapter(mAdapter, true)
            setEmptyView(emptyView)
            addItemDecorationSize(dp2px(7f))
            addOnItemChildClickListener { _, view, position ->
                val item = mAdapter.data[position]
                when (view.id) {
                    // 进入详情
                    R.id.overdue_order_view -> {
                        WaybillDetailStatueActivity.start(context, item.orderId)
                    }
                    // 违约申请
                    R.id.btnApply -> {
                        getViewModel(OrderOverdueFragmentModel::class.java).checkNospecify(item)
                    }
                    // 问题反馈
                    R.id.tvAppeal -> {

                    }

                    R.id.goDoing -> {
                        if (TextUtils.equals(item.consignorState, "7") && TextUtils.equals(item.backStatus, "3")) {
                            //跳转回单确认
                            ReturnedOrderConfirmListActivity.start(context, 0, "")
                        } else {
                            //跳转结算申请
                            SettlementApplicationListActivity.start(context, 0, "")
                        }
                    }
                }
            }
            setOnLoadListener2 { nowPage ->
                getViewModel(OrderOverdueFragmentModel::class.java).getSearchListInfo(
                    deltype = "1",
                    nowPage = nowPage,
                    remarks = remarks,
                    limitPublishFlag = limitPublishFlag
                )
            }
        }
    }

    override fun initData() {
        swipe_refresh_more_layout.onAutoRefresh()
    }


    @LiveDataMatch
    open fun onGetUndoListInfo(data: PageList<OrderOverDueItem>?) {
        swipe_refresh_more_layout.onRefreshCompale(data)
    }

    @LiveDataMatch
    open fun onViolateSuccess(item: OrderOverDueItem) {
        OrderViolateListActivity.startContentUI(this@OrderOverdueSearchActivityV1, item.orderId, item.specifyFlag, item.noResponsibilityFlag)
    }

}