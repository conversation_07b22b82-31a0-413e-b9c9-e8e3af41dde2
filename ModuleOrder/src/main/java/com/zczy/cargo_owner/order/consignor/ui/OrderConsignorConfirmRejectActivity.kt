package com.zczy.cargo_owner.order.consignor.ui

import android.app.Activity
import android.content.Intent
import android.os.Bundle
import androidx.fragment.app.Fragment
import android.view.View
import com.jakewharton.rxbinding2.widget.RxTextView
import com.sfh.lib.mvvm.annotation.LiveDataMatch
import com.zczy.cargo_owner.order.R
import com.zczy.cargo_owner.order.consignor.model.OrderConsignorConfirmRejectModel
import com.zczy.cargo_owner.order.consignor.req.Req3RepulseDeliverGoodsByOrderId
import com.zczy.cargo_owner.order.consignor.req.Rsp1QueryConsignorOrderStateOfMobile
import com.zczy.comm.ui.BaseActivity
import com.zczy.comm.utils.json.toJson
import com.zczy.comm.utils.json.toJsonObject
import kotlinx.android.synthetic.main.order_consignor_confirm_reject_activity.*

/** 功能描述:
 * 发货单确认 打回界面
 * <AUTHOR>
 * @company 中储智运科技股份有限公司
 * @copyright （版权）中储南京智慧物流科技有限公司所有
 * @date 2019/4/3
 */
open class OrderConsignorConfirmRejectActivity : BaseActivity<OrderConsignorConfirmRejectModel>() {

    private val eData by lazy {
        intent.getStringExtra(EXTRA_DATA_JSON).toJsonObject(Rsp1QueryConsignorOrderStateOfMobile::class.java)
                ?: Rsp1QueryConsignorOrderStateOfMobile()
    }

    companion object {
        private const val EXTRA_DATA_JSON = "extra_data_json"

        @JvmStatic
        fun start(fragment: androidx.fragment.app.Fragment?, data: Rsp1QueryConsignorOrderStateOfMobile, requestCode: Int) {
            if (fragment == null || fragment.context == null) return
            val intent = Intent(fragment.context, OrderConsignorConfirmRejectActivity::class.java)
            intent.putExtra(EXTRA_DATA_JSON, data.toJson())
            fragment.startActivityForResult(intent, requestCode)
        }

        @JvmStatic
        fun start(activity: Activity?, data: Rsp1QueryConsignorOrderStateOfMobile, requestCode: Int) {
            if (activity == null) return
            val intent = Intent(activity, OrderConsignorConfirmRejectActivity::class.java)
            intent.putExtra(EXTRA_DATA_JSON, data.toJson())
            activity.startActivityForResult(intent, requestCode)
        }
    }

    override fun getLayout(): Int = R.layout.order_consignor_confirm_reject_activity

    override fun bindView(bundle: Bundle?) {
        bindClickEvent(btn_commit)
        RxTextView.textChanges(ed_reason)
                .map(CharSequence::toString)
                .subscribe {
                    btn_commit.isEnabled = !it.isEmpty()
                }
                .apply {
                    putDisposable(this)
                }
    }

    override fun initData() {
    }

    override fun onSingleClick(v: View) {
        super.onSingleClick(v)
        when (v.id) {
            R.id.btn_commit -> {
                val req = Req3RepulseDeliverGoodsByOrderId(
                        detailId = eData.detailId,
                        orderId = eData.orderId,
                        repulseReason = ed_reason.text.toString().trim()
                )
                viewModel?.repulseDeliverGoodsByOrderId(req)
            }
        }
    }

    @LiveDataMatch
    open fun onRepulseDeliverGoodsByOrderId() {
        setResult(Activity.RESULT_OK)
        finish()
    }
}