package com.zczy.cargo_owner.order.billing

import android.content.Context
import android.content.Intent
import android.os.Bundle
import com.sfh.lib.mvvm.service.BaseViewModel
import com.zczy.cargo_owner.order.R
import com.zczy.cargo_owner.order.billing.fragment.OrderBillingReviewHomeFragment
import com.zczy.comm.ui.BaseActivity
import kotlinx.android.synthetic.main.order_billing_review_home_activity.*
import kotlinx.android.synthetic.main.order_billing_review_home_activity.tabLayout
import kotlinx.android.synthetic.main.order_billing_review_home_activity.viewPager

/**
 * 功能描述: 结算申请审核
 * <AUTHOR>
 * @date 2022/6/23-10:06
 */

class OrderBillingReviewHomeActivity : BaseActivity<BaseViewModel>() {

    companion object {

        @JvmStatic
        fun jumpPage(context: Context?) {
            val intent = Intent(context, OrderBillingReviewHomeActivity::class.java)
            context?.startActivity(intent)
        }
    }

    override fun getLayout(): Int {
        return R.layout.order_billing_review_home_activity
    }

    override fun bindView(bundle: Bundle?) {
        val titles = arrayOf("待审核", "已申请", "全部")
        val arrayList = ArrayList<androidx.fragment.app.Fragment>()
        arrayList.add(
            OrderBillingReviewHomeFragment.newInstance(
                type = OrderBillingReviewHomeFragment.TYPE_NOT_CONFIRM,
                showCheckboxLayoutAndView = true
            )
        )
        arrayList.add(
            OrderBillingReviewHomeFragment.newInstance(
                type = OrderBillingReviewHomeFragment.TYPE_CONFIRMED,
                showCheckboxLayoutAndView = false
            )
        )
        arrayList.add(
            OrderBillingReviewHomeFragment.newInstance(
                type = OrderBillingReviewHomeFragment.TYPE_ALL,
                showCheckboxLayoutAndView = false
            )
        )
        tabLayout.setViewPager(viewPager, titles, this, arrayList)
        appToolbar.setRightOnClickListener {
            val applyType = when (tabLayout.currentTab) {
                0 -> {
                    OrderBillingReviewHomeFragment.TYPE_NOT_CONFIRM
                }
                1 -> {
                    OrderBillingReviewHomeFragment.TYPE_CONFIRMED
                }
                else -> {
                    OrderBillingReviewHomeFragment.TYPE_ALL
                }
            }

            OrderBillingReviewFilterActivity.start(
                context = this@OrderBillingReviewHomeActivity,
                applyType
            )
        }
    }

    override fun initData() {

    }
}