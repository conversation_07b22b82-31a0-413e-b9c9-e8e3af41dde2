package com.zczy.cargo_owner.order.settlement.bean

import com.zczy.comm.http.entity.BaseNewRequest
import com.zczy.comm.http.entity.BaseRsp
import com.zczy.comm.http.entity.PageList

/**
 *  desc: 结算申请列表
 *  user: 宋双朋
 *  time: 2024/12/13 9:09
 */
data class QuerySettlementApplicationByTypeReq(
    val userId: String? = null,
    val userName: String? = null,
    val orderId: String? = null,
    val nowPage: Int=1,
    val applyType: Int = -1,
    val orderInfo: String? = null,
    val pageSize: Int = 10,
    val isApp: Int = 1,
    val sortedOfApply: String? = null,
    val consignorSubSelect: String? = null,
    var settleType: String? = "0",
    var preOrderId: String? = null,
    var periodFlag: String? = null,
    var searchOrderId: String? = null, //运单号
    var searchPlateNumber: String? = null, //车牌号
    var searchCargoName: String? = null, //货物名称
    var despatchPlace: String? = null, //发货地
    var deliverPlace: String? = null, //收货地
    var childUserId: String? = null, //
    var searchPublishChildId: String? = null, //	发单员
    var searchCarrierName: String? = null, //承运方
    var quickSettleFlag: String? = null, //是否满足快速结算 1为是 0为否
    var modeType: String? = null, //0网货专票 1代开专票
) : BaseNewRequest<BaseRsp<SettlementApplicationPageList<SettlementApplicationItemBean>>>("oms-app/settle/apply/consignor/settleAccountListData")

class SettleSearchBean(
    var searchOrderId: String? = null, //运单号
    var searchPlateNumber: String? = null, //车牌号
    var searchCargoName: String? = null, //货物名称
    var despatchPlace: String? = null, //发货地
    var deliverPlace: String? = null, //收货地
    var searchPublishChildId: String? = null, //	发单员
    var searchCarrierName: String? = null, //承运方
    var quickSettleFlag: String? = null, //是否满足快速结算 1为是 0为否
    var periodFlag: String? = null, //时间范围
)

class RspSortType(
    val name: String = "", // 名称
    val sortType: String = "", // 对应type
)

fun initModeType(): MutableList<RspSortType> {
    val list = mutableListOf<RspSortType>()
    list.add(RspSortType(name = "网货专票", sortType = "0"))
    list.add(RspSortType(name = "代开专票", sortType = "1"))
    return list
}

fun initSortType(): MutableList<RspSortType> {
    val list = mutableListOf<RspSortType>()
    list.add(RspSortType(name = "最新货主回单确认", sortType = "0"))
    list.add(RspSortType(name = "最早货主回单确认", sortType = "1"))
    list.add(RspSortType(name = "最新发货", sortType = "2"))
    list.add(RspSortType(name = "最早发货", sortType = "3"))
    list.add(RspSortType(name = "最新收货", sortType = "4"))
    list.add(RspSortType(name = "最早收货", sortType = "5"))
    return list
}

class RxBusSettleSuccess(
    var success: Boolean = false
)

fun QuerySettlementApplicationByTypeReq.setSettleApply() {
    settleType = when (applyType) {
        1 -> {
            null
        }

        -1 -> {
            null
        }

        else -> {
            "0"
        }
    }
}

fun QuerySettlementApplicationByTypeReq.setSettleApply1() {
    settleType = null
}

class SettlementApplicationPageList<E> : PageList<E>() {
    val settlementApplyAuditSwitch: String = "" // 是否开启结算申请审核 0-否 1-是
    val showAHMessageFlag: String = "" // 1展示
    val existConsignorFeeConfigFlag: String = "" // 1展示
}