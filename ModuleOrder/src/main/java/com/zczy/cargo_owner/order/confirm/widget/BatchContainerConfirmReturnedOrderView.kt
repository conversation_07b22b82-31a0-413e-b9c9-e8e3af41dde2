package com.zczy.cargo_owner.order.confirm.widget

import android.annotation.SuppressLint
import android.content.Context
import android.graphics.Color
import android.text.TextUtils
import android.util.AttributeSet
import androidx.constraintlayout.widget.ConstraintLayout
import com.sfh.lib.rx.ui.UtilRxView
import com.sfh.lib.utils.UtilTool
import com.zczy.cargo_owner.order.R
import com.zczy.cargo_owner.order.confirm.bean.BatchConfirmItemDetailBean
import com.zczy.cargo_owner.order.confirm.bean.ContainerNoJsonArray
import com.zczy.cargo_owner.order.confirm.bean.RspOrderIgnoreSmall
import com.zczy.cargo_owner.order.confirm.bean.computeMoneyV1
import com.zczy.cargo_owner.order.confirm.bean.getCargoCategory
import com.zczy.cargo_owner.order.confirm.bean.showContainerNum
import com.zczy.cargo_owner.order.confirm.roundToFourDecimals4Point
import com.zczy.cargo_owner.order.confirm.v2.DecimalDigitsInputFilter
import com.zczy.comm.utils.NumUtil
import com.zczy.comm.utils.ex.isNull
import com.zczy.comm.utils.ex.isTrue
import com.zczy.comm.utils.ex.setVisible
import com.zczy.comm.utils.ex.toDoubleRoundDownString
import kotlinx.android.synthetic.main.order_returned_batch_container_confirm_item.view.cl_order_textview12_2
import kotlinx.android.synthetic.main.order_returned_batch_container_confirm_item.view.etConfirmSettlementPriceIncludedTax
import kotlinx.android.synthetic.main.order_returned_batch_container_confirm_item.view.etConfirmSettlementPriceNotIncludedTax
import kotlinx.android.synthetic.main.order_returned_batch_container_confirm_item.view.etConfirmSettlementTonnage
import kotlinx.android.synthetic.main.order_returned_batch_container_confirm_item.view.etConsignorNoRateUnitShowMoney
import kotlinx.android.synthetic.main.order_returned_batch_container_confirm_item.view.ivLTLOrderFlag
import kotlinx.android.synthetic.main.order_returned_batch_container_confirm_item.view.llContainerNumber
import kotlinx.android.synthetic.main.order_returned_batch_container_confirm_item.view.orderLossRisesTonsNoTaxV2
import kotlinx.android.synthetic.main.order_returned_batch_container_confirm_item.view.orderLossRisesTonsNoTaxV3
import kotlinx.android.synthetic.main.order_returned_batch_container_confirm_item.view.orderLossRisesTonsTaxV1
import kotlinx.android.synthetic.main.order_returned_batch_container_confirm_item.view.orderLossRisesTonsTaxV2
import kotlinx.android.synthetic.main.order_returned_batch_container_confirm_item.view.orderLossRisesTonsTaxV3
import kotlinx.android.synthetic.main.order_returned_batch_container_confirm_item.view.orderSettleType
import kotlinx.android.synthetic.main.order_returned_batch_container_confirm_item.view.orderSettleTypeUnit
import kotlinx.android.synthetic.main.order_returned_batch_container_confirm_item.view.orderSettleTypeUnitEditText
import kotlinx.android.synthetic.main.order_returned_batch_container_confirm_item.view.orderSettlementBasis
import kotlinx.android.synthetic.main.order_returned_batch_container_confirm_item.view.order_textview21
import kotlinx.android.synthetic.main.order_returned_batch_container_confirm_item.view.returnedOrderDetailNo
import kotlinx.android.synthetic.main.order_returned_batch_container_confirm_item.view.tvPlaceOfEnd
import kotlinx.android.synthetic.main.order_returned_batch_container_confirm_item.view.tvPlaceOfStart
import kotlinx.android.synthetic.main.order_returned_batch_container_confirm_item.view.tvWaybillGoodsInfo
import kotlinx.android.synthetic.main.order_returned_batch_container_confirm_item.view.tvWaybillPlateNumber

/**
 *描述：批量回单确认单个view
 *auth:宋双朋
 *time:2024/5/30 20:49
 */

@SuppressLint("SetTextI18n")
class BatchContainerConfirmReturnedOrderView : ConstraintLayout {
    var computeAllMoneyBlock: () -> Unit = { }
    var item: BatchConfirmItemDetailBean = BatchConfirmItemDetailBean()

    constructor(context: Context) : super(context)

    constructor(context: Context, attrs: AttributeSet?) : super(context, attrs)

    constructor(context: Context, attrs: AttributeSet?, defStyleAttr: Int) : super(context, attrs, defStyleAttr)

    init {
        setBackgroundColor(Color.WHITE)
        inflate(context, R.layout.order_returned_batch_container_confirm_item, this)
    }

    fun getContainerData(): MutableList<ContainerNoJsonArray> {
        val list = mutableListOf<ContainerNoJsonArray>()
        for (i in 0 until llContainerNumber.childCount) {
            val view = llContainerNumber.getChildAt(i)
            if (view is BatchContainerNumberConfirmReturnedOrderView) {
                list.add(view.getItemData())
            }
        }
        return list
    }

    @SuppressLint("UseCompatLoadingForDrawables")
    fun setData(mItem: BatchConfirmItemDetailBean, consignorNoRateUnitShowMoneyFlag: String?, consignorRateMoneyUpdateFlag: String?) {
        this.item = mItem
        if (item.orderIgnoreSmallObj.isNull) {
            item.orderIgnoreSmallObj = RspOrderIgnoreSmall()
        }
        //运单信息
        returnedOrderDetailNo.text = item.orderId
        tvPlaceOfStart.text = "${item.despatchCity}${item.despatchDis}"
        tvPlaceOfEnd.text = "${item.deliverCity}${item.deliverDis}"
        tvWaybillGoodsInfo.text = String.format(
            context.resources.getString(R.string.order_goods_info),
            item.allCargoName, item.slipLoad, item.cargoCategoryName
                ?: "", item.orgBackMoney
        )
        llContainerNumber.removeAllViews()
        item.containerDataObj?.receiptContainerDataObj?.containerNoJsonArray?.forEach { containerItem ->
            val itemView = BatchContainerNumberConfirmReturnedOrderView(context)
            itemView.setItemData(containerItem)
            llContainerNumber.addView(itemView)
        }
        tvWaybillPlateNumber.text = "车牌号：" + item.plateNumber
        orderLossRisesTonsTaxV1.setVisible(true)
        orderLossRisesTonsTaxV2.setVisible(true)
        orderLossRisesTonsNoTaxV2.setVisible(true)
        orderLossRisesTonsNoTaxV3.setVisible(true)
        orderLossRisesTonsTaxV3.setVisible(true)
        ivLTLOrderFlag.setVisible(item.lTLOrder.isTrue)
        etConfirmSettlementPriceNotIncludedTax.filters = arrayOf(DecimalDigitsInputFilter(7, 2))
        etConsignorNoRateUnitShowMoney.filters = arrayOf(DecimalDigitsInputFilter(7, 2))
        item.computeMoneyV1()
        etConsignorNoRateUnitShowMoney.setText(item.consignorNoRateUnitShowMoney)
        cl_order_textview12_2.setVisible(consignorNoRateUnitShowMoneyFlag.isTrue)
        if (item.freightType.isTrue) {
            //单价
            orderSettleTypeUnitEditText.setText(item.pbConsignorUnitMoney.toDoubleRoundDownString(2))
            orderSettleType.text = "运费计价方式(单价)"
            when (item.releaseUseFlag) {
                "1" -> {
                    //禁止修改
                    orderSettleTypeUnitEditText.isEnabled = false
                }

                else -> {
                    orderSettleTypeUnitEditText.isEnabled = consignorRateMoneyUpdateFlag.isTrue
                }
            }
            orderSettleTypeUnit.text = "元/箱"
        } else {
            //包车价
            orderSettleTypeUnitEditText.setText(item.pbConsignorMoney.toDoubleRoundDownString(2))
            orderSettleTypeUnitEditText.isEnabled = false
            orderSettleTypeUnit.text = "元"
            orderSettleType.text = "运费计价方式(包车价)"
        }
        //结算信息,此处必须先初始化塞值，避免Listenr 重复计算，导致四舍五入错误
        etConfirmSettlementTonnage.setText(item.slipLoad)
        UtilRxView.afterTextChangeEvents(etConfirmSettlementPriceIncludedTax, 500) { content ->
            if (etConfirmSettlementPriceIncludedTax.isFocused) {
                //实际付款金额(含税)
                item.consignorRateMoney = content.toString()
                item.consignorNoRateMoney = getMoneyRateText(item.settleRate, item.consignorRateMoney)
                etConfirmSettlementPriceNotIncludedTax.setText(item.consignorNoRateMoney)
                //计算外部价格
                computeAllMoneyBlock()
            }
        }
        etConfirmSettlementPriceIncludedTax.setText(item.consignorRateMoney)
        etConfirmSettlementPriceNotIncludedTax.setText(item.consignorNoRateMoney)
        UtilRxView.afterTextChangeEvents(etConfirmSettlementPriceNotIncludedTax, 500) { content ->
            if (etConfirmSettlementPriceNotIncludedTax.isFocused) {
                //实际付款金额(承运方预估到手价)
                item.consignorNoRateMoney = content.toString()
                //实际付款金额(不含税): 返推算->实际付款金额(含税)
                item.consignorRateMoney = getTurnMoney(item.consignorNoRateMoney, item.settleRate)
                etConfirmSettlementPriceIncludedTax.setText(item.consignorRateMoney)
                //计算外部价格
                computeAllMoneyBlock()
            }
        }
        orderSettlementBasis.text = item.settleBasisTypeName
        UtilRxView.afterTextChangeEvents(etConfirmSettlementPriceIncludedTax, 500) { inContent ->
            if (etConfirmSettlementPriceIncludedTax.isFocused) {
                //结算金额(含税价)
                item.consignorRateMoney = inContent.toString()
                //结算金额(含税价): 推算->承运方预估到手价(不含税价)
                val money1 = getMoneyRateText(inContent.toString(), item.settleRate, item.consignorNoRateMoney)
                item.consignorNoRateMoney = money1
                etConfirmSettlementPriceNotIncludedTax.setText(item.consignorNoRateMoney)
                //结算金额(含税价): 推算->承运方预估到手价单价
                val money2 = computeMoney2(item.consignorNoRateMoney, item.slipLoad)
                item.consignorNoRateUnitShowMoney = money2
                etConsignorNoRateUnitShowMoney.setText(item.consignorNoRateUnitShowMoney)
                //计算外部价格
                computeAllMoneyBlock()
            }
        }
        UtilRxView.afterTextChangeEvents(etConsignorNoRateUnitShowMoney, 500) { inContent ->
            if (etConsignorNoRateUnitShowMoney.isFocused) {
                //承运方预估到手价单价
                item.consignorNoRateUnitShowMoney = inContent.toString()
                //承运方预估到手价单价: 返推算->承运方预估到手价(不含税价)
                val money1 = computeMoney1(item.consignorNoRateUnitShowMoney, item.slipLoad)
                item.consignorNoRateMoney = money1
                etConfirmSettlementPriceNotIncludedTax.setText(item.consignorNoRateMoney)
                //承运方预估到手价(不含税价): 返推算->结算金额(含税价)
                val money2 = getTurnMoney(item.consignorNoRateMoney ?: "", item.settleRate, item.consignorRateMoney)
                item.consignorRateMoney = money2
                etConfirmSettlementPriceIncludedTax.setText(item.consignorRateMoney)
                //计算外部价格
                computeAllMoneyBlock()
            }
        }
        if (item.releaseUseFlag.isTrue) {
            //应用了亏涨吨
            etConfirmSettlementTonnage.isEnabled = false
            etConfirmSettlementPriceIncludedTax.isEnabled = false
            etConfirmSettlementPriceNotIncludedTax.isEnabled = false
        } else {
            etConfirmSettlementTonnage.isEnabled = item.updateSettleWeightFlag == "1"
            etConfirmSettlementPriceIncludedTax.isEnabled = consignorRateMoneyUpdateFlag.isTrue
            etConfirmSettlementPriceNotIncludedTax.isEnabled = true
        }
        etConfirmSettlementTonnage.isEnabled = true
        etConfirmSettlementPriceIncludedTax.isEnabled = true
        etConfirmSettlementPriceNotIncludedTax.isEnabled = true
        //结算吨位
        UtilRxView.afterTextChangeEvents(etConfirmSettlementTonnage, 500) { content ->
            if (etConfirmSettlementTonnage.isFocused) {
                item.slipLoad = content.toString()
                //单价直接丢给含税结算金额
                if (item.freightType.isTrue) {
                    item.let { it ->
                        if (it.pbConsignorUnitMoney.isNullOrEmpty()) {
                            etConfirmSettlementPriceIncludedTax.setText("")
                            item.consignorRateMoney = ""
                        } else {
                            val s1 = item.showContainerNum()
                            val s2 = it.pbConsignorUnitMoney?.toDoubleOrNull() ?: 0.0
                            var s3 = 0.0
                            //单价，竞价模式加保费
                            if (item.orderModel.isTrue) {
                                item.guaranteeFee?.let { s3 = it.toDouble() }
                                val toString = (NumUtil.mulEgnorNull(s1, s2) + s3).roundTo2DecimalPlaces().toString()
                                etConfirmSettlementPriceIncludedTax.setText(toString)
                                item.consignorRateMoney = toString
                            } else {
                                val toString = NumUtil.mulEgnorNull(s1, s2).roundTo2DecimalPlaces().toString()
                                etConfirmSettlementPriceIncludedTax.setText(toString)
                                item.consignorRateMoney = toString
                            }
                        }
                        //计算结算金额 承运方预估到手价
                        val money1 = getMoneyRateText(item.consignorRateMoney, item.settleRate, item.consignorNoRateMoney)
                        item.consignorNoRateMoney = money1
                        etConfirmSettlementPriceNotIncludedTax.setText(item.consignorNoRateMoney)
                        //结算金额(含税价): 推算->承运方预估到手价单价
                        val money2 = computeMoney2(item.consignorNoRateMoney, item.slipLoad)
                        item.consignorNoRateUnitShowMoney = money2
                        etConsignorNoRateUnitShowMoney.setText(item.consignorNoRateUnitShowMoney)
                    }
                }
            }
        }
        //运费计价方式(单价)
        UtilRxView.afterTextChangeEvents(orderSettleTypeUnitEditText, 500) { content ->
            if (orderSettleTypeUnitEditText.isFocused) {
                if (item.freightType.isTrue) {
                    item.pbConsignorUnitMoney = content.toString()
                    //运费计价方式（单价）
                    val m1 = item.showContainerNum()
                    val m2 = if (TextUtils.isEmpty(item.pbConsignorUnitMoney.toString())) {
                        0.00
                    } else {
                        item.pbConsignorUnitMoney?.toDoubleOrNull() ?: 0.00
                    }
                    //计算结算金额(含税)
                    val money = NumUtil.mulEgnorNull(m1, m2)
                    item.consignorRateMoney = money.toString().toDoubleRoundDownString(2)
                    etConfirmSettlementPriceIncludedTax.setText(money.roundTo2DecimalPlaces().toString())
                    //计算结算金额 承运方预估到手价
                    val money1 = getMoneyRateText(item.consignorRateMoney, item.settleRate, item.consignorNoRateMoney)
                    item.consignorNoRateMoney = money1
                    etConfirmSettlementPriceNotIncludedTax.setText(item.consignorNoRateMoney)
                    //结算金额(含税价): 推算->承运方预估到手价单价
                    val money2 = computeMoney2(item.consignorNoRateMoney, item.slipLoad)
                    item.consignorNoRateUnitShowMoney = money2
                    etConsignorNoRateUnitShowMoney.setText(item.consignorNoRateUnitShowMoney)
                }
            }
        }
        UtilTool.setEditTextInputSize(orderSettleTypeUnitEditText, 2)
        UtilTool.setEditTextInputSize(etConfirmSettlementTonnage, 4)

        var replace1 = item.receiveWeight ?: ""
        if (TextUtils.isEmpty(replace1)) {
            replace1 = "0.000"
        }
        var replace2 = item.deliverWeight ?: ""
        if (TextUtils.isEmpty(replace2)) {
            replace2 = "1.000"
        }

        val sub = NumUtil.sub(replace1.toDoubleOrNull() ?: 0.00, replace2.toDouble())
        val div = NumUtil.div(sub, replace2.toDoubleOrNull() ?: 0.00, 8)
        val mul = NumUtil.mul(div, 100.00)
        order_textview21.text = "${sub.roundToFourDecimals4Point()}${item.getCargoCategory()} | ${mul.roundToFourDecimals4Point()}%"
        //计算外部价格
        computeAllMoneyBlock()
    }

}
