package com.zczy.cargo_owner.order.qrcode

import com.zczy.comm.http.entity.BaseNewRequest
import com.zczy.comm.http.entity.BaseRsp
import com.zczy.comm.http.entity.ResultData

/**
 * PS:
 * <AUTHOR> by sdx on 2020/6/5.
 */
data class ReqQueryHugeOrderDetailQrcodeForApp(
        var hugeOrderId: String = ""
) : BaseNewRequest<BaseRsp<RspQueryHugeOrderDetailQrcodeForApp>>
("oms-app/consignor/qrCode/queryHugeOrderDetailQrcodeForApp")

data class RspQueryHugeOrderDetailQrcodeForApp(
        var qrCodeUrl: String = "",
        var hugeOrderInfo: GoodsItem? = null
) : ResultData()

data class GoodsItem(
//        var distance: String = "", //  运距
        var hugeOrderId: String = "", //   批量货订单ID
        var consignorState: String = "", //  运单状态 1 保存,2 发布中,3 发布/上线, 4 取消, 5 暂停/下线, 8 已终止/完成
        var orderFranType: String = "", //  货主发单类型：0 普通货主发单, 1 加盟商代发单, 2 加盟商关联的货主自己发单
        var publishUserType: String = "", //   发布用户类型：1 已认证货主，13 已认证加盟商
        var consignorUserId: String = "", //   货主用户ID
        var consignorUserName: String = "", //   货主用户名称
        var consignorCompany: String = "", //  货主公司名称
        var pbCarrierUnitMoney: String = "", // 成交承运方单价
        var pbCarrierMoney: String = "", // 成交承运方总价
        var consignorChildName: String = "", // 货主子账号名称
        var orderModel: String = "", // 订单类型：0 抢单,1 议价
        var freightType: String = "", // 费用类型：0 包车价,1 单价
        var invoiceFlag: String = "", // 是否需要发票 0否 1是
//        var pbConsignorMoney: String = "", // 货主总价
//        var pbConsignorUnitMoney: String = "", // 货主单价
        var allCargoName: String = "", // 货物名称
        var despatchCompanyName: String = "", // 发货单位名称	包含在orderInfo里，如果配置发货单位隐藏，返回空
        var deliverCompanyName: String = "", // 收货单位名称	包含在orderInfo里，如果配置受货单位隐藏，返回空
//        var title: String = "", // 运单标题
//        var receiptFlag: String = "", // 是否押回单：0 否,1 是
//        var createdTimeStr: String = "", // 订单发布时间
//        var sdOilCardFlag: String = "", //   是否山东油卡 0否 1是
//        var oilCardFlag: String = "", //   是否油卡 0否 1是
//        var selfComment: String = "", //  编辑标签
//        var despatchStartTime: String = "", //   发货开始时间
//        var despatchName: String = "", //   发货人姓名
        var despatchPro: String = "", //   启运地省
        var despatchCity: String = "", //    启运地市
        var despatchDis: String = "", // 启运地区
        var despatchPlace: String = "", //    启运地详细地址
//        var deliverName: String = "", //   收货人姓名
        var deliverPro: String = "", //   目的地省
        var deliverCity: String = "", //   目的地市
        var deliverDis: String = "", // 目的地区
        var deliverPlace: String = "", //    目的地详细地址
//        var matchState: String = "", //    撮合状态
        var remainWeight: String = "",  //   剩余重量
        var weight: String = "", // 发布重量
//        var specifyFlag: String = "",
//        var advanceFlag: String = "",
//        var runningCount: String = "", // 成交子单数
//        var expectNum: String = "", // 已推送报价
        var cargoCategory: String = "", // 货物类别：1：重货，2：泡货
//        var consignorSubsidiaryName: String = ""//业务主体
        var orderMark: String, //运单标识
) {
    fun formatStartAddress(): String {
        val sb = StringBuilder()
        sb.append(despatchPro)
        sb.append(despatchCity)
        sb.append(despatchDis)
        sb.append(despatchPlace)
        return sb.toString()
    }
    fun formatStartAddress2(): String {
        val sb = StringBuilder()
        sb.append(despatchCity)
        sb.append(despatchDis)
        return sb.toString()
    }
    fun formatEndAddress(): String {
        val sb = StringBuilder()
        sb.append(deliverPro)
        sb.append(deliverCity)
        sb.append(deliverDis)
        sb.append(deliverPlace)
        return sb.toString()
    }
    fun formatEndAddress2(): String {
        val sb = StringBuilder()
        sb.append(deliverCity)
        sb.append(deliverDis)
        return sb.toString()
    }
}

