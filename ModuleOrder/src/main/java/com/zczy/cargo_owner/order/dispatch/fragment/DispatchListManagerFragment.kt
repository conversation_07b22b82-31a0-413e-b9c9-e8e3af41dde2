package com.zczy.cargo_owner.order.dispatch.fragment

import android.os.Bundle
import android.util.Log
import android.view.View
import com.chad.library.adapter.base.BaseQuickAdapter
import com.chad.library.adapter.base.listener.OnItemClickListener
import com.sfh.lib.mvvm.annotation.LiveDataMatch
import com.sfh.lib.utils.UtilTool
import com.zczy.cargo_owner.order.R
import com.zczy.cargo_owner.order.dispatch.adapter.DispatchListManagerAdapter
import com.zczy.cargo_owner.order.dispatch.model.DispatchModel
import com.zczy.cargo_owner.order.dispatch.req.ReqQueryOrderJidongShipmentList
import com.zczy.cargo_owner.order.dispatch.req.ReqReaddOrderJidongShipment
import com.zczy.cargo_owner.order.dispatch.req.RspQueryOrderJidongShipmentList
import com.zczy.comm.http.entity.PageList
import com.zczy.comm.ui.BaseFragment
import com.zczy.comm.utils.dp2px
import com.zczy.comm.widget.pulltorefresh.CommEmptyView
import kotlinx.android.synthetic.main.dispatch_manager_list_fragment.*

/*=============================================================================================
 * 功能描述:派车单管理
 *---------------------------------------------------------------------------------------------
 *  涉及业务 & 更新说明:
 *
 *
 *--------------------------------------------------------------------------------------------
 *  <AUTHOR>  on  2022/3/30
 *  @Company    中储智运科技股份有限公司
 *  @Copyright （版权）中储南京智慧物流科技有限公司所有                                                                                        *
 *=============================================================================================*/

class DispatchListManagerFragment : BaseFragment<DispatchModel>() {

    private val containerType by lazy { arguments?.getInt(CONTAINER_TYPE) ?: 0 }
    private val mAdapter = DispatchListManagerAdapter()

    companion object {
        const val CONTAINER_TYPE = "container_type"
        const val CONTAINER_TYPE_V1 = "1"
        const val CONTAINER_TYPE_V2 = "2"

        @JvmStatic
        fun instanceFragment(containerType: String): DispatchListManagerFragment {
            val dispatchListManagerFragment = DispatchListManagerFragment()
            val bundle = Bundle()
            bundle.putString(CONTAINER_TYPE, containerType)
            dispatchListManagerFragment.arguments = bundle
            return dispatchListManagerFragment
        }
    }

    override fun getLayout(): Int {
        return R.layout.dispatch_manager_list_fragment
    }

    override fun initData() {
    }

    override fun bindView(view: View, bundle: Bundle?) {
        var type = arguments?.getString(CONTAINER_TYPE)
        refresh_layout.apply {
            addItemDecorationSize(dp2px(8f))
            setAdapter(mAdapter, true)
            setEmptyView(CommEmptyView.creatorDef(context))
            addOnItemListener(onItemClickListener)
            setOnLoadListener2 { nowPage ->
                val reqDeliverContainerList = ReqQueryOrderJidongShipmentList(
                        consignorUserId = "",
                        queryType = type,
                        nowPage = nowPage,
                        pageSize = 10
                )
                viewModel?.queryList(reqDeliverContainerList)
            }
            onAutoRefresh()
        }
    }

    private val onItemClickListener = object : OnItemClickListener() {
        override fun onSimpleItemClick(adapter: BaseQuickAdapter<*, *>, view: View, position: Int) {}

        override fun onItemChildClick(adapter: BaseQuickAdapter<*, *>, view: View, position: Int) {
            super.onItemChildClick(adapter, view, position)
            val item = adapter.getItem(position)
            if (item is RspQueryOrderJidongShipmentList) {
                when (view.id) {
                    R.id.tv_copy -> {
                        //复制order
                        UtilTool.setCopyText(context, "运单号", item.orderId)
                        showToast("复制成功")
                    }
                    R.id.tv_order -> {
                        //复制order
                        UtilTool.setCopyText(context, "运单号", item.orderId)
                        showToast("复制成功")
                    }
                    // 生成派车单
                    R.id.tv_one -> {
                        val readdOrderJidongShipment = ReqReaddOrderJidongShipment(
                                orderId = item.orderId
                        )
                        viewModel?.queryReaddOrderJidongShipment(readdOrderJidongShipment)
                    }
                    // 重新生成派车单
                    R.id.tv_two -> {
                        val readdOrderJidongShipment = ReqReaddOrderJidongShipment(
                                orderId = item.orderId
                        )
                        viewModel?.queryReaddOrderJidongShipment(readdOrderJidongShipment)
                    }
                }
            }
        }
    }

    @LiveDataMatch(tag = "派车单管理列表查询接口")
    open fun onQueryOrderJidongShipmentListSuccess(data: PageList<RspQueryOrderJidongShipmentList>?) {
        data?.apply {
            refresh_layout.onRefreshCompale(this)
        }
        if (data == null) {
            refresh_layout.onLoadMoreFail()
        }

    }

    @LiveDataMatch(tag = "生成派车单，重新生成派车单")
    open fun onqueryReaddOrderJidongShipmentSuccess(data: String?) {
        refresh_layout.onAutoRefresh()
        showToast(data ?: "")
    }

}