package com.zczy.cargo_owner.order.reminder.adapter

import com.chad.library.adapter.base.BaseQuickAdapter
import com.chad.library.adapter.base.BaseViewHolder
import com.zczy.cargo_owner.order.R
import com.zczy.cargo_owner.order.reminder.req.RemindItem
import com.zczy.cargo_owner.order.reminder.widget.TransportReminderGridView

/**
 *  desc: 运输提醒类型
 *  user: 宋双朋
 *  time: 2024/11/6 17:29
 */

class TransportReminderHomeItemAdapter : BaseQuickAdapter<RemindItem, BaseViewHolder>(R.layout.transport_home_item) {
    override fun convert(helper: BaseViewHolder?, item: RemindItem) {
        helper?.apply {
            val viewItem = getView<TransportReminderGridView>(R.id.transportReminderGridView)
            viewItem.setViewTypeData(item.title)
            viewItem.setViewCountData(item.size ?: "")
        }
    }
}