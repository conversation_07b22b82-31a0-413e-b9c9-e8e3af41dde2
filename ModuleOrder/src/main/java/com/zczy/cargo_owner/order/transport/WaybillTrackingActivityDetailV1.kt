package com.zczy.cargo_owner.order.transport

import android.annotation.SuppressLint
import android.app.Activity
import android.content.Intent
import android.os.Bundle
import android.text.TextUtils
import android.view.View
import com.zczy.cargo_owner.order.R
import com.zczy.cargo_owner.order.transport.model.TransportModel
import com.zczy.cargo_owner.order.transport.req.OrderInfoBean
import com.zczy.comm.ui.BaseActivity
import com.zczy.comm.utils.ex.isTrue
import com.zczy.comm.utils.ex.setVisible
import com.zczy.comm.utils.ex.toCommaString
import kotlinx.android.synthetic.main.waybill_tracking_detail_activity_v1.driverInfo
import kotlinx.android.synthetic.main.waybill_tracking_detail_activity_v1.etTime
import kotlinx.android.synthetic.main.waybill_tracking_detail_activity_v1.ivCheck
import kotlinx.android.synthetic.main.waybill_tracking_detail_activity_v1.ivCheckV1
import kotlinx.android.synthetic.main.waybill_tracking_detail_activity_v1.ivCheck_2
import kotlinx.android.synthetic.main.waybill_tracking_detail_activity_v1.llTime
import kotlinx.android.synthetic.main.waybill_tracking_detail_activity_v1.tv6
import kotlinx.android.synthetic.main.waybill_tracking_detail_activity_v1.tvEnd
import kotlinx.android.synthetic.main.waybill_tracking_detail_activity_v1.tvMacType1
import kotlinx.android.synthetic.main.waybill_tracking_detail_activity_v1.tvMacType2
import kotlinx.android.synthetic.main.waybill_tracking_detail_activity_v1.tvOrderId
import kotlinx.android.synthetic.main.waybill_tracking_detail_activity_v1.tvPlatformTracks
import kotlinx.android.synthetic.main.waybill_tracking_detail_activity_v1.tvPlatformTracksTime
import kotlinx.android.synthetic.main.waybill_tracking_detail_activity_v1.tvQueryData
import kotlinx.android.synthetic.main.waybill_tracking_detail_activity_v1.tvSelfTime
import kotlinx.android.synthetic.main.waybill_tracking_detail_activity_v1.tvShowNum
import kotlinx.android.synthetic.main.waybill_tracking_detail_activity_v1.tvStart
import kotlinx.android.synthetic.main.waybill_tracking_detail_activity_v1.tvTime1
import kotlinx.android.synthetic.main.waybill_tracking_detail_activity_v1.tvTime2
import kotlinx.android.synthetic.main.waybill_tracking_detail_activity_v1.tvTime3
import kotlinx.android.synthetic.main.waybill_tracking_detail_activity_v1.tvTime4
import kotlinx.android.synthetic.main.waybill_tracking_detail_activity_v1.tvTitle

/**
 *    author : ssp
 *    e-mail : <EMAIL>
 *    date   : 2021/7/2017:07
 *    desc   :
 */
class WaybillTrackingActivityDetailV1 : BaseActivity<TransportModel>() {

    private var selectTime = ""
    private var orderId = ""
    private var stopLength = 0
    private val backMapTime by lazy { intent.getStringExtra(BACK_MAP_TIME) ?: "" }
    private val haveShowBackUpLocation by lazy { intent.getStringExtra(HAVE_SHOW_BACK_UP_LOCATION) ?: "" }

    companion object {

        const val REQUEST_CODE = 0x15
        const val REQUEST_ORDER_INFO = "request_order_info"
        const val REQUEST_ORDER_TYPE_DATA = "request_order_type_data"
        const val REQUEST_ORDER_TIME_DATA = "request_order_time_data"
        private const val BACK_MAP_TIME = "backMapTime"
        private const val HAVE_SHOW_BACK_UP_LOCATION = "haveShowBackUpLocation"
        private const val CHECK_TYPE = "checkType"
        private const val CHECK_TYPE2 = "checkType2"

        @JvmStatic
        fun startUI(activity: Activity, requestCode: Int, orderInfoBean: OrderInfoBean?, backMapTime: String?, haveShowBackUpLocation: String?) {
            val intent = Intent(activity, WaybillTrackingActivityDetailV1::class.java)
            intent.putExtra(REQUEST_ORDER_INFO, orderInfoBean)
            intent.putExtra(BACK_MAP_TIME, backMapTime)
            intent.putExtra(HAVE_SHOW_BACK_UP_LOCATION, haveShowBackUpLocation)
            activity.startActivityForResult(intent, requestCode)
        }

        @JvmStatic
        fun obtainTime(intent: Intent): String? {
            return intent.getStringExtra(REQUEST_ORDER_TIME_DATA)
        }

        @JvmStatic
        fun obtainMacType(intent: Intent): String? {
            return intent.getStringExtra(REQUEST_ORDER_TYPE_DATA)
        }

        @JvmStatic
        fun obtainCheckType(intent: Intent): Boolean {
            return intent.getBooleanExtra(CHECK_TYPE, false)
        }

        @JvmStatic
        fun obtainCheckType2(intent: Intent): Boolean {
            return intent.getBooleanExtra(CHECK_TYPE2, false)
        }
    }

    override fun initData() {
    }

    override fun getLayout(): Int {
        return R.layout.waybill_tracking_detail_activity_v1
    }

    @SuppressLint("SetTextI18n")
    override fun bindView(bundle: Bundle?) {
        val orderInfoBean = intent.getParcelableExtra<OrderInfoBean>(REQUEST_ORDER_INFO) ?: null
        orderId = orderInfoBean?.orderId ?: ""
        orderInfoBean?.let { it ->
            ("运单号  " + it.orderId).also { tvOrderId.text = it }
            tvStart.text = it.startAddressDetail
            tvEnd.text = it.endAddressDetail
            (it.carrierName + "  " + it.carrierPhone).also { driverInfo.text = it }
            tvTitle.text = it.nodeName
        }
        ivCheckV1.setVisible(haveShowBackUpLocation.isTrue)
        tvPlatformTracks.setVisible(haveShowBackUpLocation.isTrue)
        tvPlatformTracksTime.text = "轨迹时间   $backMapTime"
        bindClickEvent(tvMacType1)
        bindClickEvent(tvMacType2)
        tvMacType1.isSelected = true
        tvMacType2.isSelected = false
        bindClickEvent(tvTime1)
        bindClickEvent(tvTime2)
        bindClickEvent(tvTime3)
        bindClickEvent(tvTime4)
        bindClickEvent(ivCheck)
        bindClickEvent(ivCheck_2)
        bindClickEvent(ivCheckV1)
        bindClickEvent(tvQueryData)
        bindClickEvent(tvShowNum)
    }

    override fun onSingleClick(v: View) {
        super.onSingleClick(v)
        when (v.id) {
            R.id.tvMacType1 -> {
                tvMacType1.isSelected = !tvMacType1.isSelected
            }

            R.id.tvMacType2 -> {
                tvMacType2.isSelected = !tvMacType2.isSelected
            }

            R.id.tvTime1 -> {
                tvTime1.isSelected = true
                tvTime2.isSelected = false
                tvTime3.isSelected = false
                tvTime4.isSelected = false
                etTime.setVisible(false)
                tvSelfTime.setVisible(false)
            }

            R.id.tvTime2 -> {
                tvTime1.isSelected = false
                tvTime2.isSelected = true
                tvTime3.isSelected = false
                tvTime4.isSelected = false
                etTime.setVisible(false)
                tvSelfTime.setVisible(false)
            }

            R.id.tvTime3 -> {
                tvTime1.isSelected = false
                tvTime2.isSelected = false
                tvTime3.isSelected = true
                tvTime4.isSelected = false
                etTime.setVisible(false)
                tvSelfTime.setVisible(false)
            }

            R.id.tvTime4 -> {
                tvTime1.isSelected = false
                tvTime2.isSelected = false
                tvTime3.isSelected = false
                tvTime4.isSelected = true
                //展示自定义时间输入框
                etTime.setVisible(true)
                tvSelfTime.setVisible(true)
            }

            R.id.ivCheck -> {
                ivCheck.isSelected = !ivCheck.isSelected
                if (ivCheck.isSelected) {
                    //展示停留时间
                    tv6.setVisible(true)
                    llTime.setVisible(true)
                    etTime.setVisible(false)
                    tvSelfTime.setVisible(false)
                    tvTime1.isSelected = true
                    tvTime2.isSelected = false
                    tvTime3.isSelected = false
                    tvTime4.isSelected = false
                } else {
                    //不展示停留时长
                    tv6.setVisible(false)
                    llTime.setVisible(false)
                    etTime.setVisible(false)
                    tvSelfTime.setVisible(false)
                    tvTime1.isSelected = false
                    tvTime2.isSelected = false
                    tvTime3.isSelected = false
                    tvTime4.isSelected = false
                }
            }

            R.id.ivCheck_2 -> {
                ivCheck_2.isSelected = !ivCheck_2.isSelected
                tv6.setVisible(false)
                llTime.setVisible(false)
                etTime.setVisible(false)
                tvSelfTime.setVisible(false)
                tvTime1.isSelected = false
                tvTime2.isSelected = false
                tvTime3.isSelected = false
                tvTime4.isSelected = false
            }

            R.id.ivCheckV1 -> {
                ivCheckV1.isSelected = !ivCheckV1.isSelected
            }

            R.id.tvQueryData -> {
                //查询停留信息
                val list = mutableListOf<String>()
                if (tvMacType1.isSelected) {
                    //车辆定位(车载北斗、车联网、T-BOX、中物联)
                    list.add("3")
                    list.add("1")
                    list.add("4")
                    list.add("11")
                }
                if (tvMacType2.isSelected) {
                    //手机定位
                    list.add("0,2")
                }
                if (list.isEmpty()) {
                    showDialogToast("请选择轨迹定位设备!")
                    return
                }
                intent.putExtra(REQUEST_ORDER_TYPE_DATA, list.toCommaString { it })
                if (ivCheck.isSelected) {
                    //选择展示停留时长
                    val time = if (tvTime4.isSelected) {
                        //选择自定义时长
                        val trimTime = etTime.text.toString().trim()
                        if (TextUtils.isEmpty(trimTime)) {
                            //没有输入时间
                            showDialogToast("停留时间不能为空!")
                            return
                        }
                        if (trimTime.toDouble() < 20) {
                            showDialogToast("停留时间不能小于20分钟!")
                            return
                        }
                        trimTime
                    } else if (tvTime2.isSelected) {
                        "40"
                    } else if (tvTime3.isSelected) {
                        "60"
                    } else {
                        "20"
                    }
                    intent.putExtra(REQUEST_ORDER_TIME_DATA, time)
                }
                intent.putExtra(CHECK_TYPE, ivCheckV1.isSelected)
                intent.putExtra(CHECK_TYPE2, ivCheck_2.isSelected)
                setResult(Activity.RESULT_OK, intent)
                finish()
            }
        }
    }

    /**
     * ZCZY-8526 停留未行驶异常判定与展示,后经讨论需求取消，所以暂时注释掉
     */
//    @LiveDataMatch(tag = "查询偏移点 停留点")
//    open fun onQueryTrajectoryStopLocationSuccess(data: RspQueryTrajectoryStopLocation?) {
//        data?.apply {
//            val length = trajectoryStopList.size
//            stopLength = length
//            tvShowNum.visibility = View.VISIBLE
//            tvShowNum.text = Html.fromHtml("停留次数<font color='#5086FC'>$length</font>")
//        }
//    }
}