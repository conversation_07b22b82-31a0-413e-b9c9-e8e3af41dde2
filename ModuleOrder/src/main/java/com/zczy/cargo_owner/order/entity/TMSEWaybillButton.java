package com.zczy.cargo_owner.order.entity;

/**
 * 功能描述:运单按钮
 *
 * <AUTHOR> 孙飞虎
 * @company 中储智运科技股份有限公司
 * @copyright （版权）中储南京智慧物流科技有限公司所有
 * @date 2019/1/2
 */
public class TMSEWaybillButton {

    /***取消发布*/
    boolean cancelRelease;
    /***修改运价	*/
    boolean modifyFreightRate;
    /***处理变更*/
    boolean handleChange;
    /***变更信息*/
    boolean canChange;
    /***签订协议*/
    boolean signAgreement;
    /***确认收货*/
    boolean receiveGoods;
    /***支付运费*/
    boolean payFee;
    /***再来一单*/
    boolean carryAgain;
    /***申请开票*/
    boolean applyInvoice;
    /***违约申请*/
    boolean breachApply;
    /*** 处理违约*/
    boolean handleBreach;
    boolean estimatedTimeButton;//预估运输时间

    public boolean isEstimatedTimeButton() {
        return estimatedTimeButton;
    }

    public boolean isCancelRelease() {
        return cancelRelease;
    }

    public boolean isModifyFreightRate() {
        return modifyFreightRate;
    }

    public boolean isHandleChange() {
        return handleChange;
    }

    public boolean isCanChange() {
        return canChange;
    }

    public boolean isSignAgreement() {
        return signAgreement;
    }

    public boolean isReceiveGoods() {
        return receiveGoods;
    }

    public boolean isPayFee() {
        return payFee;
    }

    public boolean isCarryAgain() {
        return carryAgain;
    }

    public boolean isApplyInvoice() {
        return applyInvoice;
    }

    public boolean isBreachApply() {
        return breachApply;
    }

    public boolean isHandleBreach() {
        return handleBreach;
    }
}
