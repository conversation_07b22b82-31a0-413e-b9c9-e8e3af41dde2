package com.zczy.cargo_owner.order.settlement.view

import android.annotation.SuppressLint
import android.content.Context
import android.graphics.Color
import android.util.AttributeSet
import android.view.LayoutInflater
import android.widget.LinearLayout
import com.zczy.cargo_owner.order.R
import com.zczy.cargo_owner.order.confirm.bean.isNotNullOrNotEmpty
import com.zczy.cargo_owner.order.settlement.bean.WatermarkPic
import com.zczy.comm.data.entity.EImage
import com.zczy.comm.utils.ex.setVisible
import kotlinx.android.synthetic.main.order_settlement_image_view_v1.view.imgs_bill
import kotlinx.android.synthetic.main.order_settlement_image_view_v1.view.isvReturnImage
import kotlinx.android.synthetic.main.order_settlement_image_view_v1.view.isvReturnImageRl
import kotlinx.android.synthetic.main.order_settlement_image_view_v1.view.isvReturnImageRlV1
import kotlinx.android.synthetic.main.order_settlement_image_view_v1.view.isvReturnImageV1
import kotlinx.android.synthetic.main.order_settlement_image_view_v1.view.ivReceiptDocumentPhotoSwitch
import kotlinx.android.synthetic.main.order_settlement_image_view_v1.view.ll_title_2
import kotlinx.android.synthetic.main.order_settlement_image_view_v1.view.orderReturnAiDocuments
import kotlinx.android.synthetic.main.order_settlement_image_view_v1.view.rl_bill
import kotlinx.android.synthetic.main.order_settlement_image_view_v1.view.tvReceiptDocumentPhoto
import kotlinx.android.synthetic.main.order_settlement_image_view_v1.view.tvReceiptDocumentPhotoV1
import kotlinx.android.synthetic.main.order_settlement_image_view_v1.view.tv_1
import kotlinx.android.synthetic.main.order_settlement_image_view_v1.view.tv_2
import kotlinx.android.synthetic.main.order_settlement_image_view_v1.view.tv_3
import kotlinx.android.synthetic.main.order_settlement_image_view_v1.view.tv_4
import kotlinx.android.synthetic.main.order_settlement_image_view_v1.view.viewAiOrder

/**
 * <AUTHOR> by sdx on 2020/9/1.
 */
class SettlementImageViewV1 : LinearLayout {
    private var mList1: List<WatermarkPic>? = null
    private var mList2: List<WatermarkPic>? = null

    constructor(context: Context) : super(context)

    constructor(context: Context, attrs: AttributeSet?) : super(context, attrs)

    constructor(context: Context, attrs: AttributeSet?, defStyleAttr: Int) : super(
        context,
        attrs,
        defStyleAttr
    )

    init {
        orientation = VERTICAL
        setBackgroundColor(Color.WHITE)
        LayoutInflater.from(context).inflate(R.layout.order_settlement_image_view_v1, this)
        ivReceiptDocumentPhotoSwitch.setOnClickListener {
            switchPic()
        }
    }

    fun setTitle(title: String, title2: String = "") {
        tv_1.text = title
        tv_2.text = title2
    }

    fun hideTitle2() {
        ll_title_2.setVisible(false)
    }

    @SuppressLint("SetTextI18n")
    fun setData(
        list1: List<WatermarkPic>?,
        list2: List<WatermarkPic>?,
        num: String? = "",
        unit: String? = "",
        deliverReceiptNumber: String? = null,
    ) {
        mList1 = list1
        mList2 = list2
        viewAiOrder.setVisible(deliverReceiptNumber.isNotNullOrNotEmpty())
        if (deliverReceiptNumber.isNotNullOrNotEmpty()) {
            orderReturnAiDocuments.text = "发货单据号：${deliverReceiptNumber}"
        }
        val filter1 = mList1?.filter { !it.picOrgUrl.isNullOrEmpty() }
        val filter2 = mList2?.filter { !it.picOrgUrl.isNullOrEmpty() }
        if (mList1.isNullOrEmpty() && mList2.isNullOrEmpty()) {
            //都无原图
            ivReceiptDocumentPhotoSwitch.setVisible(false)
        } else if (filter1.isNullOrEmpty() && filter2.isNullOrEmpty()) {
            //都无原图
            ivReceiptDocumentPhotoSwitch.setVisible(false)
        } else {
            ivReceiptDocumentPhotoSwitch.setVisible(true)
        }

        tv_3.text = num ?: ""
        val map = list1?.map {
            EImage(netUrl = it.picUrl ?: "")
        } ?: emptyList()
        val arrayListOf = arrayListOf(*map.toTypedArray())
        if (arrayListOf.size <= 0) {
            isvReturnImage.setVisible(false)
            isvReturnImageRl.setVisible(false)
            tvReceiptDocumentPhoto.setVisible(false)
        } else {
            isvReturnImage.setVisible(true)
            isvReturnImageRl.setVisible(true)
            tvReceiptDocumentPhoto.setVisible(true)
        }
        isvReturnImage.imgList = arrayListOf

        val map2 = list2?.map {
            EImage(netUrl = it.picUrl ?: "")
        } ?: emptyList()
        val arrayListOf2 = arrayListOf(*map2.toTypedArray())
        if (arrayListOf2.size <= 0) {
            tvReceiptDocumentPhotoV1.setVisible(false)
            isvReturnImageV1.setVisible(false)
            isvReturnImageRlV1.setVisible(false)
        } else {
            tvReceiptDocumentPhotoV1.setVisible(true)
            isvReturnImageV1.setVisible(true)
            isvReturnImageRlV1.setVisible(true)
        }
        isvReturnImageV1.imgList = arrayListOf2

        //  ：重货，2：泡货
        when (unit) {
            "1" -> {
                tv_4.text = "吨"
            }

            "2" -> {
                tv_4.text = "方"
            }

            "3" -> {
                tv_4.text = "箱"
            }
        }
    }

    private fun switchPic() {
        when (ivReceiptDocumentPhotoSwitch.text) {
            "切换原图" -> {
                //带水印图片 切换成原图
                mList1?.filter { !it.picOrgUrl.isNullOrEmpty() }?.map { EImage(imageId = it.picOrgUrl ?: "") }?.let { map ->
                    isvReturnImage.imgList = arrayListOf(*map.toTypedArray())
                }
                mList2?.filter { !it.picOrgUrl.isNullOrEmpty() }?.map { EImage(imageId = it.picOrgUrl ?: "") }?.let { map ->
                    isvReturnImageV1.imgList = arrayListOf(*map.toTypedArray())
                }
                ivReceiptDocumentPhotoSwitch.text = "切换默认"
            }

            "切换默认" -> {
                //原图 切换成带水印图片
                mList1?.map { EImage(imageId = it.picUrl ?: "") }?.let { map ->
                    isvReturnImage.imgList = arrayListOf(*map.toTypedArray())
                }
                mList2?.map { EImage(imageId = it.picUrl ?: "") }?.let { map ->
                    isvReturnImageV1.imgList = arrayListOf(*map.toTypedArray())
                }
                ivReceiptDocumentPhotoSwitch.text = "切换原图"
            }
        }
    }

    //设置卸货拍照图片
    fun setBillWate(list: List<WatermarkPic>?) {

        val map = list?.map {
            EImage(netUrl = it.picUrl ?: "")
        } ?: emptyList()
        val arrayListOf = arrayListOf(*map.toTypedArray())
        if (arrayListOf.size <= 0) {
            rl_bill.setVisible(false)
        } else {
            rl_bill.setVisible(true)
        }
        imgs_bill.imgList = arrayListOf
    }
}