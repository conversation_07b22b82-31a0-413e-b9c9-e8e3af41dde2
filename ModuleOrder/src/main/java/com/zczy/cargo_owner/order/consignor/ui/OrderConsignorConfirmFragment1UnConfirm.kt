package com.zczy.cargo_owner.order.consignor.ui

import android.app.Activity
import android.content.Context
import android.content.Intent
import android.os.Bundle
import android.view.View
import com.chad.library.adapter.base.BaseQuickAdapter
import com.chad.library.adapter.base.listener.OnItemClickListener
import com.sfh.lib.mvvm.annotation.LiveDataMatch
import com.zczy.cargo_owner.libcomm.config.HttpConfig
import com.zczy.cargo_owner.libcomm.config.SubUserAuthUtils
import com.zczy.cargo_owner.libcomm.utils.getChildPermission
import com.zczy.cargo_owner.order.R
import com.zczy.cargo_owner.order.consignor.adapter.OrderConsignorConfirmAdapter1UnConfirm
import com.zczy.cargo_owner.order.consignor.model.OrderConsignorConfirmFragmentModel
import com.zczy.cargo_owner.order.consignor.req.Rsp1QueryConsignorOrderStateOfMobile
import com.zczy.cargo_owner.order.consignor.req.RspQueryDeliverOrderPicForMobile
import com.zczy.cargo_owner.order.consignor.widget.OrderConsignorUnConfirmSortView
import com.zczy.cargo_owner.order.detail.WaybillDetailStatueActivity
import com.zczy.comm.data.entity.EImage
import com.zczy.comm.http.entity.PageList
import com.zczy.comm.ui.BaseFragment
import com.zczy.comm.utils.CommUtils
import com.zczy.comm.utils.dp2px
import com.zczy.comm.utils.ex.setVisible
import com.zczy.comm.utils.imageselector.ImagePreviewActivity
import com.zczy.comm.widget.pulltorefresh.CommEmptyView
import com.zczy.comm.widget.pulltorefresh.OnLoadingListener
import kotlinx.android.synthetic.main.order_consignor_confirm_fragment_1_unconfirm.img_all_choose
import kotlinx.android.synthetic.main.order_consignor_confirm_fragment_1_unconfirm.swipe_refresh_more
import kotlinx.android.synthetic.main.order_consignor_confirm_fragment_1_unconfirm.tv_all_choose_num
import kotlinx.android.synthetic.main.order_consignor_confirm_fragment_1_unconfirm.tv_confirm
import kotlinx.android.synthetic.main.order_consignor_confirm_fragment_1_unconfirm.view_bottom

/** 功能描述:
 * 发货单确认 未确认界面
 * <AUTHOR>
 * @company 中储智运科技股份有限公司
 * @copyright （版权）中储南京智慧物流科技有限公司所有
 * @date 2019/4/3
 */
open class OrderConsignorConfirmFragment1UnConfirm : BaseFragment<OrderConsignorConfirmFragmentModel>() {

    private val mAdapter = OrderConsignorConfirmAdapter1UnConfirm()

    private val mSelectData = mutableListOf<Rsp1QueryConsignorOrderStateOfMobile>()

    private var mSort = "0"

    companion object {

        private const val REQUEST_REJECT = 0x33
        private const val REQUEST_COMMIT = 0x32

        @JvmStatic
        fun newInstance(context: Context): OrderConsignorConfirmFragment1UnConfirm {
            val args = Bundle()
            return androidx.fragment.app.Fragment.instantiate(context, OrderConsignorConfirmFragment1UnConfirm::class.java.name, args)
                    as OrderConsignorConfirmFragment1UnConfirm
        }
    }

    override fun getLayout(): Int = R.layout.order_consignor_confirm_fragment_1_unconfirm

    override fun bindView(view: View, bundle: Bundle?) {
        val emptyView = CommEmptyView.creatorDef(context)
        swipe_refresh_more.apply {
            mAdapter.selectData = mSelectData
            setAdapter(mAdapter, true)
            setEmptyView(emptyView)
            addItemDecorationSize(dp2px(7f))
            addOnItemListener(onItemClickListener)
            setOnLoadListener(object : OnLoadingListener {
                override fun onRefreshUI(nowPage: Int) {
                    viewModel?.getListInfo("0", nowPage, mSort)
                }

                override fun onLoadMoreUI(nowPage: Int) {
                    viewModel?.getListInfo("0", nowPage, mSort)
                }
            })
        }
        bindClickEvent(img_all_choose)
        bindClickEvent(tv_all_choose_num)
        bindClickEvent(tv_confirm)
    }

    override fun initData() {
        swipe_refresh_more.onAutoRefresh()
    }

    override fun onSingleClick(v: View) {
        super.onSingleClick(v)
        when (v.id) {
            R.id.img_all_choose,
            R.id.tv_all_choose_num -> {
                mSelectData.clear()
                if (!img_all_choose.isSelected) {
                    mSelectData.addAll(mAdapter.data)
                }
                mAdapter.notifyDataSetChanged()
                img_all_choose.isSelected = mSelectData.size == mAdapter.data.size
            }

            R.id.tv_confirm -> {
                if (mSelectData.isEmpty()) {
                    showDialogToast("请选择待确认的发货单")
                    return
                }
                OrderConsignorConfirmPreviewActivity.start(
                    this@OrderConsignorConfirmFragment1UnConfirm,
                    mSelectData,
                    REQUEST_COMMIT
                )
            }
        }
    }

    override fun onActivityResult(requestCode: Int, resultCode: Int, data: Intent?) {
        super.onActivityResult(requestCode, resultCode, data)
        if (requestCode == Activity.RESULT_OK) {
            when (resultCode) {
                REQUEST_REJECT,
                REQUEST_COMMIT -> {
                    refresh()
                }
            }
        }
    }

    override fun refresh() {
        if (!isFirstLoad) {
            initData()
        }
    }

    private val onItemClickListener = object : OnItemClickListener() {
        override fun onSimpleItemClick(adapter: BaseQuickAdapter<*, *>, view: View, position: Int) {
            val item = adapter.getItem(position)
            if (item is Rsp1QueryConsignorOrderStateOfMobile) {
                if (!mSelectData.remove(item)) {
                    mSelectData.add(item)
                }
                adapter.notifyDataSetChanged()
                tv_confirm.text = "确认发货单(${mSelectData.size})"

                img_all_choose.isSelected = mSelectData.size == adapter.data.size
            }
        }

        override fun onItemChildClick(adapter: BaseQuickAdapter<*, *>, view: View, position: Int) {
            super.onItemChildClick(adapter, view, position)
            val item = adapter.getItem(position)
            if (item is Rsp1QueryConsignorOrderStateOfMobile) {
                when (view.id) {
                    // 复制
                    R.id.tv_order_copy -> {
                        val isCopy = CommUtils.copyText(context, "运单号", item.orderId)
                        showToast(if (isCopy) "复制成功" else "复制失败")
                    }
                    // 确认
                    R.id.tv_confirm -> {
                        OrderConsignorConfirmPreviewActivity.start(
                            this@OrderConsignorConfirmFragment1UnConfirm,
                            listOf(item),
                            REQUEST_COMMIT
                        )
                    }
                    // 打回
                    R.id.tv_reject -> {
                        OrderConsignorConfirmRejectActivity.start(
                            this@OrderConsignorConfirmFragment1UnConfirm,
                            item,
                            REQUEST_REJECT
                        )
                    }
                    // 运单号
                    R.id.tv_order_id -> {
                        WaybillDetailStatueActivity.start(context, item.orderId)
                    }

                    R.id.tv_look -> {
                        viewModel?.getPicture(item.orderId)
                    }
                }
            }
        }
    }

    @LiveDataMatch
    open fun onGetListInfo(data: PageList<Rsp1QueryConsignorOrderStateOfMobile>?) {
        swipe_refresh_more.onRefreshCompale(data)
        // 子账号权限
        val hasPermission = SubUserAuthUtils.get().sgoSendGoodOk.getChildPermission().isEmpty()
        if (mAdapter.data.isNotEmpty() && hasPermission) {
            val c = context
            if (c != null && mAdapter.headerLayoutCount == 0) {
                val headerView = OrderConsignorUnConfirmSortView(c)
                headerView.setSort(mSort)
                headerView.setListener {
                    if (mSort != it) {
                        mSort = it
                        swipe_refresh_more.isRefreshing = true
                    }
                }
                mAdapter.addHeaderView(headerView)
            }
            view_bottom.setVisible(true)
            img_all_choose.isSelected = mSelectData.size == mAdapter.data.size
        } else {
            view_bottom.setVisible(false)
        }
    }

    @LiveDataMatch
    open fun onGetPicture(data: PageList<RspQueryDeliverOrderPicForMobile>?) {
        val itemList : MutableList<RspQueryDeliverOrderPicForMobile>? = data?.rootArray
        if(itemList != null && itemList.size>0){
            val list: MutableList<EImage> = ArrayList<EImage>(itemList.size)
            for (RspQueryDeliverOrderPicForMobile in itemList) {
                val image = EImage()
                image.netUrl = HttpConfig.getUrlImage(RspQueryDeliverOrderPicForMobile.imageUrl)
                list.add(image)
            }
            ImagePreviewActivity.start(this, list, 0)
        }
    }
}