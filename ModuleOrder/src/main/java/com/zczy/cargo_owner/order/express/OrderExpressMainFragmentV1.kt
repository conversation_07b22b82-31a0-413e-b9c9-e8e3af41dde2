package com.zczy.cargo_owner.order.express

import android.annotation.SuppressLint
import android.app.Activity.RESULT_OK
import android.content.Intent
import android.os.Bundle
import android.text.TextUtils
import android.view.Gravity
import android.view.KeyEvent
import android.view.View
import android.view.inputmethod.EditorInfo
import android.widget.TextView
import com.chad.library.adapter.base.BaseQuickAdapter
import com.chad.library.adapter.base.listener.OnItemClickListener
import com.huawei.hms.hmsscankit.ScanUtil
import com.huawei.hms.ml.scan.HmsScan
import com.jakewharton.rxbinding2.widget.RxTextView
import com.sfh.lib.event.RxBusEvent
import com.sfh.lib.mvvm.annotation.LiveDataMatch
import com.sfh.lib.ui.dialog.DialogBuilder
import com.zczy.cargo_owner.order.R
import com.zczy.cargo_owner.order.detail.WaybillDetailActivity
import com.zczy.cargo_owner.order.express.model.LTPageList
import com.zczy.cargo_owner.order.express.model.OrderExpressMainModel
import com.zczy.cargo_owner.order.express.req.RespEnsureExpressSign
import com.zczy.cargo_owner.order.express.req.RspCarrierExpressData
import com.zczy.cargo_owner.order.express.req.RxBusDoSign
import com.zczy.cargo_owner.order.express.req.isClickItem
import com.zczy.comm.CommServer
import com.zczy.comm.scan.RxCodeResult
import com.zczy.comm.ui.BaseFragment
import com.zczy.comm.utils.dp2px
import com.zczy.comm.utils.ex.isNull
import com.zczy.comm.utils.json.toJson
import com.zczy.comm.widget.pulltorefresh.CommEmptyView
import com.zczy.comm.widget.pulltorefresh.OnLoadingListener
import kotlinx.android.synthetic.main.order_express_main_fragment_v1.*
import com.zczy.cargo_owner.order.express.adapter.OrderExpressMainAdapterV1 as OrderExpressMainAdapter1

class OrderExpressMainFragmentV1 : BaseFragment<OrderExpressMainModel>() {

    var selectList: ArrayList<RspCarrierExpressData> = ArrayList()
    var signList: ArrayList<String> = ArrayList()
    var dataList: LTPageList<RspCarrierExpressData> = LTPageList()
    var selectStatus: Boolean = false
    private var orderIds: String? = null
    private var selfRefresh: Boolean = false
    private var isRefresh: Boolean = false
    private val mOrderExpressMainAdapter1 = OrderExpressMainAdapter1()

    companion object {

        const val ORDER_IDS = "order_ids"

        @JvmStatic
        fun instanceFragment(orderIds: String): OrderExpressMainFragmentV1 {
            val deliverContainerListFragment = OrderExpressMainFragmentV1()
            val bundle = Bundle()
            bundle.putString(ORDER_IDS, orderIds)
            deliverContainerListFragment.arguments = bundle
            return deliverContainerListFragment
        }
    }

    override fun getLayout(): Int = R.layout.order_express_main_fragment_v1

    override fun bindView(view: View, bundle: Bundle?) {
        initList()
        initSearch()
    }

    @SuppressLint("NotifyDataSetChanged")
    override fun initData() {
        orderIds = arguments?.getString(ORDER_IDS)
        swipe_refresh.onAutoRefresh()

        ll_select_status.setOnClickListener {
            val rootArrayData =
                swipe_refresh.pullToRefreshAdapter.data as ArrayList<RspCarrierExpressData>

            if (!selectStatus) {
                selectStatus = true

                iv_select_status.setBackgroundResource(R.drawable.qs_select)
                tv_select_status.text = "全不选"

                selectList.clear()

                for (rspCarrierExpressData in rootArrayData) {
                    if (rspCarrierExpressData.selectFlag != "0") {
                        rspCarrierExpressData.selectFlag = "1"
                        selectList.add(rspCarrierExpressData)
                    }
                }
            } else {
                selectStatus = false

                iv_select_status.setBackgroundResource(R.drawable.qs_unselect)
                tv_select_status.text = "全选"

                for (rspCarrierExpressData in rootArrayData) {
                    if (rspCarrierExpressData.selectFlag != "0") {
                        rspCarrierExpressData.selectFlag = "2"
                    }
                }
                selectList.clear()
            }

            ("已选" + selectList.size + "单").also { tv_have_select.text = it }

            swipe_refresh.recyclerView.adapter?.notifyDataSetChanged()
        }

        ("已选" + selectList.size + "单").also { tv_have_select.text = it }

        if (selectList.size == 0) {
            iv_select_status.setBackgroundResource(R.drawable.qs_unselect)
            tv_select_status.text = "全选"
        }
        tv_jump_reason.setOnClickListener(View.OnClickListener {
            if (selectList.size <= 0) {
                showToast("请先勾选运单")
                return@OnClickListener
            }
            var selectOrderIds = ""

            selectList.forEach {
                if (TextUtils.equals("3", it.signState)) {
                    selectOrderIds += "${it.orderId},"
                }
            }
            if (TextUtils.isEmpty(selectOrderIds)) {
                showDialogToast("您好，当前已勾选运单无可被代签收，请重新选择。")
                return@OnClickListener
            }
            <EMAIL>?.let { it1 ->
                OrderExpressMainReasonActivity.starts(it1, "", selectOrderIds, 0x01)
            }
        })
        tvSignForReceipt.setOnClickListener(View.OnClickListener {
            if (selectList.size <= 0) {
                showToast("请先勾选运单")
                return@OnClickListener
            }

            var selectOrderIds = ""
            selectList.forEach {
                if (TextUtils.equals("0", it.signState)) {
                    selectOrderIds += "${it.detailId},"
                }
            }
            if (TextUtils.isEmpty(selectOrderIds)) {
                showDialogToast("您好，当前已勾选运单无可被代签收，请重新选择。")
                return@OnClickListener
            }
            val dialogBuilder = DialogBuilder()
            dialogBuilder.title = "回单签收提醒"
            dialogBuilder.message = "是否批量确认已收到司机寄来的纸质回单，签收成功后承运方将解冻回单押金"
            dialogBuilder.gravity = Gravity.CENTER
            dialogBuilder.okListener = DialogBuilder.DialogInterface.OnClickListener { d, _ ->
                d?.dismiss()
                viewModel?.consignorExpressSign(selectOrderIds)
            }
            showDialog(dialogBuilder)
        })
    }

    override fun onResume() {
        super.onResume()

        edit_search.setText("")
        selectList.clear()
        ("已选" + selectList.size + "单").also { tv_have_select.text = it }
        initData()
    }

    override fun onSingleClick(v: View) {
        super.onSingleClick(v)
        when (v.id) {
            R.id.btn_clear -> {
                edit_search.setText("")
                selectList.clear()
                ("已选" + selectList.size + "单").also { tv_have_select.text = it }
                initData()
            }

            R.id.tvSearch -> {
                val trim = edit_search.text.toString().trim()
                if (trim.isEmpty()) {
                    showDialogToast("请输入运单号")
                } else {
                    viewModel?.getSearchNetInfo(1, trim, null, "0")
                }
            }
        }
    }

    private fun initList() {
        swipe_refresh.apply {
            val emptyView = CommEmptyView.creatorDef(context)
            setAdapter(mOrderExpressMainAdapter1, true)
            setEmptyView(emptyView)
            addItemDecorationSize(dp2px(7f))
            addOnItemListener(onItemClickListener)
            setOnLoadListener(object : OnLoadingListener {
                override fun onRefreshUI(nowPage: Int) {
                    if (selfRefresh) {
                        orderIds = null
                    }
                    selfRefresh = true
                    isRefresh = true
                    viewModel?.getNetInfo(
                        nowPage = nowPage,
                        trim = edit_search.text.toString().trim(),
                        searchOrderId = orderIds,
                        signState = "0",
                        preOrderId = ""
                    )
                }

                override fun onLoadMoreUI(nowPage: Int) {
                    viewModel?.getNetInfo(
                        nowPage = nowPage,
                        trim = edit_search.text.toString().trim(),
                        searchOrderId = orderIds,
                        signState = "0",
                        preOrderId = mOrderExpressMainAdapter1.data.last().orderId
                    )
                }
            })
        }
    }

    private fun initSearch() {
        RxTextView.textChanges(edit_search).map(CharSequence::toString).subscribe {
            btn_clear.visibility = if (it.isEmpty()) View.GONE else View.VISIBLE
        }.apply {
            putDisposable(this)
        }

        edit_search.setOnEditorActionListener(object : TextView.OnEditorActionListener {
            override fun onEditorAction(v: TextView, actionId: Int, event: KeyEvent?): Boolean {
                if (actionId == EditorInfo.IME_ACTION_SEARCH) {
                    val trim = edit_search.text.toString().trim()
                    if (trim.isEmpty()) {
                        showDialogToast("请输入运单号")
                    } else {
                        viewModel?.getSearchNetInfo(1, trim, null, "0")
                    }
                    return true
                }
                return false
            }
        })
        bindClickEvent(btn_clear)
        bindClickEvent(tvSearch)
    }

    private val onItemClickListener = object : OnItemClickListener() {
        override fun onSimpleItemClick(adapter: BaseQuickAdapter<*, *>, view: View, position: Int) {
            val item = adapter.getItem(position)
            if (item is RspCarrierExpressData) {
                // 只有待录入没有详情
                when {
                    item.isClickItem() -> {
                        when (item.sendType) {
                            "1" -> {
                                <EMAIL>?.let {
                                    OrderExpressDetailActivity.start(it, item)
                                }
                            }

                            "0", "2" -> {
                                <EMAIL>?.let {
                                    OrderExpressUnLineDetailActivity.start(it, item)
                                }
                            }
                        }

                    }
                }
            }
        }

        override fun onItemChildClick(adapter: BaseQuickAdapter<*, *>, view: View, position: Int) {
            super.onItemChildClick(adapter, view, position)
            val item = adapter.getItem(position)
            if (item is RspCarrierExpressData) {
                when (view.id) {
                    // 确定签收
                    R.id.btn_sure -> {
                        val dialogBuilder = DialogBuilder().setTitle("回单签收提醒")
                            .setMessage("是否确认已收到司机寄来的纸质回单，签收成功后承运方将解冻回单押金。")
                            .setGravity(Gravity.CENTER).setOkListener { dialogInterface, _ ->
                                dialogInterface.dismiss()
                                viewModel?.consignorExpressSign(item.detailId)
                            }
                        showDialog(dialogBuilder)
                    }

                    // 打回
                    R.id.btn_return -> {
                        OrderExpressReturnReasonActivity.startContentUI(
                            <EMAIL>,
                            item.detailId,
                            item.orderId,
                            item.expressId
                        )
                    }
                    // 查看物流
                    R.id.btn_query -> {
                        // 1 快递送达  2 线下送达
                        when (item.sendType) {
                            "1" -> {
                                <EMAIL>?.let {
                                    OrderExpressDetailActivity.start(it, item)
                                }
                            }
                        }
                    }
                    // 代签收
                    R.id.btn_wait_sign -> {
                        //  回单签收原因
                        <EMAIL>?.let {
                            OrderExpressMainReasonActivity.start(it, "", item.orderId, 0x01)
                        }
                    }
                    // 多选标记
                    R.id.ll_selected_tag -> {
                        when {
                            TextUtils.equals("1", item.selectFlag) -> {
                                item.selectFlag = "2"
                                selectList.remove(item)
                            }

                            TextUtils.equals("2", item.selectFlag) -> {
                                item.selectFlag = "1"
                                selectList.add(item)
                            }
                        }

                        adapter.notifyItemChanged(position)
                        ("已选" + selectList.size + "单").also { tv_have_select.text = it }
                    }
                    // 回单签收 点运单号跳转运单详情
                    R.id.tv_order -> {
                        //运单详情
                        WaybillDetailActivity.start(
                            <EMAIL>,
                            item.orderId
                        )
                    }
                    // 查看榜单码
                    R.id.tv_seek_bound_code -> {
                        BoundDetailActivity.start(
                            <EMAIL>,
                            item.orderId
                        )
                    }
                }
            }
        }
    }

    @RxBusEvent(from = "回单签收原因")
    open fun onDoSignSuccess(success: RxBusDoSign) {
        swipe_refresh.onAutoRefresh()
    }

    @LiveDataMatch
    open fun getNetInfoSuccess(data: LTPageList<RspCarrierExpressData>?) {
        if (data?.rootArray.isNull) {
            swipe_refresh.onLoadMoreFail()
            return
        }
        data?.apply {
            dataList.rootArray = rootArray
            dataList.nowPage = nowPage
            dataList.totalPage = totalPage
            dataList.pageSize = pageSize
            dataList.totalSize = totalSize
            dataList.resultCode = resultCode
            dataList.resultMsg = resultMsg
        }

        //货主签收状态 0:未签收
        dataList.rootArray.forEachIndexed { _, rspCarrierExpressData ->
            if (!(TextUtils.equals("3", rspCarrierExpressData.signState) || TextUtils.equals("0", rspCarrierExpressData.signState))) {
                rspCarrierExpressData.selectFlag = "0"
            }
        }

        if (isRefresh) {
            selectList.clear()
            ("已选" + selectList.size + "单").also { tv_have_select.text = it }

            if (selectList.size == 0) {
                iv_select_status.setBackgroundResource(R.drawable.qs_unselect)
                tv_select_status.text = "全选"
            }
            isRefresh = false
        }

        swipe_refresh.onRefreshCompale(dataList)
    }

    @LiveDataMatch
    open fun getSearchNetInfoSuccess(data: LTPageList<RspCarrierExpressData>?) {
        ("已选" + selectList.size + "单").also { tv_have_select.text = it }
        if (data?.rootArray.isNull) {
            swipe_refresh.onLoadMoreFail()
            return
        }
        data?.apply {
            dataList.rootArray = rootArray
            dataList.nowPage = nowPage
            dataList.totalPage = totalPage
            dataList.pageSize = pageSize
            dataList.totalSize = totalSize
            dataList.resultCode = resultCode
            dataList.resultMsg = resultMsg
        }
        //货主签收状态 0:未签收
        dataList.rootArray.forEachIndexed { _, rspCarrierExpressData ->
            if (!(TextUtils.equals("3", rspCarrierExpressData.signState) || TextUtils.equals("0", rspCarrierExpressData.signState))) {
                rspCarrierExpressData.selectFlag = "0"
            }
        }
        swipe_refresh.onRefreshCompale(dataList)
    }

    @LiveDataMatch
    open fun onConsignorExpressSign() {
        initData()
    }

    @LiveDataMatch
    open fun onConsignorExpressReturn(msg: String) {
        val dialogBuilder = DialogBuilder()
            .setMessage(msg)
            .setGravity(Gravity.CENTER)
            .setOkListener { dialogInterface, _ ->
                dialogInterface.dismiss()
                initData()
            }
    }

    @RxBusEvent(from = "回单打回")
    open fun onData(msg: String) {
        if (TextUtils.equals("回单打回", msg)) {
            initData()
        }
    }

    override fun onActivityResult(requestCode: Int, resultCode: Int, data: Intent?) {
        super.onActivityResult(requestCode, resultCode, data)
        if (resultCode != RESULT_OK || data == null) {
            return
        }
        if (requestCode == 10000) {
            val hmsScanResult: HmsScan? = data.getParcelableExtra(ScanUtil.RESULT)
            hmsScanResult?.let {
                //展示解码结果
                val result = RxCodeResult()
                result.result = it.getOriginalValue()
                handleDecode(result)
            }
        }
    }

    private fun handleDecode(result: RxCodeResult?) {
        val login = CommServer.getUserServer().login
        if (login == null) {
            showToast("请先登录")
            return
        }
        if (result == null || TextUtils.isEmpty(result.result)) {
            showDialogToast("扫描识别失败,重新扫描!")
            return
        }
        val data = TextUtils.split(result.result, ";")

        getViewModel(OrderExpressMainModel::class.java).ensureExpressSign(result.result)
    }

    @LiveDataMatch
    open fun onEnsureExpressSignSuccess(data: RespEnsureExpressSign) {
        HandInputBoundSuccessActivity.start(<EMAIL>, data.toJson())
    }

}