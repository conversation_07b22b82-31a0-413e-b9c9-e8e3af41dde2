package com.zczy.cargo_owner.order.confirm.bean

import android.os.Parcelable
import android.text.TextUtils
import com.zczy.comm.data.OrderPriceBean
import com.zczy.comm.data.entity.EImage
import com.zczy.comm.http.entity.ResultData
import com.zczy.comm.utils.NumUtil
import com.zczy.comm.utils.ex.isNull
import com.zczy.comm.utils.ex.isTrue
import kotlinx.android.parcel.Parcelize


/**
 * @description
 * <AUTHOR> 韩正亚
 * @company 中储智运科技股份有限公司
 * @copyright （版权）中储南京智慧物流科技有限公司所有
 * @date 2019/3/18
 */
data class ReturnedOrderDetailBean(
    val selfComment: String?,
    val backOrderReconsiderState: String?,
    val advanceFlag: String?,
    val backStatus: String?,
    val backTime: String?,
    val cargoCategory: String?,
    val carrierCustomerName: String?,
    val createdBy: String?,
    val deliverCity: String?,
    val deliverDis: String?,
    val deliverWeight: String?,
    val despatchCity: String?,
    val despatchDis: String?,
    val detailId: String?,
    val driverMobile: String?,
    val driverUserName: String?,
    val finSlipLoad: String?,
    val firstEndTime: String?,
    val freightType: String?,
    val receiptFlag: String?,
    val oilCardFlag: String?,
    val orderModel: String?,
    val orderType: String?,
    val pbAllConsignorMoney: String?,
    val pbConsignorMoney: String?,
    val plateNumber: String?,
    val price: String?,
    val receiveWeight: String?,
    val backRegectReason: String?,
    val dealReason: String?,
    val allCargoName: String?,
    var slipLoad: String?,//结算数量
    val delistWeight: String?,
    val cargoCategoryName: String?,
    val backRejectReason: String?,
    var consignorRateMoney: String?,
    var consignorNoRateMoney: String?,
    val settleBasisTypeName: String?,
    val orgBackMoney: String?,
    val updateSettleWeightFlag: String?,
    val consignorRateMoneyUpdateFlag: String?,//结算金额 0-不可修改,1-必填
    val carrierUserType: String?,
    var guaranteeFee: String? = null,
    var pbConsignorUnitMoney: String,
    var settleRate: String = "",
    val consignorDeductionFlag: String?, //“1”显示加款项；”0”不显示
    val consignorDeductionObj: RspAddAndDeductMoneyV1? = null,//发单选择的加扣款明细  type加扣款类型:1:加款;2:扣款;money加扣款金额;reason加扣款原因:1:运费浮动;2:延时扣款;3:磅差费用4.装卸货费 5.信息费
    val consignorDeductiorRateMoney: String?,
    val consignorDeductiorNoRateMoney: String?,
    val deductionMoney: String?, //加款金额
    val deductionType: String?, //加扣款项1:加款;2:扣款
    val isSlipLoadUpdated: String?, //0：不显示，1：显示 载重
    val isBackMoneyUpdated: String?, //0:不显示，1：显示变更钱
    val finSlipLoadOld: String?, //变更前载重
    val moneyRateOld: String?, //含税变更前
    val moneyOld: String?, //不含税变更前
    val consignorDeliverWeightFlag: String?, //回单确认时是否支持修改发货吨位；0否；1：是
    val cargoUnitMoney: String?, //货值单价
    val orderRiskReceiveFlag: String?, //风险卸货
    val lTLOrder: String?,//1 是 0 否
    val weightExceptionMsg: String?, // e.g:收发货吨位数差异超10%，请注意核对！
    val deficitCalculatorShowFlag: String?, //是否显示亏吨计算器
    val pbConsignorUnitMoneyAndUnit: String?, //单价（含税）
    val releaseLossTonData: ReturnedOrderDetailBeanChild1? = null,//扣款配置信息
    val orderReceiptCheckState: String? = null,//平台审核结果  0:"未审核"1:"异常"2:"审核通过"
    val orderReceiptToDealFlag: String? = null,//回单审核去处理 0:不处理1:去处理
    val orderReceiptItemReason: String? = null,//平台审核意见
    val orderTraceExceptionFlag: String? = null,//轨迹异常是否待复审 1:"是" 0:"非异常"
    val orderTraceExceptionToDealFlag: String? = null,//轨迹异常去处理 0:不处理1:去处理
    val orderTraceExceptionItemReason: String? = null,//轨迹异常意见
    val returnMoneyTime: String? = "",//回款到期日,
    val remainderWeight: String? = "",//差额吨位
    val weightRadio: String? = "",//差额比例
    val consignorReceiptRemark: String? = "",//备注
    val receiptEditSettleFlag: String? = "",//是否禁止修改金额和吨位 1：是 0：否
//        val driverDeliverPicJsonArr: ArrayList<String>?,//2021-03-08 ZCZY-5531 因移动端图片展示是根据","切割展示的，如果图片旋转过，则会有问题，故新增数组字段 对应 driverDeliverPic
    val nativeEvidencePicJsonArr: ArrayList<String>?,//现场证据图片Json数组
    val consignorUploadPicJsonObjArr: ArrayList<UploadPic>? = null,//对应 consignorUploadPic 货主图片
    val driverDeliverProofPicJsonArr: ArrayList<WatermarkPic>?,   // 承运方发货单信息 纸质单据
    val deliverTrailerPicList: ArrayList<WatermarkPic>?,   // 发货挂车车牌合影图片
    val examinePicUrlArr: ArrayList<WatermarkPic>?,//证明材料图片数组
    val driverDeliverPhotoPicJsonArr: ArrayList<WatermarkPic>?, // 承运方发货单信息 运单照片
    val driverReceiveProofPicJsonArr: ArrayList<WatermarkPic>?, // 承运方回单信息 纸质单据
    val isBackUpReceive: String? = "",//是否包含备案卸货 1 是 0 否
    val driverReceivePhotoPicJsonArr: ArrayList<WatermarkPic>?, // 承运方回单信息 运单照片
    val receiveTrailerPicList: ArrayList<WatermarkPic>?, // 卸货部分 挂车车牌合影
    val consignorDeliverPicUrlArr: ArrayList<WatermarkPic>?, // 货主发货单照片
    val consignorReveivePicUrlArr: ArrayList<WatermarkPic>?, // 货主收货单照片
    val outTime: String? = "",//出厂时间
    val isShowOutTime: String? = "",//是否展示出场时间
    val goodsSource: String? = "",//是否集装箱货源 1:是0;否
    val showDeficitSuperTonSwitch: String? = "",//货主是否开通了亏涨吨 1：开通
    val deliverReceiveUpdateInfoFlag: String? = "",//否支持修改收发货信息 1:支持，其余为不支持
    val updateDeliverParamsFlag: String? = "",//是否能够修改发货信息 默认为0，不能修改 0：不能修改 1：可修改
    val updateReveiveParamsFlag: String? = "",//是否能够修改收货信息 默认为0，不能修改 0：不能修改 1：可修改
    val driverUpdateDeliverWeightFlag: String? = "",//司机是否修改过发货吨位 默认为0，没修改过 1 修改过 3 平台修改
    val receiveUpdateDeliverWeightFlag: String? = "",//司机是否修改过发货吨位 默认为0，没修改过 1 修改过 3 平台修改
    val settleBasisType: String? = "",//结算依据 1 确认发货吨位结算,2 确认收货吨位结算 4.按理计重量计算
    val supportReceiptSmallerFlag: String? = "",//货主是否支持按收发货磅单中较小吨位值结算
    var containerDataObj: ReceiptContainerDataObj? = null,
    var settleApplyState: String = "",//1.已提交结算申请
    val deliverOrgPicFlag: String = "",//是否存在发货原图 “1”存在 “0”不存在
    val receiveOrgPicFlag: String = "",//是否存在收货原图 “1”存在 “0”不存在
    val carrierDeliverRemark: String = "",//司机发货备注
    val carrierReceiveRemark: String = "",//司机收货备注
    var deliverVideoJsonArr: ArrayList<WatermarkPic>?, // 司机发货视频
    var receiveVideoJsonArr: ArrayList<WatermarkPic>?, // 司机收货视频
    var carrierReveivePicUrlArr: ArrayList<WatermarkPic>?, // 卸货拍照图片
    var deliverTime: String = "",//发货信息-上传时间(确认发货时间)
    var receiveTime: String = "",//回单信息-上传时间(确认收货时间)
    var ignoreSmallChangeType: String = "", // 1:按含税价抹零
    var consignorNoRateUnitShowMoneyFlag: String = "", // 等于1就是展示
    var consignorNoRateUnitShowMoney: String = "", // 承运方预估到手价单价
    var receiveCargoArray: MutableList<ReturnedOrderDetailCargo>? = null, // 收货货物明细
    var deliverCargoArray: MutableList<ReturnedOrderDetailCargo>? = null, // 发货货物明细
    var orderIgnoreSmallObj: ReturnedOrderDetailBeanV1? = null, // 发单是否抹零
    var orderIgnoreSmallFlag: String? = null, // 货主配置是否支持抹零
    var loadPayMoney: String? = null, // 装卸货费
    var receiptMoneySnapShotObj: ReceiptMoneySnapShotObj? = null, //App回单修改记录
    var lastUptTime: String = "",
    var controlAmount: String = "",//单月管控金额
    var showDeliverReceiptFlag: String? = null,//是否展示 发货单据号模块   1展示
    var deliverAiReceiptNumber: String? = null,//Ai 识别的发货单据号
    var deliverReceiptNumber: String? = null,//货主回单 确认的发货单据号
) : ResultData()

fun ReturnedOrderDetailBean.showContainerNum(): Double {
    return (containerDataObj?.receiptContainerDataObj?.containerNoJsonArray?.size ?: 1).toDouble()
}

fun ReturnedOrderDetailBean?.getSettleRate(): Double {
    if (this.isNull) {
        return 0.00
    }
    if (this?.settleRate.isNullOrEmpty()) {
        return 0.00
    }
    return this?.settleRate?.toDoubleOrNull() ?: 0.00
}

fun ReturnedOrderDetailBean?.isShowCheckMonth(s: String): String {
    if (this.isNull) {
        return ""
    } else {
        val a = NumUtil.mul(this?.controlAmount?.toDoubleOrNull() ?: 0.00, 10000.00)
        return if (a <= 0.00) {
            ""
        } else {
            val b = s.toDoubleOrNull() ?: 0.00
            if (b <= 0.00) {
                ""
            } else {
                if (b > a) {
                    "单笔运单交易不得超过${this?.controlAmount}万，如最终结算时仍超出该金额则无法结算，请知悉！"
                } else {
                    ""
                }
            }
        }
    }
}


class ReceiptMoneySnapShotObj(
    val slipLoad: String? = null,
    val ignoreSmallChangeRateMoney: String? = null,
    val overLossRuleName: String? = null,
    val releaseLossConsignorRateMoney: String? = null,
    val deductionRateMoney: String? = null,
    val releaseLossConsignorNoRateMoney: String? = null,
    val ignoreSmallChangeFlag: String? = null,
    val lossRiseMoney: String? = null,
    val consignorRateMoney: String? = null,
    val consignorNoRateMoney: String? = null,
    val ignoreSmallChangeMoney: String? = null,
    val consignorDeduction: String? = null,
    val deductionNoRateMoney: String? = null,
    val consignorSmartDeficitTonUseFlag: String? = null,
    val ignoreSmallChangeType: String? = null,
    var deductionDetail: String? = null,
    val consignorSmartDeficitTonUseRateType: String? = null,
    val ignoreSmallChangeNoRateMoney: String? = null,
    val consignorDeductionFlag: String? = null
)

data class ConsignorDeduction(
    val reason: String? = null,
    val type: String? = null,
    val money: String? = null
)

class ReturnedOrderDetailBeanV1(
    var ignoreSmallChangeType: String? = null,// 0否 ；1：含税价角分抹零； 2：承运方预估到手价十元以下抹零   3：含税价十元以下抹零: 4：承运方预估到手价角分抹零
    var mOrderPriceBean: OrderPriceBean = OrderPriceBean(),
    var ignoreSmallChangeRateMoney: String? = null,//抹零后含税金额
    var ignoreSmallChangeNoRateMoney: String? = null,//抹零后不含税金额
)

class ReturnedOrderDetailCargo(
    var cargoId: String? = null, //货物id
    var cargoName: String? = null, // 货物名称
    var cargoCategory: String? = null, //货物类别：1：重货，2：泡货
    var beforeCargoWeight: String? = null, //展示的货物明细重量
    var weight: String? = null, //货物总量
    var unit: String? = null, //货物单位：1：吨，2：m3  3:集装箱
    var weightRatio: String? = null, //理计比例
)

fun ReturnedOrderDetailCargo.showUnit(): String {
    return when (unit) {
        "2" -> {
            "方"
        }

        "3" -> {
            "箱"
        }

        else -> {
            "吨"
        }
    }
}

class ReturnedOrderDetailRule(
    val ruleId: String? = null,
    val ruleName: String? = null,
)

fun ReturnedOrderDetailBean?.showRule(): Boolean {
    if (this.isNull) {
        return false
    }
    this?.let {
        if (it.releaseLossTonData.isNull) {
            return false
        }
        it.releaseLossTonData?.let { it1 ->
            return !it1.consignorSmartDeficitTonDefauleRuleList.isNullOrEmpty()
        }
    }
    return false
}

fun ReturnedOrderDetailBean.showVideo1(): ArrayList<EImage> {
    val list = arrayListOf<EImage>()
    deliverVideoJsonArr?.forEach {
        list.add(EImage(imageId = it.picUrl))
    }
    return list
}

fun ReturnedOrderDetailBean.showVideo2(): ArrayList<EImage> {
    val list = arrayListOf<EImage>()
    receiveVideoJsonArr?.forEach {
        list.add(EImage(imageId = it.picUrl))
    }
    return list
}

fun ReturnedOrderDetailBean.showCarrierDeliverRemark(): String {
    return "备注：${carrierDeliverRemark}"
}

fun ReturnedOrderDetailBean.showCarrierReceiveRemark(): String {
    return "备注：${carrierReceiveRemark}"
}

fun ReturnedOrderDetailBean.showDeliverTime(): String {
    return "上传时间：${deliverTime}"
}

fun ReturnedOrderDetailBean.showReceiveTime(): String {
    return "上传时间：${receiveTime}"
}

fun ReturnedOrderDetailBean.showEditV1(): Boolean {
    return if (deliverReceiveUpdateInfoFlag.isTrue) {
        //该货主支持收发货信息修改
        updateDeliverParamsFlag.isTrue
    } else {
        false
    }
}

fun ReturnedOrderDetailBean.showEditV2(): Boolean {
    return if (deliverReceiveUpdateInfoFlag.isTrue) {
        //该货主支持收发货信息修改
        updateReveiveParamsFlag.isTrue
    } else {
        false
    }
}

data class ReceiptContainerDataObj(
    var receiptContainerDataObj: ReceiveContainerDataObj? = null
)

data class ReceiveContainerDataObj(
    var containerName: String = "",
    var containerNoJsonArray: ArrayList<ContainerNoJsonArray> = arrayListOf()
)

data class ContainerNoJsonArray(
    var containerListNo: String = ""
)

fun ReturnedOrderDetailBean.differenceV1(): String {
    return if (TextUtils.isEmpty(remainderWeight) && TextUtils.isEmpty(weightRadio)) {
        ""
    } else if (!TextUtils.isEmpty(remainderWeight) && TextUtils.isEmpty(weightRadio)) {
        "差额：$remainderWeight"
    } else if (!TextUtils.isEmpty(remainderWeight) && !TextUtils.isEmpty(weightRadio)) {
        "差额：$remainderWeight | $weightRadio"
    } else if (TextUtils.isEmpty(remainderWeight) && !TextUtils.isEmpty(weightRadio)) {
        "差额：$weightRadio"
    } else {
        ""
    }
}

fun ReturnedOrderDetailBean.getDeliverCargoCategoryStr(): String {
    return when (cargoCategory) {
        "2" -> {
            "方"
        }

        else -> {
            "吨"
        }
    }
}

fun ReturnedOrderDetailBean.getDeliverCargoCategoryStrV1(): String {
    return when (goodsSource) {
        "1" -> {
            "箱"
        }

        else -> {
            when (cargoCategory) {
                "1" -> {
                    "吨"
                }

                else -> {
                    "方"
                }
            }
        }
    }
}

fun ReturnedOrderDetailBean.getDeliverWeightStr(): String {
    return deliverWeight.toString()
}

fun ReturnedOrderDetailBean.getReturnMoneyTime(): String {
    return returnMoneyTime ?: ""
}

fun ReturnedOrderDetailBean.showReturnMoneyTime(): Boolean {
    return !TextUtils.isEmpty(returnMoneyTime)
}

class UploadPic(
    var pictureUrl: String = "",//
    var photoId: String = "",//
    var pictureType: String = ""//13平台上传
)

@Parcelize
class WatermarkPic(
    var picUrl: String = "",//带水印图片
    var picOrgUrl: String = ""//原始图片不带水印
) : Parcelable

class ReturnedOrderDetailBeanChild1(
    val consignorSmartDeficitTonFlag: String?, //货主开通小型亏吨计算算法 1:开通
    var releaseLossTonType: String? = null, //货主开通小型亏吨计算类型 1:亏吨;2:涨吨
    var releaseLossTonNum: String? = null, //亏/超吨数
    var cargoUnitMoney: String? = null, //单位货值
    var releaseLossTonActSubMoney: String? = null, //扣款金额
    var consignorSmartDeficitTonDefaultConfFlag: String? = null, //是否有默认配置 1:有
    var consignorSmartDeficitTonDefauleRuleId: String? = null,//默认规则Id
    var consignorSmartDeficitTonDefauleRuleName: String? = null,//默认规则名称
    var lossRiseConsignorRateMoney: String? = null, //扣款后含税价格
    var lossRiseConsignorNoRateMoney: String? = null,  //扣款后不含税价格
    var consignorSmartDeficitTonUseRateType: String? = null, //1:按结算(含税)价扣减;2: 按结算(不含税)价扣减
    var consignorSmartDeficitTonDefauleRuleList: MutableList<ReturnedOrderDetailRule>? = mutableListOf(), // 亏涨吨规则列表
)
