package com.zczy.cargo_owner.order.settlement

import com.sfh.lib.exception.HandleException
import com.sfh.lib.mvvm.service.BaseViewModel
import com.sfh.lib.rx.IResult
import com.zczy.cargo_owner.order.settlement.bean.*
import com.zczy.comm.http.entity.BaseRsp
import com.zczy.comm.http.entity.PageList
import com.zczy.comm.http.entity.ResultData
import com.zczy.comm.utils.ex.isTrue

/**
 * @description
 * <AUTHOR> 韩正亚
 * @company 中储智运科技股份有限公司
 * @copyright （版权）中储南京智慧物流科技有限公司所有
 * @date 2019/4/16
 */
class SettlementApplicationModel : BaseViewModel() {
    /**
     * 结算申请列表
     */
    fun querySettlementApplicationByType(req: QuerySettlementApplicationByTypeReq) {
        req.setSettleApply()
        execute(true, req, object : IResult<BaseRsp<SettlementApplicationPageList<SettlementApplicationItemBean>>> {
            override fun onSuccess(t: BaseRsp<SettlementApplicationPageList<SettlementApplicationItemBean>>?) {
                hideLoading()
                val success = t?.success()
                if (success == true) {
                    setValue("querySettlementApplicationByType", t.data)
                } else {
                    setValue("querySettlementApplicationByType", null)
                    showToast(t?.msg)
                }
            }

            override fun onFail(e: HandleException?) {
                hideLoading()
                showToast(e?.msg)
                setValue("querySettlementApplicationByType", null)
            }

        })
    }

    //APP结算申请全部全选查询接口
    fun querySettleApplyDataByAllChoose(req: ReqQuerySettleApplyDataByAllChoose) {
        execute(true, req, object :
            IResult<BaseRsp<PageList<RspQuerySettleApplyDataByAllChoose>>> {
            override fun onSuccess(t: BaseRsp<PageList<RspQuerySettleApplyDataByAllChoose>>) {
                hideLoading()
                if (t.success()) {
                    setValue("querySettleApplyDataByAllChooseSuccess", t.data)
                } else {
                    showToast(t.msg)
                    setValue("querySettleApplyDataByAllChooseError")
                }
            }

            override fun onFail(e: HandleException?) {
                hideLoading()
                showToast(e?.msg)
                setValue("querySettleApplyDataByAllChooseError")
            }

        })
    }

    //APP结算申请全部全选查询接口
    fun settleAccountDetailDataByDetails(req: ReqSettleAccountDetailDataByDetails) {
        execute(true, req, object :
            IResult<BaseRsp<PageList<RspQuerySettleApplyDataByAllChoose>>> {
            override fun onSuccess(t: BaseRsp<PageList<RspQuerySettleApplyDataByAllChoose>>) {
                hideLoading()
                if (t.success()) {
                    setValue("querySettleApplyDataByAllChooseSuccess", t.data)
                } else {
                    showToast(t.msg)
                    setValue("querySettleApplyDataByAllChooseError")
                }
            }

            override fun onFail(e: HandleException?) {
                hideLoading()
                showToast(e?.msg)
                setValue("querySettleApplyDataByAllChooseError")
            }

        })
    }

    /**
     * 结算申请列表-搜索列表专用
     */
    fun querySearchSettlementApplicationByType(req: QuerySettlementApplicationByTypeReq) {
        req.setSettleApply1()
        req.sendRequest(object :
            IResult<BaseRsp<SettlementApplicationPageList<SettlementApplicationItemBean>>> {
            override fun onSuccess(t: BaseRsp<SettlementApplicationPageList<SettlementApplicationItemBean>>) {
                if (t.success()) {
                    setValue("querySettlementApplicationByType", t.data)
                } else {
                    setValue("querySettlementApplicationByType", null)
                    showToast(t.msg)
                }
            }

            override fun onFail(e: HandleException?) {
                showToast(e?.msg)
                setValue("querySettlementApplicationByType", null)
            }

        })
    }

    /**
     * 结算申请列表
     */
    fun querySettlementApplicationByTypeDetail(req: QuerySettlementApplicationByTypeReqDate) {
        execute(true, req, object :
            IResult<BaseRsp<SettlementApplicationItemBean>> {
            override fun onSuccess(t: BaseRsp<SettlementApplicationItemBean>?) {
                hideLoading()
                val success = t?.success()
                if (success == true) {
                    setValue("querySettlementApplicationByType", t.data)
                } else {
                    setValue("querySettlementApplicationByType", null)
                    showToast(t?.msg)
                }
            }

            override fun onFail(e: HandleException?) {
                hideLoading()
                showToast(e?.msg)
                setValue("querySettlementApplicationByType", null)
            }

        })
    }

    /**
     * 提交结算申请
     */
    fun confirmSettlementApplication(req: ConfirmSettlementApplicationReq) {
        execute(true, req,
            object : IResult<BaseRsp<ResultData>> {
                override fun onSuccess(t: BaseRsp<ResultData>?) {
                    hideLoading()
                    val success = t?.success()
                    if (success == true) {
                        setValue("confirmSettlementApplicationSuccess")
                    } else {
                        showToast(t?.msg)
                    }
                }

                override fun onFail(e: HandleException?) {
                    hideLoading()
                    showToast(e?.msg)
                }

            })
    }

    /**
     * 提交结算申请
     */
    fun confirmSettlementApplication(req: ReqDoInvoicingBatchFrontSettleApply) {
        execute(true, req,
            object : IResult<BaseRsp<ResultData>> {
                override fun onSuccess(t: BaseRsp<ResultData>?) {
                    hideLoading()
                    val success = t?.success()
                    if (success == true) {
                        setValue("confirmSettlementApplicationSuccess")
                    } else {
                        showToast(t?.msg)
                    }
                }

                override fun onFail(e: HandleException?) {
                    hideLoading()
                    showToast(e?.msg)
                }

            })
    }

    /**
     * 提交结算申请
     */
    fun findSettleAccountMoneyIsEnoughReq(req: FindSettleAccountMoneyIsEnoughReq) {
        execute(true, req,
            object : IResult<BaseRsp<FindSettleAccountMoneyIsEnoughRsp>> {
                override fun onSuccess(t: BaseRsp<FindSettleAccountMoneyIsEnoughRsp>?) {
                    hideLoading()
                    val success = t?.success()
                    val flag = t?.data?.moneyEnoughFlag.isTrue
                    if (success == true) {
                        if (flag) {
                            setValue("onMoneyEnough",t.data)
                        } else {
                            setValue("onMoneyUnEnough", t.data)
                        }
                    } else {
                        showToast(t?.msg)
                    }
                }

                override fun onFail(e: HandleException?) {
                    hideLoading()
                    showToast(e?.msg)
                }

            })
    }

    /**
     * 提交结算申请
     */
    fun findSettleAccountMoneyIsEnoughReq(req: ReqFindInvoicingSettleAccountMoneyIsEnough) {
        execute(true, req,
            object : IResult<BaseRsp<FindSettleAccountMoneyIsEnoughRsp>> {
                override fun onSuccess(t: BaseRsp<FindSettleAccountMoneyIsEnoughRsp>?) {
                    hideLoading()
                    val success = t?.success()
                    val flag = t?.data?.moneyEnoughFlag.isTrue
                    if (success == true) {
                        if (flag) {
                            setValue("onMoneyEnough",t.data)
                        } else {
                            setValue("onMoneyUnEnough", t.data)
                        }
                    } else {
                        showToast(t?.msg)
                    }
                }

                override fun onFail(e: HandleException?) {
                    hideLoading()
                    showToast(e?.msg)
                }

            })
    }

    /**
     * 提交结算申请
     */
    fun summitToAudit(req: ReqSummitToAudit) {
        execute(true, req,
            object : IResult<BaseRsp<ResultData>> {
                override fun onSuccess(t: BaseRsp<ResultData>) {
                    hideLoading()
                    if (t.success()) {
                        setValue("onSummitToAuditSuccess")
                    } else {
                        showToast(t.msg)
                    }
                }

                override fun onFail(e: HandleException?) {
                    hideLoading()
                    showToast(e?.msg)
                }

            })
    }

    /**
     * 未申请、已驳回数量
     */
    fun getNotConfirmAndRejectedNum(req: GetNotConfirmCountReq) {
        execute(true, req,
            object : IResult<BaseRsp<SettlementCountBean>> {
                override fun onSuccess(t: BaseRsp<SettlementCountBean>?) {
                    hideLoading()
                    val success = t?.success()
                    if (success == true) {
                        setValue("getNotConfirmAndRejectedNumSuccess", t.data)
                    }
                }

                override fun onFail(e: HandleException?) {
                    hideLoading()
                }
            })
    }

    /**
     * 查询结算平台
     */
    fun querySettlementPlatform(childId: String) {
        execute(SettlementPlatformReq(childId), object : IResult<BaseRsp<SettlementPlatformRes>> {
            override fun onSuccess(t: BaseRsp<SettlementPlatformRes>) {
                if (t.success()) {
                    setValue("querySettlementPlatformSuccess", t.data)
                }
            }

            override fun onFail(e: HandleException?) {
            }
        })
    }

    /**
     * 查询子账号结算权限
     */
    fun queryChildSettlementPermission() {
        execute(
            ChildAccountSettlementPermissionReq(),
            object : IResult<BaseRsp<ChildAccountSettlementPermissionRes>> {
                override fun onSuccess(t: BaseRsp<ChildAccountSettlementPermissionRes>?) {
                    val success = t?.success()
                    if (success == true) {
                        setValue("queryChildSettlementPermissionSuccess", t.data)
                    }
                }

                override fun onFail(e: HandleException?) {
                }
            })
    }


    /**
     * 结算申请撤销
     */
    fun settleApplyRevoke(req: SettleApplyRevokeReq) {
        execute(
            req,
            object : IResult<BaseRsp<ResultData>> {
                override fun onSuccess(t: BaseRsp<ResultData>?) {
                    val success = t?.success()
                    if (success == true) {
                        setValue("settleApplyRevokeSuccess", t.data)
                    } else {
                        showDialogToast(t?.msg)
                    }
                }

                override fun onFail(e: HandleException?) {
                    showToast(e?.msg)
                }
            })
    }
}