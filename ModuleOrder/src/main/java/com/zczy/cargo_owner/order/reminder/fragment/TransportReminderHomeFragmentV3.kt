package com.zczy.cargo_owner.order.reminder.fragment

import android.annotation.SuppressLint
import android.os.Bundle
import android.view.View
import androidx.fragment.app.Fragment
import androidx.recyclerview.widget.GridLayoutManager
import com.chad.library.adapter.base.BaseQuickAdapter
import com.luck.picture.lib.decoration.GridSpacingItemDecoration
import com.sfh.lib.mvvm.annotation.LiveDataMatch
import com.sfh.lib.ui.dialog.DialogBuilder
import com.zczy.cargo_owner.order.R
import com.zczy.cargo_owner.order.detail.WaybillDetailActivity
import com.zczy.cargo_owner.order.reminder.TransportReminderHomeSearchActivityV1
import com.zczy.cargo_owner.order.reminder.adapter.TransportReminderHomeAdapterV3
import com.zczy.cargo_owner.order.reminder.adapter.TransportReminderHomeItemAdapter
import com.zczy.cargo_owner.order.reminder.model.TransportReminderModel
import com.zczy.cargo_owner.order.reminder.req.RemindItem
import com.zczy.cargo_owner.order.reminder.req.ReqDeleteOrderRiskEventForApp
import com.zczy.cargo_owner.order.reminder.req.ReqQueryLegalRiskTypeCount
import com.zczy.cargo_owner.order.reminder.req.ReqQueryOrderRiskEventList
import com.zczy.cargo_owner.order.reminder.req.RspQueryOrderRiskEventList
import com.zczy.cargo_owner.order.reminder.req.isEmptyOrZero
import com.zczy.comm.http.entity.PageList
import com.zczy.comm.ui.BaseFragment
import com.zczy.comm.utils.dp2px
import com.zczy.comm.utils.ex.yes
import com.zczy.comm.widget.pulltorefresh.CommEmptyView
import kotlinx.android.synthetic.main.transport_reminder_home_fragment_v3.recycler_items
import kotlinx.android.synthetic.main.transport_reminder_home_fragment_v3.swipeRefreshMoreLayout
import kotlinx.android.synthetic.main.transport_reminder_home_fragment_v3.tvTotalLook
import java.util.Calendar


/**
 *  user: ssp
 *  time: 2021/7/13 16:16
 *  desc: 运输提醒-合规风险
 */

class TransportReminderHomeFragmentV3 : BaseFragment<TransportReminderModel>() {
    private var reqQueryList: ReqQueryOrderRiskEventList = ReqQueryOrderRiskEventList()
    private var itemAdapter = TransportReminderHomeItemAdapter()
    private var homeAdapter = TransportReminderHomeAdapterV3()

    companion object {

        const val SOURCE = "source"

        @JvmStatic
        fun newInstance(source: String = ""): Fragment {
            val fragment = TransportReminderHomeFragmentV3()
            val bundle = Bundle()
            bundle.putString(SOURCE, source)
            fragment.arguments = bundle
            return fragment
        }
    }

    override fun getLayout(): Int {
        return R.layout.transport_reminder_home_fragment_v3
    }

    override fun bindView(view: View, bundle: Bundle?) {
        val month = Calendar.getInstance().get(Calendar.MONTH) + 1
        reqQueryList.month = when (month < 10) {
            true -> {
                "0$month"
            }

            else -> {
                "$month"
            }

        }
        swipeRefreshMoreLayout.apply {
            setAdapter(homeAdapter, true)
            addItemDecorationSize(dp2px(7f))
            setEmptyView(CommEmptyView.creatorDef(context))
            setOnLoadListener2 {
                reqQueryList.nowPage = it
                reqQueryList.source = "2"
                viewModel?.queryDataV2(reqQueryList)
                //看板配置
                initTopView()
            }
            addOnItemChildClickListener { adapter, view, position ->
                when (view.id) {
                    R.id.tvDelete -> {
                        //删除提醒
                        val dialogBuilder = DialogBuilder()
                        dialogBuilder.message = "是否删除提醒"
                        dialogBuilder.setOKText("删除")
                        dialogBuilder.okListener = DialogBuilder.DialogInterface.OnClickListener { dialog, _ ->
                            dialog.dismiss()
                            val item = homeAdapter.data[position]
                            getViewModel(TransportReminderModel::class.java).execute(
                                ReqDeleteOrderRiskEventForApp(
                                    orderId = item.orderId,
                                    tabType = "2"
                                )
                            ) {
                                activity?.runOnUiThread {
                                    if (it.success()) {
                                        onDeleteSuccess()
                                    } else {
                                        showToast(it.msg)
                                    }
                                }
                            }
                        }
                        showDialog(dialogBuilder)
                    }

                    R.id.tvOrderId -> {
                        //跳转到运单详情
                        val item = homeAdapter.data[position]
                        WaybillDetailActivity.start(
                            <EMAIL>,
                            item.orderId
                        )
                    }

                }
            }
            onAutoRefresh()
        }

        recycler_items.apply {
            layoutManager = GridLayoutManager(<EMAIL>, 3)
            addItemDecoration(GridSpacingItemDecoration(3, 20, false))
        }

        itemAdapter.bindToRecyclerView(recycler_items)
        itemAdapter.onItemClickListener = BaseQuickAdapter.OnItemClickListener { _, _, position ->
            itemAdapter.getItem(position)?.let { refreshData(it.type) }
        }
    }

    @SuppressLint("SetTextI18n")
    private fun initTopView() {
        getViewModel(TransportReminderModel::class.java).execute(ReqQueryLegalRiskTypeCount()) {
            if (it.success()) {
                activity?.runOnUiThread {
                    val list = mutableListOf<RemindItem>()
                    it.data?.let { data ->
                        tvTotalLook.text = "持续监控中(${data.legalRisk ?: "0"})"
                        data.legalRisk1.isEmptyOrZero().yes {
                            list.add(RemindItem(size = data.legalRisk1, title = "一单多车", type = "1"))
                        }
                        data.legalRisk2.isEmptyOrZero().yes {
                            list.add(RemindItem(size = data.legalRisk2, title = "半途挂单", type = "2"))
                        }
                        data.legalRisk3.isEmptyOrZero().yes {
                            list.add(RemindItem(size = data.legalRisk3, title = "自挂自摘", type = "3"))
                        }
                        data.legalRisk4.isEmptyOrZero().yes {
                            list.add(RemindItem(size = data.legalRisk4, title = "未承运", type = "4"))
                        }
                    }
                    itemAdapter.setNewData(list)
                }
            }
        }
    }

    private fun refreshData(type: String) {
        reqQueryList.pushLegalRiskType = type
        swipeRefreshMoreLayout.onAutoRefresh()
    }

    override fun onSingleClick(v: View) {
        super.onSingleClick(v)
        when (v.id) {
            R.id.tvFilter -> {
                //筛选
                TransportReminderHomeSearchActivityV1.jumpPage(
                    <EMAIL>,
                    TransportReminderHomeSearchActivityV1.REQUEST_CODE
                )
            }
        }
    }

    override fun initData() {
    }

    @LiveDataMatch(tag = "列表查询")
    open fun queryOrderRiskEventListSuccessV2(data: PageList<RspQueryOrderRiskEventList>) {
        swipeRefreshMoreLayout.onRefreshCompale(data)
    }

    @LiveDataMatch(tag = "列表查询")
    open fun queryOrderRiskEventListErrorV2(data: String) {
        showToast(data)
        swipeRefreshMoreLayout.onLoadMoreFail()
    }

    private fun onDeleteSuccess() {
        //同时刷新顶部数据
        swipeRefreshMoreLayout.onAutoRefresh()
        //看板配置
        initTopView()
    }
}