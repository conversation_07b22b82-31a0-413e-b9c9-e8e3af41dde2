package com.zczy.cargo_owner.order.confirm

import android.app.Activity
import android.content.Context
import android.content.Intent
import android.os.Bundle
import android.text.TextUtils
import android.util.SparseArray
import android.view.View
import android.widget.Button
import com.chad.library.adapter.base.BaseQuickAdapter
import com.chad.library.adapter.base.listener.OnItemClickListener
import com.sfh.lib.event.RxBusEvent
import com.sfh.lib.mvvm.annotation.LiveDataMatch
import com.sfh.lib.mvvm.service.BaseViewModel
import com.sfh.lib.utils.UtilTool
import com.zczy.cargo_owner.order.R
import com.zczy.cargo_owner.order.confirm.ReturnedOrderConfirmListFragment.Companion.TYPE_NEGOTIATING
import com.zczy.cargo_owner.order.confirm.adapter.SearchReturnedOrderAdapter
import com.zczy.cargo_owner.order.confirm.bean.AgreeAdjustDetailBean
import com.zczy.cargo_owner.order.confirm.bean.AgreeAdjustDetailReq
import com.zczy.cargo_owner.order.confirm.bean.AgreeAdjustReq
import com.zczy.cargo_owner.order.confirm.bean.BatchChosenReturnedOrderBean
import com.zczy.cargo_owner.order.confirm.bean.ExceptionConfigTypeEnum
import com.zczy.cargo_owner.order.confirm.bean.OperationConfig
import com.zczy.cargo_owner.order.confirm.bean.QueryReturnedOrderByTypeReq
import com.zczy.cargo_owner.order.confirm.bean.ReqQueryConsignorRepulseTag
import com.zczy.cargo_owner.order.confirm.bean.ReturnOrderConfirmData
import com.zczy.cargo_owner.order.confirm.bean.ReturnedOrderItemBean
import com.zczy.cargo_owner.order.confirm.bean.ReturnedOrderPageList
import com.zczy.cargo_owner.order.confirm.bean.RxAdjust
import com.zczy.cargo_owner.order.confirm.bean.RxAgreeAdjust
import com.zczy.cargo_owner.order.confirm.bean.WaitingProcessingReq
import com.zczy.cargo_owner.order.confirm.bean.getOperationConfig
import com.zczy.cargo_owner.order.confirm.dialog.AgreeAdjustDetailDialog
import com.zczy.cargo_owner.order.confirm.dialog.ReturnOrderReasonChooseDialog
import com.zczy.cargo_owner.order.confirm.v2.SingleReturnedOrderConfirmActivityV2
import com.zczy.cargo_owner.order.confirm.widget.ReturnReseonsDialog
import com.zczy.cargo_owner.order.violate.OrderViolateAddActivity
import com.zczy.cargo_owner.order.violate.OrderViolateListActivity
import com.zczy.comm.pluginserver.AMainServer
import com.zczy.comm.ui.BaseActivity
import com.zczy.comm.utils.dp2px
import com.zczy.comm.widget.pulltorefresh.CommEmptyView
import kotlinx.android.synthetic.main.order_returned_confirm_search_activity.swipeRefreshMore

/**
 *  user: ssp
 *  time: 2021/6/11 10:48
 *  desc: 回单确认搜索
 */
open class SearchReturnedOrderActivity : BaseActivity<ReturnedOrderConfirmModel>(), AgreeAdjustDetailDialog.AgreeAdjustCallback {
    private val mAdapter = SearchReturnedOrderAdapter()
    private var queryType: String? = ""
    private var operationConfig: OperationConfig? = null
    private var mReturnOrderConfirmData: ReturnOrderConfirmData? = ReturnOrderConfirmData()
    override fun getLayout(): Int = R.layout.order_returned_confirm_search_activity
    override fun bindView(bundle: Bundle?) {
        queryType = intent.getStringExtra("queryType")
        operationConfig = intent.getParcelableExtra("operationConfig")
        mReturnOrderConfirmData = intent.getParcelableExtra("returnOrderConfirmData")
        val emptyView = CommEmptyView.creatorDef(this)
        swipeRefreshMore.apply {
            setAdapter(mAdapter, true)
            setEmptyView(emptyView)

            addItemDecorationSize(dp2px(7f))
            addOnItemListener(onItemClickListener)
            setOnLoadListener2 { nowPage ->
                loadData(nowPage = nowPage)
            }
            onAutoRefresh()
        }
    }

    private fun loadData(nowPage: Int) {
        var preOrderId: String = ""
        if (!mAdapter.data.isNullOrEmpty()) {
            preOrderId = mAdapter.data.last().orderId
        }
        viewModel?.queryReturnedOrderByType(
            QueryReturnedOrderByTypeReq(
                nowPage,
                loadStartDate = mReturnOrderConfirmData?.startTime ?: "",
                startDate = mReturnOrderConfirmData?.startDate ?: "",
                loadEndDate = mReturnOrderConfirmData?.endTime ?: "",
                endDate = mReturnOrderConfirmData?.endDate ?: "",
                search = mReturnOrderConfirmData?.search ?: "",
                searchKey = mReturnOrderConfirmData?.searchType ?: "",
                dispatchGuaranteeFlag = mReturnOrderConfirmData?.dispatchGuaranteeFlag ?: "",
                differDeliverAndReceive = mReturnOrderConfirmData?.differDeliverAndReceive ?: "",
                preOrderId = preOrderId
            )
        )
    }

    override fun initData() {
    }

    override fun onActivityResult(requestCode: Int, resultCode: Int, data: Intent?) {
        super.onActivityResult(requestCode, resultCode, data)
        if (requestCode == Activity.RESULT_OK) {
            when (resultCode) {
                REQUEST_REJECT,
                REQUEST_COMMIT,
                UPLOAD_PHOTO,
                SINGLE_CONFIRM_REQUEST_CODE -> {
                    swipeRefreshMore.onAutoRefresh()
                }
            }
        }
    }

    private val onItemClickListener = object : OnItemClickListener() {
        override fun onSimpleItemClick(adapter: BaseQuickAdapter<*, *>, view: View, position: Int) {
            val item = adapter.getItem(position) as ReturnedOrderItemBean
            when (item.backStatus) {
                ReturnedOrderConfirmListFragment.TYPE_NOT_CONFIRM,
                ReturnedOrderConfirmListFragment.TYPE_NEGOTIATING,
                ReturnedOrderConfirmListFragment.TYPE_WAITING_PROCESSING,
                ReturnedOrderConfirmListFragment.TYPE_CONFIRMED -> {
                    val batchConfirmList = mutableListOf<BatchChosenReturnedOrderBean>()
                    batchConfirmList.add(
                        BatchChosenReturnedOrderBean(
                            detailId = item.detailId,
                            money = item.orgBackMoney,
                            orderId = item.orderId,
                            lastUptTime = item.lastUptTime,
                            backOrderReconsiderState = item.backOrderReconsiderState,
                            goodsSource = item.goodsSource,
                            settleApplyState = item.settleApplyState
                        )
                    )
                    val batchChosenReturnedOrderBean = batchConfirmList[0]
                    val intent = Intent(this@SearchReturnedOrderActivity, SingleReturnedOrderConfirmActivityV2::class.java)
                    intent.putExtra(SingleReturnedOrderConfirmActivityV2.ORDER_ID, batchChosenReturnedOrderBean.orderId)
                    intent.putExtra(SingleReturnedOrderConfirmActivityV2.DETAIL_ID, batchChosenReturnedOrderBean.detailId)
                    intent.putExtra(ReturnedOrderConfirmListFragment.RETURNED_ORDER_QUERY_TYPE, item.backStatus)
                    mAdapter.getAuthority()?.let {
                        intent.putExtra(ReturnedOrderConfirmListFragment.OPERATION_CONFIG, it.getOperationConfig(item.backStatus))
                    }
                    intent.putExtra(SingleReturnedOrderConfirmActivityV2.BACK_ORDER_RECONSIDER_STATE, batchChosenReturnedOrderBean.backOrderReconsiderState)
                    val lastUptTimeList = SparseArray<String>()
                    lastUptTimeList.put(batchChosenReturnedOrderBean.detailId.hashCode(), batchChosenReturnedOrderBean.lastUptTime)
                    startActivityForResult(intent, SINGLE_CONFIRM_REQUEST_CODE)
                }

                else -> {
                }
            }
        }

        override fun onItemChildClick(adapter: BaseQuickAdapter<*, *>, view: View, position: Int) {
            super.onItemChildClick(adapter, view, position)
            val item = adapter.getItem(position) as ReturnedOrderItemBean
            when (view.id) {
                R.id.tvReturnedOrderDetail -> {
                    when (item.backStatus) {
                        TYPE_NEGOTIATING -> {
                            when {
                                TextUtils.equals("4", item.backOrderReconsiderState) -> {
                                    ReturnedOrderConfirmDetailActivity.start(
                                        context = this@SearchReturnedOrderActivity,
                                        detailId = item.detailId,
                                        orderId = item.orderId
                                    )
                                }

                                else -> {
                                    val batchConfirmList = mutableListOf<BatchChosenReturnedOrderBean>()
                                    batchConfirmList.add(
                                        BatchChosenReturnedOrderBean(
                                            detailId = item.detailId,
                                            money = item.orgBackMoney,
                                            orderId = item.orderId,
                                            lastUptTime = item.lastUptTime,
                                            backOrderReconsiderState = item.backOrderReconsiderState,
                                            goodsSource = item.goodsSource,
                                            settleApplyState = item.settleApplyState
                                        )
                                    )
                                    val batchChosenReturnedOrderBean = batchConfirmList[0]
                                    val intent = Intent(this@SearchReturnedOrderActivity, SingleReturnedOrderConfirmActivityV2::class.java)
                                    intent.putExtra(SingleReturnedOrderConfirmActivityV2.ORDER_ID, batchChosenReturnedOrderBean.orderId)
                                    intent.putExtra(SingleReturnedOrderConfirmActivityV2.DETAIL_ID, batchChosenReturnedOrderBean.detailId)
                                    intent.putExtra(ReturnedOrderConfirmListFragment.RETURNED_ORDER_QUERY_TYPE, item.backStatus)
                                    mAdapter.getAuthority()?.let {
                                        intent.putExtra(ReturnedOrderConfirmListFragment.OPERATION_CONFIG, it.getOperationConfig(item.backStatus))
                                    }
                                    intent.putExtra(SingleReturnedOrderConfirmActivityV2.BACK_ORDER_RECONSIDER_STATE, batchChosenReturnedOrderBean.backOrderReconsiderState)
                                    val lastUptTimeList = SparseArray<String>()
                                    lastUptTimeList.put(batchChosenReturnedOrderBean.detailId.hashCode(), batchChosenReturnedOrderBean.lastUptTime)
                                    startActivityForResult(intent, SINGLE_CONFIRM_REQUEST_CODE)
                                }
                            }
                        }

                        ReturnedOrderConfirmListFragment.TYPE_NOT_CONFIRM,
                        ReturnedOrderConfirmListFragment.TYPE_WAITING_PROCESSING -> {
                            val batchConfirmList = mutableListOf<BatchChosenReturnedOrderBean>()
                            batchConfirmList.add(
                                BatchChosenReturnedOrderBean(
                                    detailId = item.detailId,
                                    money = item.orgBackMoney,
                                    orderId = item.orderId,
                                    lastUptTime = item.lastUptTime,
                                    backOrderReconsiderState = item.backOrderReconsiderState,
                                    goodsSource = item.goodsSource,
                                    settleApplyState = item.settleApplyState
                                )
                            )
                            val batchChosenReturnedOrderBean = batchConfirmList[0]
                            val intent = Intent(this@SearchReturnedOrderActivity, SingleReturnedOrderConfirmActivityV2::class.java)
                            intent.putExtra(SingleReturnedOrderConfirmActivityV2.ORDER_ID, batchChosenReturnedOrderBean.orderId)
                            intent.putExtra(SingleReturnedOrderConfirmActivityV2.DETAIL_ID, batchChosenReturnedOrderBean.detailId)
                            intent.putExtra(ReturnedOrderConfirmListFragment.RETURNED_ORDER_QUERY_TYPE, item.backStatus)
                            mAdapter.getAuthority()?.let {
                                intent.putExtra(ReturnedOrderConfirmListFragment.OPERATION_CONFIG, it.getOperationConfig(item.backStatus))
                            }
                            intent.putExtra(SingleReturnedOrderConfirmActivityV2.BACK_ORDER_RECONSIDER_STATE, batchChosenReturnedOrderBean.backOrderReconsiderState)
                            val lastUptTimeList = SparseArray<String>()
                            lastUptTimeList.put(batchChosenReturnedOrderBean.detailId.hashCode(), batchChosenReturnedOrderBean.lastUptTime)
                            startActivityForResult(intent, SINGLE_CONFIRM_REQUEST_CODE)
                        }
                    }
                }

                R.id.tvReturnedOrderCopy -> {
                    // 把数据集设置（复制）到剪贴板
                    UtilTool.setCopyText(this@SearchReturnedOrderActivity, "订单号", item.orderId)
                    showToast("复制成功")
                }

                R.id.btnLeft,
                R.id.btnMiddle,
                R.id.btnRight -> {
                    if (view is Button) {
                        when (view.text) {
                            ReturnedOrderConfirmListFragment.WAITING_PROCESSING -> {
                                waitingProcessing(item.detailId)
                            }

                            ReturnedOrderConfirmListFragment.APPLY_FOR_BREACH_OF_CONTRACT -> {
                                applyForBreachOfContract(item.orderId, item.specifyFlag, item.goodsSource)
                            }

                            ReturnedOrderConfirmListFragment.RESTART_NEGOTIATE -> {
                                restartNegotiate()
                            }

                            ReturnedOrderConfirmListFragment.AGREE_ADJUST -> {
                                agreeAdjustDetail(item.detailId, "2")
                            }

                            ReturnedOrderConfirmListFragment.REJECTED -> {
                                reject(itemBean = item)
                            }

                            ReturnedOrderConfirmListFragment.UPLOAD_RETURNED_ORDER_PHOTO -> {
                                uploadReturnedOrderPhoto(item.detailId)
                            }
                        }
                    }
                }

                else -> {
                }
            }
        }
    }

    private fun waitingProcessing(detailId: String) {
        ReturnReseonsDialog(this) { dialog, yesNo, reson ->
            dialog?.dismiss()
            if (yesNo) {
                //调用上传原因
                viewModel?.submitWaitingProcessing(WaitingProcessingReq(detailId, reson.toString()))
            } else {
                viewModel?.submitWaitingProcessing(WaitingProcessingReq(detailId, ""))
            }
        }.show()
    }

    private fun reject(itemBean: ReturnedOrderItemBean) {
        getViewModel(BaseViewModel::class.java).execute(ReqQueryConsignorRepulseTag(orderId = itemBean.orderId)) {
            if (it.success()) {
                val list = it.data?.list
                if (list.isNullOrEmpty()) {
                    showDialogToast("未获取到数据")
                    return@execute
                }
                val returnOrderReasonChooseDialog = ReturnOrderReasonChooseDialog(list = list)
                returnOrderReasonChooseDialog.onToastBlock = { toastMsg ->
                    showDialogToast(toastMsg)
                }
                returnOrderReasonChooseDialog.mClickBlock = { choose ->
                    when (choose?.exceptionConfigType) {
                        ExceptionConfigTypeEnum.问题反馈.value,
                        ExceptionConfigTypeEnum.回单打回.value -> {
                            rejected(orderId = itemBean.orderId, detailId = itemBean.detailId, remarks = choose.remarks,exceptionConfigType = choose.exceptionConfigType)
                        }
                        ExceptionConfigTypeEnum.违约申请.value -> {
                            OrderViolateAddActivity.startContentUI(
                                context = this@SearchReturnedOrderActivity,
                                orderId = itemBean.orderId,
                                label = choose.remarks,
                                checkStop = false,
                                requestCode = 0x55
                            )
                        }
                    }
                }
                returnOrderReasonChooseDialog.show(this@SearchReturnedOrderActivity)
            } else {
                showDialogToast(it.msg)
            }
        }
    }

    // 打回
    private fun rejected(orderId: String?, detailId: String?, remarks: String?, exceptionConfigType: String?) {
        ReturnedOrderDeniedReasonActivity.start(
            context = this@SearchReturnedOrderActivity,
            orderId = orderId,
            detailId = detailId,
            remarks = remarks,
            exceptionConfigType = exceptionConfigType,
        )
    }

    // 违约申请
    private fun applyForBreachOfContract(orderId: String?, specifyFlag: String?, goodsSource: String?) {
        OrderViolateListActivity.startContentUI(this, orderId, specifyFlag, goodsSource)
    }

    //重新议价
    private fun restartNegotiate() {

    }

    //同意调整
    private fun agreeAdjustDetail(detailId: String?, userType: String) {
        viewModel?.agreeAdjustDetail(AgreeAdjustDetailReq(detailId, userType))
    }

    //上传回单照片
    private fun uploadReturnedOrderPhoto(detailId: String?) {
        val intent = Intent(this, UploadReturnedOrderPhotoActivity::class.java)
        intent.putExtra(UploadReturnedOrderPhotoActivity.DETAIL_ID, detailId)
        startActivityForResult(intent, UPLOAD_PHOTO)
    }


    @LiveDataMatch
    open fun onReturnedOrderConfirmPageList(data: ReturnedOrderPageList<ReturnedOrderItemBean>?) {
        data?.apply {
            mAdapter.setAuthority(data)
            swipeRefreshMore.onRefreshCompale(data)
        }
    }

    @LiveDataMatch
    open fun agreeAdjustDetailSuccess(data: AgreeAdjustDetailBean) {
        val ft = supportFragmentManager?.beginTransaction()
        val prev = supportFragmentManager?.findFragmentByTag("dialog")
        if (prev != null) {
            ft?.remove(prev)
        }
        ft?.addToBackStack(null)
        val agreeAdjustDialog = AgreeAdjustDetailDialog.instance(data, this)
        ft?.let { agreeAdjustDialog.show(it, "dialog") }
    }

    override fun agreeAdjust(consignorUserId: String?, detailId: String?) {
        viewModel?.agreeAdjust(AgreeAdjustReq(detailId, consignorUserId))
    }

    @LiveDataMatch
    open fun agreeAdjustSuccess(rsp: String) {
        showToast(rsp)
        swipeRefreshMore.onAutoRefresh()
    }

    @RxBusEvent(from = "回单打回通知")
    open fun returnRxAdjust(data: RxAdjust) {
        swipeRefreshMore.onAutoRefresh()
    }

    @RxBusEvent(from = "同意调整")
    open fun returnRxAgreeAdjust(data: RxAgreeAdjust) {
        if (data.success) {
            swipeRefreshMore.onAutoRefresh()
        }
    }

    @LiveDataMatch
    open fun submitWaitingProcessingSuccess() {
        swipeRefreshMore.onAutoRefresh()
    }

    companion object {
        private const val REQUEST_REJECT = 0x33
        private const val REQUEST_COMMIT = 0x32
        private const val UPLOAD_PHOTO = 0x939
        private const val SINGLE_CONFIRM_REQUEST_CODE = 0x947
        private const val HISTORY_SEARCH_DATA = "history_search_data"

        @JvmStatic
        fun start(
            context: Context?,
            showApplyBreachOfContractBtn: Int,
            showRejectedButton: Int,
            queryType: String?,
            operationConfig: OperationConfig?,
            returnOrderConfirmData: ReturnOrderConfirmData
        ) {
            if (context == null) return
            val intent = Intent(context, SearchReturnedOrderActivity::class.java)
            intent.putExtra(ReturnedOrderConfirmListFragment.SHOW_REJECTED_BUTTON, showRejectedButton)
            intent.putExtra("queryType", queryType)
            intent.putExtra("operationConfig", operationConfig)
            intent.putExtra(ReturnedOrderConfirmListFragment.SHOW_APPLY_FOR_BREACH_OF_CONTRACT_BUTTON, showApplyBreachOfContractBtn)
            intent.putExtra("returnOrderConfirmData", returnOrderConfirmData)
            context.startActivity(intent)
        }
    }

}