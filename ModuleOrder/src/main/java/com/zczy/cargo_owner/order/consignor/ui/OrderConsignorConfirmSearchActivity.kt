package com.zczy.cargo_owner.order.consignor.ui

import android.app.Activity
import android.content.Context
import android.content.Intent
import android.os.Bundle
import android.view.KeyEvent
import android.view.View
import android.view.inputmethod.EditorInfo
import android.widget.TextView
import com.chad.library.adapter.base.BaseQuickAdapter
import com.chad.library.adapter.base.listener.OnItemClickListener
import com.jakewharton.rxbinding2.widget.RxTextView
import com.sfh.lib.mvvm.annotation.LiveDataMatch
import com.zczy.cargo_owner.order.R
import com.zczy.cargo_owner.order.consignor.adapter.OrderConsignorConfirmSearchAdapter
import com.zczy.cargo_owner.order.consignor.adapter.Rsp1QueryConsignorOrderStateOfMobileWrapper
import com.zczy.cargo_owner.order.consignor.model.OrderConsignorConfirmSearchModel
import com.zczy.cargo_owner.order.consignor.req.Rsp1QueryConsignorOrderStateOfMobile
import com.zczy.cargo_owner.order.detail.WaybillDetailStatueActivity
import com.zczy.comm.http.entity.PageList
import com.zczy.comm.http.entity.mapWrapper
import com.zczy.comm.ui.BaseActivity
import com.zczy.comm.utils.CommUtils
import com.zczy.comm.utils.dp2px
import com.zczy.comm.utils.keyboard.KeyboardUtil
import com.zczy.comm.widget.pulltorefresh.CommEmptyView
import com.zczy.comm.widget.pulltorefresh.OnLoadingListener
import kotlinx.android.synthetic.main.order_consignor_confirm_search_activity.*

/** 功能描述:
 * 发货单确认 搜索
 * <AUTHOR>
 * @company 中储智运科技股份有限公司
 * @copyright （版权）中储南京智慧物流科技有限公司所有
 * @date 2019/4/3
 */
open class OrderConsignorConfirmSearchActivity : BaseActivity<OrderConsignorConfirmSearchModel>() {

    private val mAdapter = OrderConsignorConfirmSearchAdapter()

    companion object {
        private const val REQUEST_REJECT = 0x33
        private const val REQUEST_COMMIT = 0x32

        @JvmStatic
        fun start(context: Context?) {
            if (context == null) return
            val intent = Intent(context, OrderConsignorConfirmSearchActivity::class.java)
            context.startActivity(intent)
        }
    }

    override fun getLayout(): Int = R.layout.order_consignor_confirm_search_activity

    override fun bindView(bundle: Bundle?) {
        val emptyView = CommEmptyView.creatorDef(this)
        swipe_refresh_more.apply {
            setAdapter(mAdapter, false)
            setEmptyView(emptyView)
            addItemDecorationSize(dp2px(7f))
            addOnItemListener(onItemClickListener)
            setOnLoadListener(object : OnLoadingListener {
                override fun onRefreshUI(nowPage: Int) {
                    val trim = edit_search.text.toString().trim()
                    if (trim.isEmpty()) {
                        showDialogToast("请输入" + edit_search.hint)
                        onLoadMoreFail()
                    } else {
                        viewModel?.getListInfo(nowPage, trim)
                    }
                }

                override fun onLoadMoreUI(nowPage: Int) {
                    val trim = edit_search.text.toString().trim()
                    if (trim.isEmpty()) {
                        showDialogToast("请输入" + edit_search.hint)
                        onLoadMoreFail()
                    } else {
                        viewModel?.getListInfo(nowPage, trim)
                    }
                }
            })
        }
        initEdit()

        bindClickEvent(btn_clear)
        bindClickEvent(btn_cancel)
    }

    override fun initData() {
    }

    override fun onSingleClick(v: View) {
        super.onSingleClick(v)
        when (v.id) {
            R.id.btn_clear -> {
                edit_search.setText("")
            }
            R.id.btn_cancel -> {
                finish()
            }
        }
    }

    override fun onActivityResult(requestCode: Int, resultCode: Int, data: Intent?) {
        super.onActivityResult(requestCode, resultCode, data)
        if (requestCode == Activity.RESULT_OK) {
            when (resultCode) {
                REQUEST_REJECT,
                REQUEST_COMMIT -> {
                    swipe_refresh_more.onAutoRefresh()
                }
            }
        }
    }

    private fun initEdit() {
        RxTextView.textChanges(edit_search)
                .map(CharSequence::toString)
                .subscribe {
                    btn_clear.visibility = if (it.isEmpty()) View.GONE else View.VISIBLE
                }
                .apply {
                    putDisposable(this)
                }

        edit_search.setOnEditorActionListener(object : TextView.OnEditorActionListener {
            override fun onEditorAction(v: TextView, actionId: Int, event: KeyEvent?): Boolean {
                if (actionId == EditorInfo.IME_ACTION_SEARCH) {
                    KeyboardUtil.hideKeyboard(edit_search)
                    val trim = edit_search.text.toString().trim()
                    if (trim.isEmpty()) {
                        showDialogToast("请输入" + edit_search.hint)
                    } else {
                        swipe_refresh_more.onAutoRefresh()
                    }
                    return true
                }
                return false
            }
        })
    }

    private val onItemClickListener = object : OnItemClickListener() {
        override fun onSimpleItemClick(adapter: BaseQuickAdapter<*, *>, view: View, position: Int) {
        }

        override fun onItemChildClick(adapter: BaseQuickAdapter<*, *>, view: View, position: Int) {
            super.onItemChildClick(adapter, view, position)
            val item = adapter.getItem(position)
            if (item is Rsp1QueryConsignorOrderStateOfMobileWrapper) {
                when (view.id) {
                    // 复制
                    R.id.tv_order_copy -> {
                        val isCopy = CommUtils.copyText(this@OrderConsignorConfirmSearchActivity,
                                "运单号", item.data.orderId)
                        showToast(if (isCopy) "复制成功" else "复制失败")
                    }
                    // 确认
                    R.id.tv_confirm -> {
                        OrderConsignorConfirmPreviewActivity
                                .start(this@OrderConsignorConfirmSearchActivity, listOf(item.data), REQUEST_COMMIT)
                    }
                    // 打回
                    R.id.tv_reject -> {
                        OrderConsignorConfirmRejectActivity
                                .start(this@OrderConsignorConfirmSearchActivity, item.data, REQUEST_REJECT)
                    }
                    // 运单号
                    R.id.tv_order_id -> {
                        WaybillDetailStatueActivity.start(this@OrderConsignorConfirmSearchActivity, item.data.orderId)
                    }
                }
            }
        }
    }

    @LiveDataMatch
    open fun onGetListInfo(data: PageList<Rsp1QueryConsignorOrderStateOfMobile>?) {
        swipe_refresh_more.onRefreshCompale(data.mapWrapper { Rsp1QueryConsignorOrderStateOfMobileWrapper(data = it) })
    }
}