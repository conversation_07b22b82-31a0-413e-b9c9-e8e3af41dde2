package com.zczy.cargo_owner.order.settlement.bean

import com.zczy.comm.http.entity.BaseNewRequest
import com.zczy.comm.http.entity.BaseRsp
import com.zczy.comm.http.entity.ResultData

/**
 * 可用优惠券
 * http://wiki.zczy56.com/pages/viewpage.action?pageId=50348926
 */
class ReqQueryConsignorCouponCache(
    var usedTypes: String = "31",//                             #使用节点，结算申请-31
    var freight: String? = "",//                                       #订单金额，缓存时不传
    var orderId: String? = "",//                                     #订单号，缓存时不传
    var orderTime: String? = "",//       ,          #订单时间，缓存时不传
    var noUserCouponIdList: List<String>? = null,//                     #需要排除的用户券ids,List<String>结构
) : BaseNewRequest<BaseRsp<RspConsignorCoupon>>("/mms-app/coupon/queryConsignorCouponCache")

data class RspConsignorCoupon(
    var dataListSize: String? = "",
) : ResultData()

