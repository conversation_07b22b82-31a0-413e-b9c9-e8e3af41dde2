package com.zczy.cargo_owner.order.transport;

import android.content.Context;
import android.content.Intent;
import android.net.Uri;
import android.text.TextUtils;
import android.widget.Toast;

import com.amap.api.maps.model.LatLng;
import com.amap.api.services.core.LatLonPoint;

import java.io.File;

public class Utils {


    public static LatLng convertToLatLng(LatLonPoint latLonPoint) {
        return new LatLng(latLonPoint.getLatitude(), latLonPoint.getLongitude());
    }


    // 高德地图包名
    public static final String PN_GAODE_MAP = "com.autonavi.minimap";
    // 百度地图包名
    public static final String PN_BAIDU_MAP = "com.baidu.BaiduMap";

    /**
     * 打开高德地图导航功能
     *
     * @param context
     * @param slat    起点纬度
     * @param slon    起点经度
     * @param sname   起点名称 可不填（0,0，null）
     * @param dlat    终点纬度
     * @param dlon    终点经度
     * @param dname   终点名称 必填
     */
    public static void openGaoDeNavi(Context context, String slat, String slon, String sname, String dlat, String dlon, String dname) {
        if (isInstallByread(PN_GAODE_MAP)) {
            StringBuilder builder = new StringBuilder("amapuri://route/plan?sourceApplication=maxuslife");
            if (!TextUtils.isEmpty(slat) && !TextUtils.equals("0", slat)) {
                builder.append("&sname=").append(TextUtils.isEmpty(sname) ? "起始地" : sname)
                        .append("&slat=").append(slat)
                        .append("&slon=").append(slon);
            }
            builder.append("&dlat=").append(dlat)
                    .append("&dlon=").append(dlon)
                    .append("&dname=").append(TextUtils.isEmpty(dname) ? "目的地" : dname)
                    .append("&dev=0")
                    .append("&t=0");
            Intent intent = new Intent(Intent.ACTION_VIEW);
            intent.setPackage(PN_GAODE_MAP);
            intent.setData(Uri.parse(builder.toString()));
            context.startActivity(intent);
        } else {
            Toast.makeText(context, "没有安装高德地图客户端，请先下载该地图应用", Toast.LENGTH_SHORT).show();
        }
    }

    /**
     * 判断是否安装目标应用
     *
     * @param packageName 目标应用安装后的包名
     * @return 是否已安装目标应用
     */
    private static boolean isInstallByread(String packageName) {
        return new File("/data/data/" + packageName).exists();
    }

    /**
     * 打开百度地图导航功能(默认坐标点是高德地图，需要转换)
     *
     * @param context
     * @param slat    起点纬度
     * @param slon    起点经度
     * @param sname   起点名称 可不填（0,0，null）
     * @param dlat    终点纬度
     * @param dlon    终点经度
     * @param dname   终点名称 必填
     */
    public static void openBaiDuNavi(Context context, String slat, String slon, String sname, String dlat, String dlon, String dname) {
        if (isInstallByread(PN_BAIDU_MAP)) {
            String uriString = null;

            StringBuilder builder = new StringBuilder("baidumap://map/direction?mode=driving&");
            if (!TextUtils.isEmpty(slat) && !TextUtils.equals("0", slat)) {
                //起点坐标转换
                builder.append("origin=latlng:")
                        .append(slat)
                        .append(",")
                        .append(slon)
                        .append("|name:")
                        .append(TextUtils.isEmpty(sname) ? "起始地" : sname);
            }
            builder.append("&destination=latlng:")
                    .append(dlat)
                    .append(",")
                    .append(dlon)
                    .append("|name:")
                    .append(TextUtils.isEmpty(dname) ? "目的地" : dname);
            uriString = builder.toString();
            Intent intent = new Intent(Intent.ACTION_VIEW);
            intent.setPackage(PN_BAIDU_MAP);
            intent.setData(Uri.parse(uriString));
            context.startActivity(intent);
        } else {
            Toast.makeText(context, "没有安装百度地图客户端，请先下载该地图应用", Toast.LENGTH_SHORT).show();
        }
    }

}
