package com.zczy.cargo_owner.order.confirm.bean

/**
 * @description
 * <AUTHOR> 韩正亚
 * @company 中储智运科技股份有限公司
 * @copyright （版权）中储南京智慧物流科技有限公司所有
 * @date 2019/4/12
 */
data class BatchChosenReturnedOrderBean(
    val detailId: String?,
    val money: String?,
    val orderId: String?,
    val lastUptTime: String,
    val backOrderReconsiderState: String?,
    var goodsSource: String?,
    val settleApplyState: String?,
) {
    override fun toString(): String {
        return "$detailId-$money-$lastUptTime"
    }
}