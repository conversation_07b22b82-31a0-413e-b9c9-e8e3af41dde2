package com.zczy.cargo_owner.order.violate

import android.annotation.SuppressLint
import android.app.Activity
import android.content.Intent
import android.graphics.Color
import android.graphics.Typeface
import android.os.Bundle
import android.text.TextUtils
import android.view.Gravity
import android.view.View
import com.jakewharton.rxbinding2.view.RxView
import com.jakewharton.rxbinding2.widget.RxTextView
import com.sfh.lib.mvvm.annotation.LiveDataMatch
import com.sfh.lib.mvvm.service.BaseViewModel
import com.sfh.lib.rx.ui.UtilRxView
import com.sfh.lib.ui.dialog.DialogBuilder
import com.zczy.cargo_owner.libcomm.DeliverProvider
import com.zczy.cargo_owner.libcomm.config.HttpConfig
import com.zczy.cargo_owner.libcomm.widget.CheckSelfPermissionDialog
import com.zczy.cargo_owner.order.R
import com.zczy.cargo_owner.order.confirm.bean.isNotNullOrNotEmpty
import com.zczy.cargo_owner.order.confirm.bean.isNotNullOrNotEmptyAddBigZero
import com.zczy.cargo_owner.order.violate.model.OrderViolateListModel
import com.zczy.cargo_owner.order.violate.req.EBreachUserName
import com.zczy.cargo_owner.order.violate.req.EViolateType
import com.zczy.cargo_owner.order.violate.req.ReqAddBreach
import com.zczy.cargo_owner.order.violate.req.ReqCheckAccountBalance
import com.zczy.cargo_owner.order.violate.req.ReqCheckHandleConsultation
import com.zczy.cargo_owner.order.violate.req.ReqCheckOrderChangeInfo
import com.zczy.cargo_owner.order.violate.req.RspAddBreach
import com.zczy.cargo_owner.order.violate.req.RspCheckOrderChangeInfo
import com.zczy.comm.CommServer
import com.zczy.comm.data.entity.EImage
import com.zczy.comm.data.entity.EProcessFile
import com.zczy.comm.permission.PermissionCallBack
import com.zczy.comm.permission.PermissionUtil.openAlbum
import com.zczy.comm.ui.BaseActivity
import com.zczy.comm.utils.NumUtil
import com.zczy.comm.utils.ex.isTrue
import com.zczy.comm.utils.ex.setVisible
import com.zczy.comm.utils.imageselector.ImagePreviewActivity
import com.zczy.comm.utils.imageselector.ImageSelectProgressView
import com.zczy.comm.utils.imageselector.ImageSelector
import com.zczy.comm.widget.dialog.MenuDialogV1
import com.zczy.comm.widget.inputv2.InputViewCheckV2
import com.zczy.comm.widget.inputv2.InputViewClick
import io.reactivex.android.schedulers.AndroidSchedulers
import kotlinx.android.synthetic.main.order_violate_add_activity.btn_commit
import kotlinx.android.synthetic.main.order_violate_add_activity.check_isStop
import kotlinx.android.synthetic.main.order_violate_add_activity.check_money_type
import kotlinx.android.synthetic.main.order_violate_add_activity.ed_description
import kotlinx.android.synthetic.main.order_violate_add_activity.image_select_view
import kotlinx.android.synthetic.main.order_violate_add_activity.input_money_v2
import kotlinx.android.synthetic.main.order_violate_add_activity.iv_my
import kotlinx.android.synthetic.main.order_violate_add_activity.iv_ta
import kotlinx.android.synthetic.main.order_violate_add_activity.ll_personal_safety_money
import kotlinx.android.synthetic.main.order_violate_add_activity.ll_policy_money
import kotlinx.android.synthetic.main.order_violate_add_activity.ll_zeren_money
import kotlinx.android.synthetic.main.order_violate_add_activity.rb_my
import kotlinx.android.synthetic.main.order_violate_add_activity.rb_ta
import kotlinx.android.synthetic.main.order_violate_add_activity.tv_more_size
import kotlinx.android.synthetic.main.order_violate_add_activity.tv_my
import kotlinx.android.synthetic.main.order_violate_add_activity.tv_name
import kotlinx.android.synthetic.main.order_violate_add_activity.tv_personal_safety_money
import kotlinx.android.synthetic.main.order_violate_add_activity.tv_ta
import kotlinx.android.synthetic.main.order_violate_add_activity.tv_zeren
import kotlinx.android.synthetic.main.order_violate_add_activity.viewPolicyMoney
import kotlinx.android.synthetic.main.order_violate_add_activity.viewPolicyMoneyV1
import kotlinx.android.synthetic.main.order_violate_add_activity.view_all_money
import kotlinx.android.synthetic.main.order_violate_add_activity.view_money_v2
import kotlinx.android.synthetic.main.order_violate_add_activity.view_policyMoney
import kotlinx.android.synthetic.main.order_violate_add_activity.view_type
import java.io.File
import java.util.Objects
import java.util.concurrent.TimeUnit

/**
 *  desc: 运单添加违约
 *  user: 宋双朋
 *  time: 2024/8/20 19:44
 */
class OrderViolateAddActivity : BaseActivity<OrderViolateListModel>() {
    private var selectViolateType: EViolateType? = null
    private val addBreach = ReqAddBreach()
    private var breachUserName: EBreachUserName? = null
    private val orderId by lazy { intent.getStringExtra(ORDER_ID) ?: "" }
    private val isStopCheck by lazy { intent.getBooleanExtra(CHECK_IS_STOP, false) }
    private val specifyFlag by lazy { intent.getStringExtra(SPECIFY_FLAG) ?: "" }
    private val goodsSource by lazy { intent.getStringExtra(GOODS_SOURCE) ?: "" }
    private val label by lazy { intent.getStringExtra(LABEL) }
    private var modeType: String? = null


    companion object {
        const val ORDER_ID = "orderId"
        const val LABEL = "label"
        const val CHECK_IS_STOP = "checkIsStop"
        const val SPECIFY_FLAG = "specifyFlag"
        const val GOODS_SOURCE = "goodsSource"
        fun startContentUI(context: Activity, orderId: String?, label: String?, checkStop: Boolean, requestCode: Int) {
            val intent = Intent(context, OrderViolateAddActivity::class.java)
            intent.putExtra(ORDER_ID, orderId)
            intent.putExtra(LABEL, label)
            intent.putExtra(CHECK_IS_STOP, checkStop)
            context.startActivityForResult(intent, requestCode)
        }

        @JvmStatic
        fun startContentUI(context: Activity, orderId: String?, checkStop: Boolean, specifyFlag: String?, goodsSource: String?, requestCode: Int) {
            val intent = Intent(context, OrderViolateAddActivity::class.java)
            intent.putExtra(ORDER_ID, orderId)
            intent.putExtra(CHECK_IS_STOP, checkStop)
            intent.putExtra(SPECIFY_FLAG, specifyFlag)
            intent.putExtra(GOODS_SOURCE, goodsSource)
            context.startActivityForResult(intent, requestCode)
        }
    }

    override fun getLayout(): Int {
        return R.layout.order_violate_add_activity
    }

    override fun bindView(bundle: Bundle?) {
        initView()
    }

    override fun initData() {
        addBreach.label = label
    }

    private fun initView() {

        val myListener = View.OnClickListener { showViolateType(true) }
        iv_my.setOnClickListener(myListener)
        rb_my.setOnClickListener(myListener)

        val taListener = View.OnClickListener { showViolateType(false) }
        iv_ta.setOnClickListener(taListener)
        rb_ta.setOnClickListener(taListener)

        //违约保费
        ll_policy_money.setVisible(false)

        //违约金额

        val disposable2 = UtilRxView.afterTextChangeEvents(input_money_v2, 700) { charSequence ->
            if (TextUtils.isEmpty(charSequence)) {
                addBreach.breachMoney = "0"
            } else {
                addBreach.breachMoney = charSequence.toString()
            }
            showsPolicyMoney()
        }
        putDisposable(disposable2)

        val disposable = RxTextView.textChanges(ed_description).observeOn(AndroidSchedulers.mainThread()).subscribe { charSequence -> tv_more_size.text = charSequence.length.toString() + "/200" }
        this.putDisposable(disposable)

        val disposable1 = RxView.clicks(btn_commit).throttleFirst(1000, TimeUnit.MILLISECONDS).observeOn(AndroidSchedulers.mainThread()).subscribe {
            if (addBreach.breachUserId.isNullOrEmpty()) {
                showDialogToast("违约方不能为空")
                return@subscribe
            }
            if (selectViolateType == null) {
                showDialogToast("请选择违约类型")
                return@subscribe
            }

            if (addBreach.isStop.isNullOrEmpty()) {
                showDialogToast("请选择是否终止运输")
                //必须选择终止运输
                if (isStopCheck && !TextUtils.equals("1", addBreach.isStop)) {
                    showDialogToast("请选择终止运输")
                }
                return@subscribe
            }

            if (view_money_v2.visibility == View.VISIBLE) {
                val breachMoney = addBreach.breachMoney?.toFloatOrNull() ?: 0.0f
                if (breachMoney > 500.0f || breachMoney < 1.0f) {
                    showDialogToast("请输入1-500整数")
                    return@subscribe
                }
            } else {
                addBreach.breachMoney = "0"
            }

            addBreach.remark = ed_description.text.toString()

            val files = image_select_view.getDataList()
            if (files.isNotEmpty()) {
                val url = StringBuilder(100)
                for (processFile in files) {
                    url.append(processFile.imagUrl).append(",")
                }
                //图片数据
                addBreach.fileIds = url.substring(0, url.length - 1)
            }
            if (rb_ta.isChecked) {
                checkHandleConsultation()
            } else {
                getViewModel(BaseViewModel::class.java).execute(
                    ReqCheckAccountBalance(
                        orderId = orderId,
                        breachMoney = addBreach.breachMoney,
                        isStop = addBreach.isStop,
                        breachPolicyMoney = addBreach.breachPolicyMoney,
                        freightLossPolicyMoney = breachUserName?.person?.freightLossPolicyMoney,
                        personalAccidentPolicyMoney = breachUserName?.person?.personalAccidentPolicyMoney,
                    )
                ) { rsp ->
                    runOnUiThread {
                        if (rsp.success()) {
                            if (rsp.data?.insufficientFlag.isTrue) {
                                val builder = DialogBuilder()
                                builder.setTitle("温馨提示")
                                    .setMessage(rsp.data?.resultMsg)
                                    .setGravity(Gravity.CENTER)
                                    .setHideCancel(false)
                                    .setOkListener { dialog, _ ->
                                        dialog?.dismiss()
                                        checkHandleConsultation()
                                    }
                                    .setOKText("确定")
                                showDialog(builder)
                            } else {
                                checkHandleConsultation()
                            }
                        } else {
                            showToast(rsp.msg)
                        }
                    }
                }
            }
        }
        this.putDisposable(disposable1)
        view_type.tvTitle.typeface = Typeface.DEFAULT_BOLD
        view_type.setListener(object : InputViewClick.Listener() {
            override fun onClick(i: Int, inputViewClick: InputViewClick, s: String) {
                if (breachUserName == null) {
                    return
                }
                val violateTypes = if (rb_ta.isChecked) {
                    breachUserName?.type
                } else {
                    breachUserName?.typeOwner
                }
                if (violateTypes == null) {
                    return
                }
                //查询违约类型
                MenuDialogV1.instance(violateTypes)
                    .setClick { eViolateType: EViolateType, _: Int ->
                        selectViolateType = eViolateType
                        addBreach.breachTypeId = eViolateType.id
                        addBreach.breachTypeName = eViolateType.value
                        addBreach.breachMoney = eViolateType.memo
                        view_type.setContent(eViolateType.value ?: "")
                    }
                    .show(this@OrderViolateAddActivity)
            }
        })
        //必须选择终止
        if (isStopCheck) {
            check_isStop.setCheck(InputViewCheckV2.LEFT)
            addBreach.isStop = "1"
            check_money_type.setCheck(InputViewCheckV2.RIGHT)
            if (TextUtils.equals(modeType, "1")) {
                check_money_type.setCanClick(false)
            } else {
                check_money_type.setCanClick(true)
            }
            view_money_v2.setVisible(false)
            input_money_v2.setText("")
        } else {
            check_isStop.setCheck(InputViewCheckV2.NONE)
        }
        check_isStop.tvTitle.typeface = Typeface.DEFAULT_BOLD
        check_isStop.setListener(object : InputViewCheckV2.Listener() {
            @SuppressLint("SetTextI18n")
            override fun onClickCheck(viewId: Int, view: InputViewCheckV2, check: Int, radioStr: String): Boolean {
                if (check == InputViewCheckV2.LEFT) {
                    addBreach.isStop = "1"
                    if (TextUtils.equals(modeType, "1")) {
                        check_money_type.setCanClick(false)
                    } else {
                        check_money_type.setCanClick(true)
                    }
                    check_money_type.setCheck(InputViewCheckV2.RIGHT)
                    view_money_v2.setVisible(false)
                    input_money_v2.setText("")

                    if (breachUserName != null && breachUserName?.person?.isPersonalAccidentPolicy == true) {
                        //ZCZY-8163 司机单次人身意外保障服务
                        ll_personal_safety_money.setVisible(true)
                        tv_personal_safety_money.text = breachUserName?.person?.personalAccidentPolicyMoney + "元"
                    }
                    viewPolicyMoney.setVisible(breachUserName?.person?.freightLossPolicyMoney?.isNotNullOrNotEmptyAddBigZero() ?: false)
                    viewPolicyMoneyV1.text = breachUserName?.person?.freightLossPolicyMoney + "元"
                } else {
                    if (TextUtils.equals(modeType, "1")) {
                        addBreach.isStop = "0"
                        check_money_type.setCheck(InputViewCheckV2.RIGHT)
                        view_money_v2.setVisible(false)
                        input_money_v2.setText("")
                        check_money_type.setCanClick(false)
                        ll_personal_safety_money.setVisible(false)
                        viewPolicyMoney.setVisible(false)
                    } else {
                        addBreach.isStop = "0"
                        check_money_type.setCheck(InputViewCheckV2.LEFT)
                        check_money_type.setCanClick(false)
                        view_money_v2.setVisible(true)
                        input_money_v2.setText("")
                        ll_personal_safety_money.setVisible(false)
                        viewPolicyMoney.setVisible(false)
                    }
                }
                showsPolicyMoney()
                return true
            }
        })
        check_money_type.tvTitle.typeface = Typeface.DEFAULT_BOLD
        check_money_type.setListener(object : InputViewCheckV2.Listener() {
            override fun onClickCheck(viewId: Int, view: InputViewCheckV2, check: Int, radioStr: String): Boolean {
                if (check == InputViewCheckV2.LEFT) {
                    view_money_v2.setVisible(true)
                    ll_zeren_money.setVisible(true)
                } else {
                    view_money_v2.setVisible(false)
                    ll_zeren_money.setVisible(false)
                }
                input_money_v2.setText("")
                return true
            }
        })

        image_select_view.setOnItemSelectListener(object : ImageSelectProgressView.OnItemSelectListener {
            override fun onSelectImageClick(surplus: Int) {
                CheckSelfPermissionDialog.cameraPermissionDialog(this@OrderViolateAddActivity, object : PermissionCallBack() {
                    override fun onHasPermission() {
                        openAlbum(
                            this@OrderViolateAddActivity,
                            object : PermissionCallBack() {
                                override fun onHasPermission() {
                                    ImageSelector.open(this@OrderViolateAddActivity, surplus, true, 1002)
                                }
                            })
                    }
                })
            }

            override fun onUpImageClick(file: String) {
            }

            override fun onLookImageClick(file: List<EProcessFile>, position: Int) {
                //查看大图
                val list: MutableList<EImage> = ArrayList(file.size)
                for (processFile in file) {
                    val image = EImage()
                    image.netUrl = HttpConfig.getUrlImage(processFile.imagUrl)
                    list.add(image)
                }
                ImagePreviewActivity.start(this@OrderViolateAddActivity, list, position)
            }

            override fun onDelateClick(position: Int) {
                val dialogBuilder = DialogBuilder()
                dialogBuilder.setMessage("确定删除当前图片吗？")
                dialogBuilder.setOkListener { dialogInterface: DialogBuilder.DialogInterface, i: Int ->
                    dialogInterface.dismiss()
                    image_select_view.deleteImage(position)
                }
                showDialog(dialogBuilder)
            }
        })
        addBreach.orderId = orderId
        viewModel.queryBreachUserName(orderId)
    }

    private fun checkHandleConsultation() {
        getViewModel(BaseViewModel::class.java).execute(ReqCheckHandleConsultation(orderId = orderId)) { rsp ->
            runOnUiThread {
                if (rsp.success()) {
                    if (rsp.data?.flag == true) {
                        val builder = DialogBuilder()
                        builder.setTitle("温馨提示")
                            .setMessage("该运单平台已在违约处理中，是否仍要发起违约申请？")
                            .setGravity(Gravity.CENTER)
                            .setCancelText("暂不发起")
                            .setOKText("仍要发起")
                            .setOkListener { dialogInterface: DialogBuilder.DialogInterface, i: Int ->
                                dialogInterface.dismiss()
                                doReq()
                            }
                        showDialog(builder)
                    } else {
                        doReq()
                    }
                } else {
                    showToast(rsp.msg)
                }
            }
        }
    }

    private fun doReq() {
        if (TextUtils.equals("1", addBreach.isStop) && addBreach.breachPolicyMoney.isNotNullOrNotEmptyAddBigZero()) {
            val builder = DialogBuilder()
            builder.setTitle("温馨提示")
                .setMessage("此单包含转嫁保险，如果违约成功则违约责任方承担保险费")
                .setGravity(Gravity.CENTER)
                .setOkListener { dialogInterface: DialogBuilder.DialogInterface, i: Int ->
                    dialogInterface.dismiss()
                    viewModel.checkOrderChangeInfo(ReqCheckOrderChangeInfo(orderId, addBreach.isStop.isTrue))
                }
            showDialog(builder)
        } else {
            viewModel.checkOrderChangeInfo(ReqCheckOrderChangeInfo(orderId, addBreach.isStop.isTrue))
        }
    }

    /**
     * 摘单6小时取消运单提示框
     */
    private fun showSixHoursTipsDialog() {
        val builder = DialogBuilder()
        builder.setTitle("温馨提示")
            .setMessage("此运单已购买人身安全保障服务，摘单6小时内发起取消运单服务会自动终止，请确认是否发起")
            .setGravity(Gravity.CENTER)
            .setOkListener { dialogInterface: DialogBuilder.DialogInterface, i: Int ->
                dialogInterface.dismiss()
                Objects.requireNonNull(viewModel).addViolate(addBreach)
            }
        showDialog(builder)
    }


    public override fun onActivityResult(requestCode: Int, resultCode: Int, data: Intent?) {
        super.onActivityResult(requestCode, resultCode, data)
        if (1002 == requestCode && resultCode == RESULT_OK) {
            val file = ImageSelector.obtainPathResult(data)
            image_select_view.onUpLoadStart(file)
            this.viewModel.upFile(file)
        }
    }

    @LiveDataMatch(tag = "判断该订单是否在变更中")
    open fun checkOrderChangeInfoSuccess(rsp: RspCheckOrderChangeInfo?) {
        rsp?.let {
            // 1-运单承运人变更中 2-运单信息变更中且终止运单 3-运单信息变更中不终止运单 4-不存在变更
            when (it.flag) {
                "1" -> {
                    val dialogBuilder = DialogBuilder()
                    dialogBuilder.setMessage("运单承运人变更中，无法发起违约申请。")
                    dialogBuilder.setHideCancel(true)
                    dialogBuilder.setOKTextListener("确定") { dialog: DialogBuilder.DialogInterface, _: Int -> dialog.dismiss() }
                    showDialog(dialogBuilder)
                }

                "2" -> {
                    val dialogBuilder = DialogBuilder()
                    dialogBuilder.setMessage("运单信息变更中，运单取消后将无法变更运单信息！")
                    dialogBuilder.setTitle("提示")
                    dialogBuilder.setOKText("确定").setOkListener { dialogInterface: DialogBuilder.DialogInterface, i: Int ->
                        dialogInterface.dismiss()
                        if (breachUserName?.person?.personalAccidentPolicyMoneyFlag == true) {
                            showSixHoursTipsDialog()
                        } else {
                            getViewModel(OrderViolateListModel::class.java).addViolate(addBreach)
                        }
                    }
                    showDialog(dialogBuilder)
                }

                else -> {
                    if (breachUserName?.person?.personalAccidentPolicyMoneyFlag == true) {
                        showSixHoursTipsDialog()
                    } else {
                        getViewModel(OrderViolateListModel::class.java).addViolate(addBreach)
                    }
                }
            }
        }
    }

    @LiveDataMatch(tag = "上传文件成功")
    open fun onFileSuccess(tag: File, url: String?) {
        image_select_view.onUpLoadFileSuccess(tag.absolutePath, url)
    }

    @LiveDataMatch(tag = "上传文件失败")
    open fun onFileFailure(tag: File, error: String?) {
        image_select_view.onUpLoadFileError(tag.absolutePath)
    }


    @SuppressLint("SetTextI18n")
    @LiveDataMatch(tag = "违约方名称")
    open fun onBreachUserNameSuccess(breachUserName: EBreachUserName?) {
        if (breachUserName != null) {
            this.breachUserName = breachUserName
            val user = breachUserName.person
            addBreach.breachUserId = user?.breachUserId
            addBreach.breachUserName = user?.breachUserName
            addBreach.breachType = user?.breachType
            addBreach.breachPolicyMoney = user?.breachPolicyMoney
            //默认自己违约
            this.showViolateType(true)
            if (check_isStop.check == InputViewCheckV2.LEFT && breachUserName.person?.freightLossPolicyMoney?.isNotNullOrNotEmpty() == true) {
                viewPolicyMoney.setVisible(true)
                viewPolicyMoneyV1.text = breachUserName.person?.freightLossPolicyMoney + "元"
            }

            modeType = breachUserName.modeType
            if (TextUtils.equals(breachUserName.modeType, "1")) {
                check_money_type.setCheck(InputViewCheckV2.RIGHT)
                check_money_type.setCanClick(false)
                view_money_v2.setVisible(false)
                input_money_v2.setText("")
                ll_zeren_money.setVisible(false)
            }

        }
    }


    private fun showViolateType(me: Boolean) {
        if (me) {
            //违约方-自己
            iv_my.setBackgroundResource(R.drawable.order_violate_me)
            rb_my.isChecked = true
            tv_my.setTextColor(Color.parseColor("#ff5086fc"))

            iv_ta.setBackgroundResource(R.drawable.order_violate_ta_un)
            rb_ta.isChecked = false
            tv_ta.setTextColor(Color.parseColor("#ff666666"))

            //赔偿对方违约金
            check_money_type.title = "赔偿对方责任金"
            val login = CommServer.getUserServer().login
            if (login != null) {
                tv_name.text = login.memberName
                addBreach.breachUserId = login.userId
                addBreach.breachUserName = login.memberName
                addBreach.breachType = login.userType
                addBreach.breachUserType = login.userType
            }
            val user = breachUserName?.person
            if (user != null) {
                //取承运方的保障服务费
                addBreach.breachPolicyMoney = user.carrierPolicyMoney
            }
        } else {
            //违约方-对方
            iv_my.setBackgroundResource(R.drawable.order_violate_me_un)
            rb_my.isChecked = false
            tv_my.setTextColor(Color.parseColor("#ff666666"))

            iv_ta.setBackgroundResource(R.drawable.order_violate_ta)
            rb_ta.isChecked = true
            tv_ta.setTextColor(Color.parseColor("#ff5086fc"))

            //申请违约金
            check_money_type.title = "申请责任金"

            val user = breachUserName?.person
            if (user != null) {
                tv_name.text = user.breachUserName
                addBreach.breachUserId = user.breachUserId
                addBreach.breachUserName = user.breachUserName
                addBreach.breachType = user.breachType
                addBreach.breachUserType = user.breachType
                //取货主的保障服务费
                addBreach.breachPolicyMoney = user.breachPolicyMoney
            }
        }
        this.selectViolateType = null
        addBreach.breachTypeId = ""
        addBreach.breachTypeName = ""
        addBreach.breachMoney = ""
        view_type.content = ""
        input_money_v2.setText("")
    }

    @LiveDataMatch(tag = "新增违约成功")
    open fun onAddViolateSuccess(data: RspAddBreach?) {
        setResult(RESULT_OK)
        if (data != null && TextUtils.equals("1", data.specifyFlag)) {
            val builder = DialogBuilder()
            builder.setMessage(data.resultMsg)
            builder.setHideCancel(true)
            builder.setTitle("温馨提示")
            builder.setCancelText("我知道了")
            builder.setCancelListener { dialogInterface: DialogBuilder.DialogInterface, i: Int ->
                dialogInterface.dismiss()
                finish()
            }
            showDialog(builder)
        } else {
            val builder = DialogBuilder()
            builder.setMessage("操作成功！您可点击【再来一单】重新发布运单。")
            builder.setHideCancel(false)
            builder.setTitle("温馨提示")
            builder.setCancelText("知道了")
            builder.setCancelListener { dialogInterface: DialogBuilder.DialogInterface, i: Int ->
                dialogInterface.dismiss()
                finish()
            }
            builder.setOKTextColor("再来一单", R.color.text_blue)
            builder.setOkListener { dialogInterface: DialogBuilder.DialogInterface, i: Int ->
                dialogInterface.dismiss()
                val deliver = DeliverProvider.deliver
                if (deliver != null) {
                    deliver.openDeliverDraftsEditActivity(
                        activity = this@OrderViolateAddActivity,
                        pOrderId = orderId,
                        pSpecifyFlag = specifyFlag,
                        requestCode = 0x101,
                        goodsSource = goodsSource
                    )
                    finish()
                }
            }
            showDialog(builder)
        }
    }

    @SuppressLint("SetTextI18n")
    private fun showsPolicyMoney() {
        // ZCZY-9196 货主投保运单违约优化
        if (addBreach.isStop.isTrue && TextUtils.equals("1", breachUserName?.isShowMoney)) {
            // 有 违约保费
            if (rb_my.isChecked) {
                view_policyMoney.text = breachUserName?.person?.breachPolicyMoney + "元"
                ll_policy_money.setVisible(breachUserName?.person?.breachPolicyMoney.isNotNullOrNotEmptyAddBigZero())
            } else if (rb_ta.isChecked) {
                view_policyMoney.text = breachUserName?.person?.breachPolicyMoney + "元"
                ll_policy_money.setVisible(breachUserName?.person?.breachPolicyMoney.isNotNullOrNotEmptyAddBigZero())
            }
        } else {
            ll_policy_money.setVisible(false)
        }
        var totalB = 0.00
        if (addBreach.isStop.isTrue) {
            //取消运单
            if (TextUtils.equals("1", breachUserName?.isShowMoney)) {
                if (rb_my.isChecked) {
                    totalB = NumUtil.sum(totalB, addBreach.breachMoney?.toDoubleOrNull() ?: 0.00)
                    totalB = NumUtil.sum(totalB, breachUserName?.person?.breachPolicyMoney?.toDoubleOrNull() ?: 0.00)
                } else if (rb_ta.isChecked) {
                    totalB = NumUtil.sum(totalB, addBreach.breachMoney?.toDoubleOrNull() ?: 0.00)
                    totalB = NumUtil.sum(totalB, breachUserName?.person?.carrierPolicyMoney?.toDoubleOrNull() ?: 0.00)
                }
            }
            totalB = NumUtil.sum(totalB, breachUserName?.person?.personalAccidentPolicyMoneyNumber?.toDoubleOrNull() ?: 0.00)
            totalB = NumUtil.sum(totalB, breachUserName?.person?.freightLossPolicyMoney?.toDoubleOrNull() ?: 0.00)
        } else {
            totalB = NumUtil.sum(totalB, addBreach.breachMoney?.toDoubleOrNull() ?: 0.00)
        }
        //违约金额 = 违约金额（责任金）+违约保费+人身保障金额
        view_all_money.text = "合计：${totalB}元"
        tv_zeren.text = addBreach.breachMoney + "元"
    }
}
