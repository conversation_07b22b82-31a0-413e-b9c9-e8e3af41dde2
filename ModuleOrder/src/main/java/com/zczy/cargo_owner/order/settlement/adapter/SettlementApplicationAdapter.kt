package com.zczy.cargo_owner.order.settlement.adapter

import android.widget.TextView
import com.chad.library.adapter.base.BaseQuickAdapter
import com.chad.library.adapter.base.BaseViewHolder
import com.zczy.cargo_owner.order.R
import com.zczy.cargo_owner.order.settlement.SettlementApplicationListFragment.Companion.TYPE_NOT_CONFIRM
import com.zczy.cargo_owner.order.settlement.SettlementApplicationListFragment.Companion.TYPE_REJECTED
import com.zczy.cargo_owner.order.settlement.bean.SettlementApplicationItemBean
import com.zczy.cargo_owner.order.settlement.bean.formatPlace
import com.zczy.cargo_owner.order.settlement.bean.getCouponMoney
import com.zczy.cargo_owner.order.settlement.bean.getReturnMoneyTime
import com.zczy.cargo_owner.order.settlement.bean.showGoodsSource
import com.zczy.cargo_owner.order.settlement.bean.showReturnMoneyTime
import com.zczy.cargo_owner.order.settlement.bean.showSelfComment
import com.zczy.comm.SpannableHepler
import com.zczy.comm.utils.ex.isTrue
import com.zczy.comm.utils.ex.toDoubleRoundDownString

/**
 *@Desc 结算申请列表
 *@User ssp
 *@Date 2023/8/18-17:01
 */

class SettlementApplicationAdapter(var checkBoxVisible: Boolean = true) : BaseQuickAdapter<SettlementApplicationItemBean, BaseViewHolder>(R.layout.order_settlement_application_list_item) {

    private var mStatus: Int? = 0;
    override fun convert(helper: BaseViewHolder?, item: SettlementApplicationItemBean) {
        helper?.let {
            it.setText(R.id.tvCoupons, item.getCouponMoney())
            it.setText(R.id.tvSettleErrorMsg, item.remark)
            it.setGone(R.id.tvSettleErrorMsg, !item.remark.isNullOrEmpty())
            val tvPlaceOfStart = it.getView<TextView>(R.id.tvPlaceOfStart)
            val tvPlaceOfEnd = it.getView<TextView>(R.id.tvPlaceOfEnd)

            tvPlaceOfStart.text = item.formatPlace(place = item.startPlace ?: "")
            tvPlaceOfEnd.text = item.formatPlace(place = item.endPlace ?: "")

            /*集装箱货源展示判断*/
            item.let { item1 ->
                if (item1.showGoodsSource()) {
                    it.setText(R.id.tvRemainingBalance, item1.consignorCheckMoney.toDoubleRoundDownString())
                        .setText(R.id.tvWaybillGoodsInfo, item1.allCargoName + " | 发单运费：" + item1.orgBackMoney.toDoubleRoundDownString() + "元")
                        .setText(R.id.tvExpiryDateStr, "回款到期日：" + item1.getReturnMoneyTime())
                        .setGone(R.id.tvExpiryDateStr, item1.showReturnMoneyTime())
                } else {
                    it.setText(R.id.tvRemainingBalance, item1.consignorCheckMoney.toDoubleRoundDownString())
                        .setText(R.id.tvWaybillGoodsInfo, item1.allCargoName + " | " + item1.slipLoad + " | 发单运费：" + item1.orgBackMoney.toDoubleRoundDownString() + "元")
                        .setText(R.id.tvExpiryDateStr, "回款到期日：" + item1.getReturnMoneyTime())
                        .setGone(R.id.tvExpiryDateStr, item1.showReturnMoneyTime())
                }
            }
            val styledText = String.format(mContext.resources.getString(R.string.order_carrier_info), item.carrierCustomerName, item.plateNumber)
            it.setText(R.id.tvWaybillDriverInfo, styledText)
                .setText(R.id.tvSelfComment, item.showSelfComment())
                .setGone(R.id.tvSelfComment, true)
            if (item.specifyFlag.isTrue) {
                if (item.hzAdvanceMoney.isNullOrEmpty()) {
                    it.setText(R.id.tvPrepaymentAmount, "预支付金额：0.00元")
                } else {
                    it.setText(R.id.tvPrepaymentAmount, "预支付金额：${item.hzAdvanceMoney}元")
                }
                it.setGone(R.id.tvPrepaymentAmount, true)
            } else {
                it.setGone(R.id.tvPrepaymentAmount, false)
            }
            it.setGone(R.id.imgOilCard, item.oilCardFlag.isTrue)
                .setGone(R.id.imgReturnedOrderPay, item.receiptFlag.isTrue)
                .setGone(R.id.imgPrepaid, item.advenceFlag.isTrue)
                .setGone(R.id.imgRisk, item.riskReceiveValue.isTrue)
                .setGone(R.id.imgCallBack, item.isBackUpFlag.isTrue)
                .setGone(R.id.ivLTLOrderFlag, item.lTLOrder.isTrue)
            if (checkBoxVisible) {
                it.addOnClickListener(R.id.cbSettlementApplication)
                    .setChecked(R.id.cbSettlementApplication, item.selected)
            } else {
                it.setGone(R.id.cbSettlementApplication, checkBoxVisible)
            }
            val tvSettlementPlatform = it.getView<TextView>(R.id.tvSettlementPlatform)
            val sh = SpannableHepler().append(SpannableHepler.Txt("结算平台：", "#666666"))
                .append(SpannableHepler.Txt(item.consignorSubsidiaryName, "#5086FC"))
                .builder()
            tvSettlementPlatform.text = sh
            it.addOnClickListener(R.id.clCoupons).setText(R.id.tvOrderId, item.orderId).addOnClickListener(R.id.tvReturnedOrderCopy)
            if(mStatus == TYPE_NOT_CONFIRM || mStatus == TYPE_REJECTED){
                helper.setGone(R.id.clCoupons, false)
            } else {
                helper.setGone(R.id.clCoupons, true)
            }
        }
    }

    fun selectAll() {
        data.forEach {
            it.selected = true
        }
        notifyDataSetChanged()
    }

    fun getArrayListAll(): ArrayList<SettlementApplicationItemBean> {
        val list = arrayListOf<SettlementApplicationItemBean>()
        data.forEach {
            list.add(it)
        }
        return list
    }

    fun unSelectAll() {
        data.forEach {
            it.selected = false
        }
        notifyDataSetChanged()
    }

    fun setStatus(status: Int) {
        this.mStatus = status
    }
}