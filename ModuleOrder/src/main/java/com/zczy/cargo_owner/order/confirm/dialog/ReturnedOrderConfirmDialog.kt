package com.zczy.cargo_owner.order.confirm.dialog

import android.annotation.SuppressLint
import android.graphics.Color
import android.os.Bundle
import android.text.Html
import android.text.InputFilter
import android.text.TextUtils
import android.view.View
import android.widget.TextView
import com.sfh.lib.rx.ui.UtilRxView
import com.zczy.cargo_owner.order.R
import com.zczy.cargo_owner.order.confirm.bean.SingleReturnedOrderConfirmMsgRes
import com.zczy.comm.ui.BaseDialog
import com.zczy.comm.utils.ex.setVisible
import kotlinx.android.synthetic.main.order_returned_friend_tips_dialog.btnCancel
import kotlinx.android.synthetic.main.order_returned_friend_tips_dialog.btnConfirm
import kotlinx.android.synthetic.main.order_returned_friend_tips_dialog.ed_description
import kotlinx.android.synthetic.main.order_returned_friend_tips_dialog.noteLayout
import kotlinx.android.synthetic.main.order_returned_friend_tips_dialog.tvCheckV1
import kotlinx.android.synthetic.main.order_returned_friend_tips_dialog.tvCheckV2
import kotlinx.android.synthetic.main.order_returned_friend_tips_dialog.tvCheckV3
import kotlinx.android.synthetic.main.order_returned_friend_tips_dialog.tvCheckV4
import kotlinx.android.synthetic.main.order_returned_friend_tips_dialog.tvCheckV5
import kotlinx.android.synthetic.main.order_returned_friend_tips_dialog.tvMsg
import kotlinx.android.synthetic.main.order_returned_friend_tips_dialog.tv_more_size
import kotlinx.android.synthetic.main.order_returned_friend_tips_dialog.viewLayout1
import kotlinx.android.synthetic.main.order_returned_friend_tips_dialog.viewLayout2

/**
 *  desc: 回单议价消息提示
 *  user: ssp
 *  time: 2024/7/3 19:11
 */
class ReturnedOrderConfirmDialog(private var msgRes: SingleReturnedOrderConfirmMsgRes? = null) : BaseDialog() {
    private var okBlock: (String) -> Unit = {}
    override fun getDialogStyle(): Int {
        return R.style.order_return_dialog
    }

    override fun bindView(view: View, bundle: Bundle?) {
        initView()
    }

    override fun getDialogTag(): String {
        return "ReturnedOrderConfirmDialog"
    }

    override fun getDialogLayout(): Int {
        return R.layout.order_returned_friend_tips_dialog
    }

    @SuppressLint("SetTextI18n")
    private fun initView() {
        tvMsg.text = Html.fromHtml(msgRes?.resultMsg ?: "")
        btnConfirm.setOnClickListener {
            dismiss()
            okBlock(initReason())
        }

        btnCancel.setOnClickListener {
            dismiss()
        }
        val showReason = TextUtils.equals(msgRes?.tips, "2")
        viewLayout1.setVisible(showReason)
        viewLayout2.setVisible(showReason)
        noteLayout.setVisible(showReason)
        UtilRxView.clicks(tvCheckV1, 300) {
            tvCheckV1.isSelected = !tvCheckV1.isSelected
            initTextColor(tvCheckV1)
        }
        UtilRxView.clicks(tvCheckV2, 300) {
            tvCheckV2.isSelected = !tvCheckV2.isSelected
            initTextColor(tvCheckV2)
        }
        UtilRxView.clicks(tvCheckV3, 300) {
            tvCheckV3.isSelected = !tvCheckV3.isSelected
            initTextColor(tvCheckV3)
        }
        UtilRxView.clicks(tvCheckV4, 300) {
            tvCheckV4.isSelected = !tvCheckV4.isSelected
            initTextColor(tvCheckV4)
        }
        UtilRxView.clicks(tvCheckV5, 300) {
            tvCheckV5.isSelected = !tvCheckV5.isSelected
            initTextColor(tvCheckV5)
        }
        tv_more_size.text = "(0/100)"
        ed_description.filters = arrayOf<InputFilter>(InputFilter.LengthFilter(100))
        UtilRxView.afterTextChangeEvents(ed_description, 300) { charSequence ->
            tv_more_size.text = "(${charSequence.length}/100)"
        }
    }

    private fun initTextColor(textView: TextView) {
        if (textView.isSelected) {
            textView.setTextColor(Color.parseColor("#5086FC"))
        } else {
            textView.setTextColor(Color.parseColor("#555555"))
        }
    }

    fun setOkBlock(block: (String) -> Unit): ReturnedOrderConfirmDialog {
        this.okBlock = block
        return this
    }

    private fun initReason(): String {
        val list = mutableListOf<String>()
        if (tvCheckV1.isSelected) {
            list.add(tvCheckV1.text.toString())
        }
        if (tvCheckV2.isSelected) {
            list.add(tvCheckV2.text.toString())
        }
        if (tvCheckV3.isSelected) {
            list.add(tvCheckV3.text.toString())
        }
        if (tvCheckV4.isSelected) {
            list.add(tvCheckV4.text.toString())
        }
        if (tvCheckV5.isSelected) {
            list.add(tvCheckV5.text.toString())
        }
        if (ed_description.text.isNotEmpty()) {
            list.add(ed_description.text.toString())
        }
        return list.toCommaStringV1()
    }

    private fun <T> Iterable<T>.toCommaStringV1(): String {
        val stringBuffer = StringBuffer()
        this.forEach {
            stringBuffer.append(";")
            stringBuffer.append(it.toString())
        }
        if (stringBuffer.isNotEmpty()) {
            stringBuffer.deleteCharAt(0)
        }
        return stringBuffer.toString()
    }

}