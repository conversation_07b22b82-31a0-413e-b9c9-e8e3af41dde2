package com.zczy.cargo_owner.order.confirm

import android.annotation.SuppressLint
import android.content.Context
import android.content.Intent
import android.os.Bundle
import android.text.TextUtils
import android.util.TypedValue
import android.view.View
import androidx.core.content.ContextCompat
import androidx.recyclerview.widget.LinearLayoutManager
import com.sfh.lib.event.RxBusEventManager
import com.sfh.lib.mvvm.annotation.LiveDataMatch
import com.sfh.lib.mvvm.service.BaseViewModel
import com.zczy.cargo_owner.order.R
import com.zczy.cargo_owner.order.confirm.adapter.ReceiptContainerAdapter
import com.zczy.cargo_owner.order.confirm.adapter.UploadReturnedOrderPhotoAdapterV1
import com.zczy.cargo_owner.order.confirm.bean.AgreeAdjustDetailBean
import com.zczy.cargo_owner.order.confirm.bean.AgreeAdjustDetailReq
import com.zczy.cargo_owner.order.confirm.bean.AgreeAdjustReq
import com.zczy.cargo_owner.order.confirm.bean.ReturnedOrderDetailBean
import com.zczy.cargo_owner.order.confirm.bean.ReturnedOrderDetailReqV1
import com.zczy.cargo_owner.order.confirm.bean.RxAgreeAdjust
import com.zczy.cargo_owner.order.confirm.bean.differenceV1
import com.zczy.cargo_owner.order.confirm.bean.getDeliverCargoCategoryStr
import com.zczy.cargo_owner.order.confirm.bean.getDeliverWeightStr
import com.zczy.cargo_owner.order.confirm.bean.getReturnMoneyTime
import com.zczy.cargo_owner.order.confirm.bean.showCarrierDeliverRemark
import com.zczy.cargo_owner.order.confirm.bean.showCarrierReceiveRemark
import com.zczy.cargo_owner.order.confirm.bean.showDeliverTime
import com.zczy.cargo_owner.order.confirm.bean.showReceiveTime
import com.zczy.cargo_owner.order.confirm.dialog.AgreeAdjustDetailDialog
import com.zczy.comm.data.entity.EImage
import com.zczy.comm.ui.BaseActivity
import com.zczy.comm.utils.dp2px
import com.zczy.comm.utils.ex.isTrue
import com.zczy.comm.utils.ex.setVisible
import com.zczy.comm.widget.itemdecoration.SpaceItemDecoration
import kotlinx.android.synthetic.main.order_confirm_returned_hz_img_info_layout.*
import kotlinx.android.synthetic.main.order_confirm_returned_info_layout.*
import kotlinx.android.synthetic.main.order_return_dcl_info_layout.tvdclReason
import kotlinx.android.synthetic.main.order_return_denied_info_layout.*
import kotlinx.android.synthetic.main.order_return_platform_info_layout.*
import kotlinx.android.synthetic.main.order_returned_confirm_detail_activity.*
import kotlinx.android.synthetic.main.order_returned_delivery_info_layout.*
import kotlinx.android.synthetic.main.order_returned_detail_delivery_info_layout.isDeliveryRecyclerViewCargo
import kotlinx.android.synthetic.main.order_returned_settlement_info_layout.*

/**
 * 类描述：回单确认详情
 * 作者：ssp
 * 创建时间：2024/1/19
 */

class ReturnedOrderConfirmDetailActivity : BaseActivity<BaseViewModel>() {
    private val detailId by lazy { intent.getStringExtra(DETAIL_ID) }
    private val orderId by lazy { intent.getStringExtra(ORDER_ID) }
    private val mReceiptContainerAdapter = ReceiptContainerAdapter()
    private val mUploadReturnedOrderPhotoAdapterV11 = UploadReturnedOrderPhotoAdapterV1(str = UploadReturnedOrderPhotoAdapterV1.STR_1)
    private val mUploadReturnedOrderPhotoAdapterV12 = UploadReturnedOrderPhotoAdapterV1(str = UploadReturnedOrderPhotoAdapterV1.STR_2)

    companion object {
        private const val DETAIL_ID = "detailId"
        private const val ORDER_ID = "orderId"

        @JvmStatic
        @JvmOverloads
        fun start(context: Context?, detailId: String, orderId: String) {
            if (context == null) return
            val intent = Intent(context, ReturnedOrderConfirmDetailActivity::class.java)
            intent.putExtra(DETAIL_ID, detailId)
            intent.putExtra(ORDER_ID, orderId)
            context.startActivity(intent)
        }
    }

    override fun getLayout(): Int {
        return R.layout.order_returned_confirm_detail_activity
    }

    override fun bindView(bundle: Bundle?) {
        mUploadReturnedOrderPhotoAdapterV11.canEdit = false
        isDeliveryRecyclerViewCargo.apply {
            layoutManager = LinearLayoutManager(this@ReturnedOrderConfirmDetailActivity)
            adapter = mUploadReturnedOrderPhotoAdapterV11
        }
        mUploadReturnedOrderPhotoAdapterV12.canEdit = false
        isReceiptRecyclerViewCargo.apply {
            layoutManager = LinearLayoutManager(this@ReturnedOrderConfirmDetailActivity)
            adapter = mUploadReturnedOrderPhotoAdapterV12
        }
        bindClickEvent(btnAgreeAdjust)
    }

    override fun onSingleClick(v: View) {
        super.onSingleClick(v)
        when (v.id) {
            R.id.btnAgreeAdjust -> {
                // 同意调整
                getViewModel(ReturnedOrderConfirmModel::class.java).agreeAdjustDetail(AgreeAdjustDetailReq(detailId, "2"))
            }
        }
    }

    override fun initData() {
        getViewModel(ReturnedOrderConfirmModel::class.java).queryConsignorSingleReceiptOrderDetailApp(
            req = ReturnedOrderDetailReqV1(
                orderId = orderId,
                detailId = detailId,
            )
        )
    }

    @LiveDataMatch
    open fun agreeAdjustDetailSuccess(mReturnedOrderDetailBean: AgreeAdjustDetailBean) {
        val agreeAdjustDialog = AgreeAdjustDetailDialog.instance(mReturnedOrderDetailBean, object : AgreeAdjustDetailDialog.AgreeAdjustCallback {
            override fun agreeAdjust(consignorUserId: String?, detailId: String?) {
                getViewModel(ReturnedOrderConfirmModel::class.java).agreeAdjust(AgreeAdjustReq(detailId, consignorUserId))
            }
        })
        agreeAdjustDialog.show(<EMAIL>, "dialog")
    }

    @LiveDataMatch
    open fun agreeAdjustSuccess(rsp: String) {
        showToast(rsp)
        RxBusEventManager.postEvent(RxAgreeAdjust(true))
        finish()
    }

    @SuppressLint("SetTextI18n")
    @LiveDataMatch
    open fun queryConsignorSingleReceiptOrderDetailAppSuccess(data: ReturnedOrderDetailBean) {
        when (data.backStatus) {
            "5" -> {
                when (data.backOrderReconsiderState) {
                    "4" -> {
                        appToolbar.setTitle("回单确认详情")
                        btnAgreeAdjust.setVisible(true)
                    }

                    else -> {
                        appToolbar.setTitle("回单确认详情")
                        btnAgreeAdjust.setVisible(false)
                    }
                }
            }

            else -> {
                appToolbar.setTitle("已确认详情")
                btnAgreeAdjust.setVisible(false)
            }
        }
        returned_order_info_view.setData(data = data, showBtn = false)
        orderId?.let { returned_order_info_view.setOrderId(it) }
        //发货信息
        tvCarrierAlreadyEdited.setVisible(data.driverUpdateDeliverWeightFlag.isTrue && !data.showDeficitSuperTonSwitch.isTrue)
        tvShippingModification.setVisible(false)
        tvDeliveryNum.text = data.getDeliverWeightStr() + data.getDeliverCargoCategoryStr()
        //集装箱数据
        inputReceiptContainer.tvTitle.apply {
            setTextColor(ContextCompat.getColor(this@ReturnedOrderConfirmDetailActivity, R.color.color_666))
            setTextSize(TypedValue.COMPLEX_UNIT_SP, 13F)
        }
        inputReceiptContainer.tvContent.apply {
            setTextColor(ContextCompat.getColor(this@ReturnedOrderConfirmDetailActivity, R.color.text_33))
            setTextSize(TypedValue.COMPLEX_UNIT_SP, 13F)
        }
        //实际收货
        inputReceiptInformationContainer.tvTitle.apply {
            setTextColor(ContextCompat.getColor(this@ReturnedOrderConfirmDetailActivity, R.color.color_666))
            setTextSize(TypedValue.COMPLEX_UNIT_SP, 13F)
        }
        inputReceiptInformationContainer.tvContent.apply {
            setTextColor(ContextCompat.getColor(this@ReturnedOrderConfirmDetailActivity, R.color.text_33))
            setTextSize(TypedValue.COMPLEX_UNIT_SP, 13F)
        }
        when (data.goodsSource) {
            "1" -> {
                inputReceiptContainer.setVisible(true)
                inputReceiptInformationContainer.setVisible(true)
                recyclerViewContainer.setVisible(true)
            }

            else -> {
                inputReceiptContainer.setVisible(false)
                inputReceiptInformationContainer.setVisible(false)
                recyclerViewContainer.setVisible(false)
            }
        }
        data.containerDataObj?.let { ob1 ->
            ob1.receiptContainerDataObj?.let { ob2 ->
                //实际发货
                inputReceiptContainer.tvContent.apply {
                    text = ob2.containerName + "*" + ob2.containerNoJsonArray.size + "箱"
                }
                //实际收货
                inputReceiptInformationContainer.tvContent.apply {
                    text = ob2.containerName + "*" + ob2.containerNoJsonArray.size + "箱"
                }
                //结算信息
                ob2.containerNoJsonArray.let { ob3 ->
                    if (ob3.size > 0) {
                        mReceiptContainerAdapter.setNewData(ob2.containerNoJsonArray)
                    }
                }
            }
        }
        ivDocumentPhotoSwitch.setOnClickListener {
            val driverDeliverProofPicJsonArr = data.driverDeliverProofPicJsonArr ?: emptyList()
            val driverDeliverPhotoPicJsonArr = data.driverDeliverPhotoPicJsonArr ?: emptyList()
            when (ivDocumentPhotoSwitch.text) {
                "切换原图" -> {
                    //带水印图片 切换成原图
                    driverDeliverProofPicJsonArr.mapNotNull { it.picOrgUrl.takeIf { s -> s.isNotEmpty() }?.let { url -> EImage(imageId = url) } }.let { list ->
                        isvDeliveryImage.imgList = ArrayList(list)
                    }
                    driverDeliverPhotoPicJsonArr.mapNotNull { it.picOrgUrl.takeIf { s -> s.isNotEmpty() }?.let { url -> EImage(imageId = url) } }.let { list ->
                        isvDeliveryImageV1.imgList = ArrayList(list)
                    }
                    ivDocumentPhotoSwitch.text = "切换默认"
                }

                "切换默认" -> {
                    //原图 切换成带水印图片
                    driverDeliverProofPicJsonArr.mapNotNull { it.picUrl.takeIf { s -> s.isNotEmpty() }?.let { url -> EImage(imageId = url) } }.let { list ->
                        isvDeliveryImage.imgList = ArrayList(list)
                    }
                    driverDeliverPhotoPicJsonArr.mapNotNull { it.picUrl.takeIf { s -> s.isNotEmpty() }?.let { url -> EImage(imageId = url) } }.let { list ->
                        isvDeliveryImageV1.imgList = ArrayList(list)
                    }
                    ivDocumentPhotoSwitch.text = "切换原图"
                }
            }
        }
        ivDocumentPhotoSwitch.setVisible(data.deliverOrgPicFlag.isTrue)
        //单据照片
        if (data.driverDeliverProofPicJsonArr.isNullOrEmpty()) {
            isvDeliveryImage.setVisible(false)
            isvDeliveryImageRl.setVisible(false)
            tvDocumentPhoto.setVisible(false)
        } else {
            tvDocumentPhoto.setVisible(true)
            isvDeliveryImage.setVisible(true)
            isvDeliveryImageRl.setVisible(true)
            isvDeliveryImage.imgList = ArrayList(data.driverDeliverProofPicJsonArr.map { EImage(imageId = it.picUrl) })
        }
        //发货信息-上传时间(确认发货时间)
        //发货货物明细
        mUploadReturnedOrderPhotoAdapterV11.setNewData(data.deliverCargoArray)
        isvDeliveryTime.text = data.showDeliverTime()
        setUpdateReceiptDeliverWeightAndPic(data)
        //运单照片
        if (data.driverDeliverPhotoPicJsonArr.isNullOrEmpty()) {
            isvDeliveryImageV1.setVisible(false)
            isvDeliveryImageRlV1.setVisible(false)
            tvDocumentPhotoV1.setVisible(false)
        } else {
            tvDocumentPhotoV1.setVisible(true)
            isvDeliveryImageV1.setVisible(true)
            isvDeliveryImageRlV1.setVisible(true)
            isvDeliveryImageV1.imgList = ArrayList(data.driverDeliverPhotoPicJsonArr.map { EImage(imageId = it.picUrl) })
        }
        //挂车车牌照片
        if (data.deliverTrailerPicList.isNullOrEmpty()) {
            isvDeliveryImageV3.visibility = View.GONE
            isvDeliveryImageRlV3.visibility = View.GONE
            tvDocumentPhotoV3.visibility = View.GONE
        } else {
            tvDocumentPhotoV3.visibility = View.VISIBLE
            isvDeliveryImageV3.visibility = View.VISIBLE
            isvDeliveryImageRlV3.visibility = View.VISIBLE
            isvDeliveryImageV3.imgList =
                ArrayList(data.deliverTrailerPicList.map { EImage(imageId = it.picUrl) })
        }
        //发货备注
        isvDeliveryRemark.text = data.showCarrierDeliverRemark()
        //回单信息
        tvReceiptModification.setVisible(false)
        //发货货物明细
        mUploadReturnedOrderPhotoAdapterV12.setNewData(data.receiveCargoArray)
        tvReceiptNum.text = data.receiveWeight + data.getDeliverCargoCategoryStr()
        inputOutStageTime.content = data.outTime ?: ""
        clOutStageTime.setVisible(data.isShowOutTime.isTrue)
        ivReceiptDocumentPhotoSwitch.setVisible(data.receiveOrgPicFlag.isTrue)
        ivReceiptDocumentPhotoSwitch.setOnClickListener {
            val driverReceiveProofPicJsonArr = data.driverReceiveProofPicJsonArr ?: emptyList()
            val driverReceivePhotoPicJsonArr = data.driverReceivePhotoPicJsonArr ?: emptyList()
            when (ivReceiptDocumentPhotoSwitch.text) {
                "切换原图" -> {
                    //带水印图片 切换成原图
                    driverReceiveProofPicJsonArr.mapNotNull { it.picOrgUrl.takeIf { s -> s.isNotEmpty() }?.let { url -> EImage(imageId = url) } }.let { list ->
                        isvReturnImage.imgList = ArrayList(list)
                    }
                    driverReceivePhotoPicJsonArr.mapNotNull { it.picOrgUrl.takeIf { s -> s.isNotEmpty() }?.let { url -> EImage(imageId = url) } }.let { list ->
                        isvReturnImageV1.imgList = ArrayList(list)
                    }
                    ivReceiptDocumentPhotoSwitch.text = "切换默认"
                }

                "切换默认" -> {
                    //原图 切换成带水印图片
                    driverReceiveProofPicJsonArr.mapNotNull { it.picUrl.takeIf { s -> s.isNotEmpty() }?.let { url -> EImage(imageId = url) } }.let { list ->
                        isvReturnImage.imgList = ArrayList(list)
                    }
                    driverReceivePhotoPicJsonArr.mapNotNull { it.picUrl.takeIf { s -> s.isNotEmpty() }?.let { url -> EImage(imageId = url) } }.let { list ->
                        isvReturnImageV1.imgList = ArrayList(list)
                    }
                    ivReceiptDocumentPhotoSwitch.text = "切换原图"
                }
            }
        }
        if (data.driverReceiveProofPicJsonArr.isNullOrEmpty()) {
            isvReturnImage.setVisible(false)
            isvReturnImageRl.setVisible(false)
            tvReceiptDocumentPhoto.setVisible(false)
        } else {
            tvReceiptDocumentPhoto.setVisible(true)
            isvReturnImage.setVisible(true)
            isvReturnImageRl.setVisible(true)
            isvReturnImage.imgList = ArrayList(data.driverReceiveProofPicJsonArr.map { EImage(imageId = it.picUrl) })
        }
        isvReturnImage.isNestedScrollingEnabled = false
        isvReturnImage.setHasFixedSize(true)
        //回单信息-上传时间(确认收货时间)
        isvReturnTime.text = data.showReceiveTime()
        if (data.examinePicUrlArr.isNullOrEmpty()) {
            tvexaminePicUrlArr.setVisible(false)
            isexaminePicUrlArr.setVisible(false)
            isexaminePicUrlArrImage.setVisible(false)
        } else {
            tvexaminePicUrlArr.setVisible(true)
            isexaminePicUrlArr.setVisible(true)
            isexaminePicUrlArrImage.setVisible(true)
            isexaminePicUrlArrImage.canSelect = false
            isexaminePicUrlArrImage.imgList = ArrayList(data.examinePicUrlArr.map { EImage(imageId = it.picUrl) })
        }
        if (data.driverReceivePhotoPicJsonArr.isNullOrEmpty()) {
            isvReturnImageV1.setVisible(false)
            isvReturnImageRlV1.setVisible(false)
            tvReceiptDocumentPhotoV1.setVisible(false)
        } else {
            isvReturnImageV1.setVisible(true)
            isvReturnImageRlV1.setVisible(true)
            tvReceiptDocumentPhotoV1.setVisible(true)
            isvReturnImageV1.imgList = ArrayList(data.driverReceivePhotoPicJsonArr.map { EImage(imageId = it.picUrl) })
            ivReceiptDocumentPhotoSwitch.tag = true
        }

        // 挂车车牌合影
        if (data.receiveTrailerPicList.isNullOrEmpty()) {
            isvReceiptReturnImageV3.setVisible(false)
            isvReceiptReturnImageRlV3.setVisible(false)
            tvReceiptDocumentPhotoV3.setVisible(false)
        } else {
            isvReceiptReturnImageV3.setVisible(true)
            isvReceiptReturnImageRlV3.setVisible(true)
            tvReceiptDocumentPhotoV3.setVisible(true)
            isvReceiptReturnImageV3.imgList =
                ArrayList(data.receiveTrailerPicList.map { EImage(imageId = it.picUrl) })
        }
        //收货备注
        isvReturnRemark.text = data.showCarrierReceiveRemark()

        // 卸货图片
        val carrierReveivePicUrlArr = data.carrierReveivePicUrlArr
        if (carrierReveivePicUrlArr.isNullOrEmpty()) {
            rl_clockin.setVisible(false)
        } else {
            rl_clockin.setVisible(true)
            image_clockin.imgList = ArrayList(carrierReveivePicUrlArr.map { EImage(imageId = it.picUrl) })
        }
        jmyl_warning.setVisible(!TextUtils.isEmpty(data.differenceV1()))
        tv_warning_jmyl.text = data.differenceV1()
        //是否展示备案卸货
        cl_isBackUp.setVisible(data.isBackUpReceive.isTrue)
        //现场证据照片
        data.nativeEvidencePicJsonArr?.let { list ->
            rp_evidence_img.setImgData(ArrayList(list.map { EImage(imageId = it) }))
        }
        if (data.backRegectReason.isNullOrEmpty()) {
            deniedInfo.setVisible(false)
        } else {
            deniedInfo.setVisible(true)
            tvDeniedReason.text = data.backRegectReason
        }
        //平台审核信息
        when (data.orderReceiptCheckState) {
            // 平台审核结果  0:"未审核"1:"异常"2:"审核通过"
            "0" -> {
                tv_verify_result.text = "待审核"
            }

            "1" -> {
                tv_verify_result.text = "回单异常"
            }

            "2" -> {
                tv_verify_result.text = "回单通过"
            }

            "3" -> {
                tv_verify_result.text = "回单打回"
            }

            else -> {
                tv_verify_result.text = ""
            }
        }
        tv_suggestion_result.text = data.orderReceiptItemReason
        cl_suggestion.setVisible(true)
        tv_abnormal_trajectory_result.text = data.orderTraceExceptionItemReason
        cl_abnormal_trajectory.setVisible(!data.orderTraceExceptionItemReason.isNullOrEmpty())
        initOrderCustomNumber(data.selfComment)
        setOrderInfo(data)
        setHuozuPicUi(data)
        initRemark(data)
        if (data.dealReason.isNullOrEmpty()) {
            dclInfo.setVisible(false)
        } else {
            tvdclReason.text = data.dealReason
        }
        layoutConfirmJiaKouInfo.setVisible(false)
        tv_deal.setVisible(false)
        tv_abnormal_trajectory_deal.setVisible(false)
        view_code.setVisible(false)
    }

    private fun initOrderCustomNumber(selfComment: String?) {
        clOrderCustomNumber.setVisible(true)
        tv_code_size_v1.setVisible(false)
        orderCustomNumber1.setText(selfComment)
        orderCustomNumber1.isEnabled = false
    }

    private fun setOrderInfo(data: ReturnedOrderDetailBean) {
        smallLabel.setVisible(false)
        inputViewWipeZero.setVisible(false)
        tvLossTonnageCalc.setVisible(false)
        orderSettlementBasis.text = data.settleBasisTypeName
        etConfirmSettlementPriceNotIncludedTax.setText(data.consignorNoRateMoney)
        etConfirmSettlementPriceNotIncludedTax.isEnabled = false
        etConfirmSettlementPriceIncludedTax.setText(data.consignorRateMoney)
        etConfirmSettlementPriceIncludedTax.isEnabled = false
        order_textview12.text = "结算金额(不含税)"
        cl_order_textview12_2.setVisible(data.consignorNoRateUnitShowMoneyFlag.isTrue)
        etConsignorNoRateUnitShowMoney.isEnabled = false
        etConsignorNoRateUnitShowMoney.setText(data.consignorNoRateUnitShowMoney)
        etConfirmSettlementTonnage.setText(data.slipLoad)
        etConfirmSettlementTonnage.isEnabled = false
        tvJiaKouKuanXiangUnit.text = data.getDeliverCargoCategoryStr()
        recyclerViewContainer.apply {
            layoutManager = LinearLayoutManager(this@ReturnedOrderConfirmDetailActivity)
            addItemDecoration(SpaceItemDecoration(dp2px(0f)))
            mReceiptContainerAdapter.bindToRecyclerView(recyclerViewContainer)
            adapter = mReceiptContainerAdapter
        }
        orderSettleTypeUnitEditText.isEnabled = false
        if (data.freightType.isTrue) {
            orderSettleTypeUnit.text = "元/吨"
            orderSettleType.text = "运费计价方式(含税单价)"
            //单价
            orderSettleTypeUnitEditText.setText(data.pbConsignorUnitMoney)
        } else {
            //包车价
            orderSettleTypeUnitEditText.setText(data.pbConsignorMoney)
            orderSettleTypeUnit.text = "元"
            orderSettleType.text = "运费计价方式(含税包车价)"
        }
        //回款到期日
        tvExpiryDateStr.text = data.getReturnMoneyTime()
//        tvExpiryDateStr.setVisible(data.showReturnMoneyTime())
        tvExpiryDateStr.setVisible(false)
        tvExpiryDate.setVisible(false)
    }

    private fun setUpdateReceiptDeliverWeightAndPic(data: ReturnedOrderDetailBean) {
        data.let {
            isvDeliveryImageV2.canSelect = false
            isReceiptReturnImageV2.canSelect = false
            tvShippingModification.setVisible(false)
            tvDocumentPhotoV2.setVisible(false)
            val consignorDeliverPicUrlArr = it.consignorDeliverPicUrlArr
            if (consignorDeliverPicUrlArr == null || consignorDeliverPicUrlArr.size <= 0) {
                tvDocumentPhotoV2.setVisible(false)
                isvDeliveryImageV2.setVisible(false)
                isvDeliveryImageRlV2.setVisible(false)
            } else {
                tvDocumentPhotoV2.setVisible(true)
                isvDeliveryImageV2.setVisible(true)
                isvDeliveryImageRlV2.setVisible(true)
                val list = arrayListOf<EImage>()
                consignorDeliverPicUrlArr.forEach { item ->
                    list.add(EImage(imageId = item.picUrl))
                }
                isvDeliveryImageV2.imgList = list
            }
            val consignorReveivePicUrlArr = it.consignorReveivePicUrlArr
            tvReceiptModification.setVisible(false)
            tvReceiptDocumentPhotoV2.setVisible(false)
            if (consignorReveivePicUrlArr == null || consignorReveivePicUrlArr.size <= 0) {
                tvReceiptDocumentPhotoV2.setVisible(false)
                isReceiptReturnImageV2.setVisible(false)
                isReceiptReturnImageRlV2.setVisible(false)
            } else {
                tvReceiptDocumentPhotoV2.setVisible(true)
                isReceiptReturnImageV2.setVisible(true)
                isReceiptReturnImageRlV2.setVisible(true)
                val list = arrayListOf<EImage>()
                consignorReveivePicUrlArr.forEach { item ->
                    list.add(EImage(imageId = item.picUrl))
                }
                isReceiptReturnImageV2.imgList = list
            }
            tvCarrierAlreadyEdited.setVisible(it.driverUpdateDeliverWeightFlag.isTrue && !it.showDeficitSuperTonSwitch.isTrue)
        }
    }

    private fun setHuozuPicUi(data: ReturnedOrderDetailBean) {
        order_hz_img_up.setVisible(false)
        order_hz_img_textview.setVisible(false)
        if (data.consignorUploadPicJsonObjArr.isNullOrEmpty()) {
            isvReturnImageHz.setVisible(false)
            order_hz_divider.setVisible(false)
        } else {
            //货主回单信息不为空
            order_hz_divider.setVisible(true)
            order_hz_img_textview.setVisible(true)
            data.consignorUploadPicJsonObjArr.apply {
                val list = arrayListOf<EImage>()
                forEach { upLoadPic ->
                    val eImage = EImage()
                    eImage.imageId = upLoadPic.pictureUrl
                    eImage.toastStr = upLoadPic.photoId
                    eImage.pictureType = upLoadPic.pictureType
                    list.add(eImage)
                }
                isvReturnImageHz.canSelect = false
                isvReturnImageHz.canDelete = false
                isvReturnImageHz.imgList = list
            }
        }
    }

    @SuppressLint("SetTextI18n")
    private fun initRemark(data: ReturnedOrderDetailBean) {
        edit_code.setText(data.consignorReceiptRemark ?: "")
        edit_code.isEnabled = false
        tv_code_size.text = "${data.consignorReceiptRemark?.length}/150"
    }
}