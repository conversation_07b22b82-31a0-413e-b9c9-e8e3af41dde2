package com.zczy.cargo_owner.order

import android.annotation.SuppressLint
import android.content.Context
import android.content.Intent
import android.os.Bundle
import android.view.KeyEvent
import android.view.View
import com.sfh.lib.event.RxBusEventManager
import com.sfh.lib.mvvm.annotation.LiveDataMatch
import com.sfh.lib.mvvm.service.BaseViewModel
import com.sfh.lib.utils.UtilTool
import com.zczy.cargo_owner.order.model.ReqBeforeModifyOrder
import com.zczy.cargo_owner.order.model.ReqCancelModifyOrder
import com.zczy.cargo_owner.order.model.ReqModifyOrder
import com.zczy.cargo_owner.order.model.RspBeforeModifyOrder
import com.zczy.cargo_owner.order.model.RxBusModifyOrder
import com.zczy.cargo_owner.order.model.WayBillModelV1
import com.zczy.comm.ui.BaseActivity
import com.zczy.comm.utils.ex.isTrue
import com.zczy.comm.utils.ex.setVisible
import com.zczy.comm.utils.ex.toDoubleRoundDownString
import com.zczy.comm.widget.inputv2.InputViewEdit
import kotlinx.android.synthetic.main.waybill_modify_price_activity.inputView1
import kotlinx.android.synthetic.main.waybill_modify_price_activity.inputView2
import kotlinx.android.synthetic.main.waybill_modify_price_activity.toolbar
import kotlinx.android.synthetic.main.waybill_modify_price_activity.tvSure
import java.math.BigDecimal

/**
 * 类描述：修改运价
 * 作者：ssp
 * 创建时间：2024/5/15
 */
@SuppressLint("DefaultLocale")
class WayBillModifyPriceActivity : BaseActivity<BaseViewModel>() {
    private val orderId by lazy { intent.getStringExtra(ORDER_ID) }
    private var mRspBeforeModifyOrder: RspBeforeModifyOrder? = null

    companion object {
        const val ORDER_ID = "orderId"

        @JvmStatic
        fun jumpPage(context: Context?, orderId: String?) {
            val intent = Intent(context, WayBillModifyPriceActivity::class.java)
            intent.putExtra(ORDER_ID, orderId)
            context?.startActivity(intent)
        }
    }

    override fun getLayout(): Int {
        return R.layout.waybill_modify_price_activity
    }

    override fun bindView(bundle: Bundle?) {
        UtilTool.setEditTextInputSize(inputView1.editText, 4)
        inputView1.setListener(object : InputViewEdit.Listener() {
            override fun onTextChanged(viewId: Int, view: InputViewEdit, s: String) {
                if (view.editText.isFocused) {
                    inputView2.content = formatOwnerMoney(settleRate = mRspBeforeModifyOrder?.settleRate ?: "0.00", money = s)
                }
            }
        })
        UtilTool.setEditTextInputSize(inputView2.editText, 4)
        inputView2.setListener(object : InputViewEdit.Listener() {
            override fun onTextChanged(viewId: Int, view: InputViewEdit, s: String) {
                if (view.editText.isFocused) {
                    inputView1.content = formatDriverMoney(settleRate = mRspBeforeModifyOrder?.settleRate ?: "0.00", money = s)
                }
            }
        })
        bindClickEvent(tvSure)
        toolbar.setLeftOnClickListener {
            getViewModel(WayBillModelV1::class.java).cancelModifyOrder(
                req = ReqCancelModifyOrder(
                    orderId = orderId
                )
            )
        }
    }

    override fun onKeyDown(keyCode: Int, event: KeyEvent?): Boolean {
        if (keyCode == KeyEvent.KEYCODE_BACK) {
            getViewModel(WayBillModelV1::class.java).cancelModifyOrder(
                req = ReqCancelModifyOrder(
                    orderId = orderId
                )
            )
            return true
        }
        return super.onKeyDown(keyCode, event)
    }

    override fun onSingleClick(v: View) {
        super.onSingleClick(v)
        when (v.id) {
            R.id.tvSure -> {
                val req = ReqModifyOrder()
                req.orderId = orderId
                val consignorNoTaxMoney = inputView1.content
                if (consignorNoTaxMoney.isEmpty() && mRspBeforeModifyOrder?.supportConsignorNoTaxMoneyFlag.isTrue) {
                    showDialogToast("${inputView1.title}不能为空")
                    return
                }
                req.consignorNoTaxMoney = consignorNoTaxMoney
                val consignorMoney = inputView2.content
                if (consignorMoney.isEmpty()) {
                    showDialogToast("${inputView2.title}不能为空")
                    return
                }
                req.consignorMoney = consignorMoney
                getViewModel(WayBillModelV1::class.java).modifyOrder(
                    req = req
                )
            }
        }
    }

    override fun initData() {
        getViewModel(WayBillModelV1::class.java).beforeModifyOrder(
            req = ReqBeforeModifyOrder(
                orderId = orderId
            )
        )
    }

    @LiveDataMatch
    open fun cancelModifyOrderSuccess() {
        RxBusEventManager.postEvent(RxBusModifyOrder(success = true))
        finish()
    }

    @LiveDataMatch
    open fun modifyOrderSuccess() {
        RxBusEventManager.postEvent(RxBusModifyOrder(success = true))
        finish()
    }

    @LiveDataMatch
    open fun beforeModifyOrderSuccess(data: RspBeforeModifyOrder) {
        mRspBeforeModifyOrder = data
        if (data.freightType.isTrue) {
            inputView1.setTitle("承运方预估到手价单价", true)
            inputView1.setVisible(data.supportConsignorNoTaxMoneyFlag.isTrue)
            inputView1.content = data.consignorNoTaxMoney ?: ""
            inputView2.setTitle("货主价单价(元)(含税)", true)
            inputView2.content = data.consignorMoney ?: ""
        } else {
            inputView1.setTitle("承运方预估到手价", true)
            inputView1.setVisible(data.supportConsignorNoTaxMoneyFlag.isTrue)
            inputView1.content = data.consignorNoTaxMoney ?: ""
            inputView2.setTitle("货主价", true)
            inputView2.content = data.consignorMoney ?: ""
        }
    }

    fun formatOwnerMoney(settleRate: String, money: String): String {
        //推算 货主价/货主价单价
        return if (mRspBeforeModifyOrder?.freightType.isTrue) {
            // 费用类型：0 包车价，1 单价
            val settleRate1 = div(settleRate.toDoubleOrNull(), 100.0000, 10) + 1
            val consignorNoTaxMoney = money.toDoubleOrNull() ?: 0.0000
            mul(consignorNoTaxMoney, settleRate1).roundToFourDecimals().toString()
        } else {
            val settleRate1 = div(settleRate.toDoubleOrNull(), 100.0000, 10) + 1
            val consignorNoTaxMoney = money.toDoubleOrNull() ?: 0.0000
            mul(consignorNoTaxMoney, settleRate1).roundToFourDecimals().toString()
        }
    }

    fun formatDriverMoney(settleRate: String, money: String): String {
        // 费用类型：0 包车价，1 单价
        return if (mRspBeforeModifyOrder?.freightType == "1") {
            //结算税率
            val settleRate1 = div(settleRate.toDoubleOrNull(), 100.0000, 10) + 1
            val lTotalAmount = money.toDoubleOrNull() ?: 0.0000
            div(lTotalAmount, settleRate1, 4).roundToFourDecimals().toString()
        } else {
            val settleRate1 = div(settleRate.toDoubleOrNull(), 100.0000, 10) + 1
            val lTotalAmount = money.toDoubleOrNull() ?: 0.0000
            div(lTotalAmount, settleRate1, 4).roundToFourDecimals().toString()
        }
    }

    private fun mul(d1: Double?, d2: Double?): Double {
        val bd1 = BigDecimal(d1?.toString() ?: "0.0000")
        val bd2 = BigDecimal(d2?.toString() ?: "0.0000")
        return bd1.multiply(bd2).toDouble()
    }

    private fun div(d1: Double?, d2: Double?, scale: Int): Double {
        val bd1 = BigDecimal(d1?.toString() ?: "0.0000")
        val bd2 = BigDecimal(d2?.toString() ?: "1.0000")
        return bd1.divide(bd2, scale, BigDecimal.ROUND_HALF_UP).toDouble()
    }

    private fun Double.roundToFourDecimals(): Double {
        return String.format("%.4f", this).toDouble()
    }
}