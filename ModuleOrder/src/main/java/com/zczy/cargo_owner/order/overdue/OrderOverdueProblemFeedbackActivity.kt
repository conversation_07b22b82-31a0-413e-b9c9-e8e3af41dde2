package com.zczy.cargo_owner.order.overdue

import android.app.Activity
import android.content.Intent
import android.graphics.Typeface
import android.os.Bundle
import android.text.TextUtils
import android.view.View
import com.google.gson.Gson
import com.jakewharton.rxbinding2.view.RxView
import com.jakewharton.rxbinding2.widget.RxTextView
import com.sfh.lib.mvvm.annotation.LiveDataMatch
import com.sfh.lib.mvvm.service.BaseViewModel
import com.sfh.lib.ui.dialog.DialogBuilder
import com.zczy.cargo_owner.libcomm.config.HttpConfig
import com.zczy.cargo_owner.libcomm.widget.CheckSelfPermissionDialog
import com.zczy.cargo_owner.order.R
import com.zczy.cargo_owner.order.overdue.model.OrderOverdueFragmentModel
import com.zczy.cargo_owner.order.overdue.req.FeedbackTypeItem
import com.zczy.cargo_owner.order.overdue.req.OrderOverDueItem
import com.zczy.cargo_owner.order.overdue.req.ReqAddProblemFeedback
import com.zczy.cargo_owner.order.overdue.req.RspQueryProblemFeedbackTypeList
import com.zczy.comm.CommServer
import com.zczy.comm.data.entity.EImage
import com.zczy.comm.data.entity.EProcessFile
import com.zczy.comm.permission.PermissionCallBack
import com.zczy.comm.permission.PermissionUtil.openAlbum
import com.zczy.comm.ui.BaseActivity
import com.zczy.comm.utils.ex.setVisible
import com.zczy.comm.utils.imageselector.ImagePreviewActivity
import com.zczy.comm.utils.imageselector.ImageSelectProgressView
import com.zczy.comm.utils.imageselector.ImageSelector
import com.zczy.comm.widget.dialog.MenuDialogV1
import com.zczy.comm.widget.inputv2.InputViewClick
import io.reactivex.android.schedulers.AndroidSchedulers
import kotlinx.android.synthetic.main.order_overdue_problem_feedback_activity.*
import java.io.File
import java.text.SimpleDateFormat
import java.util.*
import java.util.concurrent.TimeUnit

/**
 * desc: 逾期问题反馈
 * user: AI Assistant
 * time: 2025/01/29
 */
class OrderOverdueProblemFeedbackActivity : BaseActivity<OrderOverdueFragmentModel>() {
    
    private val addProblemFeedback = ReqAddProblemFeedback()
    private var selectedFeedbackType: FeedbackTypeItem? = null
    private var feedbackTypeList: List<FeedbackTypeItem>? = null
    private val orderItem by lazy {
        val json = intent.getStringExtra(ORDER_ITEM)
        if (json.isNullOrBlank()) null else Gson().fromJson(json, OrderOverDueItem::class.java)
    }
    companion object {
        const val ORDER_ITEM = "orderItem"

        fun startContentUI(context: Activity, orderItem: OrderOverDueItem, requestCode: Int) {
            val intent = Intent(context, OrderOverdueProblemFeedbackActivity::class.java)
            val json = Gson().toJson(orderItem)
            intent.putExtra(ORDER_ITEM, json)
            context.startActivityForResult(intent, requestCode)
        }
    }

    override fun getLayout(): Int {
        return R.layout.order_overdue_problem_feedback_activity
    }

    override fun bindView(bundle: Bundle?) {
        initView()
    }

    override fun initData() {
        orderItem?.let { item ->
            addProblemFeedback.orderId = item.orderId
            // 设置运单信息
            tv_order_id.text = "运单号："+item.orderId
            tv_carrier.text = "承运方："+item.carrierCustomerName
            tv_carrier_info.text = "车牌号"+item.plateNumber
        }
        // 查询问题反馈类型列表
        viewModel.queryProblemFeedbackTypeList()
    }

    private fun initView() {
        // 责任方选择
        val myListener = View.OnClickListener { 
            selectResponsibleParty(true)
        }
        iv_my.setOnClickListener(myListener)
        rb_my.setOnClickListener(myListener)

        val taListener = View.OnClickListener { 
            selectResponsibleParty(false)
        }
        iv_ta.setOnClickListener(taListener)
        rb_ta.setOnClickListener(taListener)

        // 默认选择"对方"
        selectResponsibleParty(false)

        // 问题反馈类型选择
        view_feedback_type.tvTitle.typeface = Typeface.DEFAULT_BOLD
        view_feedback_type.setListener(object : InputViewClick.Listener() {
            override fun onClick(i: Int, inputViewClick: InputViewClick, s: String) {
                feedbackTypeList?.let { types ->
                    MenuDialogV1.instance(types)
                        .setClick { feedbackType: FeedbackTypeItem, _: Int ->
                            selectedFeedbackType = feedbackType
                            addProblemFeedback.feedbackType = feedbackType.feedbackType
                            view_feedback_type.setContent(feedbackType.feedbackTypeStr)
                        }
                        .show(this@OrderOverdueProblemFeedbackActivity)
                }
            }
        })

        // 情况说明字数统计
        val disposable = RxTextView.textChanges(ed_description)
            .observeOn(AndroidSchedulers.mainThread())
            .subscribe { charSequence -> 
                tv_char_count.text = "${charSequence.length}/200"
            }
        putDisposable(disposable)

        // 提交按钮
        val disposable1 = RxView.clicks(btn_submit)
            .throttleFirst(1000, TimeUnit.MILLISECONDS)
            .observeOn(AndroidSchedulers.mainThread())
            .subscribe {
                submitProblemFeedback()
            }
        putDisposable(disposable1)

        // 图片选择
        image_select_view.setOnItemSelectListener(object : ImageSelectProgressView.OnItemSelectListener {
            override fun onSelectImageClick(surplus: Int) {
                CheckSelfPermissionDialog.cameraPermissionDialog(this@OrderOverdueProblemFeedbackActivity, object : PermissionCallBack() {
                    override fun onHasPermission() {
                        openAlbum(
                            this@OrderOverdueProblemFeedbackActivity,
                            object : PermissionCallBack() {
                                override fun onHasPermission() {
                                    ImageSelector.open(this@OrderOverdueProblemFeedbackActivity, surplus, true, 1002)
                                }
                            })
                    }
                })
            }

            override fun onUpImageClick(file: String) {
            }

            override fun onLookImageClick(file: List<EProcessFile>, position: Int) {
                val list: MutableList<EImage> = ArrayList(file.size)
                for (processFile in file) {
                    val image = EImage()
                    image.netUrl = HttpConfig.getUrlImage(processFile.imagUrl)
                    list.add(image)
                }
                ImagePreviewActivity.start(this@OrderOverdueProblemFeedbackActivity, list, position)
            }

            override fun onDelateClick(position: Int) {
                image_select_view.deleteImage(position)
            }
        })
    }

    private fun selectResponsibleParty(isSelf: Boolean) {
        if (isSelf) {
            // 选择自己
            iv_my.setBackgroundResource(R.drawable.order_violate_me)
            rb_my.isChecked = true
            tv_my.setTextColor(resources.getColor(R.color.text_blue))
            
            iv_ta.setBackgroundResource(R.drawable.order_violate_ta_un)
            rb_ta.isChecked = false
            tv_ta.setTextColor(resources.getColor(R.color.text_66))
            
            addProblemFeedback.responsibleParty = "1" // 货主
            val login = CommServer.getUserServer().login
            tv_responsible_name.text = login?.memberName ?: ""
        } else {
            // 选择对方
            iv_my.setBackgroundResource(R.drawable.order_violate_me_un)
            rb_my.isChecked = false
            tv_my.setTextColor(resources.getColor(R.color.text_66))
            
            iv_ta.setBackgroundResource(R.drawable.order_violate_ta)
            rb_ta.isChecked = true
            tv_ta.setTextColor(resources.getColor(R.color.text_blue))
            
            addProblemFeedback.responsibleParty = "2" // 承运方
            tv_responsible_name.text = orderItem?.carrierCustomerName ?: ""
        }
    }

    private fun submitProblemFeedback() {
        // 验证问题反馈类型
        if (selectedFeedbackType == null) {
            showDialogToast("请选择问题反馈类型")
            return
        }

        // 验证情况说明
        val description = ed_description.text.toString().trim()
        if (description.isEmpty()) {
            showDialogToast("请输入情况说明")
            return
        }
        addProblemFeedback.description = description

        // 处理图片
        val files = image_select_view.getDataList()
        if (files.isNotEmpty()) {
            val urlList = mutableListOf<String>()
            for (processFile in files) {
                urlList.add(processFile.imagUrl)
            }
            addProblemFeedback.feedbackImgUrlList = urlList
        }

        // 提交问题反馈
        viewModel.addProblemFeedback(addProblemFeedback)
    }

    override fun onActivityResult(requestCode: Int, resultCode: Int, data: Intent?) {
        super.onActivityResult(requestCode, resultCode, data)
        if (1002 == requestCode && resultCode == RESULT_OK) {
            val file = ImageSelector.obtainPathResult(data)
            image_select_view.onUpLoadStart(file)
            viewModel.upFile(file)
        }
    }

    @LiveDataMatch(tag = "上传文件成功")
    open fun onFileSuccess(tag: File, url: String?) {
        image_select_view.onUpLoadFileSuccess(tag.absolutePath, url)
    }

    @LiveDataMatch(tag = "上传文件失败")
    open fun onFileFailure(tag: File, error: String?) {
        image_select_view.onUpLoadFileError(tag.absolutePath)
    }

    @LiveDataMatch(tag = "查询问题反馈类型列表")
    open fun onQueryProblemFeedbackTypeList(data: RspQueryProblemFeedbackTypeList?) {
        feedbackTypeList = data?.feedbackTypeList
    }

    @LiveDataMatch(tag = "新增问题反馈成功")
    open fun addProblemFeedbackSuccess() {
        setResult(RESULT_OK)
        val builder = DialogBuilder()
        builder.setMessage("问题反馈提交成功")
            .setHideCancel(true)
            .setOKText("确定")
            .setOkListener { dialogInterface, _ ->
                dialogInterface.dismiss()
                finish()
            }
        showDialog(builder)
    }
}
