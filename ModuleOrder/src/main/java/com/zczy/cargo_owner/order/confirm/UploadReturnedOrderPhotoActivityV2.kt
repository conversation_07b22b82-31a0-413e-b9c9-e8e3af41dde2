package com.zczy.cargo_owner.order.confirm

import android.content.Context
import android.content.Intent
import android.os.Bundle
import android.view.View
import com.sfh.lib.event.RxBusEventManager
import com.sfh.lib.mvvm.annotation.LiveDataMatch
import com.zczy.cargo_owner.order.R
import com.zczy.cargo_owner.order.confirm.bean.ReqSubUploadImgOfReceipt
import com.zczy.cargo_owner.order.confirm.bean.ReqSubUploadImgOfReceiptV1
import com.zczy.cargo_owner.order.confirm.bean.RxBusUploadImgSuccessV2
import com.zczy.comm.ui.BaseActivity
import com.zczy.comm.utils.imageselector.ImageSelector
import kotlinx.android.synthetic.main.order_activity_upload_returned_order_photo_v2.btnUploadPhoto
import kotlinx.android.synthetic.main.overdue_order_appeal_activity.image_select_view
import java.io.File

/**
 *描述：回单上传
 *auth:宋双朋
 *time:2024/6/18 10:10
 */

open class UploadReturnedOrderPhotoActivityV2 : BaseActivity<ReturnedOrderConfirmModel>() {

    private val orderId by lazy { intent.getStringExtra(ORDER_ID) }

    companion object {
        const val ORDER_ID = "orderId"

        @JvmStatic
        fun jumpPage(context: Context?, orderId: String?) {
            val intent = Intent(context, UploadReturnedOrderPhotoActivityV2::class.java)
            intent.putExtra(ORDER_ID, orderId)
            context?.startActivity(intent)
        }
    }

    override fun getLayout() = R.layout.order_activity_upload_returned_order_photo_v2


    override fun bindView(bundle: Bundle?) {
        image_select_view.maxCount = 5
        bindClickEvent(btnUploadPhoto)
    }

    override fun onSingleClick(v: View) {
        super.onSingleClick(v)
        when (v.id) {
            R.id.btnUploadPhoto -> {
                val req = ReqSubUploadImgOfReceipt()
                val imgList = image_select_view.imgList
                if (imgList.isEmpty()) {
                    showDialogToast("至少上传一张图片")
                    return
                }
                req.orderId = orderId
                val map1 = imgList.map { ReqSubUploadImgOfReceiptV1(imgUrl = it.imageId) }
                req.uploadImgList = map1.toMutableList()
                getViewModel(ReturnedOrderConfirmModel::class.java).subUploadImgOfReceipt(
                    req = req
                )
            }
        }
    }

    override fun initData() {}

    @LiveDataMatch(tag = "上传文件成功")
    open fun onFileSuccess(tag: File, url: String) {
        image_select_view.setSelectData(url)
    }

    @LiveDataMatch(tag = "上传文件失败")
    open fun onFileFailure(tag: File, error: String) {
        showToast(error)
    }

    public override fun onActivityResult(requestCode: Int, resultCode: Int, data: Intent?) {
        super.onActivityResult(requestCode, resultCode, data)
        if (resultCode == RESULT_OK) {
            val strings = ImageSelector.obtainPathResult(data)
            strings?.let { viewModel?.upFile(it) }
        }
    }

    @LiveDataMatch
    open fun subUploadImgOfReceiptSuccess() {
        RxBusEventManager.postEvent(RxBusUploadImgSuccessV2(true))
        finish()
    }
}