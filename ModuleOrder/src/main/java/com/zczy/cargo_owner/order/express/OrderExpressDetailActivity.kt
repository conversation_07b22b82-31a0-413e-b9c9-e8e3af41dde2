package com.zczy.cargo_owner.order.express

import android.app.Activity
import android.content.Intent
import android.os.Bundle
import androidx.recyclerview.widget.LinearLayoutManager
import androidx.recyclerview.widget.RecyclerView
import android.text.TextUtils
import android.view.View
import android.widget.TextView
import com.sfh.lib.mvvm.annotation.LiveDataMatch
import com.zczy.cargo_owner.order.R
import com.zczy.cargo_owner.order.express.adapter.OrderExpressDetailAdapter
import com.zczy.cargo_owner.order.express.adapter.OrderExpressDetailAdapter.Companion.TYPE_0
import com.zczy.cargo_owner.order.express.adapter.OrderExpressDetailAdapter.Companion.TYPE_1
import com.zczy.cargo_owner.order.express.adapter.OrderExpressDetailAdapter.Companion.TYPE_2
import com.zczy.cargo_owner.order.express.bean.UserExpressItemDataWrapper
import com.zczy.cargo_owner.order.express.model.OrderExpressDetailModel
import com.zczy.cargo_owner.order.express.req.RspCarrierExpressData
import com.zczy.cargo_owner.order.express.req.RspConsignorExpressDetailForMobile
import com.zczy.cargo_owner.order.express.req.showState
import com.zczy.comm.permission.PermissionCallBack
import com.zczy.comm.permission.PermissionUtil
import com.zczy.comm.ui.BaseActivity
import com.zczy.comm.utils.CommUtils
import com.zczy.comm.utils.PhoneUtil
import com.zczy.comm.utils.ex.setVisible
import com.zczy.comm.utils.json.toJson
import com.zczy.comm.utils.json.toJsonObject
import kotlinx.android.synthetic.main.order_express_detail_activity.*

/**
 * PS: 快递详情
 * Created by sdx on 2018/11/5.
 */
open class OrderExpressDetailActivity : BaseActivity<OrderExpressDetailModel>() {

    private val expressData by lazy { obtainData(intent) }

    private val swipeRefreshMoreLayout by lazy { findViewById<androidx.recyclerview.widget.RecyclerView>(R.id.swipe_refresh_more_layout) }

    private val tvExpressOrderValue by lazy { findViewById<TextView>(R.id.tv_express_order_value) }
    private val tvFreightOrderValue by lazy { findViewById<TextView>(R.id.tv_freight_order_value) }
    private val tvPlateNumberValue by lazy { findViewById<TextView>(R.id.tv_plate_number_value) }
    private val tvExpressCompanyValue by lazy { findViewById<TextView>(R.id.tv_express_company_value) }
    private val tv_express_sign_state by lazy { findViewById<TextView>(R.id.tv_express_sign_state) }
    private val tv_express_sign_state_value by lazy { findViewById<TextView>(R.id.tv_express_sign_state_value) }

    private val mAdapter = OrderExpressDetailAdapter()

    companion object {
        private const val EXTRA_EXPRESS_DATA = "extra_express_data"

        @JvmStatic
        fun start(activity: Activity, data: RspCarrierExpressData) {
            val intent = Intent(activity, OrderExpressDetailActivity::class.java)
            intent.putExtra(EXTRA_EXPRESS_DATA, data.toJson())
            activity.startActivity(intent)
        }

        fun obtainData(intent: Intent?): RspCarrierExpressData {
            return intent?.getStringExtra(EXTRA_EXPRESS_DATA)?.toJsonObject(RspCarrierExpressData::class.java)
                ?: RspCarrierExpressData()
        }
    }

    override fun getLayout(): Int = R.layout.order_express_detail_activity

    override fun bindView(bundle: Bundle?) {
        initExpressView()

        bindClickEvent(btn_copy)
        bindClickEvent(btn_call)
    }

    override fun initData() {
        tvFreightOrderValue.text = expressData.orderId
        tvPlateNumberValue.text = expressData.plateNumber
        viewModel?.getNetInfo(expressData.detailId, expressData.orderId)
    }

    override fun onSingleClick(v: View) {
        super.onSingleClick(v)
        when (v.id) {
            // 复制
            R.id.btn_copy -> {
                val isCopy = CommUtils.copyText(
                    this@OrderExpressDetailActivity,
                    "运单号", tvFreightOrderValue.text.toString().trim()
                )
                if (isCopy) {
                    showToast("复制成功")
                } else {
                    showToast("复制失败")
                }
            }
            R.id.btn_call -> {
//                PermissionUtil.call(this@OrderExpressDetailActivity, object : PermissionCallBack() {
//                    override fun onHasPermission() {
                        PhoneUtil.callPhone(this@OrderExpressDetailActivity, expressData.driverMobile)
//                    }
//                })
            }
        }
    }

    private fun initExpressView() {
        swipeRefreshMoreLayout.visibility = View.VISIBLE
        initList()
    }

    private fun initList() {
        swipeRefreshMoreLayout.apply {
            val manager =
                androidx.recyclerview.widget.LinearLayoutManager(this@OrderExpressDetailActivity)
            layoutManager = manager
            adapter = mAdapter
        }
    }

    @LiveDataMatch
    open fun getNetInfoSuccess(data: RspConsignorExpressDetailForMobile) {
        tvExpressOrderValue.text = data.expressNum
        tvFreightOrderValue.text = expressData.orderId
        tvPlateNumberValue.text = expressData.plateNumber
        tvExpressCompanyValue.text = data.expressCompanyName

        btn_copy.setVisible(true)
        btn_call.setVisible(true)

        mAdapter.setNewData(data.expressInfo.mapIndexed { index, it ->
            // TYPE_0 = 0 // 最后一个完成的 TYPE_1 = 1 // 除去完成倒数三个的  TYPE_2 = 2 // 其他的
            val type = when (index) {
                0 -> TYPE_0
                in 1..3 -> TYPE_1
                else -> TYPE_2
            }
            UserExpressItemDataWrapper(data = it, type = type)
        })

        tv_express_sign_state_value.text = data.showState()
        tv_express_sign_state.setVisible(true)
        tv_express_sign_state_value.setVisible(true)
        if (!TextUtils.isEmpty(data.signReason)) {
            tv_sign_reason.setVisible(true)
            tv_sign_reason_value.setVisible(true)
            tv_sign_reason_value.text = data.signReason
        }
    }
}
