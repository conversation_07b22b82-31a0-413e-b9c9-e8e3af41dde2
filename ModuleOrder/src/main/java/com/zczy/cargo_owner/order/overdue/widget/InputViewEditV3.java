package com.zczy.cargo_owner.order.overdue.widget;

import android.content.Context;
import android.content.res.TypedArray;
import androidx.annotation.DrawableRes;
import androidx.annotation.NonNull;
import androidx.annotation.Nullable;
import android.text.Editable;
import android.text.InputFilter;
import android.text.InputType;
import android.text.SpannableStringBuilder;
import android.text.TextUtils;
import android.text.TextWatcher;
import android.util.AttributeSet;
import android.view.View;
import android.widget.EditText;
import android.widget.ImageView;
import android.widget.TextView;

import com.zczy.cargo_owner.order.R;
import com.zczy.comm.widget.inputv2.BaseInputView;
import com.zczy.comm.widget.inputv2.BaseListener;

public class InputViewEditV3 extends BaseInputView<InputViewEditV3.Listener> {

    // 内容提示语
    private String mHintStr;
    private EditText editText;
    private TextView tvTitle2;
    private TextView tvHint;
    private ImageView imgRight;

    public InputViewEditV3(Context context) {
        super(context);
    }

    public InputViewEditV3(Context context, @Nullable AttributeSet attrs) {
        super(context, attrs);
    }

    public InputViewEditV3(Context context, @Nullable AttributeSet attrs, int defStyleAttr) {
        super(context, attrs, defStyleAttr);
    }

    @Override
    public void initAttrs(@Nullable AttributeSet attrs) {
        if (attrs != null) {
            TypedArray a = getContext().obtainStyledAttributes(attrs, R.styleable.InputViewEdit);
            String contentHintStr = a.getString(R.styleable.InputViewEdit_input_edit_content_hint);
            boolean hideContentHint = a.getBoolean(R.styleable.InputViewEdit_input_edit_hide_content_hint, false);
            a.recycle();
            if (!TextUtils.isEmpty(contentHintStr)) {
                mHintStr = contentHintStr;
            } else {
                mHintStr = "请输入";
            }
            if(hideContentHint) {
                this.mHintStr = "";
            }
        }
    }

    @Override
    protected void bindView() {
        initEdit();
        initImageRight();
    }

    @Override
    public int getInflateLayout() {
        return R.layout.base_ui_input_view_2_edit_v3;
    }

    private void initEdit() {
        editText = findViewById(R.id.et_input);
        tvTitle2 = findViewById(R.id.tv_title_2);
        tvHint = findViewById(R.id.tv_hint);
        editText.addTextChangedListener(new TextWatcher() {
            @Override
            public void beforeTextChanged(CharSequence s, int start, int count, int after) {

            }

            @Override
            public void onTextChanged(CharSequence s, int start, int before, int count) {

            }

            @Override
            public void afterTextChanged(Editable s) {
                if (TextUtils.isEmpty(s)) {
                    tvHint.setVisibility(View.VISIBLE);
                } else {
                    tvHint.setVisibility(View.GONE);
                }
                if (mListener != null) {
                    mListener.onTextChanged(getId(), InputViewEditV3.this, s.toString());
                }
            }
        });
    }

    private void initImageRight() {
        imgRight = findViewById(R.id.img_right);
        imgRight.setOnClickListener(this);
    }

    @Override
    public void onClick(View v) {
        super.onClick (v);
        if (mListener != null && imgRight == v) {
            mListener.onClickRightImg(getId(), InputViewEditV3.this);
        }
    }

    @Override
    protected void initData() {
        tvHint.setText(mHintStr);
        imgRight.setVisibility(View.GONE);
    }

    @Override
    public void setEnabled(boolean enabled) {
        super.setEnabled(enabled);
        editText.setEnabled(enabled);
    }

    public void setTitleSB(@NonNull SpannableStringBuilder title) {
        tvTitle.setText(title);
    }

    /**
     * title 下文字
     * @param str
     */
    public void setTitle2(String str) {
        if(str.isEmpty()) {
            tvTitle2.setVisibility(View.GONE);
        } else {
            tvTitle2.setVisibility(View.VISIBLE);
        }
        tvTitle2.setText(str);
    }

    /**
     */
    @NonNull
    public InputViewEditV3 setMaxLength(int max) {
        editText.setFilters(new InputFilter[]{new InputFilter.LengthFilter(max)});
        return this;
    }

    /**
     * 只能输入数字和小数点
     */
    @NonNull
    public InputViewEditV3 setTypeNum() {
        setInputType(InputType.TYPE_CLASS_NUMBER | InputType.TYPE_NUMBER_FLAG_DECIMAL);
        return this;
    }

    /**
     */
    @NonNull
    public InputViewEditV3 setInputType(int type) {
        editText.setInputType(type);
        return this;
    }

    /**
     */
    @NonNull
    public InputViewEditV3 setRightImg(@DrawableRes int id) {
        imgRight.setVisibility(View.VISIBLE);
        imgRight.setImageResource(id);
        return this;
    }

    public void setContent(@NonNull String content) {
        String s = content.trim();
        editText.setText(s);
    }

    @NonNull
    public String getContent() {
        return editText.getText().toString().trim();
    }

    public static abstract class Listener extends BaseListener<InputViewEditV3> {

        /**
         * 当文字变化
         */
        public abstract void onTextChanged(int viewId, @NonNull InputViewEditV3 view, @NonNull String s);

        /**
         * 点击 输入框 右图片 事件相应
         */
        public void onClickRightImg(int viewId, @NonNull InputViewEditV3 view) {
        }
    }

    public EditText getEditText() {

        return editText;
    }


    public ImageView getImgRight() {

        return imgRight;
    }
}
