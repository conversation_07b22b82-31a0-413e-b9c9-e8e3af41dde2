package com.zczy.cargo_owner.order.settlement.bean

import android.os.Parcelable
import android.text.TextUtils
import com.zczy.comm.http.entity.ResultData
import kotlinx.android.parcel.Parcelize

/**
 * @description
 * <AUTHOR> 韩正亚
 * @company 中储智运科技股份有限公司
 * @copyright （版权）中储南京智慧物流科技有限公司所有
 * @date 2019/4/16
 */
@Parcelize
data class SettlementApplicationItemBean(
    val advanceFlag: String? = null,
    val allCargoName: String? = null,
    val backCheck: String? = null,
    val backMoney: String? = null,
    val loadPayMoney: String? = null,// 装卸费
    val orgBackMoney: String? = null,
    val cargoCategory: String? = null,
    val carrierCustomerName: String? = null,
    val carrierUserType: String? = null,
    val coderType: String? = null,
    val moneyCountType: String? = null, //运费计算方式
    val consignorCheckMoney: String? = null, //实际付款金额（含税）
    val settMonAftSubConsRewMon: String? = null, //预计最终结算价格（含税）
    val consignorCheckMoneyRate: String? = null, //实际付款金额（不含税）
    val custmerNum: String? = null,
    val detailId: String? = null,
    val driverUserName: String? = null,
    val freightType: String? = null,
    val isAdvance: String? = null,
    val orderId: String? = null,
    val couponsId: String? = null,
    val orderModel: String? = null,
    val payType: String? = null,
    val payee: String? = null,
    val endPlace: String? = null,
    val startPlace: String? = null,
    val plateNumber: String? = null,
    val publishName: String? = null,
    val receiveMoney: String? = null,
    val remark: String? = null,
    val receiveWeight: String? = null,
    val settleApplyState: String? = null,
    val slipLoad: String? = null,
    val specifyFlag: String? = null, // 是否指定 0 否,1 是
    var hzAdvanceMoney: String? = null,
    val advenceFlag: String? = null, //是否预付 0 否,1 是
    val receiptFlag: String? = null, //是否押回单 0 否,1 是
    val oilCardFlag: String? = null, //1是 0不是
    val riskReceiveValue: String? = null, //1是 0不是
    val consignorSubsidiaryName: String? = null, //结算平台
    var selected: Boolean = false,
    var subOrderFileList: List<FileList>? = null,
    var subOrderFileHzList: List<FileList>? = null,
    var deliverWeight: String? = null,
    var returnMoneyTime: String? = null, //回款到期日
    var delistTime: String? = null, //摘单时间
    var subOrderFileDeliverProofList: MutableList<WatermarkPic>? = null,   // 承运方发货单信息 纸质单据
    var subOrderFileDeliverPhotoList: MutableList<WatermarkPic>? = null, // 承运方发货单信息 运单照片
    var subOrderFileProofList: MutableList<WatermarkPic>? = null, // 承运方收货 纸质单据
    var subOrderFilePhotoList: MutableList<WatermarkPic>? = null, // 承运方收货 运单照片
    var isBackUpFlag: String? = null, //1 是，0否 是否备案卸货
    var goodsSource: String? = null, //货物资源类型 1:集装箱资源  2:非集装箱资源
    var lTLOrder: String? = null, //是否零担 0 否 1 是
    var userCouponId: String? = null, //当前优惠券id
    var couponMoney: String? = null, //当前优惠券金额
    val selfComment: String?,//自定义编号
    val assistanceServiceCharges: String?,//物流辅助服务费
    val additionalCharges: String?,//附加运费
    var carrierReceivePicList: MutableList<WatermarkPic>? = null,   // 卸货拍照图片
    var existConsignorFeeConfigFlag: String? = null, //是否存在承运方费用配置 1展示
    var deliverReceiptNumber: String? = null, //确认发货单据号 有值就展示
) : Parcelable, ResultData()

@Parcelize
class WatermarkPic(
    var picUrl: String? = null, //带水印图片
    var picOrgUrl: String? = null //原始图片不带水印
) : Parcelable

fun SettlementApplicationItemBean.getCouponMoney(): String {
    return if (TextUtils.isEmpty(couponMoney)) {
        ""
    } else {
        "可减¥${couponMoney}"
    }
}

fun SettlementApplicationItemBean.showSelfComment(): String {
    return if (TextUtils.isEmpty(selfComment)) {
        "自定义编号："
    } else {
        "自定义编号：$selfComment"
    }
}

fun SettlementApplicationItemBean.getReturnMoneyTime(): String {
    return returnMoneyTime ?: ""
}

fun SettlementApplicationItemBean.showReturnMoneyTime(): Boolean {
    return !TextUtils.isEmpty(returnMoneyTime)
}

fun SettlementApplicationItemBean.showGoodsSource(): Boolean {
    return TextUtils.equals(goodsSource, "1")
}

@Parcelize
data class FileList(
    var pictureUrl: String? = null
) : Parcelable

fun SettlementApplicationItemBean.formatPlace(place: String): String {
    return place.let {
        if (it.length > 6) {
            "${it.substring(0, 6)}..."
        } else {
            it
        }
    }
}