package com.zczy.cargo_owner.order.transport.adapter;

import android.text.Html;
import android.text.Spanned;

import com.chad.library.adapter.base.BaseQuickAdapter;
import com.chad.library.adapter.base.BaseViewHolder;
import com.zczy.cargo_owner.order.R;
import com.zczy.cargo_owner.order.transport.bean.SearchRecord;

public class SearchHistoryAdapter extends BaseQuickAdapter<SearchRecord, BaseViewHolder> {
   public String mKey = "";

    public SearchHistoryAdapter() {
        super(R.layout.search_history_item);
    }

    @Override
    protected void convert(BaseViewHolder helper, SearchRecord item) {

        helper.setText(R.id.tv_plate_number, buildText(item.getRname()));
    }

    private Spanned buildText(String text) {

        String newText = text.replace(mKey, "<font color='#5086FC'>" + mKey + "</font>");
        return Html.fromHtml(newText);

    }


}
