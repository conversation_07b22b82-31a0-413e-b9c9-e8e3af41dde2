package com.zczy.cargo_owner.order.transport.adapter;


import android.text.TextUtils;
import android.widget.TextView;

import com.chad.library.adapter.base.BaseQuickAdapter;
import com.chad.library.adapter.base.BaseViewHolder;
import com.zczy.cargo_owner.order.R;
import com.zczy.cargo_owner.order.transport.bean.TransportWaybill;


public class TransportListAdapter extends BaseQuickAdapter<TransportWaybill, BaseViewHolder> {

    public TransportListAdapter() {
        super(R.layout.transport_item);
    }

    @Override
    protected void convert(BaseViewHolder helper, TransportWaybill item) {

        helper.addOnClickListener(R.id.tv_copy);
        helper.setText(R.id.tv_waybill_no,"运单号：" + item.getOrderId());
        helper.setText(R.id.tv_carrier_name,"承运人：" + item.getCarrierName());
        helper.setText(R.id.tv_plate_number,item.getPlateNum());
        TextView tvState = helper.getView(R.id.tv_state);
        //0 正常 1异常
        if (TextUtils.equals(item.getState(),"1")) {
            tvState.setText("异常");
            tvState.setBackgroundResource(R.drawable.base_view_eb8581_soild_10radius);
        } else {
            tvState.setText("运输中");
            tvState.setBackgroundResource(R.drawable.base_view_72a4ff_soild_10radius);
        }
    }
}
