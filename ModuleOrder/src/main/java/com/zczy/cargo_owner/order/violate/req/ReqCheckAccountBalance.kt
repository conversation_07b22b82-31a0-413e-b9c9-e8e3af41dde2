package com.zczy.cargo_owner.order.violate.req

import com.zczy.comm.http.entity.BaseNewRequest
import com.zczy.comm.http.entity.BaseRsp
import com.zczy.comm.http.entity.ResultData

/**
 *  desc: 校验智运账本余额是否不足以支付违约责任金额接口
 *  user: 宋双朋
 *  time: 2025/1/17 15:18
 */
data class ReqCheckAccountBalance(
    var breachMoney: String? = null,//责任方赔偿金额
    var breachPolicyMoney: String? = null,//货物保障服务费金额
    var freightLossPolicyMoney: String? = null,//运费损失保障服务费
    var isStop: String? = null, //是否终止，1-是，0-否
    var orderId: String? = null,//运单号
    var personalAccidentPolicyMoney: String? = null,//人身安全保障服务费金额
    var modeType: String? = null,////业务模式：0：网货专票 1：代开专票  2：不要票
) : BaseNewRequest<BaseRsp<RspCheckAccountBalance>>("wo-app/order/breach/checkAccountBalance")

class RspCheckAccountBalance(
    var insufficientFlag: String? = null,//账户余额不足，1-是，0-否
) : ResultData()
