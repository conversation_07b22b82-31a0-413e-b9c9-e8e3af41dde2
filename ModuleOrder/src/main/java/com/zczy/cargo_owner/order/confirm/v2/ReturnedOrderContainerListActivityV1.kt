package com.zczy.cargo_owner.order.confirm.v2

import android.annotation.SuppressLint
import android.content.Context
import android.content.Intent
import android.os.Bundle
import androidx.core.content.ContextCompat
import androidx.recyclerview.widget.LinearLayoutManager
import android.text.SpannableString
import android.text.SpannableStringBuilder
import android.text.Spanned
import android.text.style.ForegroundColorSpan
import com.sfh.lib.mvvm.annotation.LiveDataMatch
import com.zczy.cargo_owner.order.R
import com.zczy.cargo_owner.order.confirm.adapter.ReturnedOrderContainerListAdapterV1
import com.zczy.cargo_owner.order.confirm.bean.ContainerPageList
import com.zczy.cargo_owner.order.confirm.bean.ReqReturnedOrderContainerListV1
import com.zczy.cargo_owner.order.confirm.bean.RspReturnedOrderContainerListV1
import com.zczy.cargo_owner.order.confirm.model.ReturnedOrderContainerListModelV1
import com.zczy.comm.ui.BaseActivity
import com.zczy.comm.utils.dp2px
import com.zczy.comm.widget.itemdecoration.SpaceItemDecoration
import kotlinx.android.synthetic.main.returned_order_container_list_v1.*

/**
 * 功能描述: 发货详情
 * <AUTHOR>
 * @date 2022/3/8-19:33
 */

class ReturnedOrderContainerListActivityV1 : BaseActivity<ReturnedOrderContainerListModelV1>() {

    private val mReturnedOrderContainerListAdapter = ReturnedOrderContainerListAdapterV1()
    private val orderId by lazy { intent.getStringExtra(ORDER_ID) }

    companion object {

        const val ORDER_ID = "order_id"

        @JvmStatic
        fun jumpUi(context: Context, orderId: String) {
            val intent = Intent(context, ReturnedOrderContainerListActivityV1::class.java)
            intent.putExtra(ORDER_ID, orderId)
            context.startActivity(intent)
        }
    }

    override fun getLayout(): Int {
        return R.layout.returned_order_container_list_v1
    }

    override fun bindView(bundle: Bundle?) {
        recyclerView.apply {
            layoutManager =
                androidx.recyclerview.widget.LinearLayoutManager(this@ReturnedOrderContainerListActivityV1)
            addItemDecoration(SpaceItemDecoration(dp2px(0f)))
            mReturnedOrderContainerListAdapter.bindToRecyclerView(recyclerView)
            adapter = mReturnedOrderContainerListAdapter
        }
    }

    override fun initData() {
        viewModel?.queryList(ReqReturnedOrderContainerListV1(orderId = orderId?:""))
    }

    @SuppressLint("SetTextI18n")
    @LiveDataMatch(tag = "发货详情接口")
    open fun onListSuccess(data: ContainerPageList<RspReturnedOrderContainerListV1>?) {
        data?.let {
            tvContainerName.text = it.containerName ?: ""
            tvContainerPrice.text = "单价：" + it.containerUnitMoney + "元/箱"

            val spannableStringBuilder = SpannableStringBuilder()
            val spannableString1 = SpannableString("总计：")
            val spannableString2 = SpannableString(it.containerTotalNoCount)
            spannableString2.setSpan(
                ForegroundColorSpan(
                    ContextCompat.getColor(
                        this@ReturnedOrderContainerListActivityV1,
                        R.color.color_fb4f2a
                    )
                ),
                0,
                spannableString2.length,
                Spanned.SPAN_EXCLUSIVE_EXCLUSIVE
            )
            val spannableString3 = SpannableString("箱")
            spannableStringBuilder.append(spannableString1)
                .append(spannableString2)
                .append(spannableString3)
            tvTotal.text = spannableStringBuilder
            mReturnedOrderContainerListAdapter.setNewData(it.rootArray)
        }
    }

}