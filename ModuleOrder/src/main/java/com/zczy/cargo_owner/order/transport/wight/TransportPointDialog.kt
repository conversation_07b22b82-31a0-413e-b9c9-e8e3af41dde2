package com.zczy.cargo_owner.order.transport.wight

import android.annotation.SuppressLint
import android.os.Bundle
import android.view.View
import com.zczy.cargo_owner.order.R
import com.zczy.cargo_owner.order.transport.req.ClockInLocationByOrderInfo
import com.zczy.comm.ui.BaseDialog
import kotlinx.android.synthetic.main.dialog_transport_point.view.tvSubmit
import kotlinx.android.synthetic.main.dialog_transport_point.view.tv_location_address
import kotlinx.android.synthetic.main.dialog_transport_point.view.tv_location_lon
import kotlinx.android.synthetic.main.dialog_transport_point.view.tv_location_time

class TransportPointDialog(var data: ClockInLocationByOrderInfo?) : BaseDialog() {
    @SuppressLint("SetTextI18n")
    override fun bindView(view: View, bundle: Bundle?) {
        view.tv_location_address.text = data?.addr
        view.tv_location_time.text = data?.createTime
        view.tv_location_lon.text = "${data?.lon}, ${data?.lat}"
        view.tvSubmit.setOnClickListener {
            dismiss()
        }
    }

    override fun getDialogTag(): String {
        return "TransportPointDialog"
    }

    override fun getDialogLayout(): Int {
        return R.layout.dialog_transport_point
    }

    override fun getDialogType(): DialogType = DialogType.mid
}