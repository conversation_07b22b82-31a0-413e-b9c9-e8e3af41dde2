package com.zczy.cargo_owner.order;

import android.content.Context;
import android.content.Intent;
import android.graphics.Color;
import android.os.Bundle;
import androidx.annotation.Nullable;

import android.text.TextUtils;
import android.view.View;
import android.widget.LinearLayout;
import android.widget.TextView;

import com.bigkoo.pickerview.builder.TimePickerBuilder;
import com.bigkoo.pickerview.listener.OnTimeSelectListener;
import com.bigkoo.pickerview.view.TimePickerView;
import com.contrarywind.view.WheelView;
import com.sfh.lib.rx.ui.UtilRxView;
import com.sfh.lib.ui.AbstractLifecycleActivity;
import com.zczy.cargo_owner.order.entity.EWaybill;
import com.zczy.cargo_owner.order.entity.JdSearchBean;
import com.zczy.comm.widget.dialog.ChooseDialogV1;
import com.zczy.comm.widget.inputv2.InputViewClick;
import com.zczy.comm.widget.inputv2.InputViewEdit;

import java.text.SimpleDateFormat;
import java.util.Arrays;
import java.util.Calendar;
import java.util.Date;

import io.reactivex.disposables.Disposable;
import kotlin.Unit;
import kotlin.jvm.functions.Function2;

/*=============================================================================================
 * 功能描述:ZCZY-8777 冀东客户数字供应链解决方案需求 -- 搜索
 *---------------------------------------------------------------------------------------------
 *  涉及业务 & 更新说明:
 *
 *
 *--------------------------------------------------------------------------------------------
 *  <AUTHOR>  on  2022/3/31
 *  @Company    中储智运科技股份有限公司
 *  @Copyright （版权）中储南京智慧物流科技有限公司所有                                                                                        *
 *=============================================================================================*/
public class WaybillSearchJiDongActivity extends AbstractLifecycleActivity implements View.OnClickListener {
    private InputViewEdit input_goods_name;
    private InputViewEdit input_start_address;
    private InputViewEdit input_end_address;
    private InputViewEdit input_order_num;
    private InputViewEdit input_car_num;
    private InputViewEdit input_order_flag;
    private TextView tv_start_time;
    private TextView tv_end_time;
    private TextView tv_search;
    private Calendar selectedDate;
    private LinearLayout lySearch;
    private View fy_layout;
    InputViewClick tv_select_type;

    public static void start(Context context) {
        Intent intent = new Intent(context, WaybillSearchJiDongActivity.class);
        context.startActivity(intent);
    }

    @Override
    protected void onCreate(@Nullable Bundle savedInstanceState) {
        super.onCreate(savedInstanceState);
        setContentView(R.layout.order_waybill_search_jidong_activity);
        initView();
    }

    private void initView() {
        input_goods_name = findViewById(R.id.input_goods_name);
        input_start_address = findViewById(R.id.input_start_address);
        input_end_address = findViewById(R.id.input_end_address);
        input_order_num = findViewById(R.id.input_order_num);
        input_car_num = findViewById(R.id.input_car_num);
        input_order_flag = findViewById(R.id.input_order_flag);
        tv_start_time = findViewById(R.id.tv_start_time);
        tv_end_time = findViewById(R.id.tv_end_time);
        tv_search = findViewById(R.id.tv_search);
        fy_layout = findViewById(R.id.fy_layout);
        lySearch = findViewById(R.id.ly_search);
        tv_start_time.setOnClickListener(this);
        tv_end_time.setOnClickListener(this);
        tv_search.setOnClickListener(this);
        Disposable disposable = UtilRxView.clicks(tv_search, 1000, o -> searchData());
        this.putDisposable(disposable);


         tv_select_type = findViewById(R.id.tv_select_type);
        tv_select_type.setOnClickListener(new View.OnClickListener() {
            @Override
            public void onClick(View v) {
                ChooseDialogV1.instance(Arrays.asList("是","否"))
                        .setClick(new Function2<String, Integer, Unit>() {
                            @Override
                            public Unit invoke(String s, Integer integer) {
                                tv_select_type.setContent(s);
                                return null;
                            }
                        })
                        .show(WaybillSearchJiDongActivity.this);
            }
        });
    }

    private WaybillListFragment baseWaybillListFragment;

    private void searchData() {
        lySearch.setVisibility(View.GONE);
        JdSearchBean jdSearchBean = new JdSearchBean();
        jdSearchBean.setAllCargoName(input_goods_name.getContent());
        jdSearchBean.setDespatchPlace(input_start_address.getContent());
        jdSearchBean.setDeliverPlace(input_end_address.getContent());
        jdSearchBean.setOrderId(input_order_num.getContent());
        jdSearchBean.setVehicleName(input_car_num.getContent());
        jdSearchBean.setOrderMark(input_order_flag.getContent());
        jdSearchBean.setJdOutTimeStart(tv_start_time.getText().toString());
        jdSearchBean.setJdOutTimeEnd(tv_end_time.getText().toString());
        jdSearchBean.setModeType(TextUtils.equals("是",tv_select_type.getContent())?"1":
                        TextUtils.equals("否",tv_select_type.getContent())?"0":""
                );
        if (baseWaybillListFragment != null) {
            getSupportFragmentManager().beginTransaction().remove(baseWaybillListFragment).commit();
            baseWaybillListFragment = null;
        }
        baseWaybillListFragment = WaybillListFragment.newInstance(EWaybill.TYPE_ALL, jdSearchBean);
        fy_layout.setVisibility(View.VISIBLE);
        getSupportFragmentManager().beginTransaction().replace(R.id.fy_layout, baseWaybillListFragment).commit();
    }

    @Override
    public void onClick(View v) {
        int id = v.getId();
        if (id == R.id.tv_start_time) {
            showTimePickerView(tv_start_time);

        } else if (id == R.id.tv_end_time) {
            showTimePickerView(tv_end_time);

        }
    }

    private void showTimePickerView(TextView timeView) {
        //系统当前时间
        if (selectedDate == null) {
            selectedDate = Calendar.getInstance();
        }

        TimePickerBuilder timePickerBuilder = new TimePickerBuilder(this, new OnTimeSelectListener() {

            @Override
            public void onTimeSelect(Date date, View v) {
                selectedDate.setTime(date);
                timeView.setText(new SimpleDateFormat("yyyy-MM-dd HH:mm:00").format(date));
            }
        })
                //时间选择器 ，自定义布局
                .setDividerColor(Color.parseColor("#999999"))
                .setDividerType(WheelView.DividerType.FILL)
                .setContentTextSize(20)
                .setDate(selectedDate)
                //设置选中项的颜色
                .setTextColorCenter(Color.parseColor("#333333"))
                .isCenterLabel(true)
                //设置两横线之间的间隔倍数  m
                .setLineSpacingMultiplier(2.0f)
                .setLabel("", "", "", "", "", "")
                .setTextXOffset(0, 0, 0, 0, 0, 0)
                .setOutSideColor(Color.parseColor("#50000000"))
                .setOutSideCancelable(true);
        //设置空字符串以隐藏单位提示   hide label //设置展示的
        timePickerBuilder.setType(new boolean[]{true, true, true, true, true, false});
        TimePickerView timePickerView = timePickerBuilder.build();
        timePickerView.show();
    }
}
