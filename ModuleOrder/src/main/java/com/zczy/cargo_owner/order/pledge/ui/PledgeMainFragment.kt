package com.zczy.cargo_owner.order.pledge.ui

import android.annotation.SuppressLint
import android.os.Bundle
import android.text.InputFilter
import android.text.InputType
import android.text.TextUtils
import android.view.View
import com.jakewharton.rxbinding2.widget.RxTextView
import com.sfh.lib.AppCacheManager
import com.sfh.lib.mvvm.annotation.LiveDataMatch
import com.sfh.lib.rx.ui.UtilRxView
import com.sfh.lib.ui.dialog.DialogBuilder
import com.sfh.lib.utils.UtilTool
import com.zczy.cargo_owner.libcomm.config.SubUserAuthUtils
import com.zczy.cargo_owner.libcomm.utils.getChildPermission
import com.zczy.cargo_owner.libcomm.widget.AgreeAdjustDetailDialog
import com.zczy.cargo_owner.order.R
import com.zczy.cargo_owner.order.pledge.PledgeMainDetailActivity
import com.zczy.cargo_owner.order.pledge.adapter.PledgeMainAdapter
import com.zczy.cargo_owner.order.pledge.model.PledgeMainModel
import com.zczy.cargo_owner.order.pledge.req.ReqPledgeMainApply
import com.zczy.cargo_owner.order.pledge.req.RspPledgeMain
import com.zczy.cargo_owner.order.pledge.req.showReceiptMoney
import com.zczy.comm.CommServer
import com.zczy.comm.http.entity.PageList
import com.zczy.comm.ui.BaseFragment
import com.zczy.comm.utils.CommUtils
import com.zczy.comm.utils.NumUtil
import com.zczy.comm.utils.PhoneUtil.callPhone
import com.zczy.comm.utils.ex.setVisible
import com.zczy.comm.utils.ex.toDoubleRoundDown
import com.zczy.comm.utils.ex.toDoubleRoundDownString
import com.zczy.comm.widget.pulltorefresh.CommEmptyView
import io.reactivex.android.schedulers.AndroidSchedulers
import kotlinx.android.synthetic.main.pledge_main_fragment.cl_warning
import kotlinx.android.synthetic.main.pledge_main_fragment.iv_2
import kotlinx.android.synthetic.main.pledge_main_fragment.swipeRefreshMoreLayout
import kotlinx.android.synthetic.main.pledge_reason_dialog.view.et3
import kotlinx.android.synthetic.main.pledge_reason_dialog.view.et5
import kotlinx.android.synthetic.main.pledge_reason_dialog.view.etRemark
import kotlinx.android.synthetic.main.pledge_reason_dialog.view.tv2
import kotlinx.android.synthetic.main.pledge_reason_dialog.view.tv3
import kotlinx.android.synthetic.main.pledge_reason_dialog.view.tv5
import kotlinx.android.synthetic.main.pledge_reason_dialog.view.tv_count

/**
 *  user: ssp
 *  time: 2020/6/15 14:10
 *  desc: 索要押金管理
 */

class PledgeMainFragment : BaseFragment<PledgeMainModel>() {

    private val pledgeAdapter: PledgeMainAdapter = PledgeMainAdapter()
    private var applyState = ""

    companion object {

        const val TYPE_0 = 0    //  未申请
        const val TYPE_1 = 1    //  已申请
        private const val QUERY_APPLY_STATE = "QUERY_APPLY_STATE"

        fun newInstance(type: String): PledgeMainFragment {
            val args = Bundle()
            args.putString(QUERY_APPLY_STATE, type)
            val fragment = PledgeMainFragment()
            fragment.arguments = args
            return fragment
        }
    }

    override fun getLayout(): Int {
        return R.layout.pledge_main_fragment
    }

    override fun bindView(view: View, bundle: Bundle?) {

        arguments?.let {
            applyState = it.getString(QUERY_APPLY_STATE) ?: ""
        }
        if (TextUtils.equals(applyState, TYPE_0.toString())) {
            cl_warning.setVisible(true)
        } else {
            cl_warning.setVisible(false)
        }
        iv_2.setOnClickListener {
            cl_warning.setVisible(false)
        }

        val emptyView = CommEmptyView.creatorDef(context)
        swipeRefreshMoreLayout.apply {
            setAdapter(pledgeAdapter, true)
            setEmptyView(emptyView)
            addOnItemChildClickListener { adapter, view, position ->
                val item = adapter?.getItem(position) as RspPledgeMain
                when (view?.id) {
                    R.id.tvPledge -> {
                        //索要押金  索要押金子帐号权限控制
                        val login = CommServer.getUserServer().login
                        val askForDeposit = SubUserAuthUtils.get().askForDeposit.getChildPermission()
                        if (!TextUtils.isEmpty(askForDeposit) && login.relation.isChildAccount) {
                            AgreeAdjustDetailDialog(askForDeposit).show(activity)
                        } else {
                            val reqPledgeMainApply = ReqPledgeMainApply()
                            reqPledgeMainApply.orderId = item.orderId
                            pledgeShowDialog(reqPledgeMainApply, item.showReceiptMoney())
                        }

                    }

                    R.id.tvDetail -> {
                        <EMAIL>?.let { PledgeMainDetailActivity.start(it, item.orderId) }
                    }

                    R.id.tv_call_phone -> {
                        // 用intent启动拨打电话
                        callPhone(AppCacheManager.getApplication(), item.carrierMobile)
                    }

                    R.id.tv_copy -> {
                        val isCopy = CommUtils.copyText(
                            <EMAIL>,
                            "订单ID",
                            item.orderId
                        )
                        if (isCopy) {
                            showToast("复制成功")
                        } else {
                            showToast("复制失败")
                        }
                    }
                }
            }
            setOnLoadListener2 { nowPage -> viewModel?.queryList(nowPage, applyState, "") }
        }
    }

    override fun initData() {
        swipeRefreshMoreLayout.onAutoRefresh()
    }

    @SuppressLint("InflateParams", "SetTextI18n")
    private fun pledgeShowDialog(reqPledgeMainApply: ReqPledgeMainApply, totalMoney: Double) {
        val dialogBuilder = DialogBuilder()
        val view = layoutInflater.inflate(R.layout.pledge_reason_dialog, null)
        UtilTool.setEditTextInputSize(view.et3, 2)
        UtilTool.setEditTextInputSize(view.et5, 2)
        view.tv2.text = totalMoney.toString() + "元"
        view.et3.inputType = InputType.TYPE_CLASS_NUMBER or InputType.TYPE_NUMBER_FLAG_DECIMAL
        view.et5.inputType = InputType.TYPE_CLASS_NUMBER or InputType.TYPE_NUMBER_FLAG_DECIMAL
        view.etRemark.filters = arrayOf<InputFilter>(InputFilter.LengthFilter(50))
        val disposable = RxTextView
            .textChanges(view.etRemark)
            .observeOn(AndroidSchedulers.mainThread())
            .subscribe { charSequence ->
                view.tv_count.text = "(${charSequence.length}/50)"
            }
        this.putDisposable(disposable)
        UtilRxView.clicks(view.tv3, 1000) {
            val builder = DialogBuilder()
            builder.title = "提示"
            builder.message = "索要金额指索要成功后平台将部分金额返还给货主账号，如金额未超过100元，司机点击同意后予以返还，如金额超过100元，将由平台审核确认后予以返还。"
            builder.isHideCancel = true
            showDialog(builder)
        }

        UtilRxView.clicks(view.tv5, 1000) {
            val builder = DialogBuilder()
            builder.title = "提示"
            builder.message = "支付司机金额指平台会将索要金额扣除后，剩余的押金总额支付给司机。"
            builder.isHideCancel = true
            showDialog(builder)
        }
        UtilRxView.afterTextChangeEvents(view.et3, 1000) {
            if (view.et5.isFocused) {
                //获取焦点的时候表示输入更改 跳出监听
                return@afterTextChangeEvents
            }
            //索要金额
            if (it.isEmpty()) {
                return@afterTextChangeEvents
            }
            val money = NumUtil.sub(totalMoney, it.toString().toDoubleRoundDown(2))
            if (money < 0) {
                showToast("索要金额不能大于押金总额！")
                return@afterTextChangeEvents
            }
            view.et5.setText(money.toString().toDoubleRoundDownString(2))
        }
        UtilRxView.afterTextChangeEvents(view.et5, 1000) {
            //支付司机金额
            if (view.et3.isFocused) {
                //获取焦点的时候表示输入更改 跳出监听
                return@afterTextChangeEvents
            }
            if (it.isEmpty()) {
                return@afterTextChangeEvents
            }
            val money = NumUtil.sub(totalMoney, it.toString().toDoubleRoundDown(2))
            if (money < 0) {
                showToast("支付司机金额不能大于押金总额！")
                return@afterTextChangeEvents
            }
            view.et3.setText(money.toString().toDoubleRoundDownString(2))
        }
        dialogBuilder.view = view
        dialogBuilder.okListener = DialogBuilder.DialogInterface.OnClickListener { dialogInterface, _ ->
            val reason = view.etRemark.text.toString().trim()
            if (TextUtils.isEmpty(reason)) {
                showToast("请输入索要原因！")
                return@OnClickListener
            }
            val money5 = view.et5.text
            if (money5.isEmpty()) {
                showToast("支付司机金额不能为空！")
                return@OnClickListener
            }
            val money3 = view.et3.text
            if (money3.isEmpty()) {
                showToast("索要金额不能为空！")
                return@OnClickListener
            }
            reqPledgeMainApply.claimReason = reason
            reqPledgeMainApply.consignorMoney = money3.toString()
            viewModel?.onApply(reqPledgeMainApply)
            dialogInterface.dismiss()
        }
        dialogBuilder.cancelListener = DialogBuilder.DialogInterface.OnClickListener { dialogInterface, _ ->
            dialogInterface.dismiss()
        }
        showDialog(dialogBuilder)
    }

    @LiveDataMatch
    open fun onQueryList(data: PageList<RspPledgeMain>?) {
        swipeRefreshMoreLayout.onRefreshCompale(data)
    }

    @LiveDataMatch
    open fun onApplySuccess(msg: String) {
        showToast(msg)
        swipeRefreshMoreLayout.onAutoRefresh()
    }

}
