package com.zczy.cargo_owner.order.confirm.bean

import android.os.Parcelable
import com.zczy.comm.http.entity.BaseNewRequest
import com.zczy.comm.http.entity.BaseRsp
import com.zczy.comm.http.entity.ResultData
import kotlinx.android.parcel.Parcelize

/**
 * 功能描述: 根据货物名称查询新版本亏涨吨信息
 * <AUTHOR>
 * @date 2022/5/20-9:41
 */

class ReqQueryEffectRuleByOrderId(
    var orderId: String? = null,    //规则Id
    var ruleId: String? = null
) : BaseNewRequest<BaseRsp<RspQueryEffectRuleByOrderId>>("oms-app/order/receipt/queryRuleDetailByReceiptChangeRuleId")

@Parcelize
class RspQueryEffectRuleByOrderId : ResultData(), Parcelable {
    var lossRuleInfo: QueryEffectRuleByOrderIdV1? = null    //亏吨扣款规则
    var riseRuleInfo: QueryEffectRuleByOrderIdV1? = null    //涨吨扣款规则
    var lossRule: QueryEffectRuleByOrderIdV1? = null
    var riseRule: QueryEffectRuleByOrderIdV1? = null
}

data class QueryEffectRuleByOrderIdV1(
    var ruleName: String? = null,  //规则名称
    var areaRowStrA: String? = null,  //亏/涨吨区间1字符串规则
    var areaRowStrB: String? = null,  //亏/涨吨区间2字符串规则
    var areaRowStrC: String? = null,  //亏/涨吨区间3字符串规则
    var upLimitMoneyRowStr: String? = null  //亏/涨吨区间3字符串规则
)

@Parcelize
data class OrderCommonTonRuleItemV1(
    var tonRuleId: String? = "",//规则id
    var tonRuleName: String? = "",//规则name
    var tonRuleDefaultFlag: String? = "",//是否默认：1 是，0 否
    var tonRuleSuperList: ArrayList<String>? = null,//涨吨规则集合 List<String>
    var tonRuleDeficitList: ArrayList<String>? = null//亏吨规则集合 List<String>
) : Parcelable