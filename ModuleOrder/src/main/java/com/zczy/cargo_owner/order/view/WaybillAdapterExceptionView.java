package com.zczy.cargo_owner.order.view;

import android.content.Context;
import android.util.AttributeSet;
import android.widget.ImageView;
import android.widget.LinearLayout;
import android.widget.TextView;

import androidx.annotation.Nullable;

import com.chad.library.adapter.base.BaseViewHolder;
import com.zczy.cargo_owner.order.R;
import com.zczy.comm.utils.ex.ViewUtil;

public class WaybillAdapterExceptionView extends LinearLayout {
    public WaybillAdapterExceptionView(Context context) {
        super(context);
        init();
    }

    public WaybillAdapterExceptionView(Context context, @Nullable AttributeSet attrs) {
        super(context, attrs);
        init();
    }

    public WaybillAdapterExceptionView(Context context, @Nullable AttributeSet attrs, int defStyleAttr) {
        super(context, attrs, defStyleAttr);
        init();
    }

    TextView tv_exception;
    TextView  tv_handle_exception;
    ImageView iv_header;
    ImageView iv_toast_icon;

    private void init() {
        inflate(getContext(), R.layout.order_waybill_list_adapter_exception_view,this);
        iv_header = findViewById(R.id.iv_header);
        iv_toast_icon = findViewById(R.id.iv_toast_icon);
        tv_exception = findViewById(R.id.tv_exception);
        tv_handle_exception = findViewById(R.id.tv_handle_exception);
    }

    public void show(BaseViewHolder helper, boolean showHandle,String contxt){
        iv_toast_icon.setVisibility(VISIBLE);
        iv_header.setVisibility(GONE);
        helper.addOnClickListener(R.id.tv_handle_exception);
        tv_exception.setText(contxt);
        tv_handle_exception.setVisibility(showHandle?VISIBLE:GONE);
    }
    public void showHead(BaseViewHolder helper, boolean showHandle,boolean showHeadImg,String context){
        iv_toast_icon.setVisibility(GONE);
        ViewUtil.setVisible(iv_header,showHeadImg);
        helper.addOnClickListener(R.id.tv_handle_exception);
        tv_exception.setText(context);
        tv_handle_exception.setVisibility(showHandle?VISIBLE:GONE);
    }
}
