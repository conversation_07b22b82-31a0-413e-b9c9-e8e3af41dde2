package com.zczy.cargo_owner.order.transport.bean;

import java.io.Serializable;

public class ProviceVehicle implements Serializable {
    /**
     * "province": "江苏省",
     *         "vehicleNum": 4,
     *         "orderList": [
     *           "101905271528055900",
     *           "101905271622491702",
     *           "101905271605449204",
     *           "101905281422089767"
     *         ],
     *         "location": "118.778074,32.057236",
     *         "time": "2019-06-11 19:07:47"
     */
    private String time; //纬度
    private String location; //经度
    private String vehicleNum; //数量
    private String province; //省中文

    public void setTime(String time) {
        this.time = time;
    }

    public void setLocation(String location) {
        this.location = location;
    }

    public void setVehicleNum(String vehicleNum) {
        this.vehicleNum = vehicleNum;
    }

    public void setProvince(String province) {
        this.province = province;
    }

    public String getTime() {
        return time;
    }

    public String getLocation() {
        return location;
    }

    public String getVehicleNum() {
        return vehicleNum;
    }

    public String getProvince() {
        return province;
    }
}
