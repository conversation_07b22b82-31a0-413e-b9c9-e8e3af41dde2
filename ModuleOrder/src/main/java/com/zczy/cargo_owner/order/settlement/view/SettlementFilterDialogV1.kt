package com.zczy.cargo_owner.order.settlement.view

import android.annotation.SuppressLint
import android.content.Context
import android.graphics.Rect
import android.graphics.drawable.ColorDrawable
import android.os.Build
import android.text.TextUtils
import android.view.LayoutInflater
import android.view.View
import android.view.ViewGroup
import android.widget.PopupWindow
import android.widget.TextView
import androidx.recyclerview.widget.LinearLayoutManager
import androidx.recyclerview.widget.RecyclerView
import com.chad.library.adapter.base.BaseQuickAdapter
import com.chad.library.adapter.base.BaseViewHolder
import com.zczy.cargo_owner.order.R
import com.zczy.cargo_owner.order.settlement.bean.SelectedTagBean
import com.zczy.comm.SpannableHepler
import com.zczy.comm.widget.dialog.ChooseDialogV1

/**
 * desc: 结算申请选择主体
 * user: 宋双朋
 * time: 2024/11/20 19:19
 */
@SuppressLint("NotifyDataSetChanged")
class SettlementFilterDialogV1(context: Context?, val data: List<String>, val itemOnClick: ItemOnClick) : PopupWindow(context) {
    private var chooseItem: String? = null

    init {
        val itemView = LayoutInflater.from(context).inflate(R.layout.order_select_settlement_platform_dialog, null, false)
        contentView = itemView
        itemView.setOnClickListener { dismiss() }
        val background = ColorDrawable()
        background.alpha = 100
        this.setBackgroundDrawable(background)
        this.isFocusable = true
        this.width = ViewGroup.LayoutParams.MATCH_PARENT
        this.height = ViewGroup.LayoutParams.MATCH_PARENT
        this.isOutsideTouchable = true

        val recyclerView = itemView.findViewById<RecyclerView>(R.id.rv_view)
        recyclerView.layoutManager = LinearLayoutManager(context)

        val adapter = ItemAdapter()
        adapter.setOnItemClickListener { _: BaseQuickAdapter<*, *>?, _: View?, position: Int ->
            this.chooseItem = adapter.data[position]
            itemOnClick.onSelectedText(this.chooseItem ?: "", position)
            adapter.notifyDataSetChanged()
            dismiss()
        }
        recyclerView.adapter = adapter
        adapter.setNewData(data)
    }

    fun setChoose(item: String): SettlementFilterDialogV1 {
        this.chooseItem = item
        return this
    }

    override fun dismiss() {
        super.dismiss()
        itemOnClick.dismiss()
    }

    fun show(anchor: View) {
        if (Build.VERSION.SDK_INT >= 24) {
            val visibleFrame = Rect()
            anchor.getGlobalVisibleRect(visibleFrame)
            val h = anchor.resources.displayMetrics.heightPixels - visibleFrame.bottom
            super.setHeight(h)
        }
        super.showAsDropDown(anchor)
    }

    private inner class ItemAdapter : BaseQuickAdapter<String, BaseViewHolder>(R.layout.order_settlement_sort_condition_item) {
        override fun convert(helper: BaseViewHolder, item: String) {
            val txtPlatform = helper.getView<TextView>(R.id.txtPlatform)
            if (TextUtils.equals(item, chooseItem)) {
                val sh = SpannableHepler().append(SpannableHepler.Txt(item, "#5086FC")).builder()
                txtPlatform.text = sh
                helper.setVisible(R.id.imgChecked, true)
            } else {
                val sh = SpannableHepler().append(SpannableHepler.Txt(item, "#333333")).builder()
                txtPlatform.text = sh
                helper.setVisible(R.id.imgChecked, false)
            }
        }
    }

    interface ItemOnClick {
        fun onSelectedText(selectedStr: String, position: Int)
        fun dismiss()
    }
}