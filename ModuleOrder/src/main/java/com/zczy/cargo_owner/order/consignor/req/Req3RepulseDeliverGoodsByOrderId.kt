package com.zczy.cargo_owner.order.consignor.req

import com.zczy.comm.http.entity.BaseNewRequest
import com.zczy.comm.http.entity.BaseRsp
import com.zczy.comm.http.entity.ResultData

/** 功能描述:
 * 3.手机端接口：单个发货单退回
 * http://wiki.zczy56.com/pages/viewpage.action?pageId=16089091
 * <AUTHOR>
 * @company 中储智运科技股份有限公司
 * @copyright （版权）中储南京智慧物流科技有限公司所有
 * @date 2019/4/9
 */
data class Req3RepulseDeliverGoodsByOrderId(
        var detailId: String = "", //	订单id	String	是	订单id
        var orderId: String = "",//	订单明细id	String	是	订单明细id
        var repulseReason: String = ""
) : BaseNewRequest<BaseRsp<ResultData>>
("oms-app/order/consignor/repulseDeliverGoodsByOrderId")