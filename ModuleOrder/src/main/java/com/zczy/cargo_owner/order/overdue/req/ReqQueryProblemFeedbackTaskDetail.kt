package com.zczy.cargo_owner.order.overdue.req

import com.zczy.comm.http.entity.BaseNewRequest
import com.zczy.comm.http.entity.BaseRsp
import com.zczy.comm.http.entity.ResultData

/**
 * PS: 逾期问题反馈 - 查询问题反馈任务单详情
 * Created by AI Assistant
 */
data class ReqQueryProblemFeedbackTaskDetail(
    /** 问题反馈任务单号 */
    var taskBillsCode: String = "",
    /** 任务单来源类型：3：CRM 4：货主 6：汽运后台 */
    var sourceType: Int = 4
) : BaseNewRequest<BaseRsp<RspQueryProblemFeedbackTaskDetail>>("oms-app/order/overDueApp/queryProblemFeedbackDetail")

data class RspQueryProblemFeedbackTaskDetail(
    /** 问题反馈任务单号 */
    var taskBillsCode: String = "",
    /** 运单号 */
    var orderId: String = "",
    /** 问题反馈类型 */
    var feedbackType: String = "",
    /** 问题反馈类型名称 */
    var feedbackTypeStr: String = "",
    /** 问题描述 */
    var description: String = "",
    /** 反馈人ID */
    var feedbackUserId: String = "",
    /** 反馈人姓名 */
    var feedbackUserRealName: String = "",
    /** 反馈时间(yyyy-MM-dd HH:mm:ss) */
    var feedbackTime: String = "",
    /** 反馈图片URL列表 */
    var feedbackImgUrlList: List<String>? = null,
    /** 处理状态：2-处理中，3-已处理 */
    var state: String = "",
    /** 处理状态名称 */
    var stateStr: String = "",
    /** 承运人姓名 */
    var carrierUserRealName: String = "",
    /** 承运人车牌号 */
    var carrierPlateNumber: String = "",
    var resultMsg: String = "",
    var resultCode: String = ""
) : ResultData()
