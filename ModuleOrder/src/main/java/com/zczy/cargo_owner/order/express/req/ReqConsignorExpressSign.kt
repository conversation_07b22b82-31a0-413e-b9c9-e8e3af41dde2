package com.zczy.cargo_owner.order.express.req

import com.zczy.comm.http.entity.BaseNewRequest
import com.zczy.comm.http.entity.BaseRsp
import com.zczy.comm.http.entity.ResultData

/**
 * PS: 货主快递签收
 * http://wiki.zczy56.com/pages/viewpage.action?pageId=12356197
 * Created by sdx on 2019/2/25.
 */
data class ReqConsignorExpressSign(
        var detailIds: String = ""//
) : BaseNewRequest<BaseRsp<ResultData>>("oms-app/order/express/consignorExpressSign")