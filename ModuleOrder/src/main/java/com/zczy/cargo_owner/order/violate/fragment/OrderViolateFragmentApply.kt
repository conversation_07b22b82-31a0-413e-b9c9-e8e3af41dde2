package com.zczy.cargo_owner.order.violate.fragment

import android.app.Activity
import android.content.Intent
import android.os.Bundle
import android.view.View
import com.chad.library.adapter.base.BaseQuickAdapter
import com.chad.library.adapter.base.listener.OnItemClickListener
import com.sfh.lib.event.RxBusEvent
import com.sfh.lib.mvvm.annotation.LiveDataMatch
import com.sfh.lib.ui.dialog.DialogBuilder
import com.zczy.cargo_owner.order.R
import com.zczy.cargo_owner.order.detail.WaybillDetailActivity
import com.zczy.cargo_owner.order.violate.OrderViolateDetailApplyingActivity
import com.zczy.cargo_owner.order.violate.adapter.OrderViolateAdapter1Apply
import com.zczy.cargo_owner.order.violate.event.EventSearchViolate
import com.zczy.cargo_owner.order.violate.model.OrderViolateDetail1Model
import com.zczy.cargo_owner.order.violate.model.OrderViolateMainModel1
import com.zczy.cargo_owner.order.violate.req.ReqQueryAllBreachApply
import com.zczy.cargo_owner.order.violate.req.RspBreachApplyItem
import com.zczy.comm.http.entity.PageList
import com.zczy.comm.ui.BaseFragment
import com.zczy.comm.utils.CommUtils
import com.zczy.comm.utils.dp2px
import com.zczy.comm.widget.pulltorefresh.CommEmptyView
import kotlinx.android.synthetic.main.order_common_fragment.swipe_refresh_more_layout

/**
 *  desc: 违约申请列表
 *  user: 宋双朋
 *  time: 2025/7/3 16:20
 */
class OrderViolateFragmentApply : BaseFragment<OrderViolateMainModel1?>() {
    var searchOrderId: String = ""
    private var finishFlag = "0"

    companion object {
        private const val REQUEST_DETAIL = 0x32
    }

    fun setFinishFlag(finishFlag: String) {
        this.finishFlag = finishFlag
    }

    override fun getLayout(): Int {
        return R.layout.order_common_fragment
    }

    override fun bindView(view: View, bundle: Bundle?) {

        val adapter = OrderViolateAdapter1Apply(true)
        val emptyView = CommEmptyView.creatorDef(context)
        swipe_refresh_more_layout.setAdapter(adapter, true)
        swipe_refresh_more_layout.setEmptyView(emptyView)
        swipe_refresh_more_layout.addItemDecorationSize(dp2px(7f))
        swipe_refresh_more_layout.addOnItemListener(onItemClickListener)
        swipe_refresh_more_layout.addOnItemChildClickListener { adapter1: BaseQuickAdapter<*, *>, view1: View, position: Int ->
            val item = adapter1.data[position] as RspBreachApplyItem
            when (view1.id) {
                R.id.tv_order_value -> {
                    WaybillDetailActivity.start(activity, item.orderId)
                }

                R.id.tv_cancel -> {
                    val builder = DialogBuilder()
                    builder.setMessage("确定取消申请吗？")
                    builder.setOkListener { dialog, _ ->
                        dialog.dismiss()
                        getViewModel(OrderViolateDetail1Model::class.java).retractBreachApply(
                            item.orderId,
                            item.breachApplyId,
                            ""
                        )
                    }
                    showDialog(builder)
                }

                R.id.tv_handle -> {
                    onItemClickListener.onSimpleItemClick(adapter1, view1, position)
                }

                R.id.tv_apply -> {
                    getViewModel(OrderViolateDetail1Model::class.java).applyPlatformIntervention(item.breachApplyId)
                }

                R.id.tv_copy -> {
                    //复制
                    val isCopy = CommUtils.copyText(context, "运单号：", item.orderId)
                    showToast(
                        if (isCopy) {
                            "复制成功"
                        } else {
                            "复制失败"
                        }
                    )
                }
            }
        }
        swipe_refresh_more_layout.setOnLoadListener2 { nowPage: Int ->
            val model = viewModel
            model?.queryAllBreachApply(
                req = ReqQueryAllBreachApply(
                    nowPage = nowPage,
                    orderId = searchOrderId,
                    finishFlag = finishFlag
                )
            )
        }
    }

    override fun initData() {
        swipe_refresh_more_layout.onAutoRefresh()
    }

    override fun onActivityResult(requestCode: Int, resultCode: Int, data: Intent?) {
        super.onActivityResult(requestCode, resultCode, data)
        if (resultCode == Activity.RESULT_OK) {
            when (requestCode) {
                REQUEST_DETAIL -> initData()
                else -> {}
            }
        }
    }

    private val onItemClickListener: OnItemClickListener = object : OnItemClickListener() {
        override fun onSimpleItemClick(adapter: BaseQuickAdapter<*, *>, view: View, position: Int) {
            val item = adapter.getItem(position)
            if (item is RspBreachApplyItem) {
                OrderViolateDetailApplyingActivity.start(
                    this@OrderViolateFragmentApply,
                    item,
                    REQUEST_DETAIL
                )
            }
        }
    }

    @LiveDataMatch
    open fun onQueryAllBreachApply(data: PageList<RspBreachApplyItem>?) {
        swipe_refresh_more_layout.onRefreshCompale(data)
    }

    @LiveDataMatch
    open fun onRetractBreachApply() {
        showToast("取消申请成功")
        swipe_refresh_more_layout.onAutoRefresh()
    }

    @RxBusEvent(from = "搜索 违约")
    open fun onEventSearchViolate(event: EventSearchViolate) {
        searchOrderId = event.searchOrderId
        swipe_refresh_more_layout.onAutoRefresh()
    }

    @LiveDataMatch(tag = "申请平台介入")
    open fun onApplyPlatformIntervention(msg: String?) {
        showDialogToast(msg)
        swipe_refresh_more_layout.onAutoRefresh()
    }
}
