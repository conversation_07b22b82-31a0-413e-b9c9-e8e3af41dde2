package com.zczy.cargo_owner.order.mileage.entity;

import com.zczy.comm.http.entity.ResultData;

/**
 * 功能描述:
 *
 * <AUTHOR>
 * @company 中储智运科技股份有限公司
 * @copyright （版权）中储南京智慧物流科技有限公司所有
 * @date 2017/8/28
 */

public class RGoodMaps extends ResultData {
    private String despatchCoordinateX;
    private String despatchCoordinateY;
    private String deliverCoordinateX;
    private String deliverCoordinateY;
    private String despatchProCityDisPlace;// 启运地省市区详细地址
    private String deliverProCityDisPlace;// 目的地省市区详细地址

    public String getDespatchProCityDisPlace() {
        return despatchProCityDisPlace;
    }

    public void setDespatchProCityDisPlace(String despatchProCityDisPlace) {
        this.despatchProCityDisPlace = despatchProCityDisPlace;
    }

    public String getDeliverProCityDisPlace() {
        return deliverProCityDisPlace;
    }

    public void setDeliverProCityDisPlace(String deliverProCityDisPlace) {
        this.deliverProCityDisPlace = deliverProCityDisPlace;
    }

    public String getDespatchCoordinateX() {
        return despatchCoordinateX;
    }

    public void setDespatchCoordinateX(String despatchCoordinateX) {
        this.despatchCoordinateX = despatchCoordinateX;
    }

    public String getDespatchCoordinateY() {
        return despatchCoordinateY;
    }

    public void setDespatchCoordinateY(String despatchCoordinateY) {
        this.despatchCoordinateY = despatchCoordinateY;
    }

    public String getDeliverCoordinateX() {
        return deliverCoordinateX;
    }

    public void setDeliverCoordinateX(String deliverCoordinateX) {
        this.deliverCoordinateX = deliverCoordinateX;
    }

    public String getDeliverCoordinateY() {
        return deliverCoordinateY;
    }

    public void setDeliverCoordinateY(String deliverCoordinateY) {
        this.deliverCoordinateY = deliverCoordinateY;
    }
}
