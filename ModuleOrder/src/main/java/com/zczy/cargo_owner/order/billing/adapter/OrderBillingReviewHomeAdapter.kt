package com.zczy.cargo_owner.order.billing.adapter

import android.annotation.SuppressLint
import android.widget.TextView
import com.chad.library.adapter.base.BaseQuickAdapter
import com.chad.library.adapter.base.BaseViewHolder
import com.zczy.cargo_owner.order.R
import com.zczy.cargo_owner.order.settlement.bean.*
import com.zczy.comm.utils.ex.toDoubleRoundDownString

/**
 * 功能描述: 结算申请审核
 * <AUTHOR>
 * @date 2022/6/23-13:54
 */

class OrderBillingReviewHomeAdapter(var checkBoxVisible: Boolean = true) :
    BaseQuickAdapter<SettlementApplicationItemBean, BaseViewHolder>(R.layout.order_billing_review_home_item) {

    override fun convert(helper: BaseViewHolder?, item: SettlementApplicationItemBean?) {

        helper?.let { it ->
            val tvPlaceOfStart = it.getView<TextView>(R.id.tvPlaceOfStart)
            val tvPlaceOfEnd = it.getView<TextView>(R.id.tvPlaceOfEnd)

            tvPlaceOfStart.text = item?.formatPlace(place = item.startPlace ?: "")
            tvPlaceOfEnd.text = item?.formatPlace(place = item.endPlace ?: "")

            /*集装箱货源展示判断*/
            item?.let { item1 ->
                if (item1.showGoodsSource()) {
                    it.setText(
                        R.id.tvRemainingBalance,
                        item1.consignorCheckMoney.toDoubleRoundDownString()
                    ).setText(
                        R.id.tvWaybillGoodsInfo,
                        item1.allCargoName + " | 发单运费：" + item1.orgBackMoney.toDoubleRoundDownString() + "元"
                    ).setText(R.id.tvExpiryDateStr, "回款到期日：" + item1.getReturnMoneyTime())
                        .setGone(R.id.tvExpiryDateStr, item1.showReturnMoneyTime())
                } else {
                    it.setText(
                        R.id.tvRemainingBalance,
                        item1.consignorCheckMoney.toDoubleRoundDownString()
                    ).setText(
                        R.id.tvWaybillGoodsInfo, item1.allCargoName + " | " + item1.slipLoad
                                + " | 发单运费：" + item1.orgBackMoney.toDoubleRoundDownString() + "元"
                    ).setText(R.id.tvExpiryDateStr, "回款到期日：" + item1.getReturnMoneyTime())
                        .setGone(R.id.tvExpiryDateStr, item1.showReturnMoneyTime())
                }
            }
            val styledText = String.format(
                mContext.resources.getString(R.string.order_carrier_info),
                item?.carrierCustomerName, item?.plateNumber
            )
            it.setText(R.id.tvWaybillDriverInfo, styledText)

            if (item?.hzAdvanceMoney.isNullOrEmpty()) {
                it.setText(R.id.tvPrepaymentAmount, "预支付金额：￥0.00")
            } else {
                it.setText(R.id.tvPrepaymentAmount, "预支付金额：￥${item?.hzAdvanceMoney}")
            }
            it.setGone(R.id.imgOilCard, item?.oilCardFlag == "1")
                .setGone(R.id.imgReturnedOrderPay, item?.receiptFlag == "1")
                .setGone(R.id.imgPrepaid, item?.advenceFlag == "1")
                .setGone(R.id.imgRisk, item?.riskReceiveValue == "1")
                .setGone(R.id.imgCallBack, item?.isBackUpFlag == "1")
            if (checkBoxVisible) {
                it.addOnClickListener(R.id.cbSettlementApplication)
                    .setChecked(R.id.cbSettlementApplication, item?.selected ?: false)
            }
            it.setGone(R.id.cbSettlementApplication, checkBoxVisible)

            it.setText(R.id.tvSettlementPlatform, "结算平台：" + item?.consignorSubsidiaryName)
        }
    }

    @SuppressLint("NotifyDataSetChanged")
    fun selectAll() {
        data.forEach {
            it.selected = true
        }
        notifyDataSetChanged()
    }

    @SuppressLint("NotifyDataSetChanged")
    fun unSelectAll() {
        data.forEach {
            it.selected = false
        }
        notifyDataSetChanged()
    }
}