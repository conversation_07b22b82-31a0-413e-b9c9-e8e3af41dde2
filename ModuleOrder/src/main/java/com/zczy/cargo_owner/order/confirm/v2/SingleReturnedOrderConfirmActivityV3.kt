package com.zczy.cargo_owner.order.confirm.v2

import android.content.Context
import android.content.Intent
import android.os.Bundle
import android.view.View
import com.sfh.lib.event.RxBusEventManager
import com.sfh.lib.mvvm.annotation.LiveDataMatch
import com.sfh.lib.ui.dialog.DialogBuilder
import com.zczy.cargo_owner.order.R
import com.zczy.cargo_owner.order.change.OrderChangeHandleActivity
import com.zczy.cargo_owner.order.confirm.ReturnedOrderConfirmModel
import com.zczy.cargo_owner.order.confirm.bean.ReqBeforeConsignorTrnOrderConfirmCheck
import com.zczy.cargo_owner.order.confirm.bean.ReqConsignorTrnOrderConfirmReceive
import com.zczy.cargo_owner.order.confirm.bean.ReqConsignorTrnOrderReject
import com.zczy.cargo_owner.order.confirm.bean.ReqQueryTrnSingleReceiptOrderData
import com.zczy.cargo_owner.order.confirm.bean.RspQueryTrnSingleReceiptOrderData
import com.zczy.cargo_owner.order.confirm.bean.RxAgreeAdjust
import com.zczy.comm.ui.BaseActivity
import com.zczy.comm.utils.ex.isTrue
import com.zczy.comm.utils.ex.setVisible
import kotlinx.android.synthetic.main.order_returned_single_confirm_activity_v3.btn1
import kotlinx.android.synthetic.main.order_returned_single_confirm_activity_v3.btn2
import kotlinx.android.synthetic.main.order_returned_single_confirm_activity_v3.returned_order_info_view1
import kotlinx.android.synthetic.main.order_returned_single_confirm_activity_v3.returned_order_info_view2
import kotlinx.android.synthetic.main.order_returned_single_confirm_activity_v3.returned_order_info_view3
import kotlinx.android.synthetic.main.order_returned_single_confirm_activity_v3.returned_order_info_view4
import kotlinx.android.synthetic.main.order_returned_single_confirm_activity_v3.viewLine1
import kotlinx.android.synthetic.main.order_returned_single_confirm_activity_v3.viewLine2
import kotlinx.android.synthetic.main.order_returned_single_confirm_activity_v3.viewLine3

/**
 *  desc: 极速好货-确认收货
 *  user: ssp
 *  time: 2025/3/25 10:35
 */
class SingleReturnedOrderConfirmActivityV3 : BaseActivity<ReturnedOrderConfirmModel>() {
    //运单id
    private val orderId by lazy { intent.getStringExtra(ORDER_ID) }

    companion object {

        private const val ORDER_ID = "orderId"

        @JvmStatic
        fun jumpPage(context: Context?, orderId: String) {
            val intent = Intent(context, SingleReturnedOrderConfirmActivityV3::class.java)
            intent.putExtra(ORDER_ID, orderId)
            context?.startActivity(intent)
        }
    }

    override fun getLayout(): Int {
        return R.layout.order_returned_single_confirm_activity_v3
    }

    override fun bindView(bundle: Bundle?) {
        bindClickEvent(btn1)
        bindClickEvent(btn2)
    }

    override fun onSingleClick(v: View) {
        super.onSingleClick(v)
        when (v.id) {
            R.id.btn1 -> {
                //打回
                val dialogBuilder = DialogBuilder()
                dialogBuilder.title = "提示"
                dialogBuilder.message = "确认进行打回操作吗？"
                dialogBuilder.setCancelTextListener("再想想") { dialog, _ ->
                    dialog.dismiss()
                }
                dialogBuilder.setOKTextListener("确定") { dialog, _ ->
                    dialog.dismiss()
                    getViewModel(ReturnedOrderConfirmModel::class.java).consignorTrnOrderReject(
                        req = ReqConsignorTrnOrderReject(orderId = orderId)
                    )
                }
                showDialog(dialogBuilder)
            }

            R.id.btn2 -> {
                //确认收货
                val dialogBuilder = DialogBuilder()
                dialogBuilder.title = "提示"
                dialogBuilder.message = "确认进行收货操作吗？"
                dialogBuilder.setCancelTextListener("再想想") { dialog, _ ->
                    dialog.dismiss()
                }
                dialogBuilder.setOKTextListener("确定") { dialog, _ ->
                    dialog.dismiss()
                    getViewModel(ReturnedOrderConfirmModel::class.java).beforeConsignorTrnOrderConfirmCheck(
                        req = ReqBeforeConsignorTrnOrderConfirmCheck(orderId = orderId)
                    )
                }
                showDialog(dialogBuilder)
            }
        }
    }

    override fun initData() {
        getViewModel(ReturnedOrderConfirmModel::class.java).queryTrnSingleReceiptOrderData(
            req = ReqQueryTrnSingleReceiptOrderData(orderId = orderId)
        )
    }

    @LiveDataMatch(tag = "运单详情数据")
    open fun queryTrnSingleReceiptOrderDataSuccess(data: RspQueryTrnSingleReceiptOrderData) {
        btn1.setVisible(data.modeType.isTrue)
        returned_order_info_view1.setOrderId(orderId = orderId)
        returned_order_info_view1.setData(data)
        viewLine3.setVisible(data.modeType.isTrue)
        viewLine2.setVisible(data.modeType.isTrue)
        viewLine1.setVisible(data.modeType.isTrue)
        returned_order_info_view2.setVisible(data.modeType.isTrue)
        returned_order_info_view3.setVisible(data.modeType.isTrue)
        returned_order_info_view4.setVisible(data.modeType.isTrue)
        returned_order_info_view2.setData(data)
        returned_order_info_view3.setData(data)
        returned_order_info_view4.setData(data)
    }

    @LiveDataMatch(tag = "收货")
    open fun beforeConsignorTrnOrderConfirmCheckSuccess() {
        getViewModel(ReturnedOrderConfirmModel::class.java).consignorTrnOrderConfirmReceive(
            req = ReqConsignorTrnOrderConfirmReceive(orderId = orderId)
        )
    }

    @LiveDataMatch(tag = "收货")
    open fun consignorTrnOrderConfirmReceiveSuccess() {
        RxBusEventManager.postEvent(RxAgreeAdjust(true))
        finish()
    }

    @LiveDataMatch(tag = "收货")
    open fun beforeConsignorTrnOrderConfirmCheckError(errorMsg: String?) {
        val dialogBuilder = DialogBuilder()
        dialogBuilder.title = "提示"
        dialogBuilder.message = errorMsg
        dialogBuilder.setCancelTextListener("取消") { dialog, _ ->
            dialog.dismiss()
        }
        dialogBuilder.setOKTextListener("去处理") { dialog, _ ->
            dialog.dismiss()
            OrderChangeHandleActivity.start(activity = this@SingleReturnedOrderConfirmActivityV3, sourceId = orderId ?: "")
        }
        showDialog(dialogBuilder)
    }

    @LiveDataMatch(tag = "打回")
    open fun consignorTrnOrderRejectSuccess() {
        RxBusEventManager.postEvent(RxAgreeAdjust(true))
        finish()
    }
}