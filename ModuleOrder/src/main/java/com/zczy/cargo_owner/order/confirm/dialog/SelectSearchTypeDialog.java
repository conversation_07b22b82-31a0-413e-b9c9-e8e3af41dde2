package com.zczy.cargo_owner.order.confirm.dialog;

import android.annotation.SuppressLint;
import android.content.Context;
import android.graphics.Color;
import android.graphics.Rect;
import android.graphics.drawable.ColorDrawable;
import android.os.Build;
import androidx.annotation.NonNull;
import androidx.annotation.Nullable;
import androidx.recyclerview.widget.LinearLayoutManager;
import androidx.recyclerview.widget.RecyclerView;

import android.view.LayoutInflater;
import android.view.View;
import android.view.ViewGroup;
import android.view.WindowManager;
import android.widget.PopupWindow;
import android.widget.TextView;

import com.zczy.cargo_owner.order.R;
import com.zczy.cargo_owner.order.confirm.bean.SearchItem;
import com.zczy.comm.widget.itemdecoration.CommItemDecoration;

import java.util.List;

/**
 * 功能描述:
 * 选择回单查询类型
 *
 * <AUTHOR> 孙飞虎
 * @company 中储智运科技股份有限公司
 * @copyright （版权）中储南京智慧物流科技有限公司所有
 * @date 2019/11/6
 */
public class SelectSearchTypeDialog extends PopupWindow {


    public static void show(Context context,List<SearchItem> item, ItemOnClick itemOnClick, View view) {
        new SelectSearchTypeDialog(context,item, itemOnClick).show(view);
    }

    public interface ItemOnClick {
        void onClick(SearchItem queryType);
    }

    private List<SearchItem> item;

    private ItemOnClick itemOnClick;
    private Context context;
    private SelectSearchTypeDialog(Context context, List<SearchItem> item, ItemOnClick itemOnClick) {
        super(context);
        this.itemOnClick = itemOnClick;
        this.item = item;
        this.context = context;
        this.setSoftInputMode(WindowManager.LayoutParams.SOFT_INPUT_ADJUST_PAN);
        this.setInputMethodMode(PopupWindow.INPUT_METHOD_NEEDED);

        View itemView = LayoutInflater.from(context).inflate(R.layout.return_select_search_type_dialog, null, false);
        setContentView(itemView);
        itemView.setOnClickListener(v -> dismiss());
        ColorDrawable background = new ColorDrawable();
        background.setAlpha(100);
        this.setBackgroundDrawable(background);
        this.setFocusable(true);
        this.setWidth(ViewGroup.LayoutParams.MATCH_PARENT);
        this.setHeight(ViewGroup.LayoutParams.MATCH_PARENT);
        this.setOutsideTouchable(true);

        RecyclerView recyclerView = itemView.findViewById(R.id.rv_view);
        recyclerView.setLayoutManager(new LinearLayoutManager(context));
        recyclerView.addItemDecoration(CommItemDecoration.createVertical(context, Color.parseColor("#e3e3e3"), 1));
        recyclerView.setAdapter(new ItemAdapter());

    }

    private void show(View anchor) {
        if (Build.VERSION.SDK_INT >= 24) {
            Rect visibleFrame = new Rect();
            anchor.getGlobalVisibleRect(visibleFrame);
            int h = anchor.getResources().getDisplayMetrics().heightPixels - visibleFrame.bottom;
            super.setHeight(h);
        }
        super.showAsDropDown(anchor);
    }

    private class ItemAdapter extends RecyclerView.Adapter<ItemView> {

        ItemAdapter(){
            super();
        }
        @NonNull
        @Override
        public ItemView onCreateViewHolder(@NonNull ViewGroup viewGroup, int position) {
            TextView textView = new TextView(context);
            textView.setLayoutParams(new ViewGroup.LayoutParams(ViewGroup.LayoutParams.MATCH_PARENT,ViewGroup.LayoutParams.WRAP_CONTENT));
            textView.setPadding(10,12,10,12);
            textView.setTextColor(Color.parseColor("#333333"));
            textView.setTextSize(16.0f);
            return new ItemView(textView);
        }

        @Override
        public void onBindViewHolder(@NonNull ItemView itemView, @SuppressLint("RecyclerView") int i) {
            itemView.textView.setText(item.get(i).getSearchName());
            itemView.textView.setOnClickListener(new View.OnClickListener() {
                @Override
                public void onClick(View v) {
                    itemOnClick.onClick(item.get(i));
                    dismiss();
                }
            });
        }

        @Override
        public int getItemCount() {
            return item.size();
        }
    }
   class ItemView extends RecyclerView.ViewHolder{
       public TextView textView;
       public ItemView(@NonNull TextView itemView) {
           super(itemView);
           this.textView = itemView;
       }
   }
}
