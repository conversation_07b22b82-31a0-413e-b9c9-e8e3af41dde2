package com.zczy.cargo_owner.order.entity;

import androidx.annotation.NonNull;

public class EBusinessEntity {

    //    货主关联子公司ID
    public String consignorSubsidiaryId;
    //  货主关联子公司名称(简称)
    public String upSubsidiaryShortName ;
    @NonNull
    @Override
    public String toString() {
        return upSubsidiaryShortName;
    }

    public String getConsignorSubsidiaryId() {
        return consignorSubsidiaryId;
    }

    public void setConsignorSubsidiaryId(String consignorSubsidiaryId) {
        this.consignorSubsidiaryId = consignorSubsidiaryId;
    }

    public String getUpSubsidiaryShortName() {
        return upSubsidiaryShortName;
    }

    public void setUpSubsidiaryShortName(String upSubsidiaryShortName) {
        this.upSubsidiaryShortName = upSubsidiaryShortName;
    }
}
