package com.zczy.cargo_owner.order.pledge.adapter

import com.chad.library.adapter.base.BaseMultiItemQuickAdapter
import com.chad.library.adapter.base.BaseViewHolder
import com.zczy.cargo_owner.order.R
import com.zczy.cargo_owner.order.pledge.req.*
import com.zczy.cargo_owner.order.pledge.ui.PledgeMainFragment
import com.zczy.comm.utils.ex.isTrue

/**
 *  user: ssp
 *  time: 2020/6/17 13:59
 *  desc: 索要押金
 */

class PledgeMainAdapter : BaseMultiItemQuickAdapter<RspPledgeMain, BaseViewHolder>(null) {

    init {
        addItemType(PledgeMainFragment.TYPE_0, R.layout.pledge_main_itme_type_0)
        addItemType(PledgeMainFragment.TYPE_1, R.layout.pledge_main_itme_type_1)
    }

    override fun convert(helper: BaseViewHolder?, item: RspPledgeMain) {

        helper?.let {
            when (it.itemViewType) {
                PledgeMainFragment.TYPE_0 -> {
                    //待追回
                    commView(it, item)
                }
                PledgeMainFragment.TYPE_1 -> {
                    //已追回
                    commView(it, item)
                }
            }
        }
    }

    private fun commView(helper: BaseViewHolder, item: RspPledgeMain) {
        //运单号
        helper.setText(R.id.tv_order_num, item.orderId)
            // 起始地
            .setText(R.id.tv_order_start, item.despatchCity)
            // 目的地
            .setText(R.id.tv_order_end, item.deliverCity)
            // 货物名称
            .setText(R.id.yv_order_good_name, item.allCargoName)
            // 承运方
            .setText(R.id.tv_order_owner_name, item.getCyfStr() + " | " + item.getVehicleStr())
            // 押金总额
            .setText(R.id.tv_car_plate_number, item.showReceiptMoneyV1())
            // 货主扣款
            .setText(R.id.tv_car_boss, item.showConsignorDeposit())
            .addOnClickListener(R.id.tv_copy)
            .setText(R.id.tvState, item.showApplyState())
            .setTextColor(R.id.tvState, item.showColorResId(mContext))
            .setGone(R.id.ivLTLOrderFlag, item.LTLOrderFlag.isTrue)
        // 0:待申请; 1:平台介入中; 2:已驳回; 3:待承运方同意; 4:待结算 5:已追回
        when (item.applyState) {
            "0" -> {
                helper.setGone(R.id.tvPledge, true)
                    .setGone(R.id.tvDetail, false)
            }
            "2" -> {
                helper.setGone(R.id.tvPledge, true)
                    .setGone(R.id.tvDetail, true)
            }
            "5" -> {
                helper.setGone(R.id.tvPledge, false)
                    .setGone(R.id.tvDetail, true)
            }
            else -> {
                helper.setGone(R.id.tvPledge, false)
                    .setGone(R.id.tvDetail, true)
            }
        }
        helper.addOnClickListener(R.id.tvDetail)
            .addOnClickListener(R.id.tvPledge)
    }
}
