package com.zczy.cargo_owner.order.confirm.bean

import com.zczy.comm.http.entity.BaseNewRequest
import com.zczy.comm.http.entity.BaseRsp

/**
 * @description
 * <AUTHOR> 韩正亚
 * @company 中储智运科技股份有限公司
 * @copyright （版权）中储南京智慧物流科技有限公司所有
 * @date 2019/4/28
 */

data class AgreeAdjustDetailReq(val detailId: String?, val reconsiderUserType: String)
    : BaseNewRequest<BaseRsp<AgreeAdjustDetailBean>>("oms-app/order/receipt/queryReconsiderInfoByDetailId")
