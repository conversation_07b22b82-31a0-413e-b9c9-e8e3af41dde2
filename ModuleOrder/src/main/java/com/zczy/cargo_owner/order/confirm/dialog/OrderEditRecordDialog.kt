package com.zczy.cargo_owner.order.confirm.dialog

import android.annotation.SuppressLint
import android.app.Dialog
import android.content.Context
import android.text.TextUtils
import android.view.View
import com.google.gson.Gson
import com.zczy.cargo_owner.order.R
import com.zczy.cargo_owner.order.confirm.bean.ConsignorDeduction
import com.zczy.cargo_owner.order.confirm.bean.ReceiptMoneySnapShotObj
import kotlinx.android.synthetic.main.dialog_order_edit_record.*


/**
 * 注释：回单修改记录弹窗
 * 时间：2024/12/10 0010 9:25
 * 作者：郭翰林
 */
class OrderEditRecordDialog(context: Context) : Dialog(context) {
    init {
        setContentView(R.layout.dialog_order_edit_record)
        ivColse.setOnClickListener { dismiss() }
        tv_ok.setOnClickListener { dismiss() }
    }

    /**
     * 注释：显示回单修改记录弹窗
     * 时间：2024/12/10 0010 9:56
     * 作者：郭翰林
     */
    @SuppressLint("SetTextI18n")
    fun show(receiptMoneySnapShotObj: ReceiptMoneySnapShotObj) {
        //结算金额
        consignorRateMoney.text = "${receiptMoneySnapShotObj.consignorRateMoney}元"
        consignorNoRateMoney.text = "${receiptMoneySnapShotObj.consignorNoRateMoney}元"
        //加扣款规则
        if (!TextUtils.isEmpty(receiptMoneySnapShotObj.consignorDeduction)) {
            ll_deductionDetail.visibility = View.VISIBLE
            val gson = Gson()
            val consignorDeductionList = gson.fromJson(
                receiptMoneySnapShotObj.consignorDeduction,
                Array<ConsignorDeduction>
                ::class.java
            )
            val deductionDetailList = mutableListOf<String>()
            consignorDeductionList.map {
                deductionDetailList.add("${getReasonStr(it.reason)},${getTypeStr(it.type)},${it.money}")
            }
            receiptMoneySnapShotObj.deductionDetail = deductionDetailList.joinToString("|")
            deductionDetail.text = receiptMoneySnapShotObj.deductionDetail
        } else {
            ll_deductionDetail.visibility = View.GONE
        }
        //扣款后结算金额(含税)
        if(!TextUtils.isEmpty(receiptMoneySnapShotObj.deductionRateMoney)){
            ll_deductionRateMoney.visibility = View.VISIBLE
            deductionRateMoney.text = "${receiptMoneySnapShotObj.deductionRateMoney}元"
        }else{
            ll_deductionRateMoney.visibility = View.GONE
        }
        //扣款后结算金额(承运方预估到手价)
        if(!TextUtils.isEmpty(receiptMoneySnapShotObj.deductionNoRateMoney)){
            ll_deductionNoRateMoney.visibility = View.VISIBLE
            deductionNoRateMoney.text = "${receiptMoneySnapShotObj.deductionNoRateMoney}元"
        }else{
            ll_deductionNoRateMoney.visibility = View.GONE
        }
        //亏涨吨
        consignorSmartDeficitTonUseflag.visibility =
            if (receiptMoneySnapShotObj.consignorSmartDeficitTonUseFlag == "1") View.VISIBLE else View.GONE
        overLossRuleName.text = receiptMoneySnapShotObj.overLossRuleName
        releaseLossConsignorRateMoney.text =
            "${receiptMoneySnapShotObj.releaseLossConsignorRateMoney}元"
        releaseLossConsignorNoRateMoney.text =
            "${receiptMoneySnapShotObj.releaseLossConsignorNoRateMoney}元"
        //抹零
        ignoreSmallChangeFlag.visibility =
            if (receiptMoneySnapShotObj.ignoreSmallChangeFlag == "1") View.VISIBLE else View.GONE
        ignoreSmallChangeType.text =
            getIgnoreSmallChangeType(receiptMoneySnapShotObj.ignoreSmallChangeType)
        ignoreSmallChangeMoney.text = "${receiptMoneySnapShotObj.ignoreSmallChangeMoney}元"
        super.show()
    }

    /**
     * 注释：获取抹零规则
     * 时间：2024/12/10 0010 11:40
     * 作者：郭翰林
     */
    private fun getIgnoreSmallChangeType(ignoreSmallChangeType: String?): String {
        return when (ignoreSmallChangeType) {
            "1" -> "含税价角分抹零"
            "2" -> "承运方预估到手价十元以下抺零"
            "3" -> "含税价十元以下抹零"
            "4" -> "承运方预估到手价角分抺零"
            else -> "含税价角分抹零"
        }
    }

    /**
     * 注释：获取原因字符串
     * 时间：2024/12/11 0011 10:01
     * 作者：郭翰林
     */
    private fun getReasonStr(reason: String?): String {
        return when (reason) {
            "1" -> "运费浮动"
            "2" -> "延时扣款"
            "3" -> "磅差费用"
            "4" -> "装卸货费"
            "5" -> "信息费"
            else -> ""
        }
    }

    /**
     * 注释：获取类型字符串
     * 时间：2024/12/11 0011 10:01
     * 作者：郭翰林
     */
    private fun getTypeStr(type: String?): String {
        return when (type) {
            "1" -> "加款"
            "2" -> "扣款"
            else -> ""
        }
    }
}