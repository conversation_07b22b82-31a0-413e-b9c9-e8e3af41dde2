package com.zczy.cargo_owner.order.confirm

import android.app.Activity
import android.content.Intent
import android.os.Bundle
import com.sfh.lib.event.RxBusEventManager
import com.sfh.lib.mvvm.annotation.LiveDataMatch
import com.sfh.lib.ui.dialog.DialogBuilder
import com.zczy.cargo_owner.libcomm.config.HttpConfig
import com.zczy.cargo_owner.libcomm.widget.CheckSelfPermissionDialog
import com.zczy.cargo_owner.order.R
import com.zczy.cargo_owner.order.confirm.bean.RxBusUploadImgSuccess
import com.zczy.cargo_owner.order.confirm.bean.UploadReturnedOrderPhotoReq
import com.zczy.cargo_owner.order.confirm.bean.UploadReturnedOrderPhotoReqV1
import com.zczy.comm.data.entity.EImage
import com.zczy.comm.data.entity.EProcessFile
import com.zczy.comm.permission.PermissionCallBack
import com.zczy.comm.permission.PermissionUtil
import com.zczy.comm.ui.BaseActivity
import com.zczy.comm.utils.imageselector.ImagePreviewActivity
import com.zczy.comm.utils.imageselector.ImageSelectProgressView
import com.zczy.comm.utils.imageselector.ImageSelector
import kotlinx.android.synthetic.main.order_activity_upload_returned_order_photo.btnUploadPhoto
import kotlinx.android.synthetic.main.order_activity_upload_returned_order_photo.ispUploadReturnedOrderView
import java.io.File

/**
 *  user: ssp
 *  time: 2021/6/10 19:46
 *  desc: 上传回单照片
 */

open class UploadReturnedOrderPhotoActivity : BaseActivity<ReturnedOrderConfirmModel>() {

    override fun getLayout() = R.layout.order_activity_upload_returned_order_photo


    override fun bindView(bundle: Bundle?) {
        ispUploadReturnedOrderView.setShowSize(5 - intent.getIntExtra(UPLOAD_PHOTO_SIZE, 0), 4)
        ispUploadReturnedOrderView.setOnItemSelectListener(object :
            ImageSelectProgressView.OnItemSelectListener {
            override fun onSelectImageClick(surplus: Int) {
                CheckSelfPermissionDialog.cameraPermissionDialog(this@UploadReturnedOrderPhotoActivity,
                    object : PermissionCallBack() {
                        override fun onHasPermission() {
                            PermissionUtil.openAlbum(this@UploadReturnedOrderPhotoActivity,
                                object : PermissionCallBack() {
                                    override fun onHasPermission() {
                                        ImageSelector.open(
                                            this@UploadReturnedOrderPhotoActivity,
                                            surplus,
                                            true,
                                            SELECT_IMAGE
                                        )
                                    }
                                })
                        }
                    })

            }

            override fun onUpImageClick(file: String) {
//                viewModel?.upFile(file)
            }

            override fun onLookImageClick(file: List<EProcessFile>, position: Int) {
                //查看大图
                val list = ArrayList<EImage>(file.size)
                for (processFile in file) {
                    val image = EImage()
                    image.netUrl = HttpConfig.getUrlImage(processFile.imagUrl)
                    list.add(image)
                }
                ImagePreviewActivity.start(this@UploadReturnedOrderPhotoActivity, list, position)
            }

            override fun onDelateClick(position: Int) {
                val dialogBuilder = DialogBuilder()
                dialogBuilder.message = "确定删除当前图片吗？"
                dialogBuilder.setOkListener { dialogInterface: DialogBuilder.DialogInterface, _: Int ->

                    dialogInterface.dismiss()
                    ispUploadReturnedOrderView.deleteImage(position)
                }
                showDialog(dialogBuilder)
            }
        })

        btnUploadPhoto.setOnClickListener {

            val dataList = ispUploadReturnedOrderView.dataList
            if (dataList.isEmpty()) {
                return@setOnClickListener
            }
            val sb = StringBuilder()
            dataList.map {
                sb.append(it.imagUrl).append(",")
            }
            val uploadReturnedOrderPhotoReq = UploadReturnedOrderPhotoReq()
            uploadReturnedOrderPhotoReq.detailId = intent.getStringExtra(DETAIL_ID) ?: ""
            uploadReturnedOrderPhotoReq.orderId = intent.getStringExtra(ORDER_ID) ?: ""
            val map = ispUploadReturnedOrderView.dataList.map { UploadReturnedOrderPhotoReqV1(it.imagUrl) }
            uploadReturnedOrderPhotoReq.picUrlsArray = map.toMutableList()
            viewModel?.uploadReturnedOrderPhoto(uploadReturnedOrderPhotoReq)
        }
    }

    override fun initData() {}

    override fun onActivityResult(requestCode: Int, resultCode: Int, data: Intent?) {
        super.onActivityResult(requestCode, resultCode, data)
        if (SELECT_IMAGE == requestCode && resultCode == Activity.RESULT_OK) {
            val file = ImageSelector.obtainPathResult(data)?.toMutableList()
            ispUploadReturnedOrderView.onUpLoadStart(file)
            file?.let {
                this.viewModel?.upFile(it)
            }
        }
    }

    @LiveDataMatch(tag = "上传文件成功")
    open fun onFileSuccess(tag: File, url: String) {
        ispUploadReturnedOrderView.onUpLoadFileSuccess(tag.absolutePath, url)
    }

    @LiveDataMatch(tag = "上传文件失败")
    open fun onFileFailure(tag: File, error: String) {
        ispUploadReturnedOrderView.onUpLoadFileError(tag.absolutePath)
    }

    @LiveDataMatch
    open fun uploadReturnedOrderPhotoSuccess(msg: String?) {
        RxBusEventManager.postEvent(RxBusUploadImgSuccess(true))
        finish()
    }

    companion object {
        const val DETAIL_ID = "detailId"
        const val ORDER_ID = "order_id"
        private const val SELECT_IMAGE = 0x1640
        private const val UPLOAD_PHOTO = 0x1638
        const val UPLOAD_PHOTO_SIZE = "upload_photo_size"
    }
}