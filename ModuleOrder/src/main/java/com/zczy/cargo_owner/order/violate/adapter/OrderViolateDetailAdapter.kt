package com.zczy.cargo_owner.order.violate.adapter

import com.chad.library.adapter.base.BaseQuickAdapter
import com.chad.library.adapter.base.BaseViewHolder
import com.zczy.cargo_owner.order.R
import com.zczy.cargo_owner.order.violate.req.BreachApplyLogDto

class OrderViolateDetailAdapter
    : BaseQuickAdapter<BreachApplyLogDto, BaseViewHolder>(R.layout.order_violate_detail_item) {

    override fun convert(helper: BaseViewHolder, item: BreachApplyLogDto) {
        helper
                // 名字
                .setText(R.id.tv_name, item.recordName)
                // 时间
                .setText(R.id.tv_time, item.recordTime)
                // 内容
                .setText(R.id.tv_content, item.recordContent)

    }
}
