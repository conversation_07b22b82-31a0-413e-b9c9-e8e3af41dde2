package com.zczy.cargo_owner.order.express

import android.content.Context
import android.content.Intent
import android.os.Bundle
import androidx.fragment.app.Fragment
import android.text.TextUtils
import android.view.View
import com.flyco.tablayout.listener.CustomTabEntity
import com.huawei.hms.hmsscankit.ScanUtil
import com.huawei.hms.ml.scan.HmsScan
import com.sfh.lib.event.RxBusEvent
import com.sfh.lib.mvvm.annotation.LiveDataMatch
import com.zczy.cargo_owner.order.R
import com.zczy.cargo_owner.order.express.bean.EExpressScan
import com.zczy.cargo_owner.order.express.model.OrderExpressMainModel
import com.zczy.cargo_owner.order.express.req.RespEnsureExpressSign
import com.zczy.cargo_owner.order.express.scan.HWScanActivity
import com.zczy.comm.scan.RxCodeResult
import com.zczy.comm.ui.BaseActivity
import com.zczy.comm.utils.json.toJson
import com.zczy.comm.widget.tablayout.CommonTabEntity
import kotlinx.android.synthetic.main.order_express_main_activity.*

/**
 * PS: 快递管理
 * Created by sdx on 2019/3/1.
 */
class OrderExpressMainActivity : BaseActivity<OrderExpressMainModel>() {

    private val orderIds by lazy { intent.getStringExtra(ORDER_IDS) }

    companion object {
        const val ORDER_IDS = "order_ids"

        @JvmStatic
        fun start(context: Context?, index: Int, orderIds: String) {
            if (context == null) return
            val intent = Intent(context, OrderExpressMainActivity::class.java)
            intent.putExtra(ORDER_IDS, orderIds)
            context.startActivity(intent)
        }
    }

    override fun getLayout(): Int = R.layout.order_express_main_activity

    override fun bindView(bundle: Bundle?) {

        val fragments = ArrayList<androidx.fragment.app.Fragment>()
        val tabEntities: ArrayList<CustomTabEntity> = arrayListOf()
        tabEntities.add(CommonTabEntity("未签收"))
        tabEntities.add(CommonTabEntity("已签收"))
        tabEntities.add(CommonTabEntity("已打回"))
        fragments.add(
            OrderExpressMainFragmentV1.instanceFragment(
                orderIds = orderIds?:""
            )
        )
        fragments.add(
            OrderExpressMainFragmentV2.instanceFragment(
                orderIds = orderIds?:""
            )
        )
        fragments.add(
            OrderExpressMainFragmentV3.instanceFragment(
                orderIds = orderIds?:""
            )
        )
        common_tab_layout.setTabData(tabEntities, this, R.id.container, fragments)
    }

    override fun initData() {

        appToolbar.setRightOnClickListener {
            val intent = Intent(this, HWScanActivity::class.java)
            this.startActivityForResult(intent, 10000)
        }
        appToolbar.tvRight.visibility = View.GONE
    }

    @RxBusEvent(from = "数据")
    open fun showScan(data: EExpressScan) {
        if (data?.ltConsignor != 1) {
            appToolbar.tvRight.visibility = View.GONE
        } else {
            appToolbar.tvRight.visibility = View.VISIBLE
        }
    }

    override fun onActivityResult(requestCode: Int, resultCode: Int, data: Intent?) {
        super.onActivityResult(requestCode, resultCode, data)
        if (resultCode != RESULT_OK || data == null) {
            finish()
            return
        }
        if (requestCode == 10000) {
            val hmsScanResult: HmsScan? = data.getParcelableExtra(ScanUtil.RESULT)
            if (hmsScanResult != null) {
                //展示解码结果
                val result = RxCodeResult()
                result.result = hmsScanResult.getOriginalValue()
                handleDecode(result)
            }
        }
    }

    private fun handleDecode(result: RxCodeResult?) {
        if (result == null || TextUtils.isEmpty(result.result)) {
            showDialogToast("扫描识别失败,重新扫描!")
            return
        }

        getViewModel(OrderExpressMainModel::class.java).ensureExpressSign(result.result)
    }

    @LiveDataMatch
    open fun onEnsureExpressSignSuccess(data: RespEnsureExpressSign) {
        HandInputBoundSuccessActivity.start(this, data.toJson())
    }
}