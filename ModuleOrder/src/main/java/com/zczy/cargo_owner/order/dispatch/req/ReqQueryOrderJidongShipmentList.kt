package com.zczy.cargo_owner.order.dispatch.req

import android.text.TextUtils
import com.zczy.comm.http.entity.BaseNewRequest
import com.zczy.comm.http.entity.BaseRsp
import com.zczy.comm.http.entity.PageList
import com.zczy.comm.http.entity.ResultData


/*=============================================================================================
 * 功能描述:
 *---------------------------------------------------------------------------------------------
 *  涉及业务 & 更新说明:
 *
 *
 *--------------------------------------------------------------------------------------------
 *  <AUTHOR>  on  2022/3/30
 *  @Company    中储智运科技股份有限公司
 *  @Copyright （版权）中储南京智慧物流科技有限公司所有                                                                                        *
 *=============================================================================================*/
class ReqQueryOrderJidongShipmentList(
        var consignorUserId: String = "",//货主用户ID
        var queryType: String? = "",//查询类型，1 需要生成派车单 2 已生成派车单
        var nowPage: Int = 1,
        var pageSize: Int = 10
) : BaseNewRequest<BaseRsp<PageList<RspQueryOrderJidongShipmentList>>>("/oms-app/order/orderJidongShipment/queryOrderJidongShipmentList")

data class RspQueryOrderJidongShipmentList(
        var orderId: String? = null,//订单id
        var selfComment: String? = null,//自定义编号
        var shipmentCode: String? = null,//派车单号
        var orderModel: String? = null,//订单类型：0 抢单,1 竞价
        var freightType: String? = null,//订单类型：0 抢单,1 竞价
        var displayMoney: String? = null,//货主价格
        var despatchCity: String? = null,//起运地市
        var despatchDis: String? = null,//起运地区
        var deliverCity: String? = null,//目的地市
        var deliverDis: String? = null,//目的地区
        var allCargoName: String? = null,//货物名称|吨位
        var upSubsidiaryShortName: String? = null,//业务主体简称
        var orderCurrentState: String? = null,//运单当前状态
        /**** 运单操作按钮 */
        var buttonDto: JidongShipmentButton? = null
        /***
         * 判断运单模式 true 议价 false 抢单
         * @return
         */


) : ResultData()


fun RspQueryOrderJidongShipmentList.biddingType(): Boolean {
    return TextUtils.equals("1", this.orderModel)
}

/***
 * 启运地址
 * @return
 */
fun RspQueryOrderJidongShipmentList.getAddressSatrt(): String? {
    val sb = StringBuilder()
    if ("市辖区" != despatchCity) {
        sb.append(despatchCity)
    }
    if (sb.length != 0) {
        sb.append(" ")
    }
    sb.append(despatchDis)
    return sb.toString()
}

/***
 * 目的地址
 * @return
 */
fun RspQueryOrderJidongShipmentList.getAddressEnd(): String? {
    val sb = java.lang.StringBuilder()
    if ("市辖区" != deliverCity) {
        sb.append(deliverCity)
    }
    if (sb.length != 0) {
        sb.append(" ")
    }
    sb.append(deliverDis)
    return sb.toString()
}

/***
 * 包车价-单价
 * @return
 */
fun RspQueryOrderJidongShipmentList.getPriceTypeContent(): String? {
    return (if (TextUtils.equals("1", freightType)) "单价" else "包车价")
}