package com.zczy.cargo_owner.order.violate.model

import com.sfh.lib.exception.HandleException
import com.sfh.lib.mvvm.service.BaseViewModel
import com.sfh.lib.rx.IResult
import com.sfh.lib.ui.dialog.DialogBuilder
import com.zczy.cargo_owner.order.violate.req.ReqAddBreach
import com.zczy.cargo_owner.order.violate.req.ReqCheckAddBreachApply
import com.zczy.cargo_owner.order.violate.req.ReqCheckOrderChangeInfo
import com.zczy.cargo_owner.order.violate.req.ReqQueryAllBreachApply
import com.zczy.cargo_owner.order.violate.req.ReqQueryBreachUserName
import com.zczy.cargo_owner.order.violate.req.RspAddBreach
import com.zczy.cargo_owner.order.violate.req.RspBreachApplyItem
import com.zczy.cargo_owner.order.violate.req.RspCheckOrderChangeInfo
import com.zczy.comm.CommServer
import com.zczy.comm.file.IFileServer.OnFileUploaderListener
import com.zczy.comm.http.entity.BaseRsp
import com.zczy.comm.http.entity.PageList
import com.zczy.comm.http.entity.ResultData
import java.io.File

/**
 * desc: 运单的违约列表
 * user: 宋双朋
 * time: 2025/1/17 14:58
 */
class OrderViolateListModel : BaseViewModel() {
    /***
     * 响应方法：onPageSuccess
     * @param nowPage
     * @param orderId
     */
    fun queryPage(nowPage: Int, orderId: String) {
        this.execute(ReqQueryAllBreachApply(nowPage, 10, orderId, ""), object : IResult<BaseRsp<PageList<RspBreachApplyItem>>> {
            override fun onFail(e: HandleException) {
                showToast(e.msg)
                setValue("onPageSuccess")
            }

            @Throws(Exception::class)
            override fun onSuccess(pageListBaseRsp: BaseRsp<PageList<RspBreachApplyItem>>) {
                if (pageListBaseRsp.success()) {
                    setValue("onPageSuccess", pageListBaseRsp.data)
                } else {
                    showToast(pageListBaseRsp.msg)
                    setValue("onPageSuccess")
                }
            }
        })
    }

    /***
     * 校验是否可以新增违约申请
     * 响应方法 onCheckAddSuccess
     * @param orderId
     */
    fun checkAddBreachApply(orderId: String) {
        this.execute<BaseRsp<ResultData>>(true, ReqCheckAddBreachApply(orderId)) { resultDataBaseRsp ->
            if (resultDataBaseRsp.success()) {
                setValue("onCheckAddSuccess")
            } else {
                showDialogToast(resultDataBaseRsp.msg)
            }
        }
    }


    /***
     * 根据orderId查询违约方名称
     * 响应方法：onBreachUserNameSuccess
     */
    fun queryBreachUserName(orderId: String) {
        execute(true, ReqQueryBreachUserName(orderId)) { eBreachUserNameBaseRsp ->
            if (eBreachUserNameBaseRsp.success()) {
                setValue("onBreachUserNameSuccess", eBreachUserNameBaseRsp.data)
            } else {
                showDialogToast(eBreachUserNameBaseRsp.msg)
            }
        }
    }

    /***
     * 上传文件
     * 响应方法：onaddViolateSuccess，onFileFailure
     * @param file
     */
    fun upFile(file: List<String>?) {
        file?.forEach { f ->
            this.upFile(f)
        }
    }

    fun upFile(file: String) {
        val disposable = CommServer.getFileServer().update(File(file), object : OnFileUploaderListener {
            override fun onSuccess(tag: File, url: String) {
                setValue("onFileSuccess", tag, url)
            }

            override fun onFailure(tag: File, error: String) {
                showToast(error)
                setValue("onFileFailure", tag, error)
            }
        })
        this.putDisposable(disposable)
    }

    /***
     * 新增违约申请
     * 响应方法：onaddViolateSuccess
     */
    fun addViolate(addBreach: ReqAddBreach) {
        this.execute<BaseRsp<RspAddBreach>>(false, addBreach) { resultDataBaseRsp: BaseRsp<RspAddBreach> ->
            if (resultDataBaseRsp.success()) {
                setValue("onAddViolateSuccess", resultDataBaseRsp.data)
            } else {
                showDialogToast(resultDataBaseRsp.msg)
            }
        }
    }

    fun checkOrderChangeInfo(req: ReqCheckOrderChangeInfo) {
        this.execute(false, req) { rsp: BaseRsp<RspCheckOrderChangeInfo> ->
            if (rsp.success()) {
                setValue("checkOrderChangeInfoSuccess", rsp.data)
            } else {
                val dialogBuilder = DialogBuilder()
                dialogBuilder.setMessage(rsp.msg)
                dialogBuilder.setHideCancel(true)
                dialogBuilder.setOKTextListener("确定") { dialog: DialogBuilder.DialogInterface, which: Int -> dialog.dismiss() }
                showDialog(dialogBuilder)
            }
        }
    }
}
