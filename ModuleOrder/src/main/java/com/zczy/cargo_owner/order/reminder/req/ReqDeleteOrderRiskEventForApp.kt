package com.zczy.cargo_owner.order.reminder.req

import com.zczy.comm.http.entity.BaseNewRequest
import com.zczy.comm.http.entity.BaseRsp
import com.zczy.comm.http.entity.ResultData

/**
 *  desc: 货主APP 单个删除提醒  1运输风险 2合规风险
 *  user: 宋双朋
 *  time: 2024/11/6 17:13
 */

class ReqDeleteOrderRiskEventForApp(
    var orderId: String? = null,//运单号
    var tabType: String? = null,//列表tab页 1运输风险 2合规风险
) : BaseNewRequest<BaseRsp<ResultData>>("/oms-app/risk/event/deleteOrderRiskEventForApp")
