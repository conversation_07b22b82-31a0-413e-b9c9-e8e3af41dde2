package com.zczy.cargo_owner.order.settlement

import android.annotation.SuppressLint
import android.app.Activity
import android.content.Intent
import android.os.Bundle
import android.text.TextUtils
import android.view.Gravity
import android.view.View
import androidx.fragment.app.Fragment
import androidx.recyclerview.widget.LinearLayoutManager
import com.sfh.lib.event.RxBusEventManager
import com.sfh.lib.mvvm.annotation.LiveDataMatch
import com.sfh.lib.ui.dialog.DialogBuilder
import com.zczy.cargo_owner.order.R
import com.zczy.cargo_owner.order.settlement.SettlementApplicationDetailActivity.Companion.CONSIGNOR_SUB_SELECT
import com.zczy.cargo_owner.order.settlement.adapter.ConfirmSettlementSingleAdapter
import com.zczy.cargo_owner.order.settlement.bean.*
import com.zczy.comm.CommServer
import com.zczy.comm.ui.BaseActivity
import com.zczy.comm.utils.NumUtil
import com.zczy.comm.utils.dp2px
import com.zczy.comm.utils.ex.isTrue
import com.zczy.comm.utils.json.toJsonObject
import com.zczy.comm.widget.itemdecoration.SpaceItemDecoration
import kotlinx.android.synthetic.main.dialog_view_settlement.view.tv_money
import kotlinx.android.synthetic.main.order_settlement_confirm_single_activity.*

/**
 *@Desc 结算申请 单个
 *@User ssp
 *@Date 2023/8/3-15:11
 */
class ConfirmSettlementSingleActivity : BaseActivity<SettlementApplicationModel>() {

    private val mConfirmSettlementSingleAdapter = ConfirmSettlementSingleAdapter()

    // 是否开启结算申请审核 0-否 1-是
    private val settlementApplyAuditSwitch by lazy {
        intent.getStringExtra(
            SETTLEMENT_APPLY_AUDIT_SWITCH
        )
    }

    //业务主体
    private val consignorSubSelect by lazy { intent.getStringExtra(CONSIGNOR_SUB_SELECT) ?: "" }

    //运单id
    private val orderInfo: String? by lazy { intent.getStringExtra(ORDER_INFO) }
    private val periodFlag: String? by lazy { intent.getStringExtra(PERIOD_FLAG) }
    private val modeType: String? by lazy { intent.getStringExtra(MODE_TYPE) }
    private var settleItem: SettlementApplicationItemBean? = null

    companion object {

        const val SETTLEMENT_APPLY_AUDIT_SWITCH = "settlement_apply_audit_switch"
        const val SETTLEMENT_APPLY_AUDIT_SWITCH_CODE = "1000"
        private const val ORDER_INFO = "orderInfo"
        private const val PERIOD_FLAG = "periodFlag"
        private const val SETTLE_ITEM = "settleItem"
        private const val MODE_TYPE = "modeType"

        @JvmStatic
        fun jumpPage(
            fragment: Fragment?,
            settlementApplyAuditSwitch: String,
            settleItem: String,
            consignorSubSelect: String,
            orderInfo: String? = null,
            periodFlag: String? = null,
            modeType: String? = null,
            requestCode: Int,
        ) {
            val intent = Intent(fragment?.context, ConfirmSettlementSingleActivity::class.java)
            intent.putExtra(SETTLEMENT_APPLY_AUDIT_SWITCH, settlementApplyAuditSwitch)
            intent.putExtra(CONSIGNOR_SUB_SELECT, consignorSubSelect)
            intent.putExtra(ORDER_INFO, orderInfo)
            intent.putExtra(PERIOD_FLAG, periodFlag)
            intent.putExtra(SETTLE_ITEM, settleItem)
            intent.putExtra(MODE_TYPE, modeType)
            fragment?.startActivityForResult(intent, requestCode)
        }

        @JvmStatic
        fun jumpPage(
            activity: Activity?,
            settlementApplyAuditSwitch: String,
            settleItem: String,
            consignorSubSelect: String,
            orderInfo: String? = null,
            periodFlag: String? = null,
            requestCode: Int,
        ) {
            val intent = Intent(activity, ConfirmSettlementSingleActivity::class.java)
            intent.putExtra(SETTLEMENT_APPLY_AUDIT_SWITCH, settlementApplyAuditSwitch)
            intent.putExtra(SETTLE_ITEM, settleItem)
            intent.putExtra(CONSIGNOR_SUB_SELECT, consignorSubSelect)
            intent.putExtra(ORDER_INFO, orderInfo)
            intent.putExtra(PERIOD_FLAG, periodFlag)
            activity?.startActivityForResult(intent, requestCode)
        }
    }

    override fun getLayout(): Int {
        return R.layout.order_settlement_confirm_single_activity
    }

    @SuppressLint("SetTextI18n")
    override fun bindView(bundle: Bundle?) {
        settleItem = intent.getStringExtra(SETTLE_ITEM)
            ?.toJsonObject(SettlementApplicationItemBean::class.java)
        if (TextUtils.equals(SETTLEMENT_APPLY_AUDIT_SWITCH_CODE, settlementApplyAuditSwitch)) {
            appToolber.setTitle("结算申请审核")
        } else {
            appToolber.setTitle("结算申请")
        }
        settlementRecycler.apply {
            layoutManager = LinearLayoutManager(this@ConfirmSettlementSingleActivity)
            adapter = mConfirmSettlementSingleAdapter
            addItemDecoration(SpaceItemDecoration(dp2px(7f)))
        }
        btnConfirmSettlementApplication.setOnClickListener {
            if (TextUtils.equals(SETTLEMENT_APPLY_AUDIT_SWITCH_CODE, settlementApplyAuditSwitch)) {
                //结算审核通过
                val dialogBuilder = DialogBuilder()
                dialogBuilder.message = "确定将选择的记录审核通过吗？"
                dialogBuilder.okListener =
                    DialogBuilder.DialogInterface.OnClickListener { p0, _ ->
                        p0?.dismiss()
                        val userId: String = CommServer.getUserServer().login.userId
                        viewModel?.findSettleAccountMoneyIsEnoughReq(
                            FindSettleAccountMoneyIsEnoughReq(
                                detailIds = settleItem?.detailId,
                                userId = userId,
                                modeType = modeType
                            )
                        )
                    }
                showDialog(dialogBuilder)
            } else {
                val view = View.inflate(this, R.layout.dialog_view_settlement, null)
                var totalConsignorCheckMoney = 0.00
                mConfirmSettlementSingleAdapter.data.forEach { bean ->
                    bean.consignorCheckMoney.let {
                        totalConsignorCheckMoney =
                            NumUtil.sum(totalConsignorCheckMoney, it?.toDoubleOrNull() ?: 0.00)
                    }
                    bean.hzAdvanceMoney.let {
                        totalConsignorCheckMoney =
                            NumUtil.sum(totalConsignorCheckMoney, it?.toDoubleOrNull() ?: 0.00)
                    }
                    bean.assistanceServiceCharges.let {
                        totalConsignorCheckMoney =
                            NumUtil.sum(totalConsignorCheckMoney, it?.toDoubleOrNull() ?: 0.00)
                    }
                    bean.additionalCharges.let {
                        totalConsignorCheckMoney =
                            NumUtil.sum(totalConsignorCheckMoney, it?.toDoubleOrNull() ?: 0.00)
                    }
                }
                view.tv_money.text = String.format("%1\$.2f", totalConsignorCheckMoney) + "元"
                showDialog(DialogBuilder()
                    .setTitle("提示")
                    .setView(view)
                    .setGravity(Gravity.CENTER)
                    .setOKTextColor("确定", R.color.text_blue)
                    .setOkListener { dialogInterface, _ ->
                        dialogInterface.dismiss()
                        if (TextUtils.equals("1", modeType)) {
                            val userId: String = CommServer.getUserServer().login.userId
                            viewModel?.findSettleAccountMoneyIsEnoughReq(
                                ReqFindInvoicingSettleAccountMoneyIsEnough(
                                    detailIds = settleItem?.detailId,
                                    userId = userId,
                                    modeType = modeType
                                )
                            )
                        } else {
                            if (settlementApplyAuditSwitch.isTrue) {
                                val reqSummitToAudit = ReqSummitToAudit()
                                val list = mutableListOf<String>()
                                mConfirmSettlementSingleAdapter.data.forEach { bean ->
                                    list.add(bean.orderId ?: "")
                                }
                                reqSummitToAudit.orderIds = list.joinToString(",")
                                reqSummitToAudit.modeType = modeType
                                viewModel?.summitToAudit(reqSummitToAudit)
                            } else {
                                val userId: String = CommServer.getUserServer().login.userId
                                viewModel?.findSettleAccountMoneyIsEnoughReq(
                                    FindSettleAccountMoneyIsEnoughReq(
                                        detailIds = settleItem?.detailId,
                                        userId = userId,
                                        modeType = modeType
                                    )
                                )
                            }
                        }
                    })
            }
        }
    }


    override fun initData() {
        val eLogin = CommServer.getUserServer().login
        val userId: String = eLogin.userId
        val userName: String = eLogin.userName
        val childId: String = eLogin.childId
        viewModel?.querySettlementApplicationByTypeDetail(
            QuerySettlementApplicationByTypeReqDate(
                userId = userId,
                userName = userName,
                orderId = settleItem?.orderId,
                childUserId = childId,
                nowPage = 1,
                applyType = -1,
                orderInfo = orderInfo,
                periodFlag = periodFlag,
                modeType = modeType,
                consignorSubSelect = consignorSubSelect
            )
        )
    }

    @SuppressLint("SetTextI18n")
    @LiveDataMatch
    open fun querySettlementApplicationByType(data: SettlementApplicationItemBean) {
        mConfirmSettlementSingleAdapter.setNewData(listOf(data))
    }

    @LiveDataMatch
    open fun confirmSettlementApplicationSuccess() {
        ConfirmSettlementSuccessActivity.start(this)
        RxBusEventManager.postEvent(RxBusSettleSuccess(success = true))
        setResult(Activity.RESULT_OK)
        finish()
    }

    @LiveDataMatch
    open fun onSummitToAuditSuccess() {
        ConfirmSettlementSuccessActivity.start(this)
        setResult(Activity.RESULT_OK)
        finish()
    }

    @LiveDataMatch
    open fun onMoneyUnEnough(data: FindSettleAccountMoneyIsEnoughRsp) {
        //余额不足
        if (data.shortCycleFlag.isTrue) {
            val dialogBuilder = DialogBuilder()
            dialogBuilder.message = data.rechargeMongyMsg
            dialogBuilder.title = "短周期额度不足"
            dialogBuilder.isHideCancel = true
            dialogBuilder.setOKText("我知道了")
            showDialog(dialogBuilder)
        } else {
            val dialogBuilder = DialogBuilder()
            dialogBuilder.message = data.rechargeMongyMsg
            dialogBuilder.title = "提示"
            dialogBuilder.isHideCancel = true
            dialogBuilder.setOKText("我知道了")
            showDialog(dialogBuilder)
        }
    }

    @LiveDataMatch
    open fun onMoneyEnough(data: FindSettleAccountMoneyIsEnoughRsp) {
        if (!TextUtils.isEmpty(data.showMsgCount)) {
            val showMsgCountInt = data.showMsgCount?.toIntOrNull() ?: 0
            if (showMsgCountInt > 0) {
                val dialogBuilder = DialogBuilder()
                dialogBuilder.message = data.showMsg
                dialogBuilder.title = "提示"
                dialogBuilder.isHideCancel = true
                dialogBuilder.setOKTextColor("我知道了", R.color.text_blue)
                dialogBuilder.setOkListener { dialogInterface, _ ->
                    dialogInterface.dismiss()
                    settlementContinue()
                }
                showDialog(dialogBuilder)
            }else{
                settlementContinue()
            }
        }else{
            settlementContinue()
        }
    }

    private fun settlementContinue() {
        //余额充足
        val userId: String = CommServer.getUserServer().login.userId
        val userName: String = CommServer.getUserServer().login.userName
        val childId: String = CommServer.getUserServer().login.childId
        if (TextUtils.equals("1", modeType)) {
            viewModel?.confirmSettlementApplication(
                ReqDoInvoicingBatchFrontSettleApply(
                    detailIds = settleItem?.detailId,
                    userId = userId,
                    userName = userName,
                    childUserId = childId,
                    consignorSubSelect = consignorSubSelect,
                    detailId2Coupon = mutableListOf(
                        DetailId2CouponIdReq(
                            detailId = settleItem?.detailId ?: "",
                            quanId = settleItem?.userCouponId ?: ""
                        )
                    ),
                    modeType = modeType
                )
            )
        } else {
            viewModel?.confirmSettlementApplication(
                ConfirmSettlementApplicationReq(
                    detailIds = settleItem?.detailId,
                    userId = userId,
                    userName = userName,
                    childUserId = childId,
                    consignorSubSelect = consignorSubSelect,
                    detailId2Coupon = mutableListOf(
                        DetailId2CouponIdReq(
                            detailId = settleItem?.detailId ?: "",
                            quanId = settleItem?.userCouponId ?: ""
                        )
                    ),
                    modeType = modeType
                )
            )
        }
    }

}