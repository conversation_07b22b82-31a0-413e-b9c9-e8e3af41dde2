package com.zczy.cargo_owner.order.confirm.bean

import com.zczy.comm.http.entity.BaseNewRequest
import com.zczy.comm.http.entity.BaseRsp
import com.zczy.comm.http.entity.ResultData

/**
 *  desc: 查询批量回单异步处理的结果
 *  user: 宋双朋
 *  time: 2025/5/15 19:22
 *  wiki: http://wiki.zczy56.com/pages/viewpage.action?pageId=83001699
 *  give: 李庆雄
 */
class ReqQueryBatchReceiptSubmitProcess : BaseNewRequest<BaseRsp<RspQueryBatchReceiptSubmitProcessV1>>("oms-app/order/receipt/queryBatchReceiptSubmitProcess")

class RspQueryBatchReceiptSubmitProcessV1(
    val data: RspQueryBatchReceiptSubmitProcess? = null
) : ResultData()

class RspQueryBatchReceiptSubmitProcess(
    val failCount: String? = null,  // 失败个数
    val finishFlag: String? = null, // 0 处理中； 1 处理完成;2 无处理记录
    val successCount: String? = null, // 成功个数
    val showMsg: String? = null,  // 提示文字
    val totalCount: String? = null  // 总提交条数
)