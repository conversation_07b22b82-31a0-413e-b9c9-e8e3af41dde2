package com.zczy.cargo_owner.order.pledge.adapter

import com.chad.library.adapter.base.BaseMultiItemQuickAdapter
import com.chad.library.adapter.base.BaseViewHolder
import com.zczy.cargo_owner.order.R
import com.zczy.cargo_owner.order.pledge.req.RspPledgeMain
import com.zczy.cargo_owner.order.pledge.req.getCyfStr
import com.zczy.cargo_owner.order.pledge.req.getVehicleStr
import com.zczy.cargo_owner.order.pledge.ui.PledgeMainFragment
import com.zczy.comm.utils.ex.isTrue

/**
 *  user: ssp
 *  time: 2020/6/17 13:59
 *  desc: 索要押金
 */

class PledgeMainSearchAdapter : BaseMultiItemQuickAdapter<RspPledgeMain, BaseViewHolder>(null) {

    init {
        addItemType(PledgeMainFragment.TYPE_0, R.layout.pledge_main_search_itme_type_0)
        addItemType(PledgeMainFragment.TYPE_1, R.layout.pledge_main_search_itme_type_1)
    }

    override fun convert(helper: BaseViewHolder?, item: RspPledgeMain) {

        helper?.let {
            commView(it, item)

            when (it.itemViewType) {

                PledgeMainFragment.TYPE_0 -> {//未申请
                    pledgeType0(it, item)
                }
                PledgeMainFragment.TYPE_1 -> {//已申请

                }
            }


        }

    }

    private fun commView(helper: BaseViewHolder, item: RspPledgeMain) {
        //运单号
        helper.setText(R.id.tv_order_num, item.orderId)
                // 起始地
                .setText(R.id.tv_order_start, item.despatchCity)
                // 目的地
                .setText(R.id.tv_order_end, item.deliverCity)
                // 货物名称
                .setText(R.id.yv_order_good_name, item.allCargoName)
                // 承运方
                .setText(R.id.tv_order_owner_name, item.getCyfStr() + " | " + item.getVehicleStr())
        helper.addOnClickListener(R.id.tv_copy)
    }

    private fun pledgeType0(helper: BaseViewHolder, item: RspPledgeMain) {
        if (!item.applyState.isTrue) {
            helper.addOnClickListener(R.id.tv_call_phone)
                    .addOnClickListener(R.id.btn_pledge)
        }
    }

}
