package com.zczy.cargo_owner.order.dispatch

import android.content.Context
import android.content.Intent
import android.os.Bundle
import androidx.fragment.app.Fragment
import com.flyco.tablayout.listener.CustomTabEntity
import com.sfh.lib.mvvm.service.BaseViewModel
import com.zczy.cargo_owner.order.dispatch.fragment.DispatchListManagerFragment
import com.zczy.cargo_owner.order.R
import com.zczy.comm.ui.BaseActivity
import com.zczy.comm.widget.tablayout.CommonTabEntity
import kotlinx.android.synthetic.main.dispatch_manager_list_activity.*


/*=============================================================================================
 * 功能描述:派车单管理
 *---------------------------------------------------------------------------------------------
 *  涉及业务 & 更新说明:
 *
 *
 *--------------------------------------------------------------------------------------------
 *  <AUTHOR>  on  2022/3/30
 *  @Company    中储智运科技股份有限公司
 *  @Copyright （版权）中储南京智慧物流科技有限公司所有                                                                                        *
 *=============================================================================================*/
class DispatchListManagerActivity : BaseActivity<BaseViewModel>() {
    private val fragments = ArrayList<androidx.fragment.app.Fragment>()
    private val tabEntities: ArrayList<CustomTabEntity> = arrayListOf()

    companion object {

        @JvmStatic
        fun jumpUi(context: Context) {
            val intent = Intent(context, DispatchListManagerActivity::class.java)
            context.startActivity(intent)
        }
    }

    override fun getLayout(): Int {
        return R.layout.dispatch_manager_list_activity
    }

    override fun bindView(bundle: Bundle?) {
        tabEntities.add(CommonTabEntity("需要生成派车单"))
        tabEntities.add(CommonTabEntity("已生成派车单"))
        fragments.add(DispatchListManagerFragment.instanceFragment(DispatchListManagerFragment.CONTAINER_TYPE_V1))
        fragments.add(DispatchListManagerFragment.instanceFragment(DispatchListManagerFragment.CONTAINER_TYPE_V2))
        common_tab_layout.setTabData(tabEntities, this, R.id.container, fragments)
    }

    override fun initData() {

    }
}