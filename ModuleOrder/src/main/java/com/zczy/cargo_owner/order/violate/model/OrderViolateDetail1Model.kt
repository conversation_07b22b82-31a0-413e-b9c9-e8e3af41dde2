package com.zczy.cargo_owner.order.violate.model

import com.sfh.lib.mvvm.service.BaseViewModel
import com.zczy.cargo_owner.order.violate.req.Req11RetractBreachApply
import com.zczy.cargo_owner.order.violate.req.Req5DecideBreach
import com.zczy.cargo_owner.order.violate.req.Req9QueryBreachApplyDetail
import com.zczy.cargo_owner.order.violate.req.ReqApplyPlatformIntervention
import com.zczy.cargo_owner.order.violate.req.RspQueryBreachApplyDetail

/**
 * PS:
 * Created by sdx on 2019/2/28.
 */
class OrderViolateDetail1Model : BaseViewModel() {
    fun breachContractDetail(breachApplyId: String) {
        execute(
            true,
            Req9QueryBreachApplyDetail(breachApplyId = breachApplyId)
        ) { t ->
            if (t.success()) {
                setValue("onBreachContractDetail", t.data)
            } else {
                showDialogToast(t.msg)
            }
        }
    }

    // 5. 同意或者驳回申请（指定单，违约方）
    fun decideBreach(data: RspQueryBreachApplyDetail?, decide: String) {
        execute(
            true,
            Req5DecideBreach(
                breachApplyId = data?.breachApplyId, // 违约单号
                breachMoney = data?.breachMoney, // 违约金额
                orderId = data?.orderId, // 订单Id
                decide = decide, // 处理类型，1同意， 2驳回
                isStop = data?.isStop  // 是否违约终止,1是，0否
            )
        ) { t ->
            if (t.success()) {
                setValue("onDecideBreach")
            } else {
                showDialogToast(t.msg)
            }
        }
    }

    fun decideBreachTms(data: RspQueryBreachApplyDetail?, decide: String) {
        execute(
            true,
            Req5DecideBreach(
                breachApplyId = data?.breachApplyId, // 违约单号
                breachMoney = data?.breachMoney, // 违约金额
                orderId = data?.orderId, // 订单Id
                decide = decide, // 处理类型，1同意， 2驳回
                isStop = data?.isStop,  // 是否违约终止,1是，0否
                modeType = data?.modeType,
            )
        ) { t ->
            if (t.success()) {
                setValue("onDecideBreach")
            } else {
                showDialogToast(t.msg)
            }
        }
    }

    // 11. 撤回违约申请。（违约未处理前，发起人可以撤回）
    fun retractBreachApply(orderId: String, breachApplyId: String, modeType: String?) {
        execute(
            true,
            Req11RetractBreachApply(orderId = orderId, breachApplyId = breachApplyId, modeType = modeType)
        ) { t ->
            if (t.success()) {
                setValue("onRetractBreachApply")
            } else {
                showDialogToast(t.msg)
            }
        }
    }

    /**
     * 申请平台介入
     */
    fun applyPlatformIntervention(breachApplyId: String) {
        execute(
            true,
            ReqApplyPlatformIntervention(breachApplyId = breachApplyId)
        ) { t ->
            if (t.success()) {
                setValue("onApplyPlatformIntervention", t.msg)
            } else {
                setValue("onApplyPlatformIntervention", t.msg)
            }
        }
    }
}