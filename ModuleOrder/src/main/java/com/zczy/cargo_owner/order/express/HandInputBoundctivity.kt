package com.zczy.cargo_owner.order.express

import android.content.Context
import android.content.Intent
import android.os.Bundle
import android.text.TextUtils
import com.sfh.lib.mvvm.annotation.LiveDataMatch
import com.zczy.cargo_owner.order.R
import com.zczy.cargo_owner.order.express.model.OrderExpressMainModel
import com.zczy.cargo_owner.order.express.req.RespEnsureExpressSign
import com.zczy.cargo_owner.order.express.view.HandInputView
import com.zczy.comm.ui.BaseActivity
import com.zczy.comm.utils.json.toJson
import kotlinx.android.synthetic.main.activity_hand_input.*

class HandInputBoundctivity : BaseActivity<OrderExpressMainModel>() {

    companion object {
        @JvmStatic
        fun start(context: Context?) {
            if (context == null) return
            val intent = Intent(context, HandInputBoundctivity::class.java)
            context.startActivity(intent)
        }
    }

    override fun getLayout(): Int = R.layout.activity_hand_input

    override fun bindView(bundle: Bundle?) {
        val handInputView = HandInputView(this)
        ll_input_list.removeAllViews()
        ll_input_list.addView(handInputView)

        tv_add.setOnClickListener {
            val handInputView = HandInputView(this)
            ll_input_list.addView(handInputView)

            initClick()
        }

        initClick()

        tv_ensure_sign.setOnClickListener {
            var ids = ""
            for (index in 0 until ll_input_list.childCount) {
                val handInputView = ll_input_list.getChildAt(index) as HandInputView
                if (!TextUtils.isEmpty(handInputView.handInfo)) {
                    ids += handInputView.handInfo + ","
                }
            }

            if (!TextUtils.isEmpty(ids)) {
                getViewModel(OrderExpressMainModel::class.java).ensureExpressSign(ids)
            } else {
                showToast("请输入磅码单号")
            }
        }
    }

    private fun initClick() {
        for (index in 0 until ll_input_list.childCount) {
            val handInputView = ll_input_list.getChildAt(index) as HandInputView
            handInputView.setOnHandViewActionListener {
                for (index2 in 0 until ll_input_list.childCount) {
                    if (index == index2) {
                        ll_input_list.removeViewAt(index)
                        initClick()
                    }
                }
            }
        }
    }

    override fun initData() {

    }

    @LiveDataMatch
    open fun onEnsureExpressSignSuccess(data: RespEnsureExpressSign) {
        HandInputBoundSuccessActivity.start(this, data.toJson())
    }
}