package com.zczy.cargo_owner.order.confirm.dialog

import android.annotation.SuppressLint
import android.content.Context
import android.graphics.drawable.ColorDrawable
import android.view.Gravity
import android.view.LayoutInflater
import android.view.View
import android.view.ViewGroup
import android.widget.PopupWindow
import android.widget.TextView
import androidx.recyclerview.widget.LinearLayoutManager
import androidx.recyclerview.widget.RecyclerView
import com.chad.library.adapter.base.BaseQuickAdapter
import com.zczy.cargo_owner.libcomm.config.HttpConfig
import com.zczy.cargo_owner.order.R
import com.zczy.cargo_owner.order.confirm.adapter.OrderDeductionDeployAdapters
import com.zczy.cargo_owner.order.model.RspConsignorSmartDeficitTonDefaultRuleList
import com.zczy.comm.utils.dp2px
import com.zczy.comm.utils.ex.isNull
import com.zczy.comm.widget.itemdecoration.SpaceItemDecoration
import com.zczy.comm.x5.X5WebActivity

/**
 *  user: ssp
 *  time: 2020/7/23 16:28
 *  desc: 扣款配置
 */
class OrderDeductionDeployDialogs(
    context: Context?,
    orderId: String?,
    listData: List<RspConsignorSmartDeficitTonDefaultRuleList>?,
    clickListener: OnClickSubmitListener,
    selectData: RspConsignorSmartDeficitTonDefaultRuleList?
) : PopupWindow(context) {

    fun show(parent: View?) {
        super.showAtLocation(parent, Gravity.BOTTOM, 0, 0)
    }

    interface OnClickSubmitListener {

        /** 返回选择数据*/
        fun onClickSubmitListener(data: RspConsignorSmartDeficitTonDefaultRuleList?)
    }

    init {
        @SuppressLint("InflateParams")
        val itemView = LayoutInflater.from(context).inflate(R.layout.order_deduction_deploy_dialog, null, false)
        contentView = itemView
        itemView.setOnClickListener { dismiss() }
        val background = ColorDrawable()
        background.alpha = 100
        setBackgroundDrawable(background)
        this.isFocusable = true
        this.width = ViewGroup.LayoutParams.MATCH_PARENT
        this.height = ViewGroup.LayoutParams.MATCH_PARENT
        this.isOutsideTouchable = true
        this.animationStyle = R.style.take_photo_anim
        val mAdapter = OrderDeductionDeployAdapters(selectData ?: RspConsignorSmartDeficitTonDefaultRuleList())
        mAdapter.apply {
            onItemChildClickListener =
                BaseQuickAdapter.OnItemChildClickListener { _, view, position ->
                    val item = mAdapter.data[position]
                    when (view.id) {
                        R.id.view_check_1 -> {
                            mAdapter.setTonRuleId(item?.ruleId)
                        }

                        R.id.tv_detail -> {
                            // 亏涨吨详情
                            X5WebActivity.startNoTitleContentUI(context, HttpConfig.getWebUrl() + "/form_h5/h5_inner/index.html?_t=${System.currentTimeMillis()}#/lossRiseRule?ruleId=${item?.ruleId}&orderId=${orderId}")
                        }
                    }
                }
            setNewData(listData)
        }
        val tvClose = itemView.findViewById<TextView>(R.id.tvClose)
        val tv_sure = itemView.findViewById<TextView>(R.id.tv_sure)
        tv_sure.setOnClickListener {
            val checkedData = mAdapter.getCheckedData()
            if (checkedData.isNull) {
                this.dismiss()
            } else {
                clickListener.onClickSubmitListener(checkedData)
                this.dismiss()
            }
        }
        tvClose.setOnClickListener { dismiss() }
        val recyclerView: RecyclerView = itemView.findViewById(R.id.recyclerView)
        recyclerView.layoutManager = LinearLayoutManager(context)
        recyclerView.adapter = mAdapter
        recyclerView.addItemDecoration(SpaceItemDecoration(dp2px(1f)))
    }
}