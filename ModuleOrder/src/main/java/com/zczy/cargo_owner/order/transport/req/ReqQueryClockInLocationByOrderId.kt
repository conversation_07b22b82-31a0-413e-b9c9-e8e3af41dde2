package com.zczy.cargo_owner.order.transport.req

import com.zczy.comm.http.entity.BaseNewRequest
import com.zczy.comm.http.entity.BaseRsp
import com.zczy.comm.http.entity.ResultData

/**
 * 注释：WLHY-15581 【汽运】货主前台增加运单打卡信息
 * 时间：2025/5/19 20:34
 * @author：王家辉
 * wiki：http://wiki.zczy56.com/pages/viewpage.action?pageId=83002077
 * 对接：濮志尧
 * */
class ReqQueryClockInLocationByOrderId(
    var orderId: String? = null,
) : BaseNewRequest<BaseRsp<RspClockInLocation>>("oms-app/order/consignor/queryClockInLocationByOrderId") {
}

data class RspClockInLocation(
    val data: MutableList<ClockInLocationByOrderInfo>? = null,
) : ResultData()

data class ClockInLocationByOrderInfo(
    val orderId: String? = null,// 订单号 oid
    val createTime: String? = null,// 打卡时间
    val lon: String? = null,// 经度
    val lat: String? = null,// 纬度
    val addr: String? = null, // 地址
)

