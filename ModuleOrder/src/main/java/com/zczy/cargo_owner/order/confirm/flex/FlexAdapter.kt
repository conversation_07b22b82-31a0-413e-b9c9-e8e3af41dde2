package com.zczy.cargo_owner.order.confirm.flex

import android.view.View
import android.view.ViewGroup
import com.zczy.cargo_owner.order.R
import kotlinx.android.synthetic.main.flex_item_text.view.*

class FlexAdapter : BaseRecyclerViewAdapter<String>() {
    override fun onCreateViewHolder(parent: ViewGroup, viewType: Int): BaseRecyclerViewHolder<String> {
        return ViewHolder(getView(parent, R.layout.flex_item_text))
    }

    inner class ViewHolder(view: View) : BaseRecyclerViewHolder<String>(view) {
        override fun onBaseBindViewHolder(t: String, position: Int) {
            view.tv_text.text = t
        }
    }

}