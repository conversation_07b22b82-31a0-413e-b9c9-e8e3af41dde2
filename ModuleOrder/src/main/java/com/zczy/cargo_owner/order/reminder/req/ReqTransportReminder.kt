package com.zczy.cargo_owner.order.reminder.req

import com.zczy.comm.http.entity.BaseNewRequest
import com.zczy.comm.http.entity.BaseRsp
import com.zczy.comm.http.entity.PageList

/**
 *  user: ssp
 *  time: 2021/7/14 14:47
 *  desc: 运输提醒
 */
class ReqTransportReminder(
    var nowPage: Int = 1,
    var pageSize: Int = 10,
    var orderId: String = "",//订单号
    var exceptionType: String = "",//提醒类型(1:摘牌无车辆定位;2:启运地不符;3:目的地不符;4:发货无车辆定位;5:未按时发货确认;6:未按时收货确认;7:在途无定位;8:偏离路线;9:停留未行驶)
    var remindFlag: String = "",//提醒状态( 0:已关闭;1:已提醒;2:已删除)
    var remindStartTime: String = "",//提醒开始时间
    var remindEndTime: String = "",//提醒结束时间
    var carrierUserName: String = "",//承运方名称
    var plateNumber: String = "",//车牌号
    var month: String? = null,//月份
) : BaseNewRequest<BaseRsp<PageList<RspTransportReminder>>>("oms-app/order/consignorTransportRemind/queryConsignorTransportRemind")

class RspTransportReminder(
    var id: String = "",//提醒主键id
    var orderId: String = "",//订单号
    var exceptionType: String = "",//提醒类型 (1: 摘牌无车辆定位;2:启运地不符;3:目的地不符;4:发货无车辆定位;5:未按时发货确认;6:未按时收货确认;7:在途无定位;8:偏离路线;9:停留未行驶)
    var plateNumber: String = "",//车牌号
    var carrierUserName: String = "",//承运方名称
    var remindFlag: String = "",//提醒状态(0:已关闭;1:已提醒;2:已删除)
    var remindTime: String = "",//提醒时间
    var consignorUserId: String = "",   //货主id
    var carrierUserId: String = "",    //承运方id
    var remark: String = "" //备注说明
)

fun RspTransportReminder.exceptionTypeStr(): String {
    return when (exceptionType) {

        "1" -> {
            "摘牌无车辆定位"
        }

        "2" -> {
            "启运地不符"
        }

        "3" -> {
            "目的地不符"
        }

        "4" -> {
            "发货无车辆定位"
        }

        "5" -> {
            "未按时发货确认"
        }

        "6" -> {
            "未按时收货确认"
        }

        "7" -> {
            "在途无定位"
        }

        "8" -> {
            "偏离路线"
        }

        "9" -> {
            "停留未行驶"
        }

        "10" -> {
            "设备离线预警"
        }

        "11" -> {
            "人车定位偏移-在途"
        }

        else -> {
            ""
        }
    }

}