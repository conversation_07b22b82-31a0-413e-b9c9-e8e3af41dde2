package com.zczy.cargo_owner.order.violate.req

import com.zczy.comm.http.entity.BaseNewRequest
import com.zczy.comm.http.entity.BaseRsp
import com.zczy.comm.http.entity.PageList
import com.zczy.comm.http.entity.ResultData

/**
 * PS: 3. 违约列表（前台）(违约管理中违约记录列表，既同意或拒绝之后生成的违约单)
 * http://wiki.zczy56.com/pages/viewpage.action?pageId=12356059
 * Created by sdx on 2019/2/27.
 */
class Req3BreachContractList(
        var orderId: String = "",
        var nowPage: Int = 1,
        var pageSize: Int = 10
) : BaseNewRequest<BaseRsp<PageList<RspBreachContractItem>>>("wo-app/order/breach/breachContractList")

data class RspBreachContractItem(
        /** 用户ID */
        var userId: String = "",
        /** 子账户Id */
        var childId: String = "",
        /** 子账户数组 */
        var childIds: String = "",
        /** 子账户集合 */
        var nowPage: String = "",
        /** 当前页 */
        var pageSize: String = "",
        /** 开始时间 */
        var startTime: String = "",
        /** 结束时间 */
        var endTime: String = "",
        /** 违约单号 */
        var breachNumber: String = "",
        /** 订单Id */
        var orderId: String = "",
        /** 订单Id */
        var orderIdStr: String = "",
        /** 当前用户类型 */
        var userType: String = "",
        /** 被违约方会用户名称 */
        var complaintUserName: String = "",
        /** 被违约方类型：1:货主，2:承运方 */
        var complaintUserType: String = "",
        /** 被违约用户Id */
        var complaintUserId: String = "",
        /** 被违约用户Id */
        var complaintId: String = "",
        /**	被违约方用户号码 */
        var complaintUserMobile: String = "",
        /**	违约用户Id */
        var beComplaintId: String = "",
        /**	违约方用户名称 */
        var beComplaintUserName: String = "",
        /** 违约方类型：1:货主，2:承运方 */
        var beComplaintUserType: String = "",
        /** 违约方用户号码 */
        var beComplaintUserMobile: String = "",
        /** 具体违约类型名称 */
        var breachTypeName: String = "",
        /** 工单类型：-1：货主违约，-2：承运方违约 */
        var workType: String = "",
        /** 创建时间 */
        var createdTime: String = "",
        /** 创建时间 */
        var createdTimeStr: String = "",
        /** 创建人 */
        var createdBy: String = "",
        /** 违约金 */
        var indemnityMoney: String = "",
        /** 赔付投诉人金额 */
        var indemnityComplaint: String = "",
        /** 赔付平台金额 */
        var indemnitySys: String = "",
        /** 是否结算：0:未结算，1:已结算 */
        var isSettle: String = "",
        /** 说明 */
        var remark: String = "",
        /** 违约单ID */
        var breachId: String = "",
        /** 来源1：新内部工单ID */
        var workId: String = "",
        /** 来源2：违约申请单ID */
        var breachApplyId: String = "",
        /** 违约类型：包括前台违约类型和后台违约类型 */
        var leafBreachTypeName: String = "",
        /** 最后修改人 */
        var lastUptBy: String = "",
        /** 最后修改时间 */
        var lastUptTime: String = "",
        /** 删除标记：0:否，1:是 */
        var deleteFlag: String = "",
        /** 投诉人违约金结算状态 */
        var complaintSettleState: String = "",
        /** 被投诉人违约金结算状态 */
        var beComplaintSettleState: String = "",
        /** 平台理赔金额 */
        var platformAmount: String = "",
        /** 加盟商理赔金额 */
        var franchiserAmount: String = "",
        /** 货主理赔金额 */
        var carrierAmount: String = "",
        /** 是否违约终止 0:否, 1:是 */
        var stopFlag: String = "",
        /** 货主理赔金额 */
        var consignorAmount: String = "",
        /** 是否有回单信息 0:否, 1:是 */
        var backOrderFlag: String = "",
        /** 回单信息照片 */
        var backOrderFiles: List<String> = arrayListOf(),
        var breachPolicyMoney: String = "",//货物保障服务费
) : ResultData()

fun RspBreachContractItem.formatIsSettle(): String {
    // 是否结算：0:未结算，1:已结算
    return when (isSettle) {
        "0" -> "未结算"
        "1" -> "已结算"
        else -> ""
    }
}

fun RspBreachContractItem.formatBeComplaintUserType(): String {
    // 违约方类型：1:货主，2:承运方
    return when (beComplaintUserType) {
        "1" -> "高级货主"
        "2" -> "个体司机"
        "3" -> "物流企业"
        "6" -> "初级货主"
        "7" -> "初级承运人"
        "8" -> "经纪人"
        "10" -> "车队老板"
        "13" -> "加盟商"
        else -> ""
    }
}