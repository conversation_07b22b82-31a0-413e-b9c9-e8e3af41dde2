package com.zczy.cargo_owner.order.overdue.model

import com.sfh.lib.exception.HandleException
import com.sfh.lib.mvvm.service.BaseViewModel
import com.sfh.lib.rx.IResult
import com.sfh.lib.rx.IResultSuccess
import com.zczy.cargo_owner.order.model.ReqCheckNospecify
import com.zczy.cargo_owner.order.overdue.req.OrderOverDueItem
import com.zczy.cargo_owner.order.overdue.req.ReqAppealOverdueOrders
import com.zczy.cargo_owner.order.overdue.req.ReqQueryAppealDetail
import com.zczy.cargo_owner.order.overdue.req.ReqQueryOrderOverDueList
import com.zczy.cargo_owner.order.overdue.req.ReqAddProblemFeedback
import com.zczy.cargo_owner.order.overdue.req.ReqQueryProblemFeedbackDetail
import com.zczy.cargo_owner.order.overdue.req.ReqQueryProblemFeedbackPage
import com.zczy.cargo_owner.order.overdue.req.ReqQueryProblemFeedbackTypeList
import com.zczy.cargo_owner.order.overdue.req.RspQueryProblemFeedbackDetail
import com.zczy.cargo_owner.order.overdue.req.RspQueryProblemFeedbackPage
import com.zczy.cargo_owner.order.overdue.req.RspQueryProblemFeedbackTypeList
import com.zczy.comm.CommServer
import com.zczy.comm.file.IFileServer
import com.zczy.comm.http.entity.BaseRsp
import com.zczy.comm.http.entity.PageList
import com.zczy.comm.http.entity.ResultData
import java.io.File

/** 功能描述:
 * 逾期运单管理model
 */
class OrderOverdueFragmentModel : BaseViewModel() {

    /***
     * 上传文件
     * 响应方法：onaddViolateSuccess，onFileFailure
     * @param file
     */
    fun upFile(file: MutableList<String>?) {
        file?.forEach {
            this.upFile(it)
        }
    }

    fun upFile(file: String?) {
        val disposable =
            com.zczy.comm.CommServer.getFileServer().update(java.io.File(file), object : com.zczy.comm.file.IFileServer.OnFileUploaderListener {
                override fun onSuccess(tag: java.io.File, url: String) {
                    setValue("onFileSuccess", tag, url)
                }

                override fun onFailure(tag: java.io.File, error: String) {
                    showToast(error)
                    setValue("onFileFailure", tag, error)
                }
            })
        putDisposable(disposable)
    }

    /**
     * 逾期运单管理列表 待处理
     */
    fun getUndoListInfo(nowPage: Int, dealType: String) {
        execute(ReqQueryOrderOverDueList(dealType = dealType, nowPage = nowPage, remarks = ""),
            object : IResult<BaseRsp<PageList<OrderOverDueItem>>> {
                override fun onSuccess(t: BaseRsp<PageList<OrderOverDueItem>>) {
                    if (t.success()) {
                        setValue("onGetUndoListInfo", t.data)
                    } else {
                        showDialogToast(t.msg)
                        setValue("onGetUndoListInfo", null)
                    }
                }

                override fun onFail(e: HandleException) {
                    showDialogToast(e.msg)
                    setValue("onGetUndoListInfo", null)
                }
            })
    }


    /**
     * 逾期运单管理列表 已提交问题反馈列表
     */
    fun queryProblemFeedbackPage(nowPage: Int) {
        execute(
            ReqQueryProblemFeedbackPage(nowPage = nowPage),
            object : IResult<BaseRsp<PageList<RspQueryProblemFeedbackPage>>> {
                override fun onSuccess(t: BaseRsp<PageList<RspQueryProblemFeedbackPage>>) {
                    if (t.success()) {
                        setValue("onQueryProblemFeedbackPage", t.data)
                    } else {
                        showDialogToast(t.msg)
                        setValue("onQueryProblemFeedbackPage", null)
                    }
                }

                override fun onFail(e: HandleException) {
                    showDialogToast(e.msg)
                    setValue("onQueryProblemFeedbackPage", null)
                }
            })
    }

    /**
     * 逾期运单管理列表 已提交问题反馈详情
     */
    fun queryProblemFeedbackDetail(feedbackId: String) {
        execute(
            ReqQueryProblemFeedbackDetail(feedbackId = feedbackId),
            object : IResult<BaseRsp<RspQueryProblemFeedbackDetail>> {
                override fun onSuccess(t: BaseRsp<RspQueryProblemFeedbackDetail>) {
                    if (t.success()) {
                        setValue("onQueryProblemFeedbackDetail", t.data)
                    } else {
                        showDialogToast(t.msg)
                        setValue("onQueryProblemFeedbackDetail", null)
                    }
                }

                override fun onFail(e: HandleException) {
                    showDialogToast(e.msg)
                    setValue("onQueryProblemFeedbackDetail", null)
                }
            })
    }

    /**
     * 逾期运单管理列表搜索
     */
    fun getSearchListInfo(deltype: String, nowPage: Int, remarks: String?, limitPublishFlag: String?) {
        execute(ReqQueryOrderOverDueList(dealType = deltype, nowPage = nowPage, remarks = remarks, limitPublishFlag = limitPublishFlag),
            object : IResult<BaseRsp<PageList<OrderOverDueItem>>> {
                override fun onSuccess(t: BaseRsp<PageList<OrderOverDueItem>>) {
                    if (t.success()) {
                        setValue("onGetUndoListInfo", t.data)
                    } else {
                        showDialogToast(t.msg)
                        setValue("onGetUndoListInfo", null)
                    }
                }

                override fun onFail(e: HandleException) {
                    showDialogToast(e.msg)
                    setValue("onGetUndoListInfo", null)
                }
            })
    }

    /**
     * 逾期运单管理列表 处理完成
     */
    fun getDoneListInfo(nowPage: Int, dealType: String) {
        execute(ReqQueryOrderOverDueList(dealType = dealType, nowPage = nowPage, remarks = ""),
            object : IResult<BaseRsp<PageList<OrderOverDueItem>>> {
                override fun onSuccess(t: BaseRsp<PageList<OrderOverDueItem>>) {
                    if (t.success()) {
                        setValue("onGetDoneListInfo", t.data)
                    } else {
                        showDialogToast(t.msg)
                        setValue("onGetDoneListInfo", null)
                    }
                }

                override fun onFail(e: HandleException) {
                    showDialogToast(e.msg)
                    setValue("onGetDoneListInfo", null)
                }
            })
    }

    /***
     * 货主或者个体司机点击运单列表上的违约申请时，校验此运单是否是非指定预付款的运单
     * 响应方法：onViolateSuccess
     */
    fun checkNospecify(item: OrderOverDueItem) {
        execute(
            true,
            ReqCheckNospecify(item.orderId)
        ) { t ->
            if (t.success()) {
                setValue("onViolateSuccess", item)
            } else {
                showDialogToast(t.msg)
            }
        }
    }

    /** 逾期申请 **/
    fun appealOverdueOrders(req: ReqAppealOverdueOrders) {
        execute(req) { t ->
            if (t.success()) {
                setValue("appealOverdueOrdersSuccess")
            } else {
                showDialogToast(t.msg)
            }
        }
    }

    /** 申诉详情 **/
    fun queryAppealDetail(req: ReqQueryAppealDetail) {
        execute(req) { t ->
            if (t.success()) {
                setValue("queryAppealDetailSuccess", t.data)
            } else {
                showDialogToast(t.msg)
            }
        }
    }

    /** 查询问题反馈类型列表 **/
    fun queryProblemFeedbackTypeList() {
        execute(
            ReqQueryProblemFeedbackTypeList(),
            object : IResult<BaseRsp<RspQueryProblemFeedbackTypeList>> {
                override fun onSuccess(t: BaseRsp<RspQueryProblemFeedbackTypeList>) {
                    if (t.success()) {
                        setValue("onQueryProblemFeedbackTypeList", t.data)
                    } else {
                        showDialogToast(t.msg)
                        setValue("onQueryProblemFeedbackTypeList", null)
                    }
                }

                override fun onFail(e: HandleException) {
                    showDialogToast(e.msg)
                    setValue("onQueryProblemFeedbackTypeList", null)
                }
            })
    }

    /** 新增问题反馈 **/
    fun addProblemFeedback(req: ReqAddProblemFeedback) {
        execute(req) { t ->
            if (t.success()) {
                setValue("addProblemFeedbackSuccess")
            } else {
                showDialogToast(t.msg)
            }
        }
    }
}