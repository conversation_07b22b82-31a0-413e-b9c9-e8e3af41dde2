package com.zczy.cargo_owner.order.reminder

import android.app.Activity
import android.content.Context
import android.content.Intent
import android.os.Bundle
import android.view.View
import androidx.fragment.app.Fragment
import com.flyco.tablayout.listener.CustomTabEntity
import com.flyco.tablayout.listener.OnTabSelectListener
import com.jaeger.library.StatusBarUtil
import com.zczy.cargo_owner.order.R
import com.zczy.cargo_owner.order.reminder.fragment.TransportReminderHomeFragmentV1
import com.zczy.cargo_owner.order.reminder.fragment.TransportReminderHomeFragmentV2
import com.zczy.cargo_owner.order.reminder.fragment.TransportReminderHomeFragmentV3
import com.zczy.cargo_owner.order.reminder.model.TransportReminderModel
import com.zczy.cargo_owner.order.reminder.req.ReqTransportReminder
import com.zczy.comm.ui.BaseActivity
import com.zczy.comm.ui.BaseFragment
import com.zczy.comm.widget.tablayout.CommonTabEntity
import kotlinx.android.synthetic.main.transport_reminder_home_activity.back_img
import kotlinx.android.synthetic.main.transport_reminder_home_activity.common_tab_layout
import kotlinx.android.synthetic.main.transport_reminder_home_activity.tvFilter


/**
 *  user: ssp
 *  time: 2021/7/13 16:16
 *  desc: 运输提醒首页
 */

class TransportReminderHomeActivity : BaseActivity<TransportReminderModel>() {


    companion object {

        @JvmStatic
        fun jumpPage(context: Context) {
            val intent = Intent(context, TransportReminderHomeActivity::class.java)
            context.startActivity(intent)
        }
    }

    override fun initData() {
    }

    override fun getLayout(): Int {
        return R.layout.transport_reminder_home_activity
    }

    override fun bindView(bundle: Bundle?) {
        StatusBarUtil.setTranslucent(this)
        bindClickEvent(tvFilter)
        bindClickEvent(back_img)
        initCommonTabLayout()
    }

    private fun initCommonTabLayout() {
        val tabEntities = ArrayList<CustomTabEntity>()
        tabEntities.add(CommonTabEntity().apply { title = "运输提醒" })
        tabEntities.add(CommonTabEntity().apply { title = "运输风险" })
        tabEntities.add(CommonTabEntity().apply { title = "合规风险" })
        val fragments = ArrayList<Fragment>()
        fragments.add(TransportReminderHomeFragmentV1.newInstance())
        fragments.add(TransportReminderHomeFragmentV2.newInstance())
        fragments.add(TransportReminderHomeFragmentV3.newInstance())
        common_tab_layout.setTabData(tabEntities, this, R.id.frame_layout, fragments)
        common_tab_layout.setOnTabSelectListener(object : OnTabSelectListener {
            override fun onTabReselect(position: Int) {
                refresh(position)
            }

            override fun onTabSelect(position: Int) {
                refresh(position)
            }

            private fun refresh(position: Int) {
                val fragment = fragments[position]
                if (fragment is BaseFragment<*>) {
                    fragment.refresh()
                }
            }
        })
        common_tab_layout.currentTab = 0
    }

    override fun onSingleClick(v: View) {
        super.onSingleClick(v)
        when (v.id) {

            R.id.tvFilter -> {
                //筛选
                TransportReminderHomeSearchActivityV1.jumpPage(
                    this@TransportReminderHomeActivity,
                    TransportReminderHomeSearchActivityV1.REQUEST_CODE
                )
            }

            R.id.back_img -> {
                //返回
                finish()
            }
        }
    }

    override fun onActivityResult(requestCode: Int, resultCode: Int, data: Intent?) {
        super.onActivityResult(requestCode, resultCode, data)
        if (resultCode == Activity.RESULT_OK) {
            val reqTransportReminder = TransportReminderHomeSearchActivityV1.obtainSelectData(data) ?: ReqTransportReminder()
            //TODO 刷新列表
        }
    }
}