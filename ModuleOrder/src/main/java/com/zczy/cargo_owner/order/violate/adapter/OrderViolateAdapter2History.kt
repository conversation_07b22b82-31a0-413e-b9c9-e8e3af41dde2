package com.zczy.cargo_owner.order.violate.adapter

import android.text.TextUtils
import com.chad.library.adapter.base.BaseQuickAdapter
import com.chad.library.adapter.base.BaseViewHolder
import com.zczy.cargo_owner.order.R
import com.zczy.cargo_owner.order.violate.req.RspBreachContractItem
import com.zczy.cargo_owner.order.violate.req.formatBeComplaintUserType
import com.zczy.cargo_owner.order.violate.req.formatIsSettle

class OrderViolateAdapter2History : BaseQuickAdapter<RspBreachContractItem, BaseViewHolder>(R.layout.order_violate_item_2_history) {

    override fun convert(helper: BaseViewHolder, item: RspBreachContractItem) {
        helper
                // 申请单号
                .setText(R.id.tv_apply_order_value, item.breachNumber)
                // 投诉人
                .setText(R.id.tv_complainant_value, item.complaintUserName)
                // 被投诉人
                .setText(R.id.tv_violate_people_value, item.beComplaintUserName)
                // 违约类型
                .setText(R.id.tv_violate_type_value, item.leafBreachTypeName)
                // 违约金额
                .setText(R.id.tv_violate_money_value, item.indemnityMoney)
                // 违约会员类型
                .setText(R.id.tv_member_type_value, item.formatBeComplaintUserType())
                // 提交时间
                .setText(R.id.tv_time_value, item.createdTime)
                // 结算状态
                .setText(R.id.tv_reason_value, item.formatIsSettle())

        val stopFlag = item.stopFlag
        when {
            TextUtils.equals("1", stopFlag) -> helper.setText(R.id.tv_violate_state, "是否取消运单：   是")
                    .setGone(R.id.tv_reason_value, false)
                    .setGone(R.id.tv_reason_title, false)
            else -> helper.setText(R.id.tv_violate_state, "是否取消运单：   否")
                    .setGone(R.id.tv_reason_value, true)
                    .setGone(R.id.tv_reason_title, true)
        }
        val backOrderFlag = item.backOrderFlag
        when {
            TextUtils.equals("1", backOrderFlag) -> {
                helper.setGone(R.id.iv_have_order_info, true)
            }
            else -> {
                helper.setGone(R.id.iv_have_order_info, false)
            }
        }
        when {
            TextUtils.equals("1", stopFlag) && !TextUtils.equals("1", backOrderFlag) -> {
                helper.setGone(R.id.cl_bottom, false)
            }
            else -> {
                helper.setGone(R.id.cl_bottom, true)
            }
        }
    }
}
