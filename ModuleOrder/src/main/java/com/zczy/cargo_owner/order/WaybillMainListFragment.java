package com.zczy.cargo_owner.order;

import android.app.Activity;
import android.graphics.Color;

import androidx.fragment.app.Fragment;
import androidx.viewpager.widget.ViewPager;

import android.view.View;
import android.view.ViewGroup;

import com.flyco.tablayout.SlidingTabLayout;
import com.flyco.tablayout.listener.OnTabSelectListener;
import com.flyco.tablayout.widget.MsgView;
import com.jaeger.library.StatusBarUtil;
import com.sfh.lib.mvvm.annotation.LiveDataMatch;
import com.sfh.lib.mvvm.service.BaseViewModel;
import com.sfh.lib.ui.AbstractLifecycleFragment;
import com.zczy.cargo_owner.libcomm.ScreenUtils;
import com.zczy.cargo_owner.libcomm.req.QuerySeniorConsignorListCountInfo;
import com.zczy.cargo_owner.order.entity.EWaybill;
import com.zczy.cargo_owner.order.model.WaybillModel;
import com.zczy.comm.pluginserver.AMainServer;
import com.zczy.comm.widget.RollLayout;
import com.zczy.comm.x5.X5WebActivity;
import com.zczy.lib_zstatistics.sdk.ZStatistics;
import com.zczy.lib_zstatistics.sdk.base.EventKeyType;
import com.zczy.lib_zstatistics.sdk.base.EventType;

import java.util.ArrayList;

/**
 * 功能描述:运单列表首页
 *
 * <AUTHOR> 孙飞虎
 * @company 中储智运科技股份有限公司
 * @copyright （版权）中储南京智慧物流科技有限公司所有
 * @date 2018/12/17
 */
public class WaybillMainListFragment extends AbstractLifecycleFragment<WaybillModel> {


    public SlidingTabLayout mCommonTabLayout;

    public ViewPager mViewPager;

    private RollLayout rollLayout;

    @Override
    public int getLayout() {

        return R.layout.order_waybill_process_list_fragment;
    }

    @Override
    public void initData(View view) {

        this.rollLayout = view.findViewById(R.id.rollLayout);
        this.mCommonTabLayout = view.findViewById(R.id.tab_layout);
        this.mViewPager = view.findViewById(R.id.viewPager);

        OnRefreshList onRefreshList = () -> {
            //请求运单角标
            getViewModel(WaybillModel.class).querySeniorConsignorListCountInfo();
        };
        String[] title = {"我的发布", "待发货", "运输中", "已完成", "全部"};
        ArrayList<Fragment> arrayList = new ArrayList<>(5);
        arrayList.add(WaybillListFragment.newInstance(EWaybill.TYPE_DELIVERMINE, onRefreshList));
        arrayList.add(WaybillListFragment.newInstance(EWaybill.TYPE_DELAYSHIPMENT, onRefreshList));
        arrayList.add(WaybillListFragment.newInstance(EWaybill.TYPE_UNLOAD, onRefreshList));
        arrayList.add(WaybillListFragment.newInstance(EWaybill.TYPE_COMPLETE, onRefreshList));
        arrayList.add(WaybillListFragment.newInstance(EWaybill.TYPE_ALL, onRefreshList));

        this.mCommonTabLayout.setViewPager(mViewPager, title, getActivity(), arrayList);
        View ly_search = view.findViewById(R.id.ly_search);

        ly_search.setOnClickListener((View v) -> {
            //8777，判断是否为冀东账号
            getViewModel().querySingleDictConfigV1();
        });

        StatusBarUtil.setTransparentForImageViewInFragment(getActivity(), ly_search);
        this.rollLayout.setBackgroundColor(Color.parseColor("#ffecf5ff"));

        this.mCommonTabLayout.setOnTabSelectListener(new OnTabSelectListener() {
            @Override
            public void onTabSelect(int position) {
                //点击顶边菜单项
                try {
                    Fragment fragment = arrayList.get(position);
                    if (fragment instanceof OnRefreshList) {
                        ((OnRefreshList) fragment).onRefresh();
                    }

                } catch (Exception e) {
                }
            }

            @Override
            public void onTabReselect(int position) {

            }
        });
        this.mViewPager.addOnPageChangeListener(new ViewPager.OnPageChangeListener() {
            @Override
            public void onPageScrolled(int i, float v, int i1) {
            }

            @Override
            public void onPageSelected(int position) {
                //左右滑动顶边菜单项
                try {
                    Fragment fragment = arrayList.get(position);
                    if (fragment instanceof OnRefreshList) {
                        ((OnRefreshList) fragment).onRefresh();
                    }
                } catch (Exception e) {
                }
            }

            @Override
            public void onPageScrollStateChanged(int i) {

            }
        });
    }

    @LiveDataMatch
    public void showHomeWaybillSubscript(QuerySeniorConsignorListCountInfo data) {
        if (data == null) {
            return;
        }
        //发货数量
        if (data.getPubOrderCountForApp() > 0) {
            this.mCommonTabLayout.showMsg(0, data.getPubOrderCountForApp());
            this.setMsgView(this.mCommonTabLayout.getMsgView(0), data.getPubOrderCountForApp());
        } else {
            this.mCommonTabLayout.hideMsg(0);
        }
        //待发货数量
        if (data.getDeliverOrderCount() > 0) {
            this.mCommonTabLayout.showMsg(1, data.getDeliverOrderCount());
            this.setMsgView(this.mCommonTabLayout.getMsgView(1), data.getDeliverOrderCount());
        } else {
            this.mCommonTabLayout.hideMsg(1);
        }
        //运输中数量
        if (data.getReceiptOrderCount() > 0) {
            this.mCommonTabLayout.showMsg(2, data.getReceiptOrderCount());
            this.setMsgView(this.mCommonTabLayout.getMsgView(2), data.getReceiptOrderCount());
        } else {
            this.mCommonTabLayout.hideMsg(2);
        }

        //通知主页底部菜单刷新
        postEvent(data);
    }


    private void setMsgView(MsgView msgView, int count) {
        ViewGroup.MarginLayoutParams params = (ViewGroup.MarginLayoutParams) msgView.getLayoutParams();
        params.topMargin = ScreenUtils.dpToPixels(getContext(), 2);
        params.leftMargin = ScreenUtils.dpToPixels(getContext(), 50);
        params.width = ScreenUtils.dpToPixels(getContext(), 20);
        params.height = ScreenUtils.dpToPixels(getContext(), 20);
        msgView.setPadding(0, 0, 0, 0);
        if (count > 999) {
            msgView.setText("999+");
        } else {
            msgView.setText(String.valueOf(count));
        }
        msgView.setTextSize(8);
        msgView.setLayoutParams(params);
    }

    @LiveDataMatch(tag = "冀东账号判断")
    public void showJiDongMenu(boolean show) {
        if (show) {
            WaybillSearchJiDongActivity.start(getActivity());
        } else {
            WaybillFilterActivity.jumpPage(getActivity());
        }
    }

    long zStatisticsTime = 0L;

    @Override
    public void onResume() {
        super.onResume();
        BaseViewModel viewModel;
        if (this.rollLayout != null && (viewModel = getViewModel()) != null) {
            rollLayout.setOnShareListener(v -> {
                X5WebActivity.listener = v1 -> {
                    // 通过View获取Activity上下文，并检查Activity是否仍然有效
                    if (v1 != null && v1.getContext() instanceof Activity) {
                        Activity activity = (Activity) v1.getContext();
                        if (!activity.isFinishing() && !activity.isDestroyed()) {
                            AMainServer mainServer = AMainServer.getPluginServer();
                            if (mainServer != null) {
                                mainServer.jumpShareDialog(activity, X5WebActivity.text, X5WebActivity.Shareurl);
                            }
                        }
                    }
                };
                X5WebActivity.startContentUI(WaybillMainListFragment.this.getContext(), v.getLink());

            });
            this.rollLayout.queryRollOrder(viewModel);
        }
        zStatisticsTime = System.currentTimeMillis();

        //请求运单角标
        getViewModel(WaybillModel.class).querySeniorConsignorListCountInfo();

    }

    @Override
    public void onPause() {
        super.onPause();
        ZStatistics.onEvent(this.getClass().getName(), this.getClass().getSimpleName(), map -> {
            map.put(EventKeyType.EVENT.value(), EventType.ON_VIEW);
            map.put(EventKeyType.EVENTDURATION.value(), System.currentTimeMillis() - zStatisticsTime);
        });
    }
}
