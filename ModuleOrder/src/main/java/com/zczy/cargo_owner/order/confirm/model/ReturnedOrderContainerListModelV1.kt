package com.zczy.cargo_owner.order.confirm.model

import com.sfh.lib.mvvm.service.BaseViewModel
import com.zczy.cargo_owner.order.confirm.bean.ReqReturnedOrderContainerListV1

/**
 * 功能描述: 发货详情
 * <AUTHOR>
 * @date 2022/3/8-20:05
 */

class ReturnedOrderContainerListModelV1 : BaseViewModel() {

    fun queryList(req: ReqReturnedOrderContainerListV1) {
        execute(req) {
            if (it.success()) {
                setValue("onListSuccess", it.data)
            } else {
                showDialogToast(it.msg)
            }
        }
    }
}