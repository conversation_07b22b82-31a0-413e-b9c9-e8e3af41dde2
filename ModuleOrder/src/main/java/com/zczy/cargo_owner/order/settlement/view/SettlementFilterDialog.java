package com.zczy.cargo_owner.order.settlement.view;

import android.content.Context;
import android.graphics.Rect;
import android.graphics.drawable.ColorDrawable;
import android.os.Build;
import androidx.annotation.Nullable;
import androidx.recyclerview.widget.LinearLayoutManager;
import androidx.recyclerview.widget.RecyclerView;
import android.view.LayoutInflater;
import android.view.View;
import android.view.ViewGroup;
import android.widget.PopupWindow;
import android.widget.TextView;

import com.chad.library.adapter.base.BaseQuickAdapter;
import com.chad.library.adapter.base.BaseViewHolder;
import com.zczy.cargo_owner.order.R;
import com.zczy.cargo_owner.order.settlement.bean.SelectedTagBean;

import java.util.ArrayList;
import java.util.List;

/**
 * 功能描述:
 * 运单状态选择
 *
 * <AUTHOR> 孙飞虎
 * @company 中储智运科技股份有限公司
 * @copyright （版权）中储南京智慧物流科技有限公司所有
 * @date 2019/11/6
 */
public class SettlementFilterDialog extends PopupWindow {
    private List<String> data;
    private ItemOnClick itemOnClick;

    public SettlementFilterDialog(Context context, List<String> data, ItemOnClick itemOnClick) {
        super(context);
        this.data = data;
        this.itemOnClick = itemOnClick;

        View itemView = LayoutInflater.from(context).inflate(R.layout.order_select_settlement_platform_dialog, null, false);
        setContentView(itemView);
        itemView.setOnClickListener(v -> dismiss());
        ColorDrawable background = new ColorDrawable();
        background.setAlpha(100);
        this.setBackgroundDrawable(background);
        this.setFocusable(true);
        this.setWidth(ViewGroup.LayoutParams.MATCH_PARENT);
        this.setHeight(ViewGroup.LayoutParams.MATCH_PARENT);
        this.setOutsideTouchable(true);

        RecyclerView recyclerView = itemView.findViewById(R.id.rv_view);
        recyclerView.setLayoutManager(new LinearLayoutManager(context));

        List<SelectedTagBean> selectedTagList = new ArrayList<>();
        for (String tag : data) {
            selectedTagList.add(new SelectedTagBean(tag, false));
        }

        ItemAdapter adapter = new ItemAdapter(selectedTagList);
        adapter.setOnItemClickListener((adapter1, view, position) -> {
            for (SelectedTagBean bean : selectedTagList) {
                bean.setSelected(false);
            }
            SelectedTagBean bean = selectedTagList.get(position);
            bean.setSelected(!bean.getSelected());
            itemOnClick.onSelectedText(bean.getTag(),position);
            adapter.notifyDataSetChanged();
            dismiss();
        });
        recyclerView.setAdapter(adapter);
    }

    @Override
    public void dismiss() {
        super.dismiss();
        itemOnClick.dismiss();
    }

    public void show(View anchor) {
        if (Build.VERSION.SDK_INT >= 24) {
            Rect visibleFrame = new Rect();
            anchor.getGlobalVisibleRect(visibleFrame);
            int h = anchor.getResources().getDisplayMetrics().heightPixels - visibleFrame.bottom;
            super.setHeight(h);
        }
        super.showAsDropDown(anchor);
    }

    private class ItemAdapter extends BaseQuickAdapter<SelectedTagBean, BaseViewHolder> {

        ItemAdapter(@Nullable List<SelectedTagBean> data) {
            super(R.layout.order_settlement_sort_condition_item, data);
        }

        @Override
        protected void convert(BaseViewHolder helper, SelectedTagBean item) {
            TextView txtPlatform = helper.getView(R.id.txtPlatform);
            txtPlatform.setText(item.getTag());
            if (item.getSelected()) {
                helper.setVisible(R.id.imgChecked, true);
            } else {
                helper.setVisible(R.id.imgChecked, false);
            }
        }
    }

    public interface ItemOnClick {
        void onSelectedText(String selectedStr,int position);

        void dismiss();
    }
}