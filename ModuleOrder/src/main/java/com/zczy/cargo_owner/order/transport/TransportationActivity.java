package com.zczy.cargo_owner.order.transport;

import android.app.ProgressDialog;
import android.content.Context;
import android.content.Intent;
import android.graphics.Color;
import android.os.Bundle;
import androidx.annotation.Nullable;
import androidx.recyclerview.widget.LinearLayoutManager;
import androidx.recyclerview.widget.RecyclerView;
import android.text.TextUtils;
import android.view.View;
import android.view.ViewGroup;
import android.widget.RelativeLayout;
import android.widget.TextView;

import com.amap.api.maps.AMap;
import com.amap.api.maps.CameraUpdateFactory;
import com.amap.api.maps.MapView;
import com.amap.api.maps.model.BitmapDescriptor;
import com.amap.api.maps.model.BitmapDescriptorFactory;
import com.amap.api.maps.model.LatLng;
import com.amap.api.maps.model.MultiPointItem;
import com.amap.api.maps.model.MultiPointOverlay;
import com.amap.api.maps.model.MultiPointOverlayOptions;
import com.chad.library.adapter.base.BaseQuickAdapter;
import com.sfh.lib.mvvm.annotation.LiveDataMatch;
import com.sfh.lib.ui.AbstractLifecycleActivity;
import com.zczy.cargo_owner.order.R;
import com.zczy.cargo_owner.order.transport.adapter.VehicleAdapter;
import com.zczy.cargo_owner.order.transport.bean.ProviceVehicle;
import com.zczy.cargo_owner.order.transport.model.TransportModel;
import com.zczy.comm.http.entity.PageList;
import com.zczy.comm.ui.UtilStatus;
import com.zczy.comm.utils.ResUtil;
import com.zczy.comm.widget.AppToolber;

import java.util.ArrayList;
import java.util.List;

/**
 * 运输中
 */
public class TransportationActivity extends AbstractLifecycleActivity<TransportModel> implements BaseQuickAdapter.OnItemClickListener {

    private AppToolber mAppToolber;
    private MapView mMap;
    private AMap aMap;
    private RelativeLayout mRlHead;
    private RecyclerView mRecyclerView;
    private RelativeLayout mRlFood;
    private VehicleAdapter vehicleAdapter;
    private List<ProviceVehicle> proviceVehicleList;
    private TextView tvDataTime;


    public static void startUI(Context context) {
        Intent intent = new Intent(context, TransportationActivity.class);
        context.startActivity(intent);
    }

    @Override
    protected void onCreate(@Nullable Bundle savedInstanceState) {
        super.onCreate(savedInstanceState);
        setContentView(R.layout.transportation_activity);
        UtilStatus.initStatus(this, Color.WHITE);
        initView();
        mMap.onCreate(savedInstanceState);// 此方法必须重写
        initListener();
        getViewModel().queryTransPortVehicleList();
    }

    private void initListener() {
        mAppToolber.setRightOnClickListener(view -> TransportSearchActivity.startUI(TransportationActivity.this));
        mRlFood.setOnClickListener(v -> {
            // 第一个可见位置
            int firstItem = mRecyclerView.getChildLayoutPosition(mRecyclerView.getChildAt(0));
            if (firstItem > 0) {
                mRecyclerView.scrollToPosition(firstItem - 1);
            }
        });
        mRlHead.setOnClickListener(v -> {
            // 最后一个可见位置
            if (proviceVehicleList != null && !proviceVehicleList.isEmpty()){
                int lastItem = mRecyclerView.getChildLayoutPosition(mRecyclerView.getChildAt(mRecyclerView.getChildCount() - 1));
                if (lastItem + 1 < proviceVehicleList.size()) {
                    mRecyclerView.scrollToPosition(lastItem + 1);
                }
            }
        });
    }

    @LiveDataMatch(tag = "查询运输中车辆成功")
    public void onQueryTransPortVehicleList(PageList<ProviceVehicle> pageList) {
        String time = "";
        proviceVehicleList = pageList.getRootArray();
        if (proviceVehicleList != null && proviceVehicleList.size() > 0) {
            ProviceVehicle proviceVehicle = proviceVehicleList.get(0);
            if (proviceVehicle != null) time = proviceVehicle.getTime();
        }
        if (TextUtils.isEmpty(time)) {
            tvDataTime.setVisibility(View.GONE);
        } else {
            tvDataTime.setVisibility(View.VISIBLE);
            tvDataTime.setText("数据时间：" + time);
        }

        vehicleAdapter.setNewData(pageList.getRootArray());
        ViewGroup.LayoutParams lp = mRecyclerView.getLayoutParams();
        if (proviceVehicleList.size() > 5) {
            lp.height = ResUtil.dp2px(62 * 5);
            mRecyclerView.setLayoutParams(lp);
        } else {
            mRlHead.setVisibility(View.INVISIBLE);
            mRlFood.setVisibility(View.INVISIBLE);
        }
        BitmapDescriptor bitmapDescriptor = BitmapDescriptorFactory.fromResource(R.drawable.gps_point);
        MultiPointOverlayOptions overlayOptions = new MultiPointOverlayOptions();
        overlayOptions.icon(bitmapDescriptor);
        overlayOptions.anchor(0.5f, 0.5f);
        final MultiPointOverlay multiPointOverlay = aMap.addMultiPointOverlay(overlayOptions);
        showProgressDialog();
        new Thread(() -> {
            List<MultiPointItem> list = new ArrayList<MultiPointItem>();
            for (int i = 0; i < proviceVehicleList.size(); i++) {
                if (isDestroy) {
                    //已经销毁地图了，退出循环
                    return;
                }
                String location = proviceVehicleList.get(i).getLocation();
                if (!TextUtils.isEmpty(location)){
                    String[] Latlong = location.split(",");
                    LatLng latLng = new LatLng(Double.valueOf(Latlong[1]), Double.valueOf(Latlong[0]), false);//保证经纬度没有问题的时候可以填false
                    MultiPointItem multiPointItem = new MultiPointItem(latLng);
                    list.add(multiPointItem);
                }
            }

            if (multiPointOverlay != null) {
                multiPointOverlay.setItems(list);
                multiPointOverlay.setEnable(true);
            }
            dissmissProgressDialog();
        }).start();
    }

    private void initView() {
        mAppToolber = findViewById(R.id.appToolber);
        mMap = findViewById(R.id.map);
        tvDataTime = findViewById(R.id.tv_data_time);
        mRlFood = findViewById(R.id.rl_food);
        mRlHead = findViewById(R.id.rl_head);
        mRecyclerView = findViewById(R.id.recyclerView);
        mRecyclerView.setLayoutManager(new LinearLayoutManager(this));
        vehicleAdapter = new VehicleAdapter();
        mRecyclerView.setAdapter(vehicleAdapter);
        vehicleAdapter.setOnItemClickListener(this);
        if (aMap == null) {
            aMap = mMap.getMap();
        }
        aMap.moveCamera(CameraUpdateFactory.zoomTo(3));
    }


    boolean isDestroy = false;


    private ProgressDialog progDialog = null;// 添加海量点时

    /**
     * 显示进度框
     */
    private void showProgressDialog() {
        if (progDialog == null)
            progDialog = new ProgressDialog(this);
        progDialog.setProgressStyle(ProgressDialog.STYLE_SPINNER);
        progDialog.setIndeterminate(false);
        progDialog.setCancelable(true);
        progDialog.setMessage("正在解析添加海量点中，请稍后...");
        progDialog.show();
    }

    /**
     * 隐藏进度框
     */
    private void dissmissProgressDialog() {
        if (progDialog != null) {
            progDialog.dismiss();
        }
    }

    /**
     * 方法必须重写
     */
    @Override
    protected void onResume() {
        super.onResume();
        mMap.onResume();
    }

    /**
     * 方法必须重写
     */
    @Override
    protected void onPause() {
        super.onPause();
        mMap.onPause();
        dissmissProgressDialog();
    }

    /**
     * 方法必须重写
     */
    @Override
    protected void onSaveInstanceState(Bundle outState) {
        super.onSaveInstanceState(outState);
        mMap.onSaveInstanceState(outState);
    }

    /**
     * 方法必须重写
     */
    @Override
    protected void onDestroy() {
        super.onDestroy();
        mMap.onDestroy();
        isDestroy = true;
    }

    @Override
    public void onItemClick(BaseQuickAdapter adapter, View view, int position) {
        ProviceVehicle proviceVehicle = (ProviceVehicle) adapter.getData().get(position);
        if (TextUtils.equals("最新",proviceVehicle.getProvince())){
            return;
        }else {
            TransportationBoundaryActivity.startUI(TransportationActivity.this, proviceVehicle);
        }

    }
}
