package com.zczy.cargo_owner.order.overdue.widget

import android.content.Context
import android.graphics.Color
import androidx.constraintlayout.widget.ConstraintLayout
import android.text.TextUtils
import android.util.AttributeSet
import android.view.LayoutInflater
import com.zczy.cargo_owner.order.R
import com.zczy.cargo_owner.order.overdue.req.OrderOverDueItem
import com.zczy.cargo_owner.order.overdue.req.formatSpecifyFlag
import com.zczy.cargo_owner.order.overdue.req.formatSpecifyFlagOne
import com.zczy.comm.CommServer
import com.zczy.comm.utils.ex.isTrue
import com.zczy.comm.utils.ex.setVisible
import com.zczy.comm.utils.getResColor
import kotlinx.android.synthetic.main.order_overdue_order_view.view.*

/** 功能描述:
 * <AUTHOR>
 * @company 中储智运科技股份有限公司
 * @copyright （版权）中储南京智慧物流科技有限公司所有
 */
class OrderOverdueOrderView : ConstraintLayout {

    constructor(context: Context) : super(context) {
        init()
    }

    constructor(context: Context, attrs: AttributeSet?) : super(context, attrs) {
        init()
    }

    constructor(context: Context, attrs: AttributeSet?, defStyleAttr: Int) : super(context, attrs, defStyleAttr) {
        init()
    }

    private fun init() {
        LayoutInflater.from(context).inflate(R.layout.order_overdue_order_view, this)
    }

    fun setData(item: OrderOverDueItem, isUndo: Boolean = false, ivCheckBlock: (isSelected: Boolean) -> Unit = {}) {
        ivCheck.setVisible(isUndo)
        ivCheck.isSelected = item.isCheck
        ivCheck.setOnClickListener {
            item.isCheck = !ivCheck.isSelected
            ivCheck.isSelected = item.isCheck
            ivCheckBlock(item.isCheck)
        }
        ivLTLOrderFlag.setVisible(item.noResponsibilityFlag.isTrue)
        iv_zhiya.setVisible(TextUtils.equals("2", item.businessSource))
        // 运单号:
        tv_order_id_value.text = item.orderId
        // 运单状态
        tv_order_overdue_deal_result_value.text = item.orderState
        tv_order_overdue_deal_result_title.setVisible(!isUndo)
        overdueState.setVisible(item.appealOverTimeFlag.isTrue)
        tv_order_overdue_deal_result_value.setVisible(!isUndo)
        // 逾期原因:
        tv_order_overdue_reason_value.text = item.remarks
        if (isUndo) {
            tv_order_overdue_reason_value.setTextColor(Color.parseColor("#FFFF602E"))
        } else {
            tv_order_overdue_reason_value.setTextColor(getResColor(R.color.text_66))
        }
        // 原合作运力范围:
        tv_order_overdue_yhzylfw_value.text = item.formatSpecifyFlag()
        tv_order_overdue_carrier_value1.text = item.formatSpecifyFlagOne()
        // 承运方:
        tv_order_overdue_carrier_value.text = item.carrierCustomerName
        // 承运方手机:
        tv_order_overdue_carrier_phone_value.text = item.carrierMobile
        // 车牌号:
        tv_order_overdue_plate_number_value.text = item.plateNumber
        // 逾期天数:
        tv_order_overdue_overdue_day_value.text = item.orderDate
        tv_order_overdue_limit_publish_value.text = if(TextUtils.equals("1", item.limitPublishFlag)) {
            "是"
        } else {
            "否"
        }
        // 倒计时
        if (isUndo) {
            tv_order_overdue_rxtime_title.setVisible(true)
            tv_order_overdue_rxtime_value.setVisible(true)
            // 原合作运力范围承运方（0-否，1-是）
            if (item.specifyFlag.isTrue) {
                tv_order_overdue_rxtime_title.text = "禁止挂单倒计时"
                // 禁止挂单倒计时:
                tv_order_overdue_rxtime_value.startInterval(
                    CommServer.getCacheServer().systemTime,
                    item.countDown,
                    "",
                    ""
                )
            } else {
                if (item.remarks == "未回单确认") {
                    tv_order_overdue_rxtime_title.text = "限制发单倒计时"
                }else{
                    tv_order_overdue_rxtime_title.text = "运单终止倒计时"
                }
                if (item.remarks == "未发货确认") {
                    // 运单终止倒计时:
                    tv_order_overdue_rxtime_value.startInterval(
                        CommServer.getCacheServer().systemTime,
                        item.countDown,
                        "",
                        ""
                    )
                } else {
                    tv_order_overdue_rxtime_title.setVisible(false)
                    tv_order_overdue_rxtime_value.setVisible(false)
                }
            }
        } else {
            tv_order_overdue_rxtime_title.setVisible(false)
            tv_order_overdue_rxtime_value.setVisible(false)
        }
    }
}