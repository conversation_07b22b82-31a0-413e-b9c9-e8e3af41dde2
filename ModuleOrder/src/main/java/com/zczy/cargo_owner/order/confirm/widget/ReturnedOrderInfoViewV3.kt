package com.zczy.cargo_owner.order.confirm.widget

import android.annotation.SuppressLint
import android.content.Context
import android.graphics.Color
import android.text.TextUtils
import android.util.AttributeSet
import android.util.TypedValue
import androidx.constraintlayout.widget.ConstraintLayout
import androidx.core.content.ContextCompat
import androidx.recyclerview.widget.LinearLayoutManager
import com.sfh.lib.rx.ui.UtilRxView
import com.zczy.cargo_owner.order.R
import com.zczy.cargo_owner.order.confirm.adapter.UploadReturnedOrderPhotoAdapterV1
import com.zczy.cargo_owner.order.confirm.bean.RspQueryTrnSingleReceiptOrderData
import com.zczy.cargo_owner.order.confirm.bean.differenceV1
import com.zczy.cargo_owner.order.confirm.bean.getDeliverCargoCategoryStr
import com.zczy.cargo_owner.order.confirm.bean.showCarrierReceiveRemark
import com.zczy.cargo_owner.order.confirm.bean.showReceiveTime
import com.zczy.comm.data.entity.EImage
import com.zczy.comm.utils.ex.getFormatTime
import com.zczy.comm.utils.ex.isTrue
import com.zczy.comm.utils.ex.setVisible
import com.zczy.comm.widget.inputv2.InputViewClick
import com.zczy.comm.widget.pickerview.TimePickerUtil
import kotlinx.android.synthetic.main.returned_order_info_view_v3.view.*

/**
 *  desc: 回单确认-回单信息
 *  user: ssp
 *  time: 2025/3/25 13:53
 */
class ReturnedOrderInfoViewV3 @JvmOverloads constructor(
    context: Context,
    attrs: AttributeSet? = null,
    defStyleAttr: Int = 0
) : ConstraintLayout(context, attrs, defStyleAttr) {

    private var mReturnedOrderDetailBean: RspQueryTrnSingleReceiptOrderData? = null
    private val mUploadReturnedOrderPhotoAdapterV12 = UploadReturnedOrderPhotoAdapterV1()

    init {
        setBackgroundColor(Color.WHITE)
        inflate(context, R.layout.returned_order_info_view_v3, this)
        initView()
    }

    private fun initView() {
        //切换原图
        UtilRxView.clicks(ivReceiptDocumentPhotoSwitch, 500) {
            val driverReceiveProofPicJsonArr = mReturnedOrderDetailBean?.driverReceiveProofPicJsonArr ?: emptyList()
            val driverReceivePhotoPicJsonArr = mReturnedOrderDetailBean?.driverReceivePhotoPicJsonArr ?: emptyList()
            when (ivReceiptDocumentPhotoSwitch.text) {
                "切换原图" -> {
                    //带水印图片 切换成原图
                    driverReceiveProofPicJsonArr.mapNotNull { it.picOrgUrl.takeIf { s -> s.isNotEmpty() }?.let { url -> EImage(imageId = url) } }.let { list ->
                        isvReturnImage.imgList = ArrayList(list)
                    }
                    driverReceivePhotoPicJsonArr.mapNotNull { it.picOrgUrl.takeIf { s -> s.isNotEmpty() }?.let { url -> EImage(imageId = url) } }.let { list ->
                        isvReturnImageV1.imgList = ArrayList(list)
                    }
                    ivReceiptDocumentPhotoSwitch.text = "切换默认"
                }

                "切换默认" -> {
                    //原图 切换成带水印图片
                    driverReceiveProofPicJsonArr.mapNotNull { it.picUrl.takeIf { s -> s.isNotEmpty() }?.let { url -> EImage(imageId = url) } }.let { list ->
                        isvReturnImage.imgList = ArrayList(list)
                    }
                    driverReceivePhotoPicJsonArr.mapNotNull { it.picUrl.takeIf { s -> s.isNotEmpty() }?.let { url -> EImage(imageId = url) } }.let { list ->
                        isvReturnImageV1.imgList = ArrayList(list)
                    }
                    ivReceiptDocumentPhotoSwitch.text = "切换原图"
                }
            }
        }
        //收货货物明细
        mUploadReturnedOrderPhotoAdapterV12.canEdit = false
        mUploadReturnedOrderPhotoAdapterV12.str = UploadReturnedOrderPhotoAdapterV1.STR_2
        isReceiptRecyclerViewCargo.apply {
            layoutManager = LinearLayoutManager(context)
            adapter = mUploadReturnedOrderPhotoAdapterV12
        }
        //发货磅单出场时间
        inputOutStageTime.let {
            it.tvTitle.apply {
                setTextColor(ContextCompat.getColor(context, R.color.color_666))
                setTextSize(TypedValue.COMPLEX_UNIT_SP, 13F)
            }
            it.tvContent.apply {
                setTextColor(ContextCompat.getColor(context, R.color.text_33))
                setTextSize(TypedValue.COMPLEX_UNIT_SP, 13F)
            }
            it.setListener(object : InputViewClick.Listener() {
                override fun onClick(viewId: Int, view: InputViewClick, content: String) {
                    TimePickerUtil.showComm(context, "选择出厂时间", null) { date ->
                        inputOutStageTime.content = date.getFormatTime("yyyy-MM-dd HH:mm:00")
                    }
                }
            })
        }
    }

    @SuppressLint("SetTextI18n")
    fun setData(data: RspQueryTrnSingleReceiptOrderData?) {
        this.mReturnedOrderDetailBean = data
        data?.let { detail ->
            //收货数量
            tvReceiptNum.text = detail.receiveWeight + detail.getDeliverCargoCategoryStr()
            //司机是否修改
            when (detail.receiveUpdateDeliverWeightFlag) {
                "1" -> {
                    tvReceiptCarrierAlreadyEdited.setVisible(true)
                    tvReceiptCarrierAlreadyEdited.text = "司机已修改"
                }

                "3" -> {
                    tvReceiptCarrierAlreadyEdited.setVisible(true)
                    tvReceiptCarrierAlreadyEdited.text = "平台修改"
                }

                else -> {
                    tvReceiptCarrierAlreadyEdited.setVisible(false)
                }
            }
            //发货磅单出场时间
            clOutStageTime.setVisible(detail.isShowOutTime.isTrue)
            inputOutStageTime.content = detail.outTime ?: ""
            //司机单据照片
            if (detail.driverReceiveProofPicJsonArr.isNullOrEmpty()) {
                isvReturnImage.setVisible(false)
                isvReturnImageRl.setVisible(false)
                tvReceiptDocumentPhoto.setVisible(false)
            } else {
                tvReceiptDocumentPhoto.setVisible(true)
                isvReturnImage.setVisible(true)
                isvReturnImageRl.setVisible(true)
                isvReturnImage.imgList = ArrayList(detail.driverReceiveProofPicJsonArr.map { EImage(imageId = it.picUrl) })
            }
            //司机上传的证明材料
            if (detail.examinePicUrlArr.isNullOrEmpty()) {
                tvexaminePicUrlArr.setVisible(false)
                isexaminePicUrlArr.setVisible(false)
                isexaminePicUrlArrImage.setVisible(false)
            } else {
                tvexaminePicUrlArr.setVisible(true)
                isexaminePicUrlArr.setVisible(true)
                isexaminePicUrlArrImage.setVisible(true)
                isexaminePicUrlArrImage.canSelect = false
                isexaminePicUrlArrImage.imgList = ArrayList(detail.examinePicUrlArr.map { EImage(imageId = it.picUrl) })
            }
            //货主上传单据照片
            val consignorReveivePicUrlArr = detail.consignorReveivePicUrlArr
            if (consignorReveivePicUrlArr.isNullOrEmpty()) {
                tvReceiptDocumentPhotoV2.setVisible(false)
                isReceiptReturnImageV2.setVisible(false)
                isReceiptReturnImageRlV2.setVisible(false)
            } else {
                tvReceiptDocumentPhotoV2.setVisible(true)
                isReceiptReturnImageV2.setVisible(true)
                isReceiptReturnImageRlV2.setVisible(true)
                val list = arrayListOf<EImage>()
                consignorReveivePicUrlArr.forEach { item ->
                    list.add(EImage(imageId = item.picUrl))
                }
                isReceiptReturnImageV2.imgList = list
            }
            //运单照片
            if (detail.driverReceivePhotoPicJsonArr.isNullOrEmpty()) {
                isvReturnImageV1.setVisible(false)
                isvReturnImageRlV1.setVisible(false)
                tvReceiptDocumentPhotoV1.setVisible(false)
            } else {
                isvReturnImageV1.setVisible(true)
                isvReturnImageRlV1.setVisible(true)
                tvReceiptDocumentPhotoV1.setVisible(true)
                isvReturnImageV1.imgList = ArrayList(detail.driverReceivePhotoPicJsonArr.map { EImage(imageId = it.picUrl) })
                ivReceiptDocumentPhotoSwitch.tag = true
            }
            //回单信息-上传时间(确认收货时间)
            isvReturnTime.text = detail.showReceiveTime()
            //差额
            jmyl_warning.setVisible(detail.differenceV1().isNotEmpty())
            tv_warning_jmyl.text = detail.differenceV1()
            //收货备注
            isvReturnRemark.text = detail.showCarrierReceiveRemark()
            //货物明细
            mUploadReturnedOrderPhotoAdapterV12.setNewData(detail.receiveCargoArray)
        }
    }

}