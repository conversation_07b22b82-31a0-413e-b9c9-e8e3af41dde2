package com.zczy.cargo_owner.order.overdue

import android.content.Context
import android.content.Intent
import android.os.Bundle
import androidx.fragment.app.Fragment
import com.sfh.lib.mvvm.service.BaseViewModel
import com.zczy.cargo_owner.order.R
import com.zczy.cargo_owner.order.overdue.fragment.OrderOverdueUndoFragment
import com.zczy.comm.ui.BaseActivity
import kotlinx.android.synthetic.main.order_consignor_confirm_main_activity.appToolber

/**
 * PS: 逾期运单管理
 * Created by sdx on 2019-06-24.
 */
class OrderOverdueMainActivity : BaseActivity<BaseViewModel>() {
    companion object {
        const val DEAL_TYPE = "dealType"
        const val DEAL_TYPE_1 = "1"
        const val DEAL_TYPE_3 = "3"

        @JvmStatic
        fun start(context: Context?) {
            context ?: return
            val intent = Intent(context, OrderOverdueMainActivity::class.java)
            context.startActivity(intent)
        }
    }

    override fun getLayout(): Int = R.layout.order_overdue_main_activity

    override fun bindView(bundle: Bundle?) {
        initCommonTabLayout()
        appToolber.setRightOnClickListener {
            //跳转高级搜索页
            OrderOverdueHigeSearchActivity.start(this, 0)
        }
    }

    override fun initData() {
    }

    private fun initCommonTabLayout() {
        val fragments = ArrayList<Fragment>()
        fragments.add(OrderOverdueUndoFragment.newInstance(context = this@OrderOverdueMainActivity, dealType = DEAL_TYPE_1))
        // 获取FragmentManager并开始事务
        val fragmentManager = supportFragmentManager
        val fragmentTransaction = fragmentManager.beginTransaction()
        // 将Fragment添加到指定的FrameLayout容器中（假设id为frame_layout）
        fragmentTransaction.add(R.id.frame_layout, fragments[0])
        // 提交事务
        fragmentTransaction.commit()
    }
}