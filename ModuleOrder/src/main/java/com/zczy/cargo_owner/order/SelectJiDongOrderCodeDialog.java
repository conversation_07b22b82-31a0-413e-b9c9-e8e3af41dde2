package com.zczy.cargo_owner.order;

import android.os.Bundle;
import androidx.annotation.NonNull;
import androidx.annotation.Nullable;
import androidx.recyclerview.widget.LinearLayoutManager;
import androidx.recyclerview.widget.RecyclerView;
import android.text.TextUtils;
import android.view.Gravity;
import android.view.View;
import android.view.Window;
import android.view.WindowManager;
import android.widget.ImageView;
import android.widget.TextView;

import com.zczy.cargo_owner.order.entity.EJiDongOrderCode;
import com.zczy.comm.http.entity.PageList;
import com.zczy.comm.ui.BaseDialog;

/*=============================================================================================
 * 功能描述:冀东--自定义编号列表dialog
 *---------------------------------------------------------------------------------------------
 *  涉及业务 & 更新说明:
 *
 *
 *--------------------------------------------------------------------------------------------
 *  <AUTHOR>  on  2022/4/1
 *  @Company    中储智运科技股份有限公司
 *  @Copyright （版权）中储南京智慧物流科技有限公司所有                                                                                        *
 *=============================================================================================*/
public class SelectJiDongOrderCodeDialog extends BaseDialog implements View.OnClickListener {
    private SelectJiDongOrderCodeAdapter mAdapter;
    private PageList<EJiDongOrderCode> pageList;
    private static TextView tv_commit;
    private String orderId;
    // 列表页面弹出还是设置自定义编号页面弹出标识 1列表页面 2设置自定义编号页面
    private int flag = 0;
    // 是否是批量设置
    private boolean isBatch = false;
    // 选择的年份
    String periodFlag;

    public static void setBtnTextToNext(boolean b) {
        if (b) {
            tv_commit.setText("下一步");
        } else {
            tv_commit.setText("确定提交");
        }
    }

    public interface SelectJiDongOrderCodeDialogListener {
        // 选中条目
        void selectItem(String posStr, boolean isBatch);

        // 未选中
        void noSelect();

        // 更新
        void isUpdate(boolean b);
    }

    SelectJiDongOrderCodeDialogListener listener;

    public SelectJiDongOrderCodeDialog setListener(SelectJiDongOrderCodeDialogListener listener) {
        this.listener = listener;
        return this;
    }

    public SelectJiDongOrderCodeDialog setData(PageList<EJiDongOrderCode> list, String orderId, int flag, boolean isBatch, String periodFlag) {
        this.pageList = list;
        this.orderId = orderId;
        this.flag = flag;
        this.isBatch = isBatch;
        this.periodFlag = periodFlag;
        return this;
    }

    public SelectJiDongOrderCodeDialog setData(PageList<EJiDongOrderCode> list, String orderId, int flag, boolean isBatch) {
        this.pageList = list;
        this.orderId = orderId;
        this.flag = flag;
        this.isBatch = isBatch;
        return this;
    }

    @Override
    protected void bindView(@NonNull View view, @Nullable Bundle bundle) {
        TextView tv_update = view.findViewById(R.id.tv_update);
        ImageView iv_dismiss = view.findViewById(R.id.iv_dismiss);
        tv_commit = view.findViewById(R.id.tv_commit);
        RecyclerView recycleview = view.findViewById(R.id.recycleview);
        recycleview.setLayoutManager(new LinearLayoutManager(getContext()));//设置布局管理器 为线性布局
        iv_dismiss.setOnClickListener(this);
        tv_commit.setOnClickListener(this);
        tv_update.setOnClickListener(this);
        mAdapter = new SelectJiDongOrderCodeAdapter(getContext(), listener);
        recycleview.setAdapter(mAdapter);
        mAdapter.setDataSource(pageList);

    }

    @Override
    protected String getDialogTag() {
        return null;
    }

    @Override
    protected int getDialogLayout() {
        return R.layout.dialog_jidong_order_code;
    }

    @Override
    protected int getDialogStyle() {
        return R.style.BottomDialog;
    }

    @Override
    public void onActivityCreated(@Nullable Bundle savedInstanceState) {
        super.onActivityCreated(savedInstanceState);
    }

    @Override
    public void onViewCreated(@NonNull View view, @Nullable Bundle savedInstanceState) {
        super.onViewCreated(view, savedInstanceState);
        Window window = getDialog().getWindow();
        window.getDecorView().setPadding(0, 0, 0, 0); //消除边距
        window.setWindowAnimations(R.style.BottomDialog_Animation);
        WindowManager.LayoutParams lp = window.getAttributes();
        lp.width = WindowManager.LayoutParams.MATCH_PARENT;
        lp.height = WindowManager.LayoutParams.WRAP_CONTENT;
        //设置dialog的位置在底部
        lp.gravity = Gravity.BOTTOM;
        window.setAttributes(lp);
        //第一个参数是宽度-1代表默认占满；第二个参数高度-2默认自适应高度（这两个参数也可以直接设置固定宽高）。
        window.setLayout(WindowManager.LayoutParams.MATCH_PARENT, WindowManager.LayoutParams.WRAP_CONTENT);
    }

    @Override
    public void onClick(View v) {
        if (v.getId() == R.id.tv_commit) {
            int position = mAdapter.getSelectedPos();
            if (position == -1) {
                listener.noSelect();
                return;
            }
            String s = tv_commit.getText().toString();
            if (flag == 1) {
                if (TextUtils.equals("下一步", s)) {
                    // 下一步
                    JidongEditSelfCommentActivity.start(getContext(), orderId, isBatch, periodFlag);
                    dismiss();

                } else {
                    //确定提交
                    String selfComment = pageList.getRootArray().get(position).getOrderCode();
                    listener.selectItem(selfComment, isBatch);
                }
            } else if (flag == 2) {
                String selfComment = pageList.getRootArray().get(position).getOrderCode();
                listener.selectItem(selfComment, isBatch);
                dismiss();
            }

        } else if (v.getId() == R.id.iv_dismiss) {
            getDialog().dismiss();
        } else if (v.getId() == R.id.tv_update) {
            listener.isUpdate(true);
//            EJiDongOrderCodeHandle eJiDongOrderCodeHandle = new EJiDongOrderCodeHandle();
//            eJiDongOrderCodeHandle.setHandel("update");
//            RxBusEventManager.postEvent(eJiDongOrderCodeHandle);

        }
    }
}
