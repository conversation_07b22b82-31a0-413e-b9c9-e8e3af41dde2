package com.zczy.cargo_owner.order.transport;

import android.content.Context;
import android.content.Intent;
import android.content.SharedPreferences;
import android.os.Bundle;
import androidx.annotation.Nullable;
import androidx.recyclerview.widget.LinearLayoutManager;
import androidx.recyclerview.widget.RecyclerView;
import android.text.TextUtils;
import android.view.LayoutInflater;
import android.view.View;
import android.widget.RelativeLayout;
import android.widget.TextView;

import com.zczy.cargo_owner.order.R;
import com.zczy.cargo_owner.order.transport.adapter.SearchHistoryAdapter;
import com.zczy.cargo_owner.order.transport.bean.SearchRecord;
import com.zczy.cargo_owner.order.transport.wight.TransportSearchLayout;
import com.zczy.comm.CommServer;
import com.zczy.comm.data.entity.ELogin;
import com.zczy.comm.ui.BaseActivity;
import com.zczy.comm.utils.ResUtil;
import com.zczy.comm.widget.itemdecoration.SpaceItemDecoration;
import com.zhy.view.flowlayout.FlowLayout;
import com.zhy.view.flowlayout.TagAdapter;
import com.zhy.view.flowlayout.TagFlowLayout;

import java.util.ArrayList;
import java.util.Collections;
import java.util.Comparator;
import java.util.HashMap;
import java.util.List;
import java.util.Map;
import java.util.Set;

/**
 * 运输中搜索
 */
public class TransportSearchActivity extends BaseActivity {
    private TransportSearchLayout mSearchLayout;
    private TagFlowLayout mFlowlayout;
    /**
     * 本地存储
     */
    private SharedPreferences preRecord;
    private SharedPreferences.Editor editorRecord;
    private Map<String, String> maprecord;
    private List<SearchRecord> recordList = new ArrayList<SearchRecord>();
    private RecyclerView recyclerView;
    private SearchHistoryAdapter searchHistoryAdapter;
    private RelativeLayout rlRoot;
    private FlowAdatpter flowAdatpter;

    public static void startUI(Context context) {
        Intent intent = new Intent(context, TransportSearchActivity.class);
        context.startActivity(intent);
    }


    @Override
    protected int getLayout() {
        return R.layout.transport_search_activity;
    }

    @Override
    protected void bindView(@Nullable Bundle bundle) {
        ELogin login = CommServer.getUserServer().getLogin();
        if (login != null) {
            preRecord = this.getSharedPreferences(login.getMobile(),
                    Context.MODE_PRIVATE);
            editorRecord = preRecord.edit();
        }
        initView();
    }

    @Override
    protected void initData() {
        searchHistoryAdapter = new SearchHistoryAdapter();
        recyclerView.setAdapter(searchHistoryAdapter);
        flowAdatpter = new FlowAdatpter(recordList);
        mFlowlayout.setAdapter(flowAdatpter);
        searchHistoryAdapter.setOnItemClickListener((adapter, view, position) -> {
            SearchRecord searchRecord = (SearchRecord) adapter.getItem(position);
            TransportsListActivity.startUI(TransportSearchActivity.this,
                    searchRecord.getRname(),"");
        });
        mFlowlayout.setOnTagClickListener((view, position, parent) -> {
            rlRoot.setVisibility(View.GONE);
            TransportsListActivity.startUI(TransportSearchActivity.this,
                    recordList.get(position).getRname(),"");
            return true;
        });
        mFlowlayout.setOnSelectListener(selectPosSet -> {
//                getActivity().setTitle("choose:" + selectPosSet.toString());
        });
        history();
    }

    private void initView() {
        mSearchLayout = findViewById(R.id.searchLayout);
        mSearchLayout.setSearchEtHintText("请输入运单号/车牌号");
        mSearchLayout.setOnSearchListener(seachContentListener);
        mFlowlayout = findViewById(R.id.flowlayout);
        rlRoot = findViewById(R.id.rl_root);
        recyclerView = findViewById(R.id.recycler_view);
        recyclerView.setLayoutManager(new LinearLayoutManager(this));
        recyclerView.setHasFixedSize(true);
        recyclerView.setNestedScrollingEnabled(false);
        recyclerView.setFocusable(false);
        recyclerView.addItemDecoration(new SpaceItemDecoration(ResUtil.dp2px(1)));
    }

    private class FlowAdatpter extends TagAdapter<SearchRecord> {

        public FlowAdatpter(List<SearchRecord> datas) {
            super(datas);
        }

        @Override
        public View getView(FlowLayout parent, int position, SearchRecord searchRecord) {
            TextView tv = (TextView) LayoutInflater.from(TransportSearchActivity.this).inflate(R.layout.search_layout_item,
                    mFlowlayout, false);
            tv.setText(searchRecord.getRname());
            return tv;
        }
    }

    private TransportSearchLayout.SeachContentListener seachContentListener = new TransportSearchLayout.SeachContentListener() {
        @Override
        public void doSearchListener(String searchContent) {
            editorRecord.putString(searchContent,
                    "" + System.currentTimeMillis());
            editorRecord.commit();
            history();
            rlRoot.setVisibility(View.GONE);
            TransportsListActivity.startUI(TransportSearchActivity.this, searchContent,"");
        }

        @Override
        public void doCancel() {
            rlRoot.setVisibility(View.GONE);
            finish();
        }

        @Override
        public void onTextChanged(String charSequence) {
            if (!TextUtils.isEmpty(charSequence)) {
                rlRoot.setVisibility(View.VISIBLE);
                List<SearchRecord> searchRecords = new ArrayList<>();
                for (SearchRecord searchRecord : recordList) {
                    if (searchRecord.getRname().contains(charSequence)) {
                        SearchRecord record = new SearchRecord();
                        record.setRname(searchRecord.getRname());
                        searchRecords.add(record);
                    }
                }
                searchHistoryAdapter.mKey = charSequence;
                searchHistoryAdapter.setNewData(searchRecords);
                history();
            }
        }
    };

    private void history() {
        recordList.clear();
        // TODO Auto-generated method stub
        maprecord = new HashMap<>();
        maprecord = (Map<String, String>) preRecord.getAll();
        Set set = maprecord.entrySet();
        for (Object o : set) {
            Map.Entry<String, String> entry = (Map.Entry<String, String>) o;
            SearchRecord record = new SearchRecord();
            record.setRid(entry.getValue());
            record.setRname(entry.getKey());
            recordList.add(record);
        }
        Collections.sort(recordList, new Comparator<SearchRecord>() {
            @Override
            public int compare(SearchRecord sr1, SearchRecord sr2) {
                return sr2.getRid().compareTo(sr1.getRid());
            }
        });
        if (recordList.size() > 6) {
            editorRecord.remove(recordList.get(recordList.size() - 1).getRname());
            editorRecord.commit();
        }
        flowAdatpter.notifyDataChanged();
    }
}
