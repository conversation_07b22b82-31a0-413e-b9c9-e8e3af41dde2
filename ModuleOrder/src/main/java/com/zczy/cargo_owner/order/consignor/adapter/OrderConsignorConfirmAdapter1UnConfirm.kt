package com.zczy.cargo_owner.order.consignor.adapter

import android.view.View
import android.widget.ImageView
import com.chad.library.adapter.base.BaseQuickAdapter
import com.chad.library.adapter.base.BaseViewHolder
import com.zczy.cargo_owner.libcomm.config.SubUserAuthUtils
import com.zczy.cargo_owner.libcomm.utils.getChildPermission
import com.zczy.cargo_owner.order.R
import com.zczy.cargo_owner.order.consignor.req.Rsp1QueryConsignorOrderStateOfMobile
import com.zczy.cargo_owner.order.consignor.req.showOrderId
import com.zczy.cargo_owner.order.consignor.widget.OrderConsignorConfirmOrderView

class OrderConsignorConfirmAdapter1UnConfirm : BaseQuickAdapter<Rsp1QueryConsignorOrderStateOfMobile, BaseViewHolder>
    (R.layout.order_consignor_confirm_item_1_unconfirm) {

    var selectData: List<Rsp1QueryConsignorOrderStateOfMobile>? = null

    override fun convert(helper: BaseViewHolder, item: Rsp1QueryConsignorOrderStateOfMobile) {
        //  子账号权限 打回
        val hasReturnPermission = SubUserAuthUtils.get().sgoRefuse.getChildPermission().isEmpty()
        //  子账号权限 确认
        val hasConfirmPermission = SubUserAuthUtils.get().sgoSendGoodOk.getChildPermission().isEmpty()
        val checkView = helper.getView<ImageView>(R.id.check_order)
        checkView.isSelected = selectData?.contains(item) == true
        // order id
        helper.setText(R.id.tv_order_id, item.showOrderId())

        // order
        val viewOrder = helper.getView<OrderConsignorConfirmOrderView>(R.id.view_order)
        viewOrder.setData(item)
        helper
            // 复制
            .addOnClickListener(R.id.tv_order_copy)
            // 确认
            .addOnClickListener(R.id.tv_confirm)
            // 打回
            .addOnClickListener(R.id.tv_reject)
            // 运单号
            .addOnClickListener(R.id.tv_order_id)
            .addOnClickListener(R.id.tv_look)
            // 子账号权限 打回
            .setGone(R.id.tv_reject, hasReturnPermission)
            // 子账号权限 确认
            .setGone(R.id.tv_confirm, hasConfirmPermission)
    }
}
