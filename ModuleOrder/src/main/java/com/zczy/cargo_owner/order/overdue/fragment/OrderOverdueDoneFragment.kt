package com.zczy.cargo_owner.order.overdue.fragment

import android.content.Context
import android.os.Bundle
import androidx.fragment.app.Fragment
import android.text.TextUtils
import android.view.View
import com.chad.library.adapter.base.BaseQuickAdapter
import com.chad.library.adapter.base.listener.OnItemClickListener
import com.sfh.lib.mvvm.annotation.LiveDataMatch
import com.zczy.cargo_owner.order.R
import com.zczy.cargo_owner.order.confirm.ReturnedOrderConfirmListActivity
import com.zczy.cargo_owner.order.detail.WaybillDetailStatueActivity
import com.zczy.cargo_owner.order.overdue.OrderOverdueMainActivity.Companion.DEAL_TYPE
import com.zczy.cargo_owner.order.overdue.adapter.OrderOverdueDoneAdapter
import com.zczy.cargo_owner.order.overdue.model.OrderOverdueFragmentModel
import com.zczy.cargo_owner.order.overdue.req.OrderOverDueItem
import com.zczy.cargo_owner.order.settlement.SettlementApplicationListActivity
import com.zczy.cargo_owner.order.violate.OrderViolateListActivity
import com.zczy.comm.http.entity.PageList
import com.zczy.comm.ui.BaseFragment
import com.zczy.comm.utils.dp2px
import com.zczy.comm.widget.pulltorefresh.CommEmptyView
import com.zczy.comm.widget.pulltorefresh.OnLoadingListener
import kotlinx.android.synthetic.main.order_common_fragment.*

/**
 * PS: 逾期运单管理 处理完成
 * Created by sdx on 2019-06-24.
 */
class OrderOverdueDoneFragment : BaseFragment<OrderOverdueFragmentModel>() {

    private val mAdapter = OrderOverdueDoneAdapter()
    private val dealType by lazy { arguments?.getString(DEAL_TYPE) ?: "" }

    companion object {
        @JvmStatic
        fun newInstance(context: Context, dealType: String): OrderOverdueDoneFragment {
            val bundle = Bundle()
            bundle.putString(DEAL_TYPE, dealType)
            return instantiate(context, OrderOverdueDoneFragment::class.java.name, bundle) as OrderOverdueDoneFragment
        }
    }

    override fun getLayout(): Int = R.layout.order_common_fragment

    override fun bindView(view: View, bundle: Bundle?) {
        val emptyView = CommEmptyView.creatorDef(context)
        swipe_refresh_more_layout.apply {
            setAdapter(mAdapter, true)
            setEmptyView(emptyView)
            addItemDecorationSize(dp2px(7f))
            addOnItemListener(onItemClickListener)
            setOnLoadListener2 { nowPage ->
                viewModel?.getDoneListInfo(nowPage = nowPage, dealType = dealType)
            }
        }
    }

    override fun initData() {
        swipe_refresh_more_layout.onAutoRefresh()
    }

    private val onItemClickListener = object : OnItemClickListener() {
        override fun onSimpleItemClick(adapter: BaseQuickAdapter<*, *>, view: View, position: Int) {
            val item = adapter.getItem(position)
            if (item is OrderOverDueItem) {
            }
        }

        override fun onItemChildClick(adapter: BaseQuickAdapter<*, *>, view: View, position: Int) {
            super.onItemChildClick(adapter, view, position)
            val item = adapter.getItem(position)
            if (item is OrderOverDueItem) {
                when (view.id) {
                    // 进入详情
                    R.id.overdue_order_view -> {
                        WaybillDetailStatueActivity.start(context, item.orderId)
                    }
                    // 违约记录
                    R.id.btn_history -> {
                        OrderViolateListActivity.startNoAdd(activity, item.orderId)
                    }

                    R.id.goDoing -> {
                        if (TextUtils.equals(item.consignorState, "7") && TextUtils.equals(item.backStatus, "3")) {
                            //跳转回单确认
                            ReturnedOrderConfirmListActivity.start(context, 0, "")
                        } else {
                            //跳转结算申请
                            SettlementApplicationListActivity.start(context, 0, "")
                        }
                    }
                }
            }
        }
    }

    @LiveDataMatch
    open fun onGetDoneListInfo(data: PageList<OrderOverDueItem>?) {
        swipe_refresh_more_layout.onRefreshCompale(data)
    }

}