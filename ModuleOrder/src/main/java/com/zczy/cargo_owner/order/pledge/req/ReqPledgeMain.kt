package com.zczy.cargo_owner.order.pledge.req

import android.content.Context
import androidx.core.content.ContextCompat
import android.text.TextUtils
import com.chad.library.adapter.base.entity.MultiItemEntity
import com.zczy.cargo_owner.order.R
import com.zczy.cargo_owner.order.pledge.ui.PledgeMainFragment
import com.zczy.comm.http.entity.BaseNewRequest
import com.zczy.comm.http.entity.BaseRsp
import com.zczy.comm.http.entity.PageList
import com.zczy.comm.utils.ex.toDoubleRoundDown

/**
 * 功能描述: 押金列表
 * <AUTHOR>
 * @date 2022/11/11-14:57
 */

class ReqPledgeMain(
    var nowPage: Int = 1,
    var pageSize: Int = 10,
    var takeBackFlag: String? = null,   //0待追回 1已追回
    var searchContent: String? = null,  //搜索项
) : BaseNewRequest<BaseRsp<PageList<RspPledgeMain>>>("oms-app/consignor/deposit/depositManageList")

data class RspPledgeMain(
    var allCargoName: String = "",   //  货物总名称
    var carrierMobile: String = "",  //  司机电话号码
    var carrierName: String = "",    //  司机名
    var consigorName: String = "",   //	货主名
    var deliverCity: String = "",    //  目的地市
    var deliverDis: String = "",    //  目的区
    var despatchCity: String = "",   //  启运地市
    var despatchDis: String = "",   //  启运区
    var orderId: String = "",    //  订单id
    var applyState: String = "",  // 0:待申请; 1:平台介入中; 2:已驳回; 3:待承运方同意; 4:待结算 5:已追回
    var vehicleNum: String = "", //  车牌号
    var receiptMoney: String = "", //  押金总额
    var consignorDeposit: String = "", //  货主索要金额
    var LTLOrderFlag: String = "", //零担标识 1 是
) : MultiItemEntity {
    var selected: Boolean = false   //  本地维护是否选中
    override fun getItemType(): Int = when (applyState) {
        "0" -> {
            PledgeMainFragment.TYPE_0
        }
        else -> {
            PledgeMainFragment.TYPE_1
        }
    }
}

fun RspPledgeMain.showReceiptMoney(): Double {
    return if (TextUtils.isEmpty(receiptMoney)) {
        0.00
    } else {
        receiptMoney.toDoubleRoundDown(2)
    }
}

fun RspPledgeMain.showReceiptMoneyV1(): String {
    return if (receiptMoney.isEmpty()) {
        "押金总额：0元"
    } else {
        "押金总额：$receiptMoney" + "元"
    }
}

fun RspPledgeMain.showConsignorDeposit(): String {
    return if (consignorDeposit.isEmpty()) {
        "货主扣款：0元"
    } else {
        "货主扣款：$consignorDeposit" + "元"
    }
}

fun RspPledgeMain.showColorResId(context: Context): Int {
    return when (applyState) {
        "2" -> {
            ContextCompat.getColor(context, R.color.color_FF602E)
        }
        "5" -> {
            ContextCompat.getColor(context, R.color.color_64C06C)
        }
        else -> {
            ContextCompat.getColor(context, R.color.color_F5A623)
        }
    }
}

fun RspPledgeMain.showApplyState(): String {
    //0:待申请; 1:平台介入中; 2:已驳回; 3:待承运方同意; 4:待结算 5:已追回
    return when (applyState) {
        "0" -> {
            "待申请"
        }
        "1" -> {
            "平台介入中"
        }
        "2" -> {
            "已驳回"
        }
        "3" -> {
            "待承运方同意"
        }
        "4" -> {
            "待结算"
        }
        "5" -> {
            "已追回"
        }
        else -> {
            ""
        }
    }
}

fun RspPledgeMain.getCyfStr(): String {

    return when {
        TextUtils.isEmpty(carrierName) -> {
            "承运方：--"
        }
        else -> {
            "承运方：$carrierName"
        }
    }
}

fun RspPledgeMain.getVehicleStr(): String {

    return when {
        TextUtils.isEmpty(vehicleNum) -> {
            "车牌号：--"
        }
        else -> {
            "车牌号：$vehicleNum"
        }
    }
}
