package com.zczy.cargo_owner.order.reminder.req

import com.zczy.comm.http.entity.BaseNewRequest
import com.zczy.comm.http.entity.BaseRsp
import com.zczy.comm.http.entity.ResultData

/**
 *  user: ssp
 *  time: 2021/7/14 16:41
 *  desc: 运输提醒
 */

class ReqTransportRemindTop : BaseNewRequest<BaseRsp<RspTransportRemindTop>>("oms-app/display/queryConsignorTransportRemindInfo")

class RspTransportRemindTop(
    var consignorTransportRemind: String? = "",//异常提醒总数
    var consignorTransportRemind1: String? = "",//摘牌无车辆定位
    var consignorTransportRemind2: String? = "",//启运地不符
    var consignorTransportRemind3: String? = "",//目的地不符
    var consignorTransportRemind4: String? = "",//发货无车辆定位
    var consignorTransportRemind5: String? = "",//未按时发货确认
    var consignorTransportRemind6: String? = "",//未按时收货确认
    var consignorTransportRemind7: String? = "",//在途无定位
    var consignorTransportRemind8: String? = "",//偏离路线
    var consignorTransportRemind9: String? = "",//停留未行驶
    var consignorTransportRemind10: String? = "",//设备离线预警
    var consignorTransportRemind11: String? = "",//设备离线预警
) : ResultData()