package com.zczy.cargo_owner.order.overdue.adapter

import android.widget.ImageView
import com.bumptech.glide.Glide
import com.chad.library.adapter.base.BaseQuickAdapter
import com.chad.library.adapter.base.BaseViewHolder
import com.zczy.cargo_owner.libcomm.config.HttpConfig
import com.zczy.cargo_owner.order.R

/**
 * desc: 问题反馈图片适配器
 * user: AI Assistant
 * time: 2025/01/29
 */
class ProblemFeedbackImageAdapter : BaseQuickAdapter<String, BaseViewHolder>(R.layout.item_problem_feedback_image) {

    override fun convert(helper: BaseViewHolder, item: String) {
        val imageView = helper.getView<ImageView>(R.id.iv_image)
        
        // 加载图片
        Glide.with(mContext)
            .load(HttpConfig.getUrlImage(item))
            .placeholder(R.drawable.base_image_default)
            .error(R.drawable.base_image_default)
            .into(imageView)
    }
}
