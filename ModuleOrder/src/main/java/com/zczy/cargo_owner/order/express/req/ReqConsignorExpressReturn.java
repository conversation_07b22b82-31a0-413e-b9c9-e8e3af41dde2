package com.zczy.cargo_owner.order.express.req;

import com.zczy.comm.http.entity.BaseNewRequest;
import com.zczy.comm.http.entity.BaseRsp;
import com.zczy.comm.http.entity.ResultData;

public class ReqConsignorExpressReturn extends BaseNewRequest<BaseRsp<ResultData>> {

    public String detailId;
    public String orderId;
    public String expressId;
    public String repulseReason;

    public String getDetailId() {
        return detailId;
    }

    public void setDetailId(String detailId) {
        this.detailId = detailId;
    }

    public String getOrderId() {
        return orderId;
    }

    public void setOrderId(String orderId) {
        this.orderId = orderId;
    }

    public String getExpressId() {
        return expressId;
    }

    public void setExpressId(String expressId) {
        this.expressId = expressId;
    }

    public String getRepulseReason() {
        return repulseReason;
    }

    public void setRepulseReason(String repulseReason) {
        this.repulseReason = repulseReason;
    }

    public ReqConsignorExpressReturn() {
        super("oms-app/order/consignor/doPass");
    }
}
