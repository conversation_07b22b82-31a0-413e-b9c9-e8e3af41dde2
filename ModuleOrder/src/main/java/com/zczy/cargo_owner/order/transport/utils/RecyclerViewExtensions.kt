package com.zczy.cargo_owner.order.transport.utils

import android.content.Context
import android.view.View
import android.view.ViewGroup
import androidx.recyclerview.widget.RecyclerView

/**
 * RecyclerView扩展函数
 * 提供便捷的高度设置方法
 */

/**
 * 设置RecyclerView高度为屏幕的指定比例
 * @param ratio 屏幕高度比例 (0.0 - 1.0)
 */
fun RecyclerView.setHeightByScreenRatio(
    ratio: Float,
) {
    val screenHeight = context.resources.displayMetrics.heightPixels
    val targetHeight = (screenHeight * ratio).toInt()
    setFixedHeight(targetHeight)
}

/**
 * 设置RecyclerView高度为屏幕的1/4
 */
fun RecyclerView.setQuarterScreenHeight() {
    setHeightByScreenRatio(0.25f)
}

/**
 * 设置RecyclerView高度为屏幕的3/4
 */
fun RecyclerView.setThreeQuarterScreenHeight() {
    setHeightByScreenRatio(0.75f)
}

/**
 * 设置RecyclerView为自适应高度
 */
fun RecyclerView.setWrapContentHeight() {
    // 测量内容高度
    measure(
        View.MeasureSpec.makeMeasureSpec(width, View.MeasureSpec.EXACTLY),
        View.MeasureSpec.makeMeasureSpec(0, View.MeasureSpec.UNSPECIFIED)
    )
    setFixedHeight(ViewGroup.LayoutParams.WRAP_CONTENT)
}


/**
 * 检查内容是否超过指定屏幕比例的高度
 */
fun RecyclerView.isContentExceedScreenRatio(ratio: Float): Boolean {
    measure(
        View.MeasureSpec.makeMeasureSpec(width, View.MeasureSpec.EXACTLY),
        View.MeasureSpec.makeMeasureSpec(0, View.MeasureSpec.UNSPECIFIED)
    )
    val contentHeight = measuredHeight
    val screenHeight = context.resources.displayMetrics.heightPixels
    val targetHeight = (screenHeight * ratio).toInt()

    return contentHeight > targetHeight
}

/**
 * 检查内容是否超过1/4屏幕高度
 */
fun RecyclerView.isContentExceedQuarterScreen(): Boolean {
    return isContentExceedScreenRatio(0.25f)
}

/**
 * 获取屏幕高度的指定比例
 */
fun Context.getScreenHeightByRatio(ratio: Float): Int {
    val screenHeight = resources.displayMetrics.heightPixels
    return (screenHeight * ratio).toInt()
}

/**
 * 获取屏幕1/4高度
 */
fun Context.getQuarterScreenHeight(): Int = getScreenHeightByRatio(0.25f)

/**
 * 获取屏幕3/4高度
 */
fun Context.getThreeQuarterScreenHeight(): Int = getScreenHeightByRatio(0.75f)

/**
 * 设置固定高度
 */
private fun RecyclerView.setFixedHeight(height: Int) {
    val layoutParams = layoutParams
    layoutParams.height = height
    this.layoutParams = layoutParams
}

/**
 * 智能设置初始高度
 * 如果内容超过1/4屏幕高度，设置为1/4屏幕高度
 * 如果内容不足1/4屏幕高度，设置为wrap_content
 */
fun RecyclerView.setSmartInitialHeight(): Boolean {
    return if (isContentExceedQuarterScreen()) {
        setQuarterScreenHeight()
        true // 返回true表示需要显示展开按钮
    } else {
        setWrapContentHeight()
        false // 返回false表示不需要显示展开按钮
    }
}
