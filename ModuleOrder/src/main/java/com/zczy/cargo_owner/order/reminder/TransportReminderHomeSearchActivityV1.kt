package com.zczy.cargo_owner.order.reminder

import android.annotation.SuppressLint
import android.app.Activity
import android.content.Intent
import android.os.Bundle
import android.text.TextUtils
import android.view.View
import androidx.recyclerview.widget.GridLayoutManager
import com.sfh.lib.mvvm.service.BaseViewModel
import com.zczy.cargo_owner.order.R
import com.zczy.cargo_owner.order.reminder.adapter.TransportReminderSearchAdapter
import com.zczy.cargo_owner.order.reminder.adapter.generateAllData
import com.zczy.cargo_owner.order.reminder.adapter.generatePartData
import com.zczy.cargo_owner.order.reminder.req.ReqTransportReminder
import com.zczy.cargo_owner.order.reminder.widget.TransportReminderFilterTimeView
import com.zczy.comm.TimePickerUtilV1
import com.zczy.comm.ui.BaseActivity
import com.zczy.comm.utils.dp2px
import com.zczy.comm.utils.getResColor
import com.zczy.comm.utils.toJson
import com.zczy.comm.utils.toJsonObject
import com.zczy.comm.widget.inputv2.InputViewClick
import com.zczy.comm.widget.itemdecoration.CommItemDecoration
import kotlinx.android.synthetic.main.transport_reminder_home_search_activity.editCarrier
import kotlinx.android.synthetic.main.transport_reminder_home_search_activity.editOrderId
import kotlinx.android.synthetic.main.transport_reminder_home_search_activity.editPlateNumber
import kotlinx.android.synthetic.main.transport_reminder_home_search_activity.inputViewMonth
import kotlinx.android.synthetic.main.transport_reminder_home_search_activity.recycler_view
import kotlinx.android.synthetic.main.transport_reminder_home_search_activity.returnOrderFilterTime
import kotlinx.android.synthetic.main.transport_reminder_home_search_activity.tvClear
import kotlinx.android.synthetic.main.transport_reminder_home_search_activity.tvSave
import java.util.Calendar

/**
 *  user: ssp
 *  time: 2021/7/15 14:37
 *  desc: 运输提醒搜索页面-运输提醒
 */

class TransportReminderHomeSearchActivityV1 : BaseActivity<BaseViewModel>() {
    private val transportReminderSearchAdapter = TransportReminderSearchAdapter()
    private var reqQueryList: ReqTransportReminder = ReqTransportReminder()

    override fun initData() {
        transportReminderSearchAdapter.setNewData(generatePartData())
    }

    companion object {

        const val SELECT_DATA = "select_data"
        const val REQUEST_CODE = 0x11

        @JvmStatic
        fun jumpPage(activity: Activity?, requestCode: Int) {
            val intent = Intent(activity, TransportReminderHomeSearchActivityV1::class.java)
            activity?.startActivityForResult(intent, requestCode)
        }

        @JvmStatic
        fun obtainSelectData(intent: Intent?): ReqTransportReminder? {
            return intent?.getStringExtra(SELECT_DATA)?.toJsonObject(ReqTransportReminder::class.java)
        }
    }

    override fun getLayout(): Int {
        return R.layout.transport_reminder_home_search_activity
    }

    override fun bindView(p0: Bundle?) {
        returnOrderFilterTime.listener = object : TransportReminderFilterTimeView.Listener {
            override fun onClickTime(startTime: String, endTime: String) {
                reqQueryList.apply {
                    remindStartTime = startTime
                    remindEndTime = endTime
                }
                returnOrderFilterTime.setTime(startTime, endTime)
            }
        }

        transportReminderSearchAdapter.setOnItemChildClickListener { _, view, position ->
            when (view?.id) {
                R.id.tvTitle -> {
                    val rspReminderSearch = transportReminderSearchAdapter.data[position]
                    when (rspReminderSearch.showMore) {
                        true -> {
                            //查看更多
                            transportReminderSearchAdapter.setNewData(generateAllData())
                        }

                        else -> {
                            transportReminderSearchAdapter.setSelect(position)
                        }
                    }
                }
            }
        }
        recycler_view.apply {
            layoutManager = GridLayoutManager(this@TransportReminderHomeSearchActivityV1, 3)
            addItemDecoration(CommItemDecoration.createVertical(this@TransportReminderHomeSearchActivityV1, getResColor(R.color.white), dp2px(10f)))
            adapter = transportReminderSearchAdapter
        }
        inputViewMonth.setListener(object : InputViewClick.Listener() {
            override fun onClick(viewId: Int, view: InputViewClick, content: String) {
                TimePickerUtilV1.showMonthV1(
                    context = this@TransportReminderHomeSearchActivityV1,
                    title = "选择月份",
                    selectedDate = null,
                ) { month ->
                    inputViewMonth.content = month.toString()
                }
            }
        })
        bindClickEvent(tvClear)
        bindClickEvent(tvSave)
    }

    override fun onSingleClick(v: View) {
        super.onSingleClick(v)
        when (v.id) {
            R.id.tvClear -> {
                //清空条件
                clearData()
            }

            R.id.tvSave -> {
                //保存条件
                reqQueryList.apply {
                    nowPage = 1
                    orderId = editOrderId.text.toString().trim()
                    plateNumber = editPlateNumber.text.toString().trim()
                    carrierUserName = editCarrier.text.toString().trim()
                    exceptionType = transportReminderSearchAdapter.getSelectData()
                    val content = inputViewMonth.content
                    month = if (TextUtils.isEmpty(content)) {
                        val m = Calendar.getInstance().get(Calendar.MONTH) + 1
                        when (m < 10) {
                            true -> {
                                "0$m"
                            }

                            else -> {
                                "$m"
                            }

                        }
                    } else {
                        when (content.toInt() < 10) {
                            true -> {
                                "0$content"
                            }

                            else -> {
                                content
                            }

                        }
                    }
                }
                intent.putExtra(SELECT_DATA, reqQueryList.toJson())
                setResult(Activity.RESULT_OK, intent)
                finish()
            }
        }
    }

    @SuppressLint("SetTextI18n")
    private fun clearData() {
        //搜索信息
        editOrderId.setText("")
        editPlateNumber.setText("")
        editCarrier.setText("")
        //提醒类型
        transportReminderSearchAdapter.clearSelectData()
        //请求req
        reqQueryList = ReqTransportReminder()
        //发货时间
        returnOrderFilterTime.setTime("", "")
    }
}