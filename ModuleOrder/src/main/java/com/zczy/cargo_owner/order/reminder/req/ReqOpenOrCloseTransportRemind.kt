package com.zczy.cargo_owner.order.reminder.req

import com.zczy.comm.http.entity.BaseNewRequest
import com.zczy.comm.http.entity.BaseRsp
import com.zczy.comm.http.entity.ResultData

/**
 *  user: ssp
 *  time: 2021/7/15 11:00
 *  desc: 开启或关闭运输提醒
 */

class ReqOpenOrCloseTransportRemind(
    var remindId: String = "",
    var orderId: String = "", // 订单号
    var remindFlag: String = "",//0-关闭 1-开启
) : BaseNewRequest<BaseRsp<ResultData>>("oms-app/order/consignorTransportRemind/openOrCloseTransportRemind")