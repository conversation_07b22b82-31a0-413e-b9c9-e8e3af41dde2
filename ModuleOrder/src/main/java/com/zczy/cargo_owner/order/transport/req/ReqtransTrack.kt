package com.zczy.cargo_owner.order.transport.req

import android.os.Parcelable
import com.zczy.comm.http.entity.BaseNewRequest
import com.zczy.comm.http.entity.BaseRsp
import com.zczy.comm.http.entity.ResultData
import kotlinx.android.parcel.Parcelize

/**
 *    author : ssp
 *    e-mail : <EMAIL>
 *    date   : 2021/7/22 10:45
 *    desc   : 在途跟踪
 */

data class ReqtransTrack(
    var orderId: String = "",
    var isCheckBackUpLocation: String? = null, // 平台核实轨迹 1是  0否   非必传
) : BaseNewRequest<BaseRsp<WayBillTracking>>("oms-app/order/consignor/transTrack")

@Parcelize
class WayBillTracking(
    var orderInfo: OrderInfoBean? = null,
    var haveShowBackUpLocation: String? = null, //1有备份轨迹
    var backMapTime: String? = null, // 筛选时间
    val driverDataArray: List<DriverDataArrayBean> = listOf()
) : ResultData(), Parcelable

@Parcelize
class OrderInfoBean(
    var startAddress: String = "",
    var end_longitude: String = "0.00",
    var view_type: String = "",
    var carrierPhone: String = "",
    var end_latitude: String = "0.00",
    var company_mobile: String = "",
    var consignorName: String = "",
    var consignorPhone: String = "",
    var start_longitude: String = "0.00",
    var carrierName: String = "",
    var start_latitude: String = "0.00",
    var company_name: String = "",
    var endAddress: String = "",
    var nodeName: String = "",
    var startAddressDetail: String = "",
    var endAddressDetail: String = "",
    var orderId: String = ""
) : Parcelable

@Parcelize
class DriverDataArrayBean(
    var time: String = "",
    var location: String = "",
    var address: String = ""
) : Parcelable