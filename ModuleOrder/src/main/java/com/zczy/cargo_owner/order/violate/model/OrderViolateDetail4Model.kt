package com.zczy.cargo_owner.order.violate.model

import com.sfh.lib.mvvm.service.BaseViewModel
import com.sfh.lib.rx.IResultSuccess
import com.zczy.cargo_owner.order.violate.req.Req4BreachContractDetail
import com.zczy.cargo_owner.order.violate.req.RspBreachContractItem
import com.zczy.comm.http.entity.BaseRsp

/**
 * PS:
 * Created by sdx on 2019/2/28.
 */
class OrderViolateDetail4Model : BaseViewModel() {
    fun breachContractDetail(breachNumber: String) {
        execute(true,
                Req4BreachContractDetail(breachNumber = breachNumber),
                IResultSuccess<BaseRsp<RspBreachContractItem>> { t ->
                    if (t.success()) {
                        setValue("onBreachContractDetail", t.data)
                    } else {
                        showDialogToast(t.msg)
                    }
                })
    }
}