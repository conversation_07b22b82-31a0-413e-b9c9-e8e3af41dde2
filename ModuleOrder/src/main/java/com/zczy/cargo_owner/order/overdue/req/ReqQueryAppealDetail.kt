package com.zczy.cargo_owner.order.overdue.req

import com.zczy.comm.http.entity.BaseNewRequest
import com.zczy.comm.http.entity.BaseRsp
import com.zczy.comm.http.entity.ResultData

/**
 * 功能描述: 申诉详情
 * <AUTHOR>
 * @date 2023/3/30-14:40
 */

class ReqQueryAppealDetail(
    var orderId: String? = null, //
) : BaseNewRequest<BaseRsp<RspQueryAppealDetailV1>>("oms-app/order/overDueApp/queryAppealDetail")

data class RspQueryAppealDetailV1(
    val data: RspQueryAppealDetail? = null, //
) : ResultData()

data class RspQueryAppealDetail(
    val orderId: String? = null, //订单号
    val carrierCustomerName: String? = null, //司机名称
    val plateNumber: String? = null, //车牌号
    val appealReason: String? = null, //申诉原因1-货损货差 2-存在纷纷 3-回单原件未回 4-其他(必须填写情况说明)
    val appealRemark: String? = null, //申诉备注
    val examineState: String? = null, //审核状态 0-待审核 1-已通过 2-已驳回
    val examineRemark: String? = null, //审核备注
    val appealType: String? = null, //处理方式 1-等待结算 2-等待违约
    val appealValidityDay: String? = null, //申诉有效期
    val files: MutableList<RspFile> = mutableListOf(), //申诉图片
) : ResultData()

class RspFile(
    var pictureUrl: String = ""
)