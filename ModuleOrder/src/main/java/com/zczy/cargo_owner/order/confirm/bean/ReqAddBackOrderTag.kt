package com.zczy.cargo_owner.order.confirm.bean

import com.zczy.comm.http.entity.BaseNewRequest
import com.zczy.comm.http.entity.BaseRsp
import com.zczy.comm.http.entity.PageList
import com.zczy.comm.http.entity.ResultData

/**
 *@Desc 回单打回删除标签
 *@User ssp
 *@Date 2023/8/30-16:04
 */

class ReqAddBackOrderTag(
    var backReason: String? = null,//打回原因
) : BaseNewRequest<BaseRsp<ResultData>>("oms-app/order/receipt/editBackOrderTag")