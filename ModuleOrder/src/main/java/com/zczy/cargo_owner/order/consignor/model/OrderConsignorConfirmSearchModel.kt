package com.zczy.cargo_owner.order.consignor.model

import com.sfh.lib.exception.HandleException
import com.sfh.lib.mvvm.service.BaseViewModel
import com.sfh.lib.rx.IResult
import com.zczy.cargo_owner.order.consignor.req.Req1DeliverConfirmForMobile
import com.zczy.cargo_owner.order.consignor.req.Rsp1QueryConsignorOrderStateOfMobile
import com.zczy.comm.http.entity.BaseRsp
import com.zczy.comm.http.entity.PageList

/** 功能描述:
 * 发货单确认 子页面 model
 * <AUTHOR>
 * @company 中储智运科技股份有限公司
 * @copyright （版权）中储南京智慧物流科技有限公司所有
 * @date 2019/4/3
 */
class OrderConsignorConfirmSearchModel : BaseViewModel() {

    /**
     * // 查询状态条件  0-未确认  1-已确认  不传为查全部（搜索框）
     */
    fun getListInfo(nowPage: Int, search: String) {
        execute(Req1DeliverConfirmForMobile(
                search = search,
                nowPage = nowPage,
                advanceState = ""),
                object : IResult<BaseRsp<PageList<Rsp1QueryConsignorOrderStateOfMobile>>> {
                    override fun onSuccess(t: BaseRsp<PageList<Rsp1QueryConsignorOrderStateOfMobile>>) {
                        if (t.success()) {
                            setValue("onGetListInfo", t.data)
                        } else {
                            showDialogToast(t.msg)
                            setValue("onGetListInfo", null)
                        }
                    }

                    override fun onFail(e: HandleException) {
                        showDialogToast(e.msg)
                        setValue("onGetListInfo", null)

                    }
                })
    }

}