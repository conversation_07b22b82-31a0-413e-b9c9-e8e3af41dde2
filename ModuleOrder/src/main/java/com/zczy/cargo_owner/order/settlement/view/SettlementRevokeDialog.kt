package com.zczy.cargo_owner.order.settlement.view

import android.annotation.SuppressLint
import android.app.Dialog
import android.content.Context
import android.content.DialogInterface
import android.graphics.Color
import android.os.Bundle
import android.text.*
import android.text.style.ForegroundColorSpan
import android.view.Gravity
import com.zczy.cargo_owner.order.R
import kotlinx.android.synthetic.main.settlement_revoke_dialog.*


/**
 * 类描述:结算申请撤回
 * 作者：zzf
 * 创建时间：2024/9/23
 */
class SettlementRevokeDialog(context: Context) : Dialog(context, R.style.palte_style_dialog) {

    var callback: ICallback? = null
    var money: String? = null

    interface ICallback {
        fun next(dialog: DialogInterface?, tips: String)
    }

    override fun onCreate(savedInstanceState: Bundle?) {
        super.onCreate(savedInstanceState)
        setContentView(R.layout.settlement_revoke_dialog)
        setSpannableString()
        initListener()
    }

    private fun setSpannableString() {
        val sb = SpannableStringBuilder("当前结算撤回金额：")
        val span = SpannableString(money)
        span.setSpan(
            ForegroundColorSpan(Color.parseColor("#FE522E")),
            0,
            span.length,
            Spanned.SPAN_INCLUSIVE_EXCLUSIVE
        )
        val span2 = SpannableString("元")
        sb.append(span)
        sb.append(span2)
        tv_title1.text = sb
    }

    fun setCallback(callback: ICallback?): SettlementRevokeDialog {
        this.callback = callback
        return this
    }

    fun setMoney(money: String): SettlementRevokeDialog {
        this.money = money
        return this
    }

    @SuppressLint("ServiceCast")
    private fun initListener() {
        btn_cancel.setOnClickListener {
            this.dismiss()
        }
        btn_sure.setOnClickListener {
            callback?.next(this, et_tips.text.toString())
        }
    }
}

