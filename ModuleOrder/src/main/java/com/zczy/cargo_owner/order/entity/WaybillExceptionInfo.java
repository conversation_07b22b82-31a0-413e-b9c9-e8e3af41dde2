package com.zczy.cargo_owner.order.entity;

import android.text.TextUtils;

/**
 * <AUTHOR>
 * @description
 * @company 中储智运科技股份有限公司
 * @copyright （版权）中储南京智慧物流科技有限公司所有
 * @since 2019-12-27
 */
public class WaybillExceptionInfo {
    private String needHandle;
    private String exceptionName;
    public boolean isShow(){
        return TextUtils.equals("1",needHandle) || TextUtils.equals("2",needHandle) || TextUtils.equals("3",needHandle);
    }
    public String getNeedHandle() {
        return needHandle;
    }

    public void setNeedHandle(String needHandle) {
        this.needHandle = needHandle;
    }

    public String getExceptionName() {
        return exceptionName;
    }

    public void setExceptionName(String exceptionName) {
        this.exceptionName = exceptionName;
    }
}
