package com.zczy.cargo_owner.order.express.adapter

import android.os.Build
import androidx.annotation.RequiresApi
import androidx.core.content.ContextCompat
import android.text.TextUtils
import android.widget.TextView
import com.chad.library.adapter.base.BaseQuickAdapter
import com.chad.library.adapter.base.BaseViewHolder
import com.zczy.cargo_owner.order.R
import com.zczy.cargo_owner.order.express.req.*
import com.zczy.comm.utils.ex.isTrue
import com.zczy.comm.utils.getResColor

/**
 * PS:
 * Created by sdx on 2018/11/5.
 */
class OrderExpressMainAdapterV2
    :
    BaseQuickAdapter<RspCarrierExpressData, BaseViewHolder>(R.layout.order_express_main_list_end_item_v2) {

    @RequiresApi(Build.VERSION_CODES.M)
    override fun convert(helper: BaseViewHolder, item: RspCarrierExpressData) {
        convertEnd(helper, item)
    }

    @RequiresApi(Build.VERSION_CODES.M)
    private fun convertEnd(helper: BaseViewHolder, item: RspCarrierExpressData) {
        val formatGoodsInfo = item.formatGoodsInfo()
        val formatDriverInfo = item.formatDriverInfo()
        helper
            // 运单号
            .setText(R.id.tv_order, item.orderId)
            // 起点
            .setText(R.id.tv_origin, item.formatStartAddress())
            // 终点
            .setText(R.id.tv_end, item.formatEndAddress())
            // 货物明细
            .setGone(R.id.tv_goods_info, formatGoodsInfo.isNotEmpty())
            .setGone(R.id.ivLTLOrderFlag, item.LTLOrderFlag.isTrue)
            .setText(R.id.tv_goods_info, formatGoodsInfo)
            //是否展示索要发票
            .setGone(R.id.havemoney, TextUtils.equals("1", item.depositState))
            .setGone(R.id.ivOrderExpressRestrictedBilling, item.restrictBillingFlag.isTrue)
            .setGone(R.id.ivOutDeadlin, item.restrictBillingFlag.isTrue)
            // 承运方信息
            .setGone(R.id.tv_car_boss, formatDriverInfo.isNotEmpty())
            .setText(R.id.tv_car_boss, formatDriverInfo)
            // 按键 查看物流
            .addOnClickListener(R.id.btn_query)
            .setGone(R.id.tv_seek_bound_code, TextUtils.equals("1", item.LTPoundFlag.toString()))
            .addOnClickListener(R.id.tv_order)
            .addOnClickListener(R.id.tv_seek_bound_code)
            .setText(R.id.tv_deposit_money, "押回单金额：" + item.receiptMoney + "元")
            .setGone(R.id.tv_deposit_money, item.receiptMoney.isNotEmpty())

        val sendType = item.sendType
        val signState = item.signState
        when (sendType) {
            "1" -> {
                helper.setGone(R.id.tv_sign_state, true)
                helper.setText(R.id.tv_sign_state, "快递送达")
            }
            "2" -> {
                helper.setGone(R.id.tv_sign_state, true)
                helper.setText(R.id.tv_sign_state, "线下送达")
            }
            else -> {
                helper.setGone(R.id.tv_sign_state, false)
                helper.setText(R.id.tv_sign_state, "")
            }
        }
        if (item.restrictBillingFlag.isTrue) {
            helper.setBackgroundColor(R.id.ll_item_hdkd, ContextCompat.getColor(mContext, R.color.color_fff7f6))
        }else{
            helper.setBackgroundColor(R.id.ll_item_hdkd, ContextCompat.getColor(mContext, R.color.white))
        }
        //货主签收状态 0:未签收 1:已签收 ，3：待录入，4：已打回, 5：已签收
        when (signState) {
            "0" -> {
                helper.setText(R.id.tv_sign_in, "未签收")
                    .setTextColor(R.id.tv_sign_in, getResColor(R.color.text_blue))
            }
            "1", "5" -> {
                helper.setText(R.id.tv_sign_in, "已签收")
                    .setTextColor(R.id.tv_sign_in, getResColor(R.color.text_66))
            }
            "3" -> {
                helper.setText(R.id.tv_sign_in, "待录入")
                    .setTextColor(R.id.tv_sign_in, getResColor(R.color.text_blue))
            }
            "4" -> {
                helper.setText(R.id.tv_sign_in, "已打回")
                    .setTextColor(R.id.tv_sign_in, getResColor(R.color.text_orange))
            }
            else -> {
                helper.setText(R.id.tv_sign_in, "")
            }
        }

        val text1 = helper.getView<TextView>(R.id.tv_sign_state).text.toString().trim()
        val text2 = helper.getView<TextView>(R.id.tv_sign_in).text.toString().trim()
        if (TextUtils.isEmpty(text1) || TextUtils.isEmpty(text2)) {
            //有一个为空 就不展示竖线
            helper.setGone(R.id.view_line, false)
        } else {
            helper.setGone(R.id.view_line, true)
        }

        if (TextUtils.equals(sendType, "1") && (TextUtils.equals(
                signState,
                "0"
            ) || TextUtils.equals(signState, "1"))
        ) {
            helper.setGone(R.id.btn_query, true)
        } else {
            helper.setGone(R.id.btn_query, false)
        }

    }
}