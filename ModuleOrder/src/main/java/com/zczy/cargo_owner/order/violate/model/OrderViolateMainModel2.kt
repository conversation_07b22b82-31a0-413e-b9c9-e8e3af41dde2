package com.zczy.cargo_owner.order.violate.model

import com.sfh.lib.exception.HandleException
import com.sfh.lib.mvvm.service.BaseViewModel
import com.sfh.lib.rx.IResult
import com.zczy.cargo_owner.order.violate.req.Req3BreachContractList
import com.zczy.cargo_owner.order.violate.req.RspBreachContractItem
import com.zczy.comm.http.entity.BaseRsp
import com.zczy.comm.http.entity.PageList

class OrderViolateMainModel2 : BaseViewModel() {
    fun breachContractList(nowPage: Int, searchOrderId: String) {
        execute(Req3BreachContractList(nowPage = nowPage, orderId = searchOrderId),
                object : IResult<BaseRsp<PageList<RspBreachContractItem>>> {
                    @Throws(Exception::class)
                    override fun onSuccess(t: BaseRsp<PageList<RspBreachContractItem>>) {
                        if (t.success()) {
                            setValue("onGetNetInfoSuccess", t.data)
                        } else {
                            setValue("onGetNetInfoSuccess", null)
                            showDialogToast(t.msg)
                        }
                    }

                    override fun onFail(e: HandleException) {
                        setValue("onGetNetInfoSuccess", null)
                        showDialogToast(e.msg)
                    }
                })
    }
}
