package com.zczy.cargo_owner.order.overdue.adapter

import android.annotation.SuppressLint
import android.graphics.Color
import android.text.TextUtils
import com.chad.library.adapter.base.BaseQuickAdapter
import com.chad.library.adapter.base.BaseViewHolder
import com.zczy.cargo_owner.order.R
import com.zczy.cargo_owner.order.overdue.req.OrderOverDueItem
import com.zczy.cargo_owner.order.overdue.req.formatSpecifyFlag
import com.zczy.cargo_owner.order.overdue.req.formatSpecifyFlagOne
import com.zczy.comm.CommServer
import com.zczy.comm.utils.ex.isTrue
import com.zczy.comm.utils.ex.setVisible
import kotlinx.android.synthetic.main.order_overdue_undo_item.view.*

/**
 *  desc: 逾期运单管理
 *  user: 宋双朋
 *  time: 2024/8/2 10:06
 */
@SuppressLint("SetTextI18n")
class OrderOverdueUndoAdapter : BaseQuickAdapter<OrderOverDueItem, BaseViewHolder>(R.layout.order_overdue_undo_item) {
    override fun convert(helper: BaseViewHolder, item: OrderOverDueItem) {
        helper.itemView.ivLTLOrderFlag.setVisible(item.noResponsibilityFlag.isTrue)
        helper.itemView.iv_zhiya.setVisible(TextUtils.equals("2", item.businessSource))
        // 运单号:
        helper.itemView.tv_order_id_value.text = item.orderId
        // 运单状态
        helper.itemView.tv_order_overdue_deal_result_value.text = item.orderState
        helper.itemView.tv_order_overdue_deal_result_title.setVisible(true)
        helper.itemView.overdueState.setVisible(item.appealOverTimeFlag.isTrue)
        helper.itemView.tv_order_overdue_deal_result_value.setVisible(true)
        // 逾期原因:
        helper.itemView.tv_order_overdue_reason_value.text = item.remarks
        helper.itemView.tv_order_overdue_reason_value.setTextColor(Color.parseColor("#FFFF602E"))
        // 原合作运力范围:
        helper.itemView.tv_order_overdue_yhzylfw_value.text = item.formatSpecifyFlag()
        helper.itemView.tv_order_overdue_carrier_value1.text = item.formatSpecifyFlagOne()
        // 承运方:
        helper.itemView.tv_order_overdue_carrier_value.text = item.carrierCustomerName
        // 承运方手机:
        helper.itemView.tv_order_overdue_carrier_phone_value.text = item.carrierMobile
        // 车牌号:
        helper.itemView.tv_order_overdue_plate_number_value.text = item.plateNumber
        // 逾期天数:
        helper.itemView.tv_order_overdue_overdue_day_value.text = item.orderDate
        // 倒计时
        helper.itemView.tv_order_overdue_limit_publish_value.text = if (TextUtils.equals("1", item.limitPublishFlag)) {
            "是"
        } else {
            "否"
        }
        // 是否问题反馈
        helper.itemView.tv_order_overdue_yesorno.text = if (TextUtils.equals("1", item.problemFeedbackFlag)) {
            "是"
        } else {
            "否"
        }

        // 原合作运力范围承运方（0-否，1-是）
        if (item.specifyFlag.isTrue) {
            helper.itemView.tv_order_overdue_rxtime_title.setVisible(true)
            helper.itemView.tv_order_overdue_rxtime_value.setVisible(true)
            helper.itemView.tv_order_overdue_rxtime_title.text = "禁止挂单倒计时"
            // 禁止挂单倒计时:
            helper.itemView.tv_order_overdue_rxtime_value.startInterval(
                CommServer.getCacheServer().systemTime,
                item.countDown,
                "",
                ""
            )
        } else {
            when (item.remarks) {
                "未回单确认" -> {
                    helper.itemView.tv_order_overdue_rxtime_title.setVisible(true)
                    helper.itemView.tv_order_overdue_rxtime_value.setVisible(true)
                    helper.itemView.tv_order_overdue_rxtime_title.text = "限制发单倒计时"
                    helper.itemView.tv_order_overdue_rxtime_value.startInterval(
                        CommServer.getCacheServer().systemTime,
                        item.countDown,
                        "",
                        ""
                    )
                }

                "运单终止" -> {
                    helper.itemView.tv_order_overdue_rxtime_title.setVisible(true)
                    helper.itemView.tv_order_overdue_rxtime_value.setVisible(true)
                    helper.itemView.tv_order_overdue_rxtime_title.text = "运单终止倒计时"
                    helper.itemView.tv_order_overdue_rxtime_value.startInterval(
                        CommServer.getCacheServer().systemTime,
                        item.countDown,
                        "",
                        ""
                    )
                }

                "未发货确认" -> {
                    helper.itemView.tv_order_overdue_rxtime_title.setVisible(true)
                    helper.itemView.tv_order_overdue_rxtime_value.setVisible(true)
                    // 运单终止倒计时:
                    helper.itemView.tv_order_overdue_rxtime_value.startInterval(
                        CommServer.getCacheServer().systemTime,
                        item.countDown,
                        "",
                        ""
                    )
                }

                else -> {
                    helper.itemView.tv_order_overdue_rxtime_title.setVisible(false)
                    helper.itemView.tv_order_overdue_rxtime_value.setVisible(false)
                }
            }
        }

        // 按钮
        helper.addOnClickListener(R.id.btnApply)
            .addOnClickListener(R.id.tvAppeal)
            .addOnClickListener(R.id.goDoing)
            .addOnClickListener(R.id.tv_order_detail)
            .addOnClickListener(R.id.tv_order_detail_arrow)
            .setGone(R.id.tvAppeal, TextUtils.equals(item.transactionType, "0"))
        if (TextUtils.equals("1", item.problemFeedbackFlag)){
            helper.setText(R.id.tvAppeal, "问题反馈详情")
        }else{
            helper.setText(R.id.tvAppeal, "问题反馈")
        }
        //判断是否展示结算申请和回单确认按钮
        val hdShow = TextUtils.equals(item.consignorState, "7") && TextUtils.equals(item.backStatus, "3")
        val jsShow = TextUtils.equals(item.backStatus, "2") && TextUtils.equals(item.settleApplyPlag, "0")
        helper.setGone(R.id.goDoing, hdShow || jsShow)
        if (hdShow) {
            helper.setText(R.id.goDoing, "去回单确认")
        }
        if (jsShow) {
            helper.setText(R.id.goDoing, "去结算申请")
        }
    }

}
