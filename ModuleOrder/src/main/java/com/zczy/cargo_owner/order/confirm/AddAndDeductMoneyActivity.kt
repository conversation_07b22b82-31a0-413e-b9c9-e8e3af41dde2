package com.zczy.cargo_owner.order.confirm

import android.annotation.SuppressLint
import android.app.Activity
import android.content.Intent
import android.os.Bundle
import android.view.View
import androidx.core.content.ContextCompat
import androidx.recyclerview.widget.LinearLayoutManager
import com.chad.library.adapter.base.BaseQuickAdapter
import com.chad.library.adapter.base.BaseViewHolder
import com.sfh.lib.utils.UtilTool
import com.zczy.cargo_owner.libcomm.utils.toDoubleRoundDownString
import com.zczy.cargo_owner.order.R
import com.zczy.cargo_owner.order.confirm.bean.RspAddAndDeductMoney
import com.zczy.cargo_owner.order.confirm.bean.RspAddAndDeductMoney.Companion.TYPE_1
import com.zczy.cargo_owner.order.confirm.bean.RspAddAndDeductMoney.Companion.TYPE_2
import com.zczy.cargo_owner.order.confirm.bean.getReasonValue
import com.zczy.cargo_owner.order.confirm.bean.setReasonValue
import com.zczy.comm.ui.BaseActivity
import com.zczy.comm.utils.NumUtil
import com.zczy.comm.utils.ex.setVisible
import com.zczy.comm.utils.ex.yes
import com.zczy.comm.utils.toJson
import com.zczy.comm.utils.toJsonArray
import com.zczy.comm.widget.dialog.ChooseDialogV1
import com.zczy.comm.widget.inputv2.InputViewClick
import com.zczy.comm.widget.inputv2.InputViewEdit
import kotlinx.android.synthetic.main.add_and_deduct_money_item.view.addOrderBigCargoBig
import kotlinx.android.synthetic.main.add_and_deduct_money_item.view.addOrderBigCargoUnBig
import kotlinx.android.synthetic.main.add_and_deduct_money_item.view.inputView1
import kotlinx.android.synthetic.main.add_and_deduct_money_item.view.inputView2
import kotlinx.android.synthetic.main.add_and_deduct_money_item.view.ivDelete
import kotlinx.android.synthetic.main.add_and_deduct_money_item.view.tvItemName
import kotlinx.android.synthetic.main.order_activity_add_and_deduct_money.appToolbar
import kotlinx.android.synthetic.main.order_activity_add_and_deduct_money.recyclerView
import kotlinx.android.synthetic.main.order_activity_add_and_deduct_money.tvLeft
import kotlinx.android.synthetic.main.order_activity_add_and_deduct_money.tvRight
import kotlinx.android.synthetic.main.order_returned_jixkou_info_layout_v3.view.tvAdd
import kotlinx.android.synthetic.main.order_returned_jixkou_info_layout_v3.view.tvTaxIncludedPrice
import kotlinx.android.synthetic.main.order_returned_jixkou_info_layout_v3.view.tvTaxNotIncludedPrice

/**
 *描述：加扣款项
 *auth:宋双朋
 *time:2024/5/29 11:21
 */

@SuppressLint("NotifyDataSetChanged")
class AddAndDeductMoneyActivity : BaseActivity<ReturnedOrderConfirmModel>() {
    private val rate by lazy { intent.getStringExtra(EXTRA_RATE) }
    private val consignorNoRateMoney by lazy { intent.getStringExtra(EXTRA_CONSIGNOR_NO_RATE_MONEY) }
    private val mExtraData by lazy { intent.getStringExtra(EXTRA_DATA)?.toJsonArray(RspAddAndDeductMoney::class.java) ?: mutableListOf(RspAddAndDeductMoney(type = "1")) }
    private val mAddAndDeductMoneyAdapter = AddAndDeductMoneyAdapter()

    companion object {
        const val ADD_AND_DEDUCT_MONEY_CODE = 0x55
        private const val EXTRA_RATE = "rate"
        private const val EXTRA_DATA = "extraData"
        private const val EXTRA_DATA_PRICE_1 = "EXTRA_DATA_PRICE_1" //结算金额(承运方预估到手价)
        private const val EXTRA_DATA_PRICE_2 = "EXTRA_DATA_PRICE_2" //结算金额(含税)
        private const val EXTRA_CONSIGNOR_NO_RATE_MONEY = "consignorNoRateMoney"

        @JvmStatic
        fun jumpPage(
            activity: Activity?,
            consignorNoRateMoney: String?, // 结算金额(承运方预估到手价)注 有亏涨吨就是亏涨吨后 结算金额(承运方预估到手价)
            rate: String,//结算税率
            extraData: String?,//选择的加扣款数据
            requestCode: Int
        ) {
            val intent = Intent(activity, AddAndDeductMoneyActivity::class.java).apply {
                putExtra(EXTRA_CONSIGNOR_NO_RATE_MONEY, consignorNoRateMoney)
                putExtra(EXTRA_DATA, extraData)
                putExtra(EXTRA_RATE, rate)
            }
            activity?.startActivityForResult(intent, requestCode)
        }

        @JvmStatic
        fun obtainData(intent: Intent?): MutableList<RspAddAndDeductMoney> {
            val list = intent?.getStringExtra(EXTRA_DATA)?.toJsonArray(RspAddAndDeductMoney::class.java) ?: mutableListOf(RspAddAndDeductMoney())
            return list
        }
    }

    override fun getLayout(): Int {
        return R.layout.order_activity_add_and_deduct_money
    }

    override fun initData() {

    }

    override fun bindView(bundle: Bundle?) {
        mAddAndDeductMoneyAdapter.apply {
            val footView = layoutInflater.inflate(R.layout.order_returned_jixkou_info_layout_v3, null)
            footView.tvAdd.setOnClickListener {
                val size = mAddAndDeductMoneyAdapter.data.size
                if (size >= 3) {
                    showToast("加扣款项最多添加3条")
                    return@setOnClickListener
                }
                mAddAndDeductMoneyAdapter.addData(RspAddAndDeductMoney(type = TYPE_1))
            }
            bindToRecyclerView(recyclerView)
            addFooterView(footView)
            setOnItemChildClickListener { _, view, position ->
                when (view.id) {
                    R.id.ivDelete -> {
                        val item = mAddAndDeductMoneyAdapter.data[position]
                        item.isChecked = !item.isChecked
                        mAddAndDeductMoneyAdapter.notifyItemChanged(position, item)
                    }

                    R.id.addOrderBigCargoBig -> {
                        val item = mAddAndDeductMoneyAdapter.data[position]
                        item.type = TYPE_1
                        mAddAndDeductMoneyAdapter.notifyItemChanged(position, item)
                        //触发计算
                        computeBlock()
                    }

                    R.id.addOrderBigCargoUnBig -> {
                        val item = mAddAndDeductMoneyAdapter.data[position]
                        item.type = TYPE_2
                        mAddAndDeductMoneyAdapter.notifyItemChanged(position, item)
                        //触发计算
                        computeBlock()
                    }
                }
            }
            computeBlock = {
                //计算结算价格(承运方预估到手价)
                val money2 = computePrice()
                footView.tvTaxNotIncludedPrice.text = money2.toDoubleRoundDownString(2)
                //计算结算价格(含税)
                val sumRate = NumUtil.sum(1.00, rate?.toDoubleOrNull() ?: 0.00)
                val money1 = NumUtil.mul(money2, sumRate).toDoubleRoundDownString(2)
                footView.tvTaxIncludedPrice.text = money1
            }
            setNewData(mExtraData)
            computeBlock()
        }
        recyclerView.apply {
            layoutManager = LinearLayoutManager(this@AddAndDeductMoneyActivity)
            adapter = mAddAndDeductMoneyAdapter
        }
        bindClickEvent(tvRight)
        bindClickEvent(tvLeft)
        appToolbar.setRightOnClickListener {
            appToolbar.tvRight.text = ""
            mAddAndDeductMoneyAdapter.showDelete = true
            mAddAndDeductMoneyAdapter.notifyDataSetChanged()
        }
    }

    override fun onSingleClick(v: View) {
        super.onSingleClick(v)
        val showDelete = mAddAndDeductMoneyAdapter.showDelete
        when (v.id) {
            R.id.tvRight -> {
                if (showDelete) {
                    //删除
                    val list = mAddAndDeductMoneyAdapter.data
                    mAddAndDeductMoneyAdapter.showDelete = false
                    appToolbar.tvRight.text = "删除"
                    val filter = list.filter { !it.isChecked }
                    mAddAndDeductMoneyAdapter.setNewData(filter)
                    //计算加扣款后结算价格
                    mAddAndDeductMoneyAdapter.computeBlock()
                } else {
                    //确定
                    checkAll().yes {
                        val data = mAddAndDeductMoneyAdapter.data
                        data.forEach {
                            if (it.isChecked) {
                                data.remove(it)
                            }
                        }
                        mAddAndDeductMoneyAdapter.notifyDataSetChanged()
                        val toJson = mAddAndDeductMoneyAdapter.data.toJson()
                        intent.putExtra(EXTRA_DATA, toJson)
                        //计算结算价格(承运方预估到手价)
                        val money2 = computePrice()
                        //计算结算价格(含税)
                        val sumRate = NumUtil.sum(1.00, rate?.toDoubleOrNull() ?: 0.00)
                        intent.putExtra(EXTRA_DATA_PRICE_1, money2.toDoubleRoundDownString(2))
                        intent.putExtra(EXTRA_DATA_PRICE_2, NumUtil.mul(money2, sumRate).toDoubleRoundDownString(2))
                        setResult(Activity.RESULT_OK, intent)
                        finish()
                    }
                }
            }

            R.id.tvLeft -> {
                if (showDelete) {
                    //取消删除
                    mAddAndDeductMoneyAdapter.showDelete = false
                    appToolbar.tvRight.text = "删除"
                    mAddAndDeductMoneyAdapter.data.forEach {
                        it.isChecked = false
                    }
                    mAddAndDeductMoneyAdapter.notifyDataSetChanged()
                } else {
                    finish()
                }
            }
        }
    }

    private fun computePrice(): Double {
        //计算加扣款后结算价格(承运方预估到手价)
        var money = consignorNoRateMoney?.toDoubleOrNull() ?: 0.00
        val computeMoney = mAddAndDeductMoneyAdapter.data.computeMoney()
        money = NumUtil.sum(money, computeMoney)
        return money
    }

    private fun checkAll(): Boolean {
        var reason: String? = ""
        val list = mAddAndDeductMoneyAdapter.data
        list.forEachIndexed { index, item ->
            if (item.money.isNullOrEmpty()) {
                showDialogToast("加扣款项${index + 1}金额不能为空！")
                return false
            }
            if (item.reason.isNullOrEmpty()) {
                showDialogToast("加扣款项${index + 1}加扣款原因不能为空！")
                return false
            }
            reason = item.reason
        }
        val distinctBy = list.distinctBy { it.reason }
        if (distinctBy.size != list.size) {
            showDialogToast("加扣款项加扣款原因不能相同！")
            return false
        }
        return true
    }

    /**
     *描述：加扣款
     *auth:宋双朋
     *time:2024/5/29 11:22
     */
    @SuppressLint("SetTextI18n")
    inner class AddAndDeductMoneyAdapter(var showDelete: Boolean = false) : BaseQuickAdapter<RspAddAndDeductMoney, BaseViewHolder>(R.layout.add_and_deduct_money_item) {
        var computeBlock: () -> Unit = {

        }

        override fun convert(helper: BaseViewHolder, item: RspAddAndDeductMoney) {
            helper.itemView.tvItemName.text = "加扣款项${helper.absoluteAdapterPosition + 1}"
            helper.itemView.ivDelete.setVisible(showDelete)
            helper.itemView.ivDelete.isSelected = item.isChecked
            helper.addOnClickListener(R.id.ivDelete)
            helper.addOnClickListener(R.id.addOrderBigCargoBig)
            helper.addOnClickListener(R.id.addOrderBigCargoUnBig)
            when (item.type) {
                TYPE_1 -> {
                    //加款
                    helper.itemView.addOrderBigCargoBig.setBackgroundResource(R.drawable.file_f2f6ff_3372fd_stroke_corner2)
                    helper.itemView.addOrderBigCargoBig.setTextColor(ContextCompat.getColor(mContext, R.color.color_3E7BFF))
                    //扣款
                    helper.itemView.addOrderBigCargoUnBig.setBackgroundResource(R.drawable.file_f6f6f6_stroke_corner2)
                    helper.itemView.addOrderBigCargoUnBig.setTextColor(ContextCompat.getColor(mContext, R.color.text_33))
                }

                TYPE_2 -> {
                    //加款
                    helper.itemView.addOrderBigCargoBig.setBackgroundResource(R.drawable.file_f6f6f6_stroke_corner2)
                    helper.itemView.addOrderBigCargoBig.setTextColor(ContextCompat.getColor(mContext, R.color.text_33))
                    //扣款
                    helper.itemView.addOrderBigCargoUnBig.setBackgroundResource(R.drawable.file_f2f6ff_3372fd_stroke_corner2)
                    helper.itemView.addOrderBigCargoUnBig.setTextColor(ContextCompat.getColor(mContext, R.color.color_3E7BFF))
                }
            }
            helper.itemView.inputView2.tag = item
            helper.itemView.inputView2.setTypeNum()
            UtilTool.setEditTextInputSize(helper.itemView.inputView2.editText, 2)
            helper.itemView.inputView2.content = item.money ?: ""
            helper.itemView.inputView2.setListener(object : InputViewEdit.Listener() {
                override fun onTextChanged(viewId: Int, view: InputViewEdit, s: String) {
                    //输入监听
                    val tag = view.tag
                    if (tag is RspAddAndDeductMoney) {
                        tag.money = s
                        //触发计算
                        computeBlock()
                    }
                }
            })
            helper.itemView.inputView1.tag = item
            helper.itemView.inputView1.content = item.getReasonValue()
            helper.itemView.inputView1.setListener(object : InputViewClick.Listener() {
                override fun onClick(viewId: Int, view: InputViewClick, content: String) {
                    //选择加扣款原因
                    val tag = view.tag
                    if (tag is RspAddAndDeductMoney) {
                        ChooseDialogV1.instance(initReasonList())
                            .setClick { s, _ ->
                                view.content = s
                                tag.setReasonValue(s)
                            }.show(this@AddAndDeductMoneyActivity)
                    }
                }
            })
        }
    }

    fun initReasonList(): MutableList<String> {
        return mutableListOf("运费浮动", "延时扣款", "磅差费用", "装卸货费", "信息费")
    }

}