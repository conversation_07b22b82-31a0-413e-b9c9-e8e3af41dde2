package com.zczy.cargo_owner.order.express.req

import com.zczy.comm.http.entity.BaseNewRequest
import com.zczy.comm.http.entity.BaseRsp
import com.zczy.comm.http.entity.ResultData


data class ReqBoundDetail(
    var orderId: String = ""
) : BaseNewRequest<BaseRsp<RespBoundDetail>>("oms-app/order/express/queryConsignorOrderPoundList")

data class RespBoundDetail(
    val orderId : Long? = null,
    val orderSignState : Int? = null,
    val rootArray: MutableList<RootArrayData>? = null
) : ResultData()

class RootArrayData {
    val orderId: String? = null
    val poundOrderNum: String? = null
    val signState: Int? = null
}