package com.zczy.cargo_owner.order.violate.req

import com.zczy.comm.http.entity.BaseNewRequest
import com.zczy.comm.http.entity.BaseRsp
import com.zczy.comm.http.entity.ResultData

/**
 *  desc: 违约申请增加变更状态逻辑校验
 *  user: 宋双朋
 *  time: 2024/8/20 19:35
 */

data class ReqCheckOrderChangeInfo(
    var orderId: String? = null, // 订单号
    var stopFlag: Boolean? = null, // 是否终止运单
    var modeType: String? = null,////业务模式：0：网货专票 1：代开专票  2：不要票
) : BaseNewRequest<BaseRsp<RspCheckOrderChangeInfo>>("wo-app/order/breach/checkOrderChangeInfo")

data class RspCheckOrderChangeInfo(
    var flag: String? = null // 1-运单承运人变更中 2-运单信息变更中且终止运单 3-运单信息变更中不终止运单 4-不存在变更
) : ResultData()

data class ReqCheckHandleConsultation(
    var orderId: String? = null, // 订单号
    var modeType: String? = null,////业务模式：0：网货专票 1：代开专票  2：不要票
) : BaseNewRequest<BaseRsp<RspCheckHandleConsultation>>("wo-app/order/breach/checkHandleConsultation")

data class RspCheckHandleConsultation(
    var flag: Boolean = false // 是否有关联咨询单 true存在 false不存在
) : ResultData()