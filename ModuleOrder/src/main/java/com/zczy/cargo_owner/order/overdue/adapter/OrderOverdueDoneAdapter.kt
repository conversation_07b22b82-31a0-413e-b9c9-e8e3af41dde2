package com.zczy.cargo_owner.order.overdue.adapter

import android.text.TextUtils
import com.chad.library.adapter.base.BaseQuickAdapter
import com.chad.library.adapter.base.BaseViewHolder
import com.zczy.cargo_owner.order.R
import com.zczy.cargo_owner.order.overdue.req.OrderOverDueItem
import com.zczy.cargo_owner.order.overdue.widget.OrderOverdueOrderView
import com.zczy.comm.utils.ex.isTrue

class OrderOverdueDoneAdapter : BaseQuickAdapter<OrderOverDueItem, BaseViewHolder>(R.layout.order_overdue_done_item) {

    override fun convert(helper: BaseViewHolder, item: OrderOverDueItem) {
        val overdueOrderView = helper.getView<OrderOverdueOrderView>(R.id.overdue_order_view)
        overdueOrderView.setData(item)
        // 违约记录
        helper.setGone(R.id.btn_history, item.defaultRecordFlag.isTrue)
            .addOnClickListener(R.id.btn_history)
            .addOnClickListener(R.id.overdue_order_view)
        //判断是否展示结算申请和回单确认按钮
        val hdShow = TextUtils.equals(item.consignorState, "7") && TextUtils.equals(item.backStatus, "3")
        val jsShow = TextUtils.equals(item.backStatus, "2") && TextUtils.equals(item.settleApplyPlag, "0")
        helper.setGone(R.id.goDoing, hdShow || jsShow)
            .addOnClickListener(R.id.goDoing)
        if (hdShow) {
            helper.setText(R.id.goDoing, "去回单确认")
        }
        if (jsShow) {
            helper.setText(R.id.goDoing, "去结算申请")
        }
    }
}
