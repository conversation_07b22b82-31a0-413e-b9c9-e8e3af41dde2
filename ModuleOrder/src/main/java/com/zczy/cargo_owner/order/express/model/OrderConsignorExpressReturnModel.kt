package com.zczy.cargo_owner.order.express.model

import android.view.Gravity
import com.sfh.lib.exception.HandleException
import com.sfh.lib.mvvm.service.BaseViewModel
import com.sfh.lib.rx.IResult
import com.sfh.lib.ui.dialog.DialogBuilder
import com.zczy.cargo_owner.order.express.req.ReqConsignorExpressReturn
import com.zczy.comm.http.entity.BaseRsp
import com.zczy.comm.http.entity.ResultData

class OrderConsignorExpressReturnModel : BaseViewModel() {

    fun consignorExpressReturn(reqConsignorExpressReturn: ReqConsignorExpressReturn) {

        val dialogBuilder = DialogBuilder()
            .setTitle("回单打回提醒")
            .setMessage("是否确认打回司机寄来的纸质回单，打回后司机需要重新录入快递单号。")
            .setGravity(Gravity.CENTER)
            .setOkListener { dialogInterface, _ ->
                dialogInterface.dismiss()
                execute<BaseRsp<ResultData>>(
                    false,
                    reqConsignorExpressReturn,
                    object : IResult<BaseRsp<ResultData>> {
                        override fun onFail(e: HandleException) {
                            showDialogToast(e.msg)
                        }

                        @Throws(Exception::class)
                        override fun onSuccess(resultDataBaseRsp: BaseRsp<ResultData>) {
                            if (resultDataBaseRsp.success()) {
                                setValue("onConsignorExpressReturn", resultDataBaseRsp.msg)
                            } else {
                                showDialogToast(resultDataBaseRsp.msg)
                            }
                        }
                    })
            }
        showDialog(dialogBuilder)
    }
}