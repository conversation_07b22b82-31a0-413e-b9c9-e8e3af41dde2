package com.zczy.cargo_owner.order.overdue.adapter

import android.annotation.SuppressLint
import com.chad.library.adapter.base.BaseQuickAdapter
import com.chad.library.adapter.base.BaseViewHolder
import com.zczy.cargo_owner.order.R
import com.zczy.cargo_owner.order.overdue.req.OrderOverDueItem
import kotlinx.android.synthetic.main.overdue_order_appeal_item.view.tvName
import kotlinx.android.synthetic.main.overdue_order_appeal_item.view.tvOrderId
import kotlinx.android.synthetic.main.overdue_order_appeal_item.view.tvPlateNumber

/**
 *  desc: 批量申诉
 *  user: 宋双朋
 *  time: 2024/8/2 16:14
 */
class OrderOverdueAppealAdapter : BaseQuickAdapter<OrderOverDueItem, BaseViewHolder>(R.layout.overdue_order_appeal_item) {
    @SuppressLint("SetTextI18n")
    override fun convert(helper: BaseViewHolder, item: OrderOverDueItem) {
        helper.itemView.tvOrderId.text = "运单号：${item.orderId}"
        helper.itemView.tvName.text = "承运方：${item.carrierCustomerName}"
        helper.itemView.tvPlateNumber.text = "车牌号：${item.plateNumber}"
    }
}