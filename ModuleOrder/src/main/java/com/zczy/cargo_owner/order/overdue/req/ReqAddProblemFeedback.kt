package com.zczy.cargo_owner.order.overdue.req

import com.zczy.comm.http.entity.BaseNewRequest
import com.zczy.comm.http.entity.BaseRsp
import com.zczy.comm.http.entity.ResultData

/**
 * PS: 逾期问题反馈 - 新增问题反馈
 * Created by AI Assistant
 */
data class ReqAddProblemFeedback(
    /** 运单号 */
    var orderId: String = "",
    /** 责任方：1货主, 2承运方, 3加盟运力 */
    var responsibleParty: String = "",
    /** 问题反馈类型:1.货损货差,2.偷换货 3.现场问题 4：多次违约 5：前期异常未处理完成 */
    var feedbackType: String = "",
    /** 任务单来源类型：3：CRM 4：货主 6：汽运后台 */
    var sourceType: Int = 4,
    /** 问题描述 */
    var description: String = "",
    /** 反馈图片URL列表(未加签) */
    var feedbackImgUrlList: List<String>? = null
) : BaseNewRequest<BaseRsp<ResultData>>("oms-app/order/overDueApp/addProblemFeedback")
