package com.zczy.cargo_owner.order.confirm.model

import com.sfh.lib.mvvm.service.BaseViewModel
import com.zczy.cargo_owner.order.confirm.bean.ReqUpdateReceiptDeliverWeightAndPic
import com.zczy.cargo_owner.order.confirm.bean.ReqUpdateReceiptReceiveWeightAndPic
import com.zczy.comm.CommServer
import com.zczy.comm.file.IFileServer
import java.io.File

/**
 * 功能描述: 修改发货信息/回单信息
 * <AUTHOR>
 * @date 2022/4/7-16:10
 */

class UploadReturnedOrderPhotoModelV1 : BaseViewModel() {

    fun upFile(file: List<String>) {
        //上传文件
        for (p in file) {
            this.upFile(p)
        }
    }

    private fun upFile(file: String) {

        val disposable = CommServer.getFileServer()
            .update(File(file), object : IFileServer.OnFileUploaderListener {
                override fun onSuccess(tag: File, url: String) {
                    setValue("onFileSuccess", tag, url)
                }

                override fun onFailure(tag: File, error: String) {
                    showToast(error)
                    setValue("onFileFailure", tag, error)
                }
            })
        this.putDisposable(disposable)
    }

    fun updateReceiptReceiveWeightAndPic(req: ReqUpdateReceiptReceiveWeightAndPic) {
        execute(req) {
            if (it.success()) {
                setValue("updateReceiptReceiveWeightAndPicSuccess", it.msg)
            } else {
                showToast(it.msg)
            }
        }
    }

    fun updateReceiptDeliverWeightAndPic(req: ReqUpdateReceiptDeliverWeightAndPic) {
        execute(req) {
            if (it.success()) {
                setValue("updateReceiptReceiveWeightAndPicSuccess", it.msg)
            } else {
                showToast(it.msg)
            }
        }
    }
}