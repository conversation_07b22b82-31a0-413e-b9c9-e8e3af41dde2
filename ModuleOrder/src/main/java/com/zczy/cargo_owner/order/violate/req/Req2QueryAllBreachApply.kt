package com.zczy.cargo_owner.order.violate.req

import com.zczy.comm.http.entity.BaseNewRequest
import com.zczy.comm.http.entity.BaseRsp
import com.zczy.comm.http.entity.PageList

/**
 * PS: 2.查询当前用户所有违约申请
 * http://wiki.zczy56.com/pages/viewpage.action?pageId=12356059
 * Created by sdx on 2019/2/27.
 */
class ReqQueryAllBreachApply(
    var nowPage: Int = 1,
    var pageSize: Int = 10,
    var orderId: String = "",
    var finishFlag: String = "",// 0-申请中，1-历史记录
    var applyTimeStartMinStr: String? = null, //       申请开始时间，yyyy-MM-dd HH:mm 分
    var applyTimeEndMinStr: String? = null, //       申请结束时间，yyyy-MM-dd HH:mm 分
    var applyBreachMoneyFlag: String? = null, //   是否申请赔偿金 0否 1是
    var workType: String? = null, //  违约方  -1货主违约    -2承运方违约
) : BaseNewRequest<BaseRsp<PageList<RspBreachApplyItem>>>("wo-app/order/breach/queryAllBreachApply")

data class RspBreachApplyItem(
    var workNumber: String = "", // String	工单编号
    var createdTimeStr: String = "", // String	创建时间
    var orderIdStr: String = "", // String	订单id
    var specifyFlag: String = "", // 0不是vip指定  1是vip指定 没查到的都默认0
    var advanceFlag: String = "", // Integer	是否预付
    var stateStr: String = "", // String	数据库中，此违约申请申请状态
    var haveAllowedRetract: String = "", // String	是否可撤回 0 不能撤回 1 可以撤回
    var userId: String = "", // String	用户id
    var childId: String = "", // String	子账户Id
    var consignorChildName: String = "", // String	发单员姓名
    var breachTypeName: String = "", // String	违约类型名称
    var breachApplyId: String = "", // Long	违约申请Id
    var breachApplyNumber: String = "", // String	违约申请编号
    var orderId: String = "", // Long	订单Id
    var applyUserId: String = "", // Long	发起人用户Id
    var applyUserName: String = "", // String	发起人姓名
    var applyUserType: String = "", // String	发起人用户类型
    var breachType: String = "", // String	违约方类型：1 货主，2 承运方
    var breachUserId: String = "", // Long	违约方用户Id
    var breachUserName: String = "", // String	违约方用户名称
    var breachUserType: String = "", // String	违约方用户类型
    var breachUserMobile: String = "", // String	违约方用户手机号
    var beBreachType: String = "", // String	被违约方类型：1 货主，2 承运方
    var beBreachUserName: String = "", // String	被违约方会用户名称
    var beBreachUserType: String = "", // String	被违约方用户类型
    var beBreachUserMobile: String = "", // String	被违约方用户手机号
    var workType: String = "", // String	工单类型：-1：货主违约，-2：承运方违约
    var leafBreachTypeId: String = "", // Long	违约类型，叶子节点
    var leafBreachTypeName: String = "", // String	具体违约类型名称
    var breachMoney: String = "", // BigDecimal	违约金额
    var isStop: String = "", // String	是否终止运输
    var remark: String = "", // String	说明
    var applyTime: String = "", // Date	申请发起时间
    var isAppoint: String = "", // String	是否指定： 0:非指定，1：指定
    var state: String = "", // String	状态：1：处理中（违约申请中，平台介入，处理违约申请），2：处理完：拒绝（违约申请失败），3:处理完：同意（完成）
    var createdBy: String = "", // String	创建人
    var createdTime: String = "", // Date	创建时间
    var lastUptBy: String = "", // String	最后修改人
    var lastUptTime: String = "", // Date	最后修改时间
    var deleteFlag: String = "", // String	删除标记
    var applyUserChildId: String = "", // Long	发起人子用户Id
    var dispatchIntervention: String = "", // String	调度员是否介入 0否
    var breachUserChildId: String = "", // Long	违约方子用户Id
    var modeType: String = "", // 1：代开 2不代开 极速好货
)

fun RspBreachApplyItem.formatBreachState(): String {
    // 1.违约申请中，2.平台介入，3.处理违约申请，4.违约处理失败，5.完成
    return when (state) {
        "1" -> "待承运方处理"
        "2" -> "平台介入中"
        "3" -> "待货主处理"
        "4" -> "处理完成"
        "5" -> "处理完成"
        "6" -> "已撤回申请"
        else -> "处理完成"
    }
}