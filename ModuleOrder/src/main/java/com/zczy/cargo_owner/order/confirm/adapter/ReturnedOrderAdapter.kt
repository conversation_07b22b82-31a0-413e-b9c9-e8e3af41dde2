package com.zczy.cargo_owner.order.confirm.adapter

import android.text.TextUtils
import android.view.View
import android.widget.ImageView
import androidx.core.content.ContextCompat
import com.chad.library.adapter.base.BaseQuickAdapter
import com.chad.library.adapter.base.BaseViewHolder
import com.zczy.cargo_owner.order.R
import com.zczy.cargo_owner.order.confirm.ReturnedOrderConfirmListFragment
import com.zczy.cargo_owner.order.confirm.ReturnedOrderConfirmListFragment.Companion.UPLOAD_RETURNED_ORDER_PHOTO
import com.zczy.cargo_owner.order.confirm.bean.ReturnedOrderItemBean
import com.zczy.cargo_owner.order.confirm.bean.getExceptionStateTag
import com.zczy.cargo_owner.order.confirm.bean.getReturnMoneyTime
import com.zczy.cargo_owner.order.confirm.bean.showBackUp
import com.zczy.cargo_owner.order.confirm.bean.showDtIcon
import com.zczy.cargo_owner.order.confirm.bean.showExceptionStateTag
import com.zczy.cargo_owner.order.confirm.bean.showGoodsSource
import com.zczy.cargo_owner.order.confirm.bean.showReturnMoneyTime
import com.zczy.cargo_owner.order.confirm.bean.showSelfComment
import com.zczy.cargo_owner.order.confirm.bean.showZhiYa
import com.zczy.comm.data.entity.EImage
import com.zczy.comm.utils.ex.isTrue
import com.zczy.comm.utils.imageselector.ImageSelectView

/**
 *描述：回单确认
 *auth:宋双朋
 *time:2024/6/18 11:20
 */

class ReturnedOrderAdapter(
    private val status: String,
    private val checkBoxVisible: Boolean = false,
    private val itemBottomBtnVisible: Boolean = false,
    private val showRejectedButton: Int = View.VISIBLE,
    private val showApplyBreachOfContractBtn: Int = View.VISIBLE
) : BaseQuickAdapter<ReturnedOrderItemBean, BaseViewHolder>(R.layout.order_returned_confirm_item) {

    override fun convert(helper: BaseViewHolder?, item: ReturnedOrderItemBean) {
        helper?.let { it ->
            if (checkBoxVisible) {
                it.getView<ImageView>(R.id.cbChosenReturnedOrder).isSelected = item.selected
                it.setGone(R.id.cbChosenReturnedOrder, true)
            } else {
                it.setGone(R.id.cbChosenReturnedOrder, false)
            }
            it.setText(R.id.tvReturnOrderNo, item.orderId)
                .setText(R.id.tvSelfComment, item.showSelfComment())
                .setText(
                    R.id.tvAutomaticReceiveDays,
                    "距离自动确认收货还有${item.automaticReceiveDays}天"
                )
                .setGone(R.id.tvAutomaticReceiveDays, !TextUtils.isEmpty(item.automaticReceiveDays))
                .setGone(
                    R.id.tvAutomaticReceiveDays1,
                    !TextUtils.isEmpty(item.automaticReceiveDays)
                )
                .setGone(R.id.tvSelfComment, true)
                .setText(R.id.tvPlaceOfStart, item.despatchCity + item.despatchDis)
                .setText(R.id.tvPlaceOfEnd, item.deliverCity + item.deliverDis)
                .setText(R.id.tvExpiryDate, "回款到期日：" + item.getReturnMoneyTime())
                .setGone(R.id.tvExpiryDate, item.showReturnMoneyTime())
                .setText(R.id.tv_cargo_remark, "运单标识：" + item.orderMark)
                .setText(R.id.tvMoneyV1, "回单确认金额：${item.finConsignorBackMoney}元")
                .setGone(R.id.tvMoneyV1, !TextUtils.isEmpty(item.finConsignorBackMoney))
                .setGone(R.id.tv_cargo_remark, !TextUtils.isEmpty(item.orderMark))
                .setGone(R.id.iv_dt_icon, item.showDtIcon())
                .setGone(R.id.iv_backUp, item.showBackUp())
                .setGone(R.id.iv_container, item.showGoodsSource())
                .setGone(R.id.iv_zhiya, item.showZhiYa())
            if (item.lTLOrder.isTrue) {
                it.setImageResource(R.id.imgGrabOrder, R.drawable.deliver_order_nil_tag)
            } else {
                if (item.orderModel.isTrue) {
                    it.setImageResource(R.id.imgGrabOrder, R.drawable.order_waybill_jj)
                } else {
                    it.setImageResource(R.id.imgGrabOrder, R.drawable.order_confirm_grab_the_order)
                }
            }
            it.setGone(R.id.llView2, false)
            //设置轨迹回放按钮显示
            if (TextUtils.equals(status, ReturnedOrderConfirmListFragment.TYPE_NOT_CONFIRM) || TextUtils.equals(status, ReturnedOrderConfirmListFragment.TYPE_WAITING_PROCESSING)) {
                it.setGone(R.id.tvgjhf, true)
                it.setGone(R.id.llView2, true)
                it.setText(R.id.tvgjhf, "轨迹回放")
            } else {
                it.setGone(R.id.tvgjhf, false)
            }
            //设置催平台审核按钮
            if ((TextUtils.equals(status, ReturnedOrderConfirmListFragment.TYPE_NOT_CONFIRM) || TextUtils.equals(status, ReturnedOrderConfirmListFragment.TYPE_CONFIRMED)) && !TextUtils.equals(item.backCheck, "2")) {
                it.setGone(R.id.tvExpeditingAudits, true)
                it.setGone(R.id.llView2, true)
                it.setText(R.id.tvExpeditingAudits, "催平台审核")
            } else {
                it.setGone(R.id.tvExpeditingAudits, false)
            }
            // 安全处理轨迹审核标签
            val showTag = item.showExceptionStateTag()
            it.setGone(R.id.llView3, showTag)
            if (showTag) {
                it.setText(R.id.exceptionStateTag, "轨迹审核：${item.getExceptionStateTag()}")
                when (item.exceptionStateTag) {
                    "1" -> {
                        helper.setBackgroundRes(R.id.llView3, R.drawable.shape_order_return_3)
                        helper.setBackgroundRes(R.id.ivExceptionStateTag, R.drawable.base_order_return_icon_4)
                        helper.setTextColor(R.id.exceptionStateTag, ContextCompat.getColor(mContext, R.color.color_ED7C00))
                    }

                    "2" -> {
                        helper.setBackgroundRes(R.id.llView3, R.drawable.shape_order_return_2)
                        helper.setBackgroundRes(R.id.ivExceptionStateTag, R.drawable.base_order_return_icon_3)
                        helper.setTextColor(R.id.exceptionStateTag, ContextCompat.getColor(mContext, R.color.color_F44E4E))
                    }

                    "3", "0" -> {
                        helper.setBackgroundRes(R.id.llView3, R.drawable.shape_order_return_1)
                        helper.setBackgroundRes(R.id.ivExceptionStateTag, R.drawable.base_order_return_icon_1)
                        helper.setTextColor(R.id.exceptionStateTag, ContextCompat.getColor(mContext, R.color.color_24BD62))
                    }
                }
            }
            it.setText(
                R.id.tvWaybillGoodsInfo, String.format(
                    mContext.resources.getString(R.string.order_goods_info),
                    item.cargoName,
                    item.slipLoad,
                    when (item.cargoCategory) {
                        "1" -> {
                            "吨"
                        }

                        "2" -> {
                            "方"
                        }

                        else -> {
                            ""
                        }
                    },
                    item.orgBackMoney
                )
            )
            helper.setGone(R.id.jmyl_warning, !item.backRejectReason.isNullOrEmpty())
            if (!TextUtils.isEmpty(item.backRejectReason)) {
                val backPlatform = when (item.dealFlag) {
                    "2" -> {
                        "货主打回，打回原因:"
                    }

                    "3" -> {
                        "平台打回，打回原因:"
                    }

                    else -> {
                        ""
                    }
                }
                helper.setText(R.id.tv_warning_jmyl, backPlatform + item.backRejectReason)
            }
            helper.setGone(R.id.tvWaybillUnitPriceXg, false)
            // 单价模式
            if (item.freightType == "1") {
                helper.setGone(R.id.tvWaybillUnitPrice, true)
                val unitPriceText = String.format(
                    mContext.resources.getString(
                        if (TextUtils.equals(
                                ReturnedOrderConfirmListFragment.TYPE_NEGOTIATING,
                                status
                            )
                        ) {
                            R.string.order_unit_price_info_no
                        } else {
                            R.string.order_unit_price_info
                        }
                    ),
                    item.pbConsignorUnitMoneyAndUnit ?: "0.00"
                )
                it.setText(R.id.tvWaybillUnitPrice, unitPriceText)
                //徐刚单价不含税展示
                if (!TextUtils.isEmpty(item.xgDisplayNoRateUnitMoney)) {
                    if (TextUtils.equals(
                            ReturnedOrderConfirmListFragment.TYPE_NOT_CONFIRM,
                            status
                        ) || TextUtils.equals(
                            ReturnedOrderConfirmListFragment.TYPE_WAITING_PROCESSING,
                            status
                        ) || TextUtils.equals(
                            ReturnedOrderConfirmListFragment.TYPE_CONFIRMED,
                            status
                        )
                    ) {
                        helper.setGone(R.id.tvWaybillUnitPriceXg, true)
                        it.setText(
                            R.id.tvWaybillUnitPriceXg,
                            "单价(不含税)：" + item.xgDisplayNoRateUnitMoney
                        )
                    }
                }
            } else {
                helper.setGone(R.id.tvWaybillUnitPrice, false)
            }
            val styledText = String.format(
                mContext.resources.getString(R.string.order_carrier_info),
                item.carrierCustomerName, item.plateNumber
            )
            it.setText(R.id.tvWaybillDriverInfo, styledText)
            val pictureUrls = item.pictureUrls ?: listOf()
            pictureUrls.isEmpty().let { it ->
                if (it) {
                    helper.setGone(R.id.isvReturnedOrderAllImage, true)
                } else {
                    helper.setVisible(R.id.isvReturnedOrderAllImage, true)
                    val isvReturnedOrderAllImage =
                        helper.getView<ImageSelectView>(R.id.isvReturnedOrderAllImage)
                    val filterList = pictureUrls.filterNotNull()
                    isvReturnedOrderAllImage.imgList =
                        ArrayList(filterList.map { EImage(imageId = it) })
                }
            }
            setOrderStatus(helper, item)
            if (itemBottomBtnVisible) {
                setButtonText(helper, item)
            } else {
                helper.setGone(R.id.btnMiddle, false)
                helper.setGone(R.id.btnRight, false)
            }
            it.addOnClickListener(R.id.tvReturnedOrderCopy)
            it.addOnClickListener(R.id.tv_copy_n)
            it.addOnClickListener(R.id.btnLeft)
            it.addOnClickListener(R.id.btnMiddle)
            it.addOnClickListener(R.id.btnRight)
            it.addOnClickListener(R.id.tvReturnedOrderDetail)
            it.addOnClickListener(R.id.tvgjhf)
            it.addOnClickListener(R.id.tvExpeditingAudits)
            if (item.selected) {
                it.setBackgroundRes(
                    R.id.layoutReturnedOrderItem,
                    R.drawable.order_returned_item_sel
                )
            } else {
                it.setBackgroundRes(
                    R.id.layoutReturnedOrderItem,
                    R.drawable.order_returned_item_unsel
                )
            }

            setReturnOrderStatus(it, item)
        }
    }

    private fun setReturnOrderStatus(helper: BaseViewHolder, item: ReturnedOrderItemBean) {
        item.apply {
            //0,1 待审核 2:回单通过, 4或5:回单异常, 3:回单打回
            when (backCheck) {
                "2" -> {
                    helper.setBackgroundRes(R.id.view_returned_order_status, R.drawable.shape_order_return_1)
                    helper.setBackgroundRes(R.id.iv_returned_order_status, R.drawable.base_order_return_icon_1)
                    helper.setText(R.id.returned_order_status, "平台回单：通过")
                    helper.setTextColor(R.id.returned_order_status, ContextCompat.getColor(mContext, R.color.color_24BD62))
                    helper.setGone(R.id.view_returned_order_status, true)
                }

                "3" -> {
                    helper.setBackgroundRes(R.id.view_returned_order_status, R.drawable.shape_order_return_4)
                    helper.setBackgroundRes(R.id.iv_returned_order_status, R.drawable.base_order_return_icon_2)
                    helper.setText(R.id.returned_order_status, "平台回单：打回")
                    helper.setTextColor(R.id.returned_order_status, ContextCompat.getColor(mContext, R.color.color_666666))
                    helper.setGone(R.id.view_returned_order_status, true)
                }

                "4", "5" -> {
                    helper.setBackgroundRes(R.id.view_returned_order_status, R.drawable.shape_order_return_2)
                    helper.setBackgroundRes(R.id.iv_returned_order_status, R.drawable.base_order_return_icon_3)
                    helper.setText(R.id.returned_order_status, "平台回单：异常")
                    helper.setTextColor(R.id.returned_order_status, ContextCompat.getColor(mContext, R.color.color_F44E4E))
                    helper.setGone(R.id.view_returned_order_status, true)
                }

                "0", "1" -> {
                    helper.setBackgroundRes(R.id.view_returned_order_status, R.drawable.shape_order_return_3)
                    helper.setBackgroundRes(R.id.iv_returned_order_status, R.drawable.base_order_return_icon_4)
                    helper.setText(R.id.returned_order_status, "平台回单：待审核")
                    helper.setTextColor(R.id.returned_order_status, ContextCompat.getColor(mContext, R.color.color_ED7C00))
                    helper.setGone(R.id.view_returned_order_status, true)
                }

                else -> {
                    helper.setGone(R.id.view_returned_order_status, false)
                }
            }
        }
    }

    /**
     * 回单状态文案展示
     */
    private fun setOrderStatus(helper: BaseViewHolder, item: ReturnedOrderItemBean) {

        when (status) {
            ReturnedOrderConfirmListFragment.TYPE_NOT_CONFIRM -> {
                helper.setText(R.id.tvReturnedOrderStatus, "")
            }

            ReturnedOrderConfirmListFragment.TYPE_WAITING_PROCESSING -> {
                helper.setText(R.id.tvReturnedOrderStatus, "")
            }

            ReturnedOrderConfirmListFragment.TYPE_NEGOTIATING -> {
                /**
                 * 1:待承运方确认; 2:待平台介入; 3:货主为重新议价; 4: 货主为待确认;
                 */
                when (item.backOrderReconsiderState) {
                    WAITING_FOR_CONFIRMATION_BY_THE_CARRIER -> {
                        helper.setText(R.id.tvReturnedOrderStatus, "待承运方确认")
                    }

                    WAITING_FOR_PLATFORM_INTERVENTION -> {
                        helper.setText(R.id.tvReturnedOrderStatus, "待平台介入")
                    }

                    WAITING_FOR_THE_OWNER_TO_RENEGOTIATE -> {
                        helper.setText(R.id.tvReturnedOrderStatus, "待货主重新议价")
                    }

                    WAITING_FOR_THE_OWNER_TO_CONFIRM -> {
                        helper.setText(R.id.tvReturnedOrderStatus, "待货主确认")
                    }
                }
            }

            else -> {
                helper.setText(R.id.tvReturnedOrderStatus, "")
            }
        }
    }

    /**
     * 未确认：违约申请，打回
     * 议价中：待平台接入--》违约申请；待货主确认--》违约申请，同意调整；
     * 已确认：无按钮
     * 已打回：无按钮
     * 全部：无按钮
     */
    private fun setButtonText(helper: BaseViewHolder, item: ReturnedOrderItemBean) {
        when (status) {
            ReturnedOrderConfirmListFragment.TYPE_TO_BE_UPLOADED -> {
                helper.setGone(R.id.btnLeft, false)
                    .setGone(R.id.btnRight, false)
                    .setGone(R.id.btnMiddle, false)
                    .setGone(R.id.btnUploadPhoto, true)
                    .addOnClickListener(R.id.btnUploadPhoto)
            }

            ReturnedOrderConfirmListFragment.TYPE_NOT_CONFIRM -> {
                helper.setGone(R.id.btnLeft, false)
                    .setGone(R.id.btnRight, false)
                    .setGone(R.id.btnMiddle, false)
                    .setGone(R.id.btnUploadPhoto, false)
            }

            ReturnedOrderConfirmListFragment.TYPE_WAITING_PROCESSING -> {
                helper.setGone(R.id.btnLeft, false)
                    .setGone(R.id.btnRight, false)
                    .setGone(R.id.btnMiddle, false)
                    .setGone(R.id.btnUploadPhoto, false)
            }

            ReturnedOrderConfirmListFragment.TYPE_NEGOTIATING -> {
                /**
                 * 1:待承运方确认; 2:待平台介入; 3:货主为重新议价; 4: 货主为待确认;
                 */
                when (item.backOrderReconsiderState) {
                    WAITING_FOR_CONFIRMATION_BY_THE_CARRIER -> {
                        //                        helper.setText(R.id.tvReturnedOrderStatus, "待承运方确认")
                        helper.setGone(R.id.btnMiddle, false)
                            .setGone(R.id.btnUploadPhoto, false)
                        if (showApplyBreachOfContractBtn == View.VISIBLE) {
                            helper.setText(
                                R.id.btnRight,
                                ReturnedOrderConfirmListFragment.APPLY_FOR_BREACH_OF_CONTRACT
                            )
                            helper.setGone(R.id.btnRight, true)
                        } else {
                            helper.setGone(R.id.btnRight, false)
                        }
                    }

                    WAITING_FOR_PLATFORM_INTERVENTION -> {
                        //                        helper.setText(R.id.tvReturnedOrderStatus, "待平台介入")
                        helper.setGone(R.id.btnMiddle, false)
                            .setGone(R.id.btnUploadPhoto, false)
                        if (showApplyBreachOfContractBtn == View.VISIBLE) {
                            helper.setText(
                                R.id.btnRight,
                                ReturnedOrderConfirmListFragment.APPLY_FOR_BREACH_OF_CONTRACT
                            )
                            helper.setGone(R.id.btnRight, true)
                        } else {
                            helper.setGone(R.id.btnRight, false)
                        }
                    }

                    WAITING_FOR_THE_OWNER_TO_RENEGOTIATE -> {
                        //                        helper.setText(R.id.tvReturnedOrderStatus, "待货主重新议价")
                        if (showApplyBreachOfContractBtn == View.VISIBLE) {
                            helper.setText(
                                R.id.btnMiddle,
                                ReturnedOrderConfirmListFragment.APPLY_FOR_BREACH_OF_CONTRACT
                            )
                            helper.setGone(R.id.btnMiddle, true)
                        } else {
                            helper.setGone(R.id.btnMiddle, false)
                        }
                        helper.setText(
                            R.id.btnRight,
                            ReturnedOrderConfirmListFragment.RESTART_NEGOTIATE
                        )
                        helper.setGone(R.id.btnUploadPhoto, false)
                    }

                    WAITING_FOR_THE_OWNER_TO_CONFIRM -> {
                        //                        helper.setText(R.id.tvReturnedOrderStatus, "待货主确认")
                        if (showApplyBreachOfContractBtn == View.VISIBLE) {
                            helper.setText(
                                R.id.btnMiddle,
                                ReturnedOrderConfirmListFragment.APPLY_FOR_BREACH_OF_CONTRACT
                            )
                            helper.setGone(R.id.btnMiddle, true)
                        } else {
                            helper.setGone(R.id.btnMiddle, false)
                        }
                        helper.setText(R.id.btnRight, ReturnedOrderConfirmListFragment.AGREE_ADJUST)
                            .setGone(R.id.btnUploadPhoto, false)
                    }

                    else -> {
                        helper.setGone(R.id.btnMiddle, false)
                            .setGone(R.id.btnRight, false)
                            .setGone(R.id.btnUploadPhoto, false)
                    }
                }
            }

            ReturnedOrderConfirmListFragment.TYPE_REJECTED -> {
                helper.setGone(R.id.btnMiddle, false)
                    .setGone(R.id.btnUploadPhoto, false)
                if (item.consignorCanUploadImg == "1") {
                    helper.setGone(R.id.btnRight, true)
                    helper.setText(R.id.btnRight, UPLOAD_RETURNED_ORDER_PHOTO)
                } else {
                    helper.setGone(R.id.btnRight, false)
                }
            }

            else -> {
                helper.setGone(R.id.btnMiddle, false)
                helper.setGone(R.id.btnRight, false)
                    .setGone(R.id.btnUploadPhoto, false)
            }
        }
    }

    fun selectAll() {
        data.forEach {
            it.selected = true
        }
        notifyDataSetChanged()
    }

    fun unSelectAll() {
        data.forEach {
            it.selected = false
        }
        notifyDataSetChanged()
    }

    companion object {
        private const val WAITING_FOR_CONFIRMATION_BY_THE_CARRIER = "1"
        private const val WAITING_FOR_PLATFORM_INTERVENTION = "2"
        private const val WAITING_FOR_THE_OWNER_TO_RENEGOTIATE = "3"
        private const val WAITING_FOR_THE_OWNER_TO_CONFIRM = "4"
    }
}