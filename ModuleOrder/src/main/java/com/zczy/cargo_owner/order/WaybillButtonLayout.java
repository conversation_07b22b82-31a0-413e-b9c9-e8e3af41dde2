package com.zczy.cargo_owner.order;

import android.content.Context;
import android.graphics.Color;
import android.graphics.drawable.ColorDrawable;
import androidx.constraintlayout.widget.ConstraintLayout;
import android.text.TextUtils;
import android.util.AttributeSet;
import android.util.SparseArray;
import android.view.Gravity;
import android.view.View;
import android.view.ViewGroup;
import android.widget.BaseAdapter;
import android.widget.ImageView;
import android.widget.ListView;
import android.widget.PopupWindow;
import android.widget.TextView;
import com.zczy.cargo_owner.order.entity.EWaybill;

import java.util.ArrayList;
import java.util.HashMap;
import java.util.Iterator;
import java.util.LinkedHashMap;
import java.util.List;
import java.util.Map;

public class WaybillButtonLayout extends ConstraintLayout implements View.OnClickListener {

    public interface OnButtonClickListener {
        void onButtonClick(View view, String title, EWaybill waybill, int position);
    }

    public interface OnCallBackItem{
        void backItem(WaybillButtonLayout bts,TextView item, String title);
    }
    private TextView[] tvList;
    private int position = 0;
    private EWaybill waybill;
    private OnButtonClickListener onButtonClickListener;
    private List<String> showName;
    private Map<String,Boolean> KeyVue = new LinkedHashMap<>(7);
    private ImageView iv_read_icon;
    private OnCallBackItem sigleOnCallBackItem;

    public WaybillButtonLayout(Context context) {
        super(context);
        init();
    }

    public WaybillButtonLayout(Context context, AttributeSet attrs) {
        super(context, attrs);
        init();
    }

    public WaybillButtonLayout(Context context, AttributeSet attrs, int defStyleAttr) {
        super(context, attrs, defStyleAttr);
        init();
    }


    private void init() {
        inflate(getContext(), R.layout.order_waybill_button_layout, this);
        TextView tv_one = findViewById(R.id.tv_one);
        TextView tv_twon = findViewById(R.id.tv_twon);
        TextView tv_three = findViewById(R.id.tv_three);
        TextView tv_four = findViewById(R.id.tv_four);
        TextView tv_more = findViewById(R.id.tv_more);
        iv_read_icon  = findViewById(R.id.iv_read_icon);

        tv_one.setOnClickListener(this);
        tv_twon.setOnClickListener(this);
        tv_three.setOnClickListener(this);
        tv_four.setOnClickListener(this);
        tv_more.setOnClickListener(this);

        tvList = new TextView[]{tv_one, tv_twon, tv_three, tv_four, tv_more};
    }

    public void setGoneView() {
        for (TextView tv:tvList){
            tv.setVisibility(GONE);
        }
        iv_read_icon.setVisibility(GONE);
        setVisibility(GONE);
    }


    public void show(Map<String,Boolean> map) {

        if (this.showName == null) {
            this.showName = new ArrayList<>(5);
        }
        this.showName.clear();

        boolean show = false;
        int indexTrue = 0;
        Iterator<Map.Entry<String,Boolean>> iterator =  map.entrySet().iterator();

        while (iterator.hasNext()){
            Map.Entry<String,Boolean> data =  iterator.next();
            if (data.getValue().booleanValue()){
                show = true;
                if (indexTrue < 4) {
                    //正常模式
                    tvList[indexTrue].setVisibility(VISIBLE);
                    tvList[indexTrue].setText(data.getKey());

                    this.handSpecial(tvList[indexTrue],data.getKey());

                } else if (indexTrue == 4) {

                    //隐藏第4个View
                    TextView tv = tvList[3];
                    tv.setVisibility(GONE);
                    this.showName.add(tv.getText().toString());

                    //显示更多View
                    tvList[4].setVisibility(VISIBLE);
                    this.showName.add(data.getKey());

                    //this.handSpecial(tvList[4],data.getKey());
                } else {
                    //更多模式
                    this.showName.add(data.getKey());
                }
                indexTrue++;
            }
        }
        setVisibility(show?VISIBLE:GONE);
    }

    private void handSpecial(TextView tv,String key){
        if (sigleOnCallBackItem != null && !TextUtils.isEmpty(specialKey.get(key.hashCode())) ){
            //需要外部特殊
            sigleOnCallBackItem.backItem(this,tv,key);
        }
    }

    private SparseArray<String> specialKey = new SparseArray<>(4);

    public WaybillButtonLayout putSpecialKey(String key){
        specialKey.put(key.hashCode(),key);
        return this;
    }

    public void setSigleOnCallBackItem(OnCallBackItem sigleOnCallBackItem) {
        this.sigleOnCallBackItem = sigleOnCallBackItem;
    }

    /***
     * 在目标按钮右上角显示红色点标签
     */
    public void showRed(View tagView){

        LayoutParams params = (LayoutParams) iv_read_icon.getLayoutParams();
        params.rightToRight = tagView.getId();
        iv_read_icon.setLayoutParams(params);
        iv_read_icon.setVisibility(VISIBLE);
    }

    public void setAdapterPosition(EWaybill waybill, int position, OnButtonClickListener onButtonClickListener) {
        this.position = position;
        this.waybill = waybill;
        this.onButtonClickListener = onButtonClickListener;
    }

    @Override
    public void onClick(View v) {
        if (this.onButtonClickListener == null) {
            return;
        }
        if (v == tvList[4]) {
            new SelectButtonView(getContext(), showName).showAsDropDown(v);
        } else {
            TextView tv = (TextView) v;
            this.onButtonClickListener.onButtonClick(v, tv.getText().toString().trim(), waybill, position);
        }
    }

    public Map<String, Boolean> getKeyVue() {
        sigleOnCallBackItem= null;
        specialKey.clear();
        KeyVue.clear();
        return KeyVue;
    }

    public void setBttextBackgradeColor(String name,int color,int backgrade){
        for (TextView tv: tvList){
            if (TextUtils.equals(name,tv.getText())){
                tv.setTextColor(color);
                tv.setBackgroundResource(backgrade);
                return;
            }
        }
    }

    class SelectButtonView extends PopupWindow implements OnClickListener {
        SelectButtonView(Context context, List<String> showName) {
            super(context);

            View view = View.inflate(context, R.layout.order_waybill_button_view, null);
            this.setContentView(view);
            this.setBackgroundDrawable(new ColorDrawable(0x0000));
            this.setClippingEnabled(false);
            this.setFocusable(true);
            this.setTouchable(true);
            this.setOutsideTouchable(true);
            this.setWidth(ViewGroup.LayoutParams.WRAP_CONTENT);
            this.setHeight(ViewGroup.LayoutParams.WRAP_CONTENT);

            ListView lv = view.findViewById(R.id.lv);
            lv.setAdapter(new ItemAdapter(context, showName, SelectButtonView.this));
        }

        @Override
        public void showAsDropDown(View anchor) {
            super.showAsDropDown(anchor, -95, 0);
        }

        @Override
        public void onClick(View v) {
            super.dismiss();
            TextView tv = (TextView) v;
            WaybillButtonLayout.this.onButtonClickListener.onButtonClick(v, tv.getText().toString().trim(), WaybillButtonLayout.this.waybill, WaybillButtonLayout.this.position);
        }


        class ItemAdapter extends BaseAdapter {
            Context context;
            List<String> showName;
            OnClickListener onClickListener;

            ItemAdapter(Context context, List<String> showName, OnClickListener onClickListener) {
                super();
                this.context = context;
                this.showName = showName;
                this.onClickListener = onClickListener;
            }

            @Override
            public int getCount() {
                return showName.size();
            }

            @Override
            public String getItem(int position) {
                return showName.get(position);
            }

            @Override
            public long getItemId(int position) {
                return position;
            }

            @Override
            public View getView(int position, View convertView, ViewGroup parent) {
                TextView textView = new TextView(context);
                textView.setPadding(7, 5, 5, 7);
                textView.setTextSize(12.0f);
                textView.setGravity(Gravity.CENTER);
                textView.setTextColor(Color.WHITE);
                textView.setText(getItem(position));
                textView.setOnClickListener(this.onClickListener);
                return textView;
            }
        }
    }
}
