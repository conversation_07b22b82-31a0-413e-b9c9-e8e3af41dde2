package com.zczy.cargo_owner.order.reminder.adapter

import android.text.TextUtils
import com.chad.library.adapter.base.BaseQuickAdapter
import com.chad.library.adapter.base.BaseViewHolder
import com.zczy.cargo_owner.order.R
import com.zczy.cargo_owner.order.reminder.req.RspQueryOrderRiskEventList
import com.zczy.cargo_owner.order.reminder.req.showTypeV2

/**
 *  user: ssp
 *  time: 2021/7/14 13:59
 *  desc: 运输提醒-合规风险
 */

class TransportReminderHomeAdapterV3 : BaseQuickAdapter<RspQueryOrderRiskEventList, BaseViewHolder>(R.layout.transport_reminder_home_item_v3) {
    override fun convert(helper: BaseViewHolder, item: RspQueryOrderRiskEventList) {
        helper.apply {
            setText(R.id.tvTitle, item.showTypeV2())
            setText(R.id.tvCarrier, "承运人：" + item.driverUserName)
            setText(R.id.tvPlateNumber, item.plateNumber)
            setText(R.id.tvOrderId, item.orderId)
            setVisible(R.id.tvRemark, !TextUtils.isEmpty(item.remark))
            setText(R.id.tvRemark, "备注说明:" + item.remark)
            addOnClickListener(R.id.tvDelete)
            addOnClickListener(R.id.tvOrderId)
        }
    }
}