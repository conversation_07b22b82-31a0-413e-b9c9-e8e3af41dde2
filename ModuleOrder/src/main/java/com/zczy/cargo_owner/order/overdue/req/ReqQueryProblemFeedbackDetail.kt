package com.zczy.cargo_owner.order.overdue.req

import com.zczy.comm.http.entity.BaseNewRequest
import com.zczy.comm.http.entity.BaseRsp
import com.zczy.comm.http.entity.ResultData

/**
 * PS: 逾期运单管理 已提交问题反馈详情查询
 * http://wiki.zczy56.com/pages/viewpage.action?pageId=91586892
 * Created by zzf
 */
data class ReqQueryProblemFeedbackDetail(
    var feedbackId: String = "",
) : BaseNewRequest<BaseRsp<RspQueryProblemFeedbackDetail>>("oms-app/order/overDueApp/queryProblemFeedbackDetail")

data class RspQueryProblemFeedbackDetail(
    /** 运单号 */
    var orderId: String = "",
    /** 问题反馈编码 */
    var feedbackCode: String = "",
    //问题反馈类型
    var feedbackType: String = "",
    /** 问题反馈类型名称 */
    var feedbackTypeStr: String = "",
    /** 问题描述 */
    var description: String = "",
    /** 状态 */
    var state: String = "",
    /** 状态名称 */
    var stateStr: String = "",
    /** 反馈人ID */
    var feedbackUserId: String = "",
    /** 反馈人姓名 */
    var feedbackUserRealName: String = "",
    /** 反馈时间(yyyy-MM-dd HH:mm:ss)
     */
    var feedbackTime: String = "",
    /** 反馈图片URL列表 */
    var feedbackImgUrlList: List<String>? = null,
    /** 承运人姓名 */
    var carrierUserRealName: String = "",
    /** 承运人车牌号 */
    var carrierPlateNumber: String = "",

    ) : ResultData()

