package com.zczy.cargo_owner.order.detail.adapter;

import android.graphics.Color;
import android.view.View;
import android.widget.TextView;

import com.chad.library.adapter.base.BaseQuickAdapter;
import com.chad.library.adapter.base.BaseViewHolder;
import com.zczy.cargo_owner.order.R;
import com.zczy.cargo_owner.order.detail.bean.EWaybillStatus;

/**
 * 功能描述:运单状态
 *
 * <AUTHOR> 孙飞虎
 * @company 中储智运科技股份有限公司
 * @copyright （版权）中储南京智慧物流科技有限公司所有
 * @date 2019/1/31
 */
public class WaybillStatusAdapter extends BaseQuickAdapter<EWaybillStatus, BaseViewHolder> {

    public WaybillStatusAdapter() {

        super(R.layout.order_waybill_status_adapter);
    }

    @Override
    protected void convert(BaseViewHolder helper, EWaybillStatus item) {

        helper.setText(R.id.tv_time, item.getTime());
        helper.setText(R.id.tv_title, item.getText());
        helper.setText(R.id.tv_lable, item.getLable());
        helper.setImageResource(R.id.iv_icon, item.getIcon());

        helper.addOnClickListener(R.id.tv_lable);
        TextView tv_line = helper.getView(R.id.tv_line);
        int position = helper.getAdapterPosition();
        tv_line.setVisibility(position == getItemCount() -1?View.GONE:View.VISIBLE);
        tv_line.setBackgroundColor(Color.parseColor(item.isOK()?"#5086FC":"#ffe3e3e3"));

    }
}
