package com.zczy.cargo_owner.order

import android.annotation.SuppressLint
import android.os.Bundle
import android.text.Editable
import android.text.InputFilter
import android.text.TextUtils
import android.text.TextWatcher
import android.view.View
import com.sfh.lib.utils.UtilTool
import com.zczy.comm.ui.BaseDialog
import com.zczy.comm.utils.ex.setVisible
import kotlinx.android.synthetic.main.waybill_custom_numbering_dialog.*
import kotlinx.android.synthetic.main.waybill_custom_numbering_dialog.edit_code
import kotlinx.android.synthetic.main.waybill_custom_numbering_dialog.tv_cancel
import kotlinx.android.synthetic.main.waybill_custom_numbering_dialog.tv_sure
import kotlinx.android.synthetic.main.waybill_tms_edit_money_dialog.*

/**
 * 类描述：TMS 货源修改运费
 */
class TMSEditOrderMoneyDialog : BaseDialog() {
    private var callBack: Callback? = null
    var orderId: String?="";
    var consignorMoney:String?="";

    @SuppressLint("SetTextI18n")
    override fun bindView(view: View, bundle: Bundle?) {
        reAdjustView(35, 0)
        tv_old_money.text = "原运价："+consignorMoney+"元"
        edit_code.setText(consignorMoney?:"")

        tv_cancel.setOnClickListener {
            dismiss()
        }
        tv_sure.setOnClickListener {
            val text = edit_code.text.toString().trim()
            this.dismiss()
            callBack?.onClick(orderId,text)
        }
       UtilTool.setEditTextInputSize(edit_code,2);
    }

    override fun getDialogTag(): String {
        return "TMSEditOrderMoneyDialog"
    }

    override fun getDialogLayout(): Int {
        return R.layout.waybill_tms_edit_money_dialog
    }

    companion object {
        @JvmStatic
        fun instance(orderId: String?, consignorMoney:String?, callBack: Callback): TMSEditOrderMoneyDialog {
            val dialog = TMSEditOrderMoneyDialog()
            dialog.orderId = orderId
            dialog.consignorMoney = consignorMoney.toString()
            dialog.callBack = callBack
            return dialog
        }
    }

    interface Callback {
        fun onClick(orderId: String?,newMoney: String)
    }
}