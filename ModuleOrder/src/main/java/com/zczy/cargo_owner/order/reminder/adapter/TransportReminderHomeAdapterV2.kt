package com.zczy.cargo_owner.order.reminder.adapter

import com.chad.library.adapter.base.BaseQuickAdapter
import com.chad.library.adapter.base.BaseViewHolder
import com.zczy.cargo_owner.order.R
import com.zczy.cargo_owner.order.reminder.req.RspQueryOrderRiskEventList
import com.zczy.cargo_owner.order.reminder.req.showTypeV1

/**
 *  user: ssp
 *  time: 2021/7/14 13:59
 *  desc: 运输提醒-运输风险
 */

class TransportReminderHomeAdapterV2 : BaseQuickAdapter<RspQueryOrderRiskEventList, BaseViewHolder>(R.layout.transport_reminder_home_item_v2) {
    override fun convert(helper: BaseViewHolder, item: RspQueryOrderRiskEventList) {
        helper.apply {
            setText(R.id.tvTitle, item.showTypeV1())
            setText(R.id.tvCarrier, "承运人：" + item.driverUserName)
            setText(R.id.tvPlateNumber, item.plateNumber)
            setText(R.id.tvOrderId, item.orderId)
            addOnClickListener(R.id.tvDelete)
            addOnClickListener(R.id.tvOrderId)
        }
    }
}