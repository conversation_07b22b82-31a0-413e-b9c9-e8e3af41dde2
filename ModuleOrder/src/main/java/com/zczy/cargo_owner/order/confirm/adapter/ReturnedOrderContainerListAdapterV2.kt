package com.zczy.cargo_owner.order.confirm.adapter

import com.chad.library.adapter.base.BaseQuickAdapter
import com.chad.library.adapter.base.BaseViewHolder
import com.zczy.cargo_owner.order.R
import com.zczy.cargo_owner.order.confirm.bean.RspReturnedOrderContainerListV2

/**
 * 功能描述: 发货详情
 * <AUTHOR>
 * @date 2022/3/8-20:02
 */

class ReturnedOrderContainerListAdapterV2 :
    BaseQuickAdapter<RspReturnedOrderContainerListV2, BaseViewHolder>(
        R.layout.returned_order_container_list_item_v2
    ) {
    override fun convert(helper: BaseViewHolder?, item: RspReturnedOrderContainerListV2?) {
        helper?.let {
            item?.apply {
                it.setText(R.id.tvContainerNo, containerListNo)
                    .setText(R.id.tvContainerWeight, containerListNoCount + "箱")
            }
        }
    }
}