package com.zczy.cargo_owner.order.express.adapter

import android.widget.ImageView
import com.chad.library.adapter.base.BaseQuickAdapter
import com.chad.library.adapter.base.BaseViewHolder
import com.zczy.cargo_owner.order.R
import com.zczy.cargo_owner.order.express.req.ListData

/**
 * 功能描述: 扫码签收结果
 * <AUTHOR>
 * @date 2022/5/12-14:39
 */

class BoundSuccessAdapter : BaseQuickAdapter<ListData, BaseViewHolder>(R.layout.item_bund_success) {

    override fun convert(helper: BaseViewHolder, item: ListData) {
        helper.setText(R.id.tv_bound_code, item.poundOrderNum)
        val ivScanStatus = helper.getView<ImageView>(R.id.iv_scan_status)
        if (item.type == 0) {
            ivScanStatus.setBackgroundResource(R.drawable.icon_failed_small)
        } else {
            ivScanStatus.setBackgroundResource(R.drawable.icon_success_small)
        }
        when (item.errCode) {
            "1" -> {
                //重复扫码
                helper.setGone(R.id.ivRepeatScan, true)
            }
            else -> {
                helper.setGone(R.id.ivRepeatScan, false)
            }
        }
    }
}