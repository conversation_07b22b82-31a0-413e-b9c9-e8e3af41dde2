package com.zczy.cargo_owner.order.overdue.req

import android.text.TextUtils
import com.zczy.comm.http.entity.BaseNewRequest
import com.zczy.comm.http.entity.BaseRsp
import com.zczy.comm.http.entity.PageList
import com.zczy.comm.utils.ex.isTrue

/**
 * PS: 逾期运单管理 列表查询
 * http://wiki.zczy56.com/pages/viewpage.action?pageId=19169318
 * Created by sdx on 2019-06-25.
 */
data class ReqQueryOrderOverDueList(
    /** 处理状态（1--待处理，2--处理完成） */
    var dealType: String,
    var nowPage: Int = 1,
    var pageSize: Int = 10,
    var remarks: String? = null,
    var limitPublishFlag: String? = null //是否限制发单 1-是 0否
) : BaseNewRequest<BaseRsp<PageList<OrderOverDueItem>>>("oms-app/order/overDueApp/queryOrderOverDueList")

data class OrderOverDueItem(
    /** 运单号 */
    var orderId: String = "",
    /** 原合作运力范围承运方（0-否，1-是） */
    var specifyFlag: String = "",
    //0 2  2就是 是
    var specifyType: String = "",
    /** 承运方 */
    var carrierCustomerName: String = "",
    /** 承运方手机号 */
    var carrierMobile: String = "",
    /** 车牌号 */
    var plateNumber: String = "",
    /** 逾期原因 */
    var remarks: String = "",
    /** 逾期天数 */
    var orderDate: String = "",
    /** 倒计时 */
    var countDown: String = "",
    /** 状态 */
    var orderState: String = "",
    /** 是否展示违约记录 */
    var defaultRecordFlag: String = "",
    /** 处理结果（已完成列表展示）*/
    var dealResult: String = "",
    var backStatus: String = "",
    var consignorState: String = "",
    var settleApplyPlag: String = "",
    var appealButtonFlag: String = "", // 是否展示申诉按钮 0-否 1-是	string	否
    var appealDetailButtonFlag: String = "", // 是否展示申诉详情按钮 0-否 1-是	string 否
    var appealOverTimeFlag: String = "", // 是否申诉过期 0-否 1-是
    var noResponsibilityFlag: String = "", // 是否零担 0-否 1-是
    var businessSource: String = "",//业务来源：0 网络货运 1 多式联运 2商贸质押单
    var limitPublishFlag: String? = null, //是否限制发单 1-是 0否
    var transactionType: String = "", // 结算方式 0:现结,1:账期
    var problemFeedbackFlag: String = "", // 是否问题反馈过 0 否  1 是
) {
    var isCheck = false
}

fun OrderOverDueItem.formatSpecifyFlag(): String {
    return if (specifyFlag.isTrue) {
        "是"
    } else {
        "否"
    }
}

fun OrderOverDueItem.formatSpecifyFlagOne(): String {
    return if (TextUtils.equals("2",specifyType)) {
        "是"
    } else {
        "否"
    }
}
