package com.zczy.cargo_owner.order.qrcode

import android.Manifest
import android.R.attr.path
import android.content.Context
import android.content.Intent
import android.graphics.Bitmap
import android.graphics.Bitmap.CompressFormat
import android.graphics.BitmapFactory
import android.graphics.Canvas
import android.graphics.Color
import android.net.Uri
import android.os.Bundle
import android.os.Environment
import android.provider.MediaStore
import android.text.SpannableString
import android.text.SpannableStringBuilder
import android.text.Spanned
import android.text.TextUtils
import android.text.style.AbsoluteSizeSpan
import android.text.style.ImageSpan
import android.view.View
import com.google.zxing.BarcodeFormat
import com.google.zxing.EncodeHintType
import com.google.zxing.MultiFormatWriter
import com.google.zxing.common.BitMatrix
import com.jaeger.library.StatusBarUtil
import com.sfh.lib.AppCacheManager
import com.sfh.lib.mvvm.annotation.LiveDataMatch
import com.zczy.cargo_owner.libcomm.config.HttpConfig
import com.zczy.cargo_owner.libcomm.widget.CheckSelfPermissionDialog
import com.zczy.cargo_owner.order.R
import com.zczy.cargo_owner.order.entity.EWaybill
import com.zczy.comm.CommServer
import com.zczy.comm.SpannableHepler
import com.zczy.comm.file.IFileServer
import com.zczy.comm.permission.PermissionCallBack
import com.zczy.comm.permission.PermissionUtil
import com.zczy.comm.pluginserver.AMainServer
import com.zczy.comm.ui.BaseActivity
import com.zczy.comm.utils.dp2px
import com.zczy.comm.utils.ex.loadUrl
import com.zczy.comm.utils.ex.setVisible
import com.zczy.comm.utils.getResDrawable
import com.zczy.comm.utils.json.toJsonObject
import com.zczy.comm.widget.span.MyImageSpan
import com.zczy.lib_zshare.ZShare
import com.zczy.lib_zshare.share.ShareInfo
import kotlinx.android.synthetic.main.order_waybill_qr_code_activity.*
import java.io.File
import java.io.FileNotFoundException
import java.io.FileOutputStream
import java.util.*


/**
 * PS:
 * <AUTHOR> by sdx on 2020/6/1.
 */
class WaybillQRCodeActivity : BaseActivity<WaybillQRCodeModel>() {

    private val eHugeOrderId by lazy {
        intent.getStringExtra(EXTRA_JSON) ?: ""
    }

    override fun getLayout(): Int = R.layout.order_waybill_qr_code_activity

    override fun initStatus() {
        StatusBarUtil.setTranslucentForImageViewInFragment(this, 0, btn_back)
    }

    override fun bindView(bundle: Bundle?) {
        bindClickEvent(btn_back)
        // 下载二维码
        bindClickEvent(btn_down_qr)
        // 分享
        bindClickEvent(btn_share)

        tv_hz_info.setText(
            SpannableHepler("或下载二维码，登录")
                .append(SpannableHepler.Txt("中储智运APP", "#FF4601"))
                .append("在“首页”右上角扫码识别")
                .builder()
        )
    }

    override fun initData() {
        viewModel?.queryHugeOrderDetailQrcodeForApp(eHugeOrderId)


    }

    override fun onSingleClick(v: View) {
        super.onSingleClick(v)
        when (v.id) {
            R.id.btn_back -> {
                finish()
            }

            R.id.btn_down_qr -> {
                CheckSelfPermissionDialog.storagePermissionDialog(this@WaybillQRCodeActivity, object : PermissionCallBack() {
                    override fun onHasPermission() {
                        PermissionUtil.checkPermissions(
                            context = this@WaybillQRCodeActivity,
                            tips = "请允许读写存储的权限",
                            permissions = arrayOf(
                                Manifest.permission.WRITE_EXTERNAL_STORAGE,
                                Manifest.permission.READ_EXTERNAL_STORAGE
                            ),
                            callback = object : PermissionCallBack() {
                                override fun onHasPermission() {
                                    val bitmap2 = Bitmap.createBitmap(
                                        ll_qr.getWidth(),
                                        ll_qr.getHeight(),
                                        Bitmap.Config.ARGB_8888
                                    )
                                    val canvas = Canvas()
                                    canvas.setBitmap(bitmap2)
                                    ll_qr.draw(canvas)
                                    val saveImageToGallery =
                                        saveImageToGallery(this@WaybillQRCodeActivity, bitmap2)
                                    if (saveImageToGallery != null) {
                                        showToast("保存成功,请在图库里查看,保存路径 : ${saveImageToGallery.absolutePath}")
                                    } else {
                                        showToast("保存失败,请重试")
                                    }
                                }
                            },
                            requestCode = 0x22
                        )
                    }
                })


            }

            R.id.btn_share -> {
                CheckSelfPermissionDialog.storagePermissionDialog(this@WaybillQRCodeActivity, object : PermissionCallBack() {
                    override fun onHasPermission() {
                        PermissionUtil.checkPermissions(
                            context = this@WaybillQRCodeActivity,
                            tips = "请允许读写存储的权限",
                            permissions = arrayOf(
                                Manifest.permission.WRITE_EXTERNAL_STORAGE,
                                Manifest.permission.READ_EXTERNAL_STORAGE
                            ),
                            callback = object : PermissionCallBack() {
                                override fun onHasPermission() {
                                    val bitmap2 = Bitmap.createBitmap(
                                        ll_qr.width,
                                        ll_qr.height,
                                        Bitmap.Config.ARGB_8888
                                    )
                                    val canvas = Canvas()
                                    canvas.setBitmap(bitmap2)
                                    ll_qr.draw(canvas)
                                    val saveImageToGallery = saveImageToGallery(this@WaybillQRCodeActivity, bitmap2)
                                    if (saveImageToGallery != null) {
//                                        CommServer.getFileServer().update(saveImageToGallery, object : IFileServer.OnFileUploaderListener {
//                                            override fun onSuccess(tag: File?, url: String?) {
//
//                                                goodsItem?.let {
//                                                    val info = ShareInfo()
//                                                    info.title = "货主邀请您摘单"
//                                                    //【启运地】-【目的地】（这里取区到区的地址）,货物名称，运单标识（以上分别以逗号隔开
//                                                    if (TextUtils.isEmpty(it.orderMark)) {
//                                                        info.content = String.format("【%s】-【%s】，%s", it.formatStartAddress2(), it.formatEndAddress2(), it.allCargoName)
//                                                    } else {
//                                                        info.content = String.format("【%s】-【%s】，%s，%s", it.formatStartAddress2(), it.formatEndAddress2(), it.allCargoName, it.orderMark)
//                                                    }
//                                                    info.thumbnail = BitmapFactory.decodeResource(resources, R.drawable.ic_launcher);
//                                                    info.webUrl = HttpConfig.getUrlImage(url)
//                                                    ZShare.share(this@WaybillQRCodeActivity, info)
//                                                }
//                                            }
//
//                                            override fun onFailure(tag: File?, error: String?) {
//                                                showToast(error)
//                                            }
//                                        })
                                        goodsItem?.let {
                                            val info = ShareInfo()
                                            info.title = "货主邀请您摘单"
                                            //【启运地】-【目的地】（这里取区到区的地址）,货物名称，运单标识（以上分别以逗号隔开
                                            if (TextUtils.isEmpty(it.orderMark)) {
                                                info.content = String.format("【%s】-【%s】，%s", it.formatStartAddress2(), it.formatEndAddress2(), it.allCargoName)
                                            } else {
                                                info.content = String.format("【%s】-【%s】，%s，%s", it.formatStartAddress2(), it.formatEndAddress2(), it.allCargoName, it.orderMark)
                                            }
                                            info.thumbnail = BitmapFactory.decodeResource(resources, R.drawable.ic_launcher);
                                            info.shareType = "TYPE_IMAGE"
                                            info.shareImageBitmapAlong = bitmap2
                                            ZShare.share(this@WaybillQRCodeActivity, info)
                                        }
                                    } else {
                                        showToast("分享失败,请重试")
                                    }
                                }
                            },
                            requestCode = 0x22
                        )
                    }
                })

            }
        }
    }

    override fun attachBaseContext(newBase: Context?) {
        super.attachBaseContext(MyContextWrapper.wrap(newBase))
    }

    private fun saveImageToGallery(context: Context, bmp: Bitmap): File? {
        val appDir = File(AppCacheManager.getFileCache(), "QR_Code")
        if (!appDir.exists()) {
            appDir.mkdirs()
        }
        val fileName = System.currentTimeMillis().toString() + ".jpg"
        val file = File(appDir, fileName)
        try {
            val fos = FileOutputStream(file)
            bmp.compress(CompressFormat.JPEG, 100, fos)
            fos.flush()
            fos.close()
        } catch (e: Exception) {
            e.printStackTrace()
            return null
        }
        try {
            MediaStore.Images.Media.insertImage(
                context.contentResolver,
                file.absolutePath, fileName, null
            )
        } catch (e: FileNotFoundException) {
            e.printStackTrace()
            return null
        }
        context.sendBroadcast(
            Intent(
                Intent.ACTION_MEDIA_SCANNER_SCAN_FILE,
                Uri.parse("file://$path")
            )
        )
        return file
    }

    var goodsItem: GoodsItem? = null

    @LiveDataMatch
    open fun onQueryHugeOrderDetailQrcodeForApp(data: RspQueryHugeOrderDetailQrcodeForApp) {

        img_qr.loadUrl(data.qrCodeUrl.replace("https://img", "http://image"))

        goodsItem = data.hugeOrderInfo
        goodsItem?.let {
            // 启运地
            tv_qiyundi.text = it.formatStartAddress()
            // 目的地
            tv_mudidi.text = it.formatEndAddress()
            //发货单位
            tvName1.text = it.despatchCompanyName
            tvName1.setVisible(it.despatchCompanyName.isNotEmpty())
            // 货物名称
            tv_huowumingcheng.text = it.allCargoName
            tv_hz_name.text = it.consignorCompany
            //收货单位
            tvName2.text = it.deliverCompanyName
            tvName2.setVisible(it.deliverCompanyName.isNotEmpty())
            //运单标识
            tvOrderMark.text = it.orderMark
            tvOrderMark.setVisible(it.orderMark.isNotEmpty())
        }

    }


    companion object {
        private const val EXTRA_JSON = "extra_json"

        @JvmStatic
        fun start(context: Context?, eHugeOrderId: String) {
            context ?: return
            val intent = Intent(context, WaybillQRCodeActivity::class.java)
            intent.putExtra(EXTRA_JSON, eHugeOrderId)
            context.startActivity(intent)
        }
    }


}