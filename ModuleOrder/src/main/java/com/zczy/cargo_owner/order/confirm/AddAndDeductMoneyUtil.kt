package com.zczy.cargo_owner.order.confirm

import android.annotation.SuppressLint
import com.zczy.cargo_owner.order.confirm.bean.RspAddAndDeductMoney
import com.zczy.cargo_owner.order.confirm.bean.RspAddAndDeductMoney.Companion.TYPE_1
import com.zczy.cargo_owner.order.confirm.bean.RspAddAndDeductMoney.Companion.TYPE_2
import com.zczy.comm.utils.NumUtil

/**
 *描述：加扣款相关计算
 *auth:宋双朋
 *time:2024/5/29 15:39
 */
fun MutableList<RspAddAndDeductMoney>?.computeMoney(): Double {
    var money = 0.00
    this?.forEach {
        when (it.type) {
            TYPE_1 -> {
                //加款
                money = NumUtil.sum(money, it.money?.toDoubleOrNull() ?: 0.00)
            }

            TYPE_2 -> {
                //扣款
                money = NumUtil.sub(money, it.money?.toDoubleOrNull() ?: 0.00)
            }
        }
    }
    return money
}

@SuppressLint("DefaultLocale")
fun Double.roundToFourDecimals4Point(): Double {
    return String.format("%.4f", this).toDoubleOrNull() ?: 0.0000
}

@SuppressLint("DefaultLocale")
fun Double.roundToFourDecimals2Point(): Double {
    return String.format("%.2f", this).toDoubleOrNull() ?: 0.00
}