package com.zczy.cargo_owner.order.consignor.widget

import android.content.Context
import android.text.TextUtils
import android.util.AttributeSet
import android.view.LayoutInflater
import androidx.constraintlayout.widget.ConstraintLayout
import com.zczy.cargo_owner.order.R
import com.zczy.cargo_owner.order.consignor.req.*
import com.zczy.comm.utils.ex.setVisible
import kotlinx.android.synthetic.main.order_consignor_confirm_order_view.view.*

/** 功能描述:
 * 发货单确认 订单信息 view
 * <AUTHOR>
 * @company 中储智运科技股份有限公司
 * @copyright （版权）中储南京智慧物流科技有限公司所有
 * @date 2019/4/3
 */
class OrderConsignorConfirmOrderView :
    ConstraintLayout {

    constructor(context: Context) : super(context) {
        init()
    }

    constructor(context: Context, attrs: AttributeSet?) : super(context, attrs) {
        init()
    }

    constructor(context: Context, attrs: AttributeSet?, defStyleAttr: Int) : super(context, attrs, defStyleAttr) {
        init()
    }

    private fun init() {
        LayoutInflater.from(context).inflate(R.layout.order_consignor_confirm_order_view, this)
    }

    fun setData(item: Rsp1QueryConsignorOrderStateOfMobile) {
        // 订单类型
        // 订单类型	订单类型：0 抢单,1 议价
        if (!TextUtils.isEmpty(item.orderCarpoolingId)) {
            img_order_model.setVisible(true)
            img_order_model.setImageResource(R.drawable.deliver_order_nil_tag)
        } else {
            when (item.orderModel) {
                "0" -> {
                    img_order_model.setVisible(true)
                    img_order_model.setImageResource(R.drawable.base_flag_1)
                }
                "1" -> {
                    img_order_model.setVisible(true)
                    img_order_model.setImageResource(R.drawable.base_flag_2)
                }
                else -> {
                    img_order_model.setVisible(false)
                }
            }
        }

        // 起点
        tv_origin.text = item.formatStartAddress()
        // 终点
        tv_end.text = item.formatEndAddress()
        // 实际数量
        tv_deliver_weight.text = "实际数量：${item.formatDeliverWeight()}"
        // 运费
        tv_consignor_money.text = "运费：${item.pbConsignorMoneyStr}元"
        // 优先支付比例
        tv_advance_ratio.text = "优先支付比例：${item.advanceRatio}%"
        // 优先支付金额
        tv_consignor_advance_money.text = "优先支付金额：${item.consignorAdvanceMoney}元"
        // 承运人 车牌号
        tv_driver_info.text = item.formatDriverInfo()
        // 图片
        // 打回原因
        // 预付款状态	预付款状态：0:未审核，1:已审核，2:货主打回
        if (item.advanceState == "2") {
            tv_repulse_reason.setVisible(true)
            tv_repulse_reason.text = "打回原因：${item.repulseReason}"
        } else {
            tv_repulse_reason.setVisible(false)
        }
    }
}