package com.zczy.cargo_owner.order.confirm.widget

import android.annotation.SuppressLint
import android.content.Context
import android.graphics.Color
import android.util.AttributeSet
import androidx.constraintlayout.widget.ConstraintLayout
import com.zczy.cargo_owner.order.R
import com.zczy.cargo_owner.order.confirm.bean.RspQueryTrnSingleReceiptOrderData
import com.zczy.comm.utils.ex.isTrue
import com.zczy.comm.utils.ex.setVisible
import kotlinx.android.synthetic.main.returned_order_info_view_v4.view.clAbnormalTrajectory
import kotlinx.android.synthetic.main.returned_order_info_view_v4.view.tvAbnormalTrajectoryDeal
import kotlinx.android.synthetic.main.returned_order_info_view_v4.view.tvAbnormalTrajectoryResult
import kotlinx.android.synthetic.main.returned_order_info_view_v4.view.tvDeal
import kotlinx.android.synthetic.main.returned_order_info_view_v4.view.tvSuggestionResult
import kotlinx.android.synthetic.main.returned_order_info_view_v4.view.tvVerifyResult

/**
 *  desc: 回单确认-平台审核信息
 *  user: ssp
 *  time: 2025/3/25 13:53
 */
class ReturnedOrderInfoViewV4 @JvmOverloads constructor(
    context: Context,
    attrs: AttributeSet? = null,
    defStyleAttr: Int = 0
) : ConstraintLayout(context, attrs, defStyleAttr) {

    private var mReturnedOrderDetailBean: RspQueryTrnSingleReceiptOrderData? = null

    init {
        setBackgroundColor(Color.WHITE)
        inflate(context, R.layout.returned_order_info_view_v4, this)
        initView()
    }

    private fun initView() {
    }

    @SuppressLint("SetTextI18n")
    fun setData(data: RspQueryTrnSingleReceiptOrderData?) {
        this.mReturnedOrderDetailBean = data
        //平台审核信息
        data?.let { detail ->
            // 平台审核结果  0:"未审核"1:"异常"2:"审核通过"
            when (detail.orderReceiptCheckState) {
                "0" -> {
                    tvVerifyResult.text = "待审核"
                }

                "1" -> {
                    tvVerifyResult.text = "回单异常"
                }

                "2" -> {
                    tvVerifyResult.text = "回单通过"
                }

                "3" -> {
                    tvVerifyResult.text = "回单打回"
                }

                else -> {
                    tvVerifyResult.text = ""
                }
            }
            //去处理
            tvDeal.setVisible(detail.orderReceiptToDealFlag.isTrue)
            //平台审核意见
            tvSuggestionResult.text = detail.orderReceiptItemReason
            //轨迹异常
            clAbnormalTrajectory.setVisible(detail.orderTraceExceptionFlag.isTrue)
            tvAbnormalTrajectoryDeal.setVisible(detail.orderTraceExceptionToDealFlag.isTrue)
            tvAbnormalTrajectoryResult.text = detail.orderTraceExceptionItemReason
        }
    }

}