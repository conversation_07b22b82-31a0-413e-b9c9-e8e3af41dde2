package com.zczy.cargo_owner.order.confirm.bean

import com.zczy.comm.http.entity.BaseNewRequest
import com.zczy.comm.http.entity.BaseRsp

/**
 * 功能描述: 确认收货时查询货物信息
 * <AUTHOR>
 * @date 2022/3/8-19:50
 */

class ReqReturnedOrderContainerListV2(
    var orderId: String = ""
) : BaseNewRequest<BaseRsp<ContainerPageList<RspReturnedOrderContainerListV2>>>("oms-app/order/receipt/queryReceiveContainerDetailInfoByOrderIdApp")

data class RspReturnedOrderContainerListV2(
    var containerListNo: String = "",
    var containerListNoCount: String = ""
)