package com.zczy.cargo_owner.order.consignor.req

import com.zczy.comm.http.entity.BaseNewRequest
import com.zczy.comm.http.entity.BaseRsp
import com.zczy.comm.http.entity.ResultData

/** 功能描述:
 * 4.手机端接口：发货单确认列表订单数量
 * http://wiki.zczy56.com/pages/viewpage.action?pageId=16089091
 * <AUTHOR>
 * @company 中储智运科技股份有限公司
 * @copyright （版权）中储南京智慧物流科技有限公司所有
 * @date 2019/4/9
 */
data class Req4QueryDeliverOrderCountForMobile(
        var advanceState: String = "" // 预付款状态 0-未确认  1-已确认  不传为查全部
) : BaseNewRequest<BaseRsp<Rsp4QueryDeliverOrderCountForMobile>>
("oms-app/order/consignor/queryDeliverOrderCountForMobile")

data class Rsp4QueryDeliverOrderCountForMobile(
        var totalSize: String = "" // 列表总数
) : ResultData()