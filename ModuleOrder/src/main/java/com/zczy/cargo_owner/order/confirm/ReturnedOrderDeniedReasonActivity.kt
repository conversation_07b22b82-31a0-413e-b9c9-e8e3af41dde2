package com.zczy.cargo_owner.order.confirm

import android.annotation.SuppressLint
import android.content.Context
import android.content.Intent
import android.graphics.Color
import android.os.Bundle
import androidx.core.content.ContextCompat
import android.view.View
import com.sfh.lib.mvvm.annotation.LiveDataMatch
import com.sfh.lib.ui.dialog.DialogBuilder
import com.zczy.cargo_owner.order.R
import com.zczy.cargo_owner.order.confirm.adapter.ReturnedOrderDeniedReasonAdapter
import com.zczy.cargo_owner.order.confirm.bean.ReturnedOrderDeniedReasonReq
import com.zczy.cargo_owner.order.confirm.bean.RspFindBackOrderTagNew
import com.zczy.cargo_owner.order.confirm.dialog.ReturnOrderReasonDialog
import com.zczy.cargo_owner.order.flexbox.AlignItems
import com.zczy.cargo_owner.order.flexbox.FlexDirection
import com.zczy.cargo_owner.order.flexbox.FlexWrap
import com.zczy.cargo_owner.order.flexbox.FlexboxLayoutManager
import com.zczy.cargo_owner.order.flexbox.JustifyContent
import com.zczy.comm.http.entity.PageList
import com.zczy.comm.ui.BaseActivity
import kotlinx.android.synthetic.main.order_return_denied_reason_activity.*

/**
 *  desc: 回单打回原因
 *  user: 宋双朋
 *  time: 2025/7/10 17:03
 */
open class ReturnedOrderDeniedReasonActivity : BaseActivity<ReturnedOrderConfirmModel>() {

    private val orderId by lazy { intent.getStringExtra(ORDER_ID) ?: "" }
    private val detailId by lazy { intent.getStringExtra(DETAIL_ID) ?: "" }
    private val remarks by lazy { intent.getStringExtra(REMARKS) ?: "" }
    private val exceptionConfigType by lazy { intent.getStringExtra(EXCEPTION_CONFIG_TYPE) ?: "" }
    private var rejectReason: StringBuilder = StringBuilder()
    private var tagEdit = false
    private val mAdapter = ReturnedOrderDeniedReasonAdapter()

    companion object {
        private const val ORDER_ID = "orderId"
        private const val DETAIL_ID = "detailId"
        private const val REMARKS = "remarks"
        private const val EXCEPTION_CONFIG_TYPE = "exceptionConfigType"

        @JvmStatic
        fun start(context: Context?, orderId: String?, detailId: String?,exceptionConfigType: String?, remarks: String?) {
            if (context == null) return
            val intent = Intent(context, ReturnedOrderDeniedReasonActivity::class.java)
            intent.putExtra(ORDER_ID, orderId)
            intent.putExtra(DETAIL_ID, detailId)
            intent.putExtra(REMARKS, remarks)
            intent.putExtra(EXCEPTION_CONFIG_TYPE, exceptionConfigType)
            context.startActivity(intent)
        }
    }

    override fun getLayout(): Int {
        return R.layout.order_return_denied_reason_activity
    }

    override fun initData() {
        viewModel?.queryTag()
//        rejectReason.append(remarks)
//        editDeniedReason.setText(rejectReason)
    }

    override fun bindView(bundle: Bundle?) {
        bindClickEvent(tvQuickAdd)
        bindClickEvent(tvQuickEdit)
        bindClickEvent(tvConfirmAdjustmentReturnedOrder)
        initFlexboxView()
    }

    private fun initFlexboxView() {
        mAdapter.apply {
            bindToRecyclerView(recyclerView)
            setOnItemChildClickListener { _, view, position ->
                val item = mAdapter.data[position]
                when (view.id) {
                    R.id.imgDelete -> {
                        val dialogBuilder = DialogBuilder()
                        dialogBuilder.title = "提示"
                        dialogBuilder.message = "是否删除该条标签?"
                        dialogBuilder.okListener = DialogBuilder.DialogInterface.OnClickListener { d, _ ->
                            d?.dismiss()
                            viewModel?.deleteTag(id = item.id, backReason = item.backReason)
                        }
                        showDialog(dialogBuilder)
                    }
                }
            }
            setOnItemClickListener { _, _, position ->
                val item = mAdapter.data[position]
                if (!tagEdit) {
                    rejectReason.append(" ${item.backReason}")
                    editDeniedReason.setText(rejectReason)
                }
            }
        }
        val flexboxLayoutManager = FlexboxLayoutManager(this)
        flexboxLayoutManager.flexWrap = FlexWrap.WRAP
        flexboxLayoutManager.flexDirection = FlexDirection.ROW
        flexboxLayoutManager.justifyContent = JustifyContent.FLEX_START
        flexboxLayoutManager.alignItems = AlignItems.FLEX_START
        recyclerView.adapter = mAdapter
        recyclerView.layoutManager = flexboxLayoutManager
    }

    @SuppressLint("SetTextI18n")
    override fun onSingleClick(v: View) {
        super.onSingleClick(v)
        when (v.id) {
            R.id.tvConfirmAdjustmentReturnedOrder -> {
                val reason = editDeniedReason.text.toString()
                if (reason.isBlank()) {
                    showDialogToast("理由不能为空，请填写")
                    return
                }
                viewModel?.returnedOrderDeniedReason(
                    deniedReasonReq = ReturnedOrderDeniedReasonReq(
                        orderId = orderId,
                        detailId = detailId,
                        repulseReason = remarks + reason,
                        exceptionConfigType =exceptionConfigType,
                        remarks = remarks
                    )
                )
            }

            R.id.tvQuickAdd -> {
                //新增
                val returnOrderReasonDialog = ReturnOrderReasonDialog()
                returnOrderReasonDialog.mClickBlock = { reason ->
                    viewModel?.addTag(reason)
                }
                returnOrderReasonDialog.show(supportFragmentManager, "ReturnOrderReasonDialog")
            }

            R.id.tvQuickEdit -> {
                //编辑
                tagEdit = !tagEdit
                if (tagEdit) {
                    tvQuickEdit.text = "完成编辑"
                    tvQuickEdit.setCompoundDrawablesWithIntrinsicBounds(null, null, null, null)
                    tvQuickEdit.setTextColor(Color.parseColor("#5086fc"))
                } else {
                    tvQuickEdit.text = "编辑"
                    tvQuickEdit.setCompoundDrawablesWithIntrinsicBounds(ContextCompat.getDrawable(this@ReturnedOrderDeniedReasonActivity, R.drawable.order_gray_edit_icon), null, null, null)
                    tvQuickEdit.setTextColor(Color.parseColor("#ff888888"))
                }
                mAdapter.tagEdit = tagEdit
                mAdapter.notifyDataSetChanged()
            }
        }
    }

    @LiveDataMatch
    open fun deleteTagSuccess() {
        viewModel?.queryTag()
    }

    @LiveDataMatch
    open fun addTagSuccess() {
        viewModel?.queryTag()
    }

    @LiveDataMatch
    open fun queryTagListSuccess(data: PageList<RspFindBackOrderTagNew>?) {
        mAdapter.setNewData(data?.rootArray)
    }

    @LiveDataMatch
    open fun returnedOrderDeniedReasonSuccess() {
        finish()
    }
}