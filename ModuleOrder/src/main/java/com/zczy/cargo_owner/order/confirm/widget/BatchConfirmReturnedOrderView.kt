package com.zczy.cargo_owner.order.confirm.widget

import android.annotation.SuppressLint
import android.content.Context
import android.graphics.Color
import android.text.Editable
import android.text.TextUtils
import android.text.TextWatcher
import android.util.AttributeSet
import androidx.constraintlayout.widget.ConstraintLayout
import com.sfh.lib.rx.ui.UtilRxView
import com.sfh.lib.utils.UtilTool
import com.zczy.cargo_owner.libcomm.utils.toDoubleRoundDownString
import com.zczy.cargo_owner.order.R
import com.zczy.cargo_owner.order.confirm.bean.BatchConfirmItemDetailBean
import com.zczy.cargo_owner.order.confirm.bean.RspOrderIgnoreSmall
import com.zczy.cargo_owner.order.confirm.bean.computeMoneyV1
import com.zczy.cargo_owner.order.confirm.bean.getCargoCategory
import com.zczy.cargo_owner.order.confirm.bean.getSettleRate
import com.zczy.cargo_owner.order.confirm.bean.isNotNullOrNotEmpty
import com.zczy.cargo_owner.order.confirm.bean.isShowCheckMonth
import com.zczy.cargo_owner.order.confirm.bean.showIgnoreSmallType
import com.zczy.cargo_owner.order.confirm.bean.showJiaKouRule
import com.zczy.cargo_owner.order.confirm.computeMoney
import com.zczy.cargo_owner.order.confirm.roundToFourDecimals4Point
import com.zczy.cargo_owner.order.confirm.v2.DecimalDigitsInputFilter
import com.zczy.comm.data.OrderPriceEnum
import com.zczy.comm.utils.NumUtil
import com.zczy.comm.utils.ex.isNotNull
import com.zczy.comm.utils.ex.isNull
import com.zczy.comm.utils.ex.isTrue
import com.zczy.comm.utils.ex.setVisible
import com.zczy.comm.utils.ex.toDoubleRoundDownString
import kotlinx.android.synthetic.main.order_returned_batch_confirm_item.view.cl_order_textview12_2
import kotlinx.android.synthetic.main.order_returned_batch_confirm_item.view.etConfirmSettlementPriceIncludedTax
import kotlinx.android.synthetic.main.order_returned_batch_confirm_item.view.etConfirmSettlementPriceNotIncludedTax
import kotlinx.android.synthetic.main.order_returned_batch_confirm_item.view.etConfirmSettlementTonnage
import kotlinx.android.synthetic.main.order_returned_batch_confirm_item.view.etConsignorNoRateUnitShowMoney
import kotlinx.android.synthetic.main.order_returned_batch_confirm_item.view.ivBaseInfo
import kotlinx.android.synthetic.main.order_returned_batch_confirm_item.view.ivLTLOrderFlag
import kotlinx.android.synthetic.main.order_returned_batch_confirm_item.view.orderLossRisesTonsNoTaxV2
import kotlinx.android.synthetic.main.order_returned_batch_confirm_item.view.orderLossRisesTonsNoTaxV3
import kotlinx.android.synthetic.main.order_returned_batch_confirm_item.view.orderLossRisesTonsTaxV1
import kotlinx.android.synthetic.main.order_returned_batch_confirm_item.view.orderLossRisesTonsTaxV2
import kotlinx.android.synthetic.main.order_returned_batch_confirm_item.view.orderLossRisesTonsTaxV3
import kotlinx.android.synthetic.main.order_returned_batch_confirm_item.view.orderSettleType
import kotlinx.android.synthetic.main.order_returned_batch_confirm_item.view.orderSettleTypeUnit
import kotlinx.android.synthetic.main.order_returned_batch_confirm_item.view.orderSettleTypeUnitEditText
import kotlinx.android.synthetic.main.order_returned_batch_confirm_item.view.orderSettlementBasis
import kotlinx.android.synthetic.main.order_returned_batch_confirm_item.view.order_textview21
import kotlinx.android.synthetic.main.order_returned_batch_confirm_item.view.returnedOrderDetailNo
import kotlinx.android.synthetic.main.order_returned_batch_confirm_item.view.tvCheckMonth
import kotlinx.android.synthetic.main.order_returned_batch_confirm_item.view.tvPlaceOfEnd
import kotlinx.android.synthetic.main.order_returned_batch_confirm_item.view.tvPlaceOfStart
import kotlinx.android.synthetic.main.order_returned_batch_confirm_item.view.tvRuleArrow
import kotlinx.android.synthetic.main.order_returned_batch_confirm_item.view.tvView1
import kotlinx.android.synthetic.main.order_returned_batch_confirm_item.view.tvView11
import kotlinx.android.synthetic.main.order_returned_batch_confirm_item.view.tvView2
import kotlinx.android.synthetic.main.order_returned_batch_confirm_item.view.tvView3
import kotlinx.android.synthetic.main.order_returned_batch_confirm_item.view.tvWaybillGoodsInfo
import kotlinx.android.synthetic.main.order_returned_batch_confirm_item.view.tvWaybillPlateNumber
import kotlinx.android.synthetic.main.order_returned_batch_confirm_item.view.view1
import java.math.BigDecimal

/**
 *描述：批量回单确认单个view
 *auth:宋双朋
 *time:2024/5/30 20:49
 */

@SuppressLint("SetTextI18n")
class BatchConfirmReturnedOrderView : ConstraintLayout {
    var showBaseInfoDialog: () -> Unit = { }
    var computeAllMoneyBlock: () -> Unit = { }
    var item: BatchConfirmItemDetailBean = BatchConfirmItemDetailBean()
    var clickRuleBlock: (item: BatchConfirmItemDetailBean) -> Unit = { _ -> } //选择规则
    var consignorNoRateUnitShowMoneyFlag: String? = null
    var consignorRateMoneyUpdateFlag: String? = null

    constructor(context: Context) : super(context)

    constructor(context: Context, attrs: AttributeSet?) : super(context, attrs)

    constructor(context: Context, attrs: AttributeSet?, defStyleAttr: Int) : super(context, attrs, defStyleAttr)

    init {
        setBackgroundColor(Color.WHITE)
        inflate(context, R.layout.order_returned_batch_confirm_item, this)
    }

    fun refreshView(mItem: BatchConfirmItemDetailBean) {
        item = mItem
        item.consignorSmartDeficitTonUseRateType = if (item.overLossRuleId.isNotNullOrNotEmpty()) {
            "2"
        } else {
            null
        }
        //亏涨吨点击事件
        tvView1.text = item.overLossRuleName
        tvView2.text = item.releaseLossTonActSubMoney
        computeLossMoney()
    }

    fun checkMonthMoney(): Boolean {
        val msg = item.isShowCheckMonth(etConfirmSettlementPriceNotIncludedTax.text.toString().trim())
        return msg.isNotEmpty()
    }

    @SuppressLint("UseCompatLoadingForDrawables")
    fun setData(mItem: BatchConfirmItemDetailBean, ignoreSmallChangeType: String?, consignorNoRateUnitShowMoneyFlag: String?, consignorRateMoneyUpdateFlag: String?) {
        this.item = mItem
        this.consignorNoRateUnitShowMoneyFlag = consignorNoRateUnitShowMoneyFlag
        this.consignorRateMoneyUpdateFlag = consignorRateMoneyUpdateFlag
        if (item.orderIgnoreSmallObj.isNull) {
            item.orderIgnoreSmallObj = RspOrderIgnoreSmall()
        }
        if (ignoreSmallChangeType.isNotNull) {
            //选否
            item.orderIgnoreSmallObj.ignoreSmallChangeType = ignoreSmallChangeType
        }
        ivBaseInfo.setOnClickListener {
            showBaseInfoDialog()
        }
        tvView11.text = item.orderIgnoreSmallObj.showIgnoreSmallType()
        //运单信息
        returnedOrderDetailNo.text = item.orderId
        tvPlaceOfStart.text = "${item.despatchCity}${item.despatchDis}"
        tvPlaceOfEnd.text = "${item.deliverCity}${item.deliverDis}"
        tvWaybillGoodsInfo.text = String.format(
            context.resources.getString(R.string.order_goods_info),
            item.allCargoName, item.slipLoad, item.cargoCategoryName
                ?: "", item.orgBackMoney
        )
        tvWaybillPlateNumber.text = "车牌号：" + item.plateNumber
        tvView2.text = item.releaseLossTonActSubMoney
        orderLossRisesTonsTaxV1.setVisible(true)
        orderLossRisesTonsTaxV2.setVisible(true)
        orderLossRisesTonsNoTaxV2.setVisible(true)
        orderLossRisesTonsNoTaxV3.setVisible(true)
        orderLossRisesTonsTaxV3.setVisible(true)
        ivLTLOrderFlag.setVisible(item.lTLOrder.isTrue)
        etConfirmSettlementPriceNotIncludedTax.filters = arrayOf(DecimalDigitsInputFilter(7, 2))
        etConsignorNoRateUnitShowMoney.filters = arrayOf(DecimalDigitsInputFilter(7, 2))
        item.computeMoneyV1()
        etConsignorNoRateUnitShowMoney.setText(item.consignorNoRateUnitShowMoney)
        cl_order_textview12_2.setVisible(consignorNoRateUnitShowMoneyFlag.isTrue)
        if (item.freightType.isTrue) {
            //单价
            orderSettleTypeUnitEditText.setText(item.pbConsignorUnitMoney)
            orderSettleType.text = "运费计价方式(单价)"
            when (item.releaseUseFlag) {
                "1" -> {
                    //禁止修改
                    orderSettleTypeUnitEditText.isEnabled = false
                }

                else -> {
                    orderSettleTypeUnitEditText.isEnabled = consignorRateMoneyUpdateFlag.isTrue
                }
            }
            orderSettleTypeUnit.text = "元/吨"
        } else {
            //包车价
            orderSettleTypeUnitEditText.setText(item.pbConsignorMoney)
            orderSettleTypeUnitEditText.isEnabled = false
            orderSettleTypeUnit.text = "元"
            orderSettleType.text = "运费计价方式(包车价)"
        }
        //结算信息,此处必须先初始化塞值，避免Listenr 重复计算，导致四舍五入错误
        etConfirmSettlementTonnage.setText(item.slipLoad)
        UtilRxView.afterTextChangeEvents(etConfirmSettlementPriceIncludedTax, 500) { content ->
            if (etConfirmSettlementPriceIncludedTax.isFocused) {
                //实际付款金额(含税)
                item.consignorRateMoney = content.toString()
                item.consignorNoRateMoney = getMoneyRateText(item.settleRate, item.consignorRateMoney)
                etConfirmSettlementPriceNotIncludedTax.setText(item.consignorNoRateMoney)
                //计算相关金额
                computeLossMoney()
            }
        }
        etConfirmSettlementPriceIncludedTax.setText(item.consignorRateMoney)
        etConfirmSettlementPriceNotIncludedTax.setText(item.consignorNoRateMoney)
        etConfirmSettlementPriceNotIncludedTax.addTextChangedListener(object : TextWatcher {
            override fun beforeTextChanged(s: CharSequence?, start: Int, count: Int, after: Int) {}

            override fun onTextChanged(s: CharSequence?, start: Int, before: Int, count: Int) {
                if (!s.isNullOrEmpty()) {
                    try {
                        val value = s.toString().toDouble()
                        if (value > 9999999.99) {
                            etConfirmSettlementPriceNotIncludedTax.error = "整数只能输入7位"
                        }
                    } catch (e: NumberFormatException) {
                        e.printStackTrace()
                    }
                }
            }

            override fun afterTextChanged(s: Editable?) {
                if (etConfirmSettlementPriceNotIncludedTax.isFocused) {
                    //实际付款金额(承运方预估到手价)
                    item.consignorNoRateMoney = s.toString()
                    //实际付款金额(不含税): 返推算->实际付款金额(含税)
                    item.consignorRateMoney = getTurnMoney(item.consignorNoRateMoney, item.settleRate)
                    etConfirmSettlementPriceIncludedTax.setText(item.consignorRateMoney)
                    //计算相关金额
                    computeLossMoney()
                }
            }
        })
        orderSettlementBasis.text = item.settleBasisTypeName
        item.slipLoad = item.slipLoad
        UtilRxView.afterTextChangeEvents(etConfirmSettlementPriceIncludedTax, 500) { inContent ->
            if (etConfirmSettlementPriceIncludedTax.isFocused) {
                //结算金额(含税价)
                item.consignorRateMoney = inContent.toString()
                //结算金额(含税价): 推算->承运方预估到手价(不含税价)
                val money1 = getMoneyRateText(inContent.toString(), item.settleRate, item.consignorNoRateMoney)
                item.consignorNoRateMoney = money1
                etConfirmSettlementPriceNotIncludedTax.setText(item.consignorNoRateMoney)
                //结算金额(含税价): 推算->承运方预估到手价单价
                val money2 = computeMoney2(item.consignorNoRateMoney, item.slipLoad)
                item.consignorNoRateUnitShowMoney = money2
                etConsignorNoRateUnitShowMoney.setText(item.consignorNoRateUnitShowMoney)
                //计算相关金额
                computeLossMoney()
            }
        }
        etConsignorNoRateUnitShowMoney.addTextChangedListener(object : TextWatcher {
            override fun beforeTextChanged(s: CharSequence?, start: Int, count: Int, after: Int) {}

            override fun onTextChanged(s: CharSequence?, start: Int, before: Int, count: Int) {
                if (!s.isNullOrEmpty()) {
                    try {
                        val value = s.toString().toDouble()
                        if (value > 9999999.99) {
                            etConsignorNoRateUnitShowMoney.error = "整数只能输入7位"
                        }
                    } catch (e: NumberFormatException) {
                        e.printStackTrace()
                    }
                }
                tvCheckMonth.setVisible(item.isShowCheckMonth(s.toString()).isNotEmpty())
                tvCheckMonth.text = item.isShowCheckMonth(s.toString())
            }

            override fun afterTextChanged(s: Editable?) {
                if (etConsignorNoRateUnitShowMoney.isFocused) {
                    //承运方预估到手价单价
                    item.consignorNoRateUnitShowMoney = s.toString()
                    //承运方预估到手价单价: 返推算->承运方预估到手价(不含税价)
                    val money1 = computeMoney1(item.consignorNoRateUnitShowMoney, item.slipLoad)
                    item.consignorNoRateMoney = money1
                    etConfirmSettlementPriceNotIncludedTax.setText(item.consignorNoRateMoney)
                    //承运方预估到手价(不含税价): 返推算->结算金额(含税价)
                    val money2 = getTurnMoney(item.consignorNoRateMoney ?: "", item.settleRate, item.consignorRateMoney)
                    item.consignorRateMoney = money2
                    etConfirmSettlementPriceIncludedTax.setText(item.consignorRateMoney)
                    //计算相关金额
                    computeLossMoney()
                }
            }
        })
        if (item.releaseUseFlag.isTrue) {
            //应用了亏涨吨
            etConfirmSettlementTonnage.isEnabled = false
            etConfirmSettlementPriceIncludedTax.isEnabled = false
            etConfirmSettlementPriceNotIncludedTax.isEnabled = false
        } else {
            etConfirmSettlementTonnage.isEnabled = item.updateSettleWeightFlag == "1"
            etConfirmSettlementPriceIncludedTax.isEnabled = consignorRateMoneyUpdateFlag.isTrue
            etConfirmSettlementPriceNotIncludedTax.isEnabled = true
        }
        etConfirmSettlementTonnage.isEnabled = true
        etConfirmSettlementPriceIncludedTax.isEnabled = true
        etConfirmSettlementPriceNotIncludedTax.isEnabled = true
        //结算吨位
        UtilRxView.afterTextChangeEvents(etConfirmSettlementTonnage, 500) { content ->
            if (etConfirmSettlementTonnage.isFocused) {
                item.slipLoad = content.toString()
                //单价直接丢给含税结算金额
                if (item.freightType.isTrue) {
                    item.let { it ->
                        if (it.pbConsignorUnitMoney.isNullOrEmpty() || item.slipLoad.isNullOrEmpty()) {
                            etConfirmSettlementPriceIncludedTax.setText("")
                            item.consignorRateMoney = ""
                        } else {
                            val s1 = item.slipLoad?.toDoubleOrNull() ?: 0.00
                            val s2 = it.pbConsignorUnitMoney?.toDoubleOrNull() ?: 0.0
                            var s3 = 0.0
                            //单价，竞价模式加保费
                            if (item.orderModel.isTrue) {
                                item.guaranteeFee?.let { s3 = it.toDouble() }
                                val toString = (NumUtil.mulEgnorNull(s1, s2) + s3).roundTo2DecimalPlaces().toString()
                                etConfirmSettlementPriceIncludedTax.setText(toString)
                                item.consignorRateMoney = toString
                            } else {
                                val toString = NumUtil.mulEgnorNull(s1, s2).roundTo2DecimalPlaces().toString()
                                etConfirmSettlementPriceIncludedTax.setText(toString)
                                item.consignorRateMoney = toString
                            }
                        }
                        //计算结算金额 承运方预估到手价
                        val money1 = getMoneyRateText(item.consignorRateMoney, item.settleRate, item.consignorNoRateMoney)
                        item.consignorNoRateMoney = money1
                        etConfirmSettlementPriceNotIncludedTax.setText(item.consignorNoRateMoney)
                        //结算金额(含税价): 推算->承运方预估到手价单价
                        val money2 = computeMoney2(item.consignorNoRateMoney, item.slipLoad)
                        item.consignorNoRateUnitShowMoney = money2
                        etConsignorNoRateUnitShowMoney.setText(item.consignorNoRateUnitShowMoney)
                    }
                }
            }
        }
        //运费计价方式(单价)
        UtilRxView.afterTextChangeEvents(orderSettleTypeUnitEditText, 500) { content ->
            if (orderSettleTypeUnitEditText.isFocused) {
                if (item.freightType.isTrue) {
                    item.pbConsignorUnitMoney = content.toString()
                    //运费计价方式（单价）
                    val m1 = if (TextUtils.isEmpty(item.slipLoad)) {
                        0.00
                    } else {
                        item.slipLoad?.toDoubleOrNull() ?: 0.00
                    }
                    val m2 = if (TextUtils.isEmpty(item.pbConsignorUnitMoney.toString())) {
                        0.00
                    } else {
                        item.pbConsignorUnitMoney?.toDoubleOrNull() ?: 0.00
                    }
                    //计算结算金额(含税)
                    val money = NumUtil.mulEgnorNull(m1, m2)
                    item.consignorRateMoney = money.toString().toDoubleRoundDownString(2)
                    etConfirmSettlementPriceIncludedTax.setText(money.roundTo2DecimalPlaces().toString())
                    //计算结算金额 承运方预估到手价
                    val money1 = getMoneyRateText(item.consignorRateMoney, item.settleRate, item.consignorNoRateMoney)
                    item.consignorNoRateMoney = money1
                    etConfirmSettlementPriceNotIncludedTax.setText(item.consignorNoRateMoney)
                    //结算金额(含税价): 推算->承运方预估到手价单价
                    val money2 = computeMoney2(item.consignorNoRateMoney, item.slipLoad)
                    item.consignorNoRateUnitShowMoney = money2
                    etConsignorNoRateUnitShowMoney.setText(item.consignorNoRateUnitShowMoney)
                }
            }
        }
        UtilTool.setEditTextInputSize(orderSettleTypeUnitEditText, 2)
        UtilTool.setEditTextInputSize(etConfirmSettlementTonnage, 4)

        var replace1 = item.receiveWeight ?: ""
        if (TextUtils.isEmpty(replace1)) {
            replace1 = "0.000"
        }
        var replace2 = item.deliverWeight ?: ""
        if (TextUtils.isEmpty(replace2)) {
            replace2 = "1.000"
        }

        val sub = NumUtil.sub(replace1.toDoubleOrNull() ?: 0.00, replace2.toDouble())
        val div = NumUtil.div(sub, replace2.toDoubleOrNull() ?: 0.00, 8)
        val mul = NumUtil.mul(div, 100.00)
        order_textview21.text = "${sub.roundToFourDecimals4Point()}${item.getCargoCategory()} | ${mul.roundToFourDecimals4Point()}%"
        //亏涨吨点击事件
        tvView1.text = item.overLossRuleName
        tvView2.text = item.releaseLossTonActSubMoney
        tvRuleArrow.setVisible(item.releaseUseFlag.isTrue)
        tvView3.text = item.showJiaKouRule()
        UtilRxView.clicks(view1, 600) {
            if (item.releaseUseFlag.isTrue) {
                //应用了亏涨吨才可以重选亏涨吨
                clickRuleBlock(mItem)
            }
        }
        computeLossMoney()
    }

    private fun computeLossMoney() {
        //计算亏涨吨金额
        if (item.overLossRuleId.isNotNullOrNotEmpty()) {
            //亏涨吨扣款金额
            val money = item.releaseLossTonActSubMoney?.toDoubleOrNull() ?: 0.00

            val money1 = item.consignorNoRateMoney?.toDoubleOrNull() ?: 0.00
            //亏涨吨结算金额承运方预估到手价
            val money2 = NumUtil.sub(money1, money)
            item.lossRiseConsignorNoRateMoney = money2.toDoubleRoundDownString(8)
            if (money != 0.00) {
                //亏涨吨结算金额 含税
                val money3 = NumUtil.mul(money2, NumUtil.sum(1.00, item.getSettleRate())).toDoubleRoundDownString(8)
                item.lossRiseConsignorRateMoney = money3
            } else {
                item.lossRiseConsignorRateMoney = item.consignorRateMoney
            }
        }
        computeChargebackMoney()
    }

    private fun computeChargebackMoney() {
        //计算加扣款
        if (item.overLossRuleId.isNotNullOrNotEmpty()) {
            if (item.consignorUseDeductionFlag.isTrue) {
                //使用亏涨吨 计算亏涨吨金额
                val money = item.lossRiseConsignorNoRateMoney?.toDoubleOrNull() ?: 0.00
                val computeMoney = item.consignorDeductionObj?.deductionDetailArray?.computeMoney() ?: 0.00
                //亏涨吨后结算金额(承运方预估到手价+亏涨吨金额)
                val money1 = NumUtil.sum(money, computeMoney)
                item.deductionNoRateMoney = money1.toDoubleRoundDownString(8)
                //计算亏涨吨后结算金额(含税)
                val money2 = NumUtil.mul(money1, NumUtil.sum(1.00, item.getSettleRate())).toDoubleRoundDownString(8)
                item.deductionRateMoney = money2
                //扣款后结算金额(元) 含税
                if (item.releaseLossTonActSubMoney.isNotNullOrNotEmpty()) {
                    if (item.releaseLossTonActSubMoney?.toDoubleOrNull() == 0.00) {
                        orderLossRisesTonsTaxV2.text = item.consignorRateMoney
                        //扣款后结算金额(元) 承运方预估到手价
                        orderLossRisesTonsNoTaxV2.text = item.consignorNoRateMoney
                    } else {
                        orderLossRisesTonsTaxV2.text = item.deductionRateMoney.roundTo2DecimalPlaces()
                        //扣款后结算金额(元) 承运方预估到手价
                        orderLossRisesTonsNoTaxV2.text = item.deductionNoRateMoney.roundTo2DecimalPlaces()
                    }
                } else {
                    orderLossRisesTonsTaxV2.text = item.deductionRateMoney.roundTo2DecimalPlaces()
                    //扣款后结算金额(元) 承运方预估到手价
                    orderLossRisesTonsNoTaxV2.text = item.deductionNoRateMoney.roundTo2DecimalPlaces()
                }
            } else {
                //使用亏涨吨 未使用加扣款
                //扣款后结算金额(元) 含税
                if (item.releaseLossTonActSubMoney.isNotNullOrNotEmpty()) {
                    if (item.releaseLossTonActSubMoney?.toDoubleOrNull() == 0.00) {
                        orderLossRisesTonsTaxV2.text = item.consignorRateMoney
                        //扣款后结算金额(元) 承运方预估到手价
                        orderLossRisesTonsNoTaxV2.text = item.consignorNoRateMoney
                    } else {
                        orderLossRisesTonsTaxV2.text = item.lossRiseConsignorRateMoney.roundTo2DecimalPlaces()
                        //扣款后结算金额(元) 承运方预估到手价
                        orderLossRisesTonsNoTaxV2.text = item.lossRiseConsignorNoRateMoney.roundTo2DecimalPlaces()
                    }
                } else {
                    orderLossRisesTonsTaxV2.text = item.lossRiseConsignorRateMoney.roundTo2DecimalPlaces()
                    //扣款后结算金额(元) 承运方预估到手价
                    orderLossRisesTonsNoTaxV2.text = item.lossRiseConsignorNoRateMoney.roundTo2DecimalPlaces()
                }
            }
        } else {
            //未使用亏涨吨
            val money = item.consignorNoRateMoney?.toDoubleOrNull() ?: 0.00
            //加扣款总金额
            val computeMoney = item.consignorDeductionObj?.deductionDetailArray?.computeMoney() ?: 0.00
            //亏涨吨后结算金额(承运方预估到手价+亏涨吨金额)
            val money1 = NumUtil.sum(money, computeMoney)
            item.deductionNoRateMoney = money1.toDoubleRoundDownString(8)
            //计算亏涨吨后结算金额(含税)
            val money2 = NumUtil.mul(money1, NumUtil.sum(1.00, item.getSettleRate())).toDoubleRoundDownString(8)
            item.deductionRateMoney = money2
            //扣款后结算金额(元) 含税
            if (item.releaseLossTonActSubMoney.isNotNullOrNotEmpty()) {
                if (item.releaseLossTonActSubMoney?.toDoubleOrNull() == 0.00) {
                    orderLossRisesTonsTaxV2.text = item.consignorRateMoney
                    //扣款后结算金额(元) 承运方预估到手价
                    orderLossRisesTonsNoTaxV2.text = item.consignorNoRateMoney
                } else {
                    orderLossRisesTonsTaxV2.text = item.deductionRateMoney.roundTo2DecimalPlaces()
                    //扣款后结算金额(元) 承运方预估到手价
                    orderLossRisesTonsNoTaxV2.text = item.deductionNoRateMoney.roundTo2DecimalPlaces()
                }
            } else {
                orderLossRisesTonsTaxV2.text = item.deductionRateMoney.roundTo2DecimalPlaces()
                //扣款后结算金额(元) 承运方预估到手价
                orderLossRisesTonsNoTaxV2.text = item.deductionNoRateMoney.roundTo2DecimalPlaces()
            }
        }
        //计算抹零
        computeWipeMoney()
    }

    //计算抹零金额
    private fun computeWipeMoney() {
        //抹零方式进行抹零计算
        when (item.orderIgnoreSmallObj.ignoreSmallChangeType) {
            OrderPriceEnum.含税价角分抹零.value -> {
                wipeMoney1()
            }

            OrderPriceEnum.承运方预估到手价十元以下抹零.value -> {
                wipeMoney2()
            }

            OrderPriceEnum.含税价十元以下抹零.value -> {
                wipeMoney3()
            }

            OrderPriceEnum.承运方预估到手价角分抹零.value -> {
                wipeMoney4()
            }

            OrderPriceEnum.含税价五元以下抹零.value -> {
                wipeMoney6()
            }

            OrderPriceEnum.承运方预估到手价五元以下抹零.value -> {
                wipeMoney5()
            }

            else -> {
                //计算无抹零
                item.orderIgnoreSmallObj.ignoreSmallChangeRateMoney = null
                item.orderIgnoreSmallObj.ignoreSmallChangeNoRateMoney = null
                item.orderIgnoreSmallObj.ignoreSmallChangeMoney = null
            }
        }
        //计算外部价格
        computeAllMoneyBlock()
    }

    private fun wipeMoney6() {
        //含税价5元以下抹零
        val money9 = if (item.overLossRuleId.isNotNullOrNotEmpty()) {
            //使用了亏涨吨
            if (item.consignorUseDeductionFlag.isTrue) {
                //使用了加扣款
                item.deductionRateMoney?.toDoubleOrNull() ?: 0.00
            } else {
                //使用亏涨吨 未使用加扣款
                item.lossRiseConsignorRateMoney?.toDoubleOrNull() ?: 0.00
            }
        } else {
            if (item.consignorUseDeductionFlag.isTrue) {
                //使用了加扣款
                item.deductionRateMoney?.toDoubleOrNull() ?: 0.00
            } else {
                //未使用亏涨吨 也未使用加扣款
                item.consignorRateMoney?.toDoubleOrNull() ?: 0.00
            }
        }
        val money91 = if (item.overLossRuleId.isNotNullOrNotEmpty()) {
            //使用了亏涨吨
            if (item.consignorUseDeductionFlag.isTrue) {
                //使用了加扣款
                item.deductionNoRateMoney?.toDoubleOrNull() ?: 0.00
            } else {
                //使用亏涨吨 未使用加扣款
                item.lossRiseConsignorNoRateMoney?.toDoubleOrNull() ?: 0.00
            }
        } else {
            if (item.consignorUseDeductionFlag.isTrue) {
                //使用了加扣款
                item.deductionNoRateMoney?.toDoubleOrNull() ?: 0.00
            } else {
                //未使用亏涨吨 也未使用加扣款
                item.consignorNoRateMoney?.toDoubleOrNull() ?: 0.00
            }
        }
        //5元以下抹零 抹零金额除以5 向下取整
        val mo1 = NumUtil.div(money9, 5.00, 2).roundTo2DecimalPlacesV1()
        val mo2 = NumUtil.mul(mo1, 5.00)
        item.orderIgnoreSmallObj.ignoreSmallChangeRateMoney = mo2.toDoubleRoundDownString(8)
        val sub = NumUtil.sub(money9, mo2)
        //推算结算价格（承运方预估到手价） 5元以下抹零
        val rate = NumUtil.sum(1.00, item.getSettleRate())
        if (sub != 0.00) {
            //抹零金额为0时不逆推
            item.orderIgnoreSmallObj.ignoreSmallChangeNoRateMoney = NumUtil.div(mo2, rate, 8).toDoubleRoundDownString(8)
        } else {
            item.orderIgnoreSmallObj.ignoreSmallChangeNoRateMoney = money91.toDoubleRoundDownString(2)
        }
        //计算抹零总金额
        item.orderIgnoreSmallObj.ignoreSmallChangeMoney = sub.toDoubleRoundDownString(8)
        println("金额${item.orderIgnoreSmallObj.ignoreSmallChangeNoRateMoney}")
    }

    private fun wipeMoney5() {
        //承运方预估到手价5元以下抹零
        //计算结算价格(承运方预估到手价 5元以下抹零)
        val money9 = if (!TextUtils.isEmpty(item.overLossRuleId)) {
            //使用了亏涨吨
            if (TextUtils.equals("1", item.consignorUseDeductionFlag)) {
                //使用了加扣款
                item.deductionNoRateMoney?.toDoubleOrNull() ?: 0.00
            } else {
                //使用亏涨吨 未使用加扣款
                item.lossRiseConsignorNoRateMoney?.toDoubleOrNull() ?: 0.00
            }
        } else {
            if (item.consignorUseDeductionFlag.isTrue) {
                //使用了加扣款
                item.deductionNoRateMoney?.toDoubleOrNull() ?: 0.00
            } else {
                //未使用亏涨吨 也未使用加扣款
                item.consignorNoRateMoney?.toDoubleOrNull() ?: 0.00
            }
        }
        val money91 = if (!TextUtils.isEmpty(item.overLossRuleId)) {
            //使用了亏涨吨
            if (TextUtils.equals("1", item.consignorUseDeductionFlag)) {
                //使用了加扣款
                item.deductionRateMoney?.toDoubleOrNull() ?: 0.00
            } else {
                //使用亏涨吨 未使用加扣款
                item.lossRiseConsignorRateMoney?.toDoubleOrNull() ?: 0.00
            }
        } else {
            if (item.consignorUseDeductionFlag.isTrue) {
                //使用了加扣款
                item.deductionRateMoney?.toDoubleOrNull() ?: 0.00
            } else {
                //未使用亏涨吨 也未使用加扣款
                item.consignorRateMoney?.toDoubleOrNull() ?: 0.00
            }
        }
        //5元以下抹零 抹零金额除以5 向下取整
        val mo1 = NumUtil.div(money9, 5.00, 2).roundTo2DecimalPlacesV1()
        val mo2 = NumUtil.mul(mo1, 5.00)
        item.orderIgnoreSmallObj.ignoreSmallChangeNoRateMoney = mo2.toDoubleRoundDownString(8)
        val sub = NumUtil.sub(money9, mo2)
        //推算结算价格（承运方预估到手价） 5元以下抹零
        val rate = NumUtil.sum(1.00, item.getSettleRate())
        if (sub != 0.00) {
            //抹零金额为0时不逆推
            item.orderIgnoreSmallObj.ignoreSmallChangeRateMoney = NumUtil.mul(mo2, rate).toDoubleRoundDownString(8)
        } else {
            item.orderIgnoreSmallObj.ignoreSmallChangeRateMoney = money91.toDoubleRoundDownString(2)
        }
        //计算抹零总金额
        item.orderIgnoreSmallObj.ignoreSmallChangeMoney = sub.toDoubleRoundDownString(8)
    }

    private fun wipeMoney4() {
        //承运方预估到手价角分抹零
        //计算结算价格(承运方预估到手价 10元以下抹零)
        val money9 = if (!TextUtils.isEmpty(item.overLossRuleId)) {
            //使用了亏涨吨
            if (TextUtils.equals("1", item.consignorUseDeductionFlag)) {
                //使用了加扣款
                item.deductionNoRateMoney?.toDoubleOrNull() ?: 0.00
            } else {
                //使用亏涨吨 未使用加扣款
                item.lossRiseConsignorNoRateMoney?.toDoubleOrNull() ?: 0.00
            }
        } else {
            if (item.consignorUseDeductionFlag.isTrue) {
                //使用了加扣款
                item.deductionNoRateMoney?.toDoubleOrNull() ?: 0.00
            } else {
                //未使用亏涨吨 也未使用加扣款
                item.consignorNoRateMoney?.toDoubleOrNull() ?: 0.00
            }
        }
        val money91 = if (!TextUtils.isEmpty(item.overLossRuleId)) {
            //使用了亏涨吨
            if (TextUtils.equals("1", item.consignorUseDeductionFlag)) {
                //使用了加扣款
                item.deductionRateMoney?.toDoubleOrNull() ?: 0.00
            } else {
                //使用亏涨吨 未使用加扣款
                item.lossRiseConsignorRateMoney?.toDoubleOrNull() ?: 0.00
            }
        } else {
            if (item.consignorUseDeductionFlag.isTrue) {
                //使用了加扣款
                item.deductionRateMoney?.toDoubleOrNull() ?: 0.00
            } else {
                //未使用亏涨吨 也未使用加扣款
                item.consignorRateMoney?.toDoubleOrNull() ?: 0.00
            }
        }
        //计算结算金额(承运方预估到手价) 角分抹零金额
        val money1 = money9.toInt()
        item.orderIgnoreSmallObj.ignoreSmallChangeNoRateMoney = money1.toDouble().toDoubleRoundDownString(8)
        //计算抹零差额
        val sub = NumUtil.sub(money9, money1.toDouble())
        //推算结算金额（承运方预估到手价）
        val rate = NumUtil.sum(1.00, item.getSettleRate())
        if (sub != 0.00) {
            //抹零金额为0时不逆推
            item.orderIgnoreSmallObj.ignoreSmallChangeRateMoney = NumUtil.mul(money1.toDouble(), rate).toDoubleRoundDownString(8)
        } else {
            item.orderIgnoreSmallObj.ignoreSmallChangeRateMoney = money91.toDoubleRoundDownString(2)
        }
        //计算抹零总金额
        item.orderIgnoreSmallObj.ignoreSmallChangeMoney = sub.toDoubleRoundDownString(8)
    }

    private fun wipeMoney3() {
        //含税价10元以下抹零
        val money9 = if (item.overLossRuleId.isNotNullOrNotEmpty()) {
            //使用了亏涨吨
            if (item.consignorUseDeductionFlag.isTrue) {
                //使用了加扣款
                item.deductionRateMoney?.toDoubleOrNull() ?: 0.00
            } else {
                //使用亏涨吨 未使用加扣款
                item.lossRiseConsignorRateMoney?.toDoubleOrNull() ?: 0.00
            }
        } else {
            if (item.consignorUseDeductionFlag.isTrue) {
                //使用了加扣款
                item.deductionRateMoney?.toDoubleOrNull() ?: 0.00
            } else {
                //未使用亏涨吨 也未使用加扣款
                item.consignorRateMoney?.toDoubleOrNull() ?: 0.00
            }
        }
        val money91 = if (item.overLossRuleId.isNotNullOrNotEmpty()) {
            //使用了亏涨吨
            if (item.consignorUseDeductionFlag.isTrue) {
                //使用了加扣款
                item.deductionNoRateMoney?.toDoubleOrNull() ?: 0.00
            } else {
                //使用亏涨吨 未使用加扣款
                item.lossRiseConsignorNoRateMoney?.toDoubleOrNull() ?: 0.00
            }
        } else {
            if (item.consignorUseDeductionFlag.isTrue) {
                //使用了加扣款
                item.deductionNoRateMoney?.toDoubleOrNull() ?: 0.00
            } else {
                //未使用亏涨吨 也未使用加扣款
                item.consignorNoRateMoney?.toDoubleOrNull() ?: 0.00
            }
        }
        //10元以下抹零 直接去除小数点后数值
        val toInt1 = money9.toInt()
        if (toInt1 < 10) {
            //数值小于10 直接10元以下抹零
            item.orderIgnoreSmallObj.ignoreSmallChangeRateMoney = "0.00"
            //计算抹零差额
            val sub = NumUtil.sub(money9, 0.00)
            //推算结算价格（承运方预估到手价） 10元以下抹零
            val rate = NumUtil.sum(1.00, item.getSettleRate())
            if (sub != 0.00) {
                //抹零金额为0时不逆推
                item.orderIgnoreSmallObj.ignoreSmallChangeNoRateMoney = NumUtil.div(0.00, rate, 2).toDoubleRoundDownString(8)
            } else {
                item.orderIgnoreSmallObj.ignoreSmallChangeNoRateMoney = money91.toDoubleRoundDownString(2)
            }
            //计算抹零总金额
            item.orderIgnoreSmallObj.ignoreSmallChangeMoney = sub.toDoubleRoundDownString(8)
        } else if (toInt1 > 10) {
            //大于10元 取最后一位整数
            val m3 = toInt1.toString().substring(toInt1.toString().length - 1).toInt()
            //抹零个位数 保留10位数
            val sub = NumUtil.sub(toInt1.toDouble(), m3.toDouble())
            item.orderIgnoreSmallObj.ignoreSmallChangeRateMoney = sub.toDoubleRoundDownString(8)
            //计算抹零差额
            val sub1 = NumUtil.sub(money9, sub)
            //计算抹零总金额
            item.orderIgnoreSmallObj.ignoreSmallChangeMoney = sub1.toDoubleRoundDownString(8)
            //推算结算价格（承运方预估到手价） 10元以下抹零
            val rate = NumUtil.sum(1.00, item.getSettleRate())
            if (sub1 != 0.00) {
                //抹零金额为0时不逆推
                item.orderIgnoreSmallObj.ignoreSmallChangeNoRateMoney = NumUtil.div(sub, rate, 2).toDoubleRoundDownString(8)
            } else {
                item.orderIgnoreSmallObj.ignoreSmallChangeNoRateMoney = money91.toDoubleRoundDownString(2)
            }
        } else {
            item.orderIgnoreSmallObj.ignoreSmallChangeRateMoney = toInt1.toDouble().toDoubleRoundDownString(8)
            //计算抹零差额
            val sub1 = NumUtil.sub(money9, toInt1.toDouble())
            //计算抹零总金额
            item.orderIgnoreSmallObj.ignoreSmallChangeMoney = sub1.toDoubleRoundDownString(8)
            //推算结算价格（承运方预估到手价） 10元以下抹零
            val rate = NumUtil.sum(1.00, item.getSettleRate())
            if (sub1 != 0.00) {
                //抹零金额为0时不逆推
                item.orderIgnoreSmallObj.ignoreSmallChangeNoRateMoney = NumUtil.div(toInt1.toDouble(), rate, 2).toDoubleRoundDownString(8)
            } else {
                item.orderIgnoreSmallObj.ignoreSmallChangeNoRateMoney = money91.toDoubleRoundDownString(2)
            }
        }
    }

    private fun wipeMoney2() {
        //承运方预估到手价10元以下抹零
        //计算结算价格(承运方预估到手价 10元以下抹零)
        val money9 = if (!TextUtils.isEmpty(item.overLossRuleId)) {
            //使用了亏涨吨
            if (TextUtils.equals("1", item.consignorUseDeductionFlag)) {
                //使用了加扣款
                item.deductionNoRateMoney?.toDoubleOrNull() ?: 0.00
            } else {
                //使用亏涨吨 未使用加扣款
                item.lossRiseConsignorNoRateMoney?.toDoubleOrNull() ?: 0.00
            }
        } else {
            if (item.consignorUseDeductionFlag.isTrue) {
                //使用了加扣款
                item.deductionNoRateMoney?.toDoubleOrNull() ?: 0.00
            } else {
                //未使用亏涨吨 也未使用加扣款
                item.consignorNoRateMoney?.toDoubleOrNull() ?: 0.00
            }
        }
        val money91 = if (!TextUtils.isEmpty(item.overLossRuleId)) {
            //使用了亏涨吨
            if (TextUtils.equals("1", item.consignorUseDeductionFlag)) {
                //使用了加扣款
                item.deductionRateMoney?.toDoubleOrNull() ?: 0.00
            } else {
                //使用亏涨吨 未使用加扣款
                item.lossRiseConsignorRateMoney?.toDoubleOrNull() ?: 0.00
            }
        } else {
            if (item.consignorUseDeductionFlag.isTrue) {
                //使用了加扣款
                item.deductionRateMoney?.toDoubleOrNull() ?: 0.00
            } else {
                //未使用亏涨吨 也未使用加扣款
                item.consignorRateMoney?.toDoubleOrNull() ?: 0.00
            }
        }
        val toInt1 = money9.toInt() //10元以下抹零 直接去除小数点后数值
        if (toInt1 < 10) {
            //数值小于10 直接10元以下抹零
            item.orderIgnoreSmallObj.ignoreSmallChangeNoRateMoney = "0.00"
            //计算抹零差额
            val sub = NumUtil.sub(money9, 0.00)
            //推算结算价格（承运方预估到手价） 10元以下抹零
            val rate = NumUtil.sum(1.00, item.getSettleRate())
            if (sub != 0.00) {
                //抹零金额为0时不逆推
                item.orderIgnoreSmallObj.ignoreSmallChangeRateMoney = NumUtil.mul(0.00, rate).toDoubleRoundDownString(8)
            } else {
                item.orderIgnoreSmallObj.ignoreSmallChangeRateMoney = money91.toDoubleRoundDownString(2)
            }
            //计算抹零总金额
            item.orderIgnoreSmallObj.ignoreSmallChangeMoney = sub.toDoubleRoundDownString(8)
        } else if (toInt1 > 10) {
            //大于10元 取最后一位整数
            val m3 = toInt1.toString().substring(toInt1.toString().length - 1).toInt()
            //抹零个位数 保留10位数
            val sub = NumUtil.sub(toInt1.toDouble(), m3.toDouble())
            item.orderIgnoreSmallObj.ignoreSmallChangeNoRateMoney = sub.toDoubleRoundDownString(8)
            //计算抹零差额
            val sub1 = NumUtil.sub(money9, sub)
            //计算抹零总金额
            item.orderIgnoreSmallObj.ignoreSmallChangeMoney = sub1.toDoubleRoundDownString(8)
            //推算结算价格（承运方预估到手价） 10元以下抹零
            val rate = NumUtil.sum(1.00, item.getSettleRate())
            if (sub1 != 0.00) {
                //抹零金额为0时不逆推
                item.orderIgnoreSmallObj.ignoreSmallChangeRateMoney = NumUtil.mul(sub, rate).toDoubleRoundDownString(8)
            } else {
                item.orderIgnoreSmallObj.ignoreSmallChangeRateMoney = money91.toDoubleRoundDownString(2)
            }
        } else {
            item.orderIgnoreSmallObj.ignoreSmallChangeNoRateMoney = toInt1.toDouble().toDoubleRoundDownString(8)
            //计算抹零差额
            val sub1 = NumUtil.sub(money9, toInt1.toDouble())
            //计算抹零总金额
            item.orderIgnoreSmallObj.ignoreSmallChangeMoney = sub1.toDoubleRoundDownString(8)
            //推算结算价格（承运方预估到手价） 10元以下抹零
            val rate = NumUtil.sum(1.00, item.getSettleRate())
            if (sub1 != 0.00) {
                //抹零金额为0时不逆推
                item.orderIgnoreSmallObj.ignoreSmallChangeRateMoney = NumUtil.mul(toInt1.toDouble(), rate).toDoubleRoundDownString(8)
            } else {
                item.orderIgnoreSmallObj.ignoreSmallChangeRateMoney = money91.toDoubleRoundDownString(2)
            }
        }
    }

    private fun wipeMoney1() {
        //含税价角分抹零
        val money9 = if (item.overLossRuleId.isNotNullOrNotEmpty()) {
            //使用了亏涨吨
            if (item.consignorUseDeductionFlag.isTrue) {
                //使用了加扣款
                item.deductionRateMoney?.toDoubleOrNull() ?: 0.00
            } else {
                //使用亏涨吨 未使用加扣款
                item.lossRiseConsignorRateMoney?.toDoubleOrNull() ?: 0.00
            }
        } else {
            if (item.consignorUseDeductionFlag.isTrue) {
                //使用了加扣款
                item.deductionRateMoney?.toDoubleOrNull() ?: 0.00
            } else {
                //未使用亏涨吨 也未使用加扣款
                item.consignorRateMoney?.toDoubleOrNull() ?: 0.00
            }
        }
        val money91 = if (item.overLossRuleId.isNotNullOrNotEmpty()) {
            //使用了亏涨吨
            if (item.consignorUseDeductionFlag.isTrue) {
                //使用了加扣款
                item.deductionNoRateMoney?.toDoubleOrNull() ?: 0.00
            } else {
                //使用亏涨吨 未使用加扣款
                item.lossRiseConsignorNoRateMoney?.toDoubleOrNull() ?: 0.00
            }
        } else {
            if (item.consignorUseDeductionFlag.isTrue) {
                //使用了加扣款
                item.deductionNoRateMoney?.toDoubleOrNull() ?: 0.00
            } else {
                //未使用亏涨吨 也未使用加扣款
                item.consignorNoRateMoney?.toDoubleOrNull() ?: 0.00
            }
        }
        //计算结算金额(含税) 角分抹零金额
        val money1 = money9.toInt()
        item.orderIgnoreSmallObj.ignoreSmallChangeRateMoney = money1.toDouble().toDoubleRoundDownString(8)
        //计算抹零差额
        val sub = NumUtil.sub(money9, money1.toDouble())
        //推算结算金额（承运方预估到手价）
        val rate = NumUtil.sum(1.00, item.getSettleRate())
        if (sub != 0.00) {
            //抹零金额为0时不逆推
            item.orderIgnoreSmallObj.ignoreSmallChangeNoRateMoney = NumUtil.div(money1.toDouble(), rate, 8).toDoubleRoundDownString(8)
        } else {
            item.orderIgnoreSmallObj.ignoreSmallChangeNoRateMoney = money91.toDoubleRoundDownString(2)
        }
        //计算抹零总金额
        item.orderIgnoreSmallObj.ignoreSmallChangeMoney = sub.toDoubleRoundDownString(8)
    }
}

fun getMoneyRateText(inputMoney: String?, settleRate: String?): String {
    if (!inputMoney.isNullOrEmpty()) {
        val a = inputMoney.toDouble()
        val b = NumUtil.sum(settleRate?.toDoubleOrNull() ?: 0.00, 1.00)
        return NumUtil.divEgnorNull(a, b, 2).toString()
    }
    return "0.00"
}

fun getTurnMoney(input: String?, settleRate: String?): String {
    if (!TextUtils.isEmpty(input)) {
        val inputBack = input?.trim()
        val a = inputBack?.toDoubleOrNull() ?: 0.00
        val b = NumUtil.sum(settleRate?.toDoubleOrNull() ?: 0.00, 1.00)
        val inputMoney = NumUtil.mulEgnorNull(a, b).roundTo2DecimalPlaces().toString()
        return inputMoney
    }
    return ""
}

fun getMoneyRateText(inputMoney: String?, settleRate: String?, moneyRate: String?): String {
    if (!inputMoney.isNullOrEmpty()) {
        val a = inputMoney.toDouble()
        val b = NumUtil.sum(settleRate?.toDoubleOrNull() ?: 0.00, 1.00)
        return NumUtil.divEgnorNull(a, b, 2).toString()
    }
    return moneyRate ?: "0.00"
}

fun computeMoney2(num1: String?, num2: String?): String {
    if (num1.isNullOrEmpty()) {
        return "0.00"
    }
    if (num2.isNullOrEmpty()) {
        return "0.00"
    }
    return NumUtil.div(num1.toDoubleOrNull() ?: 0.0, num2.toDoubleOrNull() ?: 1.00, 2).toString()
}

fun computeMoney1(num1: String?, num2: String?): String {
    if (num1.isNullOrEmpty()) {
        return "0.00"
    }
    if (num2.isNullOrEmpty()) {
        return "0.00"
    }
    return NumUtil.mul(num1.toDoubleOrNull() ?: 0.0, num2.toDoubleOrNull() ?: 0.00).toString()
}

fun getTurnMoney(input: String, settleRate: String?, money: String?): String {
    var inputMoney = "0.00"
    if (!TextUtils.isEmpty(input)) {
        val inputBack = input.trim()
        val a = inputBack.toDoubleOrNull() ?: 0.00
        val b = NumUtil.sum(settleRate?.toDoubleOrNull() ?: 0.00, 1.00)
        val inputMoney = NumUtil.mulEgnorNull(a, b).roundTo2DecimalPlaces().toString()
        return inputMoney
    }
    return money ?: "0.00"
}

fun Double.roundTo2DecimalPlaces(): Double {
    return BigDecimal(this).setScale(2, BigDecimal.ROUND_HALF_UP).toDouble()
}

fun Double.roundTo2DecimalPlacesV1(): Double {
    return BigDecimal(this).setScale(0, BigDecimal.ROUND_DOWN).toDouble()
}

fun Double.roundTo2DecimalPlaces(scale: Int = 2): String {
    return BigDecimal(this).setScale(scale, BigDecimal.ROUND_HALF_UP).toDouble().toDoubleRoundDownString(scale)
}

fun String?.roundTo2DecimalPlaces(scale: Int = 2): String {
    return if (this.isNullOrEmpty()) {
        "0.00"
    } else {
        BigDecimal(this).setScale(scale, BigDecimal.ROUND_HALF_UP).toDouble().toDoubleRoundDownString(scale)
    }
}
