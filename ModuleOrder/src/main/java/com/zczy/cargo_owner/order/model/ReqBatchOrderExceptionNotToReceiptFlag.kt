package com.zczy.cargo_owner.order.model

import com.zczy.cargo_owner.order.confirm.bean.ReqBatchConfirmData
import com.zczy.comm.http.entity.BaseNewRequest
import com.zczy.comm.http.entity.BaseRsp
import com.zczy.comm.http.entity.ResultData

/**
 *  user: ssp
 *  time: 2020/11/16 10:12
 *  desc:
 */
class ReqBatchOrderExceptionNotToReceiptFlag(
        var batchConfirmData: List<ReqBatchConfirmData>? = null//   批量确认数组,与回单确认最终提交数据一致
) : BaseNewRequest<BaseRsp<ResultData>>("oms-app/order/receipt/queryBatchOrderExceptionNotToReceiptFlag")
