package com.zczy.cargo_owner.order.detail;

import android.Manifest;
import android.app.Activity;
import android.content.Intent;
import android.graphics.BitmapFactory;
import android.os.Bundle;
import android.text.TextUtils;
import android.view.View;
import android.webkit.JavascriptInterface;
import android.widget.Toast;

import androidx.annotation.Nullable;

import com.google.gson.Gson;
import com.sfh.lib.mvvm.annotation.LiveDataMatch;
import com.sfh.lib.ui.AbstractLifecycleFragment;
import com.sfh.lib.ui.dialog.DialogBuilder;
import com.sfh.lib.utils.UtilTool;
import com.tencent.smtt.sdk.WebSettings;
import com.zczy.cargo_owner.libcomm.DeliverProvider;
import com.zczy.cargo_owner.libcomm.IDeliverProvider;
import com.zczy.cargo_owner.libcomm.config.HttpConfig;
import com.zczy.cargo_owner.libcomm.config.SubUserAuthUtils;
import com.zczy.cargo_owner.libcomm.event.EventRefreshWaybillList;
import com.zczy.cargo_owner.libcomm.utils.StringUtils;
import com.zczy.cargo_owner.libcomm.widget.CheckSelfPermissionDialog;
import com.zczy.cargo_owner.order.R;
import com.zczy.cargo_owner.order.change.deliverinfo.ui.OrderChangeDeliverInfoActivity;
import com.zczy.cargo_owner.order.confirm.ReturnedOrderConfirmListActivity;
import com.zczy.cargo_owner.order.confirm.v2.SingleReturnedOrderConfirmActivityV3;
import com.zczy.cargo_owner.order.detail.bean.ContractToPdf;
import com.zczy.cargo_owner.order.detail.model.WaybillDetailModel;
import com.zczy.cargo_owner.order.dialog.OrderPayBillDialog;
import com.zczy.cargo_owner.order.dialog.OrderPayBillTypeDialog;
import com.zczy.cargo_owner.order.entity.EWaybill;
import com.zczy.cargo_owner.order.mileage.CargoGoodsMapActivity;
import com.zczy.cargo_owner.order.model.ReqFreightPayment;
import com.zczy.cargo_owner.order.model.ReqPayCheck;
import com.zczy.cargo_owner.order.model.ReqTmsConsignorConfirmPay;
import com.zczy.cargo_owner.order.model.ReqTmsConsignorOfflinePayment;
import com.zczy.cargo_owner.order.model.RespConsignorConfirmPayMoney;
import com.zczy.cargo_owner.order.model.RspQueryChangeState;
import com.zczy.cargo_owner.order.model.RspTmsConsignorConfirmPay;
import com.zczy.cargo_owner.order.model.WayBillModelV1;
import com.zczy.cargo_owner.order.model.WaybillModel;
import com.zczy.cargo_owner.order.transport.WaybillTrackingActivityV1;
import com.zczy.cargo_owner.order.violate.OrderViolateListActivity;
import com.zczy.comm.CommServer;
import com.zczy.comm.X5BaseJavascriptInterface;
import com.zczy.comm.data.entity.EImage;
import com.zczy.comm.data.entity.ELogin;
import com.zczy.comm.http.entity.BaseRsp;
import com.zczy.comm.pay.PayDialog;
import com.zczy.comm.permission.PermissionCallBack;
import com.zczy.comm.permission.PermissionUtil;
import com.zczy.comm.pluginserver.AMainServer;
import com.zczy.comm.utils.Md5Util;
import com.zczy.comm.utils.PhoneUtil;
import com.zczy.comm.utils.imageselector.ImagePreviewActivity;
import com.zczy.comm.utils.json.JsonUtil;
import com.zczy.comm.videoplayer.VideoPlayActivity;
import com.zczy.comm.x5.X5WebView;
import com.zczy.lib_zshare.ZShare;
import com.zczy.lib_zshare.share.ShareInfo;

import org.json.JSONObject;

import java.util.ArrayList;
import java.util.List;
import java.util.Objects;

/**
 * 功能描述:运单详情
 *
 * <AUTHOR> 孙飞虎
 * @company 中储智运科技股份有限公司
 * @copyright （版权）中储南京智慧物流科技有限公司所有
 * @date 2019/1/30
 */
public class WaybillDetailFragment extends AbstractLifecycleFragment<WaybillDetailModel> {
    private static final int REQUEST_NORMAL_CHOOSE = 0x33;
    private static final int REQUEST_BATCH_CHOOSE = 0x34;
    private static final int REQUEST_BATCH_EDIT = 0x35;


    String orderId;

    X5WebView mAgentWeb;

    X5BaseJavascriptInterface jsUserInfoInterface;
    //详情中按钮是否显示
    String isShowBtn = "1";

    public static WaybillDetailFragment start(String orderId) {

        WaybillDetailFragment fragment = new WaybillDetailFragment();
        Bundle data = new Bundle();
        data.putString(WaybillDetailStatueActivity.EXTRA_ORDER_ID_STRING, orderId);
        fragment.setArguments(data);
        return fragment;
    }

    public static WaybillDetailFragment start(String orderId, String isShowBtn) {

        WaybillDetailFragment fragment = new WaybillDetailFragment();
        Bundle data = new Bundle();
        data.putString(WaybillDetailStatueActivity.EXTRA_ORDER_ID_STRING, orderId);
        data.putString("isShowBtn", isShowBtn);
        fragment.setArguments(data);
        return fragment;
    }

    @Override
    public int getLayout() {

        return R.layout.order_waybill_detail_web_fragment;
    }

    @Override
    public void initData(View view) {

        mAgentWeb = view.findViewById(R.id.webLayout);
        WebSettings webViewSettings = mAgentWeb.getSettings();
        String baseAgent = webViewSettings.getUserAgentString();
        webViewSettings.setUserAgent(baseAgent + ";app/ANDROID");
        mAgentWeb.setVerticalScrollBarEnabled(false); //垂直不显示
        mAgentWeb.setHorizontalScrollBarEnabled(false); //垂直不显示
        Bundle data = getArguments();
        if (data != null) {
            orderId = data.getString(WaybillDetailStatueActivity.EXTRA_ORDER_ID_STRING);
            isShowBtn = data.getString("isShowBtn", "1");
            mAgentWeb.loadUrl(this.getDeliveryDetail(orderId));
        }
    }


    /**
     * 运单详情访问地址：
     */
    private String getDeliveryDetail(String orderId) {
        return HttpConfig.getWebUrl()
                + "form_h5/h5_inner/index.html?_t=" + System.currentTimeMillis()
                + "#/deliveryDetail?orderId=" + orderId + "&version=" + UtilTool.getVersionCode(getContext()) + "&isShowBtn=" + isShowBtn + "&currentVersion=" + UtilTool.getVersion(getContext());
    }

    private void refreshDetailPage() {
        mAgentWeb.loadUrl("javascript:refreshDetailPage()");
    }

    @Override
    public void onActivityCreated(@Nullable Bundle savedInstanceState) {
        super.onActivityCreated(savedInstanceState);
        jsUserInfoInterface = getX5BaseJavascriptInterface();
        mAgentWeb.addJavascriptInterface(jsUserInfoInterface, "android");
    }


    @Override
    public void onDestroy() {
        //释放资源
        if (mAgentWeb != null) {
            mAgentWeb.destroy();
        }
        if (jsUserInfoInterface != null) {
            jsUserInfoInterface.destroy();
        }
        super.onDestroy();
    }

    @Override
    public void onActivityResult(int requestCode, int resultCode, Intent data) {
        super.onActivityResult(requestCode, resultCode, data);
        if (resultCode == Activity.RESULT_OK) {
            switch (requestCode) {
                case REQUEST_BATCH_EDIT:
                case REQUEST_NORMAL_CHOOSE:
                case REQUEST_BATCH_CHOOSE:
                    refreshDetailPage();
                    break;
                default:
                    break;
            }
        }
    }

    @LiveDataMatch(tag = "查询合同pdf")
    public void addContractSuccess(ContractToPdf data) {
        String url = data.getFilePath();
        String url2 = "http://img.zczy56.com/" + url;
        String[] list = new String[]{Manifest.permission.WRITE_EXTERNAL_STORAGE, Manifest.permission.READ_EXTERNAL_STORAGE};
        CheckSelfPermissionDialog.storagePermissionDialog(getActivity(), new PermissionCallBack() {
            @Override
            public void onHasPermission() {
                PermissionUtil.checkPermissions(getActivity(), "请允许访问您的存储空间", list, new PermissionCallBack() {
                    @Override
                    public void onHasPermission() {
                        getViewModel().loadFile(url2, url, getContext());
                    }
                });
            }
        });

    }

    @LiveDataMatch(tag = "下载文件成功")
    public void downLoadSuccess(String path) {
        showDialogToast("文件保存在" + path);
    }

    private X5BaseJavascriptInterface getX5BaseJavascriptInterface() {

        return new X5BaseJavascriptInterface(getActivity()) {

            private final Gson gson = new Gson();

            @JavascriptInterface
            @Override
            public void copyOrderId(String code) {
                UtilTool.setCopyText(getActivity(), "单号", code);
                Toast.makeText(getActivity(), "复制成功", Toast.LENGTH_SHORT).show();
            }

            @JavascriptInterface
            public void previewImgs(String[] imgs) {
                if (imgs != null) {
                    List<EImage> list = new ArrayList<>();
                    for (String img : imgs) {
                        EImage eImage = new EImage();
                        eImage.setNetUrl(img);
                        list.add(eImage);
                    }
                    ImagePreviewActivity.start(getActivity(), list);
                }
            }

            @JavascriptInterface
            public void viewOrderNodes(String orderId) {

                // 查看运单状态 参数：运单号
                WaybillStatusActivity.start(getActivity(), orderId);
            }

            @JavascriptInterface
            public void checkDepositStatus(String orderId) {
                AMainServer.getPluginServer().openRNActivity(getActivity(), "DepositIncomeRecordPage", "{\"orderId\":\"" + orderId + "\"}");
            }

            @JavascriptInterface
            public void viewRoute(String orderDetail) {
                WaybillBean waybillBean = JsonUtil.toJsonObject(orderDetail, WaybillBean.class);
                CargoGoodsMapActivity.startUI(getContext(), waybillBean.getOrderId());
            }

            @JavascriptInterface
            public void breachApplyFn(String orderDetail) {
                // 违约申请 公用【功能】
                try {
                    EWaybill waybill = new Gson().fromJson(orderDetail, EWaybill.class);

                    getActivity().runOnUiThread(new Runnable() {

                        @Override
                        public void run() {
                            // 违约申请   非指定 && 预付 违约提示 检查
                            getViewModel(WaybillModel.class).checkNospecify(waybill);
                        }
                    });
                } catch (Exception e) {
                    showDialogToast(e.getMessage());
                }
            }

            @JavascriptInterface
            public void settlementVoucher(String detail) {
                //跳转发货单修改
                String linkUrl = HttpConfig.getWebUrl("form_h5/order/index.html?_t=" + System.currentTimeMillis() + "#/settlementVoucher?orderId=" + orderId);
                AMainServer mainServer = AMainServer.getPluginServer();
                mainServer.jumpWebActivityHomeBanne(getActivity(), linkUrl, orderId);
//                try {
//                    JSONObject jsonObject = new JSONObject(detail);
//                    detail = jsonObject.getString("detail");
//                    final EWaybill waybill = new Gson().fromJson(detail, EWaybill.class);
//                    String orderId = waybill.getOrderId();
//
//                    AMainServer  mainServer = AMainServer.getPluginServer();
//                    mainServer.jumpWebActivityHomeBanne(getActivity(),linkUrl);
//                } catch (JSONException e) {
//                    e.printStackTrace();
//                }
            }

            @JavascriptInterface
            public void showContractFn(String orderDetail) {
                //合同
                try {
                    JSONObject jsonObject = new JSONObject(orderDetail);

                    WaybillContractDetailActivity.start(getActivity(), orderId, jsonObject.getString("contractNameCustomer"), false);
                } catch (Exception e) {

                }

            }

            @JavascriptInterface
            public void download() {
                getViewModel().addContract(orderId);
            }


            @JavascriptInterface
            public void trackOrder(String orderDetail) {

                // 在途跟踪
                WaybillBean waybillBean = JsonUtil.toJsonObject(orderDetail, WaybillBean.class);
                WaybillTrackingActivityV1.startUI(getActivity(), waybillBean.getOrderId(), waybillBean.getPlateNumber());


            }

            /**
             * 取消发布---调度认领的运单，点击取消运单，增加交互
             */
            @JavascriptInterface
            public void cancleDispatchOrder(String orderDetail) {
                // 取消运单进入违约申请
                OrderViolateListActivity.startContentUI(getActivity(), orderId, true);
            }

            /**
             * 取消发布（
             */
            @JavascriptInterface
            public void cancelReleaseFn() {
            }

            @JavascriptInterface
            public void shareOrder(String url, String title, String content) {
                Activity a = getActivity();
                if (a != null) {
                    a.runOnUiThread(() -> {
//                        AMainServer mainServer = AMainServer.getPluginServer();
//                        if (mainServer != null) {
//                            mainServer.jumpShareDialog(getContext(), title, content, url);
//                        }

                        ShareInfo info = new ShareInfo();
                        info.title = title;
                        info.content = content;
                        info.webUrl = url;
                        info.thumbnail = BitmapFactory.decodeResource(getResources(), R.drawable.ic_launcher);
                        ZShare.share(getActivity(), info);
                    });
                }
            }

            @JavascriptInterface
            public void shareOrder(String url, String title) {
                Activity a = getActivity();
                if (a != null) {
                    a.runOnUiThread(() -> {
//                        AMainServer mainServer = AMainServer.getPluginServer();
//                        if (mainServer != null) {
//                            mainServer.jumpShareDialog(getContext(), title, url);
//                        }

                        ShareInfo info = new ShareInfo();
                        info.title = title;
                        info.content = url;
                        info.webUrl = url;
                        info.thumbnail = BitmapFactory.decodeResource(getResources(), R.drawable.ic_launcher);
                        ZShare.share(getActivity(), info);
                    });
                }
            }

            /**
             * 选择承运方（
             */
            @JavascriptInterface
            public void chooseCarrierFn(String detail) {

                IDeliverProvider deliver = DeliverProvider.deliver;
                if (deliver != null) {
                    deliver.openDeliverNormalChooseActivity(
                            WaybillDetailFragment.this,
                            detail,
                            REQUEST_NORMAL_CHOOSE);
                }
            }

            /**
             *批量货选择保价特惠运力
             */
            @JavascriptInterface
            public void batchChooseCarrierFnV1() {
                IDeliverProvider deliver = DeliverProvider.deliver;
                if (deliver != null) {
                    deliver.openDeliverNormalChooseActivityV1(
                            WaybillDetailFragment.this,
                            orderId,
                            true,
                            REQUEST_BATCH_CHOOSE
                    );
                }
            }

            /**
             *普通货选择保价特惠运力
             */
            @JavascriptInterface
            public void normalChooseCarrierFnV1() {
                IDeliverProvider deliver = DeliverProvider.deliver;
                if (deliver != null) {
                    deliver.openDeliverNormalChooseActivityV1(
                            WaybillDetailFragment.this,
                            orderId,
                            false,
                            REQUEST_BATCH_CHOOSE
                    );
                }
            }

            /**
             * 批量货管理进入运单详情（
             */
            @JavascriptInterface
            public void batchChooseCarrierFn(String detail) {
                BatchTemp batchTemp = JsonUtil.toJsonObject(detail, BatchTemp.class);
                if (batchTemp == null) {
                    showToast("数据异常");
                    return;
                }
                //  子账号权限
                String moBatchOrder = StringUtils.getChildPermission(SubUserAuthUtils.get().getMoBatchOrder());
                if (!TextUtils.isEmpty(moBatchOrder)) {
                    showDialogToast(moBatchOrder);
                    return;
                }
                int i = 0;
                try {
                    i = Integer.parseInt(batchTemp.expectNum);
                } catch (Exception e) {
                    e.printStackTrace();
                }
                if (i <= 0) {
                    showToast("暂无报价");
                    return;
                }
                IDeliverProvider deliver = DeliverProvider.deliver;
                if (deliver != null) {
                    deliver.openDeliverBatchChooseActivity(
                            WaybillDetailFragment.this,
                            detail,
                            REQUEST_BATCH_CHOOSE);
                }
            }


            /**
             * 修改（货主（批量货管理进入运单详情））：
             */
            @JavascriptInterface
            public void updateBatchFn(String detail) {
                IDeliverProvider deliver = DeliverProvider.deliver;
                if (deliver != null) {
                    deliver.openDeliverBatchEditActivity(
                            WaybillDetailFragment.this,
                            detail,
                            REQUEST_NORMAL_CHOOSE);
                }
            }

            @JavascriptInterface
            public void callService(String phone) {
                // 客服电话
                PhoneUtil.callPhone(getContext(), phone);
            }

            @JavascriptInterface
            public void contactDriver(String phone) {
                // 联系司机
                PhoneUtil.callPhone(getContext(), phone);
            }

            @JavascriptInterface
            public void reviewBill(String url, String title) {
                // 去审核回单
                ReturnedOrderConfirmListActivity.start(getContext(), 0, "");
            }

            @JavascriptInterface
            public void anotherOne(String detail, boolean isHuge) {
                // 再来一单
                IDeliverProvider deliver = DeliverProvider.deliver;
                if (deliver != null) {
                    deliver.openDeliverDraftsEditActivityV2(
                            WaybillDetailFragment.this,
                            orderId,
                            detail,
                            isHuge,
                            0x101
                    );
                }
            }

            @JavascriptInterface
            public void copyChar(String carNumber) {
                UtilTool.setCopyText(getContext(), "车牌号码", carNumber);
                showToast("复制车牌号码成功");
            }

            @JavascriptInterface
            public void openWisdomHomePage() {
                //跳转智运账本页面
                AMainServer.getPluginServer().changeMenu(WaybillDetailFragment.this.getContext(), AMainServer.MENU_WIDSON);
            }

            @JavascriptInterface
            public void openVideo(String videoUrl) {
                Intent intent = new Intent(getActivity(), VideoPlayActivity.class);
                intent.putExtra("videoUri", HttpConfig.getUrlImage(videoUrl));
                startActivity(intent);
            }

            @JavascriptInterface
            public void cancelTMSReleaseFn(String detail) {
                // 取消发布
                getActivity().runOnUiThread(new Runnable() {
                    @Override
                    public void run() {
                        DialogBuilder dialogBuilder = new DialogBuilder()
                                .setMessage("确认取消发布吗？").setOkListener((DialogBuilder.DialogInterface dialog, int which) -> {
                                    dialog.dismiss();
                                    getViewModel(WaybillModel.class).cancelOrder(orderId);
                                });
                        showDialog(dialogBuilder);
                    }
                });

            }

            @JavascriptInterface
            public void canTMSChange(String detail) {
                //  TMS 变更
                EWaybill data = new Gson().fromJson(detail, EWaybill.class);
                requireActivity().runOnUiThread(() -> getViewModel().queryChangeState(getActivity(), data));
            }

            @JavascriptInterface
            public void anotherTMSOne(String detail) {
                // TMS 再来一单
                IDeliverProvider deliver = DeliverProvider.deliver;
                if (deliver != null) {
                    deliver.openDeliverDraftsEditActivityV3(
                            WaybillDetailFragment.this,
                            orderId,
                            detail,
                            0x101
                    );
                }
            }

            @JavascriptInterface
            public void payTMSFeeFn(String detail) {
                // 支付
                EWaybill data = new Gson().fromJson(detail, EWaybill.class);
                requireActivity().runOnUiThread(() -> {
                    if (TextUtils.equals(data.getInvoiceType(), "2")) {
                        //不开票
                        new OrderPayBillTypeDialog().setOnPayBlock(payType -> {
                            if (TextUtils.equals(payType, "1")) {
                                DialogBuilder dialogBuilder = new DialogBuilder();
                                dialogBuilder.setMessage("确认后，运单完结线上不再展示支付运费入口，司机会收到已完成运费支付的提示，若后续司机反馈未收到运费，核实后将会影响您再平台后续发单及其他权益，请谨慎操作");
                                dialogBuilder.setTitle("提示");
                                dialogBuilder.setHideCancel(false);
                                dialogBuilder.setOKTextListener("确定", (dialogInterface, i) -> {
                                    dialogInterface.dismiss();
                                    // 立即支付
                                    getViewModel(WaybillModel.class).execute(false, new ReqTmsConsignorOfflinePayment(orderId), rsp -> {
                                        if (rsp.success()) {
                                            WaybillDetailFragment.this.mAgentWeb.reload();
                                        } else {
                                            showDialogToast(rsp.getMsg());
                                        }
                                    });
                                });
                                showDialog(dialogBuilder);
                            } else {
                                getViewModel(WaybillModel.class).consignorConfirmPay(getActivity(), orderId);
                            }
                            return null;
                        });
                    } else {
                        getViewModel(WaybillModel.class).consignorConfirmPay(getActivity(), orderId);
                    }
                });
            }

            @JavascriptInterface
            public void breachTMSApplyFn(String detail) {
                // 违约申请
                EWaybill waybill = new Gson().fromJson(detail, EWaybill.class);
                getActivity().runOnUiThread(new Runnable() {
                    @Override
                    public void run() {
                        // 违约申请   非指定 && 预付 违约提示 检查
                        getViewModel(WaybillModel.class).checkNospecify(waybill);
                    }
                });
            }

            @JavascriptInterface
            public void signTMSAgreementFn(String detail) {
                // TMS 签订协议
                EWaybill data = new Gson().fromJson(detail, EWaybill.class);
                WaybillContractDetailActivity.start(getContext(), data.getOrderId(), true, true, false);
            }

            @JavascriptInterface
            public void receiveTMSGoodsFn(String detail) {
                //  TMS 确认收货
                EWaybill waybill = new Gson().fromJson(detail, EWaybill.class);
                SingleReturnedOrderConfirmActivityV3.jumpPage(WaybillDetailFragment.this.getContext(), waybill.getOrderId());
            }

            @JavascriptInterface
            public void applyTMSInvoiceFn(String detail) {
                // 申请开票
                AMainServer.getPluginServer().openInvoiceCenterActivity(getActivity());
            }

            @JavascriptInterface
            public void showTMSContractFn(String detail) {
                // 查看合同
                EWaybill waybill = new Gson().fromJson(detail, EWaybill.class);
                WaybillContractDetailActivity.start(getContext(), waybill.getOrderId(), false, true, false);
            }
        };
    }

    @LiveDataMatch
    public void queryConsignorConfirmPayMoney(RespConsignorConfirmPayMoney respConsignorConfirmPayMoney) {
        new OrderPayBillDialog(respConsignorConfirmPayMoney.getMoney())
                .setPayBlock((orderPayBillDialog, view) -> {
                    // 立即支付
                    getViewModel(WaybillModel.class).execute(false, new ReqTmsConsignorConfirmPay(orderId), rsp -> {
                        if (rsp.success()) {
                            RspTmsConsignorConfirmPay data = rsp.getData();
                            if (data != null) {
                                ReqPayCheck reqPayCheck = new ReqPayCheck();
                                reqPayCheck.setApplyBatchNo(data.getModel());
                                reqPayCheck.setConsignorSettleMoney(data.getConsignorSettleMoney());
                                reqPayCheck.setCarrierSettleMoney(data.getCarrierSettleMoney());
                                reqPayCheck.setCheckMoney(data.getConsignorSettleMoney());
                                reqPayCheck.setSubsidiaryId(data.getSubsidiaryId());
                                getViewModel(WayBillModelV1.class).payCheck(reqPayCheck, getContext(), WaybillDetailFragment.this);
                            }
                        } else {
                            showDialogToast(rsp.getMsg());
                        }
                    });
                    return null;
                }).show(WaybillDetailFragment.this);
    }

    @LiveDataMatch
    public void payCheckSuccess(ReqPayCheck reqPayCheck) {
        new PayDialog(getContext(), reqPayCheck.getConsignorSettleMoney(), (keyPwd, dialog) -> {
            ReqFreightPayment reqFreightPayment = new ReqFreightPayment();
            reqFreightPayment.setPassword(Md5Util.mmd5(keyPwd));
            reqFreightPayment.setApplyBatchNo(reqPayCheck.getApplyBatchNo());
            reqFreightPayment.setConsignorSettleMoney(reqPayCheck.getConsignorSettleMoney());
            reqFreightPayment.setCarrierSettleMoney(reqPayCheck.getCarrierSettleMoney());
            reqFreightPayment.setOrderId(orderId);
            ELogin login = CommServer.getUserServer().getLogin();
            reqFreightPayment.setConsignorUserId(login.getUserId());
            reqFreightPayment.setConsignorCustomerId(login.getCustomerId());
            getViewModel(WayBillModelV1.class).freightPayment(reqFreightPayment, (dealType, errorMsg) -> {
                requireActivity().runOnUiThread(() -> {
                    if (TextUtils.equals("1", dealType)) {
                        //支付成功
                        this.mAgentWeb.reload();
                        this.postEvent(new EventRefreshWaybillList("WaybillDetailFragment"));
                        dialog.dismiss();
                    } else if (TextUtils.equals("1", dealType)) {
                        //多次输入锁定
                        dialog.showLockMsg(errorMsg);
                    } else {
                        dialog.dismiss();
                    }
                });
                return null;
            });
        }).show(mRoot);
    }

    @LiveDataMatch(tag = "刷新")
    public void onCancelConsignorOrder() {
        this.mAgentWeb.reload();
        this.postEvent(new EventRefreshWaybillList("WaybillDetailFragment"));
    }

    @LiveDataMatch(tag = "违约申请检查")
    public void onViolateSuccess(boolean isExitSize, EWaybill data) {
        OrderViolateListActivity.startContentUI(getActivity(), data.getOrderId());
    }


    private class BatchTemp {
        String expectNum = "";
    }

    @LiveDataMatch
    public void onQueryChangeState(BaseRsp<RspQueryChangeState> res, EWaybill data) {
        OrderChangeDeliverInfoActivity.start(getContext(), data.getOrderId(), data.getOrderCurrentStateId(), true);
    }

}


