package com.zczy.cargo_owner.order.consignor.ui

import android.content.Context
import android.os.Bundle
import androidx.fragment.app.Fragment
import android.view.View
import com.chad.library.adapter.base.BaseQuickAdapter
import com.chad.library.adapter.base.listener.OnItemClickListener
import com.sfh.lib.mvvm.annotation.LiveDataMatch
import com.sfh.lib.utils.UtilLog
import com.zczy.cargo_owner.libcomm.config.HttpConfig
import com.zczy.cargo_owner.order.R
import com.zczy.cargo_owner.order.consignor.adapter.OrderConsignorConfirmAdapter2Confirmed
import com.zczy.cargo_owner.order.consignor.model.OrderConsignorConfirmFragmentModel
import com.zczy.cargo_owner.order.consignor.req.Rsp1QueryConsignorOrderStateOfMobile
import com.zczy.cargo_owner.order.consignor.req.RspQueryDeliverOrderPicForMobile
import com.zczy.cargo_owner.order.detail.WaybillDetailStatueActivity
import com.zczy.comm.data.entity.EImage
import com.zczy.comm.http.entity.PageList
import com.zczy.comm.ui.BaseFragment
import com.zczy.comm.utils.CommUtils
import com.zczy.comm.utils.dp2px
import com.zczy.comm.utils.imageselector.ImagePreviewActivity
import com.zczy.comm.widget.pulltorefresh.CommEmptyView
import com.zczy.comm.widget.pulltorefresh.OnLoadingListener
import kotlinx.android.synthetic.main.order_common_fragment.*

/** 功能描述:
 * 发货单确认 已确认界面
 * <AUTHOR>
 * @company 中储智运科技股份有限公司
 * @copyright （版权）中储南京智慧物流科技有限公司所有
 * @date 2019/4/3
 */
open class OrderConsignorConfirmFragment2Confirmed : BaseFragment<OrderConsignorConfirmFragmentModel>() {

    private val mAdapter = OrderConsignorConfirmAdapter2Confirmed()

    companion object {
        fun newInstance(context: Context): OrderConsignorConfirmFragment2Confirmed {
            val args = Bundle()
            return androidx.fragment.app.Fragment.instantiate(context, OrderConsignorConfirmFragment2Confirmed::class.java.name, args)
                    as OrderConsignorConfirmFragment2Confirmed
        }
    }

    override fun getLayout(): Int = R.layout.order_common_fragment

    override fun bindView(view: View, bundle: Bundle?) {
        val emptyView = CommEmptyView.creatorDef(context)
        swipe_refresh_more_layout.apply {
            setAdapter(mAdapter, true)
            setEmptyView(emptyView)
            addItemDecorationSize(dp2px(7f))
            addOnItemListener(onItemClickListener)
            setOnLoadListener(object : OnLoadingListener {
                override fun onRefreshUI(nowPage: Int) {
                    viewModel?.getListInfo("1", nowPage)
                }

                override fun onLoadMoreUI(nowPage: Int) {
                    viewModel?.getListInfo("1", nowPage)
                }
            })
        }
    }

    override fun initData() {
        UtilLog.e(TAG, "initData")
        swipe_refresh_more_layout.onAutoRefresh()
    }

    override fun onSingleClick(v: View) {
        super.onSingleClick(v)
        when (v.id) {
        }
    }

    override fun refresh() {
        if (!isFirstLoad) {
            initData()
        }
    }

    private val onItemClickListener = object : OnItemClickListener() {
        override fun onSimpleItemClick(adapter: BaseQuickAdapter<*, *>, view: View, position: Int) {
            val item = adapter.getItem(position)
            if (item is Rsp1QueryConsignorOrderStateOfMobile) {
            }
        }

        override fun onItemChildClick(adapter: BaseQuickAdapter<*, *>, view: View, position: Int) {
            super.onItemChildClick(adapter, view, position)
            val item = adapter.getItem(position)
            if (item is Rsp1QueryConsignorOrderStateOfMobile) {
                when (view.id) {
                    // 复制
                    R.id.tv_order_copy -> {
                        val isCopy = CommUtils.copyText(context,
                                "运单号", item.orderId)
                        showToast(if (isCopy) "复制成功" else "复制失败")
                    }
                    // 运单号
                    R.id.tv_order_id -> {
                        WaybillDetailStatueActivity.start(context, item.orderId)
                    }

                    R.id.tv_look -> {
                        viewModel?.getPicture(item.orderId)
                    }
                }
            }
        }
    }

    @LiveDataMatch
    open fun onGetListInfo(data: PageList<Rsp1QueryConsignorOrderStateOfMobile>?) {
        swipe_refresh_more_layout.onRefreshCompale(data)
    }

    @LiveDataMatch
    open fun onGetPicture(data: PageList<RspQueryDeliverOrderPicForMobile>?) {
        val itemList : MutableList<RspQueryDeliverOrderPicForMobile>? = data?.rootArray
        if(itemList != null && itemList.size>0){
            val list: MutableList<EImage> = ArrayList<EImage>(itemList.size)
            for (RspQueryDeliverOrderPicForMobile in itemList) {
                val image = EImage()
                image.netUrl = HttpConfig.getUrlImage(RspQueryDeliverOrderPicForMobile.imageUrl)
                list.add(image)
            }
            ImagePreviewActivity.start(this, list, 0)
        }
    }
}