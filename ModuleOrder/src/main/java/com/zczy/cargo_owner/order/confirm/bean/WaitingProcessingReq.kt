package com.zczy.cargo_owner.order.confirm.bean

import com.zczy.comm.http.entity.BaseNewRequest
import com.zczy.comm.http.entity.BaseRsp
import com.zczy.comm.http.entity.ResultData

/**
 * @description
 * <AUTHOR>
 * @company 中储智运科技股份有限公司
 * @copyright （版权）中储南京智慧物流科技有限公司所有
 * @since 2019-11-21
 */
data class WaitingProcessingReq(val detailIds: String,val dealReason: String)
    : BaseNewRequest<BaseRsp<ResultData>>("oms-app/order/receipt/consignorBatchToReceiptDealConfirm")