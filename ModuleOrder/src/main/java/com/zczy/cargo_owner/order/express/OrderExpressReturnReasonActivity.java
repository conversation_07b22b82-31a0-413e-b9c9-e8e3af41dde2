package com.zczy.cargo_owner.order.express;

import android.app.Activity;
import android.content.Intent;
import android.graphics.Color;
import android.os.Bundle;
import androidx.annotation.Nullable;
import android.view.View;
import android.widget.LinearLayout;
import android.widget.TextView;

import com.sfh.lib.event.RxBusEventManager;
import com.sfh.lib.mvvm.annotation.LiveDataMatch;
import com.sfh.lib.ui.AbstractLifecycleActivity;
import com.zczy.cargo_owner.order.R;
import com.zczy.cargo_owner.order.express.model.OrderConsignorExpressReturnModel;
import com.zczy.cargo_owner.order.express.req.ReqConsignorExpressReturn;
import com.zczy.comm.ui.UtilStatus;

public class OrderExpressReturnReasonActivity extends AbstractLifecycleActivity<OrderConsignorExpressReturnModel> implements View.OnClickListener {

    private LinearLayout reason_nopape;
    private LinearLayout reason_erro;
    private LinearLayout nm_erro;
    private TextView reason_nopape_check;
    private TextView reason_erro_check;
    private TextView nm_erro_check;
    private TextView tv_return;
    private String reason = "";
    ReqConsignorExpressReturn reqConsignorExpressReturn = new ReqConsignorExpressReturn();
    private String detailIds;
    private String orderId;
    private String expressId;

    public static void startContentUI(Activity context, String detailIds, String orderId, String expressId) {

        Intent intent = new Intent(context, OrderExpressReturnReasonActivity.class);
        intent.putExtra("detailIds", detailIds);
        intent.putExtra("orderId", orderId);
        intent.putExtra("expressId", expressId);
        context.startActivity(intent);
    }

    @Override
    protected void onCreate(@Nullable Bundle savedInstanceState) {
        super.onCreate(savedInstanceState);
        setContentView(R.layout.order_express_return_reasonl_activity);
        UtilStatus.initStatus(this, Color.WHITE);
        initView();
    }

    private void initView() {
        detailIds = getIntent().getStringExtra("detailIds");
        orderId = getIntent().getStringExtra("orderId");
        expressId = getIntent().getStringExtra("expressId");
        reason_nopape = (LinearLayout) findViewById(R.id.reason_nopape);
        reason_erro = (LinearLayout) findViewById(R.id.reason_erro);
        nm_erro = (LinearLayout) findViewById(R.id.nm_erro);
        reason_nopape_check = (TextView) findViewById(R.id.reason_nopape_check);
        reason_erro_check = (TextView) findViewById(R.id.reason_erro_check);
        nm_erro_check = (TextView) findViewById(R.id.nm_erro_check);
        tv_return = (TextView) findViewById(R.id.tv_return);
        reason_nopape.setOnClickListener(this);
        reason_erro.setOnClickListener(this);
        nm_erro.setOnClickListener(this);
        reqConsignorExpressReturn.setDetailId(detailIds);
        reqConsignorExpressReturn.setExpressId(expressId);
        reqConsignorExpressReturn.setOrderId(orderId);
        tv_return.setOnClickListener(this);
    }

    @Override
    public void onClick(View v) {
        if (v.getId() == R.id.tv_return) {
            boolean rn = this.reason_nopape_check.isEnabled();
            boolean re = this.reason_erro_check.isEnabled();
            boolean ne = this.nm_erro_check.isEnabled();
            if (rn) {
                reason = "未收到回单";
                reqConsignorExpressReturn.setRepulseReason(reason);
                getViewModel().consignorExpressReturn(reqConsignorExpressReturn);
                return;
            }
            if (re) {
                reason = "回单邮寄错误";
                reqConsignorExpressReturn.setRepulseReason(reason);
                getViewModel().consignorExpressReturn(reqConsignorExpressReturn);
                return;
            }
            if (ne) {
                reason = "快递单号错误";
                reqConsignorExpressReturn.setRepulseReason(reason);
                getViewModel().consignorExpressReturn(reqConsignorExpressReturn);
                return;
            }
            showDialogToast("请选择一种打回方式");
        } else if (v.getId() == R.id.reason_nopape) {
            this.reason_nopape_check.setEnabled(!this.reason_nopape_check.isEnabled());
            this.reason_erro_check.setEnabled(false);
            this.nm_erro_check.setEnabled(false);
            tv_return.setEnabled(true);
        } else if (v.getId() == R.id.reason_erro) {
            this.reason_erro_check.setEnabled(!this.reason_erro_check.isEnabled());
            this.reason_nopape_check.setEnabled(false);
            this.nm_erro_check.setEnabled(false);
            tv_return.setEnabled(true);
        } else if (v.getId() == R.id.nm_erro) {
            this.nm_erro_check.setEnabled(!this.nm_erro_check.isEnabled());
            this.reason_nopape_check.setEnabled(false);
            this.reason_erro_check.setEnabled(false);
            tv_return.setEnabled(true);
        }
    }

    @LiveDataMatch(tag = "回单打回")
    public void onConsignorExpressReturn(String msg) {
        showToast("快递打回成功");
        RxBusEventManager.postEvent("回单打回");
        finish();
    }
}
