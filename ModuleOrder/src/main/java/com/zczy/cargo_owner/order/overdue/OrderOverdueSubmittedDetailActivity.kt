package com.zczy.cargo_owner.order.overdue

import android.content.Context
import android.content.Intent
import android.os.Bundle
import android.view.View
import com.chad.library.adapter.base.BaseQuickAdapter
import com.chad.library.adapter.base.listener.OnItemClickListener
import com.sfh.lib.mvvm.annotation.LiveDataMatch
import com.zczy.cargo_owner.libcomm.config.HttpConfig
import com.zczy.cargo_owner.order.R
import com.zczy.cargo_owner.order.overdue.adapter.OrderOverdueSubmitDetailImgAdapter
import com.zczy.cargo_owner.order.overdue.model.OrderOverdueFragmentModel
import com.zczy.cargo_owner.order.overdue.req.RspQueryProblemFeedbackDetail
import com.zczy.comm.data.entity.EImage
import com.zczy.comm.ui.BaseActivity
import com.zczy.comm.utils.imageselector.ImagePreviewActivity
import kotlinx.android.synthetic.main.order_overdue_submitted_detail_activity.*


/**
 * PS: 逾期运单问题反馈详情
 * Created by zzf
 */
class OrderOverdueSubmittedDetailActivity : BaseActivity<OrderOverdueFragmentModel>() {
    private val imgAdapter: OrderOverdueSubmitDetailImgAdapter = OrderOverdueSubmitDetailImgAdapter()

    companion object {
        @JvmStatic
        fun start(context: Context?, feedbackId: String?) {
            context ?: return
            val intent = Intent(context, OrderOverdueSubmittedDetailActivity::class.java)
            intent.putExtra("feedbackId", feedbackId)
            context.startActivity(intent)
        }
    }

    override fun getLayout(): Int = R.layout.order_overdue_submitted_detail_activity

    override fun bindView(bundle: Bundle?) {
        imgRecycler.layoutManager =
            androidx.recyclerview.widget.GridLayoutManager(this, 4)
        imgRecycler.setHasFixedSize(true)
        imgRecycler.addOnItemTouchListener(ticklingTouchListener)
        imgRecycler.adapter = imgAdapter
    }

    override fun initData() {
        val feedbackId = intent.getStringExtra("feedbackId")
        feedbackId?.let { viewModel?.queryProblemFeedbackDetail(it) }
    }


    private val ticklingTouchListener = object : OnItemClickListener() {
        override fun onSimpleItemClick(adapter: BaseQuickAdapter<*, *>, view: View, position: Int) {
            val item = adapter.getItem(position)
            if (item is String) {
                val map = adapter.data.map {
                    val u = it as? String ?: ""
                    EImage(netUrl = HttpConfig.getUrlImage(u))
                }
                ImagePreviewActivity.start(this@OrderOverdueSubmittedDetailActivity, map, position)
            }
        }
    }

    @LiveDataMatch
    open fun onQueryProblemFeedbackDetail(data: RspQueryProblemFeedbackDetail) {
        data.apply {
            tv_order_number.text = orderId
            tv_cyf_value.text = carrierUserRealName
            tv_car_value.text = carrierPlateNumber
            tv_type_value.text = feedbackTypeStr
            tv_question_value.text = description
            tv_time_value.text = feedbackTime
            tv_status_value.text = stateStr
            if (feedbackImgUrlList?.isNotEmpty() == true) {
                imgAdapter.setNewData(feedbackImgUrlList)
            }
        }
    }
}