package com.zczy.cargo_owner.order.confirm.bean

import com.zczy.comm.http.entity.BaseNewRequest
import com.zczy.comm.http.entity.BaseRsp
import com.zczy.comm.http.entity.ResultData

/**
 *  desc: 查看回单打回--跳转标签
 *  user: 宋双朋
 *  time: 2025/7/4 16:35
 */
class ReqQueryConsignorRepulseTag(
    var orderId: String? = null,// 运单id
) : BaseNewRequest<BaseRsp<RspQueryConsignorRepulseTagV1>>("oms-app/order/receipt/queryConsignorRepulseTag")

class RspQueryConsignorRepulseTagV1(
    var list: MutableList<RspQueryConsignorRepulseTag>? = null,
) : ResultData()

class RspQueryConsignorRepulseTag(
    val exceptionDescribe: String? = null, // 取消运单
    val id: String? = null, // 166
    val exceptionConfigType: String? = null, // WTFKRWD 跳转 打回弹框 BFHSWYQLYQ 回单 打回弹框 QXYD 跳转违约
    val configType: String? = null, // 20
    val enableFlag: String? = null, // 1
    val remarks: String? = null // 备注
)

enum class ExceptionConfigTypeEnum(var value: String) {
    问题反馈("WTFKRWD"),
    回单打回("BFHSWYQLYQ"),
    违约申请("QXYD"),
}