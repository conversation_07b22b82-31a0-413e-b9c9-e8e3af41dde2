package com.zczy.cargo_owner.order.consignor.req

import com.zczy.comm.http.entity.BaseNewRequest
import com.zczy.comm.http.entity.BaseRsp
import com.zczy.comm.http.entity.ResultData

/** 功能描述:
 * 2.手机端接口：发货单确认 （批量和单个通用）
 * http://wiki.zczy56.com/pages/viewpage.action?pageId=16089091
 * <AUTHOR>
 * @company 中储智运科技股份有限公司
 * @copyright （版权）中储南京智慧物流科技有限公司所有
 * @date 2019/4/9
 */

data class Req2DeliverConfirmAdvanceMoneyByOrderId(
        var detailIds: String = "", // 订单id字符串	String	是	传参用逗号隔开 2131,12321,12321321
        var orderIds: String = "" // 	订单明细id字符串	String	是	传参用逗号隔开 2131,12321,12321321
) : BaseNewRequest<BaseRsp<ResultData>>
("oms-app/order/consignor/deliverConfirmAdvanceMoneyByOrderId")