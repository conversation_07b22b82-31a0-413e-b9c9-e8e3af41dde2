package com.zczy.cargo_owner.order.overdue.adapter;

import android.widget.ImageView;

import com.chad.library.adapter.base.BaseQuickAdapter;
import com.chad.library.adapter.base.BaseViewHolder;
import com.zczy.cargo_owner.libcomm.config.HttpConfig;
import com.zczy.cargo_owner.order.R;
import com.zczy.comm.utils.imgloader.ImgUtil;

/**
 * PS:
 * Created by zzf
 */
public class OrderOverdueSubmitDetailImgAdapter extends BaseQuickAdapter<String, BaseViewHolder> {
    public OrderOverdueSubmitDetailImgAdapter() {
        super(R.layout.order_overdue_submit_detail_img_item);
    }

    @Override
    protected void convert(BaseViewHolder helper, String item) {
        ImageView img = helper.getView(R.id.img);
        ImgUtil.loadUrl(img, HttpConfig.getUrlImage(item));
    }
}
