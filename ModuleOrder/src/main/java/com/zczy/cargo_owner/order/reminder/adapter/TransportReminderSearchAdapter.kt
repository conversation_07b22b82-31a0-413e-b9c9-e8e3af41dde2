package com.zczy.cargo_owner.order.reminder.adapter

import android.graphics.Color
import android.text.TextUtils
import android.util.SparseArray
import com.chad.library.adapter.base.BaseQuickAdapter
import com.chad.library.adapter.base.BaseViewHolder
import com.zczy.cargo_owner.order.R
import com.zczy.comm.utils.ex.isNotNull

/**
 *  user: ssp
 *  time: 2021/7/16 9:19
 *  desc: 运输提醒
 */

class TransportReminderSearchAdapter :
    BaseQuickAdapter<RspReminderSearch, BaseViewHolder>(R.layout.transport_reminder_search_item) {

    val sparseArray: SparseArray<RspReminderSearch> = SparseArray()

    override fun convert(helper: BaseViewHolder?, item: RspReminderSearch?) {
        helper?.let {
            item?.apply {
                it.setText(R.id.tvTitle, title)
                it.setGone(R.id.ivRight, false)
                val rspReminderSearch = sparseArray[title.hashCode()]
                when {
                    showMore -> {
                        it.setBackgroundRes(R.id.tvTitle, R.drawable.base_file_4corners_ffffff)
                        it.setGone(R.id.ivRight, true)
                        it.setTextColor(
                            R.id.tvTitle,
                            Color.parseColor("#666666")
                        )
                    }
                    rspReminderSearch.isNotNull -> {
                        when {
                            TextUtils.equals(rspReminderSearch.title, title) -> {
                                it.setBackgroundRes(
                                    R.id.tvTitle,
                                    R.drawable.base_file_4corners_ecf5ff
                                )
                                it.setTextColor(
                                    R.id.tvTitle,
                                    Color.parseColor("#5086FC")
                                )
                            }
                            else -> {
                                it.setBackgroundRes(R.id.tvTitle, R.drawable.file_f5f5f5_4radius)
                                it.setTextColor(
                                    R.id.tvTitle,
                                    Color.parseColor("#666666")
                                )
                            }
                        }
                    }
                    else -> {
                        it.setBackgroundRes(R.id.tvTitle, R.drawable.file_f5f5f5_4radius)
                        it.setTextColor(
                            R.id.tvTitle,
                            Color.parseColor("#666666")
                        )
                    }
                }
            }
            it.addOnClickListener(R.id.tvTitle)
        }
    }

    fun setSelect(position: Int) {
        val rspReminderSearch = data[position]
        val title = rspReminderSearch.title
        if (sparseArray.size() == 0) {
            //未选择
            sparseArray.append(title.hashCode(), rspReminderSearch)
        } else {
            val tempData = sparseArray.valueAt(0)
            if (TextUtils.equals(tempData.title, title)) {
                //点击已选中项，清除当前选择
                sparseArray.clear()
                notifyItemChanged(position)
            } else {
                //点击的和当前选择不同 清空之前选择 并选择当前点击项
                sparseArray.clear()
                sparseArray.append(title.hashCode(), rspReminderSearch)
            }
        }
        notifyDataSetChanged()
    }

    fun clearSelectData() {
        sparseArray.clear()
    }

    fun getSelectData(): String {
        return if (sparseArray.size() <= 0) {
            ""
        } else {
            sparseArray.valueAt(0).exceptionType
        }
    }
}


class RspReminderSearch(
    var title: String = "",
    var showMore: Boolean = false,
    var exceptionType: String = ""
)

fun generateAllData(): MutableList<RspReminderSearch> {
    val mutableList = mutableListOf<RspReminderSearch>()
    //提醒类型(1:摘牌无车辆定位;2:启运地不符;3:目的地不符;4:发货无车辆定位;5:未按时发货确认;6:未按时收货确认;7:在途无定位;8:偏离路线;9:停留未行驶)
    mutableList.add(
        RspReminderSearch(
            title = "摘牌无车辆定位",
            showMore = false,
            exceptionType = "1"
        )
    )
    mutableList.add(
        RspReminderSearch(
            title = "启运地不符",
            showMore = false,
            exceptionType = "2"
        )
    )
    mutableList.add(
        RspReminderSearch(
            title = "目的地不符",
            showMore = false,
            exceptionType = "3"
        )
    )
    mutableList.add(
        RspReminderSearch(
            title = "发货无车辆定位",
            showMore = false,
            exceptionType = "4"
        )
    )
    mutableList.add(
        RspReminderSearch(
            title = "未按时发货确认",
            showMore = false,
            exceptionType = "5"
        )
    )
    mutableList.add(
        RspReminderSearch(
            title = "未按时收货确认",
            showMore = false,
            exceptionType = "6"
        )
    )
    mutableList.add(
        RspReminderSearch(
            title = "在途无定位",
            showMore = false,
            exceptionType = "7"
        )
    )
    mutableList.add(
        RspReminderSearch(
            title = "偏离路线",
            showMore = false,
            exceptionType = "8"
        )
    )
    mutableList.add(
        RspReminderSearch(
            title = "停留未行驶",
            showMore = false,
            exceptionType = "9"
        )
    )
    mutableList.add(
        RspReminderSearch(
            title = "设备离线预警",
            showMore = false,
            exceptionType = "10"
        )
    )

    return mutableList
}

fun generatePartData(): MutableList<RspReminderSearch> {
    val mutableList = mutableListOf<RspReminderSearch>()
    //提醒类型(1:摘牌无车辆定位;2:启运地不符;3:目的地不符;4:发货无车辆定位;5:未按时发货确认;6:未按时收货确认;7:在途无定位;8:偏离路线;9:停留未行驶)
    mutableList.add(
        RspReminderSearch(
            title = "摘牌无车辆定位",
            showMore = false,
            exceptionType = "1"
        )
    )
    mutableList.add(
        RspReminderSearch(
            title = "启运地不符",
            showMore = false,
            exceptionType = "2"
        )
    )
    mutableList.add(
        RspReminderSearch(
            title = "目的地不符",
            showMore = false,
            exceptionType = "3"
        )
    )
    mutableList.add(
        RspReminderSearch(
            title = "发货无车辆定位",
            showMore = false,
            exceptionType = "4"
        )
    )
    mutableList.add(
        RspReminderSearch(
            title = "未按时发货确认",
            showMore = false,
            exceptionType = "5"
        )
    )
    mutableList.add(
        RspReminderSearch(
            title = "查看更多",
            showMore = true,
            exceptionType = ""
        )
    )
    return mutableList
}