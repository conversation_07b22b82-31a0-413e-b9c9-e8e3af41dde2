package com.zczy.cargo_owner.order.settlement

import android.annotation.SuppressLint
import android.app.Activity
import android.content.Intent
import android.os.Bundle
import android.text.TextUtils
import android.view.Gravity
import android.view.View
import androidx.fragment.app.Fragment
import androidx.recyclerview.widget.LinearLayoutManager
import com.sfh.lib.event.RxBusEventManager
import com.sfh.lib.mvvm.annotation.LiveDataMatch
import com.sfh.lib.ui.dialog.DialogBuilder
import com.zczy.cargo_owner.libcomm.config.SubUserAuthUtils
import com.zczy.cargo_owner.order.R
import com.zczy.cargo_owner.order.settlement.SettlementApplicationDetailActivity.Companion.CONSIGNOR_SUB_SELECT
import com.zczy.cargo_owner.order.settlement.adapter.ConfirmSettlementBatchAdapter
import com.zczy.cargo_owner.order.settlement.bean.*
import com.zczy.comm.CommServer
import com.zczy.comm.http.entity.PageList
import com.zczy.comm.ui.BaseActivity
import com.zczy.comm.utils.NumUtil
import com.zczy.comm.utils.dp2px
import com.zczy.comm.utils.ex.isTrue
import com.zczy.comm.utils.ex.toCommaString
import com.zczy.comm.widget.itemdecoration.SpaceItemDecoration
import kotlinx.android.synthetic.main.dialog_view_settlement.view.tv_money
import kotlinx.android.synthetic.main.order_settlement_confirm_batch_activity.*

/**
 *@Desc 结算申请 批量
 *@User ssp
 *@Date 2023/8/3-15:11
 */
class ConfirmSettlementBatchActivity : BaseActivity<SettlementApplicationModel>() {

    private val confirmSettlementAdapter = ConfirmSettlementBatchAdapter()

    // 是否开启结算申请审核 0-否 1-是
    private val settlementApplyAuditSwitch by lazy {
        intent.getStringExtra(
            SETTLEMENT_APPLY_AUDIT_SWITCH
        )
    }

    //业务主体
    private val consignorSubSelect by lazy { intent.getStringExtra(CONSIGNOR_SUB_SELECT) ?: "" }

    //是否全部全选
    private val all by lazy { intent.getBooleanExtra(ALL, false) }
    private val orderInfo: String? by lazy { intent.getStringExtra(ORDER_INFO) }
    private val periodFlag: String? by lazy { intent.getStringExtra(PERIOD_FLAG) }
    private val settleItem by lazy {
        intent.getParcelableArrayListExtra<SettlementApplicationItemBean>(
            SETTLE_ITEM
        )
    }
    private val modeType: String? by lazy { intent.getStringExtra(MODE_TYPE) }
    private val applyType: Int by lazy { intent.getIntExtra(APPLY_TYPE, -1) }

    companion object {

        const val SETTLEMENT_APPLY_AUDIT_SWITCH = "settlement_apply_audit_switch"
        const val SETTLEMENT_APPLY_AUDIT_SWITCH_CODE = "1000"
        const val ALL = "all"
        const val ORDER_INFO = "orderInfo"
        const val PERIOD_FLAG = "periodFlag"
        const val MODE_TYPE = "modeType"
        const val APPLY_TYPE = "applyType"
        private const val SETTLE_ITEM = "settleItem"

        @JvmStatic
        fun jumpPage(
            fragment: Fragment?,
            settleItem: ArrayList<SettlementApplicationItemBean> = arrayListOf(),
            settlementApplyAuditSwitch: String,
            all: Boolean,
            consignorSubSelect: String,
            orderInfo: String? = null,
            periodFlag: String? = null,
            modeType: String? = null,
            applyType: Int = -1,
            requestCode: Int,
        ) {
            val intent = Intent(fragment?.context, ConfirmSettlementBatchActivity::class.java)
            intent.putExtra(SETTLEMENT_APPLY_AUDIT_SWITCH, settlementApplyAuditSwitch)
            intent.putExtra(ALL, all)
            intent.putExtra(ORDER_INFO, orderInfo)
            intent.putExtra(MODE_TYPE, modeType)
            intent.putExtra(PERIOD_FLAG, periodFlag)
            intent.putExtra(APPLY_TYPE, applyType)
            intent.putParcelableArrayListExtra(SETTLE_ITEM, settleItem)
            intent.putExtra(CONSIGNOR_SUB_SELECT, consignorSubSelect)
            fragment?.startActivityForResult(intent, requestCode)
        }

        @JvmStatic
        fun jumpPage(
            fragment: Fragment?,
            settleItem: ArrayList<SettlementApplicationItemBean> = arrayListOf(),
            settlementApplyAuditSwitch: String,
            all: Boolean,
            consignorSubSelect: String,
            orderInfo: String? = null,
            periodFlag: String? = null,
            applyType: Int = -1,
            requestCode: Int,
        ) {
            val intent = Intent(fragment?.context, ConfirmSettlementBatchActivity::class.java)
            intent.putExtra(SETTLEMENT_APPLY_AUDIT_SWITCH, settlementApplyAuditSwitch)
            intent.putExtra(ALL, all)
            intent.putExtra(ORDER_INFO, orderInfo)
            intent.putExtra(PERIOD_FLAG, periodFlag)
            intent.putExtra(APPLY_TYPE, applyType)
            intent.putParcelableArrayListExtra(SETTLE_ITEM, settleItem)
            intent.putExtra(CONSIGNOR_SUB_SELECT, consignorSubSelect)
            fragment?.startActivityForResult(intent, requestCode)
        }
    }

    override fun getLayout(): Int {
        return R.layout.order_settlement_confirm_batch_activity
    }

    @SuppressLint("SetTextI18n")
    override fun bindView(bundle: Bundle?) {
        if (TextUtils.equals(SETTLEMENT_APPLY_AUDIT_SWITCH_CODE, settlementApplyAuditSwitch)) {
            appToolber.setTitle("结算申请审核")
        } else {
            appToolber.setTitle("结算申请")
        }
        settlementRecycler.apply {
            layoutManager = LinearLayoutManager(this@ConfirmSettlementBatchActivity)
            adapter = confirmSettlementAdapter
            addItemDecoration(SpaceItemDecoration(dp2px(7f)))
        }
        btnConfirmSettlementApplication.setOnClickListener {
            if (TextUtils.equals(SETTLEMENT_APPLY_AUDIT_SWITCH_CODE, settlementApplyAuditSwitch)) {
                //结算审核通过
                val dialogBuilder = DialogBuilder()
                dialogBuilder.message = "确定将选择的记录审核通过吗？"
                dialogBuilder.okListener =
                    DialogBuilder.DialogInterface.OnClickListener { p0, _ ->
                        p0?.dismiss()
                        viewModel?.findSettleAccountMoneyIsEnoughReq(
                            FindSettleAccountMoneyIsEnoughReq(
                                detailIds = confirmSettlementAdapter.data.toCommaString {
                                    it.detailId ?: ""
                                },
                                userId = CommServer.getUserServer().login.userId,
                                modeType = modeType
                            )
                        )
                    }
                showDialog(dialogBuilder)
            } else {
                val view = View.inflate(this, R.layout.dialog_view_settlement, null)
                var totalConsignorCheckMoney = 0.00
                confirmSettlementAdapter.data.forEach { bean ->
                    bean.consignorCheckMoney.let {
                        totalConsignorCheckMoney =
                            NumUtil.sum(totalConsignorCheckMoney, it?.toDoubleOrNull() ?: 0.00)
                    }
                    bean.hzAdvanceMoney.let {
                        totalConsignorCheckMoney =
                            NumUtil.sum(totalConsignorCheckMoney, it?.toDoubleOrNull() ?: 0.00)
                    }
                    bean.assistanceServiceCharges.let {
                        totalConsignorCheckMoney =
                            NumUtil.sum(totalConsignorCheckMoney, it?.toDoubleOrNull() ?: 0.00)
                    }
                    bean.additionalCharges.let {
                        totalConsignorCheckMoney =
                            NumUtil.sum(totalConsignorCheckMoney, it?.toDoubleOrNull() ?: 0.00)
                    }
                }
                view.tv_money.text = String.format("%1\$.2f", totalConsignorCheckMoney) + "元"
                showDialog(DialogBuilder()
                    .setTitle("提示")
                    .setView(view)
                    .setGravity(Gravity.CENTER)
                    .setOKTextColor("确定", R.color.text_blue)
                    .setOkListener { dialogInterface, _ ->
                        dialogInterface.dismiss()
                        if (TextUtils.equals("1", modeType)) {
                            viewModel?.findSettleAccountMoneyIsEnoughReq(
                                ReqFindInvoicingSettleAccountMoneyIsEnough(
                                    detailIds = confirmSettlementAdapter.data.toCommaString {
                                        it.detailId ?: ""
                                    },
                                    userId = CommServer.getUserServer().login.userId,
                                    modeType = modeType,
                                )
                            )
                        } else {
                            if (settlementApplyAuditSwitch.isTrue) {
                                viewModel?.summitToAudit(
                                    req = ReqSummitToAudit(
                                        orderIds = confirmSettlementAdapter.data.toCommaString { it.orderId },
                                        modeType = modeType,
                                    )
                                )
                            } else {
                                viewModel?.findSettleAccountMoneyIsEnoughReq(
                                    FindSettleAccountMoneyIsEnoughReq(
                                        detailIds = confirmSettlementAdapter.data.toCommaString {
                                            it.detailId ?: ""
                                        },
                                        userId = CommServer.getUserServer().login.userId,
                                        modeType = modeType,
                                    )
                                )
                            }
                        }
                    })
            }
        }
    }


    override fun initData() {
        if (all) {
            viewModel?.querySettleApplyDataByAllChoose(
                ReqQuerySettleApplyDataByAllChoose(
                    orderInfo = orderInfo,
                    periodFlag = periodFlag,
                    consignorSubSelect = consignorSubSelect,
                    modeType = modeType,
                    applyType = applyType
                )
            )
        } else {
            viewModel?.settleAccountDetailDataByDetails(
                ReqSettleAccountDetailDataByDetails(
                    periodFlag = periodFlag,
                    detailIds = settleItem?.toCommaString { it.detailId ?: "" },
                    modeType = modeType
                )
            )
        }
    }

    @LiveDataMatch
    open fun querySettleApplyDataByAllChooseSuccess(data: PageList<RspQuerySettleApplyDataByAllChoose>) {
        //全部全选接口数据
        confirmSettlementAdapter.setNewData(data.rootArray)
    }

    @LiveDataMatch
    open fun confirmSettlementApplicationSuccess() {
        ConfirmSettlementSuccessActivity.start(this)
        setResult(Activity.RESULT_OK)
        finish()
    }

    @LiveDataMatch
    open fun onSummitToAuditSuccess() {
        ConfirmSettlementSuccessActivity.start(this)
        RxBusEventManager.postEvent(RxBusSettleSuccess(success = true))
        setResult(Activity.RESULT_OK)
        finish()
    }

    @LiveDataMatch
    open fun onMoneyUnEnough(data: FindSettleAccountMoneyIsEnoughRsp) {
        //余额不足
        if (data.shortCycleFlag.isTrue) {
            val dialogBuilder = DialogBuilder()
            dialogBuilder.message = data.rechargeMongyMsg
            dialogBuilder.title = "短周期额度不足"
            dialogBuilder.isHideCancel = true
            dialogBuilder.setOKText("我知道了")
            showDialog(dialogBuilder)
        } else {
            val dialogBuilder = DialogBuilder()
            dialogBuilder.message = data.rechargeMongyMsg
            dialogBuilder.title = "提示"
            dialogBuilder.isHideCancel = true
            dialogBuilder.setOKText("我知道了")
            showDialog(dialogBuilder)
        }
    }

    @LiveDataMatch
    open fun onMoneyEnough(data: FindSettleAccountMoneyIsEnoughRsp) {
        if (!TextUtils.isEmpty(data.showMsgCount)) {
            val showMsgCountInt = data.showMsgCount?.toIntOrNull() ?: 0
            if (showMsgCountInt > 0) {
                val dialogBuilder = DialogBuilder()
                dialogBuilder.message = data.showMsg
                dialogBuilder.title = "提示"
                dialogBuilder.isHideCancel = true
                dialogBuilder.setOKTextColor("我知道了", R.color.text_blue)
                dialogBuilder.setOkListener { dialogInterface, _ ->
                    dialogInterface.dismiss()
                    settlementContinue()
                }
                showDialog(dialogBuilder)
            }else{
                settlementContinue()
            }
        }else{
            settlementContinue()
        }
    }

    private fun settlementContinue() {
        //余额充足
        val userId: String = CommServer.getUserServer().login.userId
        val userName: String = CommServer.getUserServer().login.userName
        val childId: String = CommServer.getUserServer().login.childId
        val mutableList = mutableListOf<DetailId2CouponIdReq>()
        settleItem?.forEach {
            mutableList.add(
                DetailId2CouponIdReq(
                    detailId = it.detailId ?: "",
                    quanId = it.userCouponId ?: ""
                )
            )
        }
        if (TextUtils.equals("1", modeType)) {
            viewModel?.confirmSettlementApplication(
                ReqDoInvoicingBatchFrontSettleApply(
                    detailIds = confirmSettlementAdapter.data.toCommaString { it.detailId ?: "" },
                    userId = userId,
                    userName = userName,
                    childUserId = childId,
                    consignorSubSelect = consignorSubSelect,
                    detailId2Coupon = mutableList,
                    modeType = modeType
                )
            )
        } else {
            viewModel?.confirmSettlementApplication(
                ConfirmSettlementApplicationReq(
                    detailIds = confirmSettlementAdapter.data.toCommaString { it.detailId ?: "" },
                    userId = userId,
                    userName = userName,
                    childUserId = childId,
                    consignorSubSelect = consignorSubSelect,
                    detailId2Coupon = mutableList,
                    modeType = modeType
                )
            )
        }
    }
}