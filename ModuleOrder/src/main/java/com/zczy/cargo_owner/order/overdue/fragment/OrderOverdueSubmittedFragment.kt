package com.zczy.cargo_owner.order.overdue.fragment

import android.content.Context
import android.os.Bundle
import android.view.View
import com.sfh.lib.mvvm.annotation.LiveDataMatch
import com.zczy.cargo_owner.order.R
import com.zczy.cargo_owner.order.detail.WaybillDetailStatueActivity
import com.zczy.cargo_owner.order.overdue.OrderOverdueSubmittedDetailActivity
import com.zczy.cargo_owner.order.overdue.OrderOverdueProblemFeedbackDetailActivity
import com.zczy.cargo_owner.order.overdue.adapter.OrderOverdueSubmittedAdapter
import com.zczy.cargo_owner.order.overdue.model.OrderOverdueFragmentModel
import com.zczy.cargo_owner.order.overdue.req.OrderOverDueItem
import com.zczy.cargo_owner.order.overdue.req.RspQueryProblemFeedbackPage
import com.zczy.comm.http.entity.PageList
import com.zczy.comm.ui.BaseFragment
import com.zczy.comm.utils.dp2px
import com.zczy.comm.widget.pulltorefresh.CommEmptyView
import kotlinx.android.synthetic.main.order_overdue_undo_fragment.swipe_refresh_more_layout

/**
 * PS: 逾期运单管理 已提交问题反馈
 * Created by zzf on 2025-07-04.
 */
class OrderOverdueSubmittedFragment : BaseFragment<OrderOverdueFragmentModel>() {

    private val mAdapter = OrderOverdueSubmittedAdapter()

    companion object {
        @JvmStatic
        fun newInstance(context: Context): OrderOverdueSubmittedFragment {
            val bundle = Bundle()
            return instantiate(context, OrderOverdueSubmittedFragment::class.java.name, bundle) as OrderOverdueSubmittedFragment
        }
    }

    override fun getLayout(): Int = R.layout.order_overdue_submitted_fragment

    override fun bindView(view: View, bundle: Bundle?) {
        val emptyView = CommEmptyView.creatorDef(context)
        swipe_refresh_more_layout.apply {
            setAdapter(mAdapter, true)
            setEmptyView(emptyView)
            addItemDecorationSize(dp2px(7f))
            addOnItemChildClickListener { adapter, view, position ->
                val item = adapter.getItem(position)
                if (item is RspQueryProblemFeedbackPage) {
                    when (view.id) {
                        // 进入详情 - 使用新的问题反馈详情页面
                        R.id.tv_order_detail, R.id.tv_order_detail_arrow -> {
                            OrderOverdueProblemFeedbackDetailActivity.startContentUI(
                                activity!!,
                                item.bizId, // 使用任务单号
                                4 // 货主来源
                            )
                        }
                    }
                }
            }

            setOnLoadListener2 { nowPage -> viewModel?.queryProblemFeedbackPage(nowPage = nowPage) }
        }
    }


    override fun initData() {
        swipe_refresh_more_layout.onAutoRefresh()
    }

    @LiveDataMatch
    open fun onQueryProblemFeedbackPage(data: PageList<RspQueryProblemFeedbackPage>?) {
        swipe_refresh_more_layout.onRefreshCompale(data)
    }

}