<manifest xmlns:android="http://schemas.android.com/apk/res/android"
    package="com.zczy.cargo_owner.order">

    <application>

        <!-- 快递管理 -->
        <activity android:name=".express.OrderExpressMainActivity" />
        <!-- 回单签收原因 -->
        <activity android:name=".express.OrderExpressMainReasonActivity" />
        <!-- 快递详情 -->
        <activity android:name=".express.OrderExpressDetailActivity" />
        <!-- 快递详情 线下送达 -->
        <activity android:name=".express.OrderExpressUnLineDetailActivity" />
        <!-- 违约管理 主页 -->
        <activity android:name=".violate.OrderViolateMainActivity" />
        <!-- 违约管理-筛选 -->
        <activity
            android:name=".violate.OrderViolateApplyFilterActivity"
            android:label="违约管理-筛选" />
        <!-- 违约管理 详情 违约申请中 -->
        <activity android:name=".violate.OrderViolateDetailApplyingActivity" />
        <!-- 回单确认列表 -->
        <activity android:name=".confirm.ReturnedOrderConfirmListActivity" />
        <!--回单打回原因-->
        <activity android:name=".confirm.ReturnedOrderDeniedReasonActivity" />
        <activity android:name=".confirm.flex.FlexBoxActivity" />
        <!--单个回单确认-->
        <activity android:name=".confirm.v2.SingleReturnedOrderConfirmActivityV2" />
        <activity
            android:name=".confirm.v2.SingleReturnedOrderConfirmActivityV3"
            android:label="极速好货-确认发货" />
        <activity
            android:name=".confirm.ReturnedOrderConfirmDetailActivity"
            android:label="回单确认详情" />
        <activity android:name=".confirm.UploadReturnedOrderPhotoActivityV1" />
        <!--发货详情-->
        <activity android:name=".confirm.v2.ReturnedOrderContainerListActivityV1" />
        <!--收货详情-->
        <activity android:name=".confirm.v2.ReturnedOrderContainerListActivityV2" />
        <!--回单确认成功-->
        <activity android:name=".confirm.ReturnedOrderConfirmSuccessActivity" />
        <!--批量确认回单-->
        <activity android:name=".confirm.BatchConfirmReturnedOrderActivityV1" />
        <!--批量确认回单-->
        <activity
            android:name=".confirm.BatchContainerConfirmReturnedOrderActivityV1"
            android:label="集装箱货源批量回单确认" />
        <!--搜索回单确认-->
        <activity android:name=".confirm.SearchReturnedOrderActivity" />
        <activity android:name=".confirm.ReturnedOrderConfirmFilterActivity" />
        <!--上传回单照片-->
        <activity android:name=".confirm.UploadReturnedOrderPhotoActivity" />
        <activity
            android:name=".confirm.UploadReturnedOrderPhotoActivityV2"
            android:label="回单上传" />
        <!-- 结算申请列表 -->
        <activity android:name=".settlement.SettlementApplicationListActivity" />
        <!-- 结算申请审核 -->
        <activity android:name=".billing.OrderBillingReviewHomeActivity" />
        <!-- 结算申请审核筛选 -->
        <activity
            android:name=".billing.OrderBillingReviewFilterActivity"
            android:windowSoftInputMode="adjustPan" />
        <!-- 结算申请详情 -->
        <activity android:name=".settlement.SettlementApplicationDetailActivity" />
        <!--确认结算申请 批量-->
        <activity android:name=".settlement.ConfirmSettlementBatchActivity" />
        <!--确认结算申请 单个-->
        <activity android:name=".settlement.ConfirmSettlementSingleActivity" />
        <!--结算申请搜索-->
        <activity
            android:name=".settlement.SearchSettlementApplicationActivity"
            android:label="结算申请搜索" />
        <!--结算申请搜索-->
        <activity
            android:name=".settlement.SettlementFilterActivity"
            android:label="结算申请搜索" />
        <!--结算申请成功-->
        <activity android:name=".settlement.ConfirmSettlementSuccessActivity" />
        <!-- 一键搜索运单 -->
        <activity android:name=".WaybillSearchActivity" />
        <activity
            android:name=".WaybillFilterActivity"
            android:label="运单筛选" />
        <!-- 一键搜索运单-冀东 -->
        <activity android:name=".WaybillSearchJiDongActivity" />
        <!-- 一键搜索运单-冀东 -设置自定义编号-->
        <activity android:name=".JidongEditSelfCommentActivity" />
        <!-- 场内物流 -->
        <activity
            android:name=".venue.VenueWaybillActivity"
            android:configChanges="keyboardHidden|orientation|screenSize" />
        <!-- 场内物流 -->
        <activity
            android:name=".venue.perfect.DriverPerfectActivity"
            android:configChanges="keyboardHidden|orientation|screenSize" />
        <!-- 发货单确认 主页面 -->
        <activity android:name=".consignor.OrderConsignorConfirmMainActivity" />
        <!-- 发货单确认 搜索 -->
        <activity android:name=".consignor.ui.OrderConsignorConfirmSearchActivity" />
        <!-- 发货单确认 确认发货单 -->
        <activity android:name=".consignor.ui.OrderConsignorConfirmPreviewActivity" />
        <!-- 发货单确认 打回界面 -->
        <activity android:name=".consignor.ui.OrderConsignorConfirmRejectActivity" />
        <!--运单详情-->
        <activity android:name=".detail.WaybillDetailActivity" />
        <!--运单详情运单状态-->
        <activity android:name=".detail.WaybillDetailStatueActivity" />
        <!--运单状态-->
        <activity android:name=".detail.WaybillStatusActivity" />
        <!--运单新增违约-->
        <activity android:name=".violate.OrderViolateAddActivity" />
        <activity android:name=".violate.OrderViolateAddTMSActivity" />
        <!--运单违约列表-->
        <activity android:name=".violate.OrderViolateListActivity" />
        <!--运输中-->
        <activity android:name=".transport.TransportationActivity" />
        <activity android:name=".transport.TransportationBoundaryActivity" />
        <activity android:name=".transport.WaybillTrackingActivityV1" />
        <activity android:name=".transport.WaybillTrackingActivityDetailV1" />
        <activity android:name=".transport.TransportSearchActivity" />
        <activity android:name=".transport.TransportsListActivity" />
        <!--查看路线-->
        <activity android:name=".mileage.CargoGoodsMapActivity" />
        <!-- 变更管理 -->
        <activity android:name=".change.OrderChangeMainActivity" />
        <!-- tms变更待处理  -->
        <activity android:name=".change.OrderChangeHandleActivity" />
        <activity
            android:name=".change.ReceiptAddressChangeActivity"
            android:label="押回单地址变更" />
        <!-- 变更发货信息 -->
        <activity android:name="com.zczy.cargo_owner.order.change.deliverinfo.ui.OrderChangeDeliverInfoActivity" />
        <!-- 变更装卸货范围 -->
        <activity android:name="com.zczy.cargo_owner.order.change.OrderChangeScopeInfoActivity" />
        <!-- 运单合同 -->
        <activity android:name=".detail.WaybillContractDetailActivity" />
        <!-- 逾期运单管理 -->
        <activity android:name=".overdue.OrderOverdueMainActivity" />
        <activity android:name=".overdue.OrderOverdueAppealActivity" />
        <activity android:name=".overdue.OrderOverdueHigeSearchActivity" />
        <activity android:name=".overdue.OrderOverdueSearchActivityV1" />
        <activity android:name=".overdue.OrderOverdueSearchActivityV2" />
        <activity android:name=".overdue.OrderOverdueSearchActivityV3" />
        <activity android:name=".overdue.OrderOverdueSubmittedDetailActivity" />
        <activity android:name=".overdue.OrderOverdueProblemFeedbackActivity" />
        <activity android:name=".overdue.OrderOverdueProblemFeedbackDetailActivity" />
        <!--加扣款项-->
        <activity android:name=".confirm.AddAndDeductMoneyActivity" />
        <activity android:name=".change.manage.OrderChangeCarrierHistorySearchActivity" />
        <activity android:name=".express.OrderExpressReturnReasonActivity" />
        <activity android:name=".qrcode.WaybillQRCodeActivity" />
        <activity android:name=".pledge.PledgeMainActivity" />
        <activity android:name=".pledge.PledgeMainSearchActivity" />
        <activity android:name=".pledge.PledgeMainDetailActivity" />
        <activity android:name="com.zczy.cargo_owner.order.qrcode.SeniorOrderWaybillQRCodeActivity" />
        <!--        运输提醒-->
        <activity
            android:name=".reminder.TransportReminderHomeActivity"
            android:screenOrientation="portrait" />
        <activity
            android:name=".reminder.TransportReminderHomeSearchActivityV1"
            android:label="筛选-运输提醒" />
        <activity
            android:name=".reminder.TransportReminderHomeSearchActivityV2"
            android:label="筛选-运输风险" />
        <activity
            android:name=".reminder.TransportReminderHomeSearchActivityV3"
            android:label="筛选-合规风险" />
        <activity
            android:name=".express.scan.HWScanActivity"
            android:screenOrientation="portrait" />

        <activity
            android:name=".express.HandInputBoundctivity"
            android:screenOrientation="portrait" />

        <activity
            android:name=".express.HandInputBoundSuccessActivity"
            android:launchMode="singleTop"
            android:screenOrientation="portrait" />

        <activity
            android:name=".express.BoundDetailActivity"
            android:screenOrientation="portrait" />
        <activity
            android:name=".transport.WayBillTeackStopDetailActivity"
            android:screenOrientation="portrait" />
        <activity
            android:name=".dispatch.DispatchListManagerActivity"
            android:screenOrientation="portrait" />

        <activity
            android:name=".WaybillShipmentsActivity"
            android:screenOrientation="portrait" />
        <activity
            android:name=".WayBillModifyPriceActivity"
            android:label="修改运价"
            android:screenOrientation="portrait" />
    </application>
</manifest>
