<?xml version="1.0" encoding="utf-8"?>
<RelativeLayout xmlns:android="http://schemas.android.com/apk/res/android"
    xmlns:tools="http://schemas.android.com/tools"
    android:id="@+id/rl_root"
    android:layout_width="match_parent"
    android:layout_height="wrap_content"
    android:background="@color/white">

    <TextView
        android:id="@+id/tv_time"
        android:layout_width="wrap_content"
        android:layout_height="wrap_content"
        android:layout_marginStart="34dp"
        android:layout_marginLeft="34dp"
        android:layout_marginTop="16dp"
        android:textColor="@color/text_66"
        android:textSize="14sp"
        tools:text="11:31" />

    <TextView
        android:id="@+id/tv_time_day"
        android:layout_width="wrap_content"
        android:layout_height="wrap_content"
        android:layout_below="@+id/tv_time"
        android:layout_marginStart="14dp"
        android:layout_marginLeft="14dp"
        android:textColor="@color/text_66"
        android:textSize="10sp"
        tools:text="2018-08-15" />

    <LinearLayout
        android:id="@+id/rl_location"
        android:layout_width="wrap_content"
        android:layout_height="wrap_content"
        android:layout_centerVertical="true"
        android:layout_marginStart="17dp"
        android:layout_marginLeft="17dp"
        android:layout_toRightOf="@+id/tv_time_day"
        android:orientation="vertical">

        <View
            android:id="@+id/top_line"
            android:layout_width="2dp"
            android:layout_height="18dp"
            android:layout_gravity="center_horizontal"
            android:layout_weight="1"
            android:background="@color/comm_divider_e3" />

        <ImageView
            android:id="@+id/iv_location"
            android:layout_width="wrap_content"
            android:layout_height="wrap_content"
            android:layout_gravity="center_horizontal"
            android:background="@drawable/unlocation" />

        <View
            android:id="@+id/bottom_line"
            android:layout_width="2dp"
            android:layout_height="28dp"
            android:layout_gravity="center_horizontal"
            android:layout_weight="1"
            android:background="@color/comm_divider_e3" />
    </LinearLayout>

    <TextView
        android:id="@+id/tv_details_add"
        android:layout_width="match_parent"
        android:layout_height="wrap_content"
        android:layout_marginLeft="17dp"
        android:layout_marginTop="16dp"
        android:layout_marginRight="11dp"
        android:layout_toRightOf="@+id/rl_location"
        android:ellipsize="end"
        android:lineSpacingExtra="3dp"
        android:maxLines="2"
        android:text="四川省遂宁县市射洪县射盐路"
        android:textColor="@color/text_33"
        android:textSize="14sp" />

</RelativeLayout>
