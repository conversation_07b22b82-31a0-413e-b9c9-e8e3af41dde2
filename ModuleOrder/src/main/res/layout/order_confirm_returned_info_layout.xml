<?xml version="1.0" encoding="utf-8"?>
<androidx.constraintlayout.widget.ConstraintLayout xmlns:android="http://schemas.android.com/apk/res/android"
    xmlns:app="http://schemas.android.com/apk/res-auto"
    xmlns:tools="http://schemas.android.com/tools"
    android:layout_width="match_parent"
    android:layout_height="wrap_content"
    android:layout_marginTop="@dimen/divider_7"
    android:background="@color/white">

    <TextView
        android:id="@+id/order_textview"
        android:layout_width="wrap_content"
        android:layout_height="wrap_content"
        android:layout_marginStart="10dp"
        android:layout_marginLeft="10dp"
        android:text="回单信息"
        android:textColor="@color/text_33"
        android:textSize="@dimen/text_14"
        android:textStyle="bold"
        app:layout_constraintBottom_toBottomOf="@+id/order_divider"
        app:layout_constraintStart_toEndOf="@+id/order_divider"
        app:layout_constraintTop_toTopOf="@+id/order_divider" />

    <TextView
        android:id="@+id/tvReceiptCarrierAlreadyEdited"
        android:layout_width="wrap_content"
        android:layout_height="wrap_content"
        android:layout_marginStart="12dp"
        android:drawableStart="@drawable/base_warning"
        android:drawablePadding="5dp"
        android:text="司机已修改"
        android:textColor="#fffb6b40"
        android:textSize="12sp"
        android:visibility="gone"
        app:layout_constraintBottom_toBottomOf="@id/order_textview"
        app:layout_constraintStart_toEndOf="@id/order_textview"
        app:layout_constraintTop_toTopOf="@id/order_textview"
        tools:visibility="visible" />

    <TextView
        android:id="@+id/tvReceiptModification"
        android:layout_width="wrap_content"
        android:layout_height="wrap_content"
        android:layout_marginEnd="14dp"
        android:background="@drawable/base_btn_blue_stroke_5radius_selector"
        android:gravity="center"
        android:paddingStart="6dp"
        android:paddingTop="3dp"
        android:paddingEnd="6dp"
        android:paddingBottom="3dp"
        android:text="回单修改"
        android:textColor="#ff5086fc"
        android:textSize="12sp"
        android:visibility="gone"
        app:layout_constraintBottom_toBottomOf="@id/order_textview"
        app:layout_constraintEnd_toEndOf="parent"
        app:layout_constraintTop_toTopOf="@id/order_textview"
        tools:visibility="visible" />

    <View
        android:id="@+id/order_divider"
        android:layout_width="3dp"
        android:layout_height="16dp"
        android:layout_marginStart="14dp"
        android:layout_marginLeft="14dp"
        android:layout_marginTop="8dp"
        android:background="@color/theme_blue"
        app:layout_constraintStart_toStartOf="parent"
        app:layout_constraintTop_toTopOf="parent" />

    <TextView
        android:id="@+id/order_textview4"
        android:layout_width="wrap_content"
        android:layout_height="wrap_content"
        android:layout_marginStart="14dp"
        android:layout_marginLeft="14dp"
        android:layout_marginTop="15dp"
        android:text="收货数量"
        app:layout_constraintStart_toStartOf="parent"
        app:layout_constraintTop_toBottomOf="@+id/order_divider" />


    <TextView
        android:id="@+id/tvReceiptNum"
        android:layout_width="wrap_content"
        android:layout_height="wrap_content"
        android:layout_marginEnd="14dp"
        android:layout_marginRight="14dp"
        android:textColor="@color/text_33"
        android:textSize="@dimen/text_15"
        android:textStyle="bold"
        app:layout_constraintBottom_toBottomOf="@+id/order_textview4"
        app:layout_constraintEnd_toEndOf="parent"
        app:layout_constraintTop_toTopOf="@+id/order_textview4"
        tools:text="120吨" />

    <androidx.recyclerview.widget.RecyclerView
        android:id="@+id/isReceiptRecyclerViewCargo"
        android:layout_width="match_parent"
        android:layout_height="wrap_content"
        android:paddingTop="10dp"
        android:paddingBottom="10dp"
        app:layout_constraintTop_toBottomOf="@id/order_textview4"
        tools:itemCount="2"
        tools:listitem="@layout/order_upload_returned_order_photo_item_v1" />

    <com.zczy.comm.widget.inputv2.InputViewClick
        android:id="@+id/inputReceiptInformationContainer"
        android:layout_width="match_parent"
        android:layout_height="wrap_content"
        app:input_click_hide_content_hint="true"
        app:input_title="实际收货"
        app:layout_constraintTop_toBottomOf="@id/isReceiptRecyclerViewCargo" />

    <androidx.constraintlayout.widget.ConstraintLayout
        android:id="@+id/clOutStageTime"
        android:layout_width="match_parent"
        android:layout_height="wrap_content"
        app:layout_constraintStart_toStartOf="parent"
        app:layout_constraintTop_toBottomOf="@+id/inputReceiptInformationContainer">

        <View
            android:id="@+id/viewOutStageTime"
            android:layout_width="3dp"
            android:layout_height="16dp"
            android:layout_marginStart="14dp"
            android:layout_marginLeft="14dp"
            android:layout_marginTop="8dp"
            android:background="@color/theme_blue"
            app:layout_constraintStart_toStartOf="parent"
            app:layout_constraintTop_toTopOf="parent" />

        <TextView
            android:id="@+id/tvOutStageTimeV1"
            android:layout_width="wrap_content"
            android:layout_height="wrap_content"
            android:layout_marginStart="10dp"
            android:text="发货磅单出场时间"
            android:textColor="#ff333333"
            android:textSize="14sp"
            android:textStyle="bold"
            app:layout_constraintBottom_toBottomOf="@id/viewOutStageTime"
            app:layout_constraintStart_toEndOf="@id/viewOutStageTime"
            app:layout_constraintTop_toTopOf="@id/viewOutStageTime" />

        <com.zczy.comm.widget.inputv2.InputViewClick
            android:id="@+id/inputOutStageTime"
            android:layout_width="match_parent"
            android:layout_height="wrap_content"
            app:input_click_hide_content_hint="true"
            app:input_title="发货磅单出场时间"
            app:layout_constraintTop_toBottomOf="@id/viewOutStageTime" />

        <TextView
            android:id="@+id/tvOutStageTimeWarning"
            android:layout_width="wrap_content"
            android:layout_height="wrap_content"
            android:layout_marginStart="14dp"
            android:layout_marginTop="4dp"
            android:text="按照发货单上的出厂时间填写"
            android:textColor="#ff999999"
            android:textSize="12sp"
            app:layout_constraintStart_toStartOf="@id/inputOutStageTime"
            app:layout_constraintTop_toBottomOf="@id/inputOutStageTime" />

    </androidx.constraintlayout.widget.ConstraintLayout>

    <TextView
        android:id="@+id/tvReceiptDocumentPhoto"
        android:layout_width="wrap_content"
        android:layout_height="wrap_content"
        android:layout_marginStart="14dp"
        android:layout_marginTop="8dp"
        android:layout_marginBottom="8dp"
        android:text="司机上传单据照片"
        app:layout_constraintStart_toStartOf="parent"
        app:layout_constraintTop_toBottomOf="@id/clOutStageTime" />

    <TextView
        android:id="@+id/ivReceiptDocumentPhotoSwitch"
        android:layout_width="wrap_content"
        android:layout_height="wrap_content"
        android:layout_marginStart="12dp"
        android:drawableStart="@drawable/base_switch_blue"
        android:drawablePadding="5dp"
        android:text="切换原图"
        android:textColor="#5086FC"
        android:visibility="gone"
        app:layout_constraintBottom_toBottomOf="@id/tvReceiptCarrierAlreadyEdited"
        app:layout_constraintStart_toEndOf="@id/tvReceiptCarrierAlreadyEdited"
        app:layout_constraintTop_toTopOf="@id/tvReceiptCarrierAlreadyEdited"
        tools:visibility="visible" />

    <RelativeLayout
        android:id="@+id/isvReturnImageRl"
        android:layout_width="match_parent"
        android:layout_height="wrap_content"
        android:descendantFocusability="blocksDescendants"
        app:layout_constraintEnd_toEndOf="parent"
        app:layout_constraintStart_toStartOf="parent"
        app:layout_constraintTop_toBottomOf="@id/tvReceiptDocumentPhoto">

        <com.zczy.comm.utils.imageselector.ImageSelectView
            android:id="@+id/isvReturnImage"
            android:layout_width="match_parent"
            android:layout_height="wrap_content"
            android:layout_marginStart="14dp"
            android:layout_marginLeft="14dp"
            android:layout_marginTop="10dp"
            android:layout_marginEnd="14dp"
            android:layout_marginRight="14dp"
            android:layout_marginBottom="14dp"
            android:visibility="gone"
            app:canSelect="false"
            tools:visibility="visible" />

    </RelativeLayout>

    <TextView
        android:id="@+id/isvReturnTime"
        android:layout_width="wrap_content"
        android:layout_height="wrap_content"
        android:layout_marginStart="14dp"
        android:textColor="#ff666666"
        android:textSize="12sp"
        app:layout_constraintStart_toStartOf="parent"
        app:layout_constraintTop_toBottomOf="@id/isvReturnImageRl"
        tools:text="上传时间：2023-01-01 11:11:11" />

    <TextView
        android:id="@+id/tvexaminePicUrlArr"
        android:layout_width="wrap_content"
        android:layout_height="wrap_content"
        android:layout_marginStart="14dp"
        android:layout_marginTop="8dp"
        android:layout_marginBottom="8dp"
        android:text="司机上传的证明材料"
        android:visibility="gone"
        tools:visibility="visible"
        app:layout_constraintStart_toStartOf="parent"
        app:layout_constraintTop_toBottomOf="@id/isvReturnTime" />

    <RelativeLayout
        android:id="@+id/isexaminePicUrlArr"
        android:layout_width="match_parent"
        android:layout_height="wrap_content"
        android:descendantFocusability="blocksDescendants"
        android:visibility="gone"
        app:layout_constraintEnd_toEndOf="parent"
        app:layout_constraintStart_toStartOf="parent"
        app:layout_constraintTop_toBottomOf="@id/tvexaminePicUrlArr">

        <com.zczy.comm.utils.imageselector.ImageSelectView
            android:id="@+id/isexaminePicUrlArrImage"
            android:layout_width="match_parent"
            android:layout_height="wrap_content"
            android:layout_marginStart="14dp"
            android:layout_marginLeft="14dp"
            android:layout_marginTop="10dp"
            android:layout_marginEnd="14dp"
            android:layout_marginRight="14dp"
            android:layout_marginBottom="14dp"
            android:visibility="gone"
            app:canSelect="false"
            tools:visibility="visible" />

    </RelativeLayout>

    <TextView
        android:id="@+id/tvReceiptDocumentPhotoV2"
        android:layout_width="wrap_content"
        android:layout_height="wrap_content"
        android:layout_marginStart="14dp"
        android:layout_marginTop="8dp"
        android:layout_marginBottom="8dp"
        android:text="货主上传单据照片"
        android:visibility="gone"
        app:layout_constraintStart_toStartOf="parent"
        app:layout_constraintTop_toBottomOf="@id/isexaminePicUrlArr"
        tools:visibility="visible" />

    <RelativeLayout
        android:id="@+id/isReceiptReturnImageRlV2"
        android:layout_width="match_parent"
        android:layout_height="wrap_content"
        android:descendantFocusability="blocksDescendants"
        android:visibility="gone"
        app:layout_constraintEnd_toEndOf="parent"
        app:layout_constraintStart_toStartOf="parent"
        app:layout_constraintTop_toBottomOf="@id/tvReceiptDocumentPhotoV2"
        tools:visibility="visible">

        <com.zczy.comm.utils.imageselector.ImageSelectView
            android:id="@+id/isReceiptReturnImageV2"
            android:layout_width="match_parent"
            android:layout_height="wrap_content"
            android:layout_marginStart="14dp"
            android:layout_marginLeft="14dp"
            android:layout_marginTop="10dp"
            android:layout_marginEnd="14dp"
            android:layout_marginRight="14dp"
            android:layout_marginBottom="14dp"
            android:visibility="gone"
            app:canSelect="false"
            tools:visibility="visible" />

    </RelativeLayout>

    <TextView
        android:id="@+id/tvReceiptDocumentPhotoV1"
        android:layout_width="wrap_content"
        android:layout_height="wrap_content"
        android:layout_marginStart="14dp"
        android:layout_marginTop="8dp"
        android:layout_marginBottom="8dp"
        android:text="运单照片"
        app:layout_constraintStart_toStartOf="parent"
        app:layout_constraintTop_toBottomOf="@id/isReceiptReturnImageRlV2" />

    <RelativeLayout
        android:id="@+id/isvReturnImageRlV1"
        android:layout_width="match_parent"
        android:layout_height="wrap_content"
        android:descendantFocusability="blocksDescendants"
        app:layout_constraintEnd_toEndOf="parent"
        app:layout_constraintStart_toStartOf="parent"
        app:layout_constraintTop_toBottomOf="@id/tvReceiptDocumentPhotoV1">

        <com.zczy.comm.utils.imageselector.ImageSelectView
            android:id="@+id/isvReturnImageV1"
            android:layout_width="match_parent"
            android:layout_height="wrap_content"
            android:layout_marginStart="14dp"
            android:layout_marginLeft="14dp"
            android:layout_marginTop="10dp"
            android:layout_marginEnd="14dp"
            android:layout_marginRight="14dp"
            android:layout_marginBottom="14dp"
            android:visibility="gone"
            app:canSelect="false"
            tools:visibility="visible" />

    </RelativeLayout>

    <TextView
        android:id="@+id/tvReceiptDocumentPhotoV3"
        android:layout_width="wrap_content"
        android:layout_height="wrap_content"
        android:layout_marginStart="14dp"
        android:layout_marginTop="8dp"
        android:layout_marginBottom="8dp"
        android:text="挂车车牌照片"
        app:layout_constraintStart_toStartOf="parent"
        app:layout_constraintTop_toBottomOf="@id/isvReturnImageRlV1" />

    <RelativeLayout
        android:id="@+id/isvReceiptReturnImageRlV3"
        android:layout_width="match_parent"
        android:layout_height="wrap_content"
        android:descendantFocusability="blocksDescendants"
        app:layout_constraintEnd_toEndOf="parent"
        app:layout_constraintStart_toStartOf="parent"
        app:layout_constraintTop_toBottomOf="@id/tvReceiptDocumentPhotoV3">

        <com.zczy.comm.utils.imageselector.ImageSelectView
            android:id="@+id/isvReceiptReturnImageV3"
            android:layout_width="match_parent"
            android:layout_height="wrap_content"
            android:layout_marginStart="14dp"
            android:layout_marginEnd="14dp"
            android:layout_marginBottom="12dp"
            android:visibility="gone"
            app:canSelect="false"
            tools:visibility="visible" />

    </RelativeLayout>


    <TextView
        android:id="@+id/isvReturnRemark"
        android:layout_width="match_parent"
        android:layout_height="wrap_content"
        android:layout_marginStart="14dp"
        android:layout_marginTop="8dp"
        android:layout_marginEnd="14dp"
        android:layout_marginBottom="8dp"
        android:paddingBottom="7dp"
        android:text="备注："
        app:layout_constrainedWidth="true"
        app:layout_constraintEnd_toEndOf="parent"
        app:layout_constraintStart_toStartOf="parent"
        app:layout_constraintTop_toBottomOf="@id/isvReceiptReturnImageRlV3" />

    <RelativeLayout
        android:id="@+id/isvReturnVideoRlV1"
        android:layout_width="match_parent"
        android:layout_height="wrap_content"
        android:descendantFocusability="blocksDescendants"
        app:layout_constraintEnd_toEndOf="parent"
        app:layout_constraintStart_toStartOf="parent"
        app:layout_constraintTop_toBottomOf="@id/isvReturnRemark">

        <com.zczy.cargo_owner.libcomm.widget.ImageSelectViewV1
            android:id="@+id/isvReturnVideoV1"
            android:layout_width="match_parent"
            android:layout_height="wrap_content"
            android:layout_marginStart="14dp"
            android:layout_marginEnd="14dp"
            android:layout_marginBottom="12dp"
            android:visibility="gone"
            app:canSelect="false"
            tools:visibility="visible" />

    </RelativeLayout>

    <RelativeLayout
        android:id="@+id/rl_clockin"
        android:layout_width="match_parent"
        android:layout_height="wrap_content"
        android:descendantFocusability="blocksDescendants"
        android:visibility="gone"
        app:layout_constraintEnd_toEndOf="parent"
        app:layout_constraintStart_toStartOf="parent"
        app:layout_constraintTop_toBottomOf="@id/isvReturnVideoRlV1"
        tools:visibility="visible">

        <TextView
            android:id="@+id/tv11"
            android:layout_width="match_parent"
            android:layout_height="wrap_content"
            android:layout_marginStart="14dp"
            android:layout_marginTop="8dp"
            android:layout_marginBottom="8dp"
            android:text="卸货拍照图片"
            app:layout_constraintStart_toStartOf="parent" />

        <com.zczy.comm.utils.imageselector.ImageSelectView
            android:id="@+id/image_clockin"
            android:layout_width="match_parent"
            android:layout_height="wrap_content"
            android:layout_below="@+id/tv11"
            android:layout_marginStart="14dp"
            android:layout_marginLeft="14dp"
            android:layout_marginTop="10dp"
            android:layout_marginEnd="14dp"
            android:layout_marginRight="14dp"
            android:layout_marginBottom="14dp"
            app:canSelect="false" />

    </RelativeLayout>


    <androidx.constraintlayout.widget.ConstraintLayout
        android:id="@+id/jmyl_warning"
        android:layout_width="match_parent"
        android:layout_height="wrap_content"
        android:layout_marginLeft="14dp"
        android:layout_marginTop="7dp"
        android:layout_marginRight="14dp"
        android:background="#fffff3f1"
        android:paddingTop="5dp"
        android:paddingRight="14dp"
        android:paddingBottom="5dp"
        android:visibility="gone"
        app:layout_constraintLeft_toLeftOf="@id/isvReturnImageRlV1"
        app:layout_constraintTop_toBottomOf="@id/rl_clockin"
        tools:visibility="visible">

        <ImageView
            android:id="@+id/iv_worm"
            android:layout_width="15dp"
            android:layout_height="15dp"
            android:layout_marginTop="2dp"
            android:src="@drawable/base_warning"
            app:layout_constrainedWidth="true"
            app:layout_constraintBottom_toBottomOf="parent"
            app:layout_constraintHorizontal_bias="0"
            app:layout_constraintHorizontal_chainStyle="packed"
            app:layout_constraintLeft_toLeftOf="parent"
            app:layout_constraintTop_toTopOf="parent"
            tools:ignore="MissingConstraints" />

        <TextView
            android:id="@+id/tv_warning_jmyl"
            android:layout_width="wrap_content"
            android:layout_height="wrap_content"
            android:layout_marginLeft="5dp"
            android:layout_marginRight="9dp"
            android:textColor="#FF5E1C"
            android:textSize="@dimen/text_13"
            app:layout_constrainedWidth="true"
            app:layout_constraintBottom_toBottomOf="parent"
            app:layout_constraintHorizontal_bias="0.0"
            app:layout_constraintLeft_toRightOf="@id/iv_worm"
            app:layout_constraintRight_toRightOf="parent"
            app:layout_constraintTop_toTopOf="parent"
            app:layout_constraintVertical_bias="1.0"
            tools:text="派车失败：平台将赔付派车失败：平台将赔付派车失败：平台将赔付" />
    </androidx.constraintlayout.widget.ConstraintLayout>

</androidx.constraintlayout.widget.ConstraintLayout>