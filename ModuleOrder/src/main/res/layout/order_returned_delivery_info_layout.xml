<?xml version="1.0" encoding="utf-8"?>
<androidx.constraintlayout.widget.ConstraintLayout xmlns:android="http://schemas.android.com/apk/res/android"
    xmlns:app="http://schemas.android.com/apk/res-auto"
    xmlns:tools="http://schemas.android.com/tools"
    android:layout_width="match_parent"
    android:layout_height="wrap_content"
    android:layout_marginTop="@dimen/divider_7"
    android:background="@color/white">

    <TextView
        android:id="@+id/order_textview"
        android:layout_width="wrap_content"
        android:layout_height="wrap_content"
        android:layout_marginStart="10dp"
        android:text="发货信息"
        android:textColor="@color/text_33"
        android:textSize="@dimen/text_14"
        app:layout_constraintBottom_toBottomOf="@+id/order_divider"
        app:layout_constraintStart_toEndOf="@+id/order_divider"
        app:layout_constraintTop_toTopOf="@+id/order_divider" />

    <TextView
        android:id="@+id/tvCarrierAlreadyEdited"
        android:layout_width="wrap_content"
        android:layout_height="wrap_content"
        android:layout_marginStart="12dp"
        android:drawableStart="@drawable/base_warning"
        android:drawablePadding="5dp"
        android:text="司机已修改"
        android:textColor="#fffb6b40"
        android:textSize="12sp"
        android:visibility="gone"
        app:layout_constraintBottom_toBottomOf="@id/order_textview"
        app:layout_constraintStart_toEndOf="@id/order_textview"
        app:layout_constraintTop_toTopOf="@id/order_textview"
        tools:visibility="visible" />

    <TextView
        android:id="@+id/tvShippingModification"
        android:layout_width="wrap_content"
        android:layout_height="wrap_content"
        android:layout_marginEnd="14dp"
        android:background="@drawable/base_btn_blue_stroke_5radius_selector"
        android:gravity="center"
        android:paddingStart="6dp"
        android:paddingTop="3dp"
        android:paddingEnd="6dp"
        android:paddingBottom="3dp"
        android:text="发货修改"
        android:textColor="#ff5086fc"
        android:textSize="12sp"
        android:visibility="gone"
        app:layout_constraintBottom_toBottomOf="@id/order_textview"
        app:layout_constraintEnd_toEndOf="parent"
        app:layout_constraintTop_toTopOf="@id/order_textview"
        tools:visibility="visible" />

    <View
        android:id="@+id/order_divider"
        android:layout_width="3dp"
        android:layout_height="16dp"
        android:layout_marginStart="14dp"
        android:layout_marginTop="8dp"
        android:background="@color/theme_blue"
        app:layout_constraintStart_toStartOf="parent"
        app:layout_constraintTop_toTopOf="parent" />

    <TextView
        android:id="@+id/order_textview4"
        android:layout_width="wrap_content"
        android:layout_height="wrap_content"
        android:layout_marginStart="14dp"
        android:layout_marginTop="15dp"
        android:text="发货数量"
        app:layout_constraintStart_toStartOf="parent"
        app:layout_constraintTop_toBottomOf="@+id/order_divider" />

    <androidx.recyclerview.widget.RecyclerView
        android:id="@+id/isDeliveryRecyclerViewCargo"
        android:layout_width="match_parent"
        android:layout_height="wrap_content"
        android:paddingTop="10dp"
        android:paddingBottom="10dp"
        app:layout_constraintTop_toBottomOf="@id/order_textview4"
        tools:itemCount="2"
        tools:listitem="@layout/order_upload_returned_order_photo_item_v1" />

    <com.zczy.comm.widget.inputv2.InputViewClick
        android:id="@+id/inputReceiptContainer"
        android:layout_width="match_parent"
        android:layout_height="wrap_content"
        app:input_click_hide_content_hint="true"
        app:input_title="实际发货"
        app:layout_constraintTop_toBottomOf="@id/isDeliveryRecyclerViewCargo" />

    <TextView
        android:id="@+id/tvDeliveryNum"
        android:layout_width="wrap_content"
        android:layout_height="wrap_content"
        android:layout_marginEnd="14dp"
        android:textColor="@color/text_33"
        android:textSize="@dimen/text_15"
        android:textStyle="bold"
        app:layout_constraintBottom_toBottomOf="@+id/order_textview4"
        app:layout_constraintEnd_toEndOf="parent"
        app:layout_constraintTop_toTopOf="@+id/order_textview4"
        tools:text="120吨" />

    <TextView
        android:id="@+id/tvDocumentPhoto"
        android:layout_width="wrap_content"
        android:layout_height="wrap_content"
        android:layout_marginStart="14dp"
        android:layout_marginTop="4dp"
        android:text="司机上传单据照片"
        app:layout_constraintStart_toStartOf="parent"
        app:layout_constraintTop_toBottomOf="@id/inputReceiptContainer" />

    <TextView
        android:id="@+id/ivDocumentPhotoSwitch"
        android:layout_width="wrap_content"
        android:layout_height="wrap_content"
        android:layout_marginStart="12dp"
        android:drawableStart="@drawable/base_switch_blue"
        android:drawablePadding="5dp"
        android:text="切换原图"
        android:textColor="#5086FC"
        android:visibility="gone"
        app:layout_constraintBottom_toBottomOf="@id/tvCarrierAlreadyEdited"
        app:layout_constraintStart_toEndOf="@id/tvCarrierAlreadyEdited"
        app:layout_constraintTop_toTopOf="@id/tvCarrierAlreadyEdited"
        tools:visibility="visible" />

    <RelativeLayout
        android:id="@+id/isvDeliveryImageRl"
        android:layout_width="match_parent"
        android:layout_height="wrap_content"
        android:descendantFocusability="blocksDescendants"
        app:layout_constraintEnd_toEndOf="parent"
        app:layout_constraintStart_toStartOf="parent"
        app:layout_constraintTop_toBottomOf="@id/tvDocumentPhoto">

        <com.zczy.comm.utils.imageselector.ImageSelectView
            android:id="@+id/isvDeliveryImage"
            android:layout_width="match_parent"
            android:layout_height="wrap_content"
            android:layout_marginStart="14dp"
            android:layout_marginEnd="14dp"
            android:layout_marginBottom="12dp"
            android:visibility="gone"
            app:canSelect="false"
            tools:visibility="visible" />
    </RelativeLayout>

    <TextView
        android:id="@+id/isvDeliveryTime"
        android:layout_width="wrap_content"
        android:layout_height="wrap_content"
        android:layout_marginStart="14dp"
        android:textColor="#ff666666"
        android:textSize="12sp"
        app:layout_constraintStart_toStartOf="parent"
        app:layout_constraintTop_toBottomOf="@id/isvDeliveryImageRl"
        tools:text="上传时间：2023-01-01 11:11:11" />

    <LinearLayout
        android:id="@+id/viewAiOrder"
        android:layout_width="match_parent"
        android:layout_height="wrap_content"
        android:layout_margin="15dp"
        android:background="@drawable/base_input_ff5757_circle"
        android:orientation="vertical"
        android:padding="10dp"
        android:visibility="gone"
        app:layout_constraintTop_toBottomOf="@id/isvDeliveryTime"
        tools:visibility="visible">

        <ImageView
            android:layout_width="wrap_content"
            android:layout_height="wrap_content"
            android:background="@drawable/order_return_ai_icon_1" />

        <TextView
            android:id="@+id/orderReturnAiDocuments"
            android:layout_width="wrap_content"
            android:layout_height="wrap_content"
            android:layout_marginTop="15dp"
            android:textColor="#ff666666"
            android:textSize="14sp"
            tools:text="发货单据号：1018080309444292274 " />

        <LinearLayout
            android:layout_width="match_parent"
            android:layout_height="wrap_content"
            android:layout_marginTop="5dp"
            android:gravity="center_vertical">

            <TextView
                android:layout_width="0dp"
                android:layout_height="wrap_content"
                android:layout_weight="1"
                android:text="确认发货单据号 "
                android:textColor="#ff666666"
                android:textSize="14sp" />

            <EditText
                android:id="@+id/inputAiOrderNum"
                android:layout_width="wrap_content"
                android:layout_height="wrap_content"
                android:background="@drawable/base_input_ff5757_circle"
                android:gravity="end"
                android:hint="请输入"
                android:inputType="numberDecimal"
                android:minWidth="200dp"
                android:paddingHorizontal="8dp"
                android:paddingVertical="8dp"
                android:textColorHint="#999999"
                android:textSize="14sp" />

        </LinearLayout>

        <TextView
            android:id="@+id/inputAiOrderNumWarning"
            android:layout_width="wrap_content"
            android:layout_height="wrap_content"
            android:layout_gravity="end"
            android:layout_marginTop="5dp"
            android:drawableStart="@drawable/base_warning"
            android:drawablePadding="5dp"
            android:text="请输入发货单据号"
            android:textColor="#fb6b40"
            android:textSize="14sp"
            android:visibility="gone"
            tools:visibility="visible" />

    </LinearLayout>

    <TextView
        android:id="@+id/tvDocumentPhotoV2"
        android:layout_width="wrap_content"
        android:layout_height="wrap_content"
        android:layout_marginStart="14dp"
        android:layout_marginTop="4dp"
        android:text="货主上传单据照片"
        android:visibility="gone"
        app:layout_constraintStart_toStartOf="parent"
        app:layout_constraintTop_toBottomOf="@id/viewAiOrder"
        tools:visibility="visible" />

    <RelativeLayout
        android:id="@+id/isvDeliveryImageRlV2"
        android:layout_width="match_parent"
        android:layout_height="wrap_content"
        android:descendantFocusability="blocksDescendants"
        android:visibility="gone"
        app:layout_constraintEnd_toEndOf="parent"
        app:layout_constraintStart_toStartOf="parent"
        app:layout_constraintTop_toBottomOf="@id/tvDocumentPhotoV2"
        tools:visibility="visible">

        <com.zczy.comm.utils.imageselector.ImageSelectView
            android:id="@+id/isvDeliveryImageV2"
            android:layout_width="match_parent"
            android:layout_height="wrap_content"
            android:layout_marginStart="14dp"
            android:layout_marginEnd="14dp"
            android:layout_marginBottom="12dp"
            android:visibility="gone"
            app:canSelect="false"
            tools:visibility="visible" />
    </RelativeLayout>

    <TextView
        android:id="@+id/tvDocumentPhotoV1"
        android:layout_width="wrap_content"
        android:layout_height="wrap_content"
        android:layout_marginStart="14dp"
        android:layout_marginTop="8dp"
        android:layout_marginBottom="8dp"
        android:text="运单照片"
        app:layout_constraintStart_toStartOf="parent"
        app:layout_constraintTop_toBottomOf="@id/isvDeliveryImageRlV2" />

    <RelativeLayout
        android:id="@+id/isvDeliveryImageRlV1"
        android:layout_width="match_parent"
        android:layout_height="wrap_content"
        android:descendantFocusability="blocksDescendants"
        app:layout_constraintEnd_toEndOf="parent"
        app:layout_constraintStart_toStartOf="parent"
        app:layout_constraintTop_toBottomOf="@id/tvDocumentPhotoV1">

        <com.zczy.comm.utils.imageselector.ImageSelectView
            android:id="@+id/isvDeliveryImageV1"
            android:layout_width="match_parent"
            android:layout_height="wrap_content"
            android:layout_marginStart="14dp"
            android:layout_marginEnd="14dp"
            android:layout_marginBottom="12dp"
            android:visibility="gone"
            app:canSelect="false"
            tools:visibility="visible" />

    </RelativeLayout>

    <TextView
        android:id="@+id/tvDocumentPhotoV3"
        android:layout_width="wrap_content"
        android:layout_height="wrap_content"
        android:layout_marginStart="14dp"
        android:layout_marginTop="8dp"
        android:layout_marginBottom="8dp"
        android:text="挂车车牌照片"
        app:layout_constraintStart_toStartOf="parent"
        app:layout_constraintTop_toBottomOf="@id/isvDeliveryImageRlV1" />

    <RelativeLayout
        android:id="@+id/isvDeliveryImageRlV3"
        android:layout_width="match_parent"
        android:layout_height="wrap_content"
        android:descendantFocusability="blocksDescendants"
        app:layout_constraintEnd_toEndOf="parent"
        app:layout_constraintStart_toStartOf="parent"
        app:layout_constraintTop_toBottomOf="@id/tvDocumentPhotoV3">

        <com.zczy.comm.utils.imageselector.ImageSelectView
            android:id="@+id/isvDeliveryImageV3"
            android:layout_width="match_parent"
            android:layout_height="wrap_content"
            android:layout_marginStart="14dp"
            android:layout_marginEnd="14dp"
            android:layout_marginBottom="12dp"
            android:visibility="gone"
            app:canSelect="false"
            tools:visibility="visible" />

    </RelativeLayout>


    <TextView
        android:id="@+id/isvDeliveryRemark"
        android:layout_width="match_parent"
        android:layout_height="wrap_content"
        android:layout_marginStart="14dp"
        android:layout_marginTop="8dp"
        android:layout_marginEnd="14dp"
        android:layout_marginBottom="8dp"
        android:paddingBottom="7dp"
        android:text="备注："
        app:layout_constrainedWidth="true"
        app:layout_constraintEnd_toEndOf="parent"
        app:layout_constraintStart_toStartOf="parent"
        app:layout_constraintTop_toBottomOf="@id/isvDeliveryImageRlV3" />

    <RelativeLayout
        android:id="@+id/isvDeliveryVideoRlV1"
        android:layout_width="match_parent"
        android:layout_height="wrap_content"
        android:descendantFocusability="blocksDescendants"
        app:layout_constraintEnd_toEndOf="parent"
        app:layout_constraintStart_toStartOf="parent"
        app:layout_constraintTop_toBottomOf="@id/isvDeliveryRemark">

        <com.zczy.cargo_owner.libcomm.widget.ImageSelectViewV1
            android:id="@+id/isvDeliveryVideoV1"
            android:layout_width="match_parent"
            android:layout_height="wrap_content"
            android:layout_marginStart="14dp"
            android:layout_marginEnd="14dp"
            android:layout_marginBottom="12dp"
            android:visibility="gone"
            app:canSelect="false"
            tools:visibility="visible" />

    </RelativeLayout>

</androidx.constraintlayout.widget.ConstraintLayout>