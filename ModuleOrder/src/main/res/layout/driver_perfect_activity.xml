<?xml version="1.0" encoding="utf-8"?>
<androidx.constraintlayout.widget.ConstraintLayout xmlns:android="http://schemas.android.com/apk/res/android"
    xmlns:app="http://schemas.android.com/apk/res-auto"
    android:layout_width="match_parent"
    android:layout_height="match_parent">

    <com.zczy.comm.widget.AppToolber
        android:id="@+id/toolbar"
        android:layout_width="match_parent"
        android:layout_height="55dp"
        android:background="#ffffff"
        app:layout_constraintTop_toTopOf="parent"
        app:leftIcon="@drawable/base_back_black"
        app:titleColor="#333333"
        app:titleSize="17sp"
        app:titleTxt="上传现场证据" />

    <androidx.constraintlayout.widget.ConstraintLayout
        android:layout_width="match_parent"
        android:layout_height="wrap_content"
        android:background="@color/white"
        android:paddingBottom="10dp"
        app:layout_constraintTop_toBottomOf="@id/toolbar">

        <TextView
            android:id="@+id/tv1"
            android:layout_width="wrap_content"
            android:layout_height="wrap_content"
            android:layout_marginLeft="15dp"
            android:layout_marginTop="13dp"
            android:text="上传现场证据"
            android:textColor="#ff333333"
            android:textSize="17sp"
            app:layout_constraintLeft_toLeftOf="parent"
            app:layout_constraintTop_toTopOf="parent" />

        <TextView
            android:layout_width="wrap_content"
            android:layout_height="wrap_content"
            android:layout_marginLeft="8dp"
            android:text="最多可上传3张照片"
            android:textColor="#ff999999"
            android:textSize="14sp"
            app:layout_constraintBottom_toBottomOf="@id/tv1"
            app:layout_constraintLeft_toRightOf="@id/tv1" />

        <com.zczy.comm.utils.imageselector.ImageSelectProgressView
            android:id="@+id/image_select_view"
            android:layout_width="match_parent"
            android:layout_height="wrap_content"
            android:layout_marginLeft="15dp"
            android:layout_marginTop="18dp"
            android:layout_marginRight="15dp"
            app:layout_constraintTop_toBottomOf="@id/tv1" />
    </androidx.constraintlayout.widget.ConstraintLayout>

    <TextView
        android:id="@+id/tv_submit"
        android:layout_width="match_parent"
        android:layout_height="55dp"
        android:background="@drawable/base_btn_blue_solid_selector"
        android:gravity="center"
        android:text="确认提交"
        android:textColor="@color/white"
        android:textSize="@dimen/text_17"
        app:layout_constraintBottom_toBottomOf="parent" />

</androidx.constraintlayout.widget.ConstraintLayout>
