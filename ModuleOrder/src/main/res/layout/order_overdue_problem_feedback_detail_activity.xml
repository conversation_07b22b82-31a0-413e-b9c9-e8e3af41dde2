<?xml version="1.0" encoding="utf-8"?>
<LinearLayout xmlns:android="http://schemas.android.com/apk/res/android"
    xmlns:app="http://schemas.android.com/apk/res-auto"
    xmlns:tools="http://schemas.android.com/tools"
    android:layout_width="match_parent"
    android:layout_height="match_parent"
    android:background="#ffeff0f3"
    android:orientation="vertical">

    <com.zczy.comm.widget.AppToolber
        android:id="@+id/appToolber"
        android:layout_width="match_parent"
        android:layout_height="55dp"
        android:background="@color/white"
        app:leftIcon="@drawable/base_back_black"
        app:titleColor="#333333"
        app:titleSize="18sp"
        app:titleTxt="问题反馈详情" />

    <androidx.core.widget.NestedScrollView
        android:layout_width="match_parent"
        android:layout_height="wrap_content"
        android:layout_weight="1">

        <LinearLayout
            android:layout_width="match_parent"
            android:layout_height="wrap_content"
            android:orientation="vertical">

            <!-- 运单号 -->
            <LinearLayout
                android:layout_width="match_parent"
                android:layout_height="44dp"
                android:layout_marginTop="7dp"
                android:background="@color/white"
                android:gravity="center_vertical"
                android:orientation="horizontal"
                android:paddingLeft="14dp"
                android:paddingRight="14dp">

                <TextView
                    android:layout_width="wrap_content"
                    android:layout_height="wrap_content"
                    android:text="运单号"
                    android:textColor="#ff333333"
                    android:textSize="16sp"
                    android:textStyle="bold" />

                <TextView
                    android:id="@+id/tv_order_id"
                    android:layout_width="0dp"
                    android:layout_height="wrap_content"
                    android:layout_weight="1"
                    android:gravity="right"
                    android:textColor="#ff666666"
                    android:textSize="16sp"
                    tools:text="123456789" />

            </LinearLayout>

            <!-- 承运信息 -->
            <LinearLayout
                android:layout_width="match_parent"
                android:layout_height="44dp"
                android:layout_marginTop="0.5dp"
                android:background="@color/white"
                android:gravity="center_vertical"
                android:orientation="horizontal"
                android:paddingLeft="14dp"
                android:paddingRight="14dp">

                <TextView
                    android:layout_width="wrap_content"
                    android:layout_height="wrap_content"
                    android:text="承运信息"
                    android:textColor="#ff333333"
                    android:textSize="16sp"
                    android:textStyle="bold" />

                <TextView
                    android:id="@+id/tv_carrier_info"
                    android:layout_width="0dp"
                    android:layout_height="wrap_content"
                    android:layout_weight="1"
                    android:gravity="right"
                    android:textColor="#ff666666"
                    android:textSize="16sp"
                    tools:text="张三 京A12345" />

            </LinearLayout>

            <!-- 问题反馈类型 -->
            <LinearLayout
                android:layout_width="match_parent"
                android:layout_height="44dp"
                android:layout_marginTop="0.5dp"
                android:background="@color/white"
                android:gravity="center_vertical"
                android:orientation="horizontal"
                android:paddingLeft="14dp"
                android:paddingRight="14dp">

                <TextView
                    android:layout_width="wrap_content"
                    android:layout_height="wrap_content"
                    android:text="问题反馈类型"
                    android:textColor="#ff333333"
                    android:textSize="16sp"
                    android:textStyle="bold" />

                <TextView
                    android:id="@+id/tv_feedback_type"
                    android:layout_width="0dp"
                    android:layout_height="wrap_content"
                    android:layout_weight="1"
                    android:gravity="right"
                    android:textColor="#ff666666"
                    android:textSize="16sp"
                    tools:text="货损货差" />

            </LinearLayout>

            <!-- 处理状态 -->
            <LinearLayout
                android:layout_width="match_parent"
                android:layout_height="44dp"
                android:layout_marginTop="0.5dp"
                android:background="@color/white"
                android:gravity="center_vertical"
                android:orientation="horizontal"
                android:paddingLeft="14dp"
                android:paddingRight="14dp">

                <TextView
                    android:layout_width="wrap_content"
                    android:layout_height="wrap_content"
                    android:text="处理状态"
                    android:textColor="#ff333333"
                    android:textSize="16sp"
                    android:textStyle="bold" />

                <TextView
                    android:id="@+id/tv_status"
                    android:layout_width="0dp"
                    android:layout_height="wrap_content"
                    android:layout_weight="1"
                    android:gravity="right"
                    android:textColor="#ff666666"
                    android:textSize="16sp"
                    tools:text="处理中" />

            </LinearLayout>

            <!-- 反馈时间 -->
            <LinearLayout
                android:layout_width="match_parent"
                android:layout_height="44dp"
                android:layout_marginTop="0.5dp"
                android:background="@color/white"
                android:gravity="center_vertical"
                android:orientation="horizontal"
                android:paddingLeft="14dp"
                android:paddingRight="14dp">

                <TextView
                    android:layout_width="wrap_content"
                    android:layout_height="wrap_content"
                    android:text="反馈时间"
                    android:textColor="#ff333333"
                    android:textSize="16sp"
                    android:textStyle="bold" />

                <TextView
                    android:id="@+id/tv_feedback_time"
                    android:layout_width="0dp"
                    android:layout_height="wrap_content"
                    android:layout_weight="1"
                    android:gravity="right"
                    android:textColor="#ff666666"
                    android:textSize="16sp"
                    tools:text="2025-01-29" />

            </LinearLayout>

            <!-- 情况说明 -->
            <LinearLayout
                android:layout_width="match_parent"
                android:layout_height="wrap_content"
                android:layout_marginTop="7dp"
                android:background="@color/white"
                android:orientation="vertical"
                android:paddingLeft="14dp"
                android:paddingRight="14dp"
                android:paddingTop="10dp"
                android:paddingBottom="10dp">

                <TextView
                    android:layout_width="wrap_content"
                    android:layout_height="wrap_content"
                    android:text="情况说明"
                    android:textColor="#ff333333"
                    android:textSize="16sp"
                    android:textStyle="bold" />

                <TextView
                    android:id="@+id/tv_description"
                    android:layout_width="match_parent"
                    android:layout_height="wrap_content"
                    android:layout_marginTop="8dp"
                    android:lineSpacingExtra="4dp"
                    android:textColor="#ff666666"
                    android:textSize="15sp"
                    tools:text="这是问题描述的详细内容，可能会有多行文字显示。" />

            </LinearLayout>

            <!-- 添加图片 -->
            <LinearLayout
                android:id="@+id/ll_images"
                android:layout_width="match_parent"
                android:layout_height="wrap_content"
                android:layout_marginTop="7dp"
                android:background="@color/white"
                android:orientation="vertical"
                android:paddingLeft="14dp"
                android:paddingRight="14dp"
                android:paddingTop="10dp"
                android:paddingBottom="10dp"
                android:visibility="gone"
                tools:visibility="visible">

                <TextView
                    android:layout_width="wrap_content"
                    android:layout_height="wrap_content"
                    android:text="添加图片"
                    android:textColor="#ff333333"
                    android:textSize="16sp"
                    android:textStyle="bold" />

                <androidx.recyclerview.widget.RecyclerView
                    android:id="@+id/rv_images"
                    android:layout_width="match_parent"
                    android:layout_height="wrap_content"
                    android:layout_marginTop="8dp"
                    tools:itemCount="3"
                    tools:listitem="@layout/item_problem_feedback_image" />

            </LinearLayout>

        </LinearLayout>
    </androidx.core.widget.NestedScrollView>

    <TextView
        android:id="@+id/btn_continue_feedback"
        android:layout_width="match_parent"
        android:layout_height="55dp"
        android:background="@drawable/base_btn_blue_solid_selector"
        android:gravity="center"
        android:text="继续问题反馈"
        android:textColor="@color/white"
        android:textSize="@dimen/text_17"
        android:visibility="gone"
        tools:visibility="visible" />

</LinearLayout>
