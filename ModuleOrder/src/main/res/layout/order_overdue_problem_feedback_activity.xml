<?xml version="1.0" encoding="utf-8"?>
<LinearLayout xmlns:android="http://schemas.android.com/apk/res/android"
    xmlns:app="http://schemas.android.com/apk/res-auto"
    xmlns:tools="http://schemas.android.com/tools"
    android:layout_width="match_parent"
    android:layout_height="match_parent"
    android:background="#ffeff0f3"
    android:orientation="vertical">

    <com.zczy.comm.widget.AppToolber
        android:id="@+id/appToolber"
        android:layout_width="match_parent"
        android:layout_height="55dp"
        android:background="@color/white"
        app:leftIcon="@drawable/base_back_black"
        app:titleColor="#333333"
        app:titleSize="18sp"
        app:titleTxt="逾期问题反馈" />

    <androidx.core.widget.NestedScrollView
        android:layout_width="match_parent"
        android:layout_height="wrap_content"
        android:layout_weight="1">

        <LinearLayout
            android:layout_width="match_parent"
            android:layout_height="wrap_content"
            android:orientation="vertical">

            <!-- 运单号 -->
            <LinearLayout
                android:layout_width="match_parent"
                android:layout_height="44dp"
                android:layout_marginTop="7dp"
                android:background="@color/white"
                android:gravity="center_vertical"
                android:orientation="horizontal"
                android:paddingLeft="14dp"
                android:paddingRight="14dp">

                <TextView
                    android:id="@+id/tv_order_id"
                    android:layout_width="wrap_content"
                    android:layout_height="wrap_content"
                    android:text="运单号"
                    android:textColor="#ff333333"
                    android:textSize="16sp"
                    android:textStyle="bold" />
            </LinearLayout>

            <!-- 承运信息 -->
            <LinearLayout
                android:layout_width="match_parent"
                android:layout_height="44dp"
                android:layout_marginTop="0.5dp"
                android:background="@color/white"
                android:gravity="center_vertical"
                android:orientation="horizontal"
                android:paddingLeft="14dp"
                android:paddingRight="14dp">

                <TextView
                    android:id="@+id/tv_carrier"
                    android:layout_width="wrap_content"
                    android:layout_height="wrap_content"
                    android:text="承运信息"
                    android:textColor="#ff333333"
                    android:textSize="16sp"
                    android:textStyle="bold" />

                <TextView
                    android:id="@+id/tv_carrier_info"
                    android:layout_width="0dp"
                    android:layout_height="wrap_content"
                    android:layout_weight="1"
                    android:gravity="right"
                    android:textColor="#ff333333"
                    android:textStyle="bold"
                    android:textSize="16sp"
                    tools:text="张三 京A12345" />

            </LinearLayout>

            <!-- 责任方 -->
            <TextView
                android:layout_width="match_parent"
                android:layout_height="44dp"
                android:layout_marginTop="7dp"
                android:background="@color/white"
                android:gravity="center|left"
                android:paddingLeft="14dp"
                android:text="责任方"
                android:textColor="#ff333333"
                android:textSize="16sp"
                android:textStyle="bold" />

            <androidx.constraintlayout.widget.ConstraintLayout
                android:layout_width="match_parent"
                android:layout_height="wrap_content"
                android:background="@color/white"
                android:paddingLeft="14dp"
                android:paddingRight="14dp">

                <ImageView
                    android:id="@+id/iv_my"
                    android:layout_width="0dp"
                    android:layout_height="67dp"
                    android:layout_marginRight="5dp"
                    android:background="@drawable/order_violate_me_un"
                    app:layout_constraintHorizontal_weight="0.5"
                    app:layout_constraintLeft_toLeftOf="parent"
                    app:layout_constraintRight_toLeftOf="@id/iv_ta"
                    tools:ignore="MissingConstraints" />

                <RadioButton
                    android:id="@+id/rb_my"
                    android:layout_width="wrap_content"
                    android:layout_height="0dp"
                    android:button="@drawable/base_check_blue_selector"
                    android:paddingRight="14dp"
                    android:text=""
                    app:layout_constraintBottom_toBottomOf="@id/iv_my"
                    app:layout_constraintRight_toRightOf="@id/iv_my"
                    app:layout_constraintTop_toTopOf="@id/iv_my" />

                <TextView
                    android:id="@+id/tv_my"
                    android:layout_width="wrap_content"
                    android:layout_height="wrap_content"
                    android:layout_marginRight="7dp"
                    android:text="自己"
                    android:textColor="#ff666666"
                    app:layout_constraintBottom_toBottomOf="@id/rb_my"
                    app:layout_constraintRight_toLeftOf="@id/rb_my"
                    app:layout_constraintTop_toTopOf="@id/rb_my" />

                <ImageView
                    android:id="@+id/iv_ta"
                    android:layout_width="0dp"
                    android:layout_height="67dp"
                    android:layout_marginLeft="5dp"
                    android:background="@drawable/order_violate_ta"
                    app:layout_constraintHorizontal_weight="0.5"
                    app:layout_constraintLeft_toRightOf="@id/iv_my"
                    app:layout_constraintRight_toRightOf="parent"
                    tools:ignore="MissingConstraints" />

                <RadioButton
                    android:id="@+id/rb_ta"
                    android:layout_width="wrap_content"
                    android:layout_height="0dp"
                    android:button="@drawable/base_check_blue_selector"
                    android:checked="true"
                    android:paddingRight="14dp"
                    android:text=""
                    app:layout_constraintBottom_toBottomOf="@id/iv_ta"
                    app:layout_constraintRight_toRightOf="@id/iv_ta"
                    app:layout_constraintTop_toTopOf="@id/iv_ta" />

                <TextView
                    android:id="@+id/tv_ta"
                    android:layout_width="wrap_content"
                    android:layout_height="wrap_content"
                    android:layout_marginRight="7dp"
                    android:text="对方"
                    android:textColor="#ff5086fc"
                    app:layout_constraintBottom_toBottomOf="@id/rb_ta"
                    app:layout_constraintRight_toLeftOf="@id/rb_ta"
                    app:layout_constraintTop_toTopOf="@id/rb_ta" />

            </androidx.constraintlayout.widget.ConstraintLayout>

            <TextView
                android:id="@+id/tv_responsible_name"
                android:layout_width="match_parent"
                android:layout_height="wrap_content"
                android:background="@color/white"
                android:gravity="center|right"
                android:paddingTop="11dp"
                android:paddingRight="14dp"
                android:paddingBottom="11dp"
                android:textColor="#ff666666"
                android:textSize="@dimen/text_17"
                tools:text="张三" />

            <!-- 问题反馈类型 -->
            <com.zczy.comm.widget.inputv2.InputViewClick
                android:id="@+id/view_feedback_type"
                android:layout_width="match_parent"
                android:layout_height="44dp"
                android:layout_marginTop="@dimen/divider_05"
                android:background="@color/white"
                app:input_title="问题反馈类型"
                app:input_title_asterisk="true" />

            <!-- 情况说明 -->
            <TextView
                android:layout_width="match_parent"
                android:layout_height="wrap_content"
                android:layout_marginTop="7dp"
                android:background="@color/white"
                android:paddingLeft="14dp"
                android:paddingTop="10dp"
                android:paddingBottom="10dp"
                android:text="情况说明"
                android:textColor="@color/text_33"
                android:textSize="16sp"
                android:textStyle="bold" />

            <FrameLayout
                android:layout_width="match_parent"
                android:layout_height="wrap_content"
                android:background="@color/white"
                android:paddingLeft="14dp"
                android:paddingRight="14dp"
                android:paddingBottom="12dp">

                <EditText
                    android:id="@+id/ed_description"
                    android:layout_width="match_parent"
                    android:layout_height="96dp"
                    android:background="#fff2f2f2"
                    android:gravity="left|top"
                    android:hint="请输入情况说明"
                    android:maxLength="200"
                    android:padding="7dp"
                    android:textColor="@color/text_66"
                    android:textColorHint="@color/text_99"
                    android:textSize="@dimen/text_15" />

                <TextView
                    android:id="@+id/tv_char_count"
                    android:layout_width="wrap_content"
                    android:layout_height="wrap_content"
                    android:layout_gravity="right|bottom"
                    android:layout_marginRight="10dp"
                    android:layout_marginBottom="7dp"
                    android:gravity="right"
                    android:text="0/200"
                    android:textColor="@color/text_99" />
            </FrameLayout>

            <!-- 添加图片 -->
            <androidx.constraintlayout.widget.ConstraintLayout
                android:layout_width="match_parent"
                android:layout_height="wrap_content"
                android:layout_marginTop="7dp"
                android:background="@color/white"
                android:paddingLeft="14dp"
                android:paddingTop="10dp"
                android:paddingBottom="10dp">

                <TextView
                    android:id="@+id/tv_image_title"
                    android:layout_width="wrap_content"
                    android:layout_height="wrap_content"
                    android:text="添加图片(选填)"
                    android:textColor="@color/text_33"
                    android:textSize="16sp"
                    android:textStyle="bold"
                    tools:ignore="MissingConstraints" />

                <TextView
                    android:layout_width="wrap_content"
                    android:layout_height="wrap_content"
                    android:layout_marginLeft="7dp"
                    android:text="最多可上传9张照片"
                    android:textColor="#ff999999"
                    android:textSize="12sp"
                    app:layout_constraintBottom_toBottomOf="@id/tv_image_title"
                    app:layout_constraintLeft_toRightOf="@id/tv_image_title"
                    app:layout_constraintTop_toTopOf="@id/tv_image_title" />

                <com.zczy.comm.utils.imageselector.ImageSelectProgressView
                    android:id="@+id/image_select_view"
                    android:layout_width="match_parent"
                    android:layout_height="wrap_content"
                    android:layout_marginTop="7dp"
                    android:background="@color/white"
                    app:layout_constraintTop_toBottomOf="@id/tv_image_title"
                    app:maxCount="9"
                    app:spanCount="4" />

            </androidx.constraintlayout.widget.ConstraintLayout>

        </LinearLayout>
    </androidx.core.widget.NestedScrollView>

    <TextView
        android:id="@+id/btn_submit"
        android:layout_width="match_parent"
        android:layout_height="55dp"
        android:background="@drawable/base_btn_blue_solid_selector"
        android:gravity="center"
        android:text="确认"
        android:textColor="@color/white"
        android:textSize="@dimen/text_17" />

</LinearLayout>
