<?xml version="1.0" encoding="utf-8"?>
<androidx.constraintlayout.widget.ConstraintLayout xmlns:android="http://schemas.android.com/apk/res/android"
    xmlns:app="http://schemas.android.com/apk/res-auto"
    xmlns:tools="http://schemas.android.com/tools"
    android:id="@+id/layoutReturnedOrderItem"
    android:layout_width="match_parent"
    android:layout_height="wrap_content"
    android:background="#fffff3f1"
    android:paddingLeft="14dp"
    android:paddingTop="5dp"
    android:paddingRight="14dp"
    android:paddingBottom="5dp">

    <FrameLayout
        android:id="@+id/fy_left"
        android:layout_width="wrap_content"
        android:layout_height="wrap_content"
        app:layout_constraintBottom_toBottomOf="parent"
        app:layout_constraintLeft_toLeftOf="parent"
        app:layout_constraintTop_toTopOf="parent">

        <ImageView
            android:id="@+id/iv_header"
            android:layout_width="31dp"
            android:layout_height="31dp"
            android:src="@drawable/icon_order_cyr" />

        <ImageView
            android:id="@+id/iv_toast_icon"
            android:layout_width="15dp"
            android:layout_height="15dp"
            android:layout_marginTop="2dp"
            android:src="@drawable/base_warning" />
    </FrameLayout>


    <TextView
        android:id="@+id/tv_exception"
        android:layout_width="0dp"
        android:layout_height="wrap_content"
        android:layout_marginLeft="5dp"
        android:layout_marginRight="9dp"
        android:ellipsize="end"
        android:maxLines="2"
        android:textColor="#FF5E1C"
        android:textSize="@dimen/text_13"
        app:layout_constraintBottom_toBottomOf="parent"
        app:layout_constraintLeft_toRightOf="@id/fy_left"
        app:layout_constraintRight_toLeftOf="@id/tv_handle_exception"
        app:layout_constraintTop_toTopOf="parent"
        tools:text="运单异常：材料模糊，请重新提交，我是平台处理建议。运单异常：材料模糊，请重新提交，我是平台处理建议。" />

    <TextView
        android:id="@+id/tv_handle_exception"
        android:layout_width="wrap_content"
        android:layout_height="match_parent"
        android:background="@drawable/order_waybill_heandler_shape"
        android:drawablePadding="5dp"
        android:gravity="center"
        android:text="去处理"
        android:textColor="#FF602E"
        android:textSize="13dp"
        app:layout_constraintBottom_toBottomOf="parent"
        app:layout_constraintRight_toRightOf="parent"
        app:layout_constraintTop_toTopOf="parent" />

</androidx.constraintlayout.widget.ConstraintLayout>