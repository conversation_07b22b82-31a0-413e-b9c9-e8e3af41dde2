<?xml version="1.0" encoding="utf-8"?>
<LinearLayout xmlns:android="http://schemas.android.com/apk/res/android"
    android:layout_width="match_parent"
    android:layout_height="match_parent"
    android:background="#4d000000"
    android:gravity="bottom"
    android:orientation="vertical">


    <RelativeLayout
        android:layout_width="match_parent"
        android:layout_height="48dp"
        android:background="#ffffff">

        <TextView
            android:layout_width="wrap_content"
            android:layout_height="match_parent"
            android:layout_centerInParent="true"
            android:layout_gravity="center"
            android:gravity="center"
            android:text="请选择扣款配置"
            android:textColor="#333333"
            android:textSize="@dimen/text_16"
            android:textStyle="bold" />

        <TextView
            android:id="@+id/tvClose"
            android:layout_width="wrap_content"
            android:layout_height="wrap_content"
            android:layout_alignParentLeft="true"
            android:paddingLeft="5dp"
            android:paddingRight="5dp"
            android:text="×"
            android:textColor="#999999"
            android:textSize="@dimen/text_30" />

        <TextView
            android:id="@+id/tv_sure"
            android:layout_width="wrap_content"
            android:layout_height="wrap_content"
            android:layout_alignParentRight="true"
            android:layout_centerInParent="true"
            android:padding="5dp"
            android:text="确定"
            android:textColor="#5086fc"
            android:textSize="@dimen/text_16" />
    </RelativeLayout>

    <View
        android:layout_width="match_parent"
        android:layout_height="1dp"
        android:background="#e3e3e3" />

    <androidx.recyclerview.widget.RecyclerView
        android:id="@+id/recyclerView"
        android:layout_width="match_parent"
        android:layout_height="wrap_content"
        android:background="#ffffff"
        android:minHeight="180dp"
        android:padding="5dp" />

</LinearLayout>
