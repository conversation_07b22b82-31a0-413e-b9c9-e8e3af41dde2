arguments=--init-script C\:\\Users\\Administrator\\AppData\\Roaming\\TabNine\\language-servers\\bundled_java\\1.24.0\\app\\configuration\\org.eclipse.osgi\\53\\0\\.cp\\gradle\\init\\init.gradle
auto.sync=false
build.scans.enabled=false
connection.gradle.distribution=GRADLE_DISTRIBUTION(WRAPPER)
connection.project.dir=
eclipse.preferences.version=1
gradle.user.home=
java.home=C\:/Program Files/Java/jdk-21
jvm.arguments=
offline.mode=false
override.workspace.settings=true
show.console.view=true
show.executions.view=true
