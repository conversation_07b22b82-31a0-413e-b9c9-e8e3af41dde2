package com.zczy.plugin.wisdom.preferential.req

import com.zczy.comm.http.entity.BaseNewRequest
import com.zczy.comm.http.entity.BaseRsp
import com.zczy.comm.http.entity.PageList
import com.zczy.comm.http.entity.ResultData

/**
 * 功能描述: 查询货主油品现金奖励列表
 * <AUTHOR>
 * @date 2022/10/28-15:38
 */

class ReqQueryConsignorOilCashRebateList(
    var nowPage: Int = 1,
    var pageSize: Int = 10,
) : BaseNewRequest<BaseRsp<PageList<RspQueryConsignorOilCashRebateList>>>("pps-app/accountConsignor/queryConsignorOilCashRebateList")

data class RspQueryConsignorOilCashRebateList(
    var orderId: String? = null, // 订单号
    var checkNo: String? = null, // 复核单号
    var settleTime: String? = null, // 运费抵扣时间
    var oilCashRebateMoney: String? = null, // 奖励金额
    var businessTypeStr: String? = null, // 业态
    var tradeRemark: String? = null, // 费用描述
    var tradeType: Int? = null, // -1 是抵扣时间，=1是退回时间， =2是结算时间
) : ResultData()