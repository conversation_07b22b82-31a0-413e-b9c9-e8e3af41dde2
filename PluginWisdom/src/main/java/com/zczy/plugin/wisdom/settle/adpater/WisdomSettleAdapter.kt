package com.zczy.plugin.wisdom.settle.adpater

import android.text.TextUtils

import com.chad.library.adapter.base.BaseQuickAdapter
import com.chad.library.adapter.base.BaseViewHolder
import com.zczy.plugin.wisdom.R
import com.zczy.plugin.wisdom.rsp.settle.RspSettle

/**
 * 功能描述: 结算明细列表
 *
 * <AUTHOR>
 * @company 中储智运科技股份有限公司
 * @copyright （版权）中储南京智慧物流科技有限公司所有
 * @date 2019/1/29 18:53
 */

class WisdomSettleAdapter : BaseQuickAdapter<RspSettle, BaseViewHolder>(R.layout.wisdom_settle_item) {

    override fun convert(helper: BaseViewHolder, item: RspSettle) {
        //设置文字
        helper.setText(R.id.tv_start_address, item.despatchCity)
                .setText(R.id.tv_end_address, item.deliverCity)
                .setText(R.id.tv_content, item.slipLoad)
                .setText(R.id.tv_time, item.settleTime)
                .setText(R.id.tv_money, item.settleMoney + "元")

    }

}
