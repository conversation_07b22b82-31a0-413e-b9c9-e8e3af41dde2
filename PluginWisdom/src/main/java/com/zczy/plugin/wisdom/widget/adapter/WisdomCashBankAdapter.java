package com.zczy.plugin.wisdom.widget.adapter;

import android.text.TextUtils;
import android.widget.ImageView;

import com.chad.library.adapter.base.BaseQuickAdapter;
import com.chad.library.adapter.base.BaseViewHolder;
import com.zczy.cargo_owner.libcomm.config.HttpConfig;
import com.zczy.comm.utils.imgloader.ImgUtil;
import com.zczy.plugin.wisdom.R;
import com.zczy.plugin.wisdom.req.cash.RspCardList;

/**
 * user: ssp
 * time: 2021/6/22 17:25
 * desc: 选择银行卡
 */

public class WisdomCashBankAdapter extends BaseQuickAdapter<RspCardList, BaseViewHolder> {

    public WisdomCashBankAdapter() {
        super(R.layout.wisdom_cash_bank_item);
    }

    @Override
    protected void convert(BaseViewHolder helper, RspCardList item) {
        //是否展示勾种选项
        helper.setGone(R.id.iv_select_state, TextUtils.equals("1", item.isDefault()));
        String bankCardNo = item.getBankCardNo();
        if (!TextUtils.isEmpty(bankCardNo) && bankCardNo.length() > 4) {
            String showBank = bankCardNo.substring(bankCardNo.length() - 4);
            helper.setText(R.id.tv_bank_detail, item.getBankName() + " " + item.getCardType() + "  (" + showBank + ")");
        }

        if (!TextUtils.isEmpty(item.getBackgroundColor())) {
            ImageView ivBankLogo = helper.getView(R.id.iv_bank_logo);
            ImgUtil.loadViewUrl(ivBankLogo, HttpConfig.getUrlImage(item.getLogo()));
        }

    }
}

