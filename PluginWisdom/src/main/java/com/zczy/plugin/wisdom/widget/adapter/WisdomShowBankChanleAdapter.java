package com.zczy.plugin.wisdom.widget.adapter;

import android.text.TextUtils;
import android.util.SparseArray;
import android.widget.ImageView;

import com.chad.library.adapter.base.BaseQuickAdapter;
import com.chad.library.adapter.base.BaseViewHolder;
import com.zczy.cargo_owner.libcomm.config.HttpConfig;
import com.zczy.comm.utils.imgloader.ImgUtil;
import com.zczy.plugin.wisdom.R;
import com.zczy.plugin.wisdom.rsp.bank.RspAddPublicBankChanleList;

/**
 * 功能描述: 选择所属银行
 *
 * <AUTHOR>
 * @company 中储智运科技股份有限公司
 * @copyright （版权）中储南京智慧物流科技有限公司所有
 * @date 2019/2/18 19:29
 */
public class WisdomShowBankChanleAdapter extends BaseQuickAdapter<RspAddPublicBankChanleList, BaseViewHolder> {

    private SparseArray<RspAddPublicBankChanleList> tempData = new SparseArray<>();

    public WisdomShowBankChanleAdapter() {
        super(R.layout.wisdom_show_bank_chanle_item);
    }

    @Override
    protected void convert(BaseViewHolder helper, RspAddPublicBankChanleList item) {

        helper.setText(R.id.tv_bank_detail, item.getBankName());
        if (!TextUtils.isEmpty(item.getLogoPic())) {
            ImageView ivBankLogo = helper.getView(R.id.iv_bank_logo);
            ImgUtil.loadViewUrl(ivBankLogo, HttpConfig.getUrlImage(item.getLogoPic()));
        }
        //设置当前选中项
        if (tempData.get(item.hashCode()) != null) {
            helper.setGone(R.id.iv_select_state, true);
        } else {
            helper.setGone(R.id.iv_select_state, false);
        }

    }

    /**
     * 设置当前选中项
     *
     * @param data
     */
    public void setSelectState(RspAddPublicBankChanleList data) {
        tempData.clear();
        tempData.put(data.hashCode(), data);
        notifyDataSetChanged();
    }
}

