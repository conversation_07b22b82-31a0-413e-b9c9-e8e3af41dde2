package com.zczy.plugin.wisdom.earnest.adapter

import com.chad.library.adapter.base.BaseQuickAdapter
import com.chad.library.adapter.base.BaseViewHolder
import com.zczy.plugin.wisdom.R
import com.zczy.plugin.wisdom.earnest.model.req.RspGetInsuranceRouteList
import com.zczy.plugin.wisdom.earnest.model.req.showEnd
import com.zczy.plugin.wisdom.earnest.model.req.showStart

/**
 * 功能描述: 免诚意金管理
 *
 * <AUTHOR>
 * @date 2022/11/9-9:28
 */
class UserInsuranceInfoListAdapterV2 : BaseQuickAdapter<RspGetInsuranceRouteList, BaseViewHolder>(R.layout.user_insurance_item_adapter_v2) {
    private val YYYY_MM_DD_HH_MM_SS = "yyyy-MM-dd HH:mm:ss"
    override fun convert(helper: BaseViewHolder, item: RspGetInsuranceRouteList) {
        helper.apply {
            setText(R.id.tv1_1, item.createdTime)
            setText(R.id.tv2_2, item.showStart())
            setText(R.id.tv3_3, item.showEnd())
        }
    }
}