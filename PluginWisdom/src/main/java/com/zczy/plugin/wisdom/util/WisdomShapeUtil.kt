package com.zczy.plugin.wisdom.util

import android.graphics.drawable.GradientDrawable
import androidx.annotation.ColorInt
import com.zczy.comm.utils.dp2px

/**
 * author: SongShuangPeng
 * company: 南京中储智慧物流
 * date:   On 2019/4/11
 */

object WisdomShapeUtil {

    @JvmOverloads
    fun creatGradientShape(@ColorInt startColor: Int,
                           @ColorInt endColor: Int,
                           orientation: GradientDrawable.Orientation = GradientDrawable.Orientation.BL_TR,
                           radiusDP: Float = 0F,
                           strokeDp: Float = 0f,
                           @ColorInt strokeColor: Int
    ): GradientDrawable {
        val gradientDrawable = GradientDrawable(orientation, intArrayOf(startColor, endColor))
        gradientDrawable.cornerRadius = dp2px(radiusDP).toFloat()
        gradientDrawable.setStroke(dp2px(strokeDp), strokeColor)
        return gradientDrawable
    }
}