package com.zczy.plugin.wisdom.earnest.fragment

import android.app.Activity.RESULT_OK
import android.content.Intent
import android.os.Bundle
import android.view.View
import com.sfh.lib.mvvm.annotation.LiveDataMatch
import com.zczy.cargo_owner.libcomm.config.HttpConfig
import com.zczy.comm.CommServer
import com.zczy.comm.data.entity.EImage
import com.zczy.comm.ui.BaseFragment
import com.zczy.comm.widget.pulltorefresh.CommEmptyView
import com.zczy.plugin.wisdom.R
import com.zczy.plugin.wisdom.earnest.FreeEarnestMoneyApplyActivity
import com.zczy.plugin.wisdom.earnest.adapter.UserInsuranceInfoListAdapterV1
import com.zczy.plugin.wisdom.earnest.model.EarnestModel
import com.zczy.plugin.wisdom.earnest.model.PageListElse
import com.zczy.plugin.wisdom.earnest.model.req.RspUserInsuranceInfoList
import kotlinx.android.synthetic.main.free_earnest_fragment_v1.*

/**
 * 功能描述: 申请记录
 * <AUTHOR>
 * @date 2022/11/9-14:27
 */

class FreeEarnestMoneyMainFragmentV1 : BaseFragment<EarnestModel>() {
    private val mAdapterV1 = UserInsuranceInfoListAdapterV1()

    companion object {

        @JvmStatic
        fun newInstance(): FreeEarnestMoneyMainFragmentV1 {
            return FreeEarnestMoneyMainFragmentV1()
        }
    }

    override fun getLayout(): Int {
        return R.layout.free_earnest_fragment_v1
    }

    override fun initData() {

    }

    override fun bindView(view: View, bundle: Bundle?) {
        swipeRefreshMoreLayout.apply {
            setAdapter(mAdapterV1, true)
            setEmptyView(CommEmptyView.creatorDef(<EMAIL>))
            addItemDecorationSize(15)
            setOnLoadListener2 { viewModel?.getUserInsuranceInfoList(userId = CommServer.getUserServer().login.userId, userType = "1") }
            addOnItemChildClickListener { adapter, view, position ->
                val data = adapter.getItem(position) as RspUserInsuranceInfoList
                when (view.id) {
                    R.id.tvReapply -> {
                        FreeEarnestMoneyApplyActivity.start(
                            activity = <EMAIL>,
                            requestCode = FreeEarnestMoneyApplyActivity.REQUESTCODE,
                            mRspUserInsuranceInfoList = data,
                            isDetail = false,
                            isApply = true
                        )
                    }
                    R.id.tvDetail -> {
                        when (data.auditState) {
                            "驳回" -> {
                                FreeEarnestMoneyApplyActivity.start(
                                    activity = <EMAIL>,
                                    requestCode = FreeEarnestMoneyApplyActivity.REQUESTCODE,
                                    mRspUserInsuranceInfoList = data,
                                    isDetail = false,
                                    isApply = false
                                )
                            }
                            else -> {
                                FreeEarnestMoneyApplyActivity.start(
                                    activity = <EMAIL>,
                                    requestCode = FreeEarnestMoneyApplyActivity.REQUESTCODE,
                                    mRspUserInsuranceInfoList = data,
                                    isDetail = true,
                                    isApply = false
                                )
                            }
                        }
                    }
                }
            }
            onAutoRefresh()
        }
        bindClickEvent(tv_apply)
    }

    override fun onSingleClick(v: View) {
        super.onSingleClick(v)
        when (v.id) {
            R.id.tv_apply -> {
                FreeEarnestMoneyApplyActivity.start(
                    activity = <EMAIL>,
                    requestCode = FreeEarnestMoneyApplyActivity.REQUESTCODE,
                    mRspUserInsuranceInfoList = null,
                    isDetail = false,
                    isApply = true
                )
            }
        }
    }

    override fun onActivityResult(requestCode: Int, resultCode: Int, data: Intent?) {
        super.onActivityResult(requestCode, resultCode, data)
        if (resultCode == RESULT_OK) {
            swipeRefreshMoreLayout.onAutoRefresh()
        }
    }

    @LiveDataMatch
    open fun onGetUserInsuranceInfoListSuccess(data: PageListElse<RspUserInsuranceInfoList?>) {
        swipeRefreshMoreLayout.onRefreshCompale(data)
        var rebut = false
        mAdapterV1.data.forEachIndexed { index, rspUserInsuranceInfoList ->
            if (index > 0) {
                return@forEachIndexed
            }
            when (rspUserInsuranceInfoList.auditState) {
                "驳回" -> {
                    //存在驳回数据
                    rebut = true
                    return@forEachIndexed
                }
                "待审核" -> {
                    //存在驳回数据
                    rebut = true
                    return@forEachIndexed
                }
            }
        }
        when (data.receiptInsureProve) {
            "0" -> {
                //关闭中
                iv_status.setImageResource(R.drawable.earnest_close)
                if (rebut) {
                    tv_apply.setBackgroundResource(R.drawable.earnest_close_bg)
                    tv_apply.isEnabled = false
                } else {
                    tv_apply.setBackgroundResource(R.drawable.earnest_open_bg)
                    tv_apply.isEnabled = true
                }
            }
            "1" -> {
                //已开通
                tv_apply.setBackgroundResource(R.drawable.earnest_close_bg)
                iv_status.setImageResource(R.drawable.earnest_open)
                tv_apply.isEnabled = false
            }
        }
    }

    @LiveDataMatch
    open fun onGetUserInsuranceInfoListError(error: String?) {
        showDialogToast(error)
        swipeRefreshMoreLayout.onLoadMoreFail()
    }

    private fun getImageList(netUrl: String?): List<EImage> {
        val list: MutableList<EImage> = ArrayList()
        val image = EImage()
        image.netUrl = HttpConfig.getUrlImage(netUrl)
        list.add(image)
        return list
    }
}