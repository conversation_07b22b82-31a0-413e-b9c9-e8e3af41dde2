package com.zczy.plugin.wisdom.rsp.home;

import com.zczy.comm.http.entity.ResultData;

/**
 * <AUTHOR>
 * @description (余额不足列表数据)
 * @company 中储智运科技股份有限公司
 * @copyright （版权）中储南京智慧物流科技有限公司所有
 * @time 2019/5/30  10:21
 */
public class RspAccount extends ResultData {

    /**
     * 运费
     */
    private String pbconsignorDisplayMoney;
    /**
     * 货物单位(1吨，2方)
     */
    private String cargoCategory;
    /**
     * 车长
     */
    private String vehicleLength;
    /**
     * 目的地(区)
     */
    private String deliverDis;
    /**
     * 货物名称
     */
    private String cargoName;
    /**
     * 运单号
     */
    private String orderId;
    /**
     * 目的地(城市)
     */
    private String deliverCity;
    /**
     * 货物数量
     */
    private String weight;
    /**
     * 起运地(城市)
     */
    private String despatchCity;
    /**
     * 起运地(区)
     */
    private String despatchDis;
    /**
     * 车辆类型
     */
    private String vehicleType;
    /**
     * 类型(0抢,1竞)
     */
    private String orderModel;
    /**
     * 待付总金额
     */
    private String totalFeeToPay;

    public String getTotalFeeToPay() {
        return totalFeeToPay;
    }

    public void setTotalFeeToPay(String totalFeeToPay) {
        this.totalFeeToPay = totalFeeToPay;
    }

    public String getPbconsignorDisplayMoney() {
        return pbconsignorDisplayMoney;
    }

    public void setPbconsignorDisplayMoney(String pbconsignorDisplayMoney) {
        this.pbconsignorDisplayMoney = pbconsignorDisplayMoney;
    }

    public String getCargoCategory() {
        return cargoCategory;
    }

    public void setCargoCategory(String cargoCategory) {
        this.cargoCategory = cargoCategory;
    }

    public String getVehicleLength() {
        return vehicleLength;
    }

    public void setVehicleLength(String vehicleLength) {
        this.vehicleLength = vehicleLength;
    }

    public String getDeliverDis() {
        return deliverDis;
    }

    public void setDeliverDis(String deliverDis) {
        this.deliverDis = deliverDis;
    }

    public String getCargoName() {
        return cargoName;
    }

    public void setCargoName(String cargoName) {
        this.cargoName = cargoName;
    }

    public String getOrderId() {
        return orderId;
    }

    public void setOrderId(String orderId) {
        this.orderId = orderId;
    }

    public String getDeliverCity() {
        return deliverCity;
    }

    public void setDeliverCity(String deliverCity) {
        this.deliverCity = deliverCity;
    }

    public String getWeight() {
        return weight;
    }

    public void setWeight(String weight) {
        this.weight = weight;
    }

    public String getDespatchCity() {
        return despatchCity;
    }

    public void setDespatchCity(String despatchCity) {
        this.despatchCity = despatchCity;
    }

    public String getDespatchDis() {
        return despatchDis;
    }

    public void setDespatchDis(String despatchDis) {
        this.despatchDis = despatchDis;
    }

    public String getVehicleType() {
        return vehicleType;
    }

    public void setVehicleType(String vehicleType) {
        this.vehicleType = vehicleType;
    }

    public String getOrderModel() {
        return orderModel;
    }

    public void setOrderModel(String orderModel) {
        this.orderModel = orderModel;
    }
}
