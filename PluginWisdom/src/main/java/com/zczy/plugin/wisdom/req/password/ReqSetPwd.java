package com.zczy.plugin.wisdom.req.password;

;
import com.zczy.comm.http.entity.BaseRsp;
import com.zczy.comm.http.entity.ResultData;
import com.zczy.plugin.wisdom.BaseWisdomRequest;

/**
 * 功能描述: 支付密码参数
 *
 * <AUTHOR>
 * @company 中储智运科技股份有限公司
 * @copyright （版权）中储南京智慧物流科技有限公司所有
 * @date 2018/11/19
 */
public class ReqSetPwd extends BaseWisdomRequest<BaseRsp<ResultData>> {
    private String userPwd;
    private String token;

    public ReqSetPwd() {
        super("pps-app/account/setPasswordWithToken");
    }

    public void setUserPwd(String userPwd) {
        this.userPwd = userPwd;
    }

    public void setToken(String token) {
        this.token = token;
    }
}
