package com.zczy.plugin.wisdom.earnest

import android.content.Context
import android.content.Intent
import android.os.Bundle
import androidx.fragment.app.Fragment
import com.flyco.tablayout.listener.CustomTabEntity
import com.sfh.lib.mvvm.service.BaseViewModel
import com.zczy.comm.ui.BaseActivity
import com.zczy.comm.widget.tablayout.CommonTabEntity
import com.zczy.plugin.wisdom.R
import com.zczy.plugin.wisdom.earnest.fragment.FreeEarnestMoneyMainFragmentV1
import com.zczy.plugin.wisdom.earnest.fragment.FreeEarnestMoneyMainFragmentV2
import kotlinx.android.synthetic.main.free_earnest_activity_v1.*

/**
 *  user: ssp
 *  time: 2020/6/15 13:59
 *  desc: 索要押金管理
 */
class FreeEarnestMoneyMainActivity : BaseActivity<BaseViewModel>() {

    companion object {
        @JvmStatic
        fun start(context: Context?) {
            val intent = Intent(context, FreeEarnestMoneyMainActivity::class.java)
            context?.startActivity(intent)
        }
    }

    override fun getLayout(): Int {
        return R.layout.free_earnest_activity_v1
    }

    override fun bindView(bundle: Bundle?) {
        initCommonTab()
    }

    override fun initData() {

    }


    private fun initCommonTab() {

        val fragments = ArrayList<androidx.fragment.app.Fragment>()
        val tabEntity = ArrayList<CustomTabEntity>()
        val tabEntity1 = CommonTabEntity()
        tabEntity1.title = "申请记录"
        val tabEntity2 = CommonTabEntity()
        tabEntity2.title = "指定路线"

        tabEntity.add(tabEntity1)
        tabEntity.add(tabEntity2)

        fragments.add(FreeEarnestMoneyMainFragmentV1.newInstance())
        fragments.add(FreeEarnestMoneyMainFragmentV2.newInstance())

        common_tab_layout.setTabData(tabEntity, this, R.id.frame_layout, fragments)
        common_tab_layout.currentTab = 0
    }
}
