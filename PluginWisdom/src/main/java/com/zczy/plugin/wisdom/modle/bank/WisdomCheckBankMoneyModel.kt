package com.zczy.plugin.wisdom.modle.bank

import com.sfh.lib.mvvm.service.BaseViewModel
import com.zczy.comm.http.entity.BaseRsp
import com.zczy.comm.http.entity.ResultData
import com.zczy.plugin.wisdom.bank.req.ReqVerifyPaymentResult

/**
 *    author : Ssp
 *    date   : 2019/12/16 14:13
 *    desc   :  企业银行卡打款认证
 */
class WisdomCheckBankMoneyModel : BaseViewModel() {

    /**
     *  企业银行卡打款认证
     */
    fun verifyPaymentResult(req: ReqVerifyPaymentResult) {
        this.execute<BaseRsp<ResultData>>(true, req) { baseRsp ->
            hideLoading()
            if (baseRsp.success()) {
                setValue("onAddBankSuccess", baseRsp.msg)
            } else {
                setValue("onAddBankError", baseRsp)
            }
        }
    }

}
