package com.zczy.plugin.wisdom.budget

import android.content.Context
import android.content.Intent
import android.os.Bundle
import android.text.TextUtils
import android.view.KeyEvent
import android.view.View
import android.view.inputmethod.InputMethodManager
import com.chad.library.adapter.base.BaseQuickAdapter
import com.chad.library.adapter.base.listener.OnItemClickListener
import com.jakewharton.rxbinding2.widget.RxTextView
import com.sfh.lib.mvvm.annotation.LiveDataMatch
import com.zczy.comm.ui.BaseActivity
import com.zczy.comm.widget.pulltorefresh.CommEmptyView
import com.zczy.plugin.wisdom.R
import com.zczy.plugin.wisdom.budget.adapter.WisdomBudgetAllAdapter
import com.zczy.plugin.wisdom.modle.budget.WisdomBudgetListModel
import com.zczy.plugin.wisdom.req.budget.BudgetIncomeList
import com.zczy.plugin.wisdom.req.budget.ReqQueryBudget
import com.zczy.plugin.wisdom.req.budget.RspBudgetIncome
import io.reactivex.android.schedulers.AndroidSchedulers
import kotlinx.android.synthetic.main.wisdom_budget_income_fragment.oilRecordListRefreshMoreLayout
import kotlinx.android.synthetic.main.wisdom_budget_search_activity.*

/**
 * 功能描述: 收支搜索
 * <AUTHOR>
 * @date 2022/8/18-10:30
 */

class WisdomBudgetSearchActivity : BaseActivity<WisdomBudgetListModel>() {

    private var queryType = ""
    private var bookNo = ""
    private var fundMode = ""
    private var searchKey = ""
    private var scrollId: String? = null

    override fun getLayout(): Int {
        return R.layout.wisdom_budget_search_activity
    }

    override fun bindView(bundle: Bundle?) {
        bindClickEvent(img_back)
        bindClickEvent(btn_search)
        bindClickEvent(img_search_clear)
        intent?.getStringExtra("bookNo")?.let {
            bookNo = it
        }
        intent?.getStringExtra("fundMode")?.let {
            fundMode = it
        }
        queryType = when (intent.getIntExtra("index", 0)) {
            1 -> "1"
            2 -> "2"
            else -> ""
        }
        ed_search.setOnKeyListener(View.OnKeyListener { v, keyCode, event ->
            if (keyCode == KeyEvent.KEYCODE_ENTER && event.action == KeyEvent.ACTION_UP) {
                if (searchKey.isEmpty()) {
                    showToast("请输入搜索运单号")
                    return@OnKeyListener false
                }
                val imm = v.context.getSystemService(Context.INPUT_METHOD_SERVICE) as InputMethodManager
                if (imm.isActive) {
                    imm.hideSoftInputFromWindow(v.applicationWindowToken, 0)
                }
                // 软键盘点击回车的事件处理
                val reqQueryBudget = ReqQueryBudget()
                reqQueryBudget.bookNo = bookNo
                reqQueryBudget.fundMode = fundMode
                reqQueryBudget.financeType = queryType
                reqQueryBudget.orderId = ed_search.text.toString()
                reqQueryBudget.scrollId = null
                viewModel?.queryBudget(reqQueryBudget = reqQueryBudget, nowPage = 1)
                return@OnKeyListener true
            }
            false
        })

        RxTextView.textChanges(ed_search)
            .observeOn(AndroidSchedulers.mainThread())
            .map(CharSequence::toString)
            .subscribe {
                when {
                    it.isEmpty() -> {
                        img_search_clear.visibility = View.GONE
                    }
                    else -> {
                        img_search_clear.visibility = View.VISIBLE
                    }
                }
                searchKey = it
            }
            .apply {
                putDisposable(this)
            }

        oilRecordListRefreshMoreLayout.apply {

            val adapter = WisdomBudgetAllAdapter()
            val emptyView = CommEmptyView.creatorDef(context)
            setAdapter(adapter, true)
            setEmptyView(emptyView)
            addOnItemListener(object : OnItemClickListener() {
                override fun onSimpleItemClick(adapter: BaseQuickAdapter<*, *>, view: View, position: Int) {
                    val rspBudgetIncome = adapter.data[position] as RspBudgetIncome
                    val ordType = rspBudgetIncome.ordType
                    val recordId = rspBudgetIncome.ordId
                    //1：转入 2：转出
                    when {
                        TextUtils.equals("1", ordType) -> {
                            //转入
                            WisdomRechargeDetailActivity.startContentUI(context, recordId, ordType, fundMode)
                        }
                        TextUtils.equals("2", ordType) -> {
                            //转出
                            WisdomCashDetailActivity.startContentUI(context, recordId, ordType, fundMode)
                        }
                        else -> {
                            WisdomBudgetDetailActivity.startContentUI(context, recordId, ordType, fundMode)
                        }
                    }
                }

                override fun onItemChildClick(adapter: BaseQuickAdapter<*, *>, view: View, position: Int) {
                    super.onItemChildClick(adapter, view, position)
                }
            })
            setOnLoadListener2 {
                val reqQueryBudget = ReqQueryBudget()
                reqQueryBudget.bookNo = bookNo
                reqQueryBudget.fundMode = fundMode
                reqQueryBudget.financeType = queryType
                reqQueryBudget.orderId = ed_search.text.toString()
                if (it == 1) {
                    reqQueryBudget.scrollId = null
                } else {
                    reqQueryBudget.scrollId = scrollId
                }
                viewModel?.queryBudget(reqQueryBudget = reqQueryBudget, nowPage = it)
            }
        }
    }

    override fun initData() {}

    override fun onSingleClick(v: View) {
        super.onSingleClick(v)
        when (v.id) {
            R.id.img_back -> {
                finish()
            }
            R.id.btn_search -> {
                if (searchKey.isEmpty()) {
                    showToast("请输入运单号搜索")
                    return
                }
                oilRecordListRefreshMoreLayout.onAutoRefresh()
            }
            R.id.img_search_clear -> {
                ed_search.setText("")
            }
        }
    }

    @LiveDataMatch
    open fun getBudgetRecordListError(error: String?) {
        showToast(error)
        oilRecordListRefreshMoreLayout.onLoadMoreFail()
    }

    @LiveDataMatch
    open fun getBudgetRecordListSuccess(data: BudgetIncomeList<RspBudgetIncome>?) {
        data?.let {
            scrollId = it.scrollId
            if (TextUtils.equals(it.scrollId, "-1")) {
                //无更多数据
                oilRecordListRefreshMoreLayout.onRefreshCompale(it.nowPage, it.nowPage, data.rootArray)
            } else {
                if ((it.rootArray?.size ?: 0) < 10) {
                    //无更多数据
                    oilRecordListRefreshMoreLayout.onRefreshCompale(it.nowPage, it.nowPage, data.rootArray)
                } else {
                    oilRecordListRefreshMoreLayout.onRefreshCompale(it.nowPage, it.nowPage + 1, data.rootArray)
                }
            }
        }
    }

    companion object {
        @JvmStatic
        fun start(context: Context, index: Int, bookNo: String, fundMode: String) {
            val intent = Intent(context, WisdomBudgetSearchActivity::class.java)
            intent.putExtra("index", index)
            intent.putExtra("bookNo", bookNo)
            intent.putExtra("fundMode", fundMode)
            context.startActivity(intent)
        }
    }
}