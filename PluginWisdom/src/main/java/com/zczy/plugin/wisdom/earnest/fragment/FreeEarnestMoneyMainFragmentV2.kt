package com.zczy.plugin.wisdom.earnest.fragment

import android.app.Activity.RESULT_OK
import android.content.Intent
import android.os.Bundle
import android.view.View
import com.sfh.lib.mvvm.annotation.LiveDataMatch
import com.zczy.cargo_owner.libcomm.config.HttpConfig
import com.zczy.comm.CommServer
import com.zczy.comm.data.entity.EImage
import com.zczy.comm.ui.BaseFragment
import com.zczy.comm.utils.ex.isTrue
import com.zczy.comm.utils.imageselector.ImagePreviewActivity
import com.zczy.comm.widget.pulltorefresh.CommEmptyView
import com.zczy.plugin.wisdom.R
import com.zczy.plugin.wisdom.earnest.adapter.UserInsuranceInfoListAdapterV2
import com.zczy.plugin.wisdom.earnest.model.EarnestModel
import com.zczy.plugin.wisdom.earnest.model.PageListV1
import com.zczy.plugin.wisdom.earnest.model.req.RspGetInsuranceRouteList
import com.zczy.plugin.wisdom.earnest.model.req.RspUserInsuranceInfoList
import kotlinx.android.synthetic.main.free_earnest_fragment_v2.*

/**
 * 功能描述: 申请记录
 * <AUTHOR>
 * @date 2022/11/9-14:27
 */

class FreeEarnestMoneyMainFragmentV2 : BaseFragment<EarnestModel>() {

    companion object {

        @JvmStatic
        fun newInstance(): FreeEarnestMoneyMainFragmentV2 {
            return FreeEarnestMoneyMainFragmentV2()
        }
    }

    override fun getLayout(): Int {
        return R.layout.free_earnest_fragment_v2
    }

    override fun initData() {

    }

    override fun bindView(view: View, bundle: Bundle?) {
        swipeRefreshMoreLayout.apply {
            setAdapter(UserInsuranceInfoListAdapterV2(), true)
            setEmptyView(CommEmptyView.creatorDef(<EMAIL>))
            addItemDecorationSize(15)
            setOnLoadListener2 { viewModel?.getInsuranceRouteList(userId = CommServer.getUserServer().login.userId, userType = "1") }
            addOnItemChildClickListener { adapter, view, position ->
                val data = adapter.getItem(position) as RspUserInsuranceInfoList?
                when (view.id) {
                    R.id.tvDetail -> {
                        ImagePreviewActivity.start(
                            fragment = this@FreeEarnestMoneyMainFragmentV2,
                            imageData = getImageList(data?.insuranceUrl),
                            selectIndex = 0,
                            canDelete = false
                        )
                    }
                }
            }
            onAutoRefresh()
        }
    }

    override fun onActivityResult(requestCode: Int, resultCode: Int, data: Intent?) {
        super.onActivityResult(requestCode, resultCode, data)
        if (resultCode == RESULT_OK) {
            swipeRefreshMoreLayout.onAutoRefresh()
        }
    }

    @LiveDataMatch
    open fun getInsuranceRouteListSuccess(data: PageListV1<RspGetInsuranceRouteList>) {
        data.let {
            whetherFree.text = if (it.shortTransportFlag.isTrue) {
                "短途是否免诚意金：   是"
            } else {
                "短途是否免诚意金：   否"
            }
        }
        swipeRefreshMoreLayout.onRefreshCompale(data)
    }

    @LiveDataMatch
    open fun getInsuranceRouteListError(error: String?) {
        showDialogToast(error)
        swipeRefreshMoreLayout.onLoadMoreFail()
    }

    private fun getImageList(netUrl: String?): List<EImage> {
        val list: MutableList<EImage> = ArrayList()
        val image = EImage()
        image.netUrl = HttpConfig.getUrlImage(netUrl)
        list.add(image)
        return list
    }
}