package com.zczy.plugin.wisdom.util;

import android.graphics.Color;
import android.text.TextUtils;
import android.view.View;

import com.sfh.lib.utils.UtilSoftKeyboard;
import com.zczy.comm.data.request.ReqSendCode;
import com.zczy.comm.widget.RxTimeCountView;
import com.zczy.plugin.wisdom.req.ReqSendCodeWisdom;

/**
 * 功能描述:智运宝获取短信验证码
 *
 * <AUTHOR>
 * @company 中储智运科技股份有限公司
 * @copyright （版权）中储南京智慧物流科技有限公司所有
 * @date 2019/2/15 15:46
 */
public class WisdomVerificationCodeUtil implements View.OnClickListener, RxTimeCountView.OnTimeListenter {
    public interface IOnCallback {
        /**
         * 获取手机号码
         */
        String getPhone();

        void onClickCode(ReqSendCodeWisdom req);

        void showToast(CharSequence toast);

    }

    private IOnCallback mOnCallback;

    private RxTimeCountView tvTime;
    private RxTimeCountView tvVoiceTime;
    private View lyVoice;
    private String moduleType;

    /***
     *
     * @param moduleType @see ReqSendCode 中的常量值,模块类型
     * @param callback
     */
    public WisdomVerificationCodeUtil(String moduleType, IOnCallback callback) {
        this.moduleType = moduleType;
        this.mOnCallback = callback;
    }

    /***
     * 设置短信验证码按钮与语音验证码按钮
     * @param tvTime
     * @param tvVoiceTime
     * @param lyVoice
     * @return
     */
    public WisdomVerificationCodeUtil build(RxTimeCountView tvTime, RxTimeCountView tvVoiceTime, View lyVoice) {

        this.tvTime = tvTime;
        this.tvVoiceTime = tvVoiceTime;
        this.lyVoice = lyVoice;

        this.tvTime.setOnClickListener(this);
        this.tvVoiceTime.setOnClickListener(this);

        this.tvTime.setTextContent("获取验证码", "重新发送(%ss)");
        this.tvTime.setListenter(this);

        this.tvVoiceTime.setTextContent("获取语音验证码", "重新发送(%ss)");
        this.tvVoiceTime.setListenter(this);
        return this;
    }


    @Override
    public void onClick(View v) {

        UtilSoftKeyboard.hide(v);

        final String phone = this.mOnCallback.getPhone();
        if (TextUtils.isEmpty(phone)) {
            this.mOnCallback.showToast("请输入手机号码");
            return;
        }
        ReqSendCodeWisdom req = new ReqSendCodeWisdom();
        req.setMobile(phone);
        req.setModuleType(moduleType);

        v.setEnabled(false);
        if (tvTime == v) {

            this.lyVoice.setVisibility(View.VISIBLE);
            // 发短信code
            req.setType(ReqSendCode.TYPE_SMS);

        } else if (tvVoiceTime == v) {
            // 发语音code
            req.setType(ReqSendCode.TYPE_VOICE);
        }
        this.mOnCallback.onClickCode(req);
        v.setEnabled(true);
    }

    @Override
    public void onComplete() {
        this.setCodeError();
    }

    @Override
    public void onRun(long time) {

    }


    /***
     *
     * 发送验证码成功
     * @param type
     */
    @Deprecated
    public void onSendCodeSuccess(String type) {

        // 开始倒计时，语音-短信获取按钮不可以
        this.tvTime.setTextColor(Color.parseColor("#c2c2c2"));
        this.tvTime.setEnabled(false);

        this.tvVoiceTime.setTextColor(Color.parseColor("#c2c2c2"));
        this.tvVoiceTime.setEnabled(false);

        if (TextUtils.equals(ReqSendCode.TYPE_SMS, type)) {
            // 短信
            this.lyVoice.setVisibility(View.VISIBLE);
            this.tvTime.startInterval(60);
            this.mOnCallback.showToast("短信验证码发送成功!");

        } else {
            this.tvVoiceTime.setVisibility(View.GONE);
            this.tvVoiceTime.startInterval(60);
            this.mOnCallback.showToast("稍后将有客服电话拨打给您,告知验证码,请耐心等待...");
        }
    }

    /***
     * 发送验证码失败调用
     */
    @Deprecated
    public void setCodeError() {
        //计时结束
        this.tvTime.setTextColor(Color.parseColor("#3c75ed"));
        this.tvTime.setEnabled(true);

        this.tvVoiceTime.setTextColor(Color.parseColor("#3c75ed"));
        this.tvVoiceTime.setEnabled(true);
    }

    /***
     * 发送验证码
     * @param type
     */
    public void onSendCodeResult(boolean success, String type) {

        // 开始倒计时，语音-短信获取按钮不可以
        this.tvTime.setTextColor(success ? Color.parseColor("#c2c2c2") : Color.parseColor("#3c75ed"));
        this.tvTime.setEnabled(!success);

        this.tvVoiceTime.setTextColor(success ? Color.parseColor("#c2c2c2") : Color.parseColor("#3c75ed"));
        this.tvVoiceTime.setEnabled(!success);

        if (success) {
            //发送验证码成功
            if (TextUtils.equals(ReqSendCode.TYPE_SMS, type)) {
                // 短信
                this.lyVoice.setVisibility(View.VISIBLE);
                this.tvTime.startInterval(60);
                this.mOnCallback.showToast("短信验证码发送成功!");

            } else {
                this.tvVoiceTime.setVisibility(View.GONE);
                this.tvVoiceTime.startInterval(60);
                this.mOnCallback.showToast("稍后将有客服电话拨打给您,告知验证码,请耐心等待...");
            }
        }
    }


    /***
     * 验证码是否调用
     */
    public void setAbled(boolean abled) {
        //计时结束
        this.tvTime.setTextColor(abled ? Color.parseColor("#3c75ed") : Color.parseColor("#c2c2c2"));
        this.tvTime.setEnabled(abled);

        this.tvVoiceTime.setTextColor(abled ? Color.parseColor("#3c75ed") : Color.parseColor("#c2c2c2"));
        this.tvVoiceTime.setEnabled(abled);
    }
}
