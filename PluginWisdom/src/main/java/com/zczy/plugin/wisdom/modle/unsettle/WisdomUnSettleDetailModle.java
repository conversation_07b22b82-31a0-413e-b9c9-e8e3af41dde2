package com.zczy.plugin.wisdom.modle.unsettle;

import com.sfh.lib.mvvm.service.BaseViewModel;
import com.sfh.lib.rx.IResultSuccess;
import com.zczy.comm.http.entity.BaseRsp;
import com.zczy.plugin.wisdom.req.unsettle.ReqUnSettleDetail;
import com.zczy.plugin.wisdom.rsp.unsettle.RspUnSettleDetail;

/**
 * 功能描述: 待确认收货金额详情
 *
 * <AUTHOR>
 * @company 中储智运科技股份有限公司
 * @copyright （版权）中储南京智慧物流科技有限公司所有
 * @date 2019/1/30 9:10
 */
public class WisdomUnSettleDetailModle extends BaseViewModel {
    /**
     * 查询详情数据
     */
    public void querySettleDetail(String detailId) {
        ReqUnSettleDetail reqSettleDetail = new ReqUnSettleDetail();
        reqSettleDetail.setDetailId(detailId);
        this.execute(reqSettleDetail, new IResultSuccess<BaseRsp<RspUnSettleDetail>>() {
            @Override
            public void onSuccess(BaseRsp<RspUnSettleDetail> pageListBaseRsp) throws Exception {
                if (pageListBaseRsp.success()) {
                    setValue("onSettleDetailSuccess", pageListBaseRsp.getData());
                } else {
                    showDialogToast(pageListBaseRsp.getMsg());
                }
            }
        });

    }

}
