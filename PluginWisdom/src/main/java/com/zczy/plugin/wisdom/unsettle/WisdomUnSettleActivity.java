package com.zczy.plugin.wisdom.unsettle;

import android.app.Activity;
import android.content.Context;
import android.content.Intent;
import android.graphics.Color;
import android.os.Bundle;
import android.text.TextUtils;
import android.view.View;

import com.chad.library.adapter.base.BaseQuickAdapter;
import com.chad.library.adapter.base.listener.OnItemClickListener;
import com.sfh.lib.mvvm.annotation.LiveDataMatch;
import com.sfh.lib.ui.AbstractLifecycleActivity;
import com.zczy.comm.http.entity.PageList;
import com.zczy.comm.ui.UtilStatus;
import com.zczy.comm.widget.AppToolber;
import com.zczy.comm.widget.pulltorefresh.CommEmptyView;
import com.zczy.comm.widget.pulltorefresh.OnLoadingListener2;
import com.zczy.comm.widget.pulltorefresh.SwipeRefreshMoreLayout;
import com.zczy.plugin.wisdom.R;
import com.zczy.plugin.wisdom.date.RxDateSelect;
import com.zczy.plugin.wisdom.date.WisdomSelectDateActivity;
import com.zczy.plugin.wisdom.home.view.WisdomSelectDateLayout;
import com.zczy.plugin.wisdom.modle.unsettle.WisdomUnSettleModle;
import com.zczy.plugin.wisdom.postdata.WisdomCommonCode;
import com.zczy.plugin.wisdom.req.unsettle.ReqUnSettle;
import com.zczy.plugin.wisdom.rsp.unsettle.RspUnSettle;
import com.zczy.plugin.wisdom.unsettle.adapter.WisdomUnSettleAdapter;
import com.zczy.plugin.wisdom.util.WisdomUtils;

/**
 * 功能描述: 待确认收货金额
 *
 * <AUTHOR>
 * @company 中储智运科技股份有限公司
 * @copyright （版权）中储南京智慧物流科技有限公司所有
 * @date 2019/1/30 9:05
 */
public class WisdomUnSettleActivity extends AbstractLifecycleActivity<WisdomUnSettleModle> implements WisdomSelectDateLayout.OnDateSelectListener {


    private AppToolber appToolber;
    private SwipeRefreshMoreLayout swipeRefreshMoreLayout;
    private WisdomUnSettleAdapter mAdapter;
    private String currentDate;
    private RxDateSelect mRxDateSelect;
    private WisdomSelectDateLayout selectDate;

    public static void startContentUI(Context context) {
        Intent intent = new Intent(context, WisdomUnSettleActivity.class);
        context.startActivity(intent);

    }

    @Override
    protected void onCreate(Bundle savedInstanceState) {
        super.onCreate(savedInstanceState);
        setContentView(R.layout.wisdom_unsettle_activity);
        initView();
    }

    private void initView() {
        UtilStatus.initStatus(this, Color.WHITE);
        currentDate = WisdomUtils.INSTANCE.getCurrentMonth();
        selectDate = findViewById(R.id.select_date);
        selectDate.setTvSelectDate(currentDate);
        selectDate.setOnDateSelectListener(this);

        appToolber = (AppToolber) findViewById(R.id.appToolber);
        swipeRefreshMoreLayout = (SwipeRefreshMoreLayout) findViewById(R.id.swipeRefreshMoreLayout);
        mAdapter = new WisdomUnSettleAdapter();

        swipeRefreshMoreLayout.setAdapter(mAdapter, true);
        swipeRefreshMoreLayout.addItemDecorationSize(15);
        swipeRefreshMoreLayout.setOnLoadListener2(new OnLoadingListener2() {
            @Override
            public void onLoadUI(int nowPage) {
                ReqUnSettle reqUnSettle = new ReqUnSettle(nowPage);
                if (mRxDateSelect != null) {
                    reqUnSettle.setDelistMonth(TextUtils.isEmpty(mRxDateSelect.getSelectMonth()) ? "" : mRxDateSelect.getSelectMonth());
                    reqUnSettle.setDelistDateS(TextUtils.isEmpty(mRxDateSelect.getSelectDayStart()) ? "" : mRxDateSelect.getSelectDayStart());
                    reqUnSettle.setDelistDateE(TextUtils.isEmpty(mRxDateSelect.getSelectDayEnd()) ? "" : mRxDateSelect.getSelectDayEnd());
                } else {
                    reqUnSettle.setDelistMonth(currentDate);
                    reqUnSettle.setDelistDateS("");
                    reqUnSettle.setDelistDateE("");
                }
                getViewModel().queryUnSettle(reqUnSettle);
            }
        });
        swipeRefreshMoreLayout.setEmptyView(CommEmptyView.creatorDef(this));
        swipeRefreshMoreLayout.addOnItemListener(new OnItemClickListener() {
            @Override
            public void onSimpleItemClick(BaseQuickAdapter adapter, View view, int position) {
                //  详情
                RspUnSettle rspSettle = (RspUnSettle) adapter.getData().get(position);
                WisdomUnSettleDetailActivity.startContentUI(WisdomUnSettleActivity.this, rspSettle.getDetailId());
            }
        });
        swipeRefreshMoreLayout.onAutoRefresh();
    }

    @LiveDataMatch
    public void onUnSettleSuccess(PageList<RspUnSettle> data) {

        this.swipeRefreshMoreLayout.onRefreshCompale(data);
    }

    @LiveDataMatch
    public void onUnSettleError() {
        this.swipeRefreshMoreLayout.onLoadMoreFail();
    }


    @Override
    public void onDateSelectListener() {
        //选择日期页面
        Intent intent = new Intent(this, WisdomSelectDateActivity.class);
        startActivityForResult(intent, WisdomCommonCode.REQUEST_CODE_UNSETTLE_ACTIVITY);
    }

    @Override
    public void onCarSelectListener() {
    }

    @Override
    public void onActivityResult(int requestCode, int resultCode, Intent data) {
        super.onActivityResult(requestCode, resultCode, data);
        if (requestCode == WisdomCommonCode.REQUEST_CODE_UNSETTLE_ACTIVITY && resultCode == Activity.RESULT_OK) {
            mRxDateSelect = data.getParcelableExtra("selectDate");
            if (mRxDateSelect == null) {
                return;
            }
            String selectMonth = mRxDateSelect.getSelectMonth();
            String selectDayStart = mRxDateSelect.getSelectDayStart();
            String selectDayEnd = mRxDateSelect.getSelectDayEnd();
            if (!TextUtils.isEmpty(selectMonth)) {
                selectDate.setTvSelectDate(selectMonth);
            } else if (!TextUtils.isEmpty(selectDayStart) && !TextUtils.isEmpty(selectDayEnd)) {
                selectDate.setTvSelectDate(selectDayStart + "  至  " + selectDayEnd);
            } else if (!TextUtils.isEmpty(selectDayStart)) {
                selectDate.setTvSelectDate(selectDayStart);
            } else if (!TextUtils.isEmpty(selectDayEnd)) {
                selectDate.setTvSelectDate(selectDayEnd);
            } else {
                selectDate.setTvSelectDate(currentDate);
            }
            //选择之后刷新列表
            swipeRefreshMoreLayout.onAutoRefresh();
        }

    }
}
