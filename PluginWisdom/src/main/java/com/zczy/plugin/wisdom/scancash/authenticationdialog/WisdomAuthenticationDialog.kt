package com.zczy.plugin.wisdom.scancash.authenticationdialog

import android.annotation.SuppressLint
import android.graphics.Color
import android.os.Bundle
import android.text.TextUtils
import android.view.View
import androidx.fragment.app.Fragment
import androidx.fragment.app.FragmentActivity
import com.zczy.cargo_owner.libcomm.widget.authenticationdialog.WisdomVerifyCodeView
import com.zczy.comm.data.entity.ELogin
import com.zczy.comm.ui.BaseDialog
import com.zczy.plugin.wisdom.R
import io.reactivex.Observable
import io.reactivex.Observer
import io.reactivex.android.schedulers.AndroidSchedulers
import io.reactivex.disposables.Disposable
import kotlinx.android.synthetic.main.wisdom_authentication_dialog.*
import java.util.concurrent.TimeUnit

/**
 * author : Ssp
 * date   : 2019/9/2 16:23
 * desc   : 新设备认证
 */
class WisdomAuthenticationDialog : BaseDialog() {

    private var mListener: Listener? = null
    private var disposable: Disposable? = null
    private lateinit var login: ELogin
    override fun bindView(view: View, bundle: Bundle?) {
        initView()
    }

    override fun getDialogTag(): String = "WisdomAuthenticationDialog"

    override fun getDialogLayout(): Int = R.layout.wisdom_authentication_dialog
    override fun isCancelableOnTouchOutside(): Boolean {
        return false
    }

    override fun isCancelable(): Boolean {
        return false
    }
    private val onClickListener = View.OnClickListener { v ->
        when (v.id) {
            R.id.tv_msg_code -> {
                mListener?.onReSendCode(login)
                verify_code_view?.clearText()
                countTime()
            }

            R.id.img_close -> {
                dismiss()
            }

            R.id.btn_commit -> {
                if (mListener != null) {
                    verify_code_view?.let {
                        mListener?.onCommit(login, it.inputValue)
                    }
                }
            }
        }
    }

    fun setLogin(login: ELogin) {
        this.login = login
    }

    override fun dismiss() {
        super.dismiss()
        try {
            if (disposable != null) {
                disposable?.let {
                    if (!it.isDisposed) {
                        it.dispose()
                    }
                }

            }
        } catch (e: Exception) {
            e.printStackTrace()
        }

    }

    fun showDialog(activity: FragmentActivity) {
        show(activity)
        if (!TextUtils.isEmpty(verify_code_view?.inputValue)) {
            verify_code_view?.clearText()
        }
        countTime()
    }

    fun showDialog(activity: Fragment) {
        show(activity)
        if (!TextUtils.isEmpty(verify_code_view?.inputValue)) {
            verify_code_view?.clearText()
        }
        countTime()
    }

    private fun countTime() {
        if (disposable != null) {
            disposable?.dispose()
        }
        Observable.interval(0, 1, TimeUnit.SECONDS)
            .subscribeOn(AndroidSchedulers.mainThread())
            .observeOn(AndroidSchedulers.mainThread())
            .map { increaseTime -> COUNT_TIME - increaseTime.toInt() }
            .take((COUNT_TIME + 1).toLong())
            .subscribe(object : Observer<Int> {

                override fun onError(e: Throwable) {

                }

                override fun onComplete() {
                    tv_msg_code?.text = "重新获取短信验证码"
                    tv_msg_code?.isEnabled = true
                    tv_msg_code?.setTextColor(Color.parseColor("#3C75ED"))
                }

                @SuppressLint("SetTextI18n")
                override fun onSubscribe(d: Disposable) {
                    disposable = d
                    tv_msg_code?.text = COUNT_TIME.toString() + "秒后重新获取短信验证码"
                    tv_msg_code?.isEnabled = false
                    tv_msg_code?.setTextColor(Color.parseColor("#999999"))
                }

                @SuppressLint("SetTextI18n")
                override fun onNext(integer: Int) {
                    tv_msg_code?.text = "(" + integer + "s)后重新获取短信验证码"
                }
            })
    }

    private fun initView() {
        val imgClose = img_close
        imgClose.setOnClickListener(onClickListener)
        verify_code_view?.setListener(object : WisdomVerifyCodeView.Listener {
            override fun autoCommit(inputText: String?, isFull: Boolean) {
                btn_commit.isEnabled = isFull
            }
        })
        btn_commit.setOnClickListener(onClickListener)
        btn_commit.isEnabled = false
        tv_msg_code?.setOnClickListener(onClickListener)
    }

    fun setListener(listener: Listener): WisdomAuthenticationDialog {
        mListener = listener
        return this
    }

    interface Listener {
        /**
         * 点击重新发送验证码
         */
        fun onReSendCode(data: ELogin)

        /**
         * 点击确定
         *
         * @param code 4位验证码
         */
        fun onCommit(data: ELogin, code: String)

        /**
         * 主动关闭当前弹框
         */
        fun onClose()
    }

    companion object {
        private val COUNT_TIME = 120
    }
}
