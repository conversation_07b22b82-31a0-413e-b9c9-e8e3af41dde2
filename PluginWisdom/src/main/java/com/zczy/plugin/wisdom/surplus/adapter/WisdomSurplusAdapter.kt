package com.zczy.plugin.wisdom.surplus.adapter

import com.chad.library.adapter.base.BaseQuickAdapter
import com.chad.library.adapter.base.BaseViewHolder
import com.zczy.plugin.wisdom.R
import com.zczy.plugin.wisdom.surplus.req.RspQueryFinanceDepositSubList

/**
 * 功能描述: 奖励明细
 * <AUTHOR>
 * @date 2022/10/28-15:41
 */

class WisdomSurplusAdapter : BaseQuickAdapter<RspQueryFinanceDepositSubList, BaseViewHolder>(R.layout.wisdom_surplus_item) {
    override fun convert(helper: BaseViewHolder?, item: RspQueryFinanceDepositSubList?) {
        helper?.apply {
            item?.let {
                setText(R.id.tvMoney, it.depositMoney)
                setText(R.id.tvTime, it.createTime)
                setText(R.id.tvType, it.depositType)
                setText(R.id.tvRemark, it.userRemark)
                when (it.state) {
                    "审核中" -> {
                        setImageResource(R.id.iv, R.drawable.icon_surplus_state2)
                        setGone(R.id.tvRemarkTitle, false)
                        setGone(R.id.tvRemark, false)
                    }
                    "审核驳回" -> {
                        setImageResource(R.id.iv, R.drawable.icon_surplus_state3)
                        setGone(R.id.tvRemarkTitle, true)
                        setGone(R.id.tvRemark, true)
                    }
                    "银行付款中" -> {
                        setImageResource(R.id.iv, R.drawable.icon_surplus_state5)
                        setGone(R.id.tvRemarkTitle, false)
                        setGone(R.id.tvRemark, false)
                    }
                    "转出成功" -> {
                        setImageResource(R.id.iv, R.drawable.icon_surplus_state4)
                        setGone(R.id.tvRemarkTitle, false)
                        setGone(R.id.tvRemark, false)
                    }
                    "转出失败" -> {
                        setImageResource(R.id.iv, R.drawable.icon_surplus_state1)
                        setGone(R.id.tvRemarkTitle, false)
                        setGone(R.id.tvRemark, false)
                    }
                }

            }
        }
    }

}