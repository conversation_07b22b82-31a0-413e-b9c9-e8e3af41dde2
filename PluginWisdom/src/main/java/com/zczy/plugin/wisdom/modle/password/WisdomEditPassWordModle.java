package com.zczy.plugin.wisdom.modle.password;

import com.sfh.lib.exception.HandleException;
import com.sfh.lib.mvvm.service.BaseViewModel;
import com.sfh.lib.rx.IResult;
import com.zczy.comm.http.entity.BaseRsp;
import com.zczy.comm.http.entity.ResultData;
import com.zczy.plugin.wisdom.req.password.ReqCheckPwd;
import com.zczy.plugin.wisdom.req.password.ReqSetPwd;
import com.zczy.plugin.wisdom.rsp.home.RspCheckToken;

/**
 * 功能描述: 设置支付密码
 *
 * <AUTHOR>
 * @company 中储智运科技股份有限公司
 * @copyright （版权）中储南京智慧物流科技有限公司所有
 * @date 2018/11/19 19:16
 */
public class WisdomEditPassWordModle extends BaseViewModel {
    /**
     * 设置支付密码
     */
    public void setPassWord(ReqSetPwd reqSetPwd) {
        showLoading(false);
        this.execute(reqSetPwd, new IResult<BaseRsp<ResultData>>() {
            @Override
            public void onSuccess(BaseRsp<ResultData> baseRsp) throws Exception {
                hideLoading();
                if (baseRsp.success()) {
                    setValue("onSetPassWordSuccess", baseRsp.getMsg());
                } else {
                    setValue("onSetPassWordError", baseRsp.getMsg());
                }
            }

            @Override
            public void onFail(HandleException e) {
                hideLoading();
                setValue("onSetPassWordError", e.getMsg());
            }
        });
    }

    /**
     * 校验原密码 验证身份
     */
    public void checkPwd(ReqCheckPwd req) {
        showLoading(false);
        this.execute(req, new IResult<BaseRsp<RspCheckToken>>() {
            @Override
            public void onSuccess(BaseRsp<RspCheckToken> baseRsp) throws Exception {
                hideLoading();
                if (baseRsp.success()) {
                    setValue("onCheckPwdSuccess", baseRsp.getData());
                } else {
                    setValue("onCheckPwdError");
                    showDialogToast(baseRsp.getMsg());
                }
            }

            @Override
            public void onFail(HandleException e) {
                hideLoading();
                setValue("onCheckPwdError");
                showDialogToast(e.getMsg());
            }
        });
    }

}
