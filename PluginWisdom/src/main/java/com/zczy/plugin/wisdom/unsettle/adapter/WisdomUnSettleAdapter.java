package com.zczy.plugin.wisdom.unsettle.adapter;

import android.text.TextUtils;

import com.chad.library.adapter.base.BaseQuickAdapter;
import com.chad.library.adapter.base.BaseViewHolder;
import com.zczy.plugin.wisdom.R;
import com.zczy.plugin.wisdom.rsp.unsettle.RspUnSettle;

/**
 * 功能描述: 待确认收货金额
 *
 * <AUTHOR>
 * @company 中储智运科技股份有限公司
 * @copyright （版权）中储南京智慧物流科技有限公司所有
 * @date 2019/1/29 18:53
 */

public class WisdomUnSettleAdapter extends BaseQuickAdapter<RspUnSettle, BaseViewHolder> {

    public WisdomUnSettleAdapter() {
        super(R.layout.wisdom_unsettle_item);
    }

    @Override
    protected void convert(BaseViewHolder helper, RspUnSettle item) {
        //设置文字
        helper.setText(R.id.tv_start_address, item.getDespatchCity() + item.getDespatchDis())
                .setText(R.id.tv_end_address, item.getDeliverCity() + item.getDeliverDis())
                .setText(R.id.tv_content, item.getCargoName() + " | " + item.getWeight() + item.getCargoCategory())
                .setText(R.id.tv_time, item.getReceiveTime())
                .setText(R.id.tv_money, item.getReceiveMoney() + "元");

        if (TextUtils.equals("0", item.getOrderModel())) {
            helper.setBackgroundRes(R.id.iv_order_pattern, R.drawable.wisdom_grab_sheet);
        } else {
            helper.setBackgroundRes(R.id.iv_order_pattern, R.drawable.wisdom_bidding_price);
        }
    }

}
