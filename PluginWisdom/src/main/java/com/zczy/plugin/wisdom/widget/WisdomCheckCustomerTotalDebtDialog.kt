package com.zczy.plugin.wisdom.widget

import android.os.Bundle
import android.view.View
import androidx.recyclerview.widget.LinearLayoutManager
import com.chad.library.adapter.base.BaseQuickAdapter
import com.chad.library.adapter.base.BaseViewHolder
import com.zczy.comm.ui.BaseDialog
import com.zczy.plugin.wisdom.R
import com.zczy.plugin.wisdom.req.RspCheckCustomerTotalDebt
import kotlinx.android.synthetic.main.wisdom_check_customer_total_dialog.recyclerView
import kotlinx.android.synthetic.main.wisdom_check_customer_total_dialog.tvKnow

/**
 * 类描述：智运账本欠款无法转出
 * 作者：ssp
 * 创建时间：2024/5/20
 */
class WisdomCheckCustomerTotalDebtDialog(private var dataList: MutableList<RspCheckCustomerTotalDebt>?) : BaseDialog() {
    private val mAdapter = WisdomCheckCustomerTotalDebtAdapter()

    companion object {
        @JvmStatic
        fun instance(dataList: MutableList<RspCheckCustomerTotalDebt>?): WisdomCheckCustomerTotalDebtDialog {
            return WisdomCheckCustomerTotalDebtDialog(dataList)
        }
    }

    override fun bindView(view: View, bundle: Bundle?) {
        recyclerView.apply {
            layoutManager = LinearLayoutManager(context)
            adapter = mAdapter
        }
        mAdapter.setNewData(dataList)
        tvKnow.setOnClickListener {
            dismiss()
        }
    }

    override fun getDialogTag(): String = "WisdomCheckCustomerTotalDebtDialog"
    override fun getDialogStyle(): Int {
        return R.style.base_common_toast_dialog_v1
    }

    override fun getDialogType(): DialogType = DialogType.full

    override fun getDialogLayout(): Int {
        return R.layout.wisdom_check_customer_total_dialog
    }

    inner class WisdomCheckCustomerTotalDebtAdapter : BaseQuickAdapter<RspCheckCustomerTotalDebt, BaseViewHolder>(R.layout.wisdom_check_customer_total_item) {
        override fun convert(helper: BaseViewHolder, item: RspCheckCustomerTotalDebt) {
            helper.setText(R.id.tv1, item.customerName)
            helper.setText(R.id.tv2, item.customerName)
            helper.setText(R.id.tv3, item.subsidiaryName)
            helper.setText(R.id.tv4, item.debtMoney)
        }
    }
}