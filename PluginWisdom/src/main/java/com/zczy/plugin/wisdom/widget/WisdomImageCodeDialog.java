package com.zczy.plugin.wisdom.widget;

import android.app.Dialog;
import android.content.Context;
import android.os.Bundle;
import android.text.TextUtils;
import android.view.View;
import android.widget.EditText;
import android.widget.ImageView;
import android.widget.TextView;
import android.widget.Toast;

import com.zczy.cargo_owner.libcomm.config.HttpConfig;
import com.zczy.comm.utils.imgloader.ImgUtil;
import com.zczy.comm.utils.imgloader.Options;
import com.zczy.plugin.wisdom.R;

/**
 * 功能描述: 图形验证码
 *
 * <AUTHOR>
 * @company 中储智运科技股份有限公司
 * @copyright （版权）中储南京智慧物流科技有限公司所有
 * @date 2019/2/21 13:40
 */

public class WisdomImageCodeDialog extends Dialog implements View.OnClickListener {

    public interface CodeCallback {
        void onClickCode(WisdomImageCodeDialog dialog, String code);
    }

    ImageView ivColse;
    EditText etCode;
    ImageView ivCode;
    ImageView ivRefre;
    TextView tvOK;

    CodeCallback codeCallback;

    String phone;

    public WisdomImageCodeDialog(Context context, String phone, CodeCallback codeCallback) {

        super(context, R.style.CommDialog);
        this.phone = phone;
        this.codeCallback = codeCallback;
    }


    @Override
    protected void onCreate(Bundle savedInstanceState) {
        super.onCreate(savedInstanceState);
        this.setContentView(R.layout.wisdom_widget_image_code);
        this.setCanceledOnTouchOutside(false);

        this.ivColse = findViewById(R.id.ivColse);
        this.ivCode = findViewById(R.id.ivCode);
        this.ivRefre = findViewById(R.id.ivRefre);
        this.tvOK = findViewById(R.id.tvOK);
        this.etCode = findViewById(R.id.etCode);

        this.ivColse.setOnClickListener(this);
        this.ivCode.setOnClickListener(this);
        this.ivRefre.setOnClickListener(this);
        this.tvOK.setOnClickListener(this);

        if (TextUtils.isEmpty(phone)) {
            Toast.makeText(getContext(), "请输入手机号码", Toast.LENGTH_SHORT).show();
            dismiss();
            return;
        }
    }

    @Override
    public void show() {
        super.show();
        this.showPic();
    }

    @Override
    public void onClick(View v) {
        //关闭
        if (v == ivColse) {
            this.dismiss();
            return;
        }

        //刷新
        if (v == ivCode || ivRefre == v) {
            v.setEnabled(false);
            this.showPic();
            v.setEnabled(true);
            return;
        }


        if (v == tvOK) {

            String code = etCode.getText().toString().trim();
            if (TextUtils.isEmpty(code)) {
                Toast.makeText(getContext(), "请输入验证码", Toast.LENGTH_SHORT).show();
                return;
            }
            v.setEnabled(false);
            this.dismiss();
            v.setEnabled(true);
            if (codeCallback != null) {
                codeCallback.onClickCode(this, code);
            }
        }
    }

    private void showPic() {
        if (this.etCode != null) {
            this.etCode.setText("");
        }
        if (this.ivCode != null) {
            ImgUtil.loadUrl(this.ivCode, HttpConfig.getUrl("mms-app/mms/verifyCode/getImageVerifyCode?mobile=" + phone + "&time=" + System.currentTimeMillis()), Options.creator().setSkipMemoryCache(true));
        }
    }
}
