package com.zczy.plugin.wisdom.bond.adapter

import android.text.TextUtils
import com.chad.library.adapter.base.BaseQuickAdapter
import com.chad.library.adapter.base.BaseViewHolder
import com.zczy.plugin.wisdom.R
import com.zczy.plugin.wisdom.bond.req.RspWisdomBondAccount

/**
 *  user: ssp
 *  time: 2021/6/24 14:25
 *  desc: 冻结金额明细
 */

class WisdomBondListAdapter : BaseQuickAdapter<RspWisdomBondAccount, BaseViewHolder>(R.layout.wisdom_bond_list_item) {

    override fun convert(helper: BaseViewHolder, item: RspWisdomBondAccount) {
        item.apply {
            //设置文字
            helper.setText(R.id.tv_title, title)
                    .setText(R.id.tv_abstract, if (TextUtils.isEmpty(abstractRemark)) "摘要：--" else "摘要：$abstractRemark")
                    .setText(R.id.tv_time, createTime)
                    .setText(R.id.tv_money, money + "元")
        }

    }
}