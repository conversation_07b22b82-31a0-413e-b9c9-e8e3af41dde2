package com.zczy.plugin.wisdom.budget.adapter

import android.text.TextUtils
import com.chad.library.adapter.base.BaseQuickAdapter
import com.chad.library.adapter.base.BaseViewHolder
import com.zczy.plugin.wisdom.R
import com.zczy.plugin.wisdom.req.budget.RspBudgetIncome

/**
 * 功能描述: 收支明细 - 收入
 * <AUTHOR>
 * @date 2022/8/18-10:34
 */

class WisdomBudgetIncomeAdapter : BaseQuickAdapter<RspBudgetIncome, BaseViewHolder>(R.layout.wisdom_budget_income_item) {
    override fun convert(helper: BaseViewHolder, item: RspBudgetIncome) {
        //设置背景图
        when {
            TextUtils.equals("1", item.ordType) -> {
                helper.setBackgroundRes(R.id.iv_icon, R.drawable.wisdom_budget_recharge)
            }
            TextUtils.equals("2", item.ordType) -> {
                helper.setBackgroundRes(R.id.iv_icon, R.drawable.wisdom_budget_cash)
            }
            TextUtils.equals("3", item.ordType) -> {
                helper.setBackgroundRes(R.id.iv_icon, R.drawable.wisdom_budget_settle)
            }
            TextUtils.equals("16", item.ordType) -> {
                helper.setBackgroundRes(R.id.iv_icon, R.drawable.wisdom_budget_red_envelopes)
            }
            TextUtils.equals("8", item.ordType) -> {
                helper.setBackgroundRes(R.id.iv_icon, R.drawable.wisdom_budget_receivables)
            }
            else -> {
                helper.setBackgroundRes(R.id.iv_icon, R.drawable.wisdom_budget_deposit)
            }
        }

        //设置文字
        helper.setText(R.id.tv_title, item.title)
            .setText(R.id.tv_time, item.createTime)
            .setText(
                R.id.tv_plate_number, when {
                    TextUtils.isEmpty(item.plateNumber) -> {
                        ""
                    }
                    else -> {
                        item.plateNumber
                    }
                }
            )
            .setText(R.id.tv_money, item.money)
            .setText(R.id.tv_status, item.tradingState)
    }
}