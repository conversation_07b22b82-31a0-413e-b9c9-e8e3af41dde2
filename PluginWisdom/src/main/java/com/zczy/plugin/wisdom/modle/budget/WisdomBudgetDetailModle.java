package com.zczy.plugin.wisdom.modle.budget;

import com.sfh.lib.mvvm.service.BaseViewModel;
import com.zczy.plugin.wisdom.req.budget.ReqOrdDetail;

/**
 * 功能描述: 获取结算金额详情
 *
 * <AUTHOR>
 * @company 中储智运科技股份有限公司
 * @copyright （版权）中储南京智慧物流科技有限公司所有
 * @date 2019/1/30 9:10
 */
public class WisdomBudgetDetailModle extends BaseViewModel {
    /**
     * 查询详情数据
     */
    public void querySettleDetail(String ordId, String ordType,String fundMode) {
        ReqOrdDetail reqSettleDetail = new ReqOrdDetail();
        reqSettleDetail.setOrdId(ordId);
        reqSettleDetail.setOrdType(ordType);
        reqSettleDetail.setFundMode(fundMode);
        this.execute(reqSettleDetail, pageListBaseRsp -> {
            if (pageListBaseRsp.success()) {
                setValue("onBudgetDetailSuccess", pageListBaseRsp.getData());
            } else {
                showDialogToast(pageListBaseRsp.getMsg());
            }
        });

    }

}
