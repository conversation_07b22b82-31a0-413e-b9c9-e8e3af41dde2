package com.zczy.plugin.wisdom.modle.settle

import com.sfh.lib.mvvm.service.BaseViewModel
import com.zczy.plugin.wisdom.req.settle.ReqSettleDetail

/**
 * 功能描述: 获取结算金额详情
 *
 * <AUTHOR>
 * @company 中储智运科技股份有限公司
 * @copyright （版权）中储南京智慧物流科技有限公司所有
 * @date 2019/1/30 9:10
 */
class WisdomSettleDetailModle : BaseViewModel() {
    /**
     * 查询详情数据
     */
    fun querySettleDetail(orderId: String) {
        val reqSettleDetail = ReqSettleDetail()
        reqSettleDetail.orderId = orderId
        this.execute(reqSettleDetail) { pageListBaseRsp ->
            if (pageListBaseRsp.success()) {
                setValue("onSettleDetailSuccess", pageListBaseRsp.data)
            } else {
                showDialogToast(pageListBaseRsp.msg)
            }
        }

    }

}
