package com.zczy.plugin.wisdom.moreaccount.adapter

import android.widget.ImageView
import com.chad.library.adapter.base.BaseQuickAdapter
import com.chad.library.adapter.base.BaseViewHolder
import com.zczy.plugin.wisdom.R
import com.zczy.plugin.wisdom.moreaccount.req.RspWisdomAppropriateMoney

/**
 * author : Ssp
 * date   : 2020/3/5 16:03
 * desc   : 资金划拨
 */
class WisdomAppropriateMoneyAdapter : BaseQuickAdapter<RspWisdomAppropriateMoney, BaseViewHolder>(R.layout.wisdom_appropriate_money_item) {
    //刷新当前位置UI
    private var priorPosition = -1

    /**  获取当前选择的bookId*/
    val rspWisdomCutAccount: RspWisdomAppropriateMoney
        get() {
            if (priorPosition == -1) {
                return RspWisdomAppropriateMoney()
            }
            return mData[priorPosition]
        }

    override fun convert(helper: <PERSON>ViewHolder, item: RspWisdomAppropriateMoney) {
        helper.setText(R.id.tvShortName, item.shortName)
        notifyPositionView(helper, item.booleanSelect)
    }

    fun reFreshItem(mPosition: Int) {
        val rspWisdomCutAccount1 = mData[mPosition]
        if (priorPosition == -1) {
            //第一次点击 更改当前点击item状态
            rspWisdomCutAccount1.booleanSelect = true
            notifyItemChanged(mPosition, rspWisdomCutAccount1)
        } else if (priorPosition != mPosition) {
            //更改之前选择item状态
            val rspWisdomCutAccount2 = mData[priorPosition]
            rspWisdomCutAccount2.booleanSelect = false
            notifyItemChanged(priorPosition, rspWisdomCutAccount2)
            //更改当前点击item状态
            rspWisdomCutAccount1.booleanSelect = true
            notifyItemChanged(mPosition, rspWisdomCutAccount1)
        }
        priorPosition = mPosition
    }

    private fun notifyPositionView(helper: BaseViewHolder, selected: Boolean) {
        val checkView = helper.getView(R.id.check_view) as ImageView?
        checkView?.isSelected = selected
    }

}

