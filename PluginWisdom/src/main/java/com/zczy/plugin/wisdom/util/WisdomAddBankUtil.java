package com.zczy.plugin.wisdom.util;

import android.content.Context;

import com.zczy.comm.CommServer;
import com.zczy.comm.data.entity.ELogin;
import com.zczy.comm.data.role.IRelation;
import com.zczy.plugin.wisdom.bank.WisdomAddPublicBankActivity;

/**
 * <AUTHOR>
 * @description (添加银行卡跳转)
 * @company 中储智运科技股份有限公司
 * @copyright （版权）中储南京智慧物流科技有限公司所有
 * @time 2019/4/23  19:17
 */
public class WisdomAddBankUtil {
    /**
     * 控制添加银行卡
     */
    public static void addBank(Context context) {
        ELogin login = CommServer.getUserServer().getLogin();
        if (login == null) {
            return;
        }
        IRelation relation = login.getRelation();
        if (relation == null) {
            return;
        }
        WisdomAddPublicBankActivity.startContentUI(context);
    }
}
