package com.zczy.plugin.wisdom.budget

import android.app.Activity
import android.content.Intent
import android.os.Bundle
import android.text.TextUtils
import android.view.View
import com.sfh.lib.mvvm.annotation.LiveDataMatch
import com.sfh.lib.ui.dialog.DialogBuilder
import com.zczy.comm.ui.BaseFragment
import com.zczy.comm.utils.ex.YYYY_MM_DD
import com.zczy.comm.utils.ex.getFormatTime
import com.zczy.comm.widget.pulltorefresh.CommEmptyView
import com.zczy.plugin.wisdom.R
import com.zczy.plugin.wisdom.budget.adapter.WisdomBudgetIncomeAdapter
import com.zczy.plugin.wisdom.date.RxDateSelect
import com.zczy.plugin.wisdom.date.WisdomSelectDateActivity
import com.zczy.plugin.wisdom.home.view.WisdomSelectDateLayout.OnDateSelectListener
import com.zczy.plugin.wisdom.modle.budget.WisdomBudgetListModel
import com.zczy.plugin.wisdom.postdata.WisdomCommonCode
import com.zczy.plugin.wisdom.req.budget.BudgetIncomeList
import com.zczy.plugin.wisdom.req.budget.ReqQueryBudget
import com.zczy.plugin.wisdom.req.budget.RspBudgetIncome
import kotlinx.android.synthetic.main.wisdom_budget_income_fragment.oilRecordListRefreshMoreLayout
import kotlinx.android.synthetic.main.wisdom_budget_income_fragment.selectDate
import java.util.Calendar

/**
 * 功能描述: 收支明细 收入
 * <AUTHOR>
 * @date 2022/8/18-10:17
 */

class WisdomBudgetIncomeFragment : BaseFragment<WisdomBudgetListModel?>(), OnDateSelectListener {
    private var mAdapter: WisdomBudgetIncomeAdapter = WisdomBudgetIncomeAdapter()
    private var mRxDateSelect: RxDateSelect = RxDateSelect()
    private var plateNumber: String? = null
    private var fundMode: String? = null
    private var bookNo: String? = null
    private var scrollId: String? = null
    override fun getLayout(): Int {
        return R.layout.wisdom_budget_income_fragment
    }

    override fun initData() {
        bookNo = arguments?.getString("bookNo")
        fundMode = arguments?.getString("fundMode")
        mRxDateSelect.selectDayStart = "${Calendar.getInstance().time.getFormatTime("yyyy")}-01-01"
        mRxDateSelect.selectDayEnd = Calendar.getInstance().time.getFormatTime(YYYY_MM_DD)
        selectDate.setTvSelectDate("${mRxDateSelect.selectDayStart}至${mRxDateSelect.selectDayEnd}")
        selectDate.setOnDateSelectListener(this)
        oilRecordListRefreshMoreLayout.setAdapter(mAdapter, true)
        oilRecordListRefreshMoreLayout.setEmptyView(CommEmptyView.creatorDef(activity))
        oilRecordListRefreshMoreLayout.addOnItemListener { adapter, _, position ->
            val rspBudgetIncome = adapter.data[position] as RspBudgetIncome
            val ordType = rspBudgetIncome.ordType
            val ordId = rspBudgetIncome.ordId
            //1：充值 2：提现
            when {
                TextUtils.equals("1", ordType) -> {
                    //充值
                    WisdomRechargeDetailActivity.startContentUI(activity, ordId, ordType, fundMode)
                }

                TextUtils.equals("2", ordType) -> {
                    //提现
                    WisdomCashDetailActivity.startContentUI(activity, ordId, ordType, fundMode)
                }

                else -> {
                    WisdomBudgetDetailActivity.startContentUI(activity, ordId, ordType, fundMode)
                }
            }
        }
        oilRecordListRefreshMoreLayout.setOnLoadListener2 {
            val reqQueryBudget = ReqQueryBudget()
            reqQueryBudget.bookNo = bookNo
            reqQueryBudget.fundMode = fundMode
            reqQueryBudget.financeType = "1"
            reqQueryBudget.financeMonth = when {
                TextUtils.isEmpty(mRxDateSelect.selectMonth) -> {
                    null
                }

                else -> {
                    mRxDateSelect.selectMonth
                }
            }
            reqQueryBudget.financeDateS = when {
                TextUtils.isEmpty(mRxDateSelect.selectDayStart) -> {
                    null
                }

                else -> {
                    mRxDateSelect.selectDayStart
                }
            }
            reqQueryBudget.financeDateE = when {
                TextUtils.isEmpty(mRxDateSelect.selectDayEnd) -> {
                    null
                }

                else -> {
                    mRxDateSelect.selectDayEnd
                }
            }
            reqQueryBudget.orderId = plateNumber

            if (it == 1) {
                reqQueryBudget.scrollId = null
            } else {
                reqQueryBudget.scrollId = scrollId
            }
            viewModel?.queryBudget(reqQueryBudget = reqQueryBudget, nowPage = it)

        }
        oilRecordListRefreshMoreLayout.onAutoRefresh()
    }

    @LiveDataMatch
    open fun getBudgetRecordListError(error: String?) {
        oilRecordListRefreshMoreLayout.onLoadMoreFail()
    }

    @LiveDataMatch
    open fun getBudgetRecordListSuccess(data: BudgetIncomeList<RspBudgetIncome>?) {
        data?.let {
            scrollId = it.scrollId
            if (TextUtils.equals(it.scrollId, "-1")) {
                //无更多数据
                oilRecordListRefreshMoreLayout.onRefreshCompale(it.nowPage, it.nowPage, data.rootArray)
            } else {
                if ((it.rootArray?.size ?: 0) < it.pageSize) {
                    //无更多数据
                    oilRecordListRefreshMoreLayout.onRefreshCompale(it.nowPage, it.nowPage, data.rootArray)
                } else {
                    oilRecordListRefreshMoreLayout.onRefreshCompale(it.nowPage, it.nowPage + 1, data.rootArray)
                }
            }
        }
    }

    override fun onDateSelectListener() {
        //选择日期页面
        val intent = Intent(activity, WisdomSelectDateActivity::class.java)
        intent.putExtra(WisdomSelectDateActivity.REMIND_TXT, "请按照自然年度查询")
        startActivityForResult(intent, WisdomCommonCode.REQUEST_CODE_INCOME)
    }

    override fun onCarSelectListener() {

    }

    override fun onActivityResult(requestCode: Int, resultCode: Int, data: Intent?) {
        super.onActivityResult(requestCode, resultCode, data)
        if (requestCode == WisdomCommonCode.REQUEST_CODE_INCOME && resultCode == Activity.RESULT_OK) {
            val time = data?.getParcelableExtra<RxDateSelect>("selectDate") ?: return
            mRxDateSelect = time
            when {
                !TextUtils.isEmpty(mRxDateSelect.selectMonth) -> {
                    selectDate.setTvSelectDate(mRxDateSelect.selectMonth)
                    mRxDateSelect.selectDayStart = null
                    mRxDateSelect.selectDayEnd = null
                }

                !TextUtils.isEmpty(mRxDateSelect.selectDayStart) && !TextUtils.isEmpty(mRxDateSelect.selectDayEnd) -> {
                    if (!TextUtils.equals(mRxDateSelect.selectDayStart?.substring(0, 4), mRxDateSelect.selectDayEnd?.substring(0, 4))) {
                        val dialogBuilder = DialogBuilder()
                        dialogBuilder.message = "开始时间和结束时间需在同一个自然年度"
                        dialogBuilder.isHideCancel = true
                        dialogBuilder.setOKText("我知道了")
                        showDialog(dialogBuilder)
                        return
                    }
                    mRxDateSelect.selectMonth = null
                    selectDate.setTvSelectDate("${mRxDateSelect.selectDayStart}  至  ${mRxDateSelect.selectDayEnd}")
                }

                !TextUtils.isEmpty(mRxDateSelect.selectDayStart) -> {
                    mRxDateSelect.selectMonth = null
                    selectDate.setTvSelectDate(mRxDateSelect.selectDayStart)
                }

                !TextUtils.isEmpty(mRxDateSelect.selectDayEnd) -> {
                    mRxDateSelect.selectMonth = null
                    selectDate.setTvSelectDate(mRxDateSelect.selectDayEnd)
                }
            }
            //选择之后刷新列表
            oilRecordListRefreshMoreLayout.onAutoRefresh()
        }
    }

    /**
     * 设置车牌号码
     *
     * @param plateNumber
     */
    fun setPlateNumber(plateNumber: String) {
        this.plateNumber = plateNumber
        //选择之后刷新列表
        oilRecordListRefreshMoreLayout.onAutoRefresh()
    }

    companion object {
        @JvmStatic
        fun newFragmnet(bookNo: String?, fundMode: String?): WisdomBudgetIncomeFragment {
            val fragment = WisdomBudgetIncomeFragment()
            val bundle = Bundle()
            bundle.putString("bookNo", bookNo)
            bundle.putString("fundMode", fundMode)
            fragment.arguments = bundle
            return fragment
        }
    }

    override fun bindView(view: View, bundle: Bundle?) {

    }
}