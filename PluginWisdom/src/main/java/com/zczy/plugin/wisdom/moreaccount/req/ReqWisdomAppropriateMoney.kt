package com.zczy.plugin.wisdom.moreaccount.req

import com.zczy.comm.http.entity.BaseRsp
import com.zczy.plugin.wisdom.BaseWisdomRequest

/**
 *    author : Ssp
 *    e-mail : <EMAIL>
 *    date   : 2020/3/514:45
 *    desc   : 划拨资金
 *    version: 1.0
 */
class ReqWisdomAppropriateMoney(
        var bookNo: String = "",
        var subsidiaryId: String = "",
        var fundMode: String = "")
    : BaseWisdomRequest<BaseRsp<PageArrayList<RspWisdomAppropriateMoney>>>("pps-app/customerCapital/queryTransferAccountBooks")

data class RspWisdomAppropriateMoney(
        /** 账户ID */
        var accountId: String = "",
        /** 交易方式 0-现结 1-账期 2-背靠背*/
        var transactionType: String = "",
        /**  可用金额 */
        var depositMoney: String = "",
        /** 冻结金额 */
        var freezeMoney: String = "",
        /** 1-汽运 2-船运 3-多式联运 */
        var fundMode: String = "",
        /** 所属平台ID */
        var bookNo: String = "",
        /** 所属平台名称 */
        var subsidiaryName: String = "",
        /** 账本id */
        var bookId: String = "",
        /** 简称 此处简称为业务类型+签约主体 */
        var shortName: String = "",
        /** 是否选中当前*/
        var booleanSelect: Boolean = false
)
