package com.zczy.plugin.wisdom.modle.budget;

import com.sfh.lib.mvvm.service.BaseViewModel;
import com.zczy.plugin.wisdom.req.budget.ReqOrdDetail;

/**
 * 提现详情
 * author: SongShuangPeng
 * company: 南京中储智慧物流
 * date:   On 2019/4/15 17:11
 */
public class WisdomCashDetailModle extends BaseViewModel {
    /**
     * 查询详情数据
     */
    public void querySettleBondDetail(ReqOrdDetail reqCashDetail) {

        this.execute(true, reqCashDetail, pageListBaseRsp -> {
            hideLoading();
            if (pageListBaseRsp.success()) {
                setValue("onSettleBondDetailSuccess", pageListBaseRsp.getData());
            } else {
                showDialogToast(pageListBaseRsp.getMsg());
            }
        });

    }

}
