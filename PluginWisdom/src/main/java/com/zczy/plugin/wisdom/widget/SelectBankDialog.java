package com.zczy.plugin.wisdom.widget;

import android.content.Context;
import android.graphics.drawable.ColorDrawable;
import androidx.recyclerview.widget.LinearLayoutManager;
import androidx.recyclerview.widget.RecyclerView;
import android.view.Gravity;
import android.view.LayoutInflater;
import android.view.View;
import android.view.ViewGroup;
import android.widget.PopupWindow;
import android.widget.TextView;

import com.zczy.plugin.wisdom.R;
import com.zczy.plugin.wisdom.req.cash.RspCardList;
import com.zczy.plugin.wisdom.util.WisdomAddBankUtil;
import com.zczy.plugin.wisdom.widget.adapter.WisdomCashBankAdapter;

import java.util.List;

/**
 * user: ssp
 * time: 2021/6/22 17:25
 * desc: 选择银行卡
 */

public class SelectBankDialog extends PopupWindow {

    private final WisdomCashBankAdapter mAdapter;

    public SelectBankDialog(final Context context, List<RspCardList> lvContent, final OnClickSubmitListener clickListener) {

        super(context);
        View itemView = LayoutInflater.from(context).inflate(R.layout.wisdom_cash_bank_dialog, null, false);
        setContentView(itemView);
        itemView.setOnClickListener(v -> dismiss());
        ColorDrawable background = new ColorDrawable();
        background.setAlpha(100);
        this.setBackgroundDrawable(background);
        this.setFocusable(true);
        this.setWidth(ViewGroup.LayoutParams.MATCH_PARENT);
        this.setHeight(ViewGroup.LayoutParams.MATCH_PARENT);
        this.setOutsideTouchable(true);
        this.setAnimationStyle(R.style.take_photo_anim);

        mAdapter = new WisdomCashBankAdapter();
        mAdapter.setNewData(lvContent);
        TextView tvClose = itemView.findViewById(R.id.tvClose);
        tvClose.setOnClickListener(view -> dismiss());
        TextView tvAddBank = itemView.findViewById(R.id.tv_add_bank);
        tvAddBank.setOnClickListener(view -> {
            //添加银行卡
            WisdomAddBankUtil.addBank(context);
        });
        RecyclerView recyclerView = itemView.findViewById(R.id.recyclerView);
        recyclerView.setLayoutManager(new LinearLayoutManager(context));
        recyclerView.setAdapter(mAdapter);
        recyclerView.addItemDecoration(new GridSpacingItemDecoration(1, 1, false));
        mAdapter.setOnItemClickListener((adapter, view, position) -> {
            RspCardList rspBankList = (RspCardList) adapter.getData().get(position);
            clickListener.onClickSubmitListener(rspBankList);
            dismiss();
        });
    }

    public void show(View parent) {
        super.showAtLocation(parent, Gravity.BOTTOM, 0, 0);
    }

    public interface OnClickSubmitListener {
        /**
         * 返回选择数据
         *
         * @param data
         */
        void onClickSubmitListener(RspCardList data);
    }

}
