package com.zczy.plugin.wisdom.widget;

import android.content.Context;
import android.graphics.drawable.ColorDrawable;
import androidx.recyclerview.widget.LinearLayoutManager;
import androidx.recyclerview.widget.RecyclerView;
import android.view.Gravity;
import android.view.LayoutInflater;
import android.view.View;
import android.view.ViewGroup;
import android.widget.PopupWindow;
import android.widget.TextView;

import com.zczy.plugin.wisdom.R;
import com.zczy.plugin.wisdom.rsp.bank.RspAddPublicBankChanleList;
import com.zczy.plugin.wisdom.widget.adapter.WisdomShowBankChanleAdapter;

import java.util.List;

/**
 * <AUTHOR>
 * @description (选择公卡开户行)
 * @company 中储智运科技股份有限公司
 * @copyright （版权）中储南京智慧物流科技有限公司所有
 * @time 2019/4/22  16:45
 */

public class SelectBankChanleDialog extends PopupWindow {

    private final WisdomShowBankChanleAdapter mAdapter;
    private RspAddPublicBankChanleList currentSelect;

    public SelectBankChanleDialog(final Context context, List<RspAddPublicBankChanleList> lvContent, final OnClickSubmitListener clickListener) {

        super(context);
        View itemView = LayoutInflater.from(context).inflate(R.layout.wisdom_show_bank_chanle_dialog, null, false);
        setContentView(itemView);
        itemView.setOnClickListener(v -> dismiss());
        ColorDrawable background = new ColorDrawable();
        background.setAlpha(100);
        this.setBackgroundDrawable(background);
        this.setFocusable(true);
        this.setWidth(ViewGroup.LayoutParams.MATCH_PARENT);
        this.setHeight(ViewGroup.LayoutParams.MATCH_PARENT);
        this.setOutsideTouchable(true);
        this.setAnimationStyle(R.style.take_photo_anim);

        mAdapter = new WisdomShowBankChanleAdapter();
        mAdapter.setNewData(lvContent);
        TextView tvClose = itemView.findViewById(R.id.tvClose);
        tvClose.setOnClickListener(view -> dismiss());
        TextView tvSure = itemView.findViewById(R.id.tv_sure);
        tvSure.setOnClickListener(view -> {
            //添加银行卡
            clickListener.onClickSubmitListener(currentSelect);
            dismiss();
        });
        RecyclerView recyclerView = itemView.findViewById(R.id.recyclerView);
        recyclerView.setLayoutManager(new LinearLayoutManager(context));
        recyclerView.setAdapter(mAdapter);
        recyclerView.addItemDecoration(new GridSpacingItemDecoration(1, 1, false));
        mAdapter.setOnItemClickListener((adapter, view, position) -> {
            currentSelect = (RspAddPublicBankChanleList) adapter.getData().get(position);
            mAdapter.setSelectState(currentSelect);
        });
    }

    public void show(View parent) {
        super.showAtLocation(parent, Gravity.BOTTOM, 0, 0);
    }

    public interface OnClickSubmitListener {
        /**
         * 返回选择数据
         *
         * @param data
         */
        void onClickSubmitListener(RspAddPublicBankChanleList data);
    }

}
