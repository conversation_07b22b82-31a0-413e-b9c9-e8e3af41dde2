package com.zczy.plugin.wisdom.budget.adapter;

import android.graphics.Color;
import android.text.TextUtils;
import android.util.SparseArray;

import com.chad.library.adapter.base.BaseQuickAdapter;
import com.chad.library.adapter.base.BaseViewHolder;
import com.zczy.plugin.wisdom.R;
import com.zczy.plugin.wisdom.rsp.budget.RspBudgetVehilcle;

/**
 * 功能描述: 车辆
 *
 * <AUTHOR>
 * @company 中储智运科技股份有限公司
 * @copyright （版权）中储南京智慧物流科技有限公司所有
 * @date 2019/1/16 14:45
 */
public class WisdomBudgetVehicleAdapter extends BaseQuickAdapter<RspBudgetVehilcle, BaseViewHolder> {
    /**
     * 是否是选择全部 true 是 false 不是
     */
    private boolean selectAll = false;
    /**
     * 记录当前选中
     */
    private SparseArray<RspBudgetVehilcle> tempData = new SparseArray<>();


    public WisdomBudgetVehicleAdapter() {
        super(R.layout.wisdom_budget_select_vehicle_item);
    }

    @Override
    protected void convert(BaseViewHolder helper, RspBudgetVehilcle item) {
        RspBudgetVehilcle balanceVehicle = tempData.get(item.hashCode());
        helper.setText(R.id.tvContent, item.getPlateNumber());
        if (balanceVehicle == null) {
            helper.setTextColor(R.id.tvContent, Color.parseColor("#666666"));
            helper.setBackgroundRes(R.id.rl_content, R.drawable.file_banlance_vehicle_gray);
        } else if (balanceVehicle != null) {
            helper.setTextColor(R.id.tvContent, Color.parseColor("#3c75ed"));
            helper.setBackgroundRes(R.id.rl_content, R.drawable.base_vehicle_selected);
        } else if ((balanceVehicle != null) &&
                TextUtils.equals(balanceVehicle.getPlateNumber(), "查看更多")) {
            helper.setTextColor(R.id.tvContent, Color.parseColor("#3c75ed"));
            helper.setBackgroundRes(R.id.rl_content, R.drawable.file_banlance_vehicle_blue);
        }


    }

    /**
     * 设置选择全部
     */
    public void selectAll() {
        selectAll = !selectAll;
        if (!selectAll) {
            //反向全选清空数据
            tempData.clear();
        } else {
            //全选
            tempData.clear();
            for (RspBudgetVehilcle bean : mData) {
                tempData.put(bean.hashCode(), bean);
            }
        }

        notifyDataSetChanged();
    }

    /**
     * 设置当前选中
     */
    public void setSelect(int position) {
        if (mData == null) {
            return;
        }
        RspBudgetVehilcle balanceVehicle = mData.get(position);
        if (tempData == null) {
            return;
        }
        RspBudgetVehilcle tempValue = tempData.get(balanceVehicle.hashCode());
        if (tempValue == null) {
            tempData.put(balanceVehicle.hashCode(), balanceVehicle);
        } else {
            tempData.remove(balanceVehicle.hashCode());
        }
        notifyDataSetChanged();
    }

    /**
     * 获取当前选择数据
     *
     * @return
     */
    public SparseArray<RspBudgetVehilcle> getTempData() {
        return this.tempData;
    }

    /**
     * 返回是否选择全部
     *
     * @return
     */
    public boolean isSelectAll() {
        return selectAll;
    }
}

