package com.zczy.plugin.wisdom.util;

import android.text.TextUtils;

import java.text.ParseException;
import java.text.SimpleDateFormat;
import java.util.Calendar;
import java.util.Date;

public class WisdomDateUtils {
    /**
     * 时间日期格式化到年月日时分秒.
     */
    public static String dateFormatYMDHMS = "yyyy-MM-dd HH:mm:ss";

    /**
     * 最近几个月 时间点
     */
    public static Calendar getNearestByMonth(int month) {
        Calendar today = Calendar.getInstance();
        today.add(Calendar.MONTH, -month);
        return today;
    }


    public static String getLongTime(String time, String format) {
        String longDate = "";
        /**
         * 先用SimpleDateFormat.parse() 方法将日期字符串转化为Date格式
         * 通过Date.getTime()方法，将其转化为毫秒数
         */
        try {
            SimpleDateFormat simpleDateFormat = new SimpleDateFormat(format);//24小时制
            longDate = String.valueOf(simpleDateFormat.parse(time).getTime());
        } catch (Exception e) {
            e.printStackTrace();
        }
        return longDate;
    }

    public static String getYMDHMSTime(Calendar calendar) {
        if (calendar == null)
            return "";
        SimpleDateFormat format = new SimpleDateFormat(dateFormatYMDHMS);
        return format.format(calendar.getTime());
    }

    public static Calendar getYMDHMSCalendar(String times) {
        Calendar calendar = null;
        if (TextUtils.isEmpty(times)) {
            return null;
        }
        // 对 calendar 设置时间的方法  
        // 设置传入的时间格式  
        SimpleDateFormat dateFormat = new SimpleDateFormat(dateFormatYMDHMS);
        // 指定一个日期  
        try {
            Date date = dateFormat.parse(times);
            // 对 calendar 设置为 date 所定的日期  
            calendar = Calendar.getInstance();
            calendar.setTime(date);
        } catch (ParseException e) {
            e.printStackTrace();
        }
        return calendar;
    }

    public static String getDefTime(Calendar calendar) {
        if (calendar == null)
            return "";
        SimpleDateFormat format = new SimpleDateFormat("yyyy-MM-dd");
        return format.format(calendar.getTime());
    }

    public static Calendar getCalendar(String times) {
        Calendar calendar = null;
        if (TextUtils.isEmpty(times)) {
            return null;
        }
        // 对 calendar 设置时间的方法  
        // 设置传入的时间格式  
        SimpleDateFormat dateFormat = new SimpleDateFormat("yyyy-MM-dd");
        // 指定一个日期  
        try {
            Date date = dateFormat.parse(times);
            // 对 calendar 设置为 date 所定的日期  
            calendar = Calendar.getInstance();
            calendar.setTime(date);
        } catch (ParseException e) {
            e.printStackTrace();
        }
        return calendar;
    }

    public static Calendar toCalendar(Date date) {
        Calendar instance = Calendar.getInstance();
        instance.setTime(date);
        return instance;
    }

}
