package com.zczy.plugin.wisdom.preferential

import android.content.Context
import android.content.Intent
import android.os.Bundle
import androidx.recyclerview.widget.LinearLayoutManager
import com.sfh.lib.mvvm.annotation.LiveDataMatch
import com.zczy.comm.http.entity.PageList
import com.zczy.comm.ui.BaseActivity
import com.zczy.comm.utils.dp2px
import com.zczy.comm.widget.pulltorefresh.CommEmptyView
import com.zczy.plugin.wisdom.R
import com.zczy.plugin.wisdom.preferential.adapter.WisdomPreferentialAdapter
import com.zczy.plugin.wisdom.preferential.model.WisdomPreferentialModel
import com.zczy.plugin.wisdom.preferential.req.ReqQueryConsignorOilCashRebateList
import com.zczy.plugin.wisdom.preferential.req.RspQueryConsignorOilCashRebateList
import kotlinx.android.synthetic.main.wisdom_preferential_activity.*

/**
 * 功能描述:
 * <AUTHOR>
 * @date 2022/10/28-15:19
 */
class WisdomPreferentialActivity : BaseActivity<WisdomPreferentialModel>() {

    companion object {

        @JvmStatic
        fun jumpPage(context: Context?) {
            val intent = Intent(context, WisdomPreferentialActivity::class.java)
            context?.startActivity(intent)
        }
    }

    override fun getLayout(): Int {
        return R.layout.wisdom_preferential_activity
    }

    override fun bindView(bundle: Bundle?) {
        appToolbar.setRightOnClickListener {
            WisdomPreferentialDialog().show(this@WisdomPreferentialActivity)
        }
        swipeRefreshMoreLayout.apply {
            setLayoutManager(
                LinearLayoutManager(
                    this@WisdomPreferentialActivity
                )
            )
            setAdapter(WisdomPreferentialAdapter(), true)
            addItemDecorationSize(dp2px(7f))
            setEmptyView(CommEmptyView.creatorDef(this@WisdomPreferentialActivity))
            setOnLoadListener2 {
                viewModel?.queryList(
                    req = ReqQueryConsignorOilCashRebateList(
                        nowPage = it
                    )
                )
            }
            onAutoRefresh()
        }
    }

    override fun initData() {

    }

    @LiveDataMatch
    open fun onListSuccess(data: PageList<RspQueryConsignorOilCashRebateList>) {
        swipeRefreshMoreLayout.onRefreshCompale(data)
    }

    @LiveDataMatch
    open fun onListError() {
        swipeRefreshMoreLayout.onLoadMoreFail()
    }
}