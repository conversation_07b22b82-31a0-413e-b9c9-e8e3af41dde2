package com.zczy.plugin.wisdom.req

import com.zczy.comm.http.entity.BaseNewRequest
import com.zczy.comm.http.entity.BaseRsp
import com.zczy.comm.http.entity.ResultData

/**
 * 注释: 查询商户进件信息
 * 时间: 2025/4/10 0010 14:57
 * <AUTHOR>
 */
class ReqQueryMerchantRegisterInfo() :
    BaseNewRequest<BaseRsp<RspQueryMerchantRegisterInfo>>("pps-app/passport/merchantRegister/queryMerchantRegisterInfo")

data class RspQueryMerchantRegisterInfo(
    val merchantRegisterInfo: MerchantRegisterInfo? = null
) : ResultData()

data class MerchantRegisterInfo(
    val applyRegisterFlag: String,    // 是否已申请开户：1-是 2-否
    val status: String               // 开户状态：1-受理中 2-成功 3-失败
)