package com.zczy.plugin.wisdom.unsettle;

import android.content.Context;
import android.content.Intent;
import android.graphics.Color;
import android.os.Bundle;
import android.text.TextUtils;
import android.widget.TextView;

import com.sfh.lib.mvvm.annotation.LiveDataMatch;
import com.sfh.lib.ui.AbstractLifecycleActivity;
import com.zczy.comm.ui.UtilStatus;
import com.zczy.comm.widget.AppToolber;
import com.zczy.plugin.wisdom.R;
import com.zczy.plugin.wisdom.modle.unsettle.WisdomUnSettleDetailModle;
import com.zczy.plugin.wisdom.rsp.unsettle.RspUnSettleDetail;

/**
 * 功能描述: 待确认收货金额详情
 *
 * <AUTHOR>
 * @company 中储智运科技股份有限公司
 * @copyright （版权）中储南京智慧物流科技有限公司所有
 * @date 2019/1/30 9:05
 */
public class WisdomUnSettleDetailActivity extends AbstractLifecycleActivity<WisdomUnSettleDetailModle> {


    private AppToolber appToolber;
    private TextView tvTime;
    private TextView tvOrder;
    private TextView tvStartAddress;
    private TextView tvEndAddress;
    private TextView tvDealType;
    private TextView planNum;
    private TextView tvActualNum;
    private TextView tvPlanMoney;
    private String detailId;

    public static void startContentUI(Context context, String detailId) {
        Intent intent = new Intent(context, WisdomUnSettleDetailActivity.class);
        intent.putExtra("detailId", detailId);
        context.startActivity(intent);

    }

    @Override
    protected void onCreate(Bundle savedInstanceState) {
        super.onCreate(savedInstanceState);
        setContentView(R.layout.wisdom_unsettle_detail_activity);
        detailId = getIntent().getStringExtra("detailId");
        initView();
        getViewModel().querySettleDetail(detailId);
    }

    private void initView() {
        UtilStatus.initStatus(this, Color.WHITE);
        appToolber = (AppToolber) findViewById(R.id.appToolber);
        tvTime = (TextView) findViewById(R.id.tv_time);
        tvOrder = (TextView) findViewById(R.id.tv_order);
        tvStartAddress = (TextView) findViewById(R.id.tv_start_address);
        tvEndAddress = (TextView) findViewById(R.id.tv_end_address);
        tvDealType = (TextView) findViewById(R.id.tv_deal_type);
        planNum = (TextView) findViewById(R.id.plan_num);
        tvActualNum = (TextView) findViewById(R.id.tv_actual_num);
        tvPlanMoney = (TextView) findViewById(R.id.tv_plan_money);
    }

    @LiveDataMatch
    public void onSettleDetailSuccess(RspUnSettleDetail data) {
        if (data == null) {
            return;
        }
        tvTime.setText(data.getReceiveTime());
        tvOrder.setText(data.getOrderId());
        tvStartAddress.setText(data.getDespatchCity() + data.getDespatchDis());
        tvEndAddress.setText(data.getDeliverCity() + data.getDeliverDis());
        tvDealType.setText(data.getFreightType() + data.getDelistMoney() + "元");
        planNum.setText(data.getWeight() + data.getCargoCategory());
        String slipLoad = data.getSlipLoad();
        if (TextUtils.isEmpty(slipLoad)) {
            tvActualNum.setText("未确认数量");
        } else {
            tvActualNum.setText(slipLoad + data.getCargoCategory());
        }
        tvPlanMoney.setText(data.getReceiveMoney() + "元");
    }
}
