package com.zczy.plugin.wisdom.moreaccount.adapter

import androidx.constraintlayout.widget.ConstraintLayout
import android.text.TextUtils
import android.widget.TextView
import com.chad.library.adapter.base.BaseQuickAdapter
import com.chad.library.adapter.base.BaseViewHolder
import com.zczy.comm.utils.ex.isTrue
import com.zczy.plugin.wisdom.R
import com.zczy.plugin.wisdom.moreaccount.req.RspWisdomAppropriateSelectAccount2

/**
 * author : Ssp
 * date   : 2020/3/5 16:03
 * desc   : 资金划拨
 */
class WisdomAppropriateSelectAccountAdapter : BaseQuickAdapter<RspWisdomAppropriateSelectAccount2, BaseViewHolder>(R.layout.wisdom_appropriate_select_account_item) {

    override fun convert(helper: BaseViewHolder, item: RspWisdomAppropriateSelectAccount2) {
        val view1 = helper.getView<ConstraintLayout>(R.id.clAppropriateMoney)
        val view3 = helper.getView<TextView>(R.id.tvAppropriateMoney)
        helper.addOnClickListener(R.id.clAppropriateMoney)
        if (item.booleanParent) {
            helper.setText(R.id.tv_company_name, item.shortTagName)
            helper.setGone(R.id.clBottom, true)
            helper.setGone(R.id.clTop, false)
        } else {
            helper.setText(R.id.tvShortName, item.shortName)
                .setText(R.id.tvUseMoney, item.depositMoney)
            helper.setGone(R.id.clBottom, false)
            helper.setGone(R.id.clTop, true)

            if (item.transferFlag.isTrue) {
                //可以进行资金划拨
                view1.setBackgroundResource(R.drawable.wisdom_file_blue_6corner)
                helper.setGone(R.id.ivAppropriateMoney, true)
                view1.isEnabled = true
                view3.text = "选择划拨"
            } else {
                view1.setBackgroundResource(R.drawable.wisdom_file_gray_6corner)
                helper.setGone(R.id.ivAppropriateMoney, false)
                view1.isEnabled = false
                view3.text = "不支持划拨"
            }

        }

    }

}

