package com.zczy.plugin.wisdom.modle.home;

import android.app.Activity;
import android.text.TextUtils;
import android.util.Log;

import com.alipay.sdk.app.PayTask;
import com.google.gson.Gson;
import com.sfh.lib.event.RxBusEvent;
import com.sfh.lib.exception.HandleException;
import com.sfh.lib.mvvm.service.BaseViewModel;
import com.sfh.lib.rx.IResult;
import com.sfh.lib.rx.RetrofitManager;
import com.tencent.mm.opensdk.modelpay.PayReq;
import com.tencent.mm.opensdk.openapi.IWXAPI;
import com.tencent.mm.opensdk.openapi.WXAPIFactory;
import com.zczy.comm.http.entity.BaseRsp;
import com.zczy.comm.http.entity.ResultData;
import com.zczy.plugin.wisdom.postdata.RxWXResultData;
import com.zczy.plugin.wisdom.req.recharge.ReqBank;
import com.zczy.plugin.wisdom.req.recharge.ReqWX;
import com.zczy.plugin.wisdom.req.recharge.ReqWXPayInfo;
import com.zczy.plugin.wisdom.req.recharge.ReqZFB;
import com.zczy.plugin.wisdom.req.recharge.ReqZFBPayInfo;
import com.zczy.plugin.wisdom.rsp.home.RspBank;
import com.zczy.plugin.wisdom.rsp.home.RspWX;
import com.zczy.plugin.wisdom.rsp.home.RspZFB;

import java.util.Map;

import io.reactivex.Observable;
import io.reactivex.disposables.Disposable;
import io.reactivex.functions.Function;

/**
 * 功能描述:
 * 智运宝 - 充值
 *
 * <AUTHOR>
 * @company 中储智运科技股份有限公司
 * @copyright （版权）中储南京智慧物流科技有限公司所有
 * @date 2018/11/7
 */
public class WisdomRechargeModle extends BaseViewModel {
    /**
     * 支付宝充值
     */
    public void selectZFB(ReqZFB reqZFB) {
        showLoading(false);
        this.execute(reqZFB, new IResult<BaseRsp<RspZFB>>() {
            @Override
            public void onFail(HandleException e) {
                hideLoading();
                setValue("onSelectZFBError", e.getMsg());
            }

            @Override
            public void onSuccess(BaseRsp<RspZFB> baseRsp) throws Exception {
                hideLoading();
                if (baseRsp.success()) {
                    setValue("onSelectZFBSuccess", baseRsp.getData());
                } else {
                    setValue("onSelectZFBError", baseRsp.getMsg());
                }
            }
        });
    }

    /**
     * 银行卡充值
     */
    public void selectBank(ReqBank reqBank) {
        showLoading(false);
        this.execute(reqBank, new IResult<BaseRsp<RspBank>>() {
            @Override
            public void onFail(HandleException e) {
                hideLoading();
                setValue("onSelectBankError", e.getMsg());
            }

            @Override
            public void onSuccess(BaseRsp<RspBank> baseRsp) throws Exception {
                hideLoading();
                if (baseRsp.success()) {
                    setValue("onSelectBankSuccess", baseRsp.getData());
                } else {
                    setValue("onSelectBankError", baseRsp.getMsg());
                }
            }
        });
    }

    /**
     * 微信充值
     */
    public void selectWX(ReqWX reqWX) {
        showLoading(false);
        this.execute(reqWX, new IResult<BaseRsp<RspWX>>() {
            @Override
            public void onFail(HandleException e) {
                hideLoading();
                setValue("onSelectWXError", e.getMsg());
            }

            @Override
            public void onSuccess(BaseRsp<RspWX> baseRsp) throws Exception {
                hideLoading();
                if (baseRsp.success()) {
                    setValue("onSelectWXSuccess", baseRsp.getData());
                } else {
                    setValue("onSelectWXError", baseRsp.getMsg());
                }
            }
        });
    }

    /**
     * 微信支付结果
     */
    public void queryWXPayInfo(String outTradeNo) {
        showLoading(true);
        ReqWXPayInfo reqWXPayInfo = new ReqWXPayInfo();
        reqWXPayInfo.setOut_trade_no(outTradeNo);
        this.execute(reqWXPayInfo, new IResult<BaseRsp<ResultData>>() {
            @Override
            public void onFail(HandleException e) {
                hideLoading();
                setValue("onWXPayError", e.getMsg());
            }

            @Override
            public void onSuccess(BaseRsp<ResultData> baseRsp) throws Exception {
                hideLoading();
                if (baseRsp.success()) {
                    setValue("onWXPaySuccess", baseRsp.getData());
                } else {
                    setValue("onWXPayError", baseRsp.getMsg());
                }
            }
        });
    }

    /**
     * 支付宝支付结果
     */
    public void queryZFBPayInfo(Map<String, String> result) {
        showLoading(true);
        Gson gson = new Gson();
        gson.toJson(result);
        ReqZFBPayInfo reqZFBPayInfo = new ReqZFBPayInfo();
        reqZFBPayInfo.setZfbResult(result);
        this.execute(reqZFBPayInfo, new IResult<BaseRsp<ResultData>>() {
            @Override
            public void onFail(HandleException e) {
                hideLoading();
                setValue("onZFBPayError", e.getMsg());
            }

            @Override
            public void onSuccess(BaseRsp<ResultData> baseRsp) throws Exception {
                hideLoading();
                if (baseRsp.success()) {
                    setValue("onZFBPaySuccess");
                } else {
                    setValue("onZFBPayError", baseRsp.getMsg());
                }
            }
        });
    }

    /**
     * 支付宝支付初始化
     *
     * @param orderInfo
     */
    public void initAliPay(final String orderInfo, Activity activity) {
        PayTask payTask = new PayTask(activity);
        Disposable disposable = RetrofitManager.executeSigin(Observable.just(payTask).map(new Function<PayTask, Map<String, String>>() {
            @Override
            public Map<String, String> apply(PayTask payTask) throws Exception {
                return payTask.payV2(orderInfo, true);
            }
        }), new IResult<Map<String, String>>() {
            @Override
            public void onFail(HandleException e) {
                setValue("onInitAipayError");
            }

            @Override
            public void onSuccess(Map<String, String> result) throws Exception {
                String resultStatus = result.get("resultStatus");
                if (TextUtils.equals("6001", resultStatus)) {
                    //用户中途取消 不回调后台接口
                } else {
                    queryZFBPayInfo(result);
                }
            }
        });
        this.putDisposable(disposable);
    }

    /**
     * 微信支付
     */
    public void weChatPay(RspWX data, Activity activity, IWXAPI api) {
        if (null == data) {
            return;
        }
        //商户APP工程中引入微信JAR包，调用API前，需要先向微信注册您的APPID，代码如下
        final IWXAPI msgApi = WXAPIFactory.createWXAPI(activity, "wx947560c84f8075fd");
        // 将该app注册到微信
        msgApi.registerApp("wx947560c84f8075fd");
        // isWXAppInstalled和isWXAppSupportAPI来判断微信客户端是否安装及安装的版本是否支持微信开放平台
        boolean isInstalled = msgApi.isWXAppInstalled();
        if (!isInstalled) {
            showDialogToast("请先安装微信!");
            return;
        }

        PayReq request = new PayReq();

        request.appId = "wx947560c84f8075fd";

        request.partnerId = data.getPartnerid();

        request.prepayId = data.getPrepayid();

        request.packageValue = data.getPackaged();

        request.nonceStr = data.getNoncestr();

        request.timeStamp = data.getTimestamp();

        request.sign = data.getSign();

        api.sendReq(request);
    }

    @Override
    public boolean eventOnOff() {
        return true;
    }

    @RxBusEvent(from = "微信支付")
    public void onWxPaySuccess(RxWXResultData data) {
        if (data.query) {
            //微信支付成功 查询支付结果
            setValue("onWxPayActionSuccess");
        }

    }
}
