package com.zczy.plugin.wisdom.modle.home;

import com.sfh.lib.mvvm.service.BaseViewModel;
import com.zczy.plugin.wisdom.req.ReqAccount;

/**
 * 功能描述: 余额不足页面
 *
 * <AUTHOR>
 * @company 中储智运科技股份有限公司
 * @copyright （版权）中储南京智慧物流科技有限公司所有
 * @date 2019/1/30 9:10
 */
public class WisdomAccountModle extends BaseViewModel {
    /**
     * 查询列表数据
     */
    public void querySettle(int nowPage) {

        this.execute(new ReqAccount(nowPage), pageListBaseRsp -> {
            if (pageListBaseRsp.success()) {
                setValue("onQuerySuccess", pageListBaseRsp.getData());
            } else {
                setValue("onQueryError");
            }
        });

    }

}
