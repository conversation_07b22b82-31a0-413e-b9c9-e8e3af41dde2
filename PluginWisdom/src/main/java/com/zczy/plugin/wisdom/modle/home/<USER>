package com.zczy.plugin.wisdom.modle.home

import android.content.Context
import android.view.Gravity
import com.sfh.lib.exception.HandleException
import com.sfh.lib.mvvm.service.BaseViewModel
import com.sfh.lib.rx.IResult
import com.sfh.lib.ui.dialog.DialogBuilder
import com.zczy.comm.data.entity.ELogin
import com.zczy.comm.http.entity.BaseRsp
import com.zczy.comm.http.entity.ResultData
import com.zczy.comm.pluginserver.AMainServer
import com.zczy.plugin.wisdom.R
import com.zczy.plugin.wisdom.req.*
import com.zczy.plugin.wisdom.req.cash.ReqWisdomAuthenticatonMac
import com.zczy.plugin.wisdom.scancash.req.ReqWisdomAuthenticaton

/**
 *  desc: 智运宝 - 首页
 *  user: 宋双朋
 *  time: 2024/11/14 14:07
 */

class WisdomHomeModel : BaseViewModel() {
    /**
     * 获取 初审通过 首页金额
     */
    fun getHomeAccount(bookNo: String?, fundMode: String?) {
        this.execute(
            ReqQueryAccount(bookNo, fundMode),
            fun(baseRsp: BaseRsp<RspHomeAccount>) {
                if (baseRsp.success()) {
                    setValue("onQueryHomeSuccess", baseRsp.data)
                } else {
                    setValue("onQueryAccountError", baseRsp.data?.resultCode)
                }
            })
    }

    /**
     * 应付卸货金额
     */
    fun queryAppUnReceiveMoney(subsidiaryId: String) {
        this.execute(ReqQueryUnReceiveMoneys(subsidiaryId)) { baseRsp ->
            if (baseRsp.success()) {
                setValue("onQueryUnReceiveMoneySuccess", baseRsp.data)
            }
        }
    }

    /**
     * 查询充值配置
     */
    fun queryRechargeConfig() {
        this.execute<BaseRsp<ResultData>>(true, ReqQueryRechargeConfig()) { baseRsp ->
            hideLoading()
            if (baseRsp.success()) {
                <EMAIL>("onQueryRechageConfigSuccess")
            } else {
                showDialogToast(baseRsp.msg)
            }
        }
    }

    /**
     * 点击提现校验 是否可以提现
     */
    fun checkCashOptEnable(bookNo: String?, fundMode: String?, context: Context?) {
        this.execute(
            true,
            ReqCashCheck(bookNo = bookNo, fundMode = fundMode),
            object : IResult<BaseRsp<RspHomeCashState>> {
                override fun onFail(e: HandleException) {
                    hideLoading()
                }

                @Throws(Exception::class)
                override fun onSuccess(baseRsp: BaseRsp<RspHomeCashState>) {
                    hideLoading()
                    if (baseRsp.success()) {
                        setValue("onCheckCashOptEnableSuccess", baseRsp.data)
                    } else {
                        when (baseRsp.data?.resultCode) {
                            "9999" -> {
                                val dialogBuilder = DialogBuilder()
                                    .setMessage(baseRsp.data?.resultMsg)
                                    .setGravity(Gravity.CENTER)
                                    .setOKText("确认")
                                    .setCancelText("咨询在线客服")
                                    .setCancelListener { _, _ ->
                                        //打开在线客服
                                        AMainServer.getPluginServer().openLineServer(context)
                                    }
                                    .setOkListener { dialogInterface, _ ->
                                        dialogInterface.dismiss()
                                    }
                                showDialog(dialogBuilder)
                            }

                            else -> {
                                showDialogToast(baseRsp.msg)
                            }
                        }
                    }
                }
            })
    }

    /***
     * 校验是否更换设备
     */
    fun checkAuthenticationMac() {

        this.execute(false, ReqWisdomAuthenticatonMac()) { resultDataBaseRsp ->
            hideLoading()
            if (resultDataBaseRsp.success()) {
                setValue("onCheckAuthenticationMacSuccess")
            } else {
                setValue("onCheckAuthenticationMacError")
            }
        }
    }

    /***
     * 发送设备认证验证码
     */
    fun sendAuthenticationSMS(login: ELogin) {
        val reqSendCode = ReqSendCodeWisdom()
        reqSendCode.type = ReqSendCodeWisdom.TYPE_SMS
        reqSendCode.setModuleType(ReqSendCodeWisdom.MODULE_TYPE_M)
        reqSendCode.mobile = login.mobile
        this.execute<BaseRsp<ResultData>>(false, reqSendCode) { resultDataBaseRsp ->
            hideLoading()
            if (resultDataBaseRsp.success()) {
                this.setValue("onSendAuthenticationCodeSuccess", login)
            } else {
                when (resultDataBaseRsp.data?.resultCode) {
                    "MI300064" -> {
                        this.setValue("onSendAuthenticationCodeSuccess", login)
                    }

                    else -> {
                        showToast(resultDataBaseRsp.msg)
                    }
                }
            }
        }
    }

    /***
     * 设备验证验证码校验
     * @param verifyCode
     */
    fun checkAuthenticationCode(verifyCode: ReqWisdomAuthenticaton) {

        this.execute(false, verifyCode) { resultDataBaseRsp ->
            hideLoading()
            if (resultDataBaseRsp.success()) {
                setValue("onCheckAuthenticationCodeSuccess")
                showToast(resultDataBaseRsp.msg)
            } else {
                showToast(resultDataBaseRsp.msg)
            }
        }
    }

    /**
     * 关于智运宝可用金额
     */
    fun accountMoneyAlert() {
        val dialogBuilder = DialogBuilder()
            .setTitleColor("关于智运账本可结算金额", R.color.color_333333)
            .setMessageGravity("指的是货主预付运费当前尚未用于支付结算的金额。", Gravity.CENTER)
            .setHideCancel(true)
            .setOKTextColor("知道了", R.color.color_5086fc)
        showDialog(dialogBuilder)
    }

    //获取视频链接
    fun getVideoPath(reqGetVideoPath: ReqGetVideoPath?) {
        reqGetVideoPath?.sendRequest(object : IResult<BaseRsp<RespGetVideoPath>> {
            override fun onSuccess(t: BaseRsp<RespGetVideoPath>) {
                if (t.success()) {
                    setValue("ongetVideoPathSuccess", t.data)
                } else {
                    setValue("ongetVideoPathFailed")
                    showToast(t?.msg)
                }
            }

            override fun onFail(e: HandleException?) {
                showToast(e?.msg)
                setValue("ongetVideoPathFailed")
            }

        })
    }

    fun queryConsignorOilCashRebateTotal(req: ReqQueryConsignorOilCashRebateTotal) {
        //查询油品现金奖励总金额
        execute(req) {
            if (it.success()) {
                setValue("queryConsignorOilCashRebateTotalSuccess", it.data)
            }
        }
    }

    fun queryAssignLeftAvailableMoney(req: ReqQueryAssignLeftAvailableMoney) {
        //查询油品现金奖励总金额
        execute(req) {
            if (it.success()) {
                setValue("onQueryAssignLeftAvailableMoney", it.data)
            }
        }
    }

    /**
     * 注释：货主APP端冻结金额增加按冻结交易类型展示框
     * 时间：2024/9/24 17:03
     * 作者：王家辉
     * */
    fun queryAccountFreezeDetail(req: ReqQueryAccountFreezeDetail) {
        //查询油品现金奖励总金额
        execute(true, req) {
            if (it.success()) {
                setValue("onQueryAccountFreezeDetail", it.data)
            }
        }
    }

    /**
     * 注释：查询保证金账本数量
     * 时间：2025/3/31 15:00
     * @author：王家辉
     * */
    fun queryBondBookCount() {
        execute(true, ReqQueryBondBookCount()) {
            if (it.success()) {
                setValue("onQueryBondBookCount", it.data)
            }
        }
    }

    /**
     * 注释: 查询交易通账户
     * 时间: 2025/4/10 0010 14:43
     * <AUTHOR>
     */
    fun queryPassportAccount() {
        execute(true, ReqPassportAccount()) {
            if (it.success()) {
                setValue("onReqPassportAccount", it.data)
            }
        }
    }

    /**
     * 注释: 查询商户进件信息
     * 时间: 2025/4/10 0010 14:58
     * <AUTHOR>
     */
    fun queryMerchantRegisterInfo() {
        execute(true, ReqQueryMerchantRegisterInfo()) {
            if (it.success()) {
                setValue("onReqQueryMerchantRegisterInfo", it.data)
            }
        }
    }

}
