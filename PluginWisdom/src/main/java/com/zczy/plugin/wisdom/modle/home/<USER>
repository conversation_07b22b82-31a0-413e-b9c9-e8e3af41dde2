package com.zczy.plugin.wisdom.modle.home

import com.zczy.comm.http.entity.BaseNewRequest
import com.zczy.comm.http.entity.BaseRsp
import com.zczy.comm.http.entity.ResultData

/**
 * 功能描述: 获取货主油品现金奖励总金额
 * <AUTHOR>
 * @date 2022/11/8-11:14
 */

class ReqQueryConsignorOilCashRebateTotal : BaseNewRequest<BaseRsp<RspQueryConsignorOilCashRebateTotal>>("pps-app/accountConsignor/queryConsignorOilCashRebateTotal")

data class RspQueryConsignorOilCashRebateTotal(
    val oilCashRebateMoney: String? = null, //油品现金奖励金额
) : ResultData()

fun RspQueryConsignorOilCashRebateTotal.showMoney(): String {
    return if (oilCashRebateMoney.isNullOrEmpty()) {
        ""
    } else {
        oilCashRebateMoney + "元"
    }
}