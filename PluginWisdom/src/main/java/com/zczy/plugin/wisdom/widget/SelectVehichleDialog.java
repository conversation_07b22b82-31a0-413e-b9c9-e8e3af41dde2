package com.zczy.plugin.wisdom.widget;

import android.content.Context;
import android.graphics.drawable.ColorDrawable;
import androidx.recyclerview.widget.GridLayoutManager;
import androidx.recyclerview.widget.RecyclerView;
import android.text.TextUtils;
import android.util.SparseArray;
import android.view.Gravity;
import android.view.LayoutInflater;
import android.view.View;
import android.view.ViewGroup;
import android.widget.ImageView;
import android.widget.PopupWindow;
import android.widget.TextView;

import com.chad.library.adapter.base.BaseQuickAdapter;
import com.zczy.plugin.wisdom.R;
import com.zczy.plugin.wisdom.budget.adapter.WisdomBudgetVehicleAdapter;
import com.zczy.plugin.wisdom.rsp.budget.RspBudgetVehilcle;

import java.util.List;

/**
 * <AUTHOR> 宋双朋
 * @description (筛选车辆)
 * @company 中储智运科技股份有限公司
 * @copyright （版权）中储南京智慧物流科技有限公司所有
 * @time 2018/10/9 16:53
 */

public class SelectVehichleDialog extends PopupWindow {

    private final WisdomBudgetVehicleAdapter mAdapter;

    public SelectVehichleDialog(final Context context, List<RspBudgetVehilcle> lvContent, final OnClickSubmitListener clickListener) {

        super(context);
        View itemView = LayoutInflater.from(context).inflate(R.layout.wisdom_budget_vehicle_dialog, null, false);
        setContentView(itemView);
        itemView.setOnClickListener(new View.OnClickListener() {

            @Override
            public void onClick(View v) {
                dismiss();
            }
        });
        ColorDrawable background = new ColorDrawable();
        background.setAlpha(100);
        this.setBackgroundDrawable(background);
        this.setFocusable(true);
        this.setWidth(ViewGroup.LayoutParams.MATCH_PARENT);
        this.setHeight(ViewGroup.LayoutParams.MATCH_PARENT);
        this.setOutsideTouchable(true);
        this.setAnimationStyle(R.style.take_photo_anim);

        mAdapter = new WisdomBudgetVehicleAdapter();
        mAdapter.setNewData(lvContent);
        ImageView ivClose = itemView.findViewById(R.id.iv_Close);
        ivClose.setOnClickListener(new View.OnClickListener() {
            @Override
            public void onClick(View view) {
                dismiss();
            }
        });
        TextView tvSubmit = itemView.findViewById(R.id.tv_submit);
        tvSubmit.setOnClickListener(new View.OnClickListener() {
            @Override
            public void onClick(View view) {
                //确定返回选择数据

                if (mAdapter == null) {
                    return;
                }

                if (clickListener == null) {
                    return;
                }
                clickListener.onClickSubmitListener(mAdapter.getTempData());

                dismiss();
            }
        });
        RecyclerView recyclerView = itemView.findViewById(R.id.recyclerView);
        recyclerView.setLayoutManager(new GridLayoutManager(context, 3));
        recyclerView.setAdapter(mAdapter);
        recyclerView.addItemDecoration(new GridSpacingItemDecoration(3, 20, false));
        mAdapter.setOnItemClickListener(new BaseQuickAdapter.OnItemClickListener() {
            @Override
            public void onItemClick(BaseQuickAdapter adapter, View view, int position) {
                RspBudgetVehilcle balanceVehicle = (RspBudgetVehilcle) adapter.getData().get(position);
                if (TextUtils.equals(balanceVehicle.getPlateNumber(), "查看更多")) {
                    dismiss();
                    clickListener.onClickLookMoreListener();
                } else {
                    mAdapter.setSelect(position);
                }
            }
        });
    }

    public void show(View parent) {
        super.showAtLocation(parent, Gravity.BOTTOM, 0, 0);
    }

    public interface OnClickSubmitListener {
        /**
         * 返回选择数据
         *
         * @param data
         */
        void onClickSubmitListener(SparseArray<RspBudgetVehilcle> data);

        /**
         * 查看更多点击事件
         */
        void onClickLookMoreListener();
    }

}
