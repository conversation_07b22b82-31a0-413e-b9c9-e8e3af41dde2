package com.zczy.plugin.wisdom.preferential.model

import com.sfh.lib.exception.HandleException
import com.sfh.lib.mvvm.service.BaseViewModel
import com.sfh.lib.rx.IResult
import com.zczy.comm.http.entity.BaseRsp
import com.zczy.comm.http.entity.PageList
import com.zczy.plugin.wisdom.preferential.req.ReqQueryConsignorOilCashRebateList
import com.zczy.plugin.wisdom.preferential.req.RspQueryConsignorOilCashRebateList

/**
 * 功能描述: 奖励明细
 * <AUTHOR>
 * @date 2022/10/28-15:41
 */

class WisdomPreferentialModel : BaseViewModel() {

    fun queryList(req: ReqQueryConsignorOilCashRebateList) {
        execute(req, object : IResult<BaseRsp<PageList<RspQueryConsignorOilCashRebateList>>> {
            override fun onSuccess(p: BaseRsp<PageList<RspQueryConsignorOilCashRebateList>>) {
                if (p.success()) {
                    setValue("onListSuccess", p.data)
                } else {
                    showToast(p.msg)
                    setValue("onListError")
                }
            }

            override fun onFail(p: HandleException) {
                showToast(p.msg)
                setValue("onListError")
            }
        })
    }
}