package com.zczy.plugin.wisdom.widget;

import android.content.Context;
import androidx.annotation.Nullable;
import androidx.constraintlayout.widget.ConstraintLayout;
import android.util.AttributeSet;
import android.view.View;
import android.widget.ImageView;
import android.widget.TextView;

import com.zczy.plugin.wisdom.R;

/**
 * 功能描述:
 * 智运宝 品牌保障 view
 * https://lanhuapp.com/web/#/item/project/board/detail?pid=373522cc-a666-4370-a0b3-43e0ad929d62&project_id=373522cc-a666-4370-a0b3-43e0ad929d62&image_id=56855c4f-fd31-496d-9b0f-5d1771c36697
 * <AUTHOR>
 * @company 中储智运科技股份有限公司
 * @copyright （版权）中储南京智慧物流科技有限公司所有
 * @date 2019/3/25
 */
public class WisdomHomeBottomLayoutV2 extends ConstraintLayout {
    private ImageView ivCustomerService;
    private ImageView ivSafeguard;
    private TextView tvTitle1;
    private TextView tvTitle2;

    private Listener mListener;

    public WisdomHomeBottomLayoutV2(Context context) {
        super(context);
        initView();
    }

    public WisdomHomeBottomLayoutV2(Context context, @Nullable AttributeSet attrs) {
        super(context, attrs);
        initView();
    }

    public WisdomHomeBottomLayoutV2(Context context, @Nullable AttributeSet attrs, int defStyleAttr) {
        super(context, attrs, defStyleAttr);
        initView();
    }

    private void initView() {
        inflate(this.getContext(), R.layout.wisdom_home_bottom_view_v2, this);

        this.ivCustomerService = findViewById(R.id.iv_customer_service);
        this.ivCustomerService.setOnClickListener(v -> {
            if (mListener != null) {
                mListener.onCustomerService();
            }
        });
        this.ivSafeguard = findViewById(R.id.iv_safeguard);
        this.tvTitle1 = findViewById(R.id.tv_title_1);
        this.tvTitle2 = findViewById(R.id.tv_title_2);
    }

    /**
     * 设置title1字体颜色
     * @param color
     */
    public void setTvTitle1TextColor(int color) {
        this.tvTitle1.setTextColor(color);
    }

    /**
     * 设置title2字体颜色
     * @param color
     */
    public void setTvTitle2TextColor(int color) {
        this.tvTitle2.setTextColor(color);
    }

    /**
     * 设置保障图标
     * @param resid
     */
    public void setIvSafeguardBackgroud(int resid) {
        ivSafeguard.setBackgroundResource(resid);
    }

    /**
     * 设置底部客服按钮背景
     * @param resid
     */
    public void setIvCustomerServiceBackgroud(int resid) {
        ivCustomerService.setBackgroundResource(resid);
    }

    public void setListener(Listener listener) {
        mListener = listener;
    }

    public void hideCustomerService() {
        ivCustomerService.setVisibility(View.GONE);
    }

    public interface Listener {
        void onCustomerService();
    }
}
