package com.zczy.plugin.wisdom.budget;

import android.app.Activity;
import android.content.Context;
import android.content.Intent;
import android.graphics.Color;
import android.os.Bundle;
import androidx.recyclerview.widget.GridLayoutManager;
import android.util.SparseArray;
import android.view.View;
import android.widget.TextView;

import com.chad.library.adapter.base.BaseQuickAdapter;
import com.sfh.lib.mvvm.annotation.LiveDataMatch;
import com.sfh.lib.ui.AbstractLifecycleActivity;
import com.zczy.comm.http.entity.PageList;
import com.zczy.comm.ui.UtilStatus;
import com.zczy.comm.widget.AppToolber;
import com.zczy.comm.widget.pulltorefresh.OnLoadingListener2;
import com.zczy.comm.widget.pulltorefresh.SwipeRefreshMoreLayout;
import com.zczy.plugin.wisdom.R;
import com.zczy.plugin.wisdom.budget.adapter.WisdomBudgetVehicleAdapter;
import com.zczy.plugin.wisdom.modle.budget.WisdomBudgetVehicleModle;
import com.zczy.plugin.wisdom.postdata.RxSelectVehicleData;
import com.zczy.plugin.wisdom.req.budget.ReqQueryVehicle;
import com.zczy.plugin.wisdom.rsp.budget.RspBudgetVehilcle;

import java.util.ArrayList;

/**
 * 功能描述: 车辆筛选 车辆信息
 *
 * <AUTHOR>
 * @company 中储智运科技股份有限公司
 * @copyright （版权）中储南京智慧物流科技有限公司所有
 * @date 2019/1/16 14:33
 */
public class WisdomBudgetVehicleActivity extends AbstractLifecycleActivity<WisdomBudgetVehicleModle> implements BaseQuickAdapter.OnItemClickListener, View.OnClickListener {

    private WisdomBudgetVehicleAdapter mAdapter;
    private TextView tvSubmit;
    private SwipeRefreshMoreLayout swipeRefreshMoreLayout;
    private AppToolber appToolber;

    /**
     * 页面跳转
     *
     * @param context
     */
    public static void startContentUI(Context context) {
        Intent intent = new Intent(context, WisdomBudgetVehicleActivity.class);
        context.startActivity(intent);

    }


    @Override
    protected void onCreate(Bundle savedInstanceState) {
        super.onCreate(savedInstanceState);
        setContentView(R.layout.wisdom_budget_vehicle_activity);
        initView();
    }

    private void initView() {
        UtilStatus.initStatus(this, Color.WHITE);
        appToolber = (AppToolber) findViewById(R.id.appToolber);
        tvSubmit = (TextView) findViewById(R.id.tv_submit);
        tvSubmit.setOnClickListener(this);
        swipeRefreshMoreLayout = (SwipeRefreshMoreLayout) findViewById(R.id.swipeRefreshMoreLayout);

        appToolber.setRightOnClickListener(new View.OnClickListener() {
            @Override
            public void onClick(View view) {
                //这里选择全部车辆
                mAdapter.selectAll();
            }
        });

        // 设置上拉下拉事件
        mAdapter = new WisdomBudgetVehicleAdapter();
        swipeRefreshMoreLayout.setAdapter(mAdapter, true);
        mAdapter.setOnItemClickListener(this);
        //设置加载模式
        swipeRefreshMoreLayout.setOnLoadListener2(new OnLoadingListener2() {
            @Override
            public void onLoadUI(int nowPage) {
                ReqQueryVehicle reqQueryVehicle = new ReqQueryVehicle();
                reqQueryVehicle.setNowPage(nowPage);
                reqQueryVehicle.setPageSize(20);
                getViewModel().queryVehicle(reqQueryVehicle);
            }
        });
        //设置展示几列
        swipeRefreshMoreLayout.setLayoutManager(new GridLayoutManager(this, 3));
        //刷新页面数据
        swipeRefreshMoreLayout.onAutoRefresh();

    }

    @Override
    public void onItemClick(BaseQuickAdapter adapter, View view, int position) {
        //选择车辆
        mAdapter.setSelect(position);
    }

    @LiveDataMatch
    public void onQueryVehicleSuccess(PageList<RspBudgetVehilcle> data) {
        swipeRefreshMoreLayout.onRefreshCompale(data);
    }

    @LiveDataMatch
    public void onQueryVehicleError() {
        swipeRefreshMoreLayout.onLoadMoreFail();
    }

    @Override
    public void onClick(View v) {
        if (v.getId() == R.id.tv_submit){
            //提交选择
            Intent intent = new Intent();
            if (mAdapter.isSelectAll()) {
                //表示选择全部
                RxSelectVehicleData rxSelectVehicle = new RxSelectVehicleData(mAdapter.isSelectAll(), new ArrayList<RspBudgetVehilcle>());
                intent.putExtra("selectVehicle", rxSelectVehicle);
            } else {
                //没有选择全部
                ArrayList<RspBudgetVehilcle> list = new ArrayList<>();
                SparseArray<RspBudgetVehilcle> tempData = mAdapter.getTempData();
                for (int i = 0; i < tempData.size(); i++) {
                    RspBudgetVehilcle rspBudgetVehilcle = tempData.valueAt(i);
                    list.add(rspBudgetVehilcle);
                }
                RxSelectVehicleData rxSelectVehicle = new RxSelectVehicleData(mAdapter.isSelectAll(), list);
                intent.putExtra("selectVehicle", rxSelectVehicle);
            }

            // 设置返回码和返回携带的数据
            setResult(Activity.RESULT_OK, intent);
            finish();
        }
    }

}
