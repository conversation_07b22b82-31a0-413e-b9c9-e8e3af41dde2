package com.zczy.plugin.wisdom.home;

import android.content.Context;
import android.content.Intent;
import android.graphics.Color;
import android.os.Bundle;
import android.text.Editable;
import android.text.TextUtils;
import android.text.TextWatcher;
import android.view.View;
import android.widget.EditText;
import android.widget.ImageView;
import android.widget.RelativeLayout;
import android.widget.TextView;

import com.sfh.lib.mvvm.annotation.LiveDataMatch;
import com.sfh.lib.ui.AbstractLifecycleActivity;
import com.tencent.mm.opensdk.openapi.IWXAPI;
import com.tencent.mm.opensdk.openapi.WXAPIFactory;
import com.zczy.comm.http.entity.ResultData;
import com.zczy.comm.ui.UtilStatus;
import com.zczy.comm.widget.AppToolber;
import com.zczy.plugin.wisdom.R;
import com.zczy.plugin.wisdom.home.view.WisdomHomeBottomLayout;
import com.zczy.plugin.wisdom.modle.home.WisdomRechargeModle;
import com.zczy.plugin.wisdom.req.recharge.ReqBank;
import com.zczy.plugin.wisdom.req.recharge.ReqWX;
import com.zczy.plugin.wisdom.req.recharge.ReqZFB;
import com.zczy.plugin.wisdom.rsp.home.RspBank;
import com.zczy.plugin.wisdom.rsp.home.RspWX;
import com.zczy.plugin.wisdom.rsp.home.RspZFB;

/**
 * 功能描述: 充值页面
 *
 * <AUTHOR>
 * @company 中储智运科技股份有限公司
 * @copyright （版权）中储南京智慧物流科技有限公司所有
 * @date 2019/1/30 9:05
 */
public class WisdomRechargeActivity extends AbstractLifecycleActivity<WisdomRechargeModle> implements View.OnClickListener {


    private AppToolber appToolber;
    private RelativeLayout rlTips;
    /**
     * 充值金额(元)
     */
    private TextView tvMoneyTips;
    private ImageView tvBank;
    private TextView bankPayTv;
    private ImageView tvWx;
    private TextView wxPayTv;
    private ImageView tvZfb;
    private TextView zfbPayTv;
    private ImageView tvCaution;
    /**
     * 立即充值
     */
    private TextView tvCommit;
    private WisdomHomeBottomLayout customerLayout;
    /**
     * ¥
     */
    private TextView tvMoneySymbol;
    private EditText etMoney;
    private RelativeLayout rlBank;
    private RelativeLayout rlWx;
    private RelativeLayout rlZfb;
    private IWXAPI api;
    private String outTradeNo;
    private String tempMoney;
    private String bookNo;
    private String fundMode;
    private String transactionType;

    public static void startContentUI(Context context, String bookNo, String fundMode, String transactionType) {
        Intent intent = new Intent(context, WisdomRechargeActivity.class);
        intent.putExtra("bookNo", bookNo);
        intent.putExtra("fundMode", fundMode);
        intent.putExtra("transactionType", transactionType);
        context.startActivity(intent);

    }

    @Override
    protected void onCreate(Bundle savedInstanceState) {
        super.onCreate(savedInstanceState);
        setContentView(R.layout.wisdom_rechargel_activity);
        bookNo = getIntent().getStringExtra("bookNo");
        fundMode = getIntent().getStringExtra("fundMode");
        transactionType = getIntent().getStringExtra("transactionType");
        api = WXAPIFactory.createWXAPI(this, "wxb4ba3c02aa476ea1");
        initView();
    }

    private void initView() {
        UtilStatus.initStatus(this, Color.WHITE);
        appToolber = (AppToolber) findViewById(R.id.appToolber);
        rlTips = (RelativeLayout) findViewById(R.id.rl_tips);
        tvMoneyTips = (TextView) findViewById(R.id.tv_money_tips);
        tvBank = (ImageView) findViewById(R.id.tv_bank);
        bankPayTv = (TextView) findViewById(R.id.bank_pay_tv);
        tvWx = (ImageView) findViewById(R.id.tv_wx);
        wxPayTv = (TextView) findViewById(R.id.wx_pay_tv);
        tvZfb = (ImageView) findViewById(R.id.tv_zfb);
        zfbPayTv = (TextView) findViewById(R.id.zfb_pay_tv);
        tvCaution = (ImageView) findViewById(R.id.tv_caution);
        tvCommit = (TextView) findViewById(R.id.tv_commit);
        customerLayout = (WisdomHomeBottomLayout) findViewById(R.id.customer_layout);
        tvMoneySymbol = (TextView) findViewById(R.id.tv_money_symbol);
        tvCommit.setOnClickListener(this);
        etMoney = (EditText) findViewById(R.id.et_money);
        rlBank = (RelativeLayout) findViewById(R.id.rl_bank);
        rlBank.setOnClickListener(this);
        rlWx = (RelativeLayout) findViewById(R.id.rl_wx);
        rlWx.setOnClickListener(this);
        rlZfb = (RelativeLayout) findViewById(R.id.rl_zfb);
        rlZfb.setOnClickListener(this);
        etMoney.addTextChangedListener(new TextWatcher() {
            @Override
            public void beforeTextChanged(CharSequence s, int start, int count, int after) {

            }

            @Override
            public void onTextChanged(CharSequence s, int start, int before, int count) {

            }

            @Override
            public void afterTextChanged(Editable s) {
                isOK();
            }
        });
    }

    @Override
    public void onClick(View v) {
        if (v.getId() == R.id.tv_commit) {
            //确认充值
            String paynum = etMoney.getText().toString().trim();
            boolean wx = this.wxPayTv.isEnabled();
            boolean zfb = this.zfbPayTv.isEnabled();
            boolean bank = this.bankPayTv.isEnabled();
            if (wx) {
                // 开始  微信支付
                ReqWX reqWX = new ReqWX();
                reqWX.setChannelType("9");
                reqWX.setPayment(paynum);
                reqWX.setFundMode(fundMode);
                reqWX.setBookNo(bookNo);
                reqWX.setTransactionType(transactionType);
                getViewModel().selectWX(reqWX);
                tvCommit.setEnabled(false);
                return;
            }

            if (zfb) {
                // 开始 支付宝
                ReqZFB reqZFB = new ReqZFB();
                reqZFB.setChannelType("8");
                reqZFB.setPayment(paynum);
                reqZFB.setFundMode(fundMode);
                reqZFB.setBookNo(bookNo);
                reqZFB.setTransactionType(transactionType);
                getViewModel().selectZFB(reqZFB);
                tvCommit.setEnabled(false);
                return;
            }

            if (bank) {
                // 开始 银联支付
                ReqBank reqBank = new ReqBank();
                reqBank.setChannelType("1");
                reqBank.setPayment(paynum);
                reqBank.setFundMode(fundMode);
                reqBank.setBookNo(bookNo);
                reqBank.setTransactionType(transactionType);
                getViewModel().selectBank(reqBank);
                return;
            }
            showDialogToast("请选择一种支付方式");
        } else if (v.getId() == R.id.rl_bank) {
            //银行卡支付
            this.bankPayTv.setEnabled(!this.bankPayTv.isEnabled());
            this.wxPayTv.setEnabled(false);
            this.zfbPayTv.setEnabled(false);
            this.tvCommit.setEnabled(isOK());
        } else if (v.getId() == R.id.rl_wx) {
            //微信支付
            this.wxPayTv.setEnabled(!this.wxPayTv.isEnabled());
            this.zfbPayTv.setEnabled(false);
            this.bankPayTv.setEnabled(false);
            this.tvCommit.setEnabled(isOK());
        } else if (v.getId() == R.id.rl_zfb) {
            //支付宝支付
            this.zfbPayTv.setEnabled(!this.zfbPayTv.isEnabled());
            this.wxPayTv.setEnabled(false);
            this.bankPayTv.setEnabled(false);
            this.tvCommit.setEnabled(isOK());
        }
    }

    /**
     * 校验是否可以点击充值
     *
     * @return
     */
    private boolean isOK() {
        String paynum = etMoney.getText().toString().trim();

        if (TextUtils.isEmpty(paynum)) {
            tvCommit.setEnabled(false);
            rlTips.setVisibility(View.VISIBLE);
            return false;
        }
        double temp = 0.0;
        try {
            temp = Double.valueOf(paynum);
        } catch (Exception e) {
        }
        if (temp <= 0) {
            tvCommit.setEnabled(false);
            rlTips.setVisibility(View.VISIBLE);
            return false;
        }

        if (temp > 50000) {
            tvCommit.setEnabled(false);
            rlTips.setVisibility(View.VISIBLE);
            return false;
        }

        rlTips.setVisibility(View.GONE);
        boolean wx = this.wxPayTv.isEnabled();
        boolean zfb = this.zfbPayTv.isEnabled();
        boolean bank = this.bankPayTv.isEnabled();
        if (!wx && !zfb && !bank) {
            tvCommit.setEnabled(false);
            return false;
        }
        //传递到成功页面
        tempMoney = paynum;
        tvCommit.setEnabled(true);
        rlTips.setVisibility(View.GONE);
        return true;
    }

    @LiveDataMatch
    public void onSelectZFBSuccess(RspZFB data) {
        if (data == null) {
            showDialogToast("订单创建不成功!");
            return;
        }
        if (TextUtils.isEmpty(data.getRechargeUrl())) {
            showDialogToast("订单创建不成功!");
            return;
        }
        getViewModel().initAliPay(data.getRechargeUrl(), this);
    }

    @LiveDataMatch
    public void onSelectZFBError(String msg) {
        tvCommit.setEnabled(true);
        showDialogToast(msg);
    }

    @LiveDataMatch
    public void onSelectWXSuccess(RspWX data) {
        outTradeNo = data.getOutTradeNo();
        getViewModel().weChatPay(data, this, api);

    }

    @LiveDataMatch
    public void onSelectWXError(String msg) {
        tvCommit.setEnabled(true);
        showDialogToast(msg);
    }

    @LiveDataMatch
    public void onSelectBankSuccess(RspBank data) {
        //银行卡充值
        WisdomRechargeBankActivity.startContentUI(this, data.getRechargeUrl());
        finish();
    }

    @LiveDataMatch
    public void onSelectBankError(String msg) {
        tvCommit.setEnabled(true);
        showDialogToast(msg);
    }

    @LiveDataMatch
    public void onWXPaySuccess(ResultData data) {
        WisdomRechargeSuccessActivity.startContentUI(this, "微信支付", tempMoney);
        finish();
    }

    @LiveDataMatch
    public void onWXPayError(String msg) {
        tvCommit.setEnabled(true);
        showDialogToast(msg);
    }

    @LiveDataMatch
    public void onWxPayActionSuccess() {
        getViewModel().queryWXPayInfo(outTradeNo);
    }

    @LiveDataMatch
    public void onZFBPaySuccess() {
        //支付宝支付成功
        WisdomRechargeSuccessActivity.startContentUI(this, "支付宝支付", tempMoney);
        finish();
    }

    @LiveDataMatch
    public void onZFBPayError(String msg) {
        tvCommit.setEnabled(true);
        showDialogToast(msg);
    }
}
