package com.zczy.plugin.wisdom.home

import android.os.Bundle
import android.view.View
import com.jaeger.library.StatusBarUtil
import com.sfh.lib.mvvm.service.BaseViewModel
import com.zczy.comm.pluginserver.AMainServer
import com.zczy.comm.ui.BaseFragment
import com.zczy.plugin.wisdom.R
import com.zczy.plugin.wisdom.home.view.WisdomHomeToolbar
import kotlinx.android.synthetic.main.wisdom_first_child_failed_fragment.*

/**
 * 功能描述: 子帐号没有开通智运宝权限
 *
 * <AUTHOR>
 * @date 2022/3/7-19:12
 */
class WisdomFirstChildFailedFragment : BaseFragment<BaseViewModel>() {

    override fun getLayout(): Int {
        return R.layout.wisdom_first_child_failed_fragment
    }

    override fun bindView(view: View, bundle: Bundle?) {
        initView(view)
    }

    override fun initData() {}

    private fun initView(view: View) {
        StatusBarUtil.setTransparentForImageViewInFragment(activity, wisdom_main_tool_bar)
        wisdom_main_tool_bar.showCustomerService()
        wisdom_main_tool_bar.showChildIcon()
        wisdom_main_tool_bar.toolBarClickListener =
            object : WisdomHomeToolbar.ToolBarClickListener {
                override fun onClickCustomerService() {
                    val mainServer = AMainServer.getPluginServer()
                    mainServer?.showLineServerPhone(activity)
                }

                override fun onLeftClickListner() {}
                override fun onRightClickListner() {}
            }
    }

    companion object {
        @JvmStatic
        fun newInstance(): WisdomFirstChildFailedFragment {
            return WisdomFirstChildFailedFragment()
        }
    }
}