package com.zczy.plugin.wisdom.home

import android.os.Bundle
import android.view.View
import com.jaeger.library.StatusBarUtil
import com.sfh.lib.mvvm.annotation.LiveDataMatch
import com.zczy.comm.pluginserver.AMainServer
import com.zczy.comm.ui.BaseFragment
import com.zczy.plugin.wisdom.R
import com.zczy.plugin.wisdom.home.view.WisdomHomeToolbar
import com.zczy.plugin.wisdom.modle.home.WisdomFirstTrialFailModle
import com.zczy.plugin.wisdom.rsp.home.RspBanner
import com.zczy.plugin.wisdom.rsp.home.RspBannerImage
import kotlinx.android.synthetic.main.wisdom_first_trial_failed_fragment.*

/**
 * 功能描述: 初级页面
 * <AUTHOR>
 * @date 2022/3/7-19:48
 */

class WisdomFirstTrialFailedFragment : BaseFragment<WisdomFirstTrialFailModle?>() {
    override fun getLayout(): Int {
        return R.layout.wisdom_first_trial_failed_fragment
    }

    override fun bindView(view: View, bundle: Bundle?) {
        initView(view)
    }

    override fun initData() {
        val bannerDataArr: MutableList<RspBannerImage> = ArrayList()
        val rspBannerImage1 = RspBannerImage()
        rspBannerImage1.resId = R.drawable.wisdom_home_banner1
        val rspBannerImage2 = RspBannerImage()
        rspBannerImage2.resId = R.drawable.wisdom_home_banner2
        val rspBannerImage3 = RspBannerImage()
        rspBannerImage3.resId = R.drawable.wisdom_home_banner3
        bannerDataArr.add(rspBannerImage1)
        bannerDataArr.add(rspBannerImage2)
        bannerDataArr.add(rspBannerImage3)
        home_banner_view.update(bannerDataArr)
    }

    @LiveDataMatch
    open fun onQuerySuccess(data: RspBanner?) {
        if (data == null) {
            return
        }
        val bannerDataArr = data.bannerDataArr
        home_banner_view.update(bannerDataArr)
    }

    private fun initView(view: View) {
        StatusBarUtil.setTransparentForImageViewInFragment(activity, wisdom_main_tool_bar)
        wisdom_main_tool_bar.showCustomerService()
        wisdom_main_tool_bar.toolBarClickListener =
            object : WisdomHomeToolbar.ToolBarClickListener {
                override fun onClickCustomerService() {
                    val mainServer = AMainServer.getPluginServer()
                    mainServer?.showLineServerPhone(activity)
                }

                override fun onLeftClickListner() {
                    val mainServer = AMainServer.getPluginServer()
                    mainServer?.userAuthent(activity)
                }

                override fun onRightClickListner() {
                    val mainServer = AMainServer.getPluginServer()
                    mainServer?.userAuthent(activity)
                }
            }
        bindClickEvent(tv_member_state)
    }

    override fun onSingleClick(v: View) {
        super.onSingleClick(v)
        val i = v.id
        if (i == R.id.tv_member_state) {
            //判断认证状态
            val mainServer = AMainServer.getPluginServer()
            mainServer?.userAuthent(activity)
        }
    }

    override fun onHiddenChanged(hidden: Boolean) {
        super.onHiddenChanged(hidden)
        if (hidden) {
            //结束轮播
            home_banner_view.stopAutoPlay()
        } else {
            //开始轮播
            home_banner_view.startAutoPlay()
        }
    }

    companion object {
        @JvmStatic
        fun newInstance(): WisdomFirstTrialFailedFragment {
            return WisdomFirstTrialFailedFragment()
        }
    }
}