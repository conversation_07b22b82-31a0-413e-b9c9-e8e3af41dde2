package com.zczy.plugin.wisdom.home;

import android.content.Context;
import android.content.Intent;
import android.graphics.Color;
import android.os.Bundle;
import android.widget.TextView;

import com.sfh.lib.mvvm.annotation.LiveDataMatch;
import com.sfh.lib.ui.AbstractLifecycleActivity;
import com.zczy.comm.http.entity.PageList;
import com.zczy.comm.ui.UtilStatus;
import com.zczy.comm.widget.AppToolber;
import com.zczy.comm.widget.pulltorefresh.CommEmptyView;
import com.zczy.comm.widget.pulltorefresh.SwipeRefreshMoreLayout;
import com.zczy.plugin.wisdom.R;
import com.zczy.plugin.wisdom.home.adapter.WisdomAccountAdapter;
import com.zczy.plugin.wisdom.modle.home.WisdomAccountModle;
import com.zczy.plugin.wisdom.rsp.home.RspAccount;

import java.util.List;

/**
 * 功能描述: 余额不足页面
 *
 * <AUTHOR>
 * @company 中储智运科技股份有限公司
 * @copyright （版权）中储南京智慧物流科技有限公司所有
 * @date 2019/1/30 9:05
 */
public class WisdomAccoutActivity extends AbstractLifecycleActivity<WisdomAccountModle> {


    private AppToolber appToolber;
    private SwipeRefreshMoreLayout swipeRefreshMoreLayout;
    private WisdomAccountAdapter adapter = new WisdomAccountAdapter();
    private TextView tvMoney;

    public static void startContentUI(Context context) {
        Intent intent = new Intent(context, WisdomAccoutActivity.class);
        context.startActivity(intent);

    }

    @Override
    protected void onCreate(Bundle savedInstanceState) {
        super.onCreate(savedInstanceState);
        setContentView(R.layout.wisdom_account_activity);
        initView();
    }

    private void initView() {
        UtilStatus.initStatus(this, Color.WHITE);
        appToolber = findViewById(R.id.appToolber);
        swipeRefreshMoreLayout = findViewById(R.id.swipeRefreshMoreLayout);
        swipeRefreshMoreLayout.setAdapter(adapter, true);
        swipeRefreshMoreLayout.addItemDecorationSize(15);
        swipeRefreshMoreLayout.setOnLoadListener2(nowPage -> getViewModel().querySettle(nowPage));
        swipeRefreshMoreLayout.setEmptyView(CommEmptyView.creatorDef(this));
        swipeRefreshMoreLayout.onAutoRefresh();
        tvMoney = findViewById(R.id.tv_money);
    }

    @LiveDataMatch
    public void onQuerySuccess(PageList<RspAccount> data) {
        if (data != null) {
            List<RspAccount> list = data.getRootArray();
            if (list != null && list.size() > 0) {
                RspAccount rspAccount = list.get(0);
                tvMoney.setText(rspAccount.getTotalFeeToPay());
            }
        }
        this.swipeRefreshMoreLayout.onRefreshCompale(data);
    }

    @LiveDataMatch
    public void onQueryError() {
        this.swipeRefreshMoreLayout.onLoadMoreFail();
    }
}
