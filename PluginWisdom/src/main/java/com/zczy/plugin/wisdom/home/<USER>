package com.zczy.plugin.wisdom.home

import android.app.Activity
import android.content.Intent
import android.os.Bundle
import android.text.TextUtils
import android.view.View
import androidx.core.content.ContextCompat
import androidx.recyclerview.widget.LinearLayoutManager
import com.sfh.lib.mvvm.annotation.LiveDataMatch
import com.sfh.lib.mvvm.service.BaseViewModel
import com.sfh.lib.ui.dialog.DialogBuilder
import com.sfh.lib.ui.dialog.DialogBuilder.DialogInterface
import com.zczy.cargo_owner.libcomm.config.SubUserAuthUtils
import com.zczy.cargo_owner.libcomm.utils.getChildPermission
import com.zczy.cargo_owner.libcomm.widget.AgreeAdjustDetailDialog
import com.zczy.comm.CommServer
import com.zczy.comm.data.ReqQueryHaveOpen
import com.zczy.comm.data.entity.ELogin
import com.zczy.comm.data.help.isLogin
import com.zczy.comm.http.entity.PageList
import com.zczy.comm.pluginserver.AMainServer
import com.zczy.comm.ui.BaseFragment
import com.zczy.comm.utils.setVisible
import com.zczy.comm.utils.toJson
import com.zczy.comm.widget.dialog.ChooseDialogV1
import com.zczy.lib_zstatistics.sdk.ZStatistics
import com.zczy.plugin.wisdom.R
import com.zczy.plugin.wisdom.bond.WisdomBondListActivity
import com.zczy.plugin.wisdom.budget.WisdomBudgetListActivity
import com.zczy.plugin.wisdom.cash.WisdomCashActivity
import com.zczy.plugin.wisdom.earnest.model.PageListV2
import com.zczy.plugin.wisdom.home.adapter.WisdomHomeAdapter2
import com.zczy.plugin.wisdom.home.model.WisdomHomeModel2
import com.zczy.plugin.wisdom.home.req.AccountStatus
import com.zczy.plugin.wisdom.home.req.DepartmentLevel
import com.zczy.plugin.wisdom.home.req.ReqGetDeptAccountBookCardInfo
import com.zczy.plugin.wisdom.home.req.ReqQueryBookNameDictLists
import com.zczy.plugin.wisdom.home.req.ReqQueryDeptBooks
import com.zczy.plugin.wisdom.home.req.RspGetDeptAccountBookCardInfo
import com.zczy.plugin.wisdom.home.req.RspGetExistSubsidiaryList
import com.zczy.plugin.wisdom.home.req.RspQueryBookNameDictLists
import com.zczy.plugin.wisdom.home.req.RspQueryDeptBooks
import com.zczy.plugin.wisdom.home.req.WarningStatus
import com.zczy.plugin.wisdom.modle.home.WisdomHomeModel
import com.zczy.plugin.wisdom.password.WisdomCheckMobileActivity
import com.zczy.plugin.wisdom.req.ReqSendCodeWisdom
import com.zczy.plugin.wisdom.req.RspHomeCashState
import com.zczy.plugin.wisdom.scancash.authenticationdialog.WisdomAuthenticationDialog
import com.zczy.plugin.wisdom.scancash.req.ReqWisdomAuthenticaton
import com.zczy.plugin.wisdom.util.WisdomAddBankUtil
import kotlinx.android.synthetic.main.wisdom_home_fragment2.btn_continue_return
import kotlinx.android.synthetic.main.wisdom_home_fragment2.btn_fund_transfer
import kotlinx.android.synthetic.main.wisdom_home_fragment2.btn_view_details
import kotlinx.android.synthetic.main.wisdom_home_fragment2.etInputSearch
import kotlinx.android.synthetic.main.wisdom_home_fragment2.ivSwitchFragment2
import kotlinx.android.synthetic.main.wisdom_home_fragment2.iv_frozen_amount
import kotlinx.android.synthetic.main.wisdom_home_fragment2.recyclerView
import kotlinx.android.synthetic.main.wisdom_home_fragment2.tvHomeView1
import kotlinx.android.synthetic.main.wisdom_home_fragment2.tv_account_name
import kotlinx.android.synthetic.main.wisdom_home_fragment2.tv_account_number
import kotlinx.android.synthetic.main.wisdom_home_fragment2.tv_account_status
import kotlinx.android.synthetic.main.wisdom_home_fragment2.tv_available_amount
import kotlinx.android.synthetic.main.wisdom_home_fragment2.tv_company_name
import kotlinx.android.synthetic.main.wisdom_home_fragment2.tv_filter
import kotlinx.android.synthetic.main.wisdom_home_fragment2.tv_frozen_amount
import kotlinx.android.synthetic.main.wisdom_home_fragment2.tv_search
import kotlinx.android.synthetic.main.wisdom_home_fragment2.tv_total_amount
import kotlinx.android.synthetic.main.wisdom_home_fragment2.viewBondMoney
import kotlinx.android.synthetic.main.wisdom_home_fragment2.viewHomeLine1
import kotlinx.android.synthetic.main.wisdom_home_fragment2.viewHomeLine2
import kotlinx.android.synthetic.main.wisdom_home_fragment2.viewWarningMoney

/**
 *  desc: 【主】【包钢】独立部门资金管理专户
 *  user: 宋双朋
 *  time: 2025/6/17 16:17
 */
class WisdomHomeFragment2 : BaseFragment<WisdomHomeModel2>() {
    private var mAdapter2 = WisdomHomeAdapter2()
    private var searchBooNo: String? = null
    private var onSwitchBlock: () -> Unit = {}
    private var isBind = false
    private var subsidiaryList: MutableList<RspGetExistSubsidiaryList>? = null
    private var chooseData1 = mutableListOf<String>()//预警状态
    private var chooseData2 = mutableListOf<String>()//部门级别
    private var chooseData3 = mutableListOf<String>()//账户状态
    private var mRspGetDeptAccountBookCardInfo: RspGetDeptAccountBookCardInfo? = null

    companion object {
        fun newInstance(): WisdomHomeFragment2 {
            return WisdomHomeFragment2()
        }
    }

    override fun getLayout(): Int {
        return R.layout.wisdom_home_fragment2
    }

    override fun initData() {
        getViewModel(WisdomHomeModel2::class.java).initData()
    }

    override fun bindView(view: View, bundle: Bundle?) {
        mAdapter2.apply {
            if (!isBind) {
                bindToRecyclerView(recyclerView)
                isBind = true
            }
            setOnItemChildClickListener { _, view, position ->
                val item = mAdapter2.data[position]
                when (view.id) {
                    R.id.btn_transfer_in_1 -> {
                        //资金转入
                        AMainServer.getPluginServer().openRNActivity(
                            activity,
                            "SpecialFundsTransferredInPage",
                            "{\"subsidiaryId\":\"${item.subsidiaryId}\",\"bookName\":\"${item?.bookName}\",\"bookNo\":\"${item?.bookNo}\",\"depositMoney\":\"${item?.depositMoney}\",\"bookNameFa\":\"${mRspGetDeptAccountBookCardInfo?.bookName}\",\"bookNoFa\":\"${mRspGetDeptAccountBookCardInfo?.bookNo}\",\"depositMoneyFa\":\"${mRspGetDeptAccountBookCardInfo?.depositMoneyStr}\",\"subsidiaryIdFa\":\"${mRspGetDeptAccountBookCardInfo?.subsidiaryId}\",\"fundModeFa\":\"${mRspGetDeptAccountBookCardInfo?.fundMode}\",\"fundMode\":\"${item?.fundMode}\"}"
                        )
                    }

                    R.id.btn_view_details_1 -> {
                        //查看明细
                        WisdomBudgetListActivity.startContentUI(
                            activity,
                            item.bookNo,
                            item.fundMode,
                        )
                    }

                    R.id.viewBondMoneyChild -> {
                        //冻结金额
                        WisdomBondListActivity.jumpPage(
                            context = context,
                            bookNo = item.bookNo,
                            fundMode = item.fundMode
                        )
                    }
                }
            }
        }
        recyclerView.apply {
            layoutManager = LinearLayoutManager(<EMAIL>)
            adapter = mAdapter2
        }
        initListeners()

        if (SubUserAuthUtils.isChild()) {
            //交易明细查询权限
            val transactionDetailEnquiry =
                SubUserAuthUtils.get().transactionDetailEnquiry.getChildPermission().isEmpty()
            btn_view_details.setVisible(transactionDetailEnquiry)
            //结余退回权限
            val balanceRefund = SubUserAuthUtils.get().balanceRefund.getChildPermission().isEmpty()
            btn_continue_return.setVisible(balanceRefund)
            //资金划拨权限
            val fundTransfer = SubUserAuthUtils.get().fundTransfer.getChildPermission().isEmpty()
            btn_fund_transfer.setVisible(fundTransfer)
            viewHomeLine1.setVisible(fundTransfer && (balanceRefund || transactionDetailEnquiry))
            if (transactionDetailEnquiry && balanceRefund && fundTransfer) {
                viewHomeLine2.setVisible(true)
            } else {
                viewHomeLine2.setVisible(false)
            }
        }
    }


    private fun initListeners() {
        //切换主体
        bindClickEvent(tv_account_status)
        // 冻结金额
        bindClickEvent(viewBondMoney)
        // 预警金额
        bindClickEvent(viewWarningMoney)
        // 切换账本
        bindClickEvent(ivSwitchFragment2)
        // 资金划拨
        bindClickEvent(btn_fund_transfer)
        // 结余退回
        bindClickEvent(btn_continue_return)
        // 查看明细
        bindClickEvent(btn_view_details)
        // 搜索
        bindClickEvent(tv_search)
        // 筛选
        bindClickEvent(tv_filter)
    }

    override fun onSingleClick(v: View) {
        super.onSingleClick(v)
        when (v.id) {
            R.id.tv_filter -> {
                //筛选
                WisdomHomeFilterActivity2.jumpPage(
                    fragment = this@WisdomHomeFragment2,
                    subsidiaryId = mRspGetDeptAccountBookCardInfo?.subsidiaryId ?: "",
                    data1 = chooseData1.toJson(),
                    data2 = chooseData2.toJson(),
                    data3 = chooseData3.toJson()
                )
            }

            R.id.tv_search -> {
                //搜索
                val bookName = etInputSearch.text.trim().toString()
                if (TextUtils.isEmpty(bookName)) {
                    searchBooNo = null
                    queryDeptBooks()
                } else {
                    getViewModel(WisdomHomeModel2::class.java).queryBookNameDictLists(
                        req = ReqQueryBookNameDictLists(
                            subsidiaryId = mRspGetDeptAccountBookCardInfo?.subsidiaryId
                        )
                    )
                }
            }

            R.id.btn_view_details -> {
                //查看明细
                WisdomBudgetListActivity.startContentUI(
                    activity,
                    mRspGetDeptAccountBookCardInfo?.bookNo,
                    mRspGetDeptAccountBookCardInfo?.fundMode,
                )
            }

            R.id.btn_continue_return -> {
                //结余退回
                if (TextUtils.equals("3", mRspGetDeptAccountBookCardInfo?.freezeType)) {
                    val dialogBuilder = DialogBuilder()
                    dialogBuilder.message = "该账户已冻结，冻结类型为不收不付，不支持结余退回"
                    dialogBuilder.cancelText = "咨询在线客服"
                    dialogBuilder.title = "提示"
                    dialogBuilder.setOKText("确认")
                    dialogBuilder.okListener = DialogInterface.OnClickListener { p, _ ->
                        p?.dismiss()
                    }
                    dialogBuilder.cancelListener = DialogInterface.OnClickListener { p, _ ->
                        p?.dismiss()
                        ZStatistics.onViewClick(this@WisdomHomeFragment2, "tv_openLineServer")
                        val mainServer = AMainServer.getPluginServer()
                        mainServer?.openLineServerHaveParams(activity, "27")
                    }
                    showDialog(dialogBuilder)
                    return
                }
                if (SubUserAuthUtils.isChild()) {
                    //子账号
                    val iPayGetCash = SubUserAuthUtils.get().ipayGetCash.getChildPermission()
                    if (iPayGetCash.isEmpty()) {
                        getViewModel(WisdomHomeModel::class.java).checkCashOptEnable(
                            bookNo = mRspGetDeptAccountBookCardInfo?.bookNo,
                            fundMode = mRspGetDeptAccountBookCardInfo?.fundMode,
                            context = context
                        )
                    } else {
                        showDialogToast(iPayGetCash)
                        return
                    }

                } else {
                    getViewModel(WisdomHomeModel::class.java).checkCashOptEnable(
                        bookNo = mRspGetDeptAccountBookCardInfo?.bookNo,
                        fundMode = mRspGetDeptAccountBookCardInfo?.fundMode,
                        context = context
                    )
                }
            }

            R.id.viewWarningMoney -> {
                //预警金额
                WisdomBondListActivity.jumpPage(
                    context = context,
                    bookNo = mRspGetDeptAccountBookCardInfo?.bookNo,
                    fundMode = mRspGetDeptAccountBookCardInfo?.fundMode
                )
            }

            R.id.viewBondMoney -> {
                //冻结金额
                WisdomBondListActivity.jumpPage(
                    context = context,
                    bookNo = mRspGetDeptAccountBookCardInfo?.bookNo,
                    fundMode = mRspGetDeptAccountBookCardInfo?.fundMode
                )
            }

            R.id.ivSwitchFragment2 -> {
                //切换账本
                onSwitchBlock()
            }

            R.id.btn_fund_transfer -> {
                //资金划拨
                AMainServer.getPluginServer().openRNActivity(
                    activity,
                    "SpecialTransferOfFundsPage",
                    "{\"subsidiaryId\":\"${mRspGetDeptAccountBookCardInfo?.subsidiaryId}\"}"
                )
            }

            R.id.tv_account_status -> {
                //切换主体
                ChooseDialogV1.instance(subsidiaryList ?: mutableListOf())
                    .setFlatMap { subsidiaryName ?: "" }
                    .setClick { rspGetExistSubsidiaryList, _ ->
                        chooseData1.clear()
                        chooseData2.clear()
                        chooseData3.clear()
                        etInputSearch.setText("")
                        loadData(subsidiaryId = rspGetExistSubsidiaryList.subsidiaryId, subsidiaryName = rspGetExistSubsidiaryList.subsidiaryName)
                    }
                    .show(this@WisdomHomeFragment2)
            }
        }
    }

    override fun onResume() {
        super.onResume()
        mRspGetDeptAccountBookCardInfo?.let {
            loadData(subsidiaryId = it.subsidiaryId, subsidiaryName = it.subsidiaryName)
        }
    }

    private fun loadData(subsidiaryId: String?, subsidiaryName: String?) {
        //查询部门专户卡片信息
        getViewModel(WisdomHomeModel2::class.java).getDeptAccountBookCardInfo(
            req = ReqGetDeptAccountBookCardInfo(
                subsidiaryId = subsidiaryId,
                subsidiaryName = subsidiaryName
            )
        )
        //查询部门专户账户信息
        getViewModel(WisdomHomeModel2::class.java).queryDeptBooks(
            req = ReqQueryDeptBooks(
                subsidiaryId = subsidiaryId,
                bookNo = searchBooNo,
                warningSign = chooseData1.getWarningSign(),
                deptLevelArr = chooseData2.getDeptLevelArr(),
                stateArr = chooseData3.getStateArr(),
            )
        )
    }

    override fun onActivityResult(requestCode: Int, resultCode: Int, data: Intent?) {
        super.onActivityResult(requestCode, resultCode, data)
        if (resultCode == Activity.RESULT_OK) {
            when (requestCode) {
                WisdomHomeFilterActivity2.WISDOM_HOME_FILTER_REQUEST_CODE -> {
                    val data1 = WisdomHomeFilterActivity2.obtainData1(data)
                    val data2 = WisdomHomeFilterActivity2.obtainData2(data)
                    val data3 = WisdomHomeFilterActivity2.obtainData3(data)
                    chooseData1 = data1 ?: mutableListOf()
                    chooseData2 = data2 ?: mutableListOf()
                    chooseData3 = data3 ?: mutableListOf()
                    //刷新专户公司信息
                    queryDeptBooks()
                }
            }
        }
    }

    private fun MutableList<String>.getStateArr(): MutableList<String> {
        return this.map {
            when (it) {
                AccountStatus.NORMAL.status -> "1"
                AccountStatus.FROZEN.status -> "2"
                AccountStatus.CANCELED.status -> "3"
                else -> ""
            }
        }.toMutableList()
    }

    private fun MutableList<String>.getDeptLevelArr(): MutableList<String> {
        return this.map {
            when (it) {
                DepartmentLevel.LEVEL_ONE.level -> "1"
                DepartmentLevel.LEVEL_TWO.level -> "2"
                DepartmentLevel.LEVEL_THREE.level -> "3"
                DepartmentLevel.LEVEL_FOUR.level -> "4"
                DepartmentLevel.LEVEL_FIVE.level -> "5"
                else -> ""
            }
        }.toMutableList()
    }

    private fun MutableList<String>.getWarningSign(): String? {
        if (this.size >= 2 || this.isEmpty()) {
            return null
        } else {
            val warnings = this[0]
            return if (TextUtils.equals(warnings, WarningStatus.WARNING.status)) {
                "1"
            } else if (TextUtils.equals(warnings, WarningStatus.NO_WARNING.status)) {
                "0"
            } else {
                null
            }
        }
    }

    @LiveDataMatch(tag = "根据账本名称查询")
    open fun queryBookNameDictListsSuccess(data: PageList<RspQueryBookNameDictLists>) {
        val list = data.rootArray ?: mutableListOf()
        val list1 = list.filterSearchList()
        if (list1.isEmpty()) {
            showDialogToast("未查询到数据!")
            return
        }
        ChooseDialogV1.instance(list1)
            .setFlatMap { text ?: "" }
            .setClick { rspQueryDeptBooks, _ ->
                searchBooNo = rspQueryDeptBooks.value
                queryDeptBooks()
            }
            .show(this@WisdomHomeFragment2)
    }

    private fun queryDeptBooks() {
        getViewModel(WisdomHomeModel2::class.java).queryDeptBooks(
            req = ReqQueryDeptBooks(
                subsidiaryId = mRspGetDeptAccountBookCardInfo?.subsidiaryId,
                bookNo = searchBooNo,
                warningSign = chooseData1.getWarningSign(),
                deptLevelArr = chooseData2.getDeptLevelArr(),
                stateArr = chooseData3.getStateArr(),
            )
        )
    }

    @LiveDataMatch(tag = "查询存在部门专户的主体")
    open fun getExistSubsidiaryListSuccess(data: PageListV2<RspGetExistSubsidiaryList>) {
        subsidiaryList = data.rootArray
        tv_account_status.setVisible(!subsidiaryList.isNullOrEmpty())
    }

    @LiveDataMatch(tag = "查询部门专户卡片信息")
    open fun getDeptAccountBookCardInfoSuccess(data: RspGetDeptAccountBookCardInfo) {
        mRspGetDeptAccountBookCardInfo = data
        //主体账户名称
        tv_company_name.text = data.subsidiaryName
        //余额查询权限
        val amountInquiry = SubUserAuthUtils.get().amountInquiry.getChildPermission().isEmpty()
        if (amountInquiry) {
            //可用金额
            tv_available_amount.text = data.depositMoneyStr
        } else {
            //可用金额
            tv_available_amount.text = "--"
        }
        //部门
        tvHomeView1.text = getPartName(partType = data.deptLevel)
        tvHomeView1.setTextColor(getPartColor(partType = data.deptLevel))
        tvHomeView1.setBackgroundResource(getPartRes(partType = data.deptLevel))
        //账户名称
        tv_account_name.text = data.bookName
        //账户号
        tv_account_number.text = data.bookNo
        viewBondMoney.isEnabled = amountInquiry
        iv_frozen_amount.setVisible(amountInquiry)
        if (amountInquiry) {
            //冻结金额
            tv_frozen_amount.text = data.freezeMoneyStr
        } else {
            //冻结金额
            tv_frozen_amount.text = "--"
        }
        //预警金额
        tv_total_amount.text = data.warningMoneyStr
        //结余退回
        btn_continue_return.setVisible(data.showRefundBtn)
        //分割线
        viewHomeLine1.setVisible(data.showRefundBtn)
    }

    @LiveDataMatch(tag = "查询部门专户账户信息")
    open fun queryDeptBooksSuccess(data: PageList<RspQueryDeptBooks>) {
        mAdapter2.setNewData(data.rootArray)
    }

    @LiveDataMatch
    open fun onCheckCashOptEnableSuccess(data: RspHomeCashState) {
        //  0：可以提现 1：没有设置提现密码 2：未认证/认证失败 3：认证资料审核中 4：没有绑定银行卡 5：此人没有提现权限
        val checkState = data.checkState
        if (TextUtils.equals(checkState, "0")) {
            //调用Hue 判断是否绑定设备
            getViewModel(WisdomHomeModel::class.java).checkAuthenticationMac()
        }

        if (TextUtils.equals(checkState, "1")) {
            val dialogBuilder = DialogBuilder()
                .setTitle("提示")
                .setOKTextColor("去设置", R.color.color_5086fc)
                .setMessage("为保护您的账户安全请设置资金密码!")
                .setCancelTextColor("取消", R.color.color_5086fc)
                .setOkListener { dialogInterface, _ ->
                    dialogInterface.dismiss()
                    WisdomCheckMobileActivity.startContentUI(activity, "1")
                }
            showDialog(dialogBuilder)
        }

        if (TextUtils.equals(checkState, "4")) {
            val dialogBuilder = DialogBuilder()
                .setTitle("提示")
                .setOKTextColor("去绑卡", R.color.color_5086fc)
                .setMessage("绑卡后可用于金额的转出和结算!")
                .setCancelTextColor("取消", R.color.color_5086fc)
                .setOkListener { dialogInterface, _ ->
                    dialogInterface.dismiss()
                    WisdomAddBankUtil.addBank(activity)
                }
            showDialog(dialogBuilder)
        }

        if (TextUtils.equals(checkState, "5")) {
            AgreeAdjustDetailDialog("暂无权限使用该功能，请联系主账号分配功能权限！").show(this)
        }

    }

    @LiveDataMatch
    open fun onCheckAuthenticationMacSuccess() {
        toCash()
    }

    private fun toCash() {
        context?.let {
            WisdomCashActivity.startContentUI(
                context = it,
                accoutMoney = mRspGetDeptAccountBookCardInfo?.depositMoney ?: "0.00",
                bookNo = mRspGetDeptAccountBookCardInfo?.bookNo,
                fundMode = mRspGetDeptAccountBookCardInfo?.fundMode,
                transactionType = mRspGetDeptAccountBookCardInfo?.transactionType
            )
        }
    }

    @LiveDataMatch
    open fun onCheckAuthenticationMacError() {
        isLogin(this@WisdomHomeFragment2) {
            //升级高级角色页面
            val mainServer = AMainServer.getPluginServer()
            if (mainServer != null) {
                val login = CommServer.getUserServer().login
                getViewModel(WisdomHomeModel::class.java).sendAuthenticationSMS(login)
            }
        }
    }

    private var wisdomAuthenticationDialog: WisdomAuthenticationDialog =
        WisdomAuthenticationDialog()

    @LiveDataMatch(tag = "新设备认证短信发送成功")
    open fun onSendAuthenticationCodeSuccess(login: ELogin) {
        //设备认证提示
        wisdomAuthenticationDialog.setListener(object : WisdomAuthenticationDialog.Listener {
            override fun onReSendCode(data: ELogin) {
                // 重新发送验证码
                getViewModel(WisdomHomeModel::class.java).sendAuthenticationSMS(data)
            }

            override fun onCommit(data: ELogin, code: String) {
                // 认证
                val reqWisdomAuthenticaton = ReqWisdomAuthenticaton()
                reqWisdomAuthenticaton.verifyCode = code
                reqWisdomAuthenticaton.moduleType = ReqSendCodeWisdom.MODULE_TYPE_M
                reqWisdomAuthenticaton.verifyCodeType = ReqSendCodeWisdom.TYPE_SMS
                reqWisdomAuthenticaton.mobile = data.mobile
                getViewModel(WisdomHomeModel::class.java).checkAuthenticationCode(
                    reqWisdomAuthenticaton
                )
            }

            override fun onClose() {
                wisdomAuthenticationDialog.dismiss()
            }

        })
        this.wisdomAuthenticationDialog.setLogin(login)
        if (!wisdomAuthenticationDialog.isVisible) {
            wisdomAuthenticationDialog.showDialog(this)
        }
    }

    @LiveDataMatch(tag = "验证设备验证码成功")
    open fun onCheckAuthenticationCodeSuccess() {
        wisdomAuthenticationDialog.dismiss()
        toCash()
    }

    fun setOnSwitchBlock(block: () -> Unit) {
        onSwitchBlock = block
    }

    private fun getPartRes(partType: String?): Int {
        return when (partType) {
            "1" -> R.drawable.wisdom_home_icon_6
            "2" -> R.drawable.wisdom_home_icon_7
            "3" -> R.drawable.wisdom_home_icon_8
            "4" -> R.drawable.wisdom_home_icon_9
            "5" -> R.drawable.wisdom_home_icon_10
            else -> 0
        }
    }

    private fun getPartColor(partType: String?): Int {
        return context?.let {
            when (partType) {
                "1" -> ContextCompat.getColor(it, R.color.color_ffffff)
                "2" -> ContextCompat.getColor(it, R.color.color_ffffff)
                "3" -> ContextCompat.getColor(it, R.color.color_ffffff)
                "4" -> ContextCompat.getColor(it, R.color.color_ffffff)
                "5 " -> ContextCompat.getColor(it, R.color.color_ffffff)
                else -> 0
            }
        } ?: 0
    }

    private fun getPartName(partType: String?): String {
        return when (partType) {
            "1" -> "一级部门"
            "2" -> "二级部门"
            "3" -> "三级部门"
            "4" -> "四级部门"
            "5" -> "五级部门"
            else -> ""
        }
    }

    private fun MutableList<RspQueryBookNameDictLists>.filterSearchList(): MutableList<RspQueryBookNameDictLists> {
        if (this.isEmpty()) {
            return this
        } else {
            val bookName = etInputSearch.text.trim().toString()
            return this.filter { it.text?.contains(bookName) ?: false }.toMutableList()
        }
    }
}