package com.zczy.plugin.wisdom.home

import android.annotation.SuppressLint
import android.app.Activity
import android.content.Intent
import android.os.Bundle
import android.view.View
import androidx.fragment.app.Fragment
import com.sfh.lib.mvvm.service.BaseViewModel
import com.zczy.comm.SpannableHepler
import com.zczy.comm.ui.BaseActivity
import com.zczy.comm.utils.toJson
import com.zczy.comm.utils.toJsonArray
import com.zczy.comm.widget.ChooseGridView
import com.zczy.plugin.wisdom.R
import com.zczy.plugin.wisdom.home.req.ReqQueryDeptLevelDictLists
import kotlinx.android.synthetic.main.wisdom_home_filter_activty2.chooseGridView1
import kotlinx.android.synthetic.main.wisdom_home_filter_activty2.chooseGridView2
import kotlinx.android.synthetic.main.wisdom_home_filter_activty2.chooseGridView3
import kotlinx.android.synthetic.main.wisdom_home_filter_activty2.tvLeft
import kotlinx.android.synthetic.main.wisdom_home_filter_activty2.tvRight

/**
 *  desc: 发票管家-筛选
 *  user: 宋双朋
 *  time: 2024/9/23 15:14
 */

@SuppressLint("SetTextI18n")
class WisdomHomeFilterActivity2 : BaseActivity<BaseViewModel>() {
    private var chooseData1 = mutableListOf<String>()//预警状态
    private var chooseData2 = mutableListOf<String>()//部门级别
    private var chooseData3 = mutableListOf<String>()//账户状态
    private val subsidiaryId by lazy { intent.getStringExtra(SUBSIDIARY_ID) }

    companion object {
        private const val SUBSIDIARY_ID = "subsidiaryId"
        private const val EXTRA_DATA_1 = "extraData1"
        private const val EXTRA_DATA_2 = "extraData2"
        private const val EXTRA_DATA_3 = "extraData3"
        const val WISDOM_HOME_FILTER_REQUEST_CODE = 0x11

        @JvmStatic
        fun jumpPage(fragment: Fragment, subsidiaryId: String, data1: String, data2: String, data3: String) {
            val intent = Intent(fragment.context, WisdomHomeFilterActivity2::class.java)
            intent.putExtra(SUBSIDIARY_ID, subsidiaryId)
            intent.putExtra(EXTRA_DATA_1, data1)
            intent.putExtra(EXTRA_DATA_2, data2)
            intent.putExtra(EXTRA_DATA_3, data3)
            fragment.startActivityForResult(intent, WISDOM_HOME_FILTER_REQUEST_CODE)
        }

        @JvmStatic
        fun obtainData1(intent: Intent?): MutableList<String>? {
            return intent?.getStringExtra(EXTRA_DATA_1).toJsonArray(String::class.java)
        }

        @JvmStatic
        fun obtainData2(intent: Intent?): MutableList<String>? {
            return intent?.getStringExtra(EXTRA_DATA_2).toJsonArray(String::class.java)
        }

        @JvmStatic
        fun obtainData3(intent: Intent?): MutableList<String>? {
            return intent?.getStringExtra(EXTRA_DATA_3).toJsonArray(String::class.java)
        }
    }

    override fun getLayout(): Int {
        return R.layout.wisdom_home_filter_activty2
    }

    override fun bindView(bundle: Bundle?) {
        bindClickEvent(tvLeft)
        bindClickEvent(tvRight)
    }

    override fun initData() {
        chooseData1 = obtainData1(intent) ?: mutableListOf()
        chooseData2 = obtainData2(intent) ?: mutableListOf()
        chooseData3 = obtainData3(intent) ?: mutableListOf()
        //预警状态
        chooseGridView1.setGridCount(3)
        chooseGridView1.setChooseData(chooseData1)
        chooseGridView1.setData(mutableListOf("预警中", "未预警"))
        val sh1 = SpannableHepler().append(SpannableHepler.Txt("预警状态", "#333333", false, true))
            .builder()
        chooseGridView1.setTitle(title = sh1)
        chooseGridView1.listener = object : ChooseGridView.Listener {
            override fun onChoose(list: MutableList<String>) {
                chooseData1 = list
            }
        }
        //部门级别
        chooseGridView2.setGridCount(3)
        chooseGridView2.setChooseData(chooseData2)
        getViewModel(BaseViewModel::class.java).execute(
            ReqQueryDeptLevelDictLists(
                subsidiaryId = subsidiaryId ?: ""
            )
        ) {
            if (it.success()) {
                chooseGridView2.setData(it.data?.rootArray?.map { mp -> getPartName(mp.value) }?.toMutableList())
            } else {
                showToast(it.msg)
            }
        }
        val sh2 = SpannableHepler().append(SpannableHepler.Txt("部门级别", "#333333", false, true))
            .builder()
        chooseGridView2.setTitle(title = sh2)
        chooseGridView2.listener = object : ChooseGridView.Listener {
            override fun onChoose(list: MutableList<String>) {
                chooseData2 = list
            }
        }
        //账户状态
        chooseGridView3.setGridCount(3)
        chooseGridView3.setChooseData(chooseData3)
        chooseGridView3.setData(mutableListOf("正常", "冻结", "注销"))
        val sh3 = SpannableHepler().append(SpannableHepler.Txt("账户状态", "#333333", false, true))
            .builder()
        chooseGridView3.setTitle(title = sh3)
        chooseGridView3.listener = object : ChooseGridView.Listener {
            override fun onChoose(list: MutableList<String>) {
                chooseData3 = list
            }
        }
    }

    override fun onSingleClick(v: View) {
        super.onSingleClick(v)
        when (v.id) {
            R.id.tvLeft -> {
                chooseData1.clear()
                chooseData2.clear()
                chooseData3.clear()
                chooseGridView1.setChooseData(chooseData1)
                chooseGridView2.setChooseData(chooseData2)
                chooseGridView3.setChooseData(chooseData3)
            }

            R.id.tvRight -> {
                intent.putExtra(EXTRA_DATA_1, chooseData1.toJson())
                intent.putExtra(EXTRA_DATA_2, chooseData2.toJson())
                intent.putExtra(EXTRA_DATA_3, chooseData3.toJson())
                setResult(Activity.RESULT_OK, intent)
                finish()
            }
        }
    }

    private fun getPartName(partType: String?): String {
        return when (partType) {
            "1" -> "一级部门"
            "2" -> "二级部门"
            "3" -> "三级部门"
            "4" -> "四级部门"
            "5" -> "五级部门"
            else -> ""
        }
    }
}