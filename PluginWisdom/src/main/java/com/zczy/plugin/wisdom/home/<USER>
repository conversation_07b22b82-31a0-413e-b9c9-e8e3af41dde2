package com.zczy.plugin.wisdom.home;

import android.content.Context;
import android.content.Intent;
import android.graphics.Color;
import android.os.Bundle;
import android.text.TextUtils;
import android.view.View;
import android.widget.RelativeLayout;

import com.sfh.lib.mvvm.annotation.LiveDataMatch;
import com.sfh.lib.ui.AbstractLifecycleActivity;
import com.zczy.comm.ui.UtilStatus;
import com.zczy.comm.widget.AppToolber;
import com.zczy.plugin.wisdom.R;
import com.zczy.plugin.wisdom.bank.WisdomBankListActivity;
import com.zczy.plugin.wisdom.modle.password.WisdomManageModle;
import com.zczy.plugin.wisdom.password.WisdomCheckMobileActivity;
import com.zczy.plugin.wisdom.password.WisdomManagePwdActivity;
import com.zczy.plugin.wisdom.rsp.home.RspCheckSetPassWord;

/**
 * <AUTHOR>
 * @description (支付宝设置)
 * @company 中储智运科技股份有限公司
 * @copyright （版权）中储南京智慧物流科技有限公司所有
 * @time 2018/11/15 14:09
 */

public class WisdomManageActivity extends AbstractLifecycleActivity<WisdomManageModle> implements View.OnClickListener {


    private AppToolber appToolber;
    private RelativeLayout rlManageBank;
    private RelativeLayout rlSetPwd;

    /**
     * 页面跳转
     *
     * @param context
     */
    public static void startContentUI(Context context) {
        Intent intent = new Intent(context, WisdomManageActivity.class);
        context.startActivity(intent);

    }

    @Override
    protected void onCreate(Bundle savedInstanceState) {
        super.onCreate(savedInstanceState);
        setContentView(R.layout.wisdom_manage_activity);
        initView();
    }

    private void initView() {
        UtilStatus.initStatus(this, Color.WHITE);
        appToolber = (AppToolber) findViewById(R.id.appToolber);
        appToolber.setTitle("智运宝设置");
        rlManageBank = (RelativeLayout) findViewById(R.id.rl_manage_bank);
        rlManageBank.setOnClickListener(this);
        rlSetPwd = (RelativeLayout) findViewById(R.id.rl_set_pwd);
        rlSetPwd.setOnClickListener(this);
    }

    @Override
    public void onClick(View view) {
        if (view.getId() == R.id.rl_manage_bank) {
            //  银行卡管理
            WisdomBankListActivity.startContentUI(this);
        }
        if (view.getId() == R.id.rl_set_pwd) {
            // 设置支付密码
            getViewModel().checkCashOptEnable();
        }

    }

    @LiveDataMatch
    public void onCheckSetPassWordSuccess(RspCheckSetPassWord data) {
        //  1-设置过 0-未设置
        String setPwdFlag = data.getSetPwdFlag();
        if (TextUtils.equals(setPwdFlag, "0")) {
            WisdomCheckMobileActivity.startContentUI(this, "1");
        } else {
            WisdomManagePwdActivity.startContentUI(this, true);
        }
    }
}
