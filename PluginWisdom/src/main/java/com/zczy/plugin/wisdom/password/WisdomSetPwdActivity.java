package com.zczy.plugin.wisdom.password;

import android.content.Context;
import android.content.Intent;
import android.graphics.Color;
import android.os.Bundle;
import android.text.TextUtils;
import android.view.View;
import android.widget.Button;
import android.widget.TextView;

import com.sfh.lib.event.RxBusEventManager;
import com.sfh.lib.mvvm.annotation.LiveDataMatch;
import com.sfh.lib.ui.AbstractLifecycleActivity;
import com.sfh.lib.ui.dialog.DialogBuilder;
import com.zczy.comm.ui.UtilStatus;
import com.zczy.comm.widget.AppToolber;
import com.zczy.plugin.wisdom.R;
import com.zczy.plugin.wisdom.home.view.WisdomKeyboard;
import com.zczy.plugin.wisdom.home.view.WisdomPayEditText;
import com.zczy.plugin.wisdom.modle.password.WisdomSetPassWordModle;
import com.zczy.plugin.wisdom.postdata.RxCloseMessageActivity;
import com.zczy.plugin.wisdom.req.password.ReqSetPwd;
import com.zczy.comm.utils.Md5Util;

/**
 * <AUTHOR>
 * @description (设置支付密码 忘记支付密码)
 * @company 中储智运科技股份有限公司
 * @copyright （版权）中储南京智慧物流科技有限公司所有
 * @time 2018/11/15 14:09
 */

public class WisdomSetPwdActivity extends AbstractLifecycleActivity<WisdomSetPassWordModle> implements View.OnClickListener, WisdomKeyboard.OnClickKeyboardListener, WisdomPayEditText.OnInputFinishedListener {

    private AppToolber appToolber;
    private TextView tvTopTitle;
    private WisdomPayEditText wisdomEt;
    private TextView tvBottomTitle;
    private WisdomKeyboard wisdomKb;
    private Button btSuccess;
    private String firstPwd;
    private String twoPwd;
    private String token;

    public static void startContentUI(Context context, String token) {
        Intent intent = new Intent(context, WisdomSetPwdActivity.class);
        intent.putExtra("token", token);
        context.startActivity(intent);

    }

    @Override
    protected void onCreate(Bundle savedInstanceState) {
        super.onCreate(savedInstanceState);
        setContentView(R.layout.wisdom_set_pwd_activity);
        token = getIntent().getStringExtra("token");
        initView();
        initListener();
    }

    /**
     * 初始化点击事件
     */
    private void initListener() {
        btSuccess.setOnClickListener(this);
        wisdomKb.setOnClickKeyboardListener(this);
        wisdomEt.setOnInputFinishedListener(this);
        wisdomEt.setInputType("1");
        wisdomKb.setInputType("1");
    }

    private void initView() {
        UtilStatus.initStatus(this, Color.WHITE);
        appToolber = (AppToolber) findViewById(R.id.appToolber);
        tvTopTitle = (TextView) findViewById(R.id.tv_top_title);
        wisdomEt = (WisdomPayEditText) findViewById(R.id.wisdom_et);
        tvBottomTitle = (TextView) findViewById(R.id.tv_bottom_title);
        wisdomKb = (WisdomKeyboard) findViewById(R.id.wisdom_kb);
        btSuccess = (Button) findViewById(R.id.bt_success);
    }

    @Override
    public void onClick(View view) {
        if (view.getId() == R.id.bt_success) {
            // 这里做设置密码的操作
            ReqSetPwd reqSetPwd = new ReqSetPwd();
            reqSetPwd.setUserPwd(Md5Util.mmd5(firstPwd));
            reqSetPwd.setToken(token);
            getViewModel().setPassWord(reqSetPwd);
        }
    }

    @Override
    public void onKeyClick(String pwd) {
        //点击0 - 9 按钮
        wisdomEt.add(pwd);
    }

    @Override
    public void onKeyDeleteClick(String inputType) {
        //点击 删除按钮
        wisdomEt.removeAll();
        //密码不够6位时设置提交按钮不可点击
        btSuccess.setEnabled(false);
        if (TextUtils.equals("1", inputType)) {
            //清空第一次输入的密码
            firstPwd = "";
        }
        if (TextUtils.equals("2", inputType)) {
            //清空第二次输入的密码
            twoPwd = "";
        }
    }

    @Override
    public void onInputFinished(String password, String inputType) {
        //输入完成监听
        //输入完成监听
        if (TextUtils.equals("1", inputType)) {
            //如果是第一次输入完成 展示完成按钮不可点击
            firstPwd = password;
            //展示完成按钮
            btSuccess.setVisibility(View.VISIBLE);
            btSuccess.setEnabled(false);
            //同时清空第一次输入
            wisdomEt.removeAll();
            //设置输入提示信息
            tvTopTitle.setText("再次输入资金密码");
            tvBottomTitle.setVisibility(View.GONE);
            wisdomEt.setInputType("2");
            wisdomKb.setInputType("2");
        } else if (TextUtils.equals("2", inputType)) {
            twoPwd = password;
            //如果是第二次输入 校验两次密码是否一致
            if (TextUtils.equals(firstPwd, twoPwd)) {
                //相同 完成可以点击
                btSuccess.setEnabled(true);
            } else {
                //否则返回上一步 重新输入
                btSuccess.setVisibility(View.GONE);
                tvBottomTitle.setVisibility(View.VISIBLE);
                firstPwd = "";
                twoPwd = "";
                wisdomEt.setInputType("2");
                wisdomKb.setInputType("2");
                wisdomEt.removeAll();
            }
        }
    }

    @LiveDataMatch
    public void onSetPassWordSuccess(String msg) {
        //设置支付密码成功
        DialogBuilder dialogBuilder = new DialogBuilder().setMessage(msg).
                setTitle("提示").
                setCancelable(false).setOKTextColor("我知道了", R.color.color_5086fc).
                setOkListener((dialogInterface, i) -> {
                    dialogInterface.dismiss();
                    RxCloseMessageActivity rxCloseMessageActivity = new RxCloseMessageActivity();
                    rxCloseMessageActivity.setClose(true);
                    RxBusEventManager.postEvent(rxCloseMessageActivity);
                    finish();
                }).setHideCancel(true);
        showDialog(dialogBuilder);

    }

    @LiveDataMatch
    public void onSetPassWordError(String error) {
        //设置支付密码成功
        commitPwdErrorAlert(error);
    }

    /**
     * 提交
     *
     * @param error
     */
    public void commitPwdErrorAlert(String error) {
        DialogBuilder dialogBuilder = new DialogBuilder().setTitle("提示").
                setMessage(error).
                setCancelTextColor("重新设置", R.color.color_5086fc).
                setOKTextColor("重新提交", R.color.color_5086fc).setCancelListener((dialogInterface, i) -> {
            //重新设置密码
            btSuccess.setEnabled(false);
            wisdomEt.setInputType("1");
            wisdomKb.setInputType("1");
            firstPwd = "";
            twoPwd = "";
            wisdomEt.removeAll();
            tvTopTitle.setText("请输入新的资金密码，用于金融交易转出");
            tvBottomTitle.setVisibility(View.GONE);
            dialogInterface.dismiss();
        }).setOkListener((dialogInterface, i) -> {
            dialogInterface.dismiss();
            //重新提交
            ReqSetPwd reqSetPwd = new ReqSetPwd();
            reqSetPwd.setUserPwd(Md5Util.mmd5(firstPwd));
            reqSetPwd.setToken(token);
            getViewModel().setPassWord(reqSetPwd);
        });

        showDialog(dialogBuilder);
    }
}
