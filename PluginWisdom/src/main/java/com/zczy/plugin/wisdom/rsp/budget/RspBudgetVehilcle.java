package com.zczy.plugin.wisdom.rsp.budget;

import android.os.Parcel;
import android.os.Parcelable;

/**
 * author: SongShuangPeng
 * company: 南京中储智慧物流
 * date:   On 2019/3/28
 */
public class RspBudgetVehilcle implements Parcelable {
    private String plateNumber;

    public RspBudgetVehilcle() {

    }

    protected RspBudgetVehilcle(Parcel in) {
        plateNumber = in.readString();
    }

    public static final Creator<RspBudgetVehilcle> CREATOR = new Creator<RspBudgetVehilcle>() {
        @Override
        public RspBudgetVehilcle createFromParcel(Parcel in) {
            return new RspBudgetVehilcle(in);
        }

        @Override
        public RspBudgetVehilcle[] newArray(int size) {
            return new RspBudgetVehilcle[size];
        }
    };

    public String getPlateNumber() {
        return plateNumber;
    }

    public void setPlateNumber(String plateNumber) {
        this.plateNumber = plateNumber;
    }

    @Override
    public int describeContents() {
        return 0;
    }

    @Override
    public void writeToParcel(Parcel dest, int flags) {
        dest.writeString(plateNumber);
    }
}
