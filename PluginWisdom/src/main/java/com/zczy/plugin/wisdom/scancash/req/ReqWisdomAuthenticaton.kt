package com.zczy.plugin.wisdom.scancash.req

import com.horizon.did.DarkPhysicsInfo
import com.horizon.did.DeviceId
import com.horizon.did.PhysicsInfo
import com.sfh.lib.AppCacheManager
import com.zczy.comm.CommServer
import com.zczy.comm.http.entity.BaseRsp
import com.zczy.comm.http.entity.ResultData
import com.zczy.plugin.wisdom.BaseWisdomRequest

/**
 *    author : Ssp
 *    date   : 2020/1/16 15:02
 *    desc   : 提现设备验证
 */
class ReqWisdomAuthenticaton : BaseWisdomRequest<BaseRsp<ResultData>>("mms-app/mms/financeDevice/checkVerifyCodeAndUpdateFinanceDevice") {
    /***短信验证码类型 1.短信 2.语音 */
    var verifyCodeType = "1"
    /***手机号 */
    var mobile: String = ""
    /***验证码 */
    var verifyCode: String = ""
    /***模块类型：传值13 */
    var moduleType: String = ""
    var androidId: String? = DeviceId.getAndroidID(AppCacheManager.getApplication())
    var serialNo: String? = DeviceId.getSerialNo()
    var physicsInfo: String? = PhysicsInfo.getHash(AppCacheManager.getApplication()).toString()
    var darkPhysicsInfo: String? = DarkPhysicsInfo.getHash(AppCacheManager.getApplication()).toString()
    var macAddress: String? = DeviceId.getMacAddress()
    var udid: String? = CommServer.getUserServer().login.udid
}
