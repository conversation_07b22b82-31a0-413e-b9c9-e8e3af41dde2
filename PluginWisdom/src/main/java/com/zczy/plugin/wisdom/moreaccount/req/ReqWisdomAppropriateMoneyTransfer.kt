package com.zczy.plugin.wisdom.moreaccount.req

import com.zczy.comm.http.entity.BaseRsp
import com.zczy.comm.http.entity.ResultData
import com.zczy.plugin.wisdom.BaseWisdomRequest

/**
 *    author : Ssp
 *    e-mail : <EMAIL>
 *    date   : 2020/3/514:45
 *    desc   : 划拨资金-转出
 *    version: 1.0
 */
class ReqWisdomAppropriateMoneyTransfer(
        /** 所属平台id*/
        var bookNo: String = "",
        /** 目标账本类型*/
        var targetFundMode: String = "",
        /** 目标账本编号*/
        var targetBookNo: String = "",
        /** 目标交易金额*/
        var targetMoney: String = "",
        /** 账本类型*/
        var fundMode: String = "")
    : BaseWisdomRequest<BaseRsp<ResultData>>("pps-app/customerCapital/transferMoney")

