<?xml version="1.0" encoding="utf-8"?>
<androidx.constraintlayout.widget.ConstraintLayout xmlns:android="http://schemas.android.com/apk/res/android"
    xmlns:app="http://schemas.android.com/apk/res-auto"
    android:layout_width="match_parent"
    android:layout_height="match_parent"
    android:background="@color/color_4d000000"
    android:orientation="vertical">

    <androidx.constraintlayout.widget.ConstraintLayout
        android:layout_width="match_parent"
        android:layout_height="278dp"
        android:layout_marginLeft="36dp"
        android:layout_marginRight="36dp"
        android:background="@drawable/file_white_coner"
        app:layout_constraintBottom_toBottomOf="parent"
        app:layout_constraintLeft_toLeftOf="parent"
        app:layout_constraintRight_toRightOf="parent"
        app:layout_constraintTop_toTopOf="parent">

        <TextView
            android:id="@+id/tvTitle"
            android:layout_width="wrap_content"
            android:layout_height="wrap_content"
            android:layout_marginTop="21dp"
            android:text="资金划拨说明"
            android:textColor="#ff5086fc"
            android:textSize="17sp"
            android:textStyle="bold"
            app:layout_constraintLeft_toLeftOf="parent"
            app:layout_constraintRight_toRightOf="parent"
            app:layout_constraintTop_toTopOf="parent" />

        <TextView
            android:id="@+id/tv1"
            android:layout_width="wrap_content"
            android:layout_height="wrap_content"
            android:layout_marginLeft="29dp"
            android:layout_marginTop="34dp"
            android:layout_marginRight="29dp"
            android:lineSpacingExtra="5dp"
            android:text="1、同一个签约公司名下的子账户余额可以相互划转，不同签约公司名下资账户余额不可以直接划转；"
            android:textColor="#ff333333"
            android:textSize="13sp"
            app:layout_constrainedWidth="true"
            app:layout_constraintLeft_toLeftOf="parent"
            app:layout_constraintRight_toRightOf="parent"
            app:layout_constraintTop_toBottomOf="@+id/tvTitle" />

        <TextView
            android:layout_width="wrap_content"
            android:layout_height="wrap_content"
            android:layout_marginLeft="29dp"
            android:layout_marginTop="15dp"
            android:layout_marginRight="29dp"
            android:text="2、不同签约公司名下资账户余额如需划拨，请将其账户余额转出即可；"
            android:textColor="#ff333333"
            android:textSize="13sp"
            app:layout_constrainedWidth="true"
            app:layout_constraintLeft_toLeftOf="parent"
            app:layout_constraintRight_toRightOf="parent"
            app:layout_constraintTop_toBottomOf="@+id/tv1" />

        <TextView
            android:id="@+id/tv_know"
            android:layout_width="match_parent"
            android:layout_height="36dp"
            android:layout_marginLeft="29dp"
            android:layout_marginRight="29dp"
            android:layout_marginBottom="19dp"
            android:background="@drawable/wisdom_file_blue_6corner"
            android:gravity="center"
            android:text="我知道了"
            android:textColor="#ffffff"
            android:textSize="13sp"
            app:layout_constraintBottom_toBottomOf="parent"
            app:layout_constraintLeft_toLeftOf="parent"
            app:layout_constraintRight_toRightOf="parent" />
    </androidx.constraintlayout.widget.ConstraintLayout>

</androidx.constraintlayout.widget.ConstraintLayout>