<?xml version="1.0" encoding="utf-8"?>
<androidx.constraintlayout.widget.ConstraintLayout xmlns:android="http://schemas.android.com/apk/res/android"
    xmlns:app="http://schemas.android.com/apk/res-auto"
    xmlns:tools="http://schemas.android.com/tools"
    android:layout_width="match_parent"
    android:layout_height="match_parent"
    android:background="@color/color_eff0f3">

    <com.zczy.comm.widget.AppToolber
        android:id="@+id/appToolber"
        android:layout_width="match_parent"
        android:layout_height="55dp"
        android:background="@color/color_ffffff"
        app:layout_constraintTop_toTopOf="parent"
        app:leftIcon="@drawable/base_back_black"
        app:titleColor="@color/color_333333"
        app:titleSize="@dimen/text_16"
        app:titleTxt="@string/wisdom_add_public_bank_title" />

    <LinearLayout
        android:id="@+id/ll_bank_info"
        android:layout_width="match_parent"
        android:layout_height="wrap_content"
        android:background="@color/color_ffffff"
        android:divider="@drawable/file_divider_gray"
        android:orientation="vertical"
        android:showDividers="end"
        app:layout_constraintTop_toBottomOf="@+id/appToolber">

        <LinearLayout
            android:layout_width="match_parent"
            android:layout_height="40dp"
            android:gravity="center_vertical">

            <TextView
                android:layout_width="wrap_content"
                android:layout_height="wrap_content"
                android:layout_marginLeft="14dp"
                android:text="公司名称"
                android:textColor="@color/color_333333"
                android:textSize="@dimen/text_16" />

            <TextView
                android:id="@+id/tv_company_name"
                android:layout_width="wrap_content"
                android:layout_height="wrap_content"
                android:layout_marginLeft="19dp"
                android:textColor="@color/color_333333"
                android:textSize="@dimen/text_16"
                tools:text="北京虾米电子信息有限公司" />

        </LinearLayout>

        <androidx.constraintlayout.widget.ConstraintLayout
            android:layout_width="match_parent"
            android:layout_height="wrap_content"
            android:divider="@drawable/file_divider_gray"
            android:minHeight="40dp"
            android:showDividers="end">

            <TextView
                android:id="@+id/tvBank1"
                android:layout_width="wrap_content"
                android:layout_height="wrap_content"
                android:layout_marginLeft="14dp"
                android:text="银行卡号"
                android:textColor="@color/color_333333"
                android:textSize="@dimen/text_16"
                app:layout_constraintBottom_toBottomOf="parent"
                app:layout_constraintStart_toStartOf="parent"
                app:layout_constraintTop_toTopOf="parent" />

            <com.zczy.plugin.wisdom.home.view.WisdomBankSpaceEditText
                android:id="@+id/et_bank_number"
                android:layout_width="0dp"
                android:layout_height="40dp"
                android:layout_marginLeft="19dp"
                android:background="@null"
                android:gravity="center_vertical"
                android:hint="请输入卡号"
                android:textColor="@color/color_333333"
                android:textSize="@dimen/text_16"
                app:layout_constrainedHeight="true"
                app:layout_constrainedWidth="true"
                app:layout_constraintBottom_toBottomOf="parent"
                app:layout_constraintEnd_toEndOf="parent"
                app:layout_constraintStart_toEndOf="@id/tvBank1"
                app:layout_constraintTop_toTopOf="parent" />

            <TextView
                android:id="@+id/bankNumWarning"
                android:layout_width="wrap_content"
                android:layout_height="wrap_content"
                android:drawableStart="@drawable/base_warning"
                android:drawablePadding="5dp"
                android:text="银行卡号长度必须在8到25位数之间"
                android:textColor="#FB6B40"
                android:visibility="gone"
                app:layout_constraintStart_toStartOf="@id/et_bank_number"
                app:layout_constraintTop_toBottomOf="@id/et_bank_number"
                tools:visibility="visible" />
        </androidx.constraintlayout.widget.ConstraintLayout>


        <LinearLayout
            android:id="@+id/ll_select_bank_chanle"
            android:layout_width="match_parent"
            android:layout_height="40dp"
            android:gravity="center_vertical">

            <TextView
                android:layout_width="wrap_content"
                android:layout_height="wrap_content"
                android:layout_marginLeft="14dp"
                android:text="归属银行"
                android:textColor="@color/color_333333"
                android:textSize="@dimen/text_16" />

            <TextView
                android:id="@+id/tv_bank_chanle"
                android:layout_width="wrap_content"
                android:layout_height="wrap_content"
                android:layout_marginLeft="19dp"
                android:hint="请选择归属银行"
                android:textColor="@color/color_333333"
                android:textColorHint="@color/color_666666"
                android:textSize="@dimen/text_16" />

        </LinearLayout>

    </LinearLayout>

    <androidx.constraintlayout.widget.ConstraintLayout
        android:id="@+id/cl_add_bank"
        android:layout_width="match_parent"
        android:layout_height="wrap_content"
        app:layout_constraintLeft_toLeftOf="parent"
        app:layout_constraintRight_toRightOf="parent"
        app:layout_constraintTop_toBottomOf="@+id/ll_bank_info">

        <com.zczy.cargo_owner.libcomm.widget.AgreementView
            android:id="@+id/bcv"
            android:layout_width="match_parent"
            android:layout_height="36dp"
            android:layout_marginTop="1dp"
            android:gravity="center_vertical"
            android:visibility="gone"
            app:layout_constraintLeft_toLeftOf="parent"
            app:layout_constraintRight_toRightOf="parent"
            app:layout_constraintTop_toTopOf="parent"
            app:txt="我已阅读并同意"
            tools:visibility="visible" />

        <TextView
            android:id="@+id/tv_add_bank"
            android:layout_width="match_parent"
            android:layout_height="53dp"
            android:layout_marginLeft="17dp"
            android:layout_marginTop="20dp"
            android:layout_marginRight="17dp"
            android:background="@drawable/file_select_bule_gray"
            android:gravity="center"
            android:text="确定添加"
            android:textColor="@color/color_ffffff"
            android:textSize="@dimen/text_16"
            app:layout_constraintLeft_toLeftOf="parent"
            app:layout_constraintRight_toRightOf="parent"
            app:layout_constraintTop_toBottomOf="@+id/bcv" />
    </androidx.constraintlayout.widget.ConstraintLayout>

    <LinearLayout
        android:layout_width="match_parent"
        android:layout_height="wrap_content"
        android:layout_marginLeft="22dp"
        android:layout_marginTop="200dp"
        android:layout_marginRight="22dp"
        android:orientation="vertical"
        app:layout_constraintTop_toBottomOf="@+id/cl_add_bank"
        tools:layout_editor_absoluteX="1dp"
        tools:layout_editor_absoluteY="330dp">

        <ImageView
            android:layout_width="match_parent"
            android:layout_height="wrap_content"
            android:src="@drawable/line_addcard" />

        <LinearLayout
            android:layout_width="wrap_content"
            android:layout_height="wrap_content"
            android:layout_marginTop="20dp"
            android:orientation="horizontal">

            <ImageView
                android:layout_width="wrap_content"
                android:layout_height="wrap_content"
                android:src="@drawable/warning_addcard" />

            <TextView
                android:layout_width="wrap_content"
                android:layout_height="wrap_content"
                android:text="  提示"
                android:textColor="#999999 "
                android:textSize="14sp" />


        </LinearLayout>

        <TextView
            android:layout_width="wrap_content"
            android:layout_height="wrap_content"
            android:text="1.目前仅支持添加企业对公银行账户；"
            android:textColor="#999999 "
            android:textSize="14sp" />

        <TextView
            android:layout_width="wrap_content"
            android:layout_height="wrap_content"
            android:text="2.如账户号前带有区号，请连同区号一起输入，例如账户号：02-87654321，请输入0287654321"
            android:textColor="#999999 "
            android:textSize="14sp" />
    </LinearLayout>


</androidx.constraintlayout.widget.ConstraintLayout>