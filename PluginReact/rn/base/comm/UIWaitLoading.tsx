import React from 'react';
import {ActivityIndicator} from 'react-native';
import Modal from 'react-native-modal';
import {gScreen_height, gScreen_width, sUIWaitDilaogRef} from '../Const';
import { Dialog } from './Dialog';

export interface UIWaitDialogRef {
    show: (cancel:boolean) => Dialog;
}

/**
 * 等待对话框
 */
const UIWaitLoading = React.memo(() => {
    const [isVisible, setIsVisible] = React.useState(false);
    const [cancel, setCancel] = React.useState(false);

    const dialog = {
      show:()=>{
        setIsVisible(true);
      },
      dismiss:()=>{
        setIsVisible(false);
      }
    } as Dialog


    const show = React.useCallback((cancel:boolean) => {
        setIsVisible(true);
        setCancel(cancel);
        return dialog
    }, []);

    const onDismiss = React.useCallback(() => {
        if(cancel){
          dialog.dismiss();
        }
    }, [cancel]);

    React.useImperativeHandle(sUIWaitDilaogRef, () => ({show}));

    return (
        // @ts-ignore
        <Modal
            animationIn={'zoomIn'}
            animationOut={'zoomOut'}
            backdropOpacity={0.3}
            isVisible={isVisible}
            hideModalContentWhileAnimating={true}
            deviceWidth={gScreen_width}
            deviceHeight={gScreen_height}
            useNativeDriver={false}
            useNativeDriverForBackdrop={false}
            onBackButtonPress={onDismiss} // 响应返回键
            onBackdropPress={onDismiss} // 点击背景遮罩层
        >
            <ActivityIndicator size={'large'} />
        </Modal>
    );
});

export default UIWaitLoading;
