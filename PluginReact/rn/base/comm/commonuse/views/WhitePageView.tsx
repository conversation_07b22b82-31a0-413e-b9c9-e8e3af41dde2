import {Text, View} from 'react-native';
import UIImage from '../../../widget/UIImage';
import React from 'react';

interface Props {
    whitePagePicture?: string;
    whitePageText?: string;
}

export default function WhitePageView(props: Props) {
    return (
        <>
            <View style={{flex: 1, justifyContent: 'center', alignItems: 'center', marginTop: 100}}>
                <UIImage source={props.whitePagePicture ?? 'http://img.zczy56.com/202406131554022016004.png'} style={{width: 200, height: 130}} />
                <Text style={{fontSize: 15, color: '#999', marginTop: 15, marginRight: 20, marginLeft: 20}}>{props.whitePageText ?? '暂无信息'}</Text>
            </View>
        </>
    );
}
