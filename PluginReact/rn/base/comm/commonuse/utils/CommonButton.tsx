import React from 'react';
import UIButton from '../../../widget/UIButton';
import {View, StyleSheet} from 'react-native';
import {CommonButtonE} from '../http/entity/CommonBody';

interface Props {
    buttonInfo?: CommonButtonE;
}

export default function CommonButton(props: Props) {
    if (props.buttonInfo?.isOneButton)
        return (
            <UIButton
                text={props.buttonInfo?.ensureText ?? '确定'}
                textColor={props.buttonInfo?.ensureTextColor ?? '#fff'}
                style={props.buttonInfo?.ensureStyle ?? {width: '100%', backgroundColor: '#5086FC'}}
                borderColor={props.buttonInfo?.ensureBorderColor ?? '#fff'}
                fontSize={16}
                onPress={() => {
                    props.buttonInfo?.onConfirm && props.buttonInfo?.onConfirm();
                }}
            />
        );
    else
        return (
            <View style={styles.buttonContainer}>
                <UIButton
                    text={props.buttonInfo?.cancelText ?? '取消'}
                    style={props.buttonInfo?.cancelStyle ?? {width: '45%', backgroundColor: '#5086FC'}}
                    fontSize={16}
                    textColor={props.buttonInfo?.cancelTextColor ?? '#fff'}
                    borderColor={props.buttonInfo?.cancelBorderColor ?? '#fff'}
                    onPress={() => {
                        props.buttonInfo?.onCancel && props.buttonInfo?.onCancel();
                    }}
                />
                <UIButton
                    text={props.buttonInfo?.ensureText ?? '确定'}
                    textColor={props.buttonInfo?.ensureTextColor ?? '#fff'}
                    style={props.buttonInfo?.ensureStyle ?? {width: '45%', backgroundColor: '#5086FC'}}
                    borderColor={props.buttonInfo?.ensureBorderColor ?? '#fff'}
                    fontSize={16}
                    onPress={() => {
                        props.buttonInfo?.onConfirm && props.buttonInfo?.onConfirm();
                    }}
                />
            </View>
        );
}
const styles = StyleSheet.create({
    add: {
        borderStyle: 'dashed',
        flex: 1,
        height: 38,
        borderWidth: 1,
        borderColor: '#3674FD',
        borderRadius: 4,
        alignItems: 'center',
        justifyContent: 'center',
        marginBottom: 15,
    },
    buttonContainer: {
        flexDirection: 'row',
        justifyContent: 'space-between',
        alignItems: 'center',
        paddingLeft: 19,
        paddingRight: 19,
        height: 64,
        marginTop: 10,
        backgroundColor: '#fff',
    },
});
