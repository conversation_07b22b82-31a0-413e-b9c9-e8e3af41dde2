import {Text, View, StyleSheet, TouchableOpacity} from 'react-native';
import React, {useState} from 'react';
import {CommonItemE} from '../http/entity/CommonBody';
import UIImage from '../../../widget/UIImage';
import TextUtils from '../../../utils/TextUtils';
import {gScreen_width} from '../../../Const';

interface Props {
    data?: CommonItemE[];
}

export default function CommonItem(props: Props) {
    const [showInfo, setShowInfo] = useState(true); //必要的状态提升

    function toggleShowInfo() {
        setShowInfo(!showInfo);
    }

    if (props.data == null || props.data.length == 0) return <></>;
    else
        return (
            <View style={{marginTop: 10}}>
                {(props.data ?? [])?.map((item) => {
                    return item.uniqueAllLabel ? (
                        <>{item.uniqueAllLabel(showInfo, toggleShowInfo)}</>
                    ) : (
                        <CommonSmallItem label={item.label} lineLength={item.lineLength} uniqueComponent={item.uniqueComponent} uniqueAllLabel={item.uniqueAllLabel} value1={item.value1} value2={item.value2} />
                    );
                })}
            </View>
        );
}

// 横线
export function HorizontalLine() {
    return (
        <View
            style={{
                width: gScreen_width,
                height: 1,

                backgroundColor: '#ddd',
            }}
        />
    );
}

export function getParseAndFIx(data: any, num: number) {
    if (TextUtils.isEmpty(data)) {
        return '';
    }
    if (data == null) return '';
    return parseFloat(data ?? 0)
        .toFixed(num)
        .replace(/\B(?=(\d{3})+(?!\d))/g, ',');
}

// 右箭头
export function RightArrow() {
    return <UIImage source={'http://img.zczy56.com/202501211158577187837.png'} style={{width: 10, height: 10}} />;
}

// 普通条目
export const CommonSmallItem = (item: CommonItemE) => {
    return (
        <View
            style={[
                styles.style_text,
                {
                    borderBottomWidth: item.lineLength ?? 1,
                    backgroundColor: item.backgroundClolor ?? 'white',
                    flexWrap: 'wrap',
                    paddingVertical: 5,
                },
            ]}>
            {(item.label || item.label == '') && (
                <Text
                    style={[
                        styles.fontSame,
                        {
                            fontSize: item.fontSize ?? 16,
                        },
                    ]}>
                    {item.label}
                </Text>
            )}
            {(item.value1 || item.value1 == '') && (
                <Text
                    style={[
                        styles.fontSame,
                        {
                            fontSize: item.fontSize ?? 16,
                        },
                    ]}>
                    {item.value1}
                </Text>
            )}
            {(item.value2 || item.value2 == '') && (
                <Text
                    style={[
                        styles.fontSame,
                        {
                            fontSize: item.fontSize ?? 16,
                        },
                    ]}>
                    {item.value2}
                </Text>
            )}
            {item.uniqueComponent && item.uniqueComponent}
        </View>
    );
};
export const uniqueAllLabelView = (options: {text?: string; url?: string; params?: any; h5?: boolean; h5Url?: string; h5Name?: string; rightText?: string}) => () => {
    return (
        <TouchableOpacity
            style={styles.style_textContainer}
            onPress={() => {
                // if (options.h5 && options.h5Url) {
                //     Method.openWebView(options.h5Url, options.h5Name);
                //     return;
                // }
                // RouterUtils.skipRouter(RouterUrl.ReactPage, {
                //     page: options.url,
                //     data: options.params,
                // });
            }}>
            <Text style={styles.style_text1}>{options.text}</Text>

            <View style={{alignItems: 'center', justifyContent: 'center', flexDirection: 'row'}}>
                <Text style={[styles.style_text1, {paddingRight: 5}]}>{options.rightText}</Text>
                <RightArrow />
            </View>
        </TouchableOpacity>
    );
};

const styles = StyleSheet.create({
    fontSame: {
        fontSize: 16,
        color: '#333',
        flex: 1,
    },
    style_text: {
        flexDirection: 'row',
        justifyContent: 'space-between',
        alignItems: 'center',
        backgroundColor: 'white',
        paddingHorizontal: 13,
        borderBottomColor: '#eee',
    },
    style_textContainer: {
        paddingLeft: 30,
        paddingRight: 20,
        height: 50,
        backgroundColor: 'white',
        alignItems: 'center',
        flexDirection: 'row',
        justifyContent: 'space-between',
    },
    style_text1: {
        textAlign: 'auto',
        fontSize: 16,
        color: '#333',
    },
});
