/**
 * 注释: 路由工具
 */

import { StackActions } from '@react-navigation/native';
import { BackHandler } from 'react-native';
import TextUtils from '../utils/TextUtils';
import { sNavigationRef } from '../Const';
import { openNativePage } from './NativeUtils';

export class ZRouter {
    /**
     * 注释: 回退到上个页面
     */
    static goBack() {
        if (sNavigationRef.current?.canGoBack()) {
            //RN回退上个页面
            sNavigationRef.current?.goBack();
        } else {
            //回原生上个页面
            BackHandler.exitApp();
        }
    }

    /**
     * 注释: 跳转回RN栈底页面
     */
    static goBackLast() {
        if (sNavigationRef.current?.canGoBack()) {
            sNavigationRef.current?.dispatch(StackActions.popToTop);
        }
    }

    /**
     * 注释: 退出多个页面
     */
    static skipPopToMul(count: number) {
        if (sNavigationRef.current?.canGoBack()) {
            sNavigationRef.current?.dispatch(StackActions.pop(count));
        }
    }

    /**
     * 注释: 跳转路由
     * @param params
     */
    static async toPage(props: { page: string; params?: any; callback?: Function }) {
        console.log('跳转路由：', `${props.page}?pageParams=${JSON.stringify(props.params)}`);

        let data = props.params;
        let callBack = props.callback;
        //路由参数
        let routerParams = props.params;
        if (callBack) {
            routerParams = { ...data, callBack };
        }
        let pushAction = StackActions.push(props.page, routerParams);
        sNavigationRef.current?.dispatch(pushAction);
    }

    static async toPageReplace(props: { page: string; params?: any; callback?: Function }) {
        console.log('跳转路由：', `${props.page}?pageParams=${JSON.stringify(props.params)}`);

        let data = props.params;
        let callBack = props.callback;
        //路由参数
        let routerParams = props.params;
        if (callBack) {
            routerParams = { ...data, callBack };
        }
        let pushAction = StackActions.replace(props.page, routerParams);
        sNavigationRef.current?.dispatch(pushAction);
    }

    /**
     * 注释: 获取页面参数
     * @private
     */
    static getParams(props: any) {
        if (props.route.params) {
            console.log(`页面传参:${JSON.stringify(props.route.params)}`);
            return props.route.params;
        } else {
            if (TextUtils.isNoEmpty(props.exreaData)) {
                console.log(`页面传参:${props.exreaData}`);
                return JSON.parse(props.exreaData);
            }
        }
        return {};
    }

    static getCallBack(props: any): () => void {
        return props.route.params!['callBack'];
    }

    /***
     * 打开原生页面
     */
    static async toNativePage(props: { page: string; params?: {} }, callback?: (code: number, json: string) => void) {
        if (callback) {
            openNativePage(props, callback);
        } else {
            openNativePage(props, (code: number, json: string) => { });
        }
    }
}
