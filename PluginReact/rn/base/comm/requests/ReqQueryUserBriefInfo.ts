import TextUtils from '../../utils/TextUtils';
import {ResultData} from '../../http/ResultData';
import {BaseRequest} from '../../http/BaseRequest';
import {BaseResponse} from '../../http/BaseResponse';
import {getLogin} from '../NativeUtils';

/**
 *  desc: 获取用户简明信息
 *  user: 宋双朋
 *  time: 2025/2/14 16:07
 */
export class RspUserBriefInfo extends ResultData {
    heavyTruckFlag?: string;
    isBangApple?: string;
    personalInfoSwitch?: string;
    idCardNo?: string;
    headUrl?: string;
    memberName?: string;
    lockType?: string;
    verifyStatus?: number;
    verifyExamineStatus?: string;
    cityName?: string;
    areaName?: string;
    userHeadPic?: string;
    customerId?: string;
    openAccount?: string;
    authType?: string;
    vehicleRiskAudit?: string;
    isBangWeiXi?: string;
    businessLicenseName?: string;
    nickName?: string;
    mobile?: string;
    examineType?: number;
    materialFlag?: number;
    userName?: string;
    userId?: string;
    riskAudit?: string;
    isSalesman?: number;
    nooType?: string;
    examineNewType?: string;
    upgradeType?: string;
    subType?: string;
    userType?: number;
    provinceName?: string;
    customerNm?: string;
    completePercent?: string; //资料完成进度
    //货主类型标记  1企业类货主  2个人类货主  3个体工商户
    consignorTypeFlag?: string;
    //自然人是否升级中  0否  1是
    naturalPersonUpgrade?: string;
    // 1 男 2 女
    sex?: string;
}

export class ReqQueryUserBriefInfo extends BaseRequest {
    userId?: string;

    public async request(): Promise<BaseResponse<RspUserBriefInfo>> {
        this.params = {};
        return super.post('mms-app/mms/upgrade/queryUserBriefInfo', RspUserBriefInfo);
    }
}
