import {BaseRequest} from '../../http/BaseRequest';
import {BaseResponse} from '../../http/BaseResponse';
import {ResultData} from '../../http/ResultData';

/**
 * 注释：获取人脸识别结果（腾讯人脸识别）
 * 时间：2024/12/10 9:57
 * 作者：王家辉
 * wiki：http://wiki.zczy56.com/pages/viewpage.action?pageId=20745818
 * 对接：原心宜
 * */
export class ReqFaceRegoin extends BaseRequest {
    idCardNo?: string; //身份证号码
    idCardName?: string; //身份证姓名
    orderNo?: string; //身份证号码
    userType?: string; //用户类型
    public async request(): Promise<BaseResponse<ResultData>> {
        this.params = {
            idCardNo: this.idCardNo,
            idCardName: this.idCardName,
            orderNo: this.orderNo,
            from: 'consignor',
            userType: this.userType,
        };
        return super.post('mms-app/platform/authentication/faceRecognitionForUpgrade', ResultData);
    }
}
