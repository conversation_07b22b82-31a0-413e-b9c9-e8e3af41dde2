import {checkMultiple, openSettings, PERMISSIONS, request, requestMultiple, RESULTS} from 'react-native-permissions';
import {Alert, Platform} from 'react-native';
import {Permission} from 'react-native-permissions/src/types';
import Toast from 'react-native-toast-message';

/**
 * 注释: 权限类型枚举
 * 时间: 2023/7/12 0012 16:19
 * <AUTHOR>
 */
export enum PermissionType {
    //定位
    LOCATION,
    //相机
    CAMERA,
    //存储
    STORAGE,
    //电话
    PHONE,
    //通讯录
    CONTACTS,
    //读取手机
    READ_PHONE_STATE,
    //麦克风
    RECORD_AUDIO,
}

export interface PermissionOption {
    permissions: PermissionType[]; //权限类型
    title?: string;
    onSuccess: () => void; //权限通过回调
    onFail: () => void; //权限不通过回调
}

export const checkPermission = async (op: PermissionOption) => {
    console.log('申请权限');
    let ps: Permission[] = new Array();

    op.permissions.map((item) => {
        switch (item) {
            case PermissionType.LOCATION: {
                if (Platform.OS === 'android') {
                    ps.push(PERMISSIONS.ANDROID.ACCESS_COARSE_LOCATION);
                    ps.push(PERMISSIONS.ANDROID.ACCESS_FINE_LOCATION);
                } else if (Platform.OS === 'ios') {
                    // ps.push(PERMISSIONS.IOS.LOCATION_ALWAYS);
                    ps.push(PERMISSIONS.IOS.LOCATION_WHEN_IN_USE);
                }
                break;
            }
            case PermissionType.CAMERA: {
                if (Platform.OS === 'android') {
                    ps.push(PERMISSIONS.ANDROID.CAMERA);
                } else if (Platform.OS === 'ios') {
                    ps.push(PERMISSIONS.IOS.CAMERA);
                }
                break;
            }
            case PermissionType.STORAGE: {
                if (Platform.OS === 'android') {
                    ps.push(PERMISSIONS.ANDROID.WRITE_EXTERNAL_STORAGE);
                    ps.push(PERMISSIONS.ANDROID.READ_EXTERNAL_STORAGE);
                } else if (Platform.OS === 'ios') {
                    ps.push(PERMISSIONS.IOS.PHOTO_LIBRARY);
                    ps.push(PERMISSIONS.IOS.PHOTO_LIBRARY_ADD_ONLY);
                }
                break;
            }
            case PermissionType.PHONE: {
                if (Platform.OS === 'android') {
                    ps.push(PERMISSIONS.ANDROID.READ_PHONE_STATE);
                    ps.push(PERMISSIONS.ANDROID.READ_EXTERNAL_STORAGE);
                } else if (Platform.OS === 'ios') {
                    ps.push(PERMISSIONS.IOS.PHOTO_LIBRARY);
                }
                break;
            }
            case PermissionType.CONTACTS: {
                if (Platform.OS === 'android') {
                    ps.push(PERMISSIONS.ANDROID.READ_CONTACTS);
                } else if (Platform.OS === 'ios') {
                    ps.push(PERMISSIONS.IOS.CONTACTS);
                }
                break;
            }
            case PermissionType.READ_PHONE_STATE: {
                if (Platform.OS === 'android') {
                    ps.push(PERMISSIONS.ANDROID.READ_PHONE_STATE);
                }
                break;
            }
            case PermissionType.RECORD_AUDIO: {
                if (Platform.OS === 'android') {
                    ps.push(PERMISSIONS.ANDROID.RECORD_AUDIO);
                }
                break;
            }
        }
    });

    try {
        let statuses = await checkMultiple(ps);
        console.log('申请权限=2=' + JSON.stringify(statuses));

        let notGranted = ps.filter((item) => statuses[item] != RESULTS.GRANTED);
        if (notGranted && notGranted.length > 0) {
            console.log('=未授权,去申请====');
            //未授权,去申请
            let statuses = await requestMultiple(ps);
            let notGranted = ps.filter((item) => statuses[item] != RESULTS.GRANTED);
            if (notGranted && notGranted.length > 0) {
                //授权失败
                op.onFail();
            } else {
                //授权成功
                op.onSuccess();
            }
        } else {
            //已授权
            console.log('=已授权====');
            op.onSuccess();
        }
    } catch (e) {
        console.log('=申请异常====' + e);
        Toast.show({text1: '申请异常', text2: JSON.stringify(e)});
        op.onFail();
    }
};
