import TextUtils from './TextUtils';
import {ClassConstructor} from "class-transformer";

export class ArrayUtils {
    /**
     * 注释: 集合是否不为空
     * @param array
     */
    static isNoEmpty(array: any) {
        return Array.isArray(array) && array.length > 0;
    }

    /**
     * 注释: 集合是否为空
     * @returns {boolean}
     */
    static isEmpty(array: any) {
        return Array.isArray(array) && array.length == 0;
    }
    /**
     * 注释: 集合是否为空
     * @returns {boolean}
     */
    static isEmptyArray(array: any) {
        return TextUtils.isEmpty(array) || (Array.isArray(array) && array.length == 0);
    }

    /**
     * 注释: 数组删除某一个值并返回结果
     * @param item
     */
    static remove<T>(array: T[], item: T) {
        if (Array.isArray(array)) {
            array = array.filter((value) => value != item);
        }
        return array;
    }

    /**
     * 注释: 列表深拷贝
     * @return {T[]}
     */
    static deepCopy<T>(array: T[], type: ClassConstructor<T>) {
        if (Array.isArray(array)) {
            let json = JSON.stringify(array);
        }
        return array;
    }
}
