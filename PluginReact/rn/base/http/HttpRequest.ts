import axios, {AxiosRequestConfig} from 'axios';
import {getHttpUrl, getCommHeader, checkcode, decheckcode} from '../comm/NativeUtils';

export type Response<T> = {
    code: string;
    msg: string;
    data: T;
};

export class HttpRequest {
    url?: string;
    method: string = 'POST';
    path?: string;
    headers = {};
    //设置是否对返回参数进行解密
    decheckcode: boolean = false;
    //设置参数数据加密
    encryption: boolean = true;

    constructor(path?: string) {
        this.path = path;
    }

    setUrl(url?: string): HttpRequest {
        this.url = url;
        return this;
    }

    setMethod(method: string): HttpRequest {
        this.method = method;
        return this;
    }

    setPath(path: string): HttpRequest {
        this.path = path;
        return this;
    }

    setHeaders(headers: any): HttpRequest {
        this.headers = headers;
        return this;
    }

    setCheckcode(decheckcode: boolean): HttpRequest {
        this.decheckcode = decheckcode;
        return this;
    }

    setEncryption(encryption: boolean): HttpRequest {
        this.encryption = encryption;
        return this;
    }

    request<T>(params?: any): Promise<Response<T>> {
        console.log('请求参数：' + JSON.stringify(params));
        console.log('请求头：' + JSON.stringify(getCommHeader()));

        return new Promise<Response<T>>(async (resolve, reject) => {
            let commHeaders = getCommHeader();
            let baseURL = this.url === undefined ? getHttpUrl() : this.url;
            // 返回数据进行解密
            if (this.decheckcode) {
                commHeaders = {encrypt: true, ...commHeaders, ...this.headers};
            }

            //参数加密
            let newParams = params;
            if (this.encryption && 'POST' === this.method.toUpperCase()) {
                newParams = checkcode(JSON.stringify(params));
                console.log('加密：' + newParams);
            }

            const config:AxiosRequestConfig = {
                baseURL: baseURL + this.path ?? '',
                method: this.method,
                timeout: 10000,
                params: 'GET' === this.method.toUpperCase() && params,
                data: 'POST' === this.method.toUpperCase() && newParams,
                headers: commHeaders,
                transformRequest: function transformRequest(data, headers) {


                    const hasJSONContentType = () => {
                        const contentType = (headers && headers['Content-Type']) || '';
                        return contentType.toString().indexOf('application/json') > -1;
                    };

                    let f = typeof data === 'string' && hasJSONContentType()
                    console.log('加密2====：' + f);

                    if (f) {
                        return data;
                    }
                    return data;
                },
            };
            console.log('请求地址：' + baseURL + this.path ?? '');
            axios(config)
                .then((response) => {
                    console.log('请求结果Code：' + response.status);
                    console.log('请求地址：' + config.baseURL);
                    console.log('请求结果：' + JSON.stringify(response.data));

                    if (response.status === 200) {
                        let data = response.data;
                        if (this.decheckcode) {
                            //返回数据进行解密
                            data = decheckcode(response.data);
                            let JSONBigString = require('json-bigint')({storeAsString: true});
                            data = JSONBigString.parse(data);
                        }
                        //JSON 数据：若服务器返回的是 JSON 格式的数据，且未特别指定 responseType，data 将自动解析为相应的 JavaScript 对象或数组。如果您显式指定了 responseType: 'json'，结果也是一样的。
                        resolve(data as Response<T>);
                    } else {
                        resolve({
                            code: `${response.status}`,
                            msg: response.statusText + '[' + response.status + ']',
                        } as Response<T>);
                    }
                })
                .catch((error) => {
                    console.log('请求结果异常：' + JSON.stringify(error));
                    let status = error.toJSON().status;
                    if (status === 401) {
                        resolve({code: status, msg: error.message} as Response<T>);
                    } else if (error.code === 'ECONNABORTED') {
                        resolve({
                            code: status,
                            msg: '请求超时，请稍后重试![H]',
                        } as Response<T>);
                    } else {
                        resolve({
                            code: status,
                            msg: '请求失败，请稍后重试![H' + status + ']',
                        } as Response<T>);
                    }
                });
        });
    }
}
