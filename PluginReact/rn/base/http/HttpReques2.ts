import axios, {AxiosRequestConfig} from 'axios';
import {getHttpUrl, getCommHeader, checkcode, decheckcode} from '../comm/NativeUtils';

export type Response<T> = {
    code: string;
    msg: string;
    data: T;
};

export class HttpRequest2 {
    url?: string;
    method: string = 'POST';
    path?: string;
    headers = {};
    //设置是否对返回参数进行解密
    decheckcode: boolean = false;
    //设置参数数据加密
    encryption: boolean = true;

    public mediaType: string = 'application/json;charset=UTF-8';

    constructor(path?: string) {
        this.path = path;
    }

    setUrl(url?: string): HttpRequest2 {
        this.url = url;
        return this;
    }

    getUrl(): string {
        return this.url === undefined ? getHttpUrl() : this.url;
    }

    setMethod(method: string): HttpRequest2 {
        this.method = method;
        return this;
    }

    setPath(path: string): HttpRequest2 {
        this.path = path;
        return this;
    }

    setHeaders(headers: any): HttpRequest2 {
        this.headers = headers;
        return this;
    }

    setCheckcode(decheckcode: boolean): HttpRequest2 {
        this.decheckcode = decheckcode;
        return this;
    }

    setEncryption(encryption: boolean): HttpRequest2 {
        this.encryption = encryption;
        return this;
    }

    private logStr: string = '';

    request<T>(params?: any): Promise<Response<T>> {
        return new Promise<Response<T>>(async (resolve, reject) => {
            //请求头
            let commHeader = {
                ...getCommHeader(),
                ...this.headers,
                encrypt: this.encryption,
                'Content-Type': 'application/json;charset=UTF-8',
            };

            let postData = '';
            if (params) {
                if ('GET' === this.method) {
                    if (this.path?.search(/\?/) === -1) {
                        this.path +=
                            '?' +
                            Object.keys(params)
                                .map((key) => key + '=' + params[key])
                                .join('&');
                    } else {
                        this.path +=
                            '&' +
                            Object.keys(params)
                                .map((key) => key + '=' + params[key])
                                .join('&');
                    }
                } else if (this.mediaType === 'application/x-www-form-urlencoded;charset=utf-8') {
                    postData = params
                        ? Object.keys(params)
                              .map((key) => key + '=' + params[key])
                              .join('&')
                        : '';
                } else {
                    postData = JSON.stringify(params);
                }
            }
            //参数加密
            if (this.encryption && 'POST' === this.method.toUpperCase()) {
                postData = checkcode(postData);
            }

            const config: AxiosRequestConfig = {
                baseURL: this.getUrl() + this.path ?? '',
                method: this.method,
                timeout: 10000,
                data: 'POST' === this.method.toUpperCase() ? postData : '',
                headers: commHeader,
                transformRequest: (data, headers) => {
                    const hasJSONContentType = () => {
                        const contentType = (headers && headers['Content-Type']) || '';
                        return contentType.toString().indexOf('application/json') > -1;
                    };
                    if (this.encryption && typeof data === 'string' && hasJSONContentType()) {
                        return data;
                    }
                    return data;
                },
                transformResponse: (data, header) => {
                    let result = data;
                    if (this.decheckcode) {
                        //需要返回数据加密
                        result = decheckcode(data);
                    }

                    this.logStr += '\n请求地址：' + config.baseURL;
                    this.logStr += '\n公共参数：' + JSON.stringify(config.headers);
                    this.logStr += '\n请求参数：' + JSON.stringify(params);
                    this.logStr += '\n参数加密：' + postData;
                    this.logStr += '\n返回数据：' + result;
                    this.logStr += '\n'
                    return result;
                },
            };

            axios(config)
                .then((response) => {
                    if (response.status === 200) {
                        let data = response.data;
                        let JSONBigString = require('json-bigint')({storeAsString: true});
                        data = JSONBigString.parse(data);
                        //JSON 数据：若服务器返回的是 JSON 格式的数据，且未特别指定 responseType，data 将自动解析为相应的 JavaScript 对象或数组。如果您显式指定了 responseType: 'json'，结果也是一样的。
                        resolve(data as Response<T>);
                    } else {
                        resolve({
                            code: `${response.status}`,
                            msg: response.statusText + '[' + response.status + ']',
                        } as Response<T>);
                    }

                    console.log(this.logStr);
                })
                .catch((error) => {
                    this.logStr += '结果异常：' + JSON.stringify(error);

                    let status = error.toJSON().status;
                    if (status === 401) {
                        resolve({code: status, msg: error.message} as Response<T>);
                    } else if (error.code === 'ECONNABORTED') {
                        resolve({
                            code: status,
                            msg: '请求超时，请稍后重试![H]',
                        } as Response<T>);
                    } else {
                        resolve({
                            code: status,
                            msg: '请求失败，请稍后重试![H' + status + ']',
                        } as Response<T>);
                    }
                    console.log(this.logStr);
                });
        });
    }
}
