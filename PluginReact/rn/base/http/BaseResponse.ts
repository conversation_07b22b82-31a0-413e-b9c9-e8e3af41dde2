import {success, msg, code} from './Http';

export class BaseResponse<T> {
    public code: string;
    public msg: string;
    public data?: T;

    /**
     * 注释: 是否成功
     * 时间: 2023/6/19 0019 10:02
     * <AUTHOR>
     * @returns {*}
     */
    public isSuccess() {
        return success(this);
    }

    public getMsg() {
        return msg(this);
    }

    public getCode() {
        return code(this);
    }
}
