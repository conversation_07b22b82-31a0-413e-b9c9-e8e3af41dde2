//常量

import {createNavigationContainerRef} from '@react-navigation/native';
import {Dimensions, StyleSheet} from 'react-native';
import React from 'react';
import { UIDialogRef } from './comm/UIDialog';
import { UIWaitDialogRef } from './comm/UIWaitLoading';

//全局Navigation 路由
const sNavigationRef = createNavigationContainerRef();
//全局声明 对话框
const sUIDilaogRef = React.createRef<UIDialogRef>()
//全局声明 等待对话框
const sUIWaitDilaogRef = React.createRef<UIWaitDialogRef>()

//屏幕宽高
const {width, height} = Dimensions.get('window');
const gScreen_width = width;
const gScreen_height = height;

//通用样式
const gStyle = StyleSheet.create({
    txt_FE4444_34: {color: '#FE4444', fontSize: 17, textAlignVertical: 'center'},
    txt_fff_22: {color: '#fff', fontSize: 11, textAlignVertical: 'center'},
    txt_fff_32: {color: '#fff', fontSize: 16, textAlignVertical: 'center'},
    txt_fff_34: {color: '#fff', fontSize: 17, textAlignVertical: 'center'},

    txt_333333_34: {color: '#333333', fontSize: 17, textAlignVertical: 'center'},
    txt_333333_28: {color: '#333333', fontSize: 14, textAlignVertical: 'center'},
    txt_333333_26: {color: '#333333', fontSize: 13, textAlignVertical: 'center'},
    txt_333333_24: {color: '#333333', fontSize: 12, textAlignVertical: 'center'},
    txt_333333_32: {color: '#333333', fontSize: 16, textAlignVertical: 'center'},
    txt_333333_17: {color: '#333333', fontSize: 8, textAlignVertical: 'center'},
    txt_666666_34: {color: '#666666', fontSize: 17, textAlignVertical: 'center'},
    txt_666666_24: {color: '#666666', fontSize: 12, textAlignVertical: 'center'},
    txt_666666_28: {color: '#666666', fontSize: 14, textAlignVertical: 'center'},
    txt_999999_28: {color: '#999999', fontSize: 14, textAlignVertical: 'center'},
    txt_999999_24: {color: '#999999', fontSize: 12, textAlignVertical: 'center'},
    txt_999999_22: {color: '#999999', fontSize: 11, textAlignVertical: 'center'},
    txt_555555_24: {color: '#555555', fontSize: 12, textAlignVertical: 'center'},

    view_padding: {paddingLeft: 10, paddingRight: 10, paddingTop: 7, paddingBottom: 5, backgroundColor: '#fff'},

    view_row: {flexDirection: 'row', justifyContent: 'center', alignItems: 'center'},
    //有对齐，填充满
    text_flex_right: {flex: 1, textAlign: 'right', textAlignVertical: 'center'},
    //输入框背景
    input_bg: {backgroundColor: '#e3e3e3', borderRadius: 5, flex: 1, padding: 4},
});
//统一声明
export {sNavigationRef,sUIDilaogRef,sUIWaitDilaogRef, gScreen_width, gScreen_height,gStyle};
