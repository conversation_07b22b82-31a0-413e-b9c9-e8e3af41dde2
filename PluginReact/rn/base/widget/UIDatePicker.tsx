import { Text, View, ViewStyle} from 'react-native';
import React, {useRef} from 'react';
import Modal from 'react-native-modal';
import DatePicker from 'rmc-date-picker/lib/DatePicker';
import {gScreen_width, gStyle} from '../Const';

interface Props {
    //标题
    title: string;
    //隐藏事件
    onHideEvent: Function;
    //选择回调
    onSelectEvent: Function;
    //默认时间
    defaultTime?: Date;
    //最小值
    minDate?: Date;
    //最大值
    maxDate?: Date;
    //分钟间隔
    minuteStep?: number;
    //时间样式
    mode?: string;
    style?: ViewStyle;
}

/**
 * 注释:  时间选择器
 * 时间: 2023/11/1 0001 11:16
 * <AUTHOR>
 * @param props
 * @constructor
 */
export default function UIDatePicker(props: Props) {
    //选中的时间
    const selectDate = useRef<Date>(props.defaultTime ?? new Date());
    return (
        <Modal
            style={{justifyContent: 'flex-end', padding: 0, margin: 0}}
            animationIn={'slideInUp'}
            backdropOpacity={0.3}
            useNativeDriver={true}
            isVisible={true}
            onBackButtonPress={() => {
                props.onHideEvent && props.onHideEvent();
            }}
            onBackdropPress={() => {
                props.onHideEvent && props.onHideEvent();
            }}>
            <View
                style={[
                    gStyle.view_row,
                    {
                        borderTopLeftRadius: 8,
                        borderTopRightRadius: 8,
                        padding: 10,
                        backgroundColor: '#fff',
                    },
                ]}>
                <Text
                    style={gStyle.txt_999999_28}
                    onPress={() => {
                        props.onHideEvent && props.onHideEvent();
                    }}>
                    取消
                </Text>
                <Text style={[gStyle.txt_333333_32, {flex: 1, textAlign: 'center'}]}>{`${props.title}`}</Text>
                <Text
                    style={gStyle.txt_333333_28}
                    onPress={() => {
                        props.onHideEvent && props.onHideEvent();
                        props.onSelectEvent && props.onSelectEvent(selectDate.current);
                    }}>
                    确定
                </Text>
            </View>
            <View style={{backgroundColor: '#fff', width: gScreen_width}}>
                <DatePicker
                    rootNativeProps={{'data-xx': 'yy'}}
                    locale={{year: '年', month: '月', day: '日', hour: '时', minute: '分'}}
                    minDate={props.minDate ?? new Date(2022, 0, 1)}
                    maxDate={props.maxDate ?? new Date(2099, 0, 1)}
                    defaultDate={props.defaultTime ?? new Date()}
                    mode={props.mode ?? 'datetime'}
                    style={{minHeight: 180,...props.style}}
                    onDateChange={(date) => {
                        selectDate.current = date;
                    }}
                    minuteStep={props.minuteStep}
                />
            </View>
        </Modal>
    );
}
