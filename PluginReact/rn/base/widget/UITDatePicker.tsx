import {Dimensions, StyleSheet, Text, TouchableOpacity, View} from 'react-native';
import React, {useEffect, useState} from 'react';

import Toast from 'react-native-toast-message';
import UIButton from './UIButton';
import ZDatePicker from '../comm/ZDatePicker';

interface Props {
    showMark: boolean; //展示视图
    showWhich?: number; //1:只展示月 2:只展示日 3:可切换 默认展示到日
    callBack?: (time1: Date, time2?: Date) => void; //返回当前选中的日期,如采用样式3,用两个值接即可
    onClose: Function; // 关闭视图
    defaultValue?: Date; //传入默认日期,当前日期则不需要传,默认格式:new Date(2024,0,1)代表2024年1月1日  注意：月份是从0开始的，所以1月是0
}

/**
 * 注释:
 *  <UITDatePicker
 showMark={this.state.show1}
 showWhich={3}
 onClose={() => this.setState({show1: false})}
 callBack={(time1, time2) => {
 console.log(time1, time2);
 console.log(dateFormat(time1, 'yyyy-MM-dd'));
 }}
 defaultValue={new Date(2023, 1, 1)}
 />
 */

export default function UITDatePicker(props: Props) {
    const [startTime, setStartTime] = useState(new Date());
    const [endTime, setEndTime] = useState(new Date());
    const [nowTime, setNowTime] = useState(props.defaultValue ?? new Date());
    const [chooseMonthOrDay, setChooseMonthOrDay] = useState<boolean>(props.showWhich != 2);
    const [chooseInputDay, setChooseInp] = useState(true);
    let start = startTime.getFullYear() + '-' + (startTime.getMonth() + 1) + '-' + startTime.getDate();
    let end = endTime.getFullYear() + '-' + (endTime.getMonth() + 1) + '-' + endTime.getDate();
    const [visible, setVisible] = useState(props.showMark);
    useEffect(() => {
        setVisible(props.showMark);
    }, [props.showMark]);
    if (visible)
        return (
            <View style={{width: '100%', height: '100%', position: 'absolute', bottom: 0, zIndex: 1}}>
                <View style={styles.topStyle} />
                <View
                    style={{
                        backgroundColor: '#fff',
                        bottom: 0,
                        position: 'absolute',
                        zIndex: 1000,
                    }}>
                    {props.showWhich == 3 && (
                        <View style={[styles.style1, {height: props.showWhich == 3 ? 40 : 0}]}>
                            <TouchableOpacity
                                style={[
                                    styles.touchStyle,
                                    {
                                        borderBottomColor: chooseMonthOrDay ? '#377efa' : '#fff',
                                    },
                                ]}
                                onPress={() => {
                                    if (!chooseMonthOrDay) {
                                        setChooseMonthOrDay(!chooseMonthOrDay);
                                    }
                                }}>
                                <Text
                                    style={{
                                        fontSize: 14,
                                        color: chooseMonthOrDay ? '#377efa' : '#333',
                                    }}>
                                    {' 按月选择 '}
                                </Text>
                            </TouchableOpacity>

                            <TouchableOpacity
                                style={[
                                    styles.touchStyle,
                                    {
                                        borderBottomColor: chooseMonthOrDay ? '#fff' : '#377efa',
                                    },
                                ]}
                                onPress={() => {
                                    if (chooseMonthOrDay) {
                                        setChooseMonthOrDay(!chooseMonthOrDay);
                                    }
                                }}>
                                <Text
                                    style={{
                                        fontSize: 14,
                                        color: chooseMonthOrDay ? '#333' : '#377efa',
                                    }}>
                                    {'按日选择'}
                                </Text>
                            </TouchableOpacity>
                        </View>
                    )}

                    <View style={{marginBottom: 10}}>
                        <View style={styles.inputStyle} />

                        {!chooseMonthOrDay && (
                            <View style={{marginTop: 10, marginBottom: 10}}>
                                <View style={{width: '100%', height: 1, borderBottomColor: '#dcdcdc'}} />

                                {props.showWhich == 3 && (
                                    <View style={{flexDirection: 'row', justifyContent: 'center', alignItems: 'center'}}>
                                        <TouchableOpacity
                                            onPress={() => {
                                                if (!chooseInputDay) {
                                                    setChooseInp(!chooseInputDay);
                                                }
                                            }}>
                                            <View
                                                style={[
                                                    styles.textContainer,
                                                    {
                                                        backgroundColor: chooseInputDay ? '#eef5ff' : '#f3f3f3',
                                                        right: 5,
                                                    },
                                                ]}>
                                                <Text>{start}</Text>
                                            </View>
                                        </TouchableOpacity>

                                        <View style={styles.lineStyle} />
                                        <TouchableOpacity
                                            onPress={() => {
                                                if (chooseInputDay) {
                                                    setChooseInp(!chooseInputDay);
                                                }
                                            }}>
                                            <View
                                                style={[
                                                    styles.textContainer,
                                                    {
                                                        backgroundColor: chooseInputDay ? '#f3f3f3' : '#eef5ff',
                                                        left: 5,
                                                    },
                                                ]}>
                                                <Text>{end}</Text>
                                            </View>
                                        </TouchableOpacity>
                                    </View>
                                )}
                            </View>
                        )}
                    </View>
                    {/*时间选择器*/}
                    <ZDatePicker
                        value={nowTime}
                        onChange={(value) => {
                            setNowTime(value);
                            if (chooseInputDay) setStartTime(value);
                            else setEndTime(value);
                        }}
                        format={chooseMonthOrDay ? 'yyyy-mm' : 'yyyy-mm-dd'}
                    />
                    <UIButton
                        text={'确定'}
                        style={{
                            marginTop: 10,
                            width: '60%',
                            borderRadius: 20,
                            marginBottom: 20,
                            alignSelf: 'center',
                        }}
                        onPress={() => {
                            if (!chooseMonthOrDay && endTime < startTime)
                                Toast.show({
                                    type: 'error',
                                    text1: '结束时间不能小于开始时间',
                                });
                            props.onClose && props.onClose();
                            if (props.showWhich == 1 || props.showWhich == 2) {
                                props.callBack && props.callBack(nowTime);
                            } else {
                                if (!chooseMonthOrDay) props.callBack && props.callBack(startTime, endTime);
                                else props.callBack && props.callBack(nowTime);
                            }
                        }}
                    />
                </View>
            </View>
        );
    else return <></>;
}

const styles = StyleSheet.create({
    textContainer: {
        position: 'relative',
        width: 162,
        height: 29,
        borderRadius: 15,
        alignItems: 'center',
        justifyContent: 'center',
    },
    lineStyle: {
        width: 8.5,
        height: 1,
        borderBottomWidth: 1,
        borderBottomColor: '#333',
    },
    inputStyle: {
        width: Dimensions.get('screen').width,
        height: 1,
        borderBottomWidth: 1,
        borderBottomColor: '#dcdcdc',
    },
    touchStyle: {
        borderBottomWidth: 2,
        justifyContent: 'center',
    },
    topStyle: {
        height: '100%',
        width: '100%',
        backgroundColor: '#666',
        opacity: 0.5,
    },
    style1: {
        flexDirection: 'row',

        alignItems: 'center',
        paddingHorizontal: 15,
    },
});
