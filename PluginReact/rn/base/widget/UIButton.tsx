import {StyleProp, Text, TouchableOpacity, ViewStyle} from 'react-native';
import React, {useEffect, useState} from 'react';
import _ from 'lodash';
import TextUtils from "../utils/TextUtils";
import {Constant} from '../Constant';

interface Props {
    text: string;
    subText?: string;
    width?: number;
    height?: number;
    textColor?: string;
    subTextColor?: string;
    fontSize?: number;
    subFontSize?: number;
    fontWeight?: boolean;
    subFontWeight?: boolean;
    backgroundColor?: string;
    borderColor?: string;
    onPress?: Function;
    enabled?: boolean;
    borderRadius?: number;
    style?: StyleProp<ViewStyle> | undefined;
}

/**
 * 注释: 自定义按钮
 * 时间: 2023/8/15 0015 15:40
 * <AUTHOR>
 * @param props
 * @constructor
 */
export default function UIButton(props: Props) {
    let textColor = props?.textColor ?? '#fff';
    let subTextColor = props?.subTextColor ?? '#fff';
    let backgroundColor = props?.backgroundColor ?? Constant.color_5086fc;
    let borderColor = props.borderColor ?? Constant.color_5086fc;
    let fontSize = props?.fontSize ?? 14;
    let subFontSize = props?.subFontSize ?? 14;
    const [enabled, setEnabled] = useState(props.enabled ?? true);
    const handlePress = _.debounce(() => {
        if (enabled) {
            props.onPress && props.onPress();
        }
    }, 300);
    useEffect(() => {
        setEnabled(props.enabled ?? true);
    }, [props.enabled]);

    return (
        <TouchableOpacity
            disabled={!enabled}
            onPress={handlePress}
            style={[
                {
                    width: props.width,
                    height: props?.height ?? 45,
                    backgroundColor: enabled ? backgroundColor : Constant.color_cccccc,
                    borderWidth: 0.5,
                    borderColor: enabled ? borderColor : Constant.color_cccccc,
                    borderRadius: props.borderRadius ?? 6,
                    justifyContent: 'center',
                    alignItems: 'center',
                },
                props.style,
            ]}>
            <Text style={{color: textColor, fontSize: fontSize, fontWeight: props?.fontWeight ? 'bold' : 'normal'}}>{props.text}</Text>
            {TextUtils.isNoEmpty(props.subText) && (
                <Text
                    style={{
                        color: subTextColor,
                        fontSize: subFontSize,
                        fontWeight: props?.subFontWeight ? 'bold' : 'normal',
                    }}>
                    {props.subText}
                </Text>
            )}
        </TouchableOpacity>
    );
}
