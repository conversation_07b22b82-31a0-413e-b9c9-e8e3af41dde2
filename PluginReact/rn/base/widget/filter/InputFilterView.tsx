import {StyleSheet, Text, View} from 'react-native';
import React from 'react';
import {FilterPopupBean} from './FilterPopupBean';
import UISearchView from '../UISearchView';

interface Props {
    filterData: FilterPopupBean;
}

/**
 * 注释: 输入筛选视图
 * 时间: 2025/4/14 星期一 15:13
 * <AUTHOR>
 * @param props
 * @constructor
 */
export default function InputFilterView(props: Props) {
    return (
        <View style={{backgroundColor: '#fff'}}>
            <Text
                style={{
                    fontSize: 14,
                    color: '#333',
                    marginBottom: 10,
                    fontWeight: 'bold',
                }}>
                {props.filterData.title}
            </Text>
            <UISearchView
                backGroundStyle={{backgroundColor: '#F6F7FA'}}
                placeholder={props.filterData?.placeholder ?? '请输入'}
                clear={true}
                value={props.filterData.inputValue}
                onChangeText={(text: string) => {
                    props.filterData.inputValue = text;
                }}
                onSubmitEditing={(text: string) => {
                    props.filterData.inputValue = text;
                }}
            />
            <View style={{marginHorizontal: 10, height: 0.5, backgroundColor: '#DDDDDD', marginTop: 10}} />
        </View>
    );
}

const styles = StyleSheet.create({});
