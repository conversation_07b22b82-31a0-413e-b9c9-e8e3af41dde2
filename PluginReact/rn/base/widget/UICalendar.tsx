import {useRef, useEffect, useCallback, forwardRef, useContext, useState, useImperativeHandle, useMemo, Fragment} from 'react';
import React from 'react';
import {Animated, PanResponder, TouchableOpacity, View, Text, Easing} from 'react-native';
import XDate from 'xdate';
import {DateData, MarkedDates} from './calendar/types';
import {UpdateSources} from './calendar/expandableCalendar/commons';
import Marking from './calendar/calendar/day/marking';
import Calendar from './calendar/calendar';
import WeekCalendar from './calendar/expandableCalendar/WeekCalendar';
import Context from './calendar/expandableCalendar/Context';

interface Props {
    //     默认日期
    initialDate?: string;
    //     传入的数组
    data: MarkedDates;
    //     点击事件回调(获取当前日期和数据)
    clickEvent: (date: string, data: any) => void;
    //     不可点击日期,之前
    disabledDateBefore?: string;
    //     不可点击日期,之后
    disabledDateAfter?: string;
    showToggleButton?: boolean;
}

export interface CalendarHandles {
    setDotText: (date: string, text: string) => {date: string; dotText: string}[];
    getAllData: () => {date: string; dotText: string}[];
    //     展开视图
    toggleExpandView: () => void;
    // 收起视图
    toggleCollapseView: () => void;
    //清空选择数据
    clearAllData: () => void;
    //     根据日期删除某个dotText
    deleteDotText: (date: string) => {date: string; dotText: string}[];
    disableDayPress: () => void;
    enableDayPress: () => void;
}

const UICalendar = React.forwardRef<CalendarHandles, Props>((props, ref) => {
    const {date, setDate} = useContext(Context);
    const {data, clickEvent} = props;
    const visibleMonth = useRef(new XDate(date).getMonth() + 1);
    const [mergedData, setMergedData] = useState<MarkedDates>(data);
    const dotMarks = useMemo(() => mergedData, [mergedData]);
    const [isDayPressEnabled, setIsDayPressEnabled] = useState(true);

    useImperativeHandle(
        ref,
        () => ({
            setDotText: (date: string, text: string) => {
                let resultArray: {date: string; dotText: string}[] = [];
                setMergedData((prev) => {
                    const newData = {...prev};
                    newData[date] = {
                        ...(prev[date] || {}),
                        dotText: text?.trim(),
                        selected: true,
                        selectedColor: '#7793E8',
                        selectedTextColor: 'white',
                        dotChoosedColor: 'white',
                    };
                    // }
                    // 生成符合要求的返回数组
                    resultArray = Object.entries(newData).map(([key, value]) => ({
                        date: key,
                        dotText: value.dotText ?? '',
                    }));

                    return newData;
                });
                return resultArray;
            },
            getAllData: () => {
                let newObj: {date: string; dotText: string}[] = [];
                Object.keys(mergedData).forEach((date) => {
                    newObj.push({
                        date: date,
                        dotText: mergedData[date]?.dotText ?? '',
                    });
                });
                return newObj;
            },
            toggleExpandView: () => {
                setIsExpanded(true);
            },
            clearAllData: () => {
                setMergedData({});
            },
            toggleCollapseView: () => {
                setIsExpanded(false);
            },
            deleteDotText: (date: string) => {
                let resultArray: {date: string; dotText: string}[] = [];
                setMergedData((prev) => {
                    const newData = {...prev};
                    delete newData[date];
                    // 生成符合要求的返回数组
                    resultArray = Object.entries(newData)
                        .filter(([_, value]) => value.dotText?.trim())
                        .map(([key, value]) => ({
                            date: key,
                            dotText: value.dotText ?? '',
                        }));
                    return newData;
                });
                return resultArray;
            },
            disableDayPress: () => setIsDayPressEnabled(false),
            enableDayPress: () => setIsDayPressEnabled(true),
        }),
        [clickEvent],
    ); // 添加必要依赖
    const handleDayPress = useCallback(
        (day: {dateString: string}) => {
            setDate?.(day.dateString, UpdateSources.DAY_PRESS);
            setMergedData((prev) => {
                // 首先将旧数据的所有选中状态取消
                Object.keys(prev).forEach((date) => {
                    // if (prev[date].dotText != undefined && prev[date].dotText != '') {
                    //     prev[date].selected = true;
                    //     prev[date].selectedColor = undefined;
                    //     prev[date].dotChoosedColor = undefined;
                    //     prev[date].selectedTextColor = undefined;
                    // } else {
                    //     prev[date].selected = false;
                    //     prev[date].selectedColor = 'red';
                    // }
                    prev[date].selected = true;
                    prev[date].selectedColor = '#F5F7FD';
                    prev[date].selectedTextColor = 'black';
                    prev[date].dotChoosedColor = '#7793E8';
                });
                // Object.keys(prev).forEach((date) => {
                //     if (!prev[date].selected) {
                //         delete prev[date];
                //     }
                // });

                const newData = {
                    ...prev,
                    [day.dateString]: {
                        selected: true,
                        dotText: prev[day.dateString]?.dotText,
                        selectedColor: !isDayPressEnabled ? 'white' : '#7793E8',
                        selectedTextColor: !isDayPressEnabled ? 'black' : 'white',
                        dotChoosedColor: !isDayPressEnabled ? 'black' : 'white',
                    },
                };
                setTimeout(() => {
                    clickEvent(day.dateString, newData);
                }, 100);
                return newData;
            });
        },
        [clickEvent, isDayPressEnabled],
    );
    const handleWeekScroll = useCallback(
        (newDate: DateData) => {
            const newMonth = new XDate(newDate.dateString).getMonth() + 1;
            if (newMonth !== visibleMonth.current) {
                setDate?.(newDate.dateString, UpdateSources.PAGE_SCROLL);
            }
        },
        [setDate],
    );
    useEffect(() => {
        const xDate = new XDate(date);
        const newMonth = xDate.getMonth() + 1;
        // 无论是否第一天都强制更新月份标识
        if (newMonth !== visibleMonth.current) {
            visibleMonth.current = newMonth;
        }
    }, [date]); // 确保依赖项只有 date

    const handleMonthChange = useCallback(
        (newDate: DateData) => {
            const newXDate = new XDate(newDate.dateString);
            // 处理跨年切换的情况（如12月切1月）
            const firstDayOfMonth = newXDate.getMonth() === 11 ? newXDate.setDate(1).setFullYear(newXDate.getFullYear() + 1) : newXDate.setDate(1);
            setDate?.(firstDayOfMonth.toString('yyyy-MM-dd'), UpdateSources.PAGE_SCROLL);
        },
        [setDate],
    );

    const [isExpanded, setIsExpanded] = useState(true);

    const toggleView = useCallback(() => {
        setIsExpanded(!isExpanded);
    }, [isExpanded]);

    return (
        <Fragment>
            <Calendar
                current={date}
                onDayPress={handleDayPress}
                markingType={Marking.markings.DOT}
                markedDates={dotMarks}
                enableSwipeMonths={true}
                disableArrowLeft={false}
                disableArrowRight={false}
                minDate={props.disabledDateBefore?.toString() ?? '1900-01-01'}
                maxDate={props.disabledDateAfter?.toString() ?? '9999-12-31'}
                onMonthChange={handleMonthChange}
                key={visibleMonth.current}
                showDays={isExpanded}
                hideDayNames={!isExpanded}
            />

            {!isExpanded && (
                <WeekCalendar
                    calendarHeight={60}
                    current={date}
                    disableAllTouchEventsForInactiveDays={true}
                    disableAllTouchEventsForDisabledDays={true}
                    minDate={props.disabledDateBefore?.toString() ?? '1900-01-01'}
                    maxDate={props.disabledDateAfter?.toString() ?? '9999-12-31'}
                    markedDates={dotMarks}
                    onDayPress={handleDayPress}
                    onMonthChange={handleWeekScroll}
                />
            )}
            {props.showToggleButton && (
                <TouchableOpacity onPress={toggleView} style={{height: 30, justifyContent: 'center'}}>
                    <Text style={{textAlign: 'center', color: '#7495ED'}}>{isExpanded ? '收起' : '展开'}</Text>
                </TouchableOpacity>
            )}
        </Fragment>
    );
});

export default UICalendar;
