import React from 'react';
import {View, StyleSheet} from 'react-native';

const UICustomProgressBar = ({progress}) => {
    return (
        <View style={styles.container}>
            <View style={[styles.progress, {width: `${progress * 100}%`}]} />
        </View>
    );
};

const styles = StyleSheet.create({
    container: {
        height: 9,
        width: 75, // 你可以根据需要调整宽度
        backgroundColor: '#e0e0e0', // 浅灰色背景
        borderRadius: 5, // 圆角
        overflow: 'hidden', // 确保圆角生效时内容不会溢出
    },
    progress: {
        height: '100%',
        backgroundColor: '#4285F4', // 蓝色进度条（可替换）
        borderRadius: 5,
    },
});

export default UICustomProgressBar;
