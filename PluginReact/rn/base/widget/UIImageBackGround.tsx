import {Platform, StyleProp, ViewStyle} from 'react-native';
import React, {useEffect, useState} from 'react';
import FastImage, {ImageStyle, ResizeMode} from 'react-native-fast-image';
import TextUtils from '../utils/TextUtils';

export enum ImageFormat {
    PNG,
    JPG,
    GIF,
}

interface Props {
    //图片链接
    source: string;
    //文件格式
    imageFormat?: ImageFormat;
    //默认图片
    placeholder?: string;
    //子控件
    children?: JSX.Element | JSX.Element[];
    //显示模式
    resizeMode?: ResizeMode;
    //样式
    style?: StyleProp<ImageStyle> & StyleProp<ViewStyle>;
}

/**
 * 注释: 通用图片加载控件（全局图片加载请都使用这个，图片资源热更都是基于这个控件）
 * 时间: 2023/7/24 0024 16:45
 * <AUTHOR>
 * @param props
 * @constructor
 */
export default function UIImageBackGround(props: Props) {
    const [imageUri, setImageUri] = useState(props.source);

    useEffect(() => {
        //在listView 列表中出现复用时候，props.source 内容变了，但imageUri 未变化导致图片不刷新，使用上次地址
        if (!TextUtils.equals(imageUri, props.source)) {
            setImageUri(props.source);
        }
    }, [props.source]);

    /**
     * 注释: 获取文件格式
     * 时间: 2023/7/24 0024 17:04
     * <AUTHOR>
     * @param format
     * @returns {string}
     */
    function getImageFormat(format: ImageFormat) {
        switch (format) {
            case ImageFormat.GIF:
                return 'gif';
            case ImageFormat.JPG:
                return 'jpg';
            case ImageFormat.PNG:
                return 'png';
            default:
                break;
        }
    }

    return (
        <FastImage
            source={{uri: imageUri}}
            style={props.style}
            resizeMode={props.resizeMode ?? 'contain'}
            onError={async () => {
                if (imageUri.startsWith('http')) {
                    //线上查找失败，直接使用默认图
                    let placeholder = props.placeholder ?? 'icon_hyxsxxg_def';
                    let appAssetsPath =
                        Platform.OS === 'ios' ? `assets/images/${placeholder}.${getImageFormat(props.imageFormat ?? ImageFormat.PNG)}` : `file:///android_asset/images/${placeholder}.${getImageFormat(props.imageFormat ?? ImageFormat.PNG)}`;
                    setImageUri(appAssetsPath);
                } else {
                    if (Platform.OS === 'ios') {
                        setImageUri(`assets/images/${props.source}.${getImageFormat(props.imageFormat ?? ImageFormat.PNG)}`);
                    } else {
                        //res图片加载失败，则从App Assets目录中查找
                        let appAssetsPath = `file:///android_asset/images/${props.source}.${getImageFormat(props.imageFormat ?? ImageFormat.PNG)}`;
                        setImageUri(appAssetsPath);
                    }
                }
            }}>
            {props.children}
        </FastImage>
    );
}
