import {StyleSheet, Text, View, ViewStyle} from 'react-native';
import React from 'react';
import Modal from 'react-native-modal';
import UIImage from './UIImage';
import UITouchableOpacity from './UITouchableOpacity';

interface Props {
    title?: string;
    subTitle?: string;
    showSubTitle?: boolean;
    headView?: JSX.Element;
    children: React.ReactNode;
    onClose?: () => void;
    onSubCallBack?: () => void;
    style?: ViewStyle;
    modalStyle?: ViewStyle;
}

/**
 * 注释:  底部弹窗
 * 时间: 2023/11/2 0002 14:53
 * <AUTHOR>
 * @param props
 * @constructor
 */
export default function UIPopup(props: Props) {
    /**
     * 注释: 绘制默认头部
     * 时间: 2023/12/8 0008 11:32
     * <AUTHOR>
     * @returns {JSX.Element}
     */
    const renderHeadView = () => {
        return (
            <View style={styles.headStyle}>
                {/*关闭按钮*/}
                <UITouchableOpacity onPress={props.onClose} style={styles.closeImage}>
                    <UIImage source={'base_close_x_grey'} style={styles.close_icon} />
                </UITouchableOpacity>
                {/*主标题*/}
                <Text style={styles.main_title_txt}>{props.title}</Text>
                {/*副标题*/}
                {props.showSubTitle && (
                    <UITouchableOpacity onPress={props.onSubCallBack} style={styles.send_title_txt}>
                        <Text style={styles.subTitleStyle}>{props.subTitle}</Text>
                    </UITouchableOpacity>
                )}
            </View>
        );
    };

    return (
        <Modal style={[{justifyContent: 'flex-end', padding: 0, margin: 0}, props.modalStyle]} animationIn={'slideInUp'} backdropOpacity={0.3} useNativeDriver={true} isVisible={true} onBackButtonPress={props.onClose} onBackdropPress={props.onClose}>
            <View style={[styles.body, props.style]}>
                {props.headView ?? renderHeadView()}
                {props.children}
            </View>
        </Modal>
    );
}

const styles = StyleSheet.create({
    body: {backgroundColor: '#fff', minHeight: 200},
    close_icon: {width: 15, height: 15},
    main_title_txt: {fontSize: 17, color: '#333', fontWeight: 'bold'},
    send_title_txt: {
        position: 'absolute',
        right: 7,
    },
    headStyle: {
        flexDirection: 'row',
        justifyContent: 'center',
        alignItems: 'center',
        height: 45,
    },
    closeImage: {
        position: 'absolute',
        left: 0,
        width: 45,
        height: 45,
        justifyContent: 'center',
        alignItems: 'center',
    },
    subTitleStyle: {
        fontSize: 14,
        color: '#5086fc',
    },
});
