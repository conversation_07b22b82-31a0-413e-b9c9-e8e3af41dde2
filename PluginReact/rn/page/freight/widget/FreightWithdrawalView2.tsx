import {StyleSheet, View} from 'react-native';
import React, {useEffect, useRef, useState} from 'react';
import UIListView from '../../../base/widget/UIListView';
import {ReqFreightList, RsqFreightList} from '../requests/ReqQueryDrawMoneyList';
import FreightWithdrawalItemView2 from './FreightWithdrawalItemView2';

interface Props {
    queryType: number;
}

/**
 * 注释: 提款申请-多渠道-已申请
 * 时间: 2025/7/15 13:47
 * <AUTHOR>
 * @param props
 * @constructor
 */
export default function FreightWithdrawalView2(props: Props) {
    const [listData, setListData] = useState<RsqFreightList[]>();
    const nowPage = useRef<number>(1);

    // 页面初始化
    useEffect(() => {
        onRefreshAndLoadMoreListener();
    }, []);
    // 加载数据
    const onRefreshAndLoadMoreListener = () => {
        // 如果是第一页，则生成假数据
        if (nowPage.current === 1) {
            const mockData = generateMockData(10);
            setListData([]);
            nowPage.current++;
        } else {
            let request = new ReqFreightList();
            request.request().then((res) => {
                if (res.isSuccess()) {
                    if (nowPage.current == 1) {
                        setListData(res.data?.rootArray);
                    } else {
                        setListData([...(listData ?? []), ...(res.data?.rootArray ?? [])]);
                    }
                    nowPage.current++;
                }
            });
        }
    };

    // 生成假数据的函数
    const generateMockData = (count: number): RsqFreightList[] => {
        const mockData: RsqFreightList[] = [];
        for (let i = 0; i < count; i++) {
            mockData.push({
                bankName: `银行名称 ${nowPage.current * 10 + i + 1}`,
                // 根据 PaymentOrderDetail 接口添加其他字段
            });
        }
        return mockData;
    };
    const renderComView = (item, index) => {
        return <FreightWithdrawalItemView2 item={item} />;
    };
    return (
        <View style={styles.container}>
            {/*绘制列表视图*/}
            <UIListView
                contentContainerStyle={{backgroundColor: '#EFF0F3'}}
                renderItem={({item, index}) => renderComView(item, index)}
                dataList={listData}
                onRefresh={() => {
                    nowPage.current = 1;
                    onRefreshAndLoadMoreListener();
                }}
                onLoadMore={() => {
                    onRefreshAndLoadMoreListener();
                }}
            />
        </View>
    );
}

const styles = StyleSheet.create({
    container: {
        backgroundColor: '#EFF0F3',
        flex: 1,
    },
});
