import {StyleSheet, Text, View} from 'react-native';
import React, {useEffect, useState} from 'react';
import UITouchableOpacity from '../../../base/widget/UITouchableOpacity';
import UIImage from '../../../base/widget/UIImage';
import {RsqQueryDrawMoneyList} from '../requests/ReqQueryDrawMoneyList';
import TextUtils from '../../../base/utils/TextUtils';

interface Props {
    item: RsqQueryDrawMoneyList;
    isSelect: boolean;
    onCallBack: Function;
}

/**
 * 注释: 申请提款-多渠道-待申请
 * 时间: 2025/7/15 15:22
 * <AUTHOR>
 * @param props
 * @constructor
 */
export default function FreightWithdrawalItemView1(props: Props) {
    const [isSelect, setSelect] = useState(props.isSelect);

    useEffect(() => {
        setSelect(props.isSelect);
    }, [props.isSelect]);
    const renderRow1 = () => {
        return (
            <UITouchableOpacity
                style={{flexDirection: 'row', flex: 1}}
                onPress={() => {
                    props.onCallBack && props.onCallBack(isSelect);
                    setSelect(!isSelect);
                }}>
                <UIImage source={isSelect ? 'freight_checked_icon_1' : 'freight_checked_icon_2'} style={{width: 16, height: 16, marginEnd: 15}} />
                <Text
                    style={{
                        flex: 1,
                        color: '#333333',
                        fontSize: 16,
                        fontWeight: 'bold',
                        marginStart: 15,
                        paddingBottom: 5,
                    }}>
                    {props.item.orderId}
                </Text>
                <Text
                    style={{
                        color: '#666666',
                        fontSize: 16,
                        marginStart: 15,
                        paddingBottom: 5,
                    }}>
                    {props.item.orderCurrentState}
                </Text>
            </UITouchableOpacity>
        );
    };
    const renderRow2 = () => {
        return (
            <View style={styles.row1}>
                <Text style={[styles.label, {flex: 1, fontWeight: 'bold', color: '#333333'}]}>{props.item.allCargoName}</Text>
                <Text style={[styles.label, {textAlign: 'right', color: '#FFB02E'}]}>{props.item.signedStr}</Text>
            </View>
        );
    };
    const renderRow = (label: string, value: string, infoIcon?: boolean, color?: string, onPress?: any) => {
        return (
            <View style={styles.row1}>
                <View style={styles.row2} key={label}>
                    <Text style={styles.label}>{label}</Text>
                    {infoIcon && (
                        <UITouchableOpacity onPress={onPress}>
                            <UIImage source={'ic_wait'} style={styles.infoIcon} />
                        </UITouchableOpacity>
                    )}
                </View>
                <Text style={[styles.value, {color: color ?? '#333333'}]}>{value}</Text>
            </View>
        );
    };
    return (
        <View style={styles.contentContainer}>
            {renderRow1()}
            {renderRow2()}
            {renderRow('发货运费：', props.item.deliverMoney ?? '')}
            {renderRow('确认发货时间：', props.item.startTime ?? '')}
            {renderRow('资金方：', props.item.moneySourceStr ?? '')}
            {renderRow('预计到达时间：', props.item.estimateReceiveTime ?? '')}
            {renderRow('是否到达卸货地：', TextUtils.equals('1', props.item.reachUnloadingLocation) ? '到达' : '没到达')}
        </View>
    );
}

const styles = StyleSheet.create({
    contentContainer: {
        backgroundColor: '#FFFFFF',
        marginTop: 7,
        paddingLeft: 15,
        paddingRight: 15,
        paddingBottom: 6,
        paddingTop: 6,
    },
    row1: {
        flexDirection: 'row',
        marginTop: 3,
    },
    row2: {
        flexDirection: 'row',
        alignItems: 'center',
        flex: 1.4,
    },
    label: {
        fontSize: 14,
        color: '#666666',
    },
    value: {
        fontSize: 14,
        color: '#333333',
        flex: 2,
    },
    infoIcon: {
        width: 14,
        height: 14,
        marginLeft: 3,
    },
    modalBackground: {
        backgroundColor: 'rgba(0, 0, 0, 0.5)',
        justifyContent: 'center',
        alignItems: 'center',
    },
    modalContainer: {
        paddingLeft: 7,
        paddingRight: 7,
        borderRadius: 5,
    },
    modalContent: {
        alignItems: 'center',
    },
    modalTitle: {
        fontSize: 16,
        color: '#FFFFFF',
    },
});
