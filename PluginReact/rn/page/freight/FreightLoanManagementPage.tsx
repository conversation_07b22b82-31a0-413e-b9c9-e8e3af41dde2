import {StyleSheet, Text, TouchableOpacity, View} from 'react-native';
import React, {createRef} from 'react';
import {NativeStackScreenProps} from '@react-navigation/native-stack';
import {ParamListBase} from '@react-navigation/native';
import BaseCommPage from '../../base/comm/BasePage';
import UITitleView from '../../base/widget/UITitleView';
import UIImage from '../../base/widget/UIImage';
import UIFlatListView, {UIController, UIFlatListViewRef} from '../../base/widget/UIFlatListView';
import {ReqQueryOwnerCardList, RsqBankList} from '../wallet/requests/ReqQueryOwnerCardList';
import {ArrayUtils} from '../../base/utils/ArrayUtils';
import UITouchableOpacity from '../../base/widget/UITouchableOpacity';
import SettlementInfoModal from './dialog/SettlementInfoModalProps';

interface State {
    showSettlementInfoModal: boolean;
}

/**
 * 注释: 运费通-放款管理
 * 时间: 2025/7/9 11:50
 * <AUTHOR>
 */
export default class FreightLoanManagementPage extends BaseCommPage<NativeStackScreenProps<ParamListBase>, State> {
    //列表ref
    uIFlatListViewRef = createRef<UIFlatListViewRef>();

    constructor(props) {
        super(props);
        this.state = {
            showSettlementInfoModal: false, // 控制弹窗显示状态
        };
    }

    // 加载数据
    onRefreshAndLoadMoreListener = (lv: UIController, index: number) => {
        let request = new ReqQueryOwnerCardList();
        request.request().then((res) => {
            if (res.isSuccess()) {
                lv.setData(res.data?.nowPage ?? 1, res.data?.totalPage ?? 1, res.data?.rootArray ?? []);
            } else {
                lv.onFail();
            }
        });
    };
    renderDialogView = () => {
        return (
            <TouchableOpacity style={styles.modalBackground} onPress={() => {}}>
                <View style={styles.modalContainer}>
                    <View style={styles.modalContent}>
                        <Text style={styles.modalTitle}>运费实际结算金额</Text>
                    </View>
                </View>
            </TouchableOpacity>
        );
    };
    renderComView = (item: RsqBankList, index: number) => {
        return (
            <View style={styles.contentContainer}>
                {this.renderRow('申请提款编号', item.bankName)}
                {this.renderRow('运单号', item.bankName)}
                {this.renderRow('发货运费(含税)', item.bankName)}
                {this.renderRow('结算金额', item.bankName, true, () => {
                    this.setState({showSettlementInfoModal: true});
                })}
                {this.renderRow('放款金额', item.bankName, true, () => {
                    this.showToast('');
                })}
                {this.renderRow('退款金额', item.bankName, true, () => {
                    this.showToast('');
                })}
                {this.renderRow('优惠奖励(销售折让)', item.bankName)}
                {this.renderRow('结算用款金额', item.bankName, true, () => {
                    this.showToast('');
                })}
                {this.renderRow('放款时间', item.bankName)}
                {this.renderRow('退款状态', item.bankName)}
                {this.renderRow('退款时间', item.bankName)}
                {this.renderRow('资金方', item.bankName)}
            </View>
        );
    };
    renderRow = (label: string, value: string, infoIcon?: boolean, onPress?: any) => {
        return (
            <View style={styles.row1}>
                <View style={styles.row2} key={label}>
                    <Text style={styles.label}>{label}</Text>
                    {infoIcon && (
                        <UITouchableOpacity onPress={onPress}>
                            <UIImage source={'ic_wait'} style={styles.infoIcon} />
                        </UITouchableOpacity>
                    )}
                </View>
                <Text style={styles.value}>{value}</Text>
            </View>
        );
    };

    render() {
        return (
            <View style={styles.container}>
                <UITitleView title={'放款管理'} />
                <UIFlatListView ref={this.uIFlatListViewRef} renderItemView={this.renderComView} autoRefresh={true} onRefreshAndLoadMoreListener={this.onRefreshAndLoadMoreListener} />
                {/* 弹窗组件 */}
                {this.state.showSettlementInfoModal && this.renderDialogView()}
                {/*<SettlementInfoModal isVisible={this.state.showSettlementInfoModal} onClose={() => this.setState({showSettlementInfoModal: false})} />*/}
            </View>
        );
    }
}

const styles = StyleSheet.create({
    container: {
        backgroundColor: '#EFF0F3',
        flex: 1,
    },
    contentContainer: {
        backgroundColor: '#FFFFFF',
        marginTop: 7,
        paddingLeft: 15,
        paddingRight: 15,
        paddingBottom: 6,
        paddingTop: 6,
    },
    row1: {
        flexDirection: 'row',
        marginTop: 3,
    },
    row2: {
        flexDirection: 'row',
        alignItems: 'center',
        flex: 1.4,
    },
    label: {
        fontSize: 14,
        color: '#666666',
    },
    value: {
        fontSize: 14,
        color: '#333333',
        flex: 2,
    },
    infoIcon: {
        width: 14,
        height: 14,
        marginLeft: 3,
    },
    modalBackground: {
        backgroundColor: 'rgba(0, 0, 0, 0.5)',
        justifyContent: 'center',
        alignItems: 'center',
    },
    modalContainer: {
        paddingLeft: 7,
        paddingRight: 7,
        borderRadius: 5,
    },
    modalContent: {
        alignItems: 'center',
    },
    modalTitle: {
        fontSize: 16,
        color: '#FFFFFF',
    },
});
