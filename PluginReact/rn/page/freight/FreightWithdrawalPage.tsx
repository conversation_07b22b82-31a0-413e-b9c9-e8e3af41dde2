import {Dimensions, StyleSheet, Text, View} from 'react-native';
import React from 'react';
import {SceneMap, TabView} from 'react-native-tab-view';
import BaseCommPage from '../../base/comm/BasePage';
import {NativeStackScreenProps} from '@react-navigation/native-stack';
import {ParamListBase} from '@react-navigation/native';
import {ZRouter} from '../../base/comm/ZRouter';
import UITitleView from '../../base/widget/UITitleView';
import UITouchableOpacity from '../../base/widget/UITouchableOpacity';
import {gScreen_width} from '../../base/Const';
import FreightWithdrawalView1 from './widget/FreightWithdrawalView1';
import FreightWithdrawalView2 from './widget/FreightWithdrawalView2';

interface State {
    tabIndex: number;
}

/**
 *  desc: 申请提款-多渠道
 *  user: 宋双朋
 *  time: 2025/7/15 14:11
 */
export default class FreightWithdrawalPage extends BaseCommPage<NativeStackScreenProps<ParamListBase>, State> {
    pageParams: any;
    orderId: string;

    constructor(props) {
        super(props);
        this.state = {
            tabIndex: 0,
        };
        this.pageParams = ZRouter.getParams(props);
        this.orderId = this.pageParams.orderId;
    }

    render() {
        return (
            <View style={{flex: 1}}>
                <UITitleView title={'申请提款'} />
                {this.renderMainView()}
            </View>
        );
    }

    /**
     * 注释：绘制TabBar
     * 时间：2025/7/15 14:11
     * @author：宋双朋
     * @param index
     */
    renderTabBar(index: number) {
        return (
            <View style={styles.tabBarStyle}>
                <UITouchableOpacity
                    style={styles.tabItemStyle}
                    onPress={() => {
                        this.setState({tabIndex: 0});
                    }}>
                    <Text style={{fontSize: 15, color: '#333'}}>待申请</Text>
                    {index == 0 && <View style={{width: 73, height: 3, backgroundColor: '#5086FC', position: 'absolute', bottom: 0}} />}
                </UITouchableOpacity>
                <UITouchableOpacity
                    style={styles.tabItemStyle}
                    onPress={() => {
                        this.setState({tabIndex: 1});
                    }}>
                    <Text style={{fontSize: 15, color: '#333'}}>已申请</Text>
                    {index == 1 && <View style={{width: 73, height: 3, backgroundColor: '#5086FC', position: 'absolute', bottom: 0}} />}
                </UITouchableOpacity>
            </View>
        );
    }

    /**
     * 注释：绘制主视图
     * 时间：2025/7/15 14:12
     * @author：宋双朋
     */
    renderMainView() {
        //界面title
        let routes: {key: string; title: string}[];
        //tabs页面
        let renderScene: any;
        routes = [
            {key: 'first', title: '待申请'},
            {key: 'second', title: '已申请'},
        ];
        renderScene = SceneMap({
            first: () => {
                return <FreightWithdrawalView1 queryType={1} />;
            },
            second: () => {
                return <FreightWithdrawalView2 queryType={2} />;
            },
        });
        return (
            <TabView
                navigationState={{index: this.state.tabIndex, routes}}
                renderScene={renderScene}
                onIndexChange={(tabIndex) => {
                    this.setState({tabIndex: tabIndex});
                }}
                renderTabBar={() => this.renderTabBar(this.state.tabIndex)}
                initialLayout={{width: Dimensions.get('window').width}}
            />
        );
    }
}

const styles = StyleSheet.create({
    tabBarStyle: {
        width: gScreen_width,
        height: (gScreen_width / 375) * 42,
        flexDirection: 'row',
        alignItems: 'center',
        backgroundColor: '#fff',
    },
    tabItemStyle: {
        flex: 1,
        height: (gScreen_width / 375) * 42,
        alignItems: 'center',
        justifyContent: 'center',
    },
});
