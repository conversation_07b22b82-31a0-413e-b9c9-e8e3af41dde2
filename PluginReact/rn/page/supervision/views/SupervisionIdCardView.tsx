import {StyleSheet, Text, View} from 'react-native';
import React, {useImperativeHandle} from 'react';
import ComCertificationUploadImgView from '../../../base/widget/ComCertificationUploadImgView';
import TextUtils from '../../../base/utils/TextUtils';
import UIFormInputView, {UIFormInputViewRef} from '../../../base/widget/UIFormInputView';
import {dateFormat} from '../../../base/utils/DateUtil';
import {idCardLicenseOcr, ReqIdCardLicenseOcr} from '../requests/SupervisionDto';
import {success} from '../../../base/http/Http';
import {DialogBuilder} from '../../../base/comm/UI';

interface Props {
    callback: Function;
}

/**
 * 注释: 监管账户资料补充/更新-身份证信息
 * 时间: 2025/3/31 19:19
 * <AUTHOR>
 * @param props
 * @constructor
 */
function SupervisionIdCardView(props: Props, ref: React.Ref<SupervisionIdCardViewRef>) {
    const [url1, setUrl1] = React.useState('');
    const [url2, setUrl2] = React.useState('');
    const [name, setName] = React.useState('');
    const [startTime, setStartTime] = React.useState('');
    const [endTime, setEndTime] = React.useState('');
    //姓名Ref
    const nameRef = React.createRef<UIFormInputViewRef>();
    const mobileRef = React.createRef<UIFormInputViewRef>();
    const startTimeRef = React.createRef<UIFormInputViewRef>();
    const endTimeRef = React.createRef<UIFormInputViewRef>();
    // 对外部公开调用方法
    useImperativeHandle(ref, () => ({setStartTime, setEndTime, getStartTime, getEndTime, getUrl1, getUrl2, getName, getMobile}));
    const getStartTime = () => {
        return startTime;
    };
    const getEndTime = () => {
        return endTime;
    };
    const getUrl1 = () => {
        return url1;
    };
    const getUrl2 = () => {
        return url2;
    };
    const getName = () => {
        return nameRef.current?.getValue() ?? '';
    };
    const getMobile = () => {
        return mobileRef.current?.getValue() ?? '';
    };
    const idCardOcr = (url: string, type: number) => {
        let req = new ReqIdCardLicenseOcr();
        switch (type) {
            case 1:
                req.idCardFrontLicenseUrl = url;
                break;
            case 2:
                req.idCardBackLicenseUrl = url;
                break;
        }
        idCardLicenseOcr(req).then((res) => {
            if (success(res)) {
                switch (type) {
                    case 1:
                        setName(res.data.userName);
                        break;
                    case 2:
                        setStartTime(TextUtils.isEmpty(res.data.validityStart) ? '' : dateFormat(res.data.validityStart, 'yyyy-MM-dd'));
                        setEndTime(TextUtils.isEmpty(res.data.validityEnd) ? '' : dateFormat(res.data.validityEnd, 'yyyy-MM-dd'));
                        break;
                }
            } else {
                new DialogBuilder().setTitle('提示').setMsg(res.getMsg()).setModel(1).setRightTxt('我知道了').show();
            }
        });
    };
    return (
        <View style={{backgroundColor: '#FFFFFF', padding: 15, marginTop: 1}}>
            <ComCertificationUploadImgView
                ivWarn={false}
                style={{
                    backgroundColor: '#FFFFFF',
                }}
                tvTitle1View={
                    <View>
                        <Text style={styles.titleStyle}>{'身份证照片'}</Text>
                        <Text style={styles.contentStyle}>{'拍摄身份证正反面，注意反光，保证身份证的内容清晰'}</Text>
                    </View>
                }
                layout={1}
                leftImg={{
                    url: url1 ?? '',
                    defaultValue: 'app_icon_1',
                    onDelPic: () => {
                        setUrl1('');
                    },
                    onPostSuccess: (url) => {
                        setUrl1(url);
                        idCardOcr(url, 1);
                    },
                }}
                rightImg={{
                    url: url2 ?? '',
                    defaultValue: 'app_icon_2',
                    onDelPic: () => {
                        setUrl2('');
                    },
                    onPostSuccess: (url) => {
                        setUrl2(url);
                        idCardOcr(url, 2);
                    },
                }}
            />
            <UIFormInputView label={'注册人姓名'} value={name} showError={false} isRequired={false} edit={true} ref={nameRef} onChangeText={() => {}} />
            <UIFormInputView label={'注册人手机号'} showError={false} isRequired={false} edit={true} ref={mobileRef} onChangeText={() => {}} />
            <UIFormInputView
                label={'注册人身份证有效期开始时间'}
                showError={false}
                isRequired={false}
                edit={true}
                isSelect={true}
                ref={startTimeRef}
                value={startTime}
                onSelect={() => {
                    props.callback && props.callback(1);
                }}
            />
            <UIFormInputView
                label={'注册人身份证有效期结束时间'}
                showError={false}
                isRequired={false}
                edit={true}
                isSelect={true}
                ref={endTimeRef}
                value={endTime}
                onSelect={() => {
                    props.callback && props.callback(2);
                }}
            />
        </View>
    );
}

export interface SupervisionIdCardViewRef {
    setStartTime: (value: any) => void;
    setEndTime: (value: any) => void;
    getStartTime: () => string;
    getEndTime: () => string;
    getUrl1: () => string;
    getUrl2: () => string;
    getName: () => string;
    getMobile: () => string;
}

export default React.forwardRef<SupervisionIdCardViewRef, Props>(SupervisionIdCardView);
const styles = StyleSheet.create({
    titleStyle: {
        fontSize: 16,
        color: '#333333',
    },
    contentStyle: {
        fontSize: 13,
        color: '#888888',
        marginTop: 5,
    },
});
