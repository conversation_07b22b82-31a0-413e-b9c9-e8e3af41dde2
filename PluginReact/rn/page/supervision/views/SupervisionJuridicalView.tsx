import {StyleSheet, Text, View} from 'react-native';
import React, {useEffect, useImperativeHandle} from 'react';
import ComCertificationUploadImgView from '../../../base/widget/ComCertificationUploadImgView';
import TextUtils from '../../../base/utils/TextUtils';
import UIFormInputView, {UIFormInputViewRef} from '../../../base/widget/UIFormInputView';
import {dateFormat} from '../../../base/utils/DateUtil';
import {idCardLicenseOcr, queryBeneficiaryInfo, ReqIdCardLicenseOcr, ReqQueryBeneficiaryInfo, RspQueryBeneficiaryInfo} from '../requests/SupervisionDto';
import {msg, success} from '../../../base/http/Http';
import {DialogBuilder, ToastBuilder} from '../../../base/comm/UI';

interface Props {
    nameCallback: Function;
    callback: Function;
}

/**
 * 注释: 监管账户资料补充/更新-法人信息
 * 时间: 2025/3/31 19:19
 * <AUTHOR>
 * @param props
 * @constructor
 */
function SupervisionJuridicalView(props: Props, ref: React.Ref<SupervisionJuridicalViewRef>) {
    const [url1, setUrl1] = React.useState('');
    const [url2, setUrl2] = React.useState('');
    const [name, setName] = React.useState('');
    const [idCardNo, setIdCardNo] = React.useState('');
    const [address, setAddress] = React.useState('');
    const [startTime, setStartTime] = React.useState('');
    const [endTime, setEndTime] = React.useState('');
    const [ocrState, setOcrState] = React.useState(false);
    //姓名Ref
    const nameRef = React.createRef<UIFormInputViewRef>();
    const mobileRef = React.createRef<UIFormInputViewRef>();
    const idCardRef = React.createRef<UIFormInputViewRef>();
    const addressRef = React.createRef<UIFormInputViewRef>();
    const startTimeRef = React.createRef<UIFormInputViewRef>();
    const endTimeRef = React.createRef<UIFormInputViewRef>();
    useEffect(() => {
        queryBeneficiaryInfoV1();
    }, [name]);
    // 对外部公开调用方法
    useImperativeHandle(ref, () => ({setStartTime, setEndTime, getStartTime, getEndTime, getUrl1, getUrl2, getName, getIdCardNo, getMobile, getAddress}));
    const getStartTime = () => {
        return startTime;
    };
    const getEndTime = () => {
        return endTime;
    };
    const getUrl1 = () => {
        return url1;
    };
    const getUrl2 = () => {
        return url2;
    };
    const getName = () => {
        return nameRef.current?.getValue() ?? '';
    };
    const getIdCardNo = () => {
        return idCardRef.current?.getValue() ?? '';
    };
    const getMobile = () => {
        return mobileRef.current?.getValue() ?? '';
    };
    const getAddress = () => {
        return addressRef.current?.getValue() ?? '';
    };

    const queryBeneficiaryInfoV1 = () => {
        let item = new RspQueryBeneficiaryInfo();
        item.beneficiaryName = getName();
        item.beneficiaryIdCardNo = getIdCardNo();
        item.beneficiaryStartTimeStr = getStartTime();
        item.beneficiaryEndTimeStr = getEndTime();
        item.beneficiaryFrontUrl = getUrl1();
        item.beneficiaryBackUrl = getUrl2();
        props.nameCallback && props.nameCallback(item);
    };
    const idCardOcr = (url: string, type: number) => {
        let req = new ReqIdCardLicenseOcr();
        switch (type) {
            case 1:
                req.idCardFrontLicenseUrl = url;
                break;
            case 2:
                req.idCardBackLicenseUrl = url;
                break;
        }
        idCardLicenseOcr(req).then((res) => {
            if (success(res)) {
                switch (type) {
                    case 1:
                        setIdCardNo(res.data.idCardNo);
                        setAddress(res.data.address);
                        setName(res.data.userName);
                        break;
                    case 2:
                        setStartTime(TextUtils.isEmpty(res.data.validityStart) ? '' : dateFormat(res.data.validityStart, 'yyyy-MM-dd'));
                        setEndTime(TextUtils.isEmpty(res.data.validityEnd) ? '' : dateFormat(res.data.validityEnd, 'yyyy-MM-dd'));
                        break;
                }
            } else {
                new DialogBuilder().setMsg(msg(res)).setModel(1).setRightTxt('我知道了').show();
            }
        });
    };
    return (
        <View style={{backgroundColor: '#FFFFFF', padding: 15, marginTop: 1}}>
            <Text style={{fontSize: 15, color: '#333333', fontWeight: 'bold', marginBottom: 7}}>{'法人信息'}</Text>
            <ComCertificationUploadImgView
                ivWarn={false}
                style={{
                    backgroundColor: '#FFFFFF',
                }}
                tvTitle1View={
                    <View>
                        <Text style={styles.titleStyle}>{'法人身份证照片'}</Text>
                        <Text style={styles.contentStyle}>{'拍摄身份证正反面，注意反光，保证身份证的内容清晰'}</Text>
                    </View>
                }
                layout={1}
                leftImg={{
                    url: url1 ?? '',
                    defaultValue: 'app_icon_1',
                    onDelPic: () => {
                        setUrl1('');
                    },
                    onPostSuccess: (url) => {
                        setUrl1(url);
                        idCardOcr(url, 1);
                    },
                }}
                rightImg={{
                    url: url2 ?? '',
                    defaultValue: 'app_icon_2',
                    onDelPic: () => {
                        setUrl2('');
                    },
                    onPostSuccess: (url) => {
                        setUrl2(url);
                        idCardOcr(url, 2);
                    },
                }}
            />
            <UIFormInputView
                label={'法人姓名'}
                value={name}
                showError={false}
                isRequired={false}
                edit={true}
                ref={nameRef}
                onChangeText={() => {
                    queryBeneficiaryInfoV1();
                }}
            />
            <UIFormInputView label={'法人手机号'} showError={false} isRequired={false} edit={true} ref={mobileRef} onChangeText={() => {}} />
            <UIFormInputView label={'法人身份证号'} value={idCardNo} showError={false} isRequired={false} edit={true} ref={idCardRef} onChangeText={() => {}} />
            <UIFormInputView
                label={'法人身份证有效期开始时间'}
                showError={false}
                isRequired={false}
                edit={true}
                isSelect={true}
                ref={startTimeRef}
                value={startTime}
                onSelect={() => {
                    props.callback && props.callback(3);
                }}
            />
            <UIFormInputView
                label={'法人身份证有效期结束时间'}
                showError={false}
                isRequired={false}
                edit={true}
                isSelect={true}
                ref={endTimeRef}
                value={endTime}
                onSelect={() => {
                    props.callback && props.callback(4);
                }}
            />
            <UIFormInputView label={'法人身份证详细地址'} value={address} showError={false} isRequired={false} edit={true} ref={addressRef} onChangeText={() => {}} />
        </View>
    );
}

export interface SupervisionJuridicalViewRef {
    setStartTime: (value: any) => void;
    setEndTime: (value: any) => void;
    getStartTime: () => string;
    getEndTime: () => string;
    getUrl1: () => string;
    getUrl2: () => string;
    getName: () => string;
    getMobile: () => string;
    getIdCardNo: () => string;
    getAddress: () => string;
}

export default React.forwardRef<SupervisionJuridicalViewRef, Props>(SupervisionJuridicalView);
const styles = StyleSheet.create({
    titleStyle: {
        fontSize: 16,
        color: '#333333',
    },
    contentStyle: {
        fontSize: 13,
        color: '#888888',
        marginTop: 5,
    },
});
