import {StyleSheet, Text, View} from 'react-native';
import React from 'react';
import UILabel from '../../../base/widget/UILabel';
import UIButton from '../../../base/widget/UIButton';

interface Props {}

/**
 * 注释:
 * 时间: 2025/3/25 11:19
 * <AUTHOR>
 * @param props
 * @constructor
 */
export default function OrderReturnFastTrackConfirmView(props: Props) {
    function renderTitleView(title: string) {
        return (
            <View style={{backgroundColor: '#FFFFFF', flexDirection: 'row', paddingLeft: 15, paddingRight: 15, marginTop: 15}}>
                <UILabel title={'运单信息'} />
            </View>
        );
    }

    return (
        <View style={{backgroundColor: '#FFFFFF'}}>
            <View style={{flexDirection: 'row', justifyContent: 'space-between', backgroundColor: '#FFFFFF', alignItems: 'center'}}>
                <UILabel title={'运单信息'} style={{paddingLeft: 14, backgroundColor: '#FFFFFF'}} textStyle={{fontSize: 14}} />
                <View style={{backgroundColor: '#FFFFFF', flex: 1}} />
                <UIButton text={'违约申请'} height={30} style={{paddingLeft: 7, marginRight: 15, paddingEnd: 7, backgroundColor: '#FFFFFF'}} textColor={'#5086FC'} />
            </View>
        </View>
    );
}

const styles = StyleSheet.create({
    blueLineStyle: {
        backgroundColor: '#5086FC',
        width: 2,
        height: 30,
        borderRadius: 10,
    },
});
