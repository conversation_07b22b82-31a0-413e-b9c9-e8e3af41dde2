import {StyleSheet, Text, View} from 'react-native';
import React, {createRef} from 'react';
import {NativeStackScreenProps} from '@react-navigation/native-stack';
import {ParamListBase} from '@react-navigation/native';
import BaseCommPage from '../../../base/comm/BasePage';
import UITitleView from '../../../base/widget/UITitleView';
import {CalendarProvider} from '../../../base/widget/calendar';
import UICalendar, {CalendarHandles} from '../../../base/widget/UICalendar';
import {gScreen_width} from '../../../base/Const';
import {ZRouter} from '../../../base/comm/ZRouter';
import {MarkedDates} from '../../../base/widget/calendar/types';
import TextUtils from '../../../base/utils/TextUtils';
import UIButton from '../../../base/widget/UIButton';
import XDate from 'xdate';
import {ReqBeforeModifyPlan, ReqModifyPlan, RspBeforeModifyPlanV2, RspBeforeModifyPlanV3} from '../bean/ReqBeforeModifyPlan';
import BatchOrderDailyPlanView, {BatchOrderDailyPlanViewRef} from '../widget/BatchOrderDailyPlanView';
import {WaitDialogBuilder} from '../../../base/comm/UI';
import {ArrayUtils} from '../../../base/utils/ArrayUtils';

const YYYY_MM_DD = 'yyyy-MM-dd';
const MAX_COUNT = 8;

interface State {
    init: boolean;
    showRightView: boolean; //标题右侧按钮
    selectDate: string; // 当前选中的日期
}

/**
 * 注释: 批量货管理发货计划修改
 * 时间: 2025/5/27 9:53
 * <AUTHOR>
 */
export default class BatchDeliverOrderPlanPage extends BaseCommPage<NativeStackScreenProps<ParamListBase>, State> {
    ref = createRef<CalendarHandles>();
    batchOrderDailyPlanViewRef = createRef<BatchOrderDailyPlanViewRef>();

    //计划信息
    mModifyPlan?: RspBeforeModifyPlanV2;
    //运单ID
    hugeOrderId?: string;
    //是否可以编辑发货计划类型:true 表示原来没有发布计划
    editPlanType: boolean;
    markedDates: MarkedDates = {}; //日历对应发布数据
    initDateStr = new XDate().toString();

    constructor(props) {
        super(props);
        this.state = {
            init: false,
            selectDate: '',
            showRightView: false,
        };

        let pageParams = ZRouter.getParams(props);
        this.hugeOrderId = pageParams.hugeOrderId ?? '';
    }

    componentDidMount(): void {
        this.queryData();
    }

    queryData = () => {
        let waitDilaog = new WaitDialogBuilder().show();
        let req = new ReqBeforeModifyPlan();
        req.hugeOrderId = this.hugeOrderId;
        req.request().then((res) => {
            if (res.isSuccess()) {
                let data = res.data?.data as RspBeforeModifyPlanV2;
                //初始化数据
                let cargoCategory = TextUtils.equals('2', data.planType) ? '车' : TextUtils.equals('1', data.cargoCategory) ? '吨' : '方';
                data.list?.forEach((item) => {
                    this.markedDates[item.planDay ?? new Date().toString()] = {
                        dotText: item.planWeight + cargoCategory,
                        selected: true,
                    };
                });

                if (TextUtils.isEmpty(data?.planType)) {
                    data.planType = '1';
                    if (data.list && data.list.length > 0) {
                        data.planType = data.list?.[0].planType ?? '1';
                    } else if (TextUtils.equals('0', data.freightType)) {
                        data.planType = '2';
                    }
                }
                if (TextUtils.equals('2', data?.planType) && TextUtils.equals('0', data.freightType)) {
                    //包车价 只能是发车模式不能编辑
                    this.editPlanType = false;
                } else {
                    this.editPlanType = ArrayUtils.isEmptyArray(data.list);
                }

                this.mModifyPlan = data;
                this.setState({
                    init: true,
                    selectDate: (data.list?.length ?? 0) > 0 ? data.list?.[0].planDay ?? '' : new Date().toString(),
                });
                console.log('当前计划数据', JSON.stringify(data));
            } else {
                this.showToast(res.getMsg());
            }
            waitDilaog?.dismiss();
        });
    };

    /**
     * 注释：最大天数
     */
    setMaxDay = () => {
        if (TextUtils.isEmpty(this.mModifyPlan?.deliverPlanDayMax)) {
            return new XDate().addDays(89);
        } else {
            let dayNum = Number(this.mModifyPlan?.deliverPlanDayMax) - 1;
            return new XDate().addDays(dayNum);
        }
    };

    /**
     * 注释：设置开始日期
     */
    setStartDay = () => {
        if (TextUtils.isEmpty(this.mModifyPlan?.lastTime)) {
            return new XDate();
        } else {
            let limitHour = Number(this.mModifyPlan?.lastTime);
            let hour = new Date().getHours();
            if (hour > limitHour) {
                return new XDate().addDays(1);
            } else {
                return new XDate();
            }
        }
    };

    /**
     * 注释：保存数据
     */
    onSave = () => {
        let list: RspBeforeModifyPlanV3[] = this.batchOrderDailyPlanViewRef.current?.getPlanList() ?? [];
        list = list
            .filter((item) => TextUtils.isNoEmpty(item.planDay) && TextUtils.isNoEmpty(item.planWeight))
            .map((item) => {
                item.hugeOrderId = this.hugeOrderId;
                return item;
            });

        let waitDilaog = new WaitDialogBuilder().show();
        let req = new ReqModifyPlan();
        req.list = list;
        req.request().then((res) => {
            waitDilaog?.dismiss();
            if (res.isSuccess()) {
                this.showToast(res.getMsg());
                ZRouter.goBack();
            } else {
                this.showDialog(res.getMsg());
            }
        });
    };

    onUICalendarClickEvent = (date, data) => {
        let list: RspBeforeModifyPlanV3[] = this.batchOrderDailyPlanViewRef.current?.getPlanList() ?? [];
        list = list.filter((item) => TextUtils.isNoEmpty(item.planDay) && TextUtils.isNoEmpty(item.planWeight));

        if (list.length >= MAX_COUNT) {
            this.showDialog(`发货计划最多添加${MAX_COUNT}条`);
            return;
        }
        this.ref.current?.toggleCollapseView();
        this.setState({
            showRightView: true,
            selectDate: date,
        });
    };

    onPressBackCalendar = () => {
        if (this.state.showRightView) {
            this.ref.current?.toggleExpandView();
            this.setState({
                showRightView: false,
            });
        }
    };

    render() {
        return (
            <View style={{flex: 1, backgroundColor: '#EFF0F3'}}>
                <UITitleView title={'维护发货计划'} rightText={this.state.showRightView ? '返回日历' : ''} rightColor="#5086FC" clickRight={this.onPressBackCalendar} />
                <Text
                    style={{
                        color: '#FF883F',
                        marginTop: 10,
                        fontSize: 13,
                        backgroundColor: '#FFF7E8',
                        padding: 10,
                        marginBottom: 10,
                    }}>
                    请先选择一个有发货计划的日期，然后继续填写/维护
                </Text>

                <CalendarProvider date={this.initDateStr}>
                    {this.state.init && (
                        <View>
                            <UICalendar
                                data={this.markedDates}
                                showToggleButton={false}
                                disabledDateBefore={this.setStartDay()}
                                disabledDateAfter={this.setMaxDay()}
                                ref={this.ref}
                                clickEvent={(date, data) => {
                                    this.onUICalendarClickEvent(date, data);
                                }}
                            />
                            <BatchOrderDailyPlanView
                                ref={this.batchOrderDailyPlanViewRef}
                                editPlanType={this.editPlanType}
                                visable={this.state.showRightView}
                                modifyPlan={this.mModifyPlan}
                                selectDate={this.state.selectDate}
                                onChangePlanType={(planType: string) => {
                                    //切换发货计划形式，清空数据
                                    this.ref.current?.clearAllData();
                                }}
                                onChangePlan={(list: RspBeforeModifyPlanV3[], unitTxt: string) => {
                                    //发布计划数据变动
                                    list.map((plan) => {
                                        switch (plan.planType) {
                                            case '1':
                                                this.ref.current?.setDotText(plan.planDay ?? '', TextUtils.isEmpty(plan.planWeight) ? '' : plan.planWeight + unitTxt);
                                                break;
                                            case '2':
                                                let weight = Number(plan.unitWeight ?? 0) * Number(plan.planWeight ?? 0);
                                                this.ref.current?.setDotText(plan.planDay ?? '', weight <= 0 ? '' : weight + unitTxt);
                                                break;
                                        }
                                    });
                                }}
                                onDeletePlan={(plan: RspBeforeModifyPlanV3) => {
                                    //删除计划
                                    this.ref.current?.setDotText(plan.planDay ?? '', '');
                                    //张开日历
                                    this.ref.current?.toggleExpandView();

                                    this.setState({showRightView: false});
                                }}
                            />
                        </View>
                    )}
                </CalendarProvider>

                <View style={styles.bt_bg}>
                    <UIButton text={'取消'} style={{width: '45%', backgroundColor: 'white'}} fontSize={16} textColor={'#2374E9'} borderColor={'#2374E9'} onPress={ZRouter.goBack} />
                    <UIButton text={'确定'} textColor={'#fff'} style={{width: '45%', backgroundColor: '#5086FC'}} borderColor={'#fff'} fontSize={16} onPress={this.onSave} />
                </View>
            </View>
        );
    }
}

const styles = StyleSheet.create({
    bt_bg: {
        flexDirection: 'row',
        justifyContent: 'space-between',
        alignItems: 'center',
        paddingLeft: 19,
        paddingRight: 19,
        height: 64,
        backgroundColor: '#fff',
    },
    rightViewStyle: {
        color: '#5086FC',
        marginRight: 13,
    },
    planViewStyle: {
        backgroundColor: '#FFFFFF',
        marginTop: 10,
        paddingTop: 10,
        paddingBottom: 10,
        paddingLeft: 15,
        paddingRight: 15,
        borderRadius: 5,
    },
    planViewStyle1: {
        color: '#333333',
        fontWeight: 'bold',
        fontSize: 16,
    },
    planViewStyle2: {
        color: '#333333',
        fontWeight: 'bold',
        fontSize: 16,
    },
    planViewStyle3: {
        color: '#A6A6A6',
        fontSize: 13,
    },
    planViewStyle4: {
        color: '#FF883F',
        fontSize: 13,
    },
    planViewStyle5: {
        color: '#333333',
        fontSize: 15,
        marginTop: 13,
    },
    planViewStyle6: {
        color: '#333333',
        fontWeight: 'bold',
        fontSize: 16,
        flex: 1,
    },
    line: {
        height: 1,
        width: gScreen_width,
        backgroundColor: '#eeeeee',
        marginTop: 15,
        marginBottom: 15,
    },
    item: {
        flexDirection: 'row',
        alignItems: 'center',
        justifyContent: 'space-between',
    },
    leftTxt: {
        fontSize: 15,
        color: '#1F2937',
    },
    redTxt: {
        fontSize: 15,
        color: '#F81212',
    },
    txt1: {
        fontSize: 13,
        color: '#C2C2C2',
        alignSelf: 'flex-end',
    },
    txt2: {
        fontSize: 15,
        color: '#333333',
    },
    textSelectStyle: {
        fontSize: 14,
        color: '#478DFB',
        backgroundColor: '#FFFFFF',
        borderColor: '#5086FC',
        borderWidth: 1,
        borderRadius: 4,
        paddingTop: 8,
        paddingBottom: 8,
        marginTop: 10,
        flex: 1,
        textAlign: 'center',
    },
    textUnSelectStyle: {
        fontSize: 14,
        color: '#666666',
        backgroundColor: '#FFFFFF',
        borderColor: '#979797',
        borderWidth: 1,
        borderRadius: 4,
        paddingTop: 8,
        paddingBottom: 8,
        marginTop: 10,
        flex: 1,
        textAlign: 'center',
    },
});
