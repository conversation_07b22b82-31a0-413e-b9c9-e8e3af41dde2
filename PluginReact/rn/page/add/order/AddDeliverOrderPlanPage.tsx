import {StyleSheet, Text, TouchableOpacity, View} from 'react-native';
import React, {createRef} from 'react';
import {NativeStackScreenProps} from '@react-navigation/native-stack';
import {ParamListBase} from '@react-navigation/native';
import BaseCommPage from '../../../base/comm/BasePage';
import UITitleView from '../../../base/widget/UITitleView';
import {CalendarProvider} from '../../../base/widget/calendar';
import UICalendar, {CalendarHandles} from '../../../base/widget/UICalendar';
import {gScreen_width} from '../../../base/Const';
import {ZRouter} from '../../../base/comm/ZRouter';
import {MarkedDates} from '../../../base/widget/calendar/types';
import TextUtils from '../../../base/utils/TextUtils';
import UIButton from '../../../base/widget/UIButton';
import {setResultAndFinish} from '../../../base/comm/NativeUtils';
import AddDeliverOrderDailyPlanView, {AddDeliverOrderDailyPlanViewRef} from '../widget/AddDeliverOrderDailyPlanView';
import XDate from 'xdate';
import {RspBeforeModifyPlanV2, RspBeforeModifyPlanV3} from '../bean/ReqBeforeModifyPlan';

const YYYY_MM_DD = 'yyyy-MM-dd';
const MAX_COUNT = 8;

interface State {
    showRightView: boolean; //标题右侧按钮
    selectDate: string; // 当前选中的日期
    inputWeight?: string; // 当前输入的吨位
}

/**
 * 注释: 批量货发布-发布计划
 * 时间: 2025/5/27 9:53
 * <AUTHOR>
 */
export default class AddDeliverOrderPlanPage extends BaseCommPage<NativeStackScreenProps<ParamListBase>, State> {
    ref = createRef<CalendarHandles>();
    pageParams: any;
    limitWeigt: string; // 每日上限吨位
    limitVolume: string; // 每日上限方位
    deliverPlanDayMax: string; // 可选最大天数
    deliverPlanLatestChangeTimeOfEachDay: string; // 发货计划每日最晚修改时间 默认16
    freightType: string; // 运费计算方式 0 包车价，1 单价
    despatchStart?: string; // 装货开始时间
    validityTime?: string; // 信息有效期
    markedDates: MarkedDates = {}; //日历对应发布数据
    addDeliverOrderDailyPlanRef = createRef<AddDeliverOrderDailyPlanViewRef>();
    //计划信息
    mModifyPlan: RspBeforeModifyPlanV2;

    constructor(props) {
        super(props);
        this.pageParams = ZRouter.getParams(props);
        this.validityTime = this.pageParams.validityTime;
        this.despatchStart = this.pageParams.despatchStart;
        this.limitWeigt = this.pageParams.limitWeigt ?? '';
        this.deliverPlanLatestChangeTimeOfEachDay = this.pageParams.deliverPlanLatestChangeTimeOfEachDay ?? '';
        this.limitVolume = this.pageParams.limitVolume ?? '';
        this.deliverPlanDayMax = this.pageParams.deliverPlanDayMax ?? '';
        this.mModifyPlan = new RspBeforeModifyPlanV2();
        this.mModifyPlan.weight = this.pageParams.weight;
        this.mModifyPlan.freightType = this.pageParams.freightType;
        this.mModifyPlan.deliverPlanDayMax = this.pageParams.deliverPlanDayMax;
        this.mModifyPlan.cargoCategory = this.pageParams.cargoCategory;
        this.mModifyPlan.vehicleWeight = this.pageParams.vehicleWeight;
        this.mModifyPlan.list = this.pageParams.hugeOrderPlan;
        //初始化数据
        let cargoCategory = TextUtils.equals('2', this.mModifyPlan?.planType) ? '车' : TextUtils.equals('1', this.mModifyPlan.cargoCategory) ? '吨' : '方';
        this.mModifyPlan.list?.map((item) => {
            let weight: string = '';
            switch (item.planType) {
                case '1':
                    weight = item.planWeight ?? '';
                    weight = weight + cargoCategory;
                    break;
                case '2':
                    weight = (Number(item.unitWeight ?? 0) * Number(item.planWeight ?? 0)).toString();
                    weight = weight + cargoCategory;
                    break;
            }
            this.markedDates[item.planDay ?? new Date().toString()] = {
                dotText: weight,
                selected: true,
            };
            item.isModifyFlag = '1';
        });
        if (TextUtils.isEmpty(this.mModifyPlan?.planType)) {
            this.mModifyPlan.planType = '1';
            if (this.mModifyPlan.list && this.mModifyPlan.list.length > 0) {
                this.mModifyPlan.planType = this.mModifyPlan.list?.[0].planType ?? '1';
            } else if (TextUtils.equals('0', this.mModifyPlan.freightType)) {
                this.mModifyPlan.planType = '2';
            }
        }
        this.state = {
            showRightView: false,
            selectDate: '',
        };
    }

    /**
     * 注释：最大天数
     * 时间：2025/5/30 10:36
     * @author：宋双朋
     */
    private setMaxDay() {
        if (TextUtils.isEmpty(this.deliverPlanDayMax)) {
            return new XDate().addDays(89);
        } else {
            let dayNum = Number(this.deliverPlanDayMax) - 1;
            return new XDate().addDays(dayNum);
        }
    }

    /**
     * 注释：设置开始日期
     * 时间：2025/5/30 10:36
     * @author：宋双朋
     */
    private setStartDay() {
        if (TextUtils.isEmpty(this.deliverPlanLatestChangeTimeOfEachDay)) {
            return new XDate();
        } else {
            let limitHour = Number(this.deliverPlanLatestChangeTimeOfEachDay);
            let hour = new Date().getHours();
            if (hour > limitHour) {
                return new XDate().addDays(1);
            } else {
                return new XDate();
            }
        }
    }

    private getFullYears(date: Date): string {
        return new XDate(date).toString('yyyy-MM-dd');
    }

    /**
     * 注释：发货计划日不能早于装货时间
     * 时间：2025/5/30 14:08
     * @author：宋双朋
     * @param d1 装货时间
     * @param d2 发货计划日
     * @returns {boolean}
     */
    private compareDateV1(d1, d2): boolean {
        if (TextUtils.isEmpty(d1)) {
            return false;
        }
        let date1 = this.getFullYears(d1);
        let date2 = this.getFullYears(d2);
        return date2 < date1;
    }

    /**
     * 注释：发货计划日不能晚于有效期
     * 时间：2025/5/30 14:08
     * @author：宋双朋
     * @param d1 有效期
     * @param d2 发货计划日
     * @returns {boolean}
     */
    private compareDateV2(d1, d2): boolean {
        if (TextUtils.isEmpty(d1)) {
            return false;
        }
        let date1 = this.getFullYears(d1);
        let date2 = this.getFullYears(d2);
        return date1 < date2;
    }

    /**
     * 注释：保存数据
     * 时间：2025/5/30 10:36
     * @author：宋双朋
     */
    private onSave() {
        let list: RspBeforeModifyPlanV3[] = this.addDeliverOrderDailyPlanRef.current?.getPlanList() ?? [];
        list = list.filter((item) => TextUtils.isNoEmpty(item.planDay));
        let hasError = list.some((item) => {
            if (TextUtils.equals('1', item.planType)) {
                if (TextUtils.isEmpty(item.planWeight)) {
                    this.showDialog(`每日计划发货吨位不能为空！`);
                    return true;
                }
            } else if (TextUtils.equals('2', item.planType)) {
                if (TextUtils.isEmpty(item.planWeight)) {
                    this.showDialog(`每天计划发车数不能为空！`);
                    return true;
                }
                if (TextUtils.isEmpty(item.unitWeight)) {
                    this.showDialog(`单车承运吨位不能为空！`);
                    return true;
                }
            }
            if (this.compareDateV1(this.despatchStart, item.planDay)) {
                this.showDialog(`发货计划日不能早于装货时间:${new XDate(this.despatchStart).toString(YYYY_MM_DD)}`);
                return true;
            }
            if (this.compareDateV2(this.validityTime, item.planDay)) {
                this.showDialog(`发货计划日不能晚于有效期:${new XDate(this.validityTime).toString(YYYY_MM_DD)}`);
                return true;
            }
            return false;
        });
        if (hasError) {
            return;
        }
        setResultAndFinish({data: JSON.stringify(list)});
    }

    /**
     * 注释：设置标题右侧view
     * 时间：2025/5/27 11:23
     * @author：宋双朋
     */
    private renderRightView() {
        return this.state.showRightView ? (
            <TouchableOpacity
                onPress={() => {
                    this.ref.current?.toggleExpandView();
                    this.setState({
                        showRightView: false,
                    });
                }}>
                <Text style={styles.rightViewStyle}>返回日历</Text>
            </TouchableOpacity>
        ) : (
            <></>
        );
    }

    /**
     * 注释：底部按钮
     * 时间：2025/5/27 14:18
     * @author：宋双朋
     */
    private renderButtonView() {
        return (
            <View
                style={{
                    flexDirection: 'row',
                    justifyContent: 'space-between',
                    alignItems: 'center',
                    paddingLeft: 19,
                    paddingRight: 19,
                    height: 64,
                    backgroundColor: '#fff',
                }}>
                <UIButton
                    text={'取消'}
                    style={{width: '45%', backgroundColor: 'white'}}
                    fontSize={16}
                    textColor={'#2374E9'}
                    borderColor={'#2374E9'}
                    onPress={() => {
                        ZRouter.goBack();
                    }}
                />
                <UIButton
                    text={'确定'}
                    textColor={'#fff'}
                    style={{width: '45%', backgroundColor: '#5086FC'}}
                    borderColor={'#fff'}
                    fontSize={16}
                    onPress={() => {
                        this.onSave();
                    }}
                />
            </View>
        );
    }

    onUICalendarClickEvent = (date, data) => {
        let list: RspBeforeModifyPlanV3[] = this.addDeliverOrderDailyPlanRef.current?.getPlanList() ?? [];
        list = list.filter((item) => TextUtils.isNoEmpty(item.planDay) && TextUtils.isNoEmpty(item.planWeight));
        if (list.length >= MAX_COUNT) {
            this.showDialog(`发货计划最多添加${MAX_COUNT}条`);
            return;
        }
        this.ref.current?.toggleCollapseView();
        this.setState({
            showRightView: true,
            selectDate: date,
        });
    };

    render() {
        return (
            <View style={{flex: 1, backgroundColor: '#EFF0F3'}}>
                <UITitleView title={'维护发货计划'} rightView={this.renderRightView()} />
                <CalendarProvider date={new Date().toString()}>
                    <View style={{flex: 1, flexDirection: 'column'}}>
                        <View>
                            <UICalendar
                                data={this.markedDates}
                                showToggleButton={false}
                                disabledDateBefore={this.setStartDay()}
                                disabledDateAfter={this.setMaxDay()}
                                ref={this.ref}
                                clickEvent={(date, data) => {
                                    this.onUICalendarClickEvent(date, data);
                                }}
                            />
                        </View>
                        <AddDeliverOrderDailyPlanView
                            ref={this.addDeliverOrderDailyPlanRef}
                            visible={this.state.showRightView}
                            modifyPlan={this.mModifyPlan}
                            selectDate={this.state.selectDate}
                            onChangePlanType={(planType: string) => {
                                //切换发货计划形式，清空数据
                                this.ref.current?.clearAllData();
                            }}
                            onChangePlan={(list: RspBeforeModifyPlanV3[], unitTxt: string) => {
                                //发布计划数据变动
                                list.map((plan) => {
                                    switch (plan.planType) {
                                        case '1':
                                            this.ref.current?.setDotText(plan.planDay ?? '', TextUtils.isEmpty(plan.planWeight) ? '' : plan.planWeight + unitTxt);
                                            break;
                                        case '2':
                                            let weight = Number(plan.unitWeight ?? 0) * Number(plan.planWeight ?? 0);
                                            this.ref.current?.setDotText(plan.planDay ?? '', weight <= 0 ? '' : weight + unitTxt);
                                            break;
                                    }
                                });
                            }}
                            onDeletePlan={(plan: RspBeforeModifyPlanV3) => {
                                //删除计划
                                this.ref.current?.setDotText(plan.planDay ?? '', '');
                                //张开日历
                                this.ref.current?.toggleExpandView();

                                this.setState({showRightView: false});
                            }}
                        />
                    </View>
                </CalendarProvider>
                {this.renderButtonView()}
            </View>
        );
    }
}

const styles = StyleSheet.create({
    rightViewStyle: {
        color: '#5086FC',
        marginRight: 13,
    },
    planViewStyle: {
        backgroundColor: '#FFFFFF',
        marginTop: 10,
        paddingTop: 10,
        paddingBottom: 10,
        paddingLeft: 15,
        paddingRight: 15,
        borderRadius: 5,
    },
    planViewStyle1: {
        color: '#333333',
        fontWeight: 'bold',
        fontSize: 16,
    },
    planViewStyle2: {
        color: '#333333',
        fontWeight: 'bold',
        fontSize: 16,
    },
    planViewStyle3: {
        color: '#A6A6A6',
        fontSize: 13,
    },
    planViewStyle4: {
        color: '#FF883F',
        fontSize: 13,
    },
    planViewStyle5: {
        color: '#333333',
        fontSize: 15,
        marginTop: 13,
    },
    planViewStyle6: {
        color: '#333333',
        fontWeight: 'bold',
        fontSize: 16,
        flex: 1,
    },
    line: {
        height: 1,
        width: gScreen_width,
        backgroundColor: '#eeeeee',
        marginTop: 15,
        marginBottom: 15,
    },
    item: {
        flexDirection: 'row',
        alignItems: 'center',
        justifyContent: 'space-between',
    },
    leftTxt: {
        fontSize: 15,
        color: '#1F2937',
    },
    redTxt: {
        fontSize: 15,
        color: '#F81212',
    },
    txt1: {
        fontSize: 13,
        color: '#C2C2C2',
        alignSelf: 'flex-end',
    },
    txt2: {
        fontSize: 15,
        color: '#333333',
    },
});
