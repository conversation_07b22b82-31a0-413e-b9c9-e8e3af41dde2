import {BaseResponse} from '../../../base/http/BaseResponse';
import {BaseRequest} from '../../../base/http/BaseRequest';
import {ResultData} from '../../../base/http/ResultData';

/**
 *  tms指定承运方支持联系司机
 *  http://wiki.zczy56.com/pages/viewpage.action?pageId=89522228
 *  对接人:王 欣睿
 *
 */
export class ReqQueryVirtualNumber extends BaseRequest {
    public carrierName?: string; //	承运方姓名	String	是
    public carrierMobile?: string; //	承运方机号	String	是
    public carrierUserType?: string; //	承运方用户类型	String	是
    public carrierUserId?: string; //	承运方用户id	String	是

    async request(): Promise<BaseResponse<RspVirtualNumber>> {
        this.params = {
            carrierName: this.carrierName,
            carrierMobile: this.carrierMobile,
            carrierUserType: this.carrierUserType,
            carrierUserId: this.carrierUserId,
        };
        return super.post('oms-app/virtualNumber/queryVirtualNumber', RspVirtualNumber);
    }
}

export class RspVirtualNumber extends ResultData {
    public inventedMobile?: string; //虚拟号码
}
