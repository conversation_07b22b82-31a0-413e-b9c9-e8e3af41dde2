/**
 * 注释: 货主id 查询平台司机
 * http://wiki.zczy56.com/pages/viewpage.action?pageId=78545607
 * 时间: 2025/6/27 10:40
 * <AUTHOR>
 */
import {BaseRequest} from '../../../base/http/BaseRequest';
import {BaseResponse} from '../../../base/http/BaseResponse';
import {ResultData} from '../../../base/http/ResultData';

export class ReqQueryPlateCapacityListDto extends BaseRequest {
    consignorUserId?: string; //货主用户ID（后台从登录信息获取）
    condition?: string; //根据手机号码查询
    public async request(): Promise<BaseResponse<CarrierDto>> {
        this.params = {
            consignorUserId: this.consignorUserId,
            condition: this.condition,
        };
        return super.post('oms-app/order/addAgileOrder//queryPlateCapacityList', CarrierDto);
    }
}

export class CarrierDto extends ResultData {
    appointedCarrierResps: AppointedCarrierResp[];
}

export class AppointedCarrierResp {
    userId?: string; //用户id
    mobile?: string; //手机
    userName?: string; //用户名
    userType?: string; //指定承运方用户类型 2 高级承运人,3 承运商,10 车老板
    ifExist?: string; //指定承运方是否关联货主 0否 1：是
    inBlacklist?: string; //在黑名单 0:否 1:是
    abnormalType?: string; //是否账号异常（冻结、锁定） 0-否 1-是
}

export class AppointedCarrierRespV1 {
    appointCarrierUserIdListStr?: string; // 指定承运人id
    appointCarrierMobileListStr?: string; // 指定承运人手机号
    appointCarrierNameListStr?: string; // 指定承运人姓名
    appointCarrierTypeListStr?: string; // 指定承运人类型 2 高级承运人,3 承运商,10 车老板'
    appointCarrierIfExistListStr?: string; // 指定承运方是否关联货主 0 否 1:是
}
