import {BaseRequest} from '../../../base/http/BaseRequest';
import {BaseResponse} from '../../../base/http/BaseResponse';
import {CarrierDto} from './ReqQueryPlateCapacityListDto';

/**
 * 注释: 货主id 历史推荐运力列表
 * http://wiki.zczy56.com/pages/viewpage.action?pageId=78545607
 * 时间: 2025/6/27 10:40
 * <AUTHOR>
 */
export class ReqQueryTmsCapacityGroupList extends BaseRequest {
    consignorUserId?: string; //货主用户ID（后台从登录信息获取）
    public async request(): Promise<BaseResponse<CarrierDto>> {
        this.params = {
            consignorUserId: this.consignorUserId,
        };
        return super.post('oms-app/order/addAgileOrder/queryTmsCapacityGroupList', CarrierDto);
    }
}
