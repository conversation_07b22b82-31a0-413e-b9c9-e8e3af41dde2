import React, {useEffect, useImperativeHandle, useRef, useState} from 'react';
import {KeyboardAvoidingView, StyleSheet, Text, TextInput, ScrollView, View} from 'react-native';
import UITouchableOpacity from '../../../base/widget/UITouchableOpacity';
import {gScreen_width} from '../../../base/Const';
import TextUtils from '../../../base/utils/TextUtils';
import UIImage from '../../../base/widget/UIImage';
import UICustomProgressBar from '../../../base/widget/UICustomProgressBar';
import {RspBeforeModifyPlanV2, RspBeforeModifyPlanV3} from '../bean/ReqBeforeModifyPlan';
import {ArrayUtils} from '../../../base/utils/ArrayUtils';

interface Props {
    visable: boolean;
    selectDate: string; // 当前选中的日期
    editPlanType: boolean; //是否可以编辑发货计划类型:true 表示原来没有发布计划
    modifyPlan?: RspBeforeModifyPlanV2; //计划信息
    onChangePlanType: (planType: string) => void; //发布计划切换回调
    onChangePlan: (plan: RspBeforeModifyPlanV3[], unitTxt: string) => void;
    onDeletePlan: (plan: RspBeforeModifyPlanV3) => void;
}

/**
 *  desc: 每日计划发车数
 *  user: 孙飞虎
 *  time: 2025/5/29 19:35
 */
function BatchOrderDailyPlanView(props: Props, ref: React.Ref<BatchOrderDailyPlanViewRef>) {
    //单位
    const cargoCategoryTxt = TextUtils.equals(1, props.modifyPlan?.cargoCategory) ? '吨' : '方';
    //整个视图是否可以见
    const [visable, setVisable] = useState(props.visable);
    //发布计划模式 //1每日计划发货总吨位 2 每日计划发车数及单车承运吨位
    const planType = useRef(props.modifyPlan?.planType ?? '1');
    //当前勋章日期发货计划对象
    const [plan, setPlan] = useState<RspBeforeModifyPlanV3>();
    //全部发布计划集合
    const list = useRef<RspBeforeModifyPlanV3[]>(props.modifyPlan?.list ?? []);
    //限制提示View
    const [limitDataView, setlimitDataView] = useState<React.JSX.Element>();
    // 进度条View
    const [progressBarTxt, setProgressBarTxt] = useState<React.JSX.Element>();

    //监听日期切换
    React.useEffect(() => {
        //初始化 or 日期切换
        let item = list.current?.find((item) => item.planDay === props.selectDate);
        if (item) {
            setPlan(item);
        } else {
            //新添加一个日期发货计划
            item = {planType: planType.current, planDay: props.selectDate, isModifyFlag: '0', newData: true};
            //包车价 并且不能编辑,使用指定吨位值
            if (props.editPlanType == false && TextUtils.equals('0', props.modifyPlan?.freightType)) {
                item.unitWeight = props.modifyPlan?.vehicleWeight;

            }else if (TextUtils.isEmpty(item.unitWeight) && TextUtils.equals('1', props.modifyPlan?.freightType) && list.current?.length > 0) {
                // 单车承运值统一 ；单价 每日计划发车数 并且集合大于0
                item.unitWeight = list.current?.[0].unitWeight;
            }
            setPlan(item);
            list.current.push(item);
        }
        if (TextUtils.equals('1', item.isModifyFlag)) {
            //不可编辑时不显示警告信息
            setlimitDataView(<></>);
        }
    }, [props.selectDate]);

    React.useEffect(() => {
        setVisable(props.visable);
    }, [props.visable]);

    //监听当前日期发货计划变化
    React.useEffect(() => {
        //比例
        if (plan) {
            list.current = list.current.filter((item) => !TextUtils.equals(item.planDay, plan.planDay)) ?? [];
            list.current.push(plan);

            if (props.editPlanType || plan.newData) {
                //计划总吨位 / 批量货总量
                let weight = 0.0;
                list.current.map((item) => {
                    item.unitWeight = plan.unitWeight;
                    if (TextUtils.isNoEmpty(item.planWeight)) {
                        weight += TextUtils.equals('1', item.planType) ? Number(item.planWeight ?? '0') : Number(item.planWeight ?? '0') * Number(item.unitWeight ?? '0');
                    }
                });
                let weightPercent = weight / parseFloat(props?.modifyPlan?.weight ?? '1');

                setProgressBarTxt(
                    <View style={{flexDirection: 'row', alignItems: 'center', marginTop: 6}}>
                        <UICustomProgressBar progress={weightPercent} />
                        <Text style={styles.planViewStyle3}>
                            {` 计划总${cargoCategoryTxt}位`}
                            <Text style={styles.planViewStyle4}>{weight}</Text>
                            {`${cargoCategoryTxt}/批量货总量`}
                            <Text style={styles.planViewStyle4}>{props?.modifyPlan?.weight ?? ''}</Text>
                            {`${cargoCategoryTxt}`}
                        </Text>
                    </View>,
                );
            } else {
                // 当前日期成交吨位
                let dealWeight = TextUtils.equals('1', plan.planType) ? Number(plan?.dealWeight ?? '0') : Number(plan?.dealWeight ?? '0') * Number(plan?.unitWeight ?? '0');
                //当前日期计划吨位
                let planWeight = TextUtils.equals('1', plan.planType) ? Number(plan?.planWeight ?? '0') : Number(plan?.planWeight ?? '0') * Number(plan?.unitWeight ?? '0');

                // 当前日期成交吨位 / 当前日期计划吨位
                let weightPercent = dealWeight / (planWeight > 0 ? planWeight : 1);

                if (dealWeight <= 0) {
                    //暂无成交
                    setProgressBarTxt(
                        <View style={{flexDirection: 'row', alignItems: 'center', marginTop: 6}}>
                            <UICustomProgressBar progress={weightPercent} />
                            <Text style={styles.planViewStyle3}>{` 暂无成交/该计划总量${planWeight}${cargoCategoryTxt}`}</Text>
                        </View>,
                    );
                } else {
                    setProgressBarTxt(
                        <View style={{flexDirection: 'row', alignItems: 'center', marginTop: 6}}>
                            <UICustomProgressBar progress={weightPercent} />
                            <Text style={styles.planViewStyle3}>
                                {' 已成交'}
                                <Text style={styles.planViewStyle4}>{dealWeight}</Text>
                                {`${cargoCategoryTxt}/该计划总量${planWeight}${cargoCategoryTxt}`}
                            </Text>
                        </View>,
                    );
                }
            }
            //超限提示
            if (TextUtils.isNoEmpty(props.modifyPlan?.limitWeigt) && !TextUtils.equals('1', plan.isModifyFlag)) {
                let limitWeigt = Number(props.modifyPlan?.limitWeigt);
                if (TextUtils.equals('1', plan.planType)) {
                    //吨位
                    if (Number(plan.planWeight) > limitWeigt) {
                        setlimitDataView(<Text style={[styles.hint, styles.redTxt]}>{`已超出每日承运最大量${props.modifyPlan?.limitWeigt}${cargoCategoryTxt}`}</Text>);
                    } else {
                        setlimitDataView(<Text style={styles.hint}>{`每日承运不得超过${props.modifyPlan?.limitWeigt}${cargoCategoryTxt}`}</Text>);
                    }
                } else {
                    if (Number(plan.unitWeight) > limitWeigt) {
                        setlimitDataView(<Text style={[styles.hint, styles.redTxt]}>{`已超出每车承运最大量${props.modifyPlan?.limitWeigt}${cargoCategoryTxt}`}</Text>);
                    } else {
                        setlimitDataView(<Text style={styles.hint}>{`注:每车承运不得超过${props.modifyPlan?.limitWeigt}${cargoCategoryTxt}`}</Text>);
                    }
                }
            }
            
            //当前日期
            props.onChangePlan(list.current, cargoCategoryTxt);
        }
    }, [plan]);

    useImperativeHandle(ref, () => ({
        getPlanList: () => {
            return list.current;
        },
    }));
    /**
     * 注释：发货计划形式
     */
    const renderPlanTypeView = () => {
        return (
            <>
                {props.editPlanType ? (
                    <>
                        <Text style={styles.planViewStyle5}>
                            {`发货计划形式`}
                            <Text style={styles.redTxt}>{'*'}</Text>
                        </Text>
                        <View style={{flexDirection: 'row'}}>
                            <Text
                                style={TextUtils.equals('1', plan?.planType) ? styles.textSelectStyle : styles.textUnSelectStyle}
                                onPress={() => {
                                    //切换发货计划形式 清空之前选择
                                    planType.current = '1';
                                    list.current = [];

                                    let p = {planDay: props.selectDate, planType: '1', isModifyFlag: '0', newData: true};
                                    setPlan(p);
                                    props.onChangePlanType('1');
                                }}>
                                {'每日计划发货总吨位'}
                            </Text>
                            <View style={{width: 10}} />
                            <Text
                                style={TextUtils.equals('2', plan?.planType) ? styles.textSelectStyle : styles.textUnSelectStyle}
                                onPress={() => {
                                    //切换发货计划形式 清空之前选择
                                    planType.current = '2';
                                    list.current = [];
                                    let p = {planDay: props.selectDate, planType: '2', isModifyFlag: '0', newData: true};
                                    setPlan(p);
                                    props.onChangePlanType('2');
                                }}>
                                {'每日计划发车数'}
                            </Text>
                        </View>
                    </>
                ) : (
                    <Text style={styles.planViewStyle5}>{TextUtils.equals('1', plan?.planType) ? '发货计划形式：每日计划发货总吨位' : '发货计划形式：每日计划发车数及单车承运吨位'}</Text>
                )}
            </>
        );
    };

    /**
     * 注释：输入监听
     */
    const inputChange = (value) => {
        let p = {...plan};
        p.planWeight = value;
        setPlan(p);
    };
    /**
     * 注释：吨/方位输入
     */
    const renderInputView = () => {
        return (
            <View style={[styles.item, {backgroundColor: '#FFFFFF', marginTop: 10}]}>
                <Text style={styles.leftTxt}>
                    {`每日计划发货${cargoCategoryTxt}`} <Text style={styles.redTxt}>{'*'}</Text>
                </Text>
                <TextInput
                    value={plan?.planWeight}
                    multiline={false}
                    inputMode={'numeric'}
                    placeholder={'请输入'}
                    style={{
                        fontSize: 15,
                        color: '#333',
                        textAlign: 'right',
                        paddingTop: 0,
                        paddingBottom: 0,
                        flex: 1,
                    }}
                    onChangeText={inputChange}
                />
                <Text style={styles.txt2}>{`${cargoCategoryTxt}`}</Text>
            </View>
        );
    };

    /**
     * 注释：每天计划发车数
     * 时间：2025/5/31 8:54
     * @author：宋双朋
     * @param text
     */
    const onChangeDailyCount = (text) => {
        // 允许空
        let p = {...plan};
        if (text === '') {
            p.planWeight = '';
            setPlan(p);
            return;
        }

        let value = text.replace(/[^0-9]/g, '');
        if (value === '') {
            p.planWeight = '';
            setPlan(p);
            return;
        }
        value = value.replace(/^0+/, '');
        if (value === '') value = '1';
        p.planWeight = value;
        setPlan(p);
    };

    const increment = () => {
        //加
        const num = parseInt(plan?.planWeight ?? '') || 0;
        let p = {...plan};
        p.planWeight = String(num + 1);
        setPlan(p);
    };

    const decrement = () => {
        //减
        const num = parseInt(plan?.planWeight ?? '') || 0;
        if (num > 1) {
            let p = {...plan};
            p.planWeight = String(num - 1);
            setPlan(p);
        }
    };

    //单车承运吨位
    const onChangeCapacity = (text) => {
        // 允许空
        let p = {...plan};

        if (text === '') {
            p.unitWeight = text;
            setPlan(p);
            return;
        }
        let filtered = text.replace(/[^0-9.]/g, '');
        const parts = filtered.split('.');
        if (parts.length > 2) {
            filtered = parts[0] + '.' + parts[1];
        }
        if (parts.length === 2) {
            parts[1] = parts[1].slice(0, 4);
            filtered = parts[0] + '.' + parts[1];
        }
        if (filtered.startsWith('00')) {
            filtered = filtered.replace(/^0+/, '0');
        }
        if (filtered === '.') {
            filtered = '0.';
        }
        if (filtered === '') {
            p.unitWeight = text;
            setPlan(p);
        } else {
            p.unitWeight = filtered;
            setPlan(p);
        }
    };
    //每天计划发车视图(不可编辑)
    const renderCarNoInputView = () => {
        return (
            <View style={styles.container}>
                <Text style={styles.label}>{`每天计划发车数：${plan?.planWeight}车`}</Text>
                <View style={styles.line} />
                <Text style={[styles.label, {marginTop: 10}]}>{`单车承运${cargoCategoryTxt}位：${plan?.unitWeight}${cargoCategoryTxt}`}</Text>
            </View>
        );
    };

    //每天计划发车视图(可编辑)
    const renderCarInputView = () => {
        let editable = props.editPlanType && TextUtils.equals('1', props.modifyPlan?.freightType);
        return (
            <View style={styles.container}>
                <View style={styles.row}>
                    <Text style={styles.label}>
                        每天计划发车数
                        <Text style={styles.required}>*</Text>
                    </Text>
                    <View style={styles.counterContainer}>
                        <UITouchableOpacity style={styles.button} onPress={decrement}>
                            <Text style={styles.buttonText}>-</Text>
                        </UITouchableOpacity>
                        <TextInput style={styles.input} keyboardType="numeric" value={plan?.planWeight} onChangeText={onChangeDailyCount} textAlign="center" placeholder="请输入" placeholderTextColor={'#ccc'} />
                        <UITouchableOpacity style={styles.button} onPress={increment}>
                            <Text style={styles.buttonText}>+</Text>
                        </UITouchableOpacity>
                    </View>
                </View>
                <View style={styles.line} />
                <View style={[styles.counterContainer, {marginTop: 10}]}>
                    <Text style={[styles.label, {flex: 1}]}>
                        {`单车承运${cargoCategoryTxt}位`}
                        <Text style={styles.required}>*</Text>
                    </Text>
                    <View style={styles.capacityWrapper}>
                        <TextInput editable={editable} style={styles.capacityInput} keyboardType="numeric" value={plan?.unitWeight} onChangeText={onChangeCapacity} placeholder={'请输入'} placeholderTextColor={'#ccc'} textAlign={'right'} />
                        {editable && (
                            <UITouchableOpacity onPress={() => onChangeCapacity('')} style={styles.clearButton}>
                                <Text style={styles.clearText}>×</Text>
                            </UITouchableOpacity>
                        )}
                    </View>
                    <Text style={[styles.label, {marginLeft: 8}]}>{cargoCategoryTxt}</Text>
                </View>
            </View>
        );
    };

    //删除当前日期发布计划
    const onDelete = () => {
        list.current = list.current.filter((item) => item.planDay !== plan?.planDay);
        props.onDeletePlan(plan ?? {});
    };
    //取消未成功计划
    const onPressCancelFlag = () => {
        let p = {...plan};
        p.cancelOpeFlag = '1';
        p.planWeight = p.dealWeight ?? '0';
        setPlan(p);
    };

    return visable ? (
        <KeyboardAvoidingView behavior={'padding'}>
            <View style={styles.planViewStyle}>
                <View style={{flexDirection: 'row'}}>
                    <UIImage source={'add_deliver_order_icon_1'} style={{width: 24, height: 24, marginRight: 10}} />
                    <Text style={styles.planViewStyle6}>{`${props.selectDate} 发货计划`}</Text>
                    {!TextUtils.equals('1', plan?.isModifyFlag) && (
                        <UITouchableOpacity onPress={onDelete}>
                            <UIImage source={'add_deliver_order_icon_2'} style={{width: 18, height: 18}} />
                        </UITouchableOpacity>
                    )}
                </View>
                {/* 进度条 */}
                {progressBarTxt}
                {renderPlanTypeView()}
                <View style={styles.line} />
                {TextUtils.equals('1', plan?.planType) ? renderInputView() : TextUtils.equals('1', plan?.isModifyFlag) ? renderCarNoInputView() : renderCarInputView()}
                {/* 警告信息 */}
                {limitDataView}
                {plan?.newData != true && !TextUtils.equals('1', plan?.isCancelFlag) && !TextUtils.equals('1', plan?.cancelOpeFlag) && (
                    <Text style={styles.cancleTxt} onPress={onPressCancelFlag}>
                        取消未成交计划
                    </Text>
                )}
            </View>
        </KeyboardAvoidingView>
    ) : (
        <></>
    );
}

const BUTTON_SIZE = 32;

const styles = StyleSheet.create({
    planViewStyle: {
        backgroundColor: '#FFFFFF',
        marginTop: 10,
        paddingTop: 10,
        paddingBottom: 10,
        paddingLeft: 15,
        paddingRight: 15,
        borderRadius: 5,
    },
    planViewStyle1: {
        color: '#333333',
        fontWeight: 'bold',
        fontSize: 16,
    },
    planViewStyle2: {
        color: '#333333',
        fontWeight: 'bold',
        fontSize: 16,
    },
    planViewStyle3: {
        color: '#A6A6A6',
        fontSize: 13,
    },
    planViewStyle4: {
        color: '#FF883F',
        fontSize: 13,
    },
    planViewStyle5: {
        color: '#333333',
        fontSize: 15,
        marginTop: 13,
    },
    planViewStyle6: {
        color: '#333333',
        fontWeight: 'bold',
        fontSize: 16,
        flex: 1,
    },
    redTxt: {
        fontSize: 15,
        color: '#F81212',
    },
    textSelectStyle: {
        fontSize: 14,
        color: '#478DFB',
        backgroundColor: '#FFFFFF',
        borderColor: '#5086FC',
        borderWidth: 1,
        borderRadius: 4,
        paddingTop: 8,
        paddingBottom: 8,
        marginTop: 10,
        flex: 1,
        textAlign: 'center',
    },
    textUnSelectStyle: {
        fontSize: 14,
        color: '#666666',
        backgroundColor: '#FFFFFF',
        borderColor: '#979797',
        borderWidth: 1,
        borderRadius: 4,
        paddingTop: 8,
        paddingBottom: 8,
        marginTop: 10,
        flex: 1,
        textAlign: 'center',
    },

    container: {
        paddingTop: 10,
        paddingBottom: 15,
        backgroundColor: '#fff',
    },
    row: {
        flexDirection: 'row',
        alignItems: 'center',
        justifyContent: 'space-between',
    },
    label: {
        fontSize: 16,
        color: '#333',
    },
    required: {
        color: 'red',
    },
    counterContainer: {
        flexDirection: 'row',
        alignItems: 'center',
    },
    button: {
        width: BUTTON_SIZE,
        height: BUTTON_SIZE,
        borderWidth: 1,
        borderColor: '#ddd',
        justifyContent: 'center',
        alignItems: 'center',
        borderRadius: 4,
        backgroundColor: '#F4F4F4',
    },
    buttonText: {
        fontSize: 22,
        color: '#555',
    },
    input: {
        width: 80,
        height: 32,
        borderWidth: 1,
        borderColor: '#ddd',
        marginHorizontal: 8,
        borderRadius: 4,
        fontSize: 18,
        color: '#333333',
        paddingTop: 0,
        paddingBottom: 0,
    },
    capacityWrapper: {
        flexDirection: 'row',
        alignItems: 'center',
        borderWidth: 1,
        borderColor: '#ddd',
        borderRadius: 4,
        paddingHorizontal: 6,
        maxWidth: 100,
    },
    capacityInput: {
        flex: 1,
        fontSize: 16,
        color: '#000',
        height: '100%',
        paddingTop: 0,
        paddingBottom: 0,
        paddingLeft: 4,
        paddingRight: 4,
    },
    clearButton: {
        marginLeft: 6,
        justifyContent: 'center',
        alignItems: 'center',
    },
    clearText: {
        fontSize: 18,
        color: '#999',
    },
    hint: {
        color: '#999',
        fontSize: 13,
        marginTop: 6,
        alignSelf: 'flex-end',
    },
    line: {
        height: 1,
        width: gScreen_width,
        backgroundColor: '#eeeeee',
        marginTop: 8,
    },
    item: {
        flexDirection: 'row',
        alignItems: 'center',
        justifyContent: 'space-between',
    },
    leftTxt: {
        fontSize: 15,
        color: '#1F2937',
    },
    txt1: {
        fontSize: 13,
        color: '#C2C2C2',
        alignSelf: 'flex-end',
    },
    txt2: {
        fontSize: 15,
        color: '#333333',
    },
    cancleTxt: {
        marginTop: 7,
        color: '#A6A6A6',
        fontSize: 13,
        borderColor: '#A6A6A6',
        borderWidth: 1,
        borderRadius: 4,
        paddingTop: 5,
        paddingBottom: 5,
        alignSelf: 'flex-end',
        paddingLeft: 10,
        paddingRight: 10,
    },
});

export interface BatchOrderDailyPlanViewRef {
    //全部发布计划
    getPlanList(): RspBeforeModifyPlanV3[];
}

export default React.forwardRef<BatchOrderDailyPlanViewRef, Props>(BatchOrderDailyPlanView);
