import MarginRouter from './marginledger/router/MarginRouter';
import IssueSpecialTicketRouter from './issuespecialticket/router/IssueSpecialTicketRouter';
import OrderComfirmRouter from './confirm/router/OrderComfirmRouter';
import SupervisionRouter from './supervision/router/SupervisionRouter';
import RouterWallet from './wallet/routers/RouterWallet';
import AddDeliverOrderRouter from './add/routers/AddDeliverOrderRouter';
import WisdomRouters from './wisdom/WisdomRouters';
import ViolateRouter from './violate/router/ViolateRouter';
import RouterFreightPass from './freight/routers/RouterFreightPass';

export default [...MarginRouter, ...IssueSpecialTicketRouter, ...OrderComfirmRouter, ...SupervisionRouter, ...RouterWallet, ...AddDeliverOrderRouter, ...WisdomRouters, ...ViolateRouter, ...RouterFreightPass];
