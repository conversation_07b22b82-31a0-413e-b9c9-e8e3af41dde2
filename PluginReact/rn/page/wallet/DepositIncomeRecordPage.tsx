import {Dimensions, StyleSheet, Text, View} from 'react-native';
import React from 'react';
import {SceneMap, TabView} from 'react-native-tab-view';
import DepositIncomeRecordView from './views/DepositIncomeRecordView';
import BaseCommPage from '../../base/comm/BasePage';
import {NativeStackScreenProps} from '@react-navigation/native-stack';
import {ParamListBase} from '@react-navigation/native';
import {ZRouter} from '../../base/comm/ZRouter';
import UITitleView from '../../base/widget/UITitleView';
import UITouchableOpacity from '../../base/widget/UITouchableOpacity';
import {gScreen_width} from '../../base/Const';

interface State {
    tabIndex: number;
}

/**
 * 注释: 订金收入记录
 * 时间: 2025/3/31 星期一 14:01
 * <AUTHOR>
 */
export default class DepositIncomeRecordPage extends BaseCommPage<NativeStackScreenProps<ParamListBase>, State> {
    pageParams: any;
    orderId: string;

    constructor(props) {
        super(props);
        this.state = {
            tabIndex: 0,
        };
        this.pageParams = ZRouter.getParams(props);
        this.orderId = this.pageParams.orderId;
    }

    render() {
        return (
            <View style={{flex: 1}}>
                <UITitleView title={'订金收入记录'} />
                {this.renderMainView()}
            </View>
        );
    }

    /**
     * 注释: 绘制TabBar
     * 时间: 2025/4/1 星期二 9:52
     * <AUTHOR>
     * @param index
     * @returns {React.JSX.Element}
     */
    renderTabBar(index: number) {
        return (
            <View style={styles.tabBarStyle}>
                <UITouchableOpacity
                    style={styles.tabItemStyle}
                    onPress={() => {
                        this.setState({tabIndex: 0});
                    }}>
                    <Text style={{fontSize: 15, color: '#333'}}>未入账</Text>
                    {index == 0 && <View style={{width: 73, height: 3, backgroundColor: '#5086FC', position: 'absolute', bottom: 0}} />}
                </UITouchableOpacity>
                <UITouchableOpacity
                    style={styles.tabItemStyle}
                    onPress={() => {
                        this.setState({tabIndex: 1});
                    }}>
                    <Text style={{fontSize: 15, color: '#333'}}>已入账</Text>
                    {index == 1 && <View style={{width: 73, height: 3, backgroundColor: '#5086FC', position: 'absolute', bottom: 0}} />}
                </UITouchableOpacity>
            </View>
        );
    }

    /**
     * 注释: 绘制主视图
     * 时间: 2025/4/1 星期二 9:52
     * <AUTHOR>
     * @returns {React.JSX.Element}
     */
    renderMainView() {
        //界面title
        let routes: {key: string; title: string}[];
        //tabs页面
        let renderScene: any;
        routes = [
            {key: 'first', title: '未入账'},
            {key: 'second', title: '已入账'},
        ];
        renderScene = SceneMap({
            first: () => {
                return <DepositIncomeRecordView queryType={1} orderId={this.orderId} />;
            },
            second: () => {
                return <DepositIncomeRecordView queryType={2} orderId={this.orderId} />;
            },
        });
        return (
            <TabView
                navigationState={{index: this.state.tabIndex, routes}}
                renderScene={renderScene}
                onIndexChange={(tabIndex) => {
                    this.setState({tabIndex: tabIndex});
                }}
                renderTabBar={() => this.renderTabBar(this.state.tabIndex)}
                initialLayout={{width: Dimensions.get('window').width}}
            />
        );
    }
}

const styles = StyleSheet.create({
    tabBarStyle: {
        width: gScreen_width,
        height: (gScreen_width / 375) * 42,
        flexDirection: 'row',
        alignItems: 'center',
        backgroundColor: '#fff',
    },
    tabItemStyle: {
        flex: 1,
        height: (gScreen_width / 375) * 42,
        alignItems: 'center',
        justifyContent: 'center',
    },
});
