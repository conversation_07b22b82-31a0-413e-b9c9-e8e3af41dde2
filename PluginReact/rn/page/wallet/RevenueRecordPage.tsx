import {StyleSheet, Text, View} from 'react-native';
import React from 'react';
import DateTimePicker, {DateType} from 'react-native-dates-picker';
import dayjs from 'dayjs';
import {TradeRecord} from './models/TradeRecord';
import BaseCommPage from '../../base/comm/BasePage';
import {NativeStackScreenProps} from '@react-navigation/native-stack';
import {ParamListBase} from '@react-navigation/native';
import {ZRouter} from '../../base/comm/ZRouter';
import {ReqQueryRevenueRecord} from './requests/ReqQueryRevenueRecord';
import {ArrayUtils} from '../../base/utils/ArrayUtils';
import UITitleView from '../../base/widget/UITitleView';
import UIListView from '../../base/widget/UIListView';
import UIPopup from '../../base/widget/UIPopup';
import UISearchView from '../../base/widget/UISearchView';
import UITouchableOpacity from '../../base/widget/UITouchableOpacity';
import UIImage from '../../base/widget/UIImage';
import {subtractDaysFromTime} from "../../base/utils/DateUtil";

interface State {
    date: any;
    showDate: boolean;
    startDate?: DateType;
    endDate?: DateType;
    showStatusPop: boolean;
    listData?: TradeRecord[];
}

/**
 * 注释: 收支记录
 * 时间: 2025/3/31 星期一 14:31
 * <AUTHOR>
 */
export default class RevenueRecordPage extends BaseCommPage<NativeStackScreenProps<ParamListBase>, State> {
    pageParams: any;
    startDate?: DateType;
    endDate?: DateType;
    bookNo?: string;
    nowPage: number = 1;
    orderId?: string;

    constructor(props) {
        super(props);
        this.state = {
            date: dayjs(),
            showDate: false,
            showStatusPop: false,
            startDate: subtractDaysFromTime(new Date(), 30),
            endDate: new Date(),
        };
        this.pageParams = ZRouter.getParams(props);
        this.bookNo = this.pageParams.bookNo;
    }

    componentDidMount() {
        this.queryRevenueRecord();
    }

    /**
     * 注释: 查询收支记录
     * 时间: 2025/4/10 星期四 9:58
     * <AUTHOR>
     */
    queryRevenueRecord() {
        let request = new ReqQueryRevenueRecord();
        request.nowPage = this.nowPage;
        request.bookNo = this.bookNo;
        request.pageSize = 10;
        request.accountDateS = this.state.startDate ? dayjs(this.state.startDate).format('YYYY-MM-DD') : '';
        request.accountDateE = this.state.endDate ? dayjs(this.state.endDate).format('YYYY-MM-DD') : '';
        request.orderId = this.orderId;
        request.request().then((res) => {
            if (res.isSuccess()) {
                if (this.nowPage == 1) {
                    this.setState({listData: res.data?.rootArray});
                } else {
                    this.setState({listData: [...(this.state.listData ?? []), ...(res.data?.rootArray ?? [])]});
                }
                this.nowPage++;
            }
        });
    }

    render() {
        return (
            <View style={{flex: 1, backgroundColor: '#EFF0F3'}}>
                <UITitleView title={'收支记录'} />
                {this.renderHeadView()}
                <UIListView
                    contentContainerStyle={{backgroundColor: '#EFF0F3'}}
                    renderItem={({item}) => this.renderItemView(item)}
                    dataList={this.state.listData}
                    onRefresh={() => {
                        this.nowPage = 1;
                        this.queryRevenueRecord();
                    }}
                    onLoadMore={() => {
                        this.queryRevenueRecord();
                    }}
                />
                {this.renderDateRange()}
            </View>
        );
    }

    /**
     * 注释: 绘制Item
     * 时间: 2025/4/1 星期二 11:30
     * <AUTHOR>
     * @returns {React.JSX.Element}
     */
    renderItemView(item: TradeRecord) {
        return (
            <View
                style={{
                    flexDirection: 'column',
                    backgroundColor: '#fff',
                    marginTop: 8,
                    paddingHorizontal: 15,
                    paddingBottom: 10,
                }}>
                {this.renderItem('交易时间：', item.tradeTime)}
                {this.renderItem('交易流水号：', item.imei)}
                {this.renderItem('交易方向：', item.financeTypeDesc)}
                {this.renderItem('交易类型：', item.operateRemark)}
                {this.renderItem('交易金额：', item.moneyStr)}
                {this.renderItem('运单号：', item.orderId)}
                {this.renderItem('摘要：', item.abstractRemark)}
            </View>
        );
    }

    /**
     * 注释: 绘制Item
     * 时间: 2025/4/1 星期二 11:02
     * <AUTHOR>
     * @param label
     * @param value
     * @param seeDetail
     * @returns {React.JSX.Element}
     */
    renderItem(label: string, value: string, seeDetail: boolean = false) {
        return (
            <View style={{flexDirection: 'row', alignItems: 'center', marginTop: 10}}>
                <Text style={{fontSize: 16, color: '#666'}}>{label}</Text>
                <Text style={{fontSize: 16, color: '#333'}}>{value}</Text>
                {seeDetail && <Text style={{fontSize: 14, color: '#1F66FF', marginLeft: 10}}>详情</Text>}
            </View>
        );
    }

    /**
     * 注释: 绘制时间选择范围
     * 时间: 2025/3/31 星期一 17:00
     * <AUTHOR>
     * @returns {React.JSX.Element}
     */
    renderDateRange() {
        if (this.state.showDate) {
            return (
                <UIPopup
                    title={'选择时间'}
                    subTitle={'确认'}
                    showSubTitle={true}
                    onSubCallBack={() => {
                        this.setState({showDate: false, startDate: this.startDate, endDate: this.endDate}, () => {
                            this.nowPage = 1;
                            this.queryRevenueRecord();
                        });
                    }}
                    onClose={() => {
                        this.setState({showDate: false});
                    }}>
                    <View style={{backgroundColor: '#fff'}}>
                        <DateTimePicker
                            mode={'range'}
                            date={this.state.date}
                            selectedRangeBackgroundColor={'#1F66FF80'}
                            onChange={({startDate, endDate}) => {
                                this.startDate = startDate;
                                this.endDate = endDate;
                            }}
                            containerStyle={{paddingBottom: 25}}
                        />
                    </View>
                </UIPopup>
            );
        }
    }

    /**
     * 注释: 绘制头部视图
     * 时间: 2025/3/31 星期一 15:11
     * <AUTHOR>
     * @returns {React.JSX.Element}
     */
    renderHeadView() {
        return (
            <View style={{backgroundColor: '#fff', padding: 15}}>
                <UISearchView
                    placeholder={'请输入运单号搜索'}
                    leftImg={'base_search_black'}
                    searchBtn={true}
                    onSubmitEditing={(e) => {
                        this.orderId = e;
                        this.nowPage = 1;
                        this.queryRevenueRecord();
                    }}
                />
                <UITouchableOpacity
                    style={{flexDirection: 'row', alignItems: 'center', marginTop: 13}}
                    onPress={() => {
                        this.setState({showDate: true});
                    }}>
                    <Text
                        style={{
                            fontSize: 14,
                            color: '#1F66FF',
                        }}>
                        {this.state.startDate ? `${dayjs(this.state.startDate).format('YYYY-MM-DD')} 至 ${dayjs(this.state.endDate).format('YYYY-MM-DD')}` : '请选择时间'}
                    </Text>
                    <UIImage source={'icon_use_login_down'} style={{width: 16, height: 10}} />
                </UITouchableOpacity>
            </View>
        );
    }
}

const styles = StyleSheet.create({});
