/**
 * 支付订单详情实体
 */
export class PaymentOrderDetail {
    /** 运单号 */
    orderId: string;
    /** 申请流水号 */
    paymentBizImei: string;
    /** 支付申请时间 */
    createdTimeStr: string;
    /** 入账时间 */
    paymentPostingTimeStr: string;

    /**
     * 账状态
     * 1-未入账
     * 2-入账中
     * 3-入账失败
     * 4-入账成功
     */
    paymentPostingStatus: string;
    /** 账状态中文描述 */
    paymentPostingStatusStr: string;

    /** 货主名称 */
    consignorName: string;
    /** 承运方名称 */
    carrierName: string;
    /** 车牌号 */
    plateNumber: string;
    /** 货物名称 */
    cargoName: string;

    /**
     * 订金类型
     * 1-可退
     * 2-不可退
     */
    earnestType: string;
    /** 订金类型中文描述 */
    earnestTypeStr: string;

    /**
     * 支付状态
     * 0-无效
     * 1-支付中
     * 2-支付成功
     * 3-支付失败
     * 4-支付关闭
     */
    paymentStatus: string;
    /** 支付状态中文描述 */
    paymentStatusStr: string;

    /**
     * 支付方式中文描述
     * 0-初始化
     * 1-微信
     * 2-支付宝
     * 3-云闪付
     * 5-交易通余额
     */
    payChannelStr: string;

    /** 支付金额（格式化） */
    actualPaymentMoneyStr: string;
    /** 订金金额（格式化） */
    earnestMoneyStr: string;
    /** 佣金金额（格式化） */
    commissionMoneyStr: string;
    /** 优惠金额（格式化） */
    couponMoneyStr: string;
    /** 退款金额（格式化） */
    refundMoneyStr: string;

    /**
     * 退款状态
     * 0-无
     * 1-退款中
     * 2-退款成功
     * 6-退款失败
     */
    refundStatusStr: string;
    /** 退款类型中文描述 */
    refundTypeStr: string;
    /** 退款原因 */
    refundRemark: string;
    /** 退款时间 */
    refundTimeStr: string;

    /** 订金状态（数字） */
    bizStatus: string;
    /** 订金状态中文描述 */
    bizStatusStr: string;

    /** 启运地详细地址 */
    despatchPlaceStr: string;
    /** 目的地详细地址 */
    deliverPlaceStr: string;
}
