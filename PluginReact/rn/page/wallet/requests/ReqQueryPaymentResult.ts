import {BaseRequest} from '../../../base/http/BaseRequest';
import {BaseResponse} from '../../../base/http/BaseResponse';
import {ResultData} from '../../../base/http/ResultData';

/**
 *  desc: 查询银行卡打款状态
 *  user: 宋双朋
 *  time: 2025/4/10 20:19
 */
export class ReqQueryPaymentResult extends BaseRequest {
    cardId: string;
    realName: string;

    async request(): Promise<BaseResponse<RspQueryPaymentResult>> {
        this.params = {
            cardId: this.cardId,
            realName: this.realName,
        };
        return super.post('pps-app/account/bankCard/queryPaymentResult', RspQueryPaymentResult);
    }
}

export class RspQueryPaymentResult extends ResultData {
    status: string = ''; //付款状态1：未付款 2：付款中 3：付款失败 4：付款成功
}
