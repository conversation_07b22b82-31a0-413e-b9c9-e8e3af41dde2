import {BaseRequest} from "../../../base/http/BaseRequest";
import {BaseResponse} from "../../../base/http/BaseResponse";
import {ResultData} from "../../../base/http/ResultData";

/**
 * 注释: 充值户信息查询
 * 时间: 2025/4/8 星期二 9:59
 * <AUTHOR>
 */
export class ReqRechargeAccountInfo extends BaseRequest {
    async request(): Promise<BaseResponse<RspRechargeAccountInfo>> {
        this.params = {};
        return super.post('pps-app/passport/account/getRechargeAccountInfo', RspRechargeAccountInfo);
    }
}

export class RspRechargeAccountInfo extends ResultData {
    public tips: string[];
    //收款账户名称
    public accountName: string;
    //充值账户号
    public accountNo: string;
    //开户行
    public bankSubName: string;
}
