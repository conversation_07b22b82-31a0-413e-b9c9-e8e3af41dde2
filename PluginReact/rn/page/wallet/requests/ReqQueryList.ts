import {BaseRequest} from "../../../base/http/BaseRequest";
import {BaseResponse} from "../../../base/http/BaseResponse";
import PageList from "../../../base/http/PageList";
import {ResultData} from "../../../base/http/ResultData";

/**
 *  desc: 查询归属银行
 *  user: 宋双朋
 *  time: 2025/4/9 19:30
 */
export class ReqQueryList extends BaseRequest {
    async request(): Promise<BaseResponse<PageList<RspQueryList>>> {
        this.params = {};
        return super.postList('pps-app/account/bankCard/queryList', RspQueryList);
    }
}

export class RspQueryList extends ResultData {
    logoPic?: string; // 银行logo
    bankno?: string; // 银行卡号
    bankName?: string; // 银行名称
}

export default function showText(item: RspQueryList): string {
    return item.bankName ?? '';
}
