import {BaseRequest} from '../../../base/http/BaseRequest';
import {BaseResponse} from '../../../base/http/BaseResponse';
import PageList from '../../../base/http/PageList';
import {ResultData} from '../../../base/http/ResultData';

/**
 * 注释: 查询银行卡列表
 * 时间: 2025/4/8 星期二 16:20
 * <AUTHOR>
 */
export class ReqBankList extends BaseRequest {
    async request(): Promise<BaseResponse<PageList<RsqBankList>>> {
        this.params = {};
        return super.postList('pps-app/account/bankCard/queryBankPassportList', RsqBankList);
    }
}

export class RsqBankList extends ResultData {
    isDefault: string; //是否默认
    backgroundColor: string; //背景色
    bankCardNo: string; //银行卡号
    cardType: string; //是否借记卡
    logo: string; //银行图标
    bankName: string; //银行卡名称
    cid: string; //id
    status: string; //1 可用 2 待认证
    bindState: string; //绑定状态 1未绑定 2已绑定
}

export const formatCardNumberV1 = (cardNumber: string) => {
    return cardNumber.replace(/(\d{4})(?=\d)/g, '$1 ');
};
export const formatCardNumberV2 = (cardNumber: string) => {
    return cardNumber.replace(/\D/g, '');
};

export function bindStateImg(data: RsqBankList): string {
    switch (data.bindState) {
        case '2':
            return 'wallet_icon_4';
        default:
            return '';
    }
}

export function bindStateStr(data: RsqBankList): string {
    switch (data.bindState) {
        case '1':
            return '绑定';
        case '2':
            return '解除绑定';
        default:
            return '';
    }
}
