import {BaseRequest} from "../../../base/http/BaseRequest";
import {BaseResponse} from "../../../base/http/BaseResponse";
import {ResultData} from "../../../base/http/ResultData";

/**
 * 注释: 提现设备认证
 * 时间: 2025/4/9 星期三 10:18
 * <AUTHOR>
 */
export class ReqWisdomAuthenticaton extends BaseRequest {
    mobile: string;
    verifyCode: string;
    moduleType: string;
    macAddress: string;
    udid?: string;

    async request(): Promise<BaseResponse<ResultData>> {
        this.params = {
            mobile: this.mobile,
            verifyCode: this.verifyCode,
            moduleType: this.moduleType,
            macAddress: this.macAddress,
            udid: this.udid,
            verifyCodeType: '1',
        };
        return super.post('mms-app/mms/financeDevice/checkVerifyCodeAndUpdateFinanceDevice', ResultData);
    }
}
