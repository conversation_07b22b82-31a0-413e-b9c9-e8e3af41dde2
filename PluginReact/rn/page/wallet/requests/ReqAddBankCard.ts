import {BaseRequest} from '../../../base/http/BaseRequest';
import {BaseResponse} from '../../../base/http/BaseResponse';
import {ResultData} from '../../../base/http/ResultData';

/**
 *  desc: 添加银行卡
 *  user: 宋双朋
 *  time: 2025/4/9 19:30
 */
export class ReqAddBankCard extends BaseRequest {
    /**
     * 银行卡号
     */
    cardNumber?: string = '';

    /**
     * 身份证号
     */
    idCard?: string = '';

    /**
     * 持卡人姓名
     */
    realName?: string = '';

    /**
     * 银行行号
     */
    bankno?: string = '';
    /**
     * 验证码
     */
    captcha?: string = '';
    /**
     * 发送图形验证码手机号
     */
    mobile?: string = '';
    /**
     * 添加银行卡次数限制标识 需要验证码 1
     */
    sendCaptcha?: string = '1';
    /**
     * 添卡接口版本号
     */
    addBankVersion?: string = '1.0.0';
    /**
     * 1-添加本人银行卡，身份证号和持卡人姓名可选填
     *
     * 2-添加非人银行卡，身份证号和持卡人信息必填
     *
     * 3-添加对公银行卡
     */
    addCardType?: string = '';
    /**
     * 是否勾选用户信息采集协议 0-否 1-是
     */
    signAgreement?: string = '';
    /**
     * 1-设置为默认 0-不设置
     */
    isDefault?: string = '';

    async request(): Promise<BaseResponse<RspAddBankCard>> {
        this.params = {
            cardNumber: this.cardNumber,
            idCard: this.idCard,
            realName: this.realName,
            bankno: this.bankno,
            captcha: this.captcha,
            mobile: this.mobile,
            sendCaptcha: this.sendCaptcha,
            addBankVersion: this.addBankVersion,
            addCardType: this.addCardType,
            signAgreement: this.signAgreement,
            isDefault: this.isDefault,
        };
        return super.post('pps-app/account/bankCard/addBankCard', RspAddBankCard);
    }
}

export class RspAddBankCard extends ResultData {
    /** 1-提醒app前往进行身份实名*/
    checkState?: string = '';
}
