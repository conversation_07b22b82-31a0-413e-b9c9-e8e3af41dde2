import {BaseRequest} from "../../../base/http/BaseRequest";
import {BaseResponse} from "../../../base/http/BaseResponse";
import PageList from "../../../base/http/PageList";
import {FreezeRecord} from "../models/FreezeRecord";

/**
 * 注释: 冻结记录列表
 * 时间: 2025/4/8 星期二 10:36
 * <AUTHOR>
 */
export class ReqQueryFreezeList extends BaseRequest {
    bookNo?: string;
    orderId?: string;
    nowPage: number;
    pageSize: number;
    accountDateS: string;
    accountDateE: string;

    async request(): Promise<BaseResponse<PageList<FreezeRecord>>> {
        this.params = {
            nowPage: this.nowPage,
            pageSize: this.pageSize,
            bookNo: this.bookNo,
            orderId: this.orderId,
            accountDateS: this.accountDateS,
            accountDateE: this.accountDateE,
        };
        return super.postList('pps-app/passport/account/freezeList', FreezeRecord);
    }
}
