import {StyleSheet, Text, View} from 'react-native';
import React, {useEffect, useRef, useState} from 'react';
import Modal from 'react-native-modal';
import {ToastBuilder} from '../../../base/comm/UI';
import {ReqSendVerifyCode} from '../requests/ReqSendVerifyCode';
import {getLogin, getMacAddress} from '../../../base/comm/NativeUtils';
import {ReqWisdomAuthenticaton} from '../requests/ReqWisdomAuthenticaton';
import UITouchableOpacity from '../../../base/widget/UITouchableOpacity';
import UIImage from '../../../base/widget/UIImage';
import SmsCodeInput from './SmsCodeInput';
import UIButton from '../../../base/widget/UIButton';

interface Props {
    onClose?: Function;
    onSubmit?: Function;
}

/**
 * 注释: 设备授权弹窗
 * 时间: 2025/4/1 星期二 16:48
 * <AUTHOR>
 * @param props
 * @constructor
 */
export default function DeviceAuthDialog(props: Props) {
    const [timer, setTimer] = useState<number>(120);
    const [smsCode, setSmsCode] = useState<string>();
    const [showDialog, setShowDialog] = useState<boolean>(false);
    const intervalRef = useRef<NodeJS.Timeout>();
    // 120s倒计时
    useEffect(() => {
        startTimer();
        sendVerifyCode();
        // 清除定时器
        return () => {
            if (intervalRef.current) {
                clearInterval(intervalRef.current);
            }
        };
    }, []);

    useEffect(() => {
        if (timer <= 0 && intervalRef.current) {
            clearInterval(intervalRef.current);
            intervalRef.current = undefined;
        }
    }, [timer]);

    /**
     * 注释: 发送设备认证验证码
     * 时间: 2025/4/9 星期三 10:21
     * <AUTHOR>
     */
    function sendVerifyCode() {
        let request = new ReqSendVerifyCode();
        request.mobile = getLogin()?.mobile ?? '';
        request.type = '1';
        request.moduleType = '13';
        request.plateFormType = '1';
        request.request().then((res) => {
            if (res.isSuccess()) {
                setShowDialog(true);
            } else {
                new ToastBuilder().showMsg(res.getMsg());
                clearInterval(intervalRef.current);
                intervalRef.current = undefined;
                props.onClose && props.onClose();
            }
        });
    }

    /**
     * 注释: 校验验证码
     * 时间: 2025/4/9 星期三 11:05
     * <AUTHOR>
     */
    async function checkVerifyCode() {
        let request = new ReqWisdomAuthenticaton();
        request.mobile = getLogin()?.mobile ?? '';
        request.macAddress = getMacAddress();
        request.verifyCode = smsCode ?? '';
        request.moduleType = '13';
        request.request().then((res) => {
            props.onClose && props.onClose();
            if (res.isSuccess()) {
                props.onSubmit && props.onSubmit();
            } else {
                new ToastBuilder().showMsg(res.getMsg());
            }
        });
    }

    /**
     * 注释: 启动倒计时
     * 时间: 2025/4/2 星期三 9:29
     * <AUTHOR>
     */
    function startTimer() {
        intervalRef.current = setInterval(() => {
            setTimer((prevTimer) => prevTimer - 1);
        }, 1000);
    }

    if (showDialog) {
        return (
            <Modal style={[{justifyContent: 'center', padding: 0, margin: 0}]} animationIn={'slideInUp'} backdropOpacity={0.3} useNativeDriver={true} isVisible={true}>
                <View style={styles.mainStyle}>
                    {/*标题头*/}
                    <View style={styles.headerStyle}>
                        <Text style={{fontSize: 18, color: '#5086FC', fontWeight: 'bold'}}>设备认证</Text>
                        <UITouchableOpacity
                            style={{position: 'absolute', top: 10, right: 10}}
                            onPress={() => {
                                props.onClose && props.onClose();
                            }}>
                            <UIImage source={'ic_dialog_close'} style={{width: 15, height: 15}} />
                        </UITouchableOpacity>
                    </View>
                    <Text style={{marginVertical: 21, fontSize: 14, color: '#333', paddingHorizontal: 20}}>您当前的登录手机需要认证， 系统已给您注册手机号发送短信验证码， 完成验证后方可进行操作。</Text>
                    <SmsCodeInput
                        onChangeText={(code) => {
                            setSmsCode(code);
                        }}
                        maxNum={4}
                    />
                    <UITouchableOpacity
                        style={{
                            alignSelf: 'flex-end',
                            marginTop: 10,
                            marginRight: 24,
                        }}
                        onPress={() => {
                            if (timer <= 0) {
                                setTimer(120);
                                setTimeout(() => {
                                    startTimer();
                                    sendVerifyCode();
                                }, 200);
                            }
                        }}>
                        <Text
                            style={{
                                fontSize: 14,
                                color: timer > 0 ? '#999' : '#5086FC',
                            }}>
                            {timer > 0 ? `(${timer}s)后重新获取短信验证码` : '重新发送验证码'}
                        </Text>
                    </UITouchableOpacity>
                    <UIButton
                        text={'确定'}
                        style={{width: 260, height: 46, marginVertical: 25}}
                        enabled={smsCode?.length == 4}
                        onPress={() => {
                            checkVerifyCode();
                        }}
                    />
                </View>
            </Modal>
        );
    } else {
        return <View />;
    }
}

const styles = StyleSheet.create({
    mainStyle: {
        borderRadius: 8,
        backgroundColor: '#fff',
        width: 300,
        alignItems: 'center',
        alignSelf: 'center',
    },
    headerStyle: {
        backgroundColor: '#ECF5FF',
        width: '100%',
        height: 65,
        justifyContent: 'center',
        alignItems: 'center',
        borderTopLeftRadius: 8,
        borderTopRightRadius: 8,
    },
});
