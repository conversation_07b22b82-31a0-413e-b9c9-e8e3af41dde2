import React, {useState, useRef, useEffect} from 'react';
import {View, TextInput, StyleSheet, Alert, ViewStyle, TextStyle} from 'react-native';

interface Props {
    onChangeText?: (text: string) => void;
    handleSubmit?: (code: string) => void;
    maxNum?: number;
    inputStyle?: TextStyle;
}

/**
 *  验证码输入框
 * @param props
 * @constructor
 */
export default function SmsCodeInput(props: Props) {
    const [code, setCode] = useState(['', '', '', '', '', '']); // 存储 6 位验证码
    const inputs = useRef<Array<TextInput | null>>([]); // 用于存储输入框的引用

    useEffect(() => {
        if (props.maxNum) {
            setCode(new Array(props.maxNum).fill(''));
        }
    }, []);
    // 处理输入变化
    const handleChangeText = (text, index) => {
        const newCode = [...code];
        newCode[index] = text;
        setCode(newCode);

        props.onChangeText && props.onChangeText(newCode.join(''));

        // 如果所有输入框都已填满，触发验证
        if (newCode.every((c) => c !== '')) {
            handleSubmit(newCode.join(''));
        }

        // 自动聚焦到下一个输入框
        if (text && index < code.length - 1) {
            // @ts-ignore
            inputs.current[index + 1].focus();
        }
    };

    // 处理删除键
    const handleKeyPress = (event, index) => {
        if (event.nativeEvent.key === 'Backspace' && index > 0 && !code[index]) {
            // @ts-ignore
            inputs.current[index - 1].focus();
        }
    };

    // 提交验证
    const handleSubmit = (fullCode) => {
        props.handleSubmit && props.handleSubmit(fullCode);
    };

    return (
        <View style={styles.container}>
            {code.map((digit, index) => (
                <TextInput
                    key={index}
                    style={[styles.input, props.inputStyle]}
                    maxLength={1}
                    keyboardType="numeric"
                    value={digit}
                    onChangeText={(text) => handleChangeText(text, index)}
                    onKeyPress={(event) => handleKeyPress(event, index)}
                    ref={(ref) => (inputs.current[index] = ref)}
                />
            ))}
        </View>
    );
}

const styles = StyleSheet.create({
    container: {
        width: '100%',
        marginTop: 15,
        flexDirection: 'row',
        justifyContent: 'space-between',
        paddingHorizontal: 20,
    },
    input: {
        width: 50,
        height: 50,
        backgroundColor: '#F7F7F7',
        borderRadius: 9,
        color: '#333',
        textAlign: 'center',
        fontWeight: 'bold',
        fontSize: 22,
    },
});
