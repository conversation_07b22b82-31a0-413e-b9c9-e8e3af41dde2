import {StyleSheet, Text, View} from 'react-native';
import React from 'react';
import Modal from 'react-native-modal';
import UITouchableOpacity from '../../../base/widget/UITouchableOpacity';
import UIImage from '../../../base/widget/UIImage';
import SmsCodeInput from './SmsCodeInput';
import {ZRouter} from '../../../base/comm/ZRouter';

interface Props {
    onClose?: Function;
    onSubmit?: Function;
    money?: string;
}

/**
 * 注释: 支付密码弹窗
 * 时间: 2025/4/7 星期一 9:43
 * <AUTHOR>
 * @param props
 * @constructor
 */
export default function PayPasswordDialog(props: Props) {
    return (
        <Modal
            style={[{justifyContent: 'center', padding: 0, margin: 0}]}
            animationIn={'slideInUp'}
            backdropOpacity={0.3}
            useNativeDriver={true}
            isVisible={true}
            onBackButtonPress={() => {
                props.onClose && props.onClose();
            }}
            onBackdropPress={() => {
                props.onClose && props.onClose();
            }}>
            <View style={{flexDirection: 'column', backgroundColor: '#fff', borderRadius: 8, marginHorizontal: 35}}>
                <View style={styles.titleStyle}>
                    <Text style={{fontSize: 16, color: '#333'}}>请输入支付密码</Text>
                    <UITouchableOpacity
                        style={{position: 'absolute', top: 20, left: 15}}
                        onPress={() => {
                            props.onClose && props.onClose();
                        }}>
                        <UIImage source={'com_close_icon'} style={{width: 13, height: 13}} />
                    </UITouchableOpacity>
                </View>
                <Text style={styles.moneyStyle}>{`¥${props.money}`}</Text>
                <SmsCodeInput
                    maxNum={6}
                    inputStyle={styles.inputStyle}
                    handleSubmit={(code) => {
                        props.onClose && props.onClose();
                        props.onSubmit && props.onSubmit(code);
                    }}
                />
                <UITouchableOpacity
                    onPress={() => {
                        props.onClose && props.onClose();
                        ZRouter.toPage({page: 'SettingPayPasswordOne', params: {type: 1}});
                    }}>
                    <Text style={styles.forgetPassword}>忘记密码</Text>
                </UITouchableOpacity>
            </View>
        </Modal>
    );
}

const styles = StyleSheet.create({
    inputStyle: {
        width: 43,
        height: 43,
        backgroundColor: '#fff',
        borderColor: '#eee',
        borderWidth: 0.5,
        borderRadius: 0,
        fontSize: 18,
    },
    forgetPassword: {
        fontSize: 14,
        color: '#5086FC',
        marginTop: 10,
        marginBottom: 15,
        marginRight: 15,
        alignSelf: 'flex-end',
    },
    moneyStyle: {
        fontSize: 21,
        color: '#333',
        marginTop: 10,
        marginBottom: 15,
        alignSelf: 'center',
    },
    titleStyle: {
        flexDirection: 'row',
        alignItems: 'center',
        justifyContent: 'center',
        height: 56,
        borderBottomWidth: 0.5,
        borderColor: '#eee',
    },
});
