import {Clipboard, StyleSheet, Text, View} from 'react-native';
import React, {useState} from 'react';
import {Swipeable} from 'react-native-gesture-handler';
import LinearGradient from 'react-native-linear-gradient';
import {DialogBuilder} from '../../../base/comm/UI';
import UIButton from '../../../base/widget/UIButton';
import {bindStateImg, formatCardNumberV1, RsqBankList} from '../requests/ReqBankList';
import {FaceRecognitionParams, getImageUrl, getLogin, startFaceRecognition} from '../../../base/comm/NativeUtils';
import {ReqGetPassportAccount} from '../requests/ReqGetPassportAccount';
import UIImage from '../../../base/widget/UIImage';
import UITouchableOpacity from '../../../base/widget/UITouchableOpacity';
import TextUtils from '../../../base/utils/TextUtils';
import {ReqQueryPaymentResult} from '../requests/ReqQueryPaymentResult';
import {ReqBindBankPassport} from '../requests/ReqBindBankPassport';
import {ReqFaceRegoin} from '../../../base/comm/requests/ReqFaceRegoin';
import {ReqChangeBankCardState} from '../requests/ReqChangeBankCardState';
import {ReqQueryBankCardNumber} from '../requests/ReqQueryBankCardNumber';
import {ReqFaceRecognitionVerifyV2} from '../requests/ReqFaceRecognitionVerifyV2';
import {ReqQueryUserBriefInfo} from '../../../base/comm/requests/ReqQueryUserBriefInfo';

interface Props {
    item: RsqBankList;
    callBack: Function;
    verifyCallBack: Function;
    serviceCallBack: Function;
}

/**
 * 注释: 银行卡条目
 * 时间: 2025/4/9 14:31
 * <AUTHOR>
 * @param props
 * @constructor
 */
export default function WalletBankItemView(props: Props) {
    const [rightMargin, setRightMargin] = useState(15);
    const [rightBorderRadius, setRightBorderRadius] = useState(5);
    const login = getLogin();

    /**
     * 注释：查询交易通账户信息
     * 时间：2025/4/16 14:02
     * @author：宋双朋
     */
    const queryPassportAccount = () => {
        let request = new ReqGetPassportAccount();
        request.request().then((res) => {
            if (res.isSuccess()) {
                bind(res.data?.bookInfo?.subsidiaryId);
            }
        });
    };

    /**
     * 注释：人脸识别
     * 时间：2025/4/16 11:45
     * @author：宋双朋
     */
    const liveImage = () => {
        let req = new ReqQueryUserBriefInfo();
        req.request().then((res) => {
            if (res.isSuccess()) {
                let params = new FaceRecognitionParams();
                params.idCardName = res.data?.customerNm ?? '';
                params.idCardNo = res.data?.idCardNo ?? '';
                params.callBack = (response) => {
                    let res = JSON.parse(response);
                    if (res.code === '200') {
                        let req = new ReqFaceRecognitionVerifyV2();
                        let faceInfo = JSON.parse(res.faceInfo);
                        req.orderNo = faceInfo?.orderNo;
                        req.request().then((response) => {
                            if (response.isSuccess()) {
                                // 人脸识别成功,查询银行卡号
                                queryBankCardNumber();
                            } else {
                                new DialogBuilder()
                                    .setMsg(res.getMsg())
                                    .setRightTxt('我知道了')
                                    .setModel(1)
                                    .setRightOnClick((dialog) => {
                                        dialog.dismiss();
                                    })
                                    .show();
                            }
                        });
                    } else {
                        this.showToast(res.msg);
                    }
                };
                startFaceRecognition(params);
            }
        });
    };
    const bind = (subsidiaryId?: string) => {
        let request = new ReqBindBankPassport();
        request.cardId = props.item?.cid ?? '';
        request.subsidiaryId = subsidiaryId ?? '';
        request.request().then((res) => {
            if (res.isSuccess()) {
                //刷新列表
                props.callBack() && props.callBack();
            } else {
                new DialogBuilder()
                    .setMsg(res.getMsg())
                    .setRightTxt('我知道了')
                    .setModel(1)
                    .setRightOnClick((dialog) => {
                        dialog.dismiss();
                    })
                    .show();
            }
        });
    };
    const deleteBank = () => {
        let request = new ReqChangeBankCardState();
        request.cardId = props.item?.cid ?? '';
        request.state = '3';
        request.request().then((res) => {
            if (res.isSuccess()) {
                //刷新列表
                props.callBack() && props.callBack();
            } else {
                new DialogBuilder()
                    .setMsg(res.getMsg())
                    .setRightTxt('我知道了')
                    .setModel(1)
                    .setRightOnClick((dialog) => {
                        dialog.dismiss();
                    })
                    .show();
            }
        });
    };
    /**
     * 注释: 复制文本到剪切板
     * 时间: 2023/9/15 14:56
     * <AUTHOR>
     */
    const copyTxt = (text?: string) => {
        Clipboard.setString(text ?? '');
        this.showToast('复制成功');
    };
    const queryBankCardNumber = () => {
        let request = new ReqQueryBankCardNumber();
        request.cardId = props.item?.cid ?? '';
        request.request().then((res) => {
            if (res.isSuccess()) {
                //展示卡号
                new DialogBuilder()
                    .setMsg(res.data?.bankCardNumber ?? '')
                    .setRightTxt('我知道了')
                    .setLeftTxt('复制卡号')
                    .setModel(2)
                    .setLeftOnClick((dialog) => {
                        dialog.dismiss();
                        //复制卡号
                        copyTxt(res.data?.bankCardNumber);
                    })
                    .setRightOnClick((dialog) => {
                        dialog.dismiss();
                    })
                    .show();
            } else {
                new DialogBuilder()
                    .setMsg(res.getMsg())
                    .setRightTxt('我知道了')
                    .setModel(1)
                    .setRightOnClick((dialog) => {
                        dialog.dismiss();
                    })
                    .show();
            }
        });
    };
    const verifyMoney = () => {
        let login = getLogin();
        let request = new ReqQueryPaymentResult();
        request.cardId = props.item?.cid ?? '';
        request.realName = login?.memberName ?? '';
        request.request().then((res) => {
            if (res.isSuccess()) {
                //判断打款状态
                switch (res.data?.status) {
                    case '4':
                        props.verifyCallBack && props.verifyCallBack(props.item);
                        break;
                    case '1':
                    case '2':
                        new DialogBuilder()
                            .setMsg('银行支付结果未返回，请您稍后再试,如有疑问请您联系,400-088-5566')
                            .setTextAlign('left')
                            .setModel(2)
                            .setLeftTxt('我知道了')
                            .setLeftOnClick((dialog) => {
                                dialog.dismiss();
                                //刷新列表
                                props.callBack && props.callBack();
                            })
                            .setRightOnClick((dialog) => {
                                dialog.dismiss();
                                //联系客服
                                props.serviceCallBack && props.serviceCallBack();
                                //刷新列表
                                props.callBack && props.callBack();
                            })
                            .setRightTxt('联系客服')
                            .show();
                        break;
                    case '3':
                        new DialogBuilder()
                            .setMsg('银行系统返回打款失败信息，请您核对银行卡账户信息后重新绑定！如有疑问请您联系,400-088-5566')
                            .setTextAlign('left')
                            .setModel(2)
                            .setLeftTxt('我知道了')
                            .setLeftOnClick((dialog) => {
                                dialog.dismiss();
                                //刷新列表
                                props.callBack && props.callBack();
                            })
                            .setRightOnClick((dialog) => {
                                dialog.dismiss();
                                //联系客服
                                props.serviceCallBack && props.serviceCallBack();
                                //刷新列表
                                props.callBack && props.callBack();
                            })
                            .setRightTxt('联系客服')
                            .show();
                        break;
                    default:
                        new DialogBuilder().setMsg(res.getMsg()).setTextAlign('left').setModel(1).show();
                        break;
                }
            } else {
                new DialogBuilder()
                    .setMsg(res.getMsg())
                    .setRightTxt('我知道了')
                    .setModel(1)
                    .setRightOnClick((dialog) => {
                        dialog.dismiss();
                    })
                    .show();
            }
        });
    };
    /**
     * 注释：绑定监管view
     * 时间：2025/4/10 19:49
     * @author：宋双朋
     * @returns {React.JSX.Element}
     */
    const renderBindView = () => {
        return (
            <View style={{backgroundColor: '#FFFFFF', padding: 10, flexDirection: 'row', alignItems: 'center'}}>
                <Text
                    style={{
                        fontSize: 13,
                        flex: 1,
                        color: '#FF631F',
                        marginTop: 10,
                    }}>
                    绑定监管账户后才可使用该卡提现
                </Text>
                <UIButton
                    text={'绑定监管账户'}
                    style={{width: 95, height: 28}}
                    borderRadius={5}
                    onPress={() => {
                        queryPassportAccount();
                    }}
                />
            </View>
        );
    };
    /**
     * 注释：验证view
     * 时间：2025/4/10 19:48
     * @author：宋双朋
     * @returns {React.JSX.Element}
     */
    const renderVerifyView = () => {
        return (
            <View style={{backgroundColor: '#FFFFFF', padding: 10, flexDirection: 'row', alignItems: 'center'}}>
                <Text
                    style={{
                        fontSize: 13,
                        flex: 1,
                        color: '#FF631F',
                        marginTop: 10,
                    }}>
                    此卡待认证，请尽快进行收款验证
                </Text>
                <UIButton
                    text={'去验证'}
                    style={{width: 95, height: 28}}
                    borderRadius={5}
                    backgroundColor={'#FF631F'}
                    borderColor={'#FF631F'}
                    onPress={() => {
                        verifyMoney();
                    }}
                />
            </View>
        );
    };
    // 渲染右侧操作按钮
    const renderRightActions = (id: string, swipeAble: Swipeable) => (
        <UIButton
            text={'删除'}
            style={{
                width: 82,
                backgroundColor: '#FB5251',
                height: '100%',
                marginTop: 7,
                borderTopLeftRadius: 0,
                borderBottomLeftRadius: 0,
            }}
            textColor={'#FFFFFF'}
            onPress={() => {
                new DialogBuilder()
                    .setMsg('确定删除该银行卡？')
                    .setModel(1)
                    .setRightOnClick((dialog) => {
                        swipeAble.close();
                        dialog.dismiss();
                        deleteBank();
                    })
                    .show();
            }}
        />
    );
    return (
        <Swipeable
            onSwipeableOpen={(direction, swipeAble) => {
                setRightMargin(0);
                setRightBorderRadius(0);
            }}
            onSwipeableClose={(direction, swipeAble) => {
                setRightMargin(15);
                setRightBorderRadius(5);
            }}
            renderRightActions={(progressAnimatedValue, dragAnimatedValue, swipeable) => renderRightActions(props.item.bankName, swipeable)}
            rightThreshold={40}>
            <View style={[styles.itemContainer, {marginBottom: 0, marginRight: rightMargin}]}>
                <View style={{height: 7, backgroundColor: '#EFF0F3'}} />
                <LinearGradient
                    style={[
                        styles.itemViewContainer,
                        {
                            borderTopRightRadius: rightBorderRadius,
                            borderBottomRightRadius: rightBorderRadius,
                        },
                    ]}
                    colors={['#71A1EE', '#316CCF']}
                    useAngle={true}
                    angle={90}>
                    <View style={{flexDirection: 'row', alignItems: 'center'}}>
                        <UIImage source={getImageUrl() + props.item.logo} style={{width: 40, height: 40}} />
                        <View style={{flexDirection: 'column'}}>
                            <View style={{flexDirection: 'row', alignItems: 'center'}}>
                                <Text style={styles.itemNameText} ellipsizeMode={'tail'} numberOfLines={1}>
                                    {props.item.bankName}
                                </Text>
                                <UIImage
                                    source={bindStateImg(props.item)}
                                    style={{
                                        width: 76,
                                        height: 23,
                                        marginTop: 5,
                                    }}
                                />
                            </View>
                            <Text style={styles.itemTypeText}>{'借记卡'}</Text>
                            <View style={{flexDirection: 'row', alignItems: 'center'}}>
                                <Text style={styles.itemBankNoText}>{formatCardNumberV1(props.item.bankCardNo)} </Text>
                                {TextUtils.equals('2', login?.consignorTypeFlag) && (
                                    <UITouchableOpacity
                                        onPress={() => {
                                            //人脸识别查看卡号
                                            new DialogBuilder()
                                                .setMsg(`当前功能只有实名本人(${login?.memberName})才能使用，请确保本人操作`)
                                                .setRightTxt('马上去人脸识别')
                                                .setRightOnClick((dialog) => {
                                                    dialog.dismiss();
                                                    liveImage();
                                                })
                                                .show();
                                        }}>
                                        <UIImage
                                            source={'wallet_icon_1'}
                                            style={{
                                                width: 16,
                                                height: 16,
                                                marginLeft: 15,
                                            }}
                                        />
                                    </UITouchableOpacity>
                                )}
                            </View>
                        </View>
                    </View>
                </LinearGradient>
                {!TextUtils.equals('1', props.item.status) && renderVerifyView()}
            </View>
        </Swipeable>
    );
}

const styles = StyleSheet.create({
    itemContainer: {
        flex: 1,
        marginLeft: 15,
        marginRight: 15,
    },
    itemViewContainer: {
        padding: 10,
        borderTopLeftRadius: 5,
        borderTopRightRadius: 5,
        borderBottomLeftRadius: 5,
        borderBottomRightRadius: 5,
    },
    itemNameText: {fontSize: 18, fontWeight: 'normal', marginStart: 20, width: 90, color: '#FFFFFF'},
    itemTypeText: {fontSize: 12, fontWeight: 'normal', color: '#FFFFFF', marginStart: 20, marginTop: 5},
    itemBankNoText: {fontSize: 12, fontWeight: 'normal', color: '#FFFFFF', marginStart: 20, marginTop: 5},
    deleteButton: {
        backgroundColor: 'red',
        justifyContent: 'center',
        alignItems: 'center',
        width: 80,
    },
    deleteText: {
        color: 'white',
        fontWeight: 'bold',
    },
});
