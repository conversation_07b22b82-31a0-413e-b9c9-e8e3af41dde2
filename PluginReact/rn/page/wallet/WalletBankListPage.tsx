import {StyleSheet, Text, View} from 'react-native';
import {GestureHandlerRootView} from 'react-native-gesture-handler';
import WalletBankItemView from './views/WalletBankItemView';
import React, {createRef} from 'react';
import WalletBankVerifyDialog from './views/WalletBankVerifyDialog';
import {NativeStackScreenProps} from '@react-navigation/native-stack';
import {ParamListBase} from '@react-navigation/native';
import UITitleView from '../../base/widget/UITitleView';
import {RsqBankList} from './requests/ReqBankList';
import BaseCommPage from '../../base/comm/BasePage';
import UIFlatListView, {UIController, UIFlatListViewRef} from '../../base/widget/UIFlatListView';
import UIButton from '../../base/widget/UIButton';
import UIImage from '../../base/widget/UIImage';
import {ZRouter} from '../../base/comm/ZRouter';
import {getLogin, openLineService} from '../../base/comm/NativeUtils';
import {ArrayUtils} from '../../base/utils/ArrayUtils';
import EventBus from '../../base/utils/EventBus';
import {Constant} from '../../base/Constant';
import {ReqQueryOwnerCardList} from './requests/ReqQueryOwnerCardList';

interface State {
    rightMargin: number;
    showVerifyDialog: boolean;
    item?: RsqBankList;
    showAddBank: boolean;
}

/**
 *  desc: 银行卡列表
 *  user: 宋双朋
 *  time: 2025/4/9 10:04
 */
export default class WalletBankListPage extends BaseCommPage<NativeStackScreenProps<ParamListBase>, State> {
    //列表ref
    uIFlatListViewRef = createRef<UIFlatListViewRef>();

    constructor(props) {
        super(props);
        this.state = {
            rightMargin: 15,
            showVerifyDialog: false,
            showAddBank: false,
        };
    }

    componentDidMount() {
        EventBus.getInstance().addListener(Constant.event_wallet_bank_list, this.refreshListListener);
    }

    componentWillUnmount() {
        EventBus.getInstance().removeListener(this.refreshListListener);
    }

    refreshListListener = (value) => {
        this.uIFlatListViewRef.current?.onRefresh();
    };
    // 加载数据
    onRefreshAndLoadMoreListener = (lv: UIController, index: number) => {
        let request = new ReqQueryOwnerCardList();
        request.request().then((res) => {
            if (res.isSuccess()) {
                lv.setData(res.data?.nowPage ?? 1, res.data?.totalPage ?? 1, res.data?.rootArray ?? []);
                if (ArrayUtils.isNoEmpty(res.data?.rootArray)) {
                    this.setState({showAddBank: true});
                } else {
                    this.setState({showAddBank: false});
                }
            } else {
                lv.onFail();
            }
        });
    };

    renderEmptyView() {
        return (
            <View style={{flex: 1, justifyContent: 'center', alignItems: 'center'}}>
                <UIImage source={'wallet_bank_icon_3'} style={{width: 180, height: 180}} />
                <Text style={{color: '#333333', fontSize: 18, fontWeight: 'bold'}}>您还没有添加银行卡</Text>
                <UIButton
                    text={'添加银行卡'}
                    borderRadius={25}
                    style={{paddingLeft: 25, paddingRight: 25, paddingTop: 5, paddingBottom: 5, marginTop: 15}}
                    onPress={() => {
                        ZRouter.toPage({page: 'WalletBankAddPageV2'});
                    }}
                />
            </View>
        );
    }

    renderAddBankView() {
        return (
            this.state.showAddBank && (
                <View style={{backgroundColor: '#FFFFFF', padding: 10}}>
                    <UIButton
                        text={'添加银行卡'}
                        borderRadius={6}
                        textColor={'#5086FC'}
                        backgroundColor={'#FFFFFF'}
                        onPress={() => {
                            ZRouter.toPage({page: 'WalletBankAddPageV2'});
                        }}
                    />
                </View>
            )
        );
    }

    /**
     * 注释：银行卡view
     * 时间：2025/4/9 10:31
     * @author：宋双朋
     * @param item
     * @param index
     * @returns {JSX.Element}
     */
    renderItemView = (item: RsqBankList, index: number) => {
        return (
            <WalletBankItemView
                item={item}
                callBack={() => {
                    this.uIFlatListViewRef.current?.onRefresh();
                }}
                verifyCallBack={(item: RsqBankList) => {
                    //打款验证
                    this.setState({showVerifyDialog: true, item: item}, () => {});
                }}
                serviceCallBack={() => {
                    //打开在线客服
                    openLineService();
                }}
            />
        );
    };

    render() {
        return (
            <View style={styles.container}>
                <UITitleView title={'银行卡管理'} />
                <GestureHandlerRootView style={{flex: 1}}>
                    <UIFlatListView ref={this.uIFlatListViewRef} renderItemView={this.renderItemView} autoRefresh={true} onRefreshAndLoadMoreListener={this.onRefreshAndLoadMoreListener} emptyComponentView={() => this.renderEmptyView()} />
                </GestureHandlerRootView>
                {this.renderAddBankView()}
                {this.state.showVerifyDialog && (
                    <WalletBankVerifyDialog
                        callBack={() => {
                            this.setState({showVerifyDialog: false});
                        }}
                        closeCallBack={() => {
                            this.setState({showVerifyDialog: false});
                        }}
                        item={this.state.item}
                    />
                )}
            </View>
        );
    }
}

const styles = StyleSheet.create({
    container: {flex: 1, backgroundColor: '#EFF0F3'},
});
