export default [
    {
        title: '交易通首页',
        name: 'WalletScreenPage',
        component: require('../WalletScreenPage').default,
    },
    {
        title: '冻结记录',
        name: 'FreezeRecordPage',
        component: require('../FreezeRecordPage').default,
    },
    {
        title: '订金收入记录',
        name: 'DepositIncomeRecordPage',
        component: require('../DepositIncomeRecordPage').default,
    },
    {
        title: '充值记录',
        name: 'RechargeRecordPage',
        component: require('../RechargeRecordPage').default,
    },
    {
        title: '收支记录',
        name: 'RevenueRecordPage',
        component: require('../RevenueRecordPage').default,
    },
    {
        title: '提现记录',
        name: 'WithdrawalRecordPage',
        component: require('../WithdrawalRecordPage').default,
    },
    {
        title: '设置支付密码第一步',
        name: 'SettingPayPasswordOne',
        component: require('../SettingPayPasswordOne').default,
    },
    {
        title: '设置支付密码第二步',
        name: 'SettingPayPasswordTwo',
        component: require('../SettingPayPasswordTwo').default,
    },
    {
        title: '设置支付密码第三步',
        name: 'SettingPayPasswordThree',
        component: require('../SettingPayPasswordThree').default,
    },
    {
        title: '提现',
        name: 'WithdrawalPage',
        component: require('../WithdrawalPage').default,
    },
    {
        title: '提现成功',
        name: 'WithdrawalSuccessPage',
        component: require('../WithdrawalSuccessPage').default,
    },
    {
        title: '提现失败',
        name: 'WithdrawalFailedPage',
        component: require('../WithdrawalFailedPage').default,
    },
    {
        title: '银行卡管理',
        name: 'WalletBankListPage',
        component: require('../WalletBankListPage').default,
    },
    {
        title: '绑定招商监管户银行卡',
        name: 'WalletBankSupervisionListPage',
        component: require('../WalletBankSupervisionListPage').default,
    },
    {
        title: '添加银行卡-个人',
        name: 'WalletBankAddPageV1',
        component: require('../WalletBankAddPageV1').default,
    },
    {
        title: '添加银行卡-对公',
        name: 'WalletBankAddPageV2',
        component: require('../WalletBankAddPageV2').default,
    },
    {
        title: '银行卡认证成功',
        name: 'WalletBankVerifySuccessPage',
        component: require('../WalletBankVerifySuccessPage').default,
    },
    {
        title: '银行卡认证失败',
        name: 'WalletBankVerifyFailedPage',
        component: require('../WalletBankVerifyFailedPage').default,
    },
    {
        title: '银行卡添加成功',
        name: 'WalletAddBankSuccessPage',
        component: require('../WalletAddBankSuccessPage').default,
    },
    {
        title: '监管账户绑定成功',
        name: 'WalletBankSupervisionSuccessPage',
        component: require('../WalletBankSupervisionSuccessPage').default,
    },
];
