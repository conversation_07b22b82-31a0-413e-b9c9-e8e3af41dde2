// 平台签约运力列表角标(待处理)
// http://wiki.zczy56.com/pages/viewpage.action?pageId=********
import NativeHttpRequest from '../../../base/http/NativeHttpRequest';

export class ApplyOrderlist {
    rootArray: ApplyOrderDetail[];
}

export class ApplyOrderDetail {
    orderNumber: string; //运单号
    id:string;
    buyerName?: string; //受票方
    buyerTaxNumber?: string; //纳税识别号
    buyerAddress?: string; //地址
    buyerMobile?: string; //电话
    buyerBank?: string; //开户行
    buyerBankNumber?: string; //账号
    invoiceMoney?: string; //开票金额
    settleSubsidiaryName?: string; //结算主体
    selfComment?: string; //自定义编号
    orderTitle?: string; //货运信息标题
    despatchPlace?: string; //起始地
    deliverPlace?: string; //目的地
    despatchCompany?: string; //发货单位
    deliverCompany?: string; //收货单位
    cargoName?: string; //货物名称
    plateNumber?: string; //车牌号
    weight?: string; //数量（吨方）
    unit?: string; //单位
    carrierName?: string; //承运方名称
    orderPublishTime?: string; //发单时间
    deliseTime?: string; //成交时间
    startTime?: string; //确认发货时间
    endTime?: string; //确认收货时间
    orderApplyTime?: string; //结算申请时间
    settleTime?: string; //结算时间
    issueRealName?: string; //发单员
    invocieApplyState?: string; //发票申请状态1未申请2已申请
    applyInvoiceTime?: string; //发票申请时间
    invoiceApplyName?: string; //申请人
    invoiceApplyTime?: string; //申请时间
    invoiceOpenState?: string; //开票状态1未开票2已开票
    invoiceType?: string; //发票类型
    invoiceNo?: string; //发票号码
    invoiceCode?: string; //发票代码
    subsidiaryName?: string; //开票方
    // checked?: boolean;
    tax?: string; //税率，没有乘以100
}

export const QueryApplyedOrderlist = (req: {
    nowPage?: number; //页数
    pageSize?: number; //每页行数
    orderNumber?: string; //运单号
    settleTimeS?: string; //结算时间开始
    settleTimeE?: string; //结算时间-结束
    deliverPlace?: string; //目的地
    despatchCompany?: string; //发货单位
    deliverCompany?: string; //收货单位
    plateNumber?: string; //车牌号
}) => {
    return new NativeHttpRequest('ams-app/ams/app/invoiceApplyAppoint/queryApplyedOrderlist').sendRequest<ApplyOrderlist>(req);
};
