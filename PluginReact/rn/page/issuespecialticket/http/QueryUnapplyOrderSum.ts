// 平台签约运力列表角标(待处理)
// http://wiki.zczy56.com/pages/viewpage.action?pageId=78545663
import NativeHttpRequest from '../../../base/http/NativeHttpRequest';

class UnapplyOrderSum {
    idList?: string[];
    totalNum?: string; //运单数
    invoiceMoneyTotal?: string; //含税金额
    weightTotal?: string; //货品数量
}

export const QueryUnapplyOrderSum = (req: {
    orderNumber?: string; //运单号
    settleTimeS?: string; //结算时间开始
    settleTimeE?: string; //结算时间-结束
    deliverPlace?: string; //目的地
    despatchCompany?: string; //发货单位
    deliverCompany?: string; //收货单位
    plateNumber?: string; //车牌号
}) => {
    return new NativeHttpRequest('ams-app/ams/app/invoiceApplyAppoint/queryUnapplyOrderSum').sendRequest<UnapplyOrderSum>(req);
};
