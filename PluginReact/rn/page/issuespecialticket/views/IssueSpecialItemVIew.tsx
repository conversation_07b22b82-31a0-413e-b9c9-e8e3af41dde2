import React, {useRef, useState} from 'react';
import {NativeModules, StyleSheet, Text, TouchableOpacity, View} from 'react-native';
import UIFlatListView, {UIController, UIFlatListViewRef} from '../../../base/widget/UIFlatListView';
import {QueryUnapplyOrderlist} from '../http/QueryUnapplyOrderlist';
import {success} from '../../../base/http/Http';
import UIImage from '../../../base/widget/UIImage';
import UIButton from '../../../base/widget/UIButton';
import {ApplyOrderDetail, QueryApplyedOrderlist} from '../http/QueryApplyOrderList';
import EventBus from '../../../base/utils/EventBus';
import {Constant} from '../../../base/Constant';
import Modal from 'react-native-modal';
import Toast from 'react-native-toast-message';
import {openWebNoTitle} from '../../../base/comm/NativeUtils';
import {QueryUnapplyOrderSum} from '../http/QueryUnapplyOrderSum';
import {AddOrderList} from '../http/AddOrderList';
import {DialogBuilder} from '../../../base/comm/UI';
import {gScreen_width} from '../../../base/Const';

interface Props {
    queryType?: string; //1待申请 2已申请
    handleChange?: Function;
}

// 横线
export function HorizontalLine() {
    return (
        <View
            style={{
                width: gScreen_width,
                height: 1,

                backgroundColor: '#ddd',
            }}
        />
    );
}

// 右箭头
export function RightArrow() {
    return <UIImage source={'icon_arrow_right_black'} style={{width: 10, height: 10}} />;
}

/**
 *  desc: 代开专票
 *  user: 宋双朋
 *  time: 2025/2/18 10:28
 */
export default function IssueSpecialItemView(props: Props) {
    const choosePage = useRef(-1);
    //运单号
    const orderNumber = useRef('');
    //结算时间开始
    const settleTimeS = useRef('');
    //结算时间-结束
    const settleTimeE = useRef('');
    //目的地
    const deliverPlace = useRef('');
    //发货单位;
    const despatchCompany = useRef('');
    //收货单位
    const deliverCompany = useRef('');
    //车牌号
    const plateNumber = useRef('');
    //货品名称
    const cargoName = useRef('');
    const [okModal, setOkModal] = useState(false);

    // 运单数量
    const ticketAmount = useRef(0);
    // 含税金额
    const invoiceMoney = useRef(0);
    // 货品数量
    const goodsAmount = useRef(0);

    let mFlatListViewRef = React.createRef<UIFlatListViewRef>();

    const [unApplyList, setUnApplyList] = useState<ApplyOrderDetail[]>([]);
    // 设置一个idList数组
    const idList = useRef<string[]>([]);
    // idList副本
    const idListCopy = useRef<string[]>([]);
    const allList = useRef<string[]>([]);

    const baseEventBusListener = (data: any) => {
        orderNumber.current = data?.orderId ?? '';
        settleTimeS.current = data?.startTime ? data?.startTime + ' 00:00:00' : '';
        settleTimeE.current = data?.endTime ? data?.endTime + ' 23:59:59' : '';
        deliverPlace.current = data?.deliverPlace ?? '';
        despatchCompany.current = data?.despatchCompany ?? '';
        deliverCompany.current = data?.deliverCompany ?? '';
        plateNumber.current = data?.plateNumber ?? '';
        cargoName.current = data?.cargoName ?? '';
        mFlatListViewRef.current?.onRefresh();
    };

    EventBus.getInstance().addListener(Constant.getIssueSpecialParams, baseEventBusListener);
    EventBus.getInstance().addListener('flashAll', () => {
        mFlatListViewRef.current?.onRefresh();
    });

    function renderItemView(item: ApplyOrderDetail, index: number) {
        return (
            <>
                <View style={{backgroundColor: 'white', padding: 10, marginTop: 5, marginBottom: 5}}>
                    <View style={{flexDirection: 'row', justifyContent: 'space-between', flex: 1, paddingTop: 5}}>
                        <TouchableOpacity
                            onPress={() => {
                                let newMap = [...unApplyList]; // 创建新的数组副本

                                // 这里需要更新运单和钱
                                if (idList.current.includes(item.id)) {
                                    ticketAmount.current -= 1;
                                    invoiceMoney.current -= parseFloat(newMap[index].invoiceMoney ?? '0');
                                    goodsAmount.current -= parseFloat(newMap[index].weight ?? '0');
                                    idList.current = idList.current.filter((id) => id !== item.id);
                                    console.log(idList.current);
                                } else if (!idList.current.includes(item.id)) {
                                    idList.current.push(item.id);
                                    ticketAmount.current += 1;
                                    invoiceMoney.current += parseFloat(newMap[index].invoiceMoney ?? '0');
                                    goodsAmount.current += parseFloat(newMap[index].weight ?? '0');
                                }
                                if (idList.current.length == 0) {
                                    choosePage.current = -1;
                                }
                                setUnApplyList([...newMap]);
                            }}
                            style={{flexDirection: 'row', alignItems: 'center'}}>
                            {props.queryType == '1' && <UIImage source={idList.current.includes(item.id) ? 'http://img.zczy56.com/202502131137341285054.png' : 'http://img.zczy56.com/202502131138398276543.png'} style={{width: 20, height: 20}} />}
                            <Text style={{fontSize: 16, color: '#333333', width: 200}}>{item.orderNumber}</Text>
                        </TouchableOpacity>
                        <TouchableOpacity
                            style={{flexDirection: 'row', alignItems: 'center', justifyContent: 'flex-end'}}
                            onPress={() => {
                                openWebNoTitle(`${NativeModules.OpenNativeModule.getHttpUrl()}/form_h5/h5_inner/index.html?_t=${new Date().getDate()}#/hzDkInvoicingDetails?orderNumber=${item.orderNumber}`);
                            }}>
                            <Text>详情</Text>
                            <RightArrow />
                        </TouchableOpacity>
                    </View>
                    <HorizontalLine />
                    {itemStyle('受票方', item?.buyerName ?? '')}
                    {props.queryType == '2' && itemStyle('申请时间', item?.applyInvoiceTime ? `${item.applyInvoiceTime.slice(0, 10)} ${item.applyInvoiceTime.slice(10)}` : '')}
                    {itemStyle('结算时间', item?.settleTime ? `${item.settleTime.slice(0, 10)} ${item.settleTime.slice(10)}` : '')}
                    <View style={styles.rowStyle}>
                        <Text style={styles.style_5D6476}>申请金额</Text>
                        <Text style={[styles.style_333, {color: 'red'}]}>{item.invoiceMoney ?? ''}</Text>
                    </View>
                    {item.invoiceOpenState == '2' && itemStyle('发票号码', item.invoiceNo ?? '')}
                    {item.invoiceOpenState == '2' && itemStyle('发票代码', item.invoiceCode ?? '')}
                </View>
            </>
        );
    }

    function itemStyle(name: string, value: any) {
        return (
            <View style={styles.rowStyle}>
                <Text style={styles.style_5D6476}>{name}</Text>
                <Text style={styles.style_333}>{value}</Text>
            </View>
        );
    }

    async function onRefreshAndLoadMoreListener(lv: UIController, nowPage: number) {
        console.log(unApplyList, '000000000000000');
        if (props.queryType == '1') {
            QueryUnapplyOrderlist({
                nowPage: nowPage,
                pageSize: 10,
                orderNumber: orderNumber.current,
                settleTimeS: settleTimeS.current,
                settleTimeE: settleTimeE.current,
                deliverPlace: deliverPlace.current,
                despatchCompany: despatchCompany.current,
                deliverCompany: deliverCompany.current,
                plateNumber: plateNumber.current,
                cargoName: cargoName.current,
            }).then((res) => {
                // 修改后的分页加载逻辑
                if (success(res)) {
                    // 新增数据去重逻辑
                    const newData = res.data?.rootArray?.filter((item) => !unApplyList.some((existing) => existing.id === item.id)) ?? [];

                    // 修改选中逻辑
                    if (choosePage.current == 1) {
                        const newIds = newData.map((item) => item.id);
                        idList.current = [...new Set([...idList.current, ...newIds])]; // 去重
                    }

                    setUnApplyList((prev) => [...prev, ...newData]); // 保持数据唯一性
                    lv.setData(Number(res.data?.nowPage ?? nowPage), res.data?.pageSize ?? 10, res.data?.rootArray ?? []);
                } else {
                    Toast.show({
                        type: 'error',
                        text1: '获取数据失败',
                    });
                }
            });
        } else if (props.queryType == '2') {
            QueryApplyedOrderlist({
                nowPage: nowPage,
                pageSize: 10,
                orderNumber: orderNumber.current,
                settleTimeS: settleTimeS.current,
                settleTimeE: settleTimeE.current,
                deliverPlace: deliverPlace.current,
                despatchCompany: despatchCompany.current,
                deliverCompany: deliverCompany.current,
                plateNumber: plateNumber.current,
            }).then((res) => {
                if (success(res)) {
                    lv.setData(Number(res.data?.nowPage ?? nowPage), res.data?.pageSize ?? 10, res.data?.rootArray ?? []);
                } else {
                    Toast.show({
                        type: 'error',
                        text1: '获取数据失败',
                    });
                }
            });
        }
    }

    return (
        <>
            <View style={{flex: 1, backgroundColor: '#F0F3F8'}}>
                <UIFlatListView ref={mFlatListViewRef} autoRefresh={true} extraData={unApplyList} onRefreshAndLoadMoreListener={onRefreshAndLoadMoreListener} renderItemView={(item: any, index) => renderItemView(item, index)} />
                {props.queryType == '1' && (
                    <View>
                        <View
                            style={{
                                backgroundColor: '#FFF2E5',
                                height: 31,
                                alignItems: 'flex-start',
                                justifyContent: 'center',
                                paddingLeft: 10,
                            }}>
                            <View style={{flexDirection: 'row'}}>
                                <Text>已勾选</Text>
                                <Text style={{color: 'red'}}>{ticketAmount.current}</Text>
                                <Text>单,共</Text>
                                <Text style={{color: 'red'}}>{Number(invoiceMoney.current ?? '0').toFixed(2)}</Text>
                                <Text>元</Text>
                            </View>
                        </View>
                        <View style={{flexDirection: 'row', height: 37, backgroundColor: 'white', paddingStart: 15}}>
                            <TouchableOpacity
                                style={{flexDirection: 'row', alignItems: 'center'}}
                                onPress={() => {
                                    idList.current = [];
                                    if (choosePage.current != 0) {
                                        if (idListCopy.current.length > 0) {
                                            idList.current = idListCopy.current;
                                            idListCopy.current = [];
                                        }
                                        // 设置当前所有的数据全部选中
                                        let newMap = unApplyList;
                                        invoiceMoney.current = 0;
                                        ticketAmount.current = 0;
                                        goodsAmount.current = 0;
                                        newMap.map((item) => {
                                            idList.current.push(item.id);
                                            invoiceMoney.current += Number(item.invoiceMoney ?? 0);
                                            ticketAmount.current += 1;
                                            goodsAmount.current += Number(item.weight ?? 0);
                                        });
                                        setUnApplyList([...newMap]);
                                        choosePage.current = 0;
                                    } else {
                                        idListCopy.current = idList.current;
                                        idList.current = [];
                                        setUnApplyList([...unApplyList]);
                                        choosePage.current = -1;
                                        invoiceMoney.current = 0;
                                        ticketAmount.current = 0;
                                        goodsAmount.current = 0;
                                    }
                                }}>
                                <UIImage source={choosePage.current == 0 ? 'http://img.zczy56.com/202502131137341285054.png' : 'http://img.zczy56.com/202502131138398276543.png'} style={{width: 20, height: 20}} />
                                <Text style={{marginRight: 10}}>本页全选</Text>
                            </TouchableOpacity>
                            <TouchableOpacity
                                style={{flexDirection: 'row', alignItems: 'center'}}
                                onPress={() => {
                                    idList.current = [];
                                    if (choosePage.current != 1) {
                                        // 调用全部全选的接口
                                        QueryUnapplyOrderSum({
                                            orderNumber: orderNumber.current,
                                            settleTimeS: settleTimeS.current,
                                            settleTimeE: settleTimeE.current,
                                            deliverPlace: deliverPlace.current,
                                            despatchCompany: despatchCompany.current,
                                            deliverCompany: deliverCompany.current,
                                            plateNumber: plateNumber.current,
                                        }).then((res) => {
                                            if (res.code != '200') {
                                                new DialogBuilder()
                                                    .setModel(1)
                                                    .setMsg(res.msg ?? '')
                                                    .show();
                                                return;
                                            }
                                            if (Number(res?.data?.totalNum ?? 0) > 5000) {
                                                new DialogBuilder().setModel(1).setMsg('最多只能选择5000条数据!').show();
                                                idList.current = []; //清空所有id
                                                ticketAmount.current = 0;
                                                invoiceMoney.current = 0;
                                                goodsAmount.current = 0;
                                                choosePage.current = -1;
                                                return;
                                            }
                                            invoiceMoney.current = res.data?.invoiceMoneyTotal ?? 0;
                                            ticketAmount.current = res.data?.totalNum ?? 0;
                                            goodsAmount.current = res.data?.weightTotal ?? 0;
                                            idList.current = res.data?.idList ?? [];
                                            allList.current = res.data?.idList ?? [];
                                            setUnApplyList([...unApplyList]);
                                        });
                                        choosePage.current = 1;
                                    } else {
                                        idList.current = [];
                                        setUnApplyList([...unApplyList]);
                                        choosePage.current = -1;
                                        invoiceMoney.current = 0;
                                        ticketAmount.current = 0;
                                        goodsAmount.current = 0;
                                    }
                                }}>
                                <UIImage source={choosePage.current == 1 ? 'http://img.zczy56.com/202502131137341285054.png' : 'http://img.zczy56.com/202502131138398276543.png'} style={{width: 20, height: 20}} />
                                <Text>全部全选</Text>
                            </TouchableOpacity>
                        </View>
                        <UIButton
                            style={{backgroundColor: idList.current.length == 0 ? 'gray' : '#487FFF'}}
                            text={'申请开票'}
                            enabled={idList.current.length != 0}
                            onPress={() => {
                                setOkModal(true);
                            }}
                        />
                        {/*@ts-ignore*/}
                        <Modal
                            style={{}}
                            animationIn={'slideInUp'}
                            backdropOpacity={0.3}
                            useNativeDriver={true}
                            isVisible={okModal}
                            onBackButtonPress={() => {
                                setOkModal(false);
                            }}
                            onBackdropPress={() => {
                                setOkModal(false);
                            }}>
                            <View style={{backgroundColor: 'white', borderRadius: 10}}>
                                <Text
                                    style={{
                                        marginTop: 10,
                                        fontSize: 16,
                                        color: '#333',
                                        alignSelf: 'center',
                                    }}>
                                    提示
                                </Text>
                                <View style={{padding: 20, justifyContent: 'flex-start'}}>
                                    <View style={{flexDirection: 'row', justifyContent: 'flex-start'}}>
                                        <Text style={{color: '#333'}}>运单数</Text>
                                        <Text style={{color: '#FF3737', marginLeft: 5}}>{ticketAmount.current}</Text>
                                        <Text style={{color: '#333', marginLeft: 5}}>单</Text>
                                    </View>
                                    <View style={{flexDirection: 'row', justifyContent: 'flex-start'}}>
                                        <Text style={{color: '#333'}}>含税金额</Text>
                                        <Text style={{color: '#FF3737', marginLeft: 5}}>{invoiceMoney.current}</Text>
                                        <Text style={{color: '#333', marginLeft: 5}}>元</Text>
                                    </View>
                                    <View style={{flexDirection: 'row', justifyContent: 'flex-start'}}>
                                        <Text style={{color: '#333'}}>货品数量</Text>
                                        <Text style={{color: '#FF3737', marginLeft: 5}}>{goodsAmount.current}</Text>
                                    </View>
                                    <Text style={{color: '#FF8F29', marginTop: 10}}>请确认是否提交申请?</Text>
                                </View>
                                <View style={{flexDirection: 'row', justifyContent: 'center', alignItems: 'center'}}>
                                    <UIButton
                                        text={'返回'}
                                        onPress={() => {
                                            setOkModal(false);
                                        }}
                                        style={{backgroundColor: 'white', width: '40%'}}
                                        textColor={'#5086FC'}
                                        borderColor={'white'}
                                    />
                                    <UIButton
                                        text={'确定'}
                                        onPress={() => {
                                            setOkModal(false);
                                            //     调用申请接口
                                            AddOrderList({
                                                idList: choosePage.current == 1 ? allList.current : idList.current,
                                            }).then((res) => {
                                                if (success(res)) {
                                                    idList.current = []; //清空所有id
                                                    allList.current = [];
                                                    setUnApplyList([]);
                                                    idListCopy.current = [];
                                                    ticketAmount.current = 0;
                                                    invoiceMoney.current = 0;
                                                    goodsAmount.current = 0;
                                                    choosePage.current = -1;
                                                    let dialog = new DialogBuilder();
                                                    dialog.setModel(1);
                                                    dialog.setMsg('申请成功');
                                                    dialog.setRightOnClick(() => {
                                                        dialog.dismiss && dialog.dismiss();
                                                    });
                                                    props.handleChange && props.handleChange();
                                                    EventBus.getInstance().fireEvent('flashAll', {});
                                                } else {
                                                    new DialogBuilder()
                                                        .setModel(1)
                                                        .setMsg(res?.data?.resultMsg ?? res?.msg ?? '')
                                                        .show();
                                                }
                                            });
                                        }}
                                        style={{backgroundColor: 'white', width: '40%'}}
                                        textColor={'#5086FC'}
                                        borderColor={'white'}
                                    />
                                </View>
                            </View>
                        </Modal>
                    </View>
                )}
            </View>
        </>
    );
}

const styles = StyleSheet.create({
    container: {
        flex: 1,
        backgroundColor: '#F0F3F8',
    },

    stype_EAF4FF: {
        backgroundColor: '#EAF4FF',
        borderRadius: 3,
        paddingLeft: 13,
        paddingRight: 13,
        paddingTop: 4,
        paddingBottom: 4,
        marginRight: 9,
    },
    textNo: {
        // width: 206,
        height: 23,
        color: '#333333',
        fontSize: 16,
    },
    textMoney: {
        // width: 67,
        height: 23,
        color: '#F5740C',
        fontSize: 16,
    },
    rowStyle: {
        flexDirection: 'row',
        marginTop: 10,
        justifyContent: 'flex-start',
    },
    style_5D6476: {
        fontSize: 15,
        color: '#5D6476',
        width: 100,
    },
    style_333: {
        fontSize: 15,
        color: '#333',
        flex: 1,
    },
    style_bold: {
        fontSize: 16,
        color: '#333',
        width: 150,
        fontWeight: 'bold',
    },
    style_bold1: {
        fontSize: 16,
        color: '#333',
        fontWeight: 'bold',
        flex: 1,
    },
    total: {
        backgroundColor: '#fff',
        borderRadius: 5,
        margin: 10,
        paddingLeft: 15,
        paddingTop: 10,
        paddingRight: 15,
        paddingBottom: 15,
        top: 10,
    },
    rectangle: {
        // width: 300,
        // height: 25,
        backgroundColor: '#fff9f5',
        borderRadius: 2,
    },
    style_backReson: {
        // height: 17,
        flex: 1,
        alignItems: 'center',
        color: '#f6653c',
        fontSize: 12,
        flexWrap: 'wrap',
        padding: 1,
    },
});
