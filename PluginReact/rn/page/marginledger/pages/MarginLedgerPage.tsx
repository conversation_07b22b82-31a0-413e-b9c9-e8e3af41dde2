import {Animated, FlatList, ScrollView, StyleSheet, Text, TouchableOpacity, View, Clipboard} from 'react-native';
import React, {useRef} from 'react';
import {NativeStackScreenProps} from '@react-navigation/native-stack';
import {ParamListBase} from '@react-navigation/native';
import BaseCommPage from '../../../base/comm/BasePage';
import UIImage from '../../../base/widget/UIImage';
import UITitleView from '../../../base/widget/UITitleView';
import {RightArrow} from '../../../base/comm/commonuse/utils/CommonItem';
import UIFlatListView, {UIController, UIFlatListViewRef} from '../../../base/widget/UIFlatListView';
import {gScreen_height, gScreen_width} from '../../../base/Const';
import Modal from 'react-native-modal';
import {ZRouter} from '../../../base/comm/ZRouter';
import SelectModalView from '../views/SelectModalView';
import RenderContractListView from '../views/RenderContractListView';
import {BondBookDetail, QueryBondBooks} from '../http/QueryBondBooks';
import Toast from 'react-native-toast-message';
import {GruarnteeList, QueryGuaranteeOrderManageApp} from '../http/GuaranteeOrderManageApp';
import {getLogin} from '../../../base/comm/NativeUtils';
import {ToastBuilder, WaitDialogBuilder} from '../../../base/comm/UI';
import UIButton from '../../../base/widget/UIButton';
import LinearGradient from 'react-native-linear-gradient';
import {queryBondBookCount} from '../http/ReqQueryBondBookCount';
import {msg, success} from '../../../base/http/Http';
import {queryDeletedBondAccountBookList} from '../http/ReqQueryDeletedBondAccountBookList';
import TextUtils from '../../../base/utils/TextUtils';

interface State {
    showModal: boolean; //切换主体
    //     切换筛选
    chooseFilter: boolean;
    states: string;
    orderNo: string;
    userId: string;
    customerId: string;
    bookNo: string; //账本编号
    bookName: string; //账本名称
    subsidiaryId: string; //签约平台id
    signingPlatformName: string; //签约平台名称
    subsidiaryShortName?: string; //所属平台简称
    funMode: string;
    depositMoney?: string;
    freezeMoney?: string;
    fundMode?: string;
    //     初始化
    init: boolean;
    //列表数据判断页面
    isHaveData: boolean;
    //展示已注销入口
    isHaveDelete: boolean;
}

export default class MarginLedgerPage extends BaseCommPage<NativeStackScreenProps<ParamListBase>, State> {
    mFlatListViewRef = React.createRef<UIFlatListViewRef>();
    scrollY = new Animated.Value(0);

    constructor(props) {
        super(props);
        this.state = {
            funMode: '',
            subsidiaryId: '',
            showModal: false,
            chooseFilter: false,
            depositMoney: '0',
            freezeMoney: '0',
            states: '',
            orderNo: '',
            userId: getLogin().userId,
            customerId: getLogin().customerId,
            bookNo: '',
            bookName: '',
            signingPlatformName: '',
            subsidiaryShortName: '',
            init: false,
            isHaveData: false,
            isHaveDelete: false,
        };
    }

    componentDidMount() {
        let wait = new WaitDialogBuilder().show();
        QueryBondBooks({fundMode: 'C04'}).then((res) => {
            if (res.data?.rootArray != null) {
                let list = res.data?.rootArray ?? [];
                let da: BondBookDetail = list[0];
                this.setState({
                    customerId: da?.customerId ?? '',
                    subsidiaryId: da?.subsidiaryId ?? '',
                    depositMoney: da?.depositMoney ?? '0',
                    freezeMoney: da?.freezeMoney ?? '0',
                    bookNo: da?.bookNo ?? '',
                    bookName: da?.bookName ?? '',
                    signingPlatformName: da?.subsidiaryName ?? '',
                    subsidiaryShortName: da?.subsidiaryShortName ?? '',
                    init: true,
                });
                queryBondBookCount().then((res) => {
                    wait?.dismiss();
                    if (success(res)) {
                        this.setState({isHaveDelete: (res.data ? res.data?.deletedNum ?? 0 : 0) > 0, isHaveData: (res.data ? res.data?.usefulNum ?? 0 : 0) > 0});
                    } else {
                        new ToastBuilder().setMsg(msg(res)).show();
                    }
                });
            } else {
                wait?.dismiss();
                Toast.show({
                    type: 'error',
                    text1: '获取账户失败',
                });
            }
        });
    }

    toLedgerPage = () => {
        ZRouter.toPage({
            page: 'LedgerWrittenPage',
        });
    };

    renderItemView = (item: GruarnteeList) => {
        return (
            <>
                <TouchableOpacity style={styles.style_flashContainer}>
                    <View style={{flexDirection: 'row', justifyContent: 'space-between', margin: 10}}>
                        <Text style={{color: '#666', fontSize: 14}}>保证金订单号</Text>
                        <Text style={{fontSize: 14, color: 'black'}}>{item.orderNo ?? ''}</Text>
                        <Text
                            style={styles.copyGray}
                            onPress={async () => {
                                try {
                                    await Clipboard.setString(item.orderNo ?? '');
                                    Toast.show({
                                        type: 'success',
                                        text1: '复制成功',
                                    });
                                } catch (err) {
                                    Toast.show({
                                        type: 'error',
                                        text1: '复制失败',
                                    });
                                }
                            }}>
                            复制
                        </Text>
                    </View>
                    {this.baseItem('订单状态', item.stateStr ?? '')}
                    {this.baseItem('收取金额', item.collectionMoney ?? '')}
                    {this.baseItem('收取方式', item.collectionWayStr ?? '')}
                    {this.baseItem('保证金分类', item.guaranteeName ?? '')}
                    {this.baseItem('收取时间', item.collectionTime ?? '')}
                </TouchableOpacity>
            </>
        );
    };

    baseItem(key?: string, value?: any) {
        let col = '';
        if (value == '收款中' || value == '收款失败') {
            col = '#FB6B40';
        } else if (value == '收款成功') {
            col = '#05AA46';
        }

        return (
            <View style={{flexDirection: 'row', justifyContent: 'space-between', margin: 10}}>
                <Text style={{color: '#666', fontSize: 14}}>{key}</Text>
                <Text style={{fontSize: 14, color: col == '' ? 'black' : col}}>{value ?? ''}</Text>
            </View>
        );
    }

    onLoadMore = (lv: UIController, nowPage: number) => {
        //     查询签约主体列表
        QueryGuaranteeOrderManageApp({
            userId: this.state.userId,
            customerId: this.state.customerId,
            signingPlatform: this.state.subsidiaryId,
            signingPlatformName: this.state.signingPlatformName,
            guaranteeCode: '001', //保证金分类代码：001-油气保证金传枚举
            guaranteeName: '油气保证金', //保证金分类名称：001-油气保证金
            states: this.state.states, //状态：1-收款中、2-收款失败、3-收款成功4-扣款中5-扣款失败6-扣款成功7-退款中8-退款失败9-退款成功多个状态英文逗号分隔符穿
            orderNo: this.state.orderNo, //保证金订单号
            pageNum: nowPage ?? 1, //当前页码
            pageSize: 10, //每页条数
        }).then((rsp) => {
            if (rsp?.data?.rootArray != null) {
                lv.setData(Number(rsp.data?.nowPage ?? 1), rsp.data?.pageSize ?? 10, rsp.data?.rootArray ?? []);
            } else {
                lv.onFail();
            }
        });
    };

    // 头部组件
    renderTopView() {
        return (
            <>
                <UITitleView
                    title={'保证金账本'}
                    style={{backgroundColor: '#5086FC', zIndex: 101}}
                    rightView={
                        this.state.isHaveDelete ? (
                            <Text
                                style={{
                                    fontSize: 14,
                                    textAlign: 'right',
                                    marginRight: 15,
                                    color: '#333',
                                }}>
                                已注销账本查询
                            </Text>
                        ) : undefined
                    }
                    clickRight={() => {
                        this.state.isHaveDelete && this.toLedgerPage();
                    }}
                />
                <Animated.View
                    style={{
                        opacity: this.headerOpacity,
                        position: 'absolute',
                        width: '100%',
                        top: 0,
                        zIndex: 100,
                    }}>
                    <UIImage source={'http://img.zczy56.com/202502121407359752762.png'} style={{width: gScreen_width, height: 300}} />
                    <View
                        style={{
                            justifyContent: 'space-between',
                            flex: 1,
                            flexDirection: 'row',
                            left: 16,
                            position: 'absolute',
                            top: 70,
                            height: 27,
                        }}>
                        <Text
                            style={{
                                backgroundColor: '#F1AB06',
                                width: 50,
                                height: 16,
                                borderRadius: 2,
                                textAlign: 'center',
                                fontSize: 11,
                                color: 'white',
                            }}>
                            签约主体
                        </Text>
                        <ScrollView style={{width: 150}} horizontal={true}>
                            <Text
                                style={{
                                    textAlign: 'center',
                                    fontSize: 14,
                                    color: 'white',
                                    overflow: 'scroll',
                                }}>
                                {this.state.subsidiaryShortName ?? ''}
                            </Text>
                        </ScrollView>
                        <TouchableOpacity
                            style={{
                                backgroundColor: '#B5CBFB',
                                width: 90,
                                left: gScreen_width - 307,
                                borderBottomLeftRadius: 10,
                                borderTopLeftRadius: 10,
                                flexDirection: 'row',
                                justifyContent: 'center',
                                alignItems: 'center',
                            }}
                            onPress={() => {
                                this.setState({showModal: true});
                            }}>
                            <UIImage source={'http://img.zczy56.com/202502121500091805033.png'} style={{width: 15, height: 15, right: 5}} />
                            <Text style={{textAlign: 'center', fontSize: 14, color: 'white'}}>切换</Text>
                        </TouchableOpacity>
                    </View>
                    <View
                        style={{
                            position: 'absolute',
                            top: 140,
                            alignSelf: 'center',
                        }}>
                        <Text style={{textAlign: 'center', fontSize: 14, color: '#333'}}>可用金额(元)</Text>
                        <Text
                            style={{
                                textAlign: 'center',
                                fontSize: 27,
                                color: '#333',
                            }}>
                            {this.state.depositMoney}
                        </Text>
                        <TouchableOpacity
                            style={{
                                flex: 1,
                                flexDirection: 'row',
                                alignItems: 'center',
                                justifyContent: 'center',
                                top: 6,
                            }}
                            onPress={() => {
                                ZRouter.toPage({
                                    page: 'MarginLedgerManagementListPage',
                                    params: {
                                        subsidiaryShortName: this.state.subsidiaryShortName,
                                        depositMoney: this.state.depositMoney,
                                        freezeMoney: this.state.freezeMoney,
                                        customerId: this.state.customerId,
                                        bookNo: this.state.bookNo,
                                        bookName: this.state.bookName,
                                        subsidiaryName: this.state.signingPlatformName,
                                        fundMode: this.state.fundMode,
                                    },
                                });
                            }}>
                            <Text style={{textAlign: 'center', fontSize: 14, color: '#333'}}>查看收支明细</Text>
                            {RightArrow()}
                        </TouchableOpacity>
                    </View>
                    <View
                        style={{
                            position: 'absolute',
                            top: 250,
                            left: 20,
                            width: '85%',
                            flexDirection: 'row',
                            justifyContent: 'space-between',
                            alignSelf: 'center',
                            alignItems: 'center',
                        }}>
                        <Text
                            style={{
                                textAlign: 'center',
                                fontSize: 14,
                                color: '#333',
                            }}>
                            冻结金额(元):{this.state.freezeMoney ?? '0'}
                        </Text>
                        {/*<TouchableOpacity style={{flexDirection: 'row', alignItems: 'center'}}>*/}
                        {/*    <Text style={{fontSize: 14, color: '#333'}}>查看</Text>*/}
                        {/*    {RightArrow()}*/}
                        {/*</TouchableOpacity>*/}
                    </View>
                </Animated.View>
            </>
        );
    }

    //吸顶组件
    renderScrollTabs() {
        return (
            <Animated.View
                style={{
                    transform: [{translateY: this.stickyTranslateY}],
                    backgroundColor: 'white',
                    position: 'absolute',
                    opacity: 1,
                    top: 300,
                    zIndex: 100,
                }}>
                <View style={{flexDirection: 'row'}}>
                    {/*横向*/}
                    <LinearGradient colors={['#FFFFFF', '#E5EDFF']} style={{height: 40, alignItems: 'center', position: 'absolute', width: 188}} useAngle={true} angle={270} />
                    <ScrollView
                        horizontal={true}
                        showsHorizontalScrollIndicator={false}
                        style={{
                            padding: 10,
                            width: gScreen_width - 80,
                        }}>
                        {['汽油保证金'].map((item, index) => {
                            return (
                                <TouchableOpacity style={{flexDirection: 'row', alignItems: 'center'}}>
                                    <UIImage source={'icon_collateral_type'} style={{height: 15, width: 15, marginRight: 4}} />
                                    <Text>{item}</Text>
                                </TouchableOpacity>
                            );
                        })}
                    </ScrollView>
                    <TouchableOpacity
                        style={{
                            flexDirection: 'row',
                            justifyContent: 'center',
                            alignContent: 'center',
                            alignItems: 'center',
                            backgroundColor: 'white',
                            width: 70,
                        }}
                        onPress={() => {
                            this.setState({chooseFilter: true});
                        }}>
                        <Text style={{fontSize: 14, color: '#333'}}>筛选</Text>
                        <UIImage source={'http://img.zczy56.com/202502121603016783560.png'} style={{width: 15, height: 15}} />
                    </TouchableOpacity>
                </View>
            </Animated.View>
        );
    }

    // 列表
    renderMList() {
        return (
            <View style={{height: gScreen_height - 50, paddingTop: 50}}>
                <UIFlatListView
                    contentContainerStyle={{paddingTop: 250}}
                    ref={this.mFlatListViewRef}
                    onRefreshAndLoadMoreListener={this.onLoadMore}
                    renderItemView={this.renderItemView}
                    autoRefresh={true}
                    onScroll={Animated.event([{nativeEvent: {contentOffset: {y: this.scrollY}}}], {useNativeDriver: false})}
                />
            </View>
        );
    }

    renderEmptyView() {
        return (
            <View style={{flex: 1}}>
                <UITitleView
                    title={'保证金账本'}
                    rightView={
                        this.state.isHaveDelete ? (
                            <Text
                                style={{
                                    fontSize: 14,
                                    textAlign: 'right',
                                    marginRight: 15,
                                    color: '#333',
                                }}>
                                已注销账本查询
                            </Text>
                        ) : undefined
                    }
                    clickRight={() => {
                        this.state.isHaveDelete && this.toLedgerPage();
                    }}
                />
                <View style={{flex: 1, alignItems: 'center'}}>
                    <UIImage source={'base_list_empty_ic_def'} style={{width: 180, height: 180, marginTop: 104}} />
                    <Text
                        style={{
                            fontSize: 18,
                            color: '#333',
                            fontWeight: 'bold',
                            marginTop: 13,
                        }}>
                        当前无生效保证金账本
                    </Text>
                    <View style={{flexDirection: 'row', alignItems: 'center', marginTop: 5}}>
                        {this.state.isHaveDelete && (
                            <Text
                                style={{
                                    fontSize: 14,
                                    color: '#999',
                                }}>
                                如需查看已注销账本数据，请点击已注销账本查询
                            </Text>
                        )}
                    </View>
                    {this.state.isHaveDelete && (
                        <View style={{flexDirection: 'row', alignItems: 'center', marginTop: 35}}>
                            <UIButton
                                text={'已注销账本查询'}
                                width={152.5}
                                height={45}
                                borderRadius={25}
                                fontSize={17}
                                onPress={() => {
                                    this.toLedgerPage();
                                }}
                            />
                        </View>
                    )}
                </View>
            </View>
        );
    }

    headerOpacity = this.scrollY.interpolate({
        inputRange: [0, 100],
        outputRange: [1, 0],
        extrapolate: 'clamp',
    });

    stickyTranslateY = this.scrollY.interpolate({
        inputRange: [0, 0, 350],
        outputRange: [0, 0, -255], // 保持吸顶位置
        extrapolate: 'clamp',
    });

    renderModal() {
        return (
            <View style={styles.container}>
                {/*@ts-ignore*/}
                <Modal
                    style={styles.body}
                    animationIn={'slideInUp'}
                    backdropOpacity={0.3}
                    useNativeDriver={true}
                    isVisible={this.state.showModal}
                    onBackButtonPress={() => {
                        this.setState({showModal: false});
                    }}
                    onBackdropPress={() => {
                        this.setState({showModal: false});
                    }}>
                    <View style={[styles.content]}>
                        <View style={{flexDirection: 'row', padding: 20, justifyContent: 'space-between'}}>
                            <TouchableOpacity
                                onPress={() => {
                                    this.setState({showModal: false});
                                }}>
                                <UIImage source={'http://img.zczy56.com/202502121709027842632.png'} style={{width: 20, height: 20}} />
                            </TouchableOpacity>
                            <Text style={{marginLeft: 10, fontSize: 16, color: '#333'}}>请选择签约主体</Text>
                            <Text
                                style={{marginLeft: 10, fontSize: 14, color: '#5086FC'}}
                                onPress={() => {
                                    this.setState({showModal: false});
                                    this.mFlatListViewRef.current?.onRefresh();
                                }}>
                                确定
                            </Text>
                        </View>
                        {/*签约主体列表*/}
                        <RenderContractListView
                            bookNo={this.state.bookNo}
                            callBack={(data) => {
                                this.setState({
                                    subsidiaryShortName: data?.subsidiaryShortName,
                                    depositMoney: data?.depositMoney,
                                    freezeMoney: data?.freezeMoney,
                                    customerId: data?.customerId,
                                    bookNo: data?.bookNo ?? '', //账本编号
                                    bookName: data?.bookName ?? '', //账本名称
                                    signingPlatformName: data?.subsidiaryName ?? '', //所属平台名称
                                    fundMode: data?.fundMode,
                                    subsidiaryId: data?.subsidiaryId,
                                });
                                console.log(data);

                                // 刷新列表
                                this.mFlatListViewRef.current?.onRefresh();
                            }}
                        />
                    </View>
                </Modal>
            </View>
        );
    }

    // 筛选
    renderFilter() {
        return (
            <>
                <SelectModalView
                    visible={this.state.chooseFilter}
                    callback={(data) => {
                        if (data != null)
                            this.setState({
                                orderNo: data?.orderNum ?? '',
                                states: (data?.chooseSaleStatus ?? []).join(','),
                            });
                        this.setState({chooseFilter: false});
                        this.mFlatListViewRef.current?.onRefresh();
                    }}
                />
            </>
        );
    }

    render() {
        return this.state.init ? (
            this.state.isHaveData ? (
                <>
                    {/*  顶上模块*/}
                    {this.renderTopView()}
                    {/*    滑动条*/}
                    {this.renderScrollTabs()}
                    {/*    列表*/}
                    {this.renderMList()}
                    {/*切换主体*/}
                    {this.renderModal()}
                    {/*    筛选*/}
                    {this.renderFilter()}
                </>
            ) : (
                this.renderEmptyView()
            )
        ) : (
            <></>
        );
    }
}

const styles = StyleSheet.create({
    style_flashContainer: {
        backgroundColor: '#fff',
        borderRadius: 5,
        margin: 10,
        padding: 10,
        justifyContent: 'space-between',
    },
    body: {justifyContent: 'flex-end', padding: 0, margin: 0},
    content: {backgroundColor: '#fff', minHeight: 200},

    style_backend: {
        flex: 1,
        flexDirection: 'row',
        marginTop: 9,
        alignItems: 'center',
        justifyContent: 'flex-end',
    },
    style_touchOp: {width: 85, height: 30, backgroundColor: '#5086fc', borderRadius: 5, right: 20},
    style_textFFF: {color: '#fff', textAlign: 'center', lineHeight: 30},
    style_flashContainerV1: {
        backgroundColor: '#F6F7F9',
        borderRadius: 5,
        margin: 10,
        padding: 20,
        justifyContent: 'space-between',
    },
    container: {
        flex: 1,
        position: 'relative',
    },
    copyGray: {
        fontSize: 10,
        color: '#5086FC',
        paddingLeft: 6,
        paddingRight: 6,
        paddingTop: 1,
        paddingBottom: 1,
        borderWidth: 0.5,
        borderRadius: 2,
        borderColor: '#D8D8D8',
        marginLeft: 8,
    },
});
