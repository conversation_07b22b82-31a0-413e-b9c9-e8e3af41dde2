// 定义每个Tab的内容
import {NativeStackScreenProps} from '@react-navigation/native-stack';
import {ParamListBase} from '@react-navigation/native';
import {TouchableOpacity, View, Text, StyleSheet} from 'react-native';
import {SceneMap, TabBar, TabView} from 'react-native-tab-view';
import UITitleView from '../../../base/widget/UITitleView';
import ToDomainPage from './tabs/ToDomainPage';
import DomainingPage from './tabs/DomainingPage';
import DomainPage from './tabs/DomainPage';
import BaseCommPage from '../../../base/comm/BasePage';
import UIImage from '../../../base/widget/UIImage';
import {ZRouter} from '../../../base/comm/ZRouter';
import {HorizontalLine} from '../../../base/comm/commonuse/utils/CommonItem';
import {gScreen_width} from '../../../base/Const';
import {OrderDetail, QueryOrderListMargin} from '../http/QueryOrderDetail';

interface State {
    data: OrderDetail;
}

export default class MarginOrderListPageDetailPage extends BaseCommPage<NativeStackScreenProps<ParamListBase>, State> {
    ordId: string;
    ordType: string;
    funMode: string;
    constructor(props) {
        super(props);
        console.log(ZRouter.getParams(props));

        this.ordId = ZRouter.getParams(props).item.ordId;
        this.ordType = ZRouter.getParams(props).item.ordType;
        this.funMode = ZRouter.getParams(props).fundMode;
        this.state = {
            data: new OrderDetail(),
        };
    }

    /**
     * 注释: 样式组件
     * 时间: 2024/7/10 14:24
     * <AUTHOR>
     */
    itemStyle(name: string, value: any) {
        return (
            <View style={styles.rowStyle}>
                <Text style={styles.style_5D6476}>{name}</Text>
                <Text style={styles.style_333}>{value}</Text>
            </View>
        );
    }

    componentDidMount() {
        //     查询保证金明细
        QueryOrderListMargin({
            ordId: this.ordId, //订单号
            ordType: this.ordType, //收支类型
            fundMode: this.funMode ?? 'C04', //账本类型C01 一般户 C02 专户
        }).then((res) => {
            if (res.data != null) {
                this.setState({
                    data: res.data,
                });
            } else {
            }
        });
    }

    render() {
        return (
            <View style={{backgroundColor: '#fff'}}>
                <UITitleView
                    title={'保证金账本收支详情'}
                    clickBack={() => {
                        ZRouter.goBack();
                    }}
                />
                <HorizontalLine />
                <View style={{alignItems: 'center', justifyContent: 'center', height: 150, width: gScreen_width}}>
                    <Text style={{fontSize: 16}}>金额</Text>
                    <Text style={{fontSize: 20, color: '#333'}}>{this.state?.data?.money ?? ''}</Text>
                </View>
                <HorizontalLine />
                <View>
                    {this.itemStyle('交易流水号', this.state?.data?.tradeImei ?? '')}
                    {this.itemStyle('交易类型', this.state?.data?.operateRemark ?? '')}
                    {this.itemStyle('保证金订单号', this.state?.data?.bizOrderNo ?? '')}
                    {this.itemStyle('摘要', this.state?.data?.abstractRemark ?? '')}
                    {this.itemStyle('交易后账户余额', this.state?.data?.sumMoney ?? '')}
                    {this.itemStyle('操作时间', this.state?.data?.createTime ?? '')}
                </View>
                <HorizontalLine />
                {this.itemStyle('备注', this.state?.data?.remark ?? '')}
                <View style={{paddingBottom: 20}} />
            </View>
        );
    }
}
const styles = StyleSheet.create({
    rowStyle: {
        flexDirection: 'row',
        marginTop: 10,
        paddingLeft: 10,
        paddingRight: 10,
        justifyContent: 'space-between',
        alignItems: 'center',
    },
    style_5D6476: {
        fontSize: 14,
        color: '#5D6476',
    },
    style_333: {
        fontSize: 14,
        color: '#333',
    },
});
