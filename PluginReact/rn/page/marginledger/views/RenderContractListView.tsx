import {StyleSheet, Text, TouchableOpacity, View} from 'react-native';
import UIImage from '../../../base/widget/UIImage';
import React, {useRef} from 'react';
import {gScreen_height} from '../../../base/Const';
import UIFlatListView, {UIFlatListViewRef} from '../../../base/widget/UIFlatListView';
import {BondBookDetail, QueryBondBooks} from '../http/QueryBondBooks';

export interface Props {
    callBack: Function;
    bookNo: string; //已经选中的id
}

export default function RenderContractListView(props: Props) {
    let mFlatListViewRefV1 = useRef<UIFlatListViewRef>(null);

    function onLoadMoreV1(lv: any, nowPage: number) {
        QueryBondBooks({
            fundMode: 'C04',
        }).then((rsp) => {
            if (rsp.data?.rootArray != null) {
                let list = rsp.data?.rootArray ?? [];
                lv.setData(1, 10, list);
            } else {
                lv.onFail();
            }
        });
    }

    function RenderItemViewV1(item: BondBookDetail, index: number) {
        return (
            <>
                <TouchableOpacity
                    style={[
                        styles.style_flashContainerV1,
                        {
                            backgroundColor: props.bookNo === item?.bookNo ? '#F2F8FF' : '#F6F7F9',
                            borderColor: props.bookNo === item?.bookNo ? '#5086FC' : '#F6F7F9',
                            borderWidth: 1,
                        },
                    ]}
                    onPress={() => {
                        // setChooseSign(index);
                        props?.callBack &&
                            props.callBack({
                                //     可用金额
                                depositMoney: item?.depositMoney ?? '0',
                                //     公司名称
                                subsidiaryShortName: item?.subsidiaryShortName ?? '',
                                //     冻结金额
                                freezeMoney: item?.freezeMoney ?? '0',
                                customerId: item?.customerId ?? '',
                                bookNo: item.bookNo ?? '', //账本编号
                                bookName: item.bookName, //账本名称
                                subsidiaryName: item.subsidiaryName, //所属平台名称
                                fundMode: item?.fundMode ?? '',
                                subsidiaryId: item?.subsidiaryId,
                            });
                        mFlatListViewRefV1.current?.onRefresh();
                    }}>
                    <View style={{flexDirection: 'row', justifyContent: 'space-between'}}>
                        <View style={{flexDirection: 'row'}}>
                            <UIImage source={'http://img.zczy56.com/202502131127559154633.png'} style={{width: 20, height: 20}}></UIImage>
                            <Text
                                style={{
                                    paddingLeft: 5,
                                    textAlign: 'center',
                                    fontSize: 14,
                                    color: 'black',
                                    fontWeight: 'bold',
                                }}>
                                {item?.subsidiaryShortName ?? ''}
                            </Text>
                        </View>

                        <UIImage source={props.bookNo == item.bookNo ? 'http://img.zczy56.com/202502131137341285054.png' : 'http://img.zczy56.com/202502131138398276543.png'} style={{width: 20, height: 20}}></UIImage>
                    </View>
                    <Text
                        style={{
                            fontSize: 13,
                            marginTop: 5,
                        }}>
                        {item?.subsidiaryName ?? ''}
                    </Text>
                    <View style={{flexDirection: 'row', justifyContent: 'space-between', marginTop: 5}}>
                        <Text>可用余额{item?.depositMoney ?? ''}</Text>
                        <Text>冻结金额{item?.freezeMoney ?? ''}</Text>
                    </View>
                </TouchableOpacity>
            </>
        );
    }

    return (
        <>
            <View style={{height: gScreen_height / 2}}>
                <UIFlatListView ref={mFlatListViewRefV1} onRefreshAndLoadMoreListener={onLoadMoreV1} renderItemView={RenderItemViewV1} autoRefresh={true} />
            </View>
        </>
    );
}

const styles = StyleSheet.create({
    style_flashContainer: {
        backgroundColor: '#fff',
        borderRadius: 5,
        margin: 10,
        padding: 10,
        justifyContent: 'space-between',
    },
    body: {justifyContent: 'flex-end', padding: 0, margin: 0},
    content: {backgroundColor: '#fff', minHeight: 200},

    style_backend: {
        flex: 1,
        flexDirection: 'row',
        marginTop: 9,
        alignItems: 'center',
        justifyContent: 'flex-end',
    },
    style_touchOp: {width: 85, height: 30, backgroundColor: '#5086fc', borderRadius: 5, right: 20},
    style_textFFF: {color: '#fff', textAlign: 'center', lineHeight: 30},
    style_flashContainerV1: {
        backgroundColor: '#F6F7F9',
        borderRadius: 5,
        margin: 10,
        padding: 20,
        justifyContent: 'space-between',
    },
    container: {
        flex: 1,
        position: 'relative',
    },
});
