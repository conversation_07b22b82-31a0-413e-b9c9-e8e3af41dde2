import React, { useEffect, useRef, useState } from 'react';
import { ScrollView, StyleSheet, Text, TextInput, TouchableOpacity, View } from 'react-native';
import UIButton from '../../../base/widget/UIButton';
import UIImage from '../../../base/widget/UIImage';
import { SalesStatusMap } from '../utils/SelectModalState';
import { gScreen_width } from '../../../base/Const';

// 弹框
interface Props {
    visible: boolean;
    callback: Function;
}

export default function SelectModalView(props: Props) {
    const [open, setOpen] = useState(props.visible);
    const [chooseSaleStatus, setChooseSaleStatus] = useState<string[]>([]);
    const [orderNum, setOrderNum] = useState('');
    useEffect(() => {
        setOpen(props.visible);
    }, [props.visible]);

    return open ? (
        <View
            style={{
                height: '100%',
                width: '100%',
                backgroundColor: 'rgba(0,0,0,0.5)',
                zIndex: 100,
                position: 'absolute',
                top: 50,
            }}>
            <View style={{}}>
                <View style={{ flexDirection: 'row' }}>
                    {/*横向*/}
                    <ScrollView
                        horizontal={true}
                        showsHorizontalScrollIndicator={false}
                        style={{
                            padding: 10,
                            width: gScreen_width - 80,
                            backgroundColor: 'white',
                        }}>
                        {['汽油保证金'].map((item, index) => {
                            return (
                                <>
                                    <TouchableOpacity>
                                        <Text
                                            style={{
                                                borderBottomWidth: 4,
                                                borderBottomColor: '#5086FC',
                                                marginRight: 10,
                                                borderStyle: 'solid',
                                            }}>
                                            {item}
                                        </Text>
                                    </TouchableOpacity>
                                </>
                            );
                        })}
                    </ScrollView>
                    <TouchableOpacity
                        style={{
                            flexDirection: 'row',
                            justifyContent: 'center',
                            alignContent: 'center',
                            alignItems: 'center',
                            backgroundColor: 'white',
                            width: 70,
                            // height: 40
                        }}
                        onPress={() => {
                            this.setState({ chooseFilter: true });
                        }}>
                        <Text style={{ fontSize: 14, color: '#333' }}>筛选</Text>
                        <UIImage source={'http://img.zczy56.com/202502121603016783560.png'} style={{ width: 15, height: 15 }} />
                    </TouchableOpacity>
                </View>
                {/*诚意金状态*/}
                {renderSaleStatusView()}
                {/*保证金订单单号*/}
                {renderOrderNoView()}
                {/*底部按钮*/}
                <View
                    style={{
                        flexDirection: 'row',
                        justifyContent: 'space-between',
                        alignItems: 'center',
                        paddingLeft: 19,
                        paddingRight: 19,
                        height: 64,
                        backgroundColor: '#fff',
                    }}>
                    <UIButton
                        text={'取消'}
                        style={{ width: '45%', backgroundColor: 'white' }}
                        fontSize={16}
                        textColor={'#2374E9'}
                        borderColor={'#2374E9'}
                        onPress={() => {
                            setOpen(false);
                            props.callback && props.callback();
                        }}
                    />
                    <UIButton
                        text={'确定'}
                        textColor={'#fff'}
                        style={{ width: '45%', backgroundColor: '#5086FC' }}
                        borderColor={'#fff'}
                        fontSize={16}
                        onPress={() => {
                            setOpen(false);
                            props.callback &&
                                props.callback({
                                    chooseSaleStatus: chooseSaleStatus,
                                    orderNum: orderNum,
                                });
                            //  选项清除
                            chooseSaleStatus.length && setChooseSaleStatus([]);
                            orderNum && setOrderNum('');
                        }}
                    />
                </View>
            </View>
        </View>
    ) : (
        <></>
    );

    //     深色框框
    function renderTextContainerItem(text: string, isChoose: boolean = false) {
        return (
            <View
                style={[
                    styles.blackContent,
                    {
                        borderColor: isChoose ? '#3171FD' : '#f6f7fa',
                        borderWidth: isChoose ? 1 : 0,
                    },
                ]}>
                <Text style={{ fontSize: 16, color: isChoose ? '#3171FD' : '#333' }}>{text}</Text>
            </View>
        );
    }

    //      诚意金状态
    function renderSaleStatusView() {
        return (
            <View
                style={{
                    backgroundColor: '#FFFFFF',
                }}>
                <View
                    style={{
                        width: '90%',
                        height: 1,
                        backgroundColor: '#ddd',
                        marginTop: 10,
                        alignSelf: 'center',
                    }}
                />
                <Text
                    style={{
                        fontSize: 16,
                        color: '#333333',
                        marginLeft: 10,
                        marginTop: 10,
                    }}>
                    诚意金状态(可多选)
                </Text>
                <View style={{ alignItems: 'center' }}>
                    <View
                        style={{
                            width: '100%',
                            justifyContent: 'flex-start',
                            flexDirection: 'row',
                            alignSelf: 'center',
                            marginTop: 10,
                            flexWrap: 'wrap',
                        }}>
                        {SalesStatusMap.map((item, index) => {
                            return (
                                <View
                                    key={index}
                                    style={{
                                        width: '33%',
                                        height: 35,
                                        marginBottom: 10,
                                    }}>
                                    <TouchableOpacity
                                        onPress={() => {
                                            // 可以多选
                                            let newMap = chooseSaleStatus;
                                            if (newMap.indexOf(item.id) == -1) newMap.push(item.id);
                                            else newMap.splice(newMap.indexOf(item.id), 1);
                                            //     更新chooseSaleStatus
                                            setChooseSaleStatus([...newMap]);
                                            console.log(chooseSaleStatus);
                                        }}>
                                        {renderTextContainerItem(item.SState, chooseSaleStatus.indexOf(item.id) !== -1)}
                                    </TouchableOpacity>
                                </View>
                            );
                        })}
                    </View>
                </View>
            </View>
        );
    }

    function renderOrderNoView() {
        return (
            <View style={{ backgroundColor: 'white' }}>
                <Text
                    style={{
                        fontSize: 16,
                        color: '#333333',
                        marginLeft: 10,
                        marginTop: 10,
                    }}>
                    保证金订单单号
                </Text>
                <View
                    style={{
                        flexDirection: 'row',
                        alignItems: 'center',
                        borderWidth: 1,
                        borderColor: '#E5E4E4',
                        margin: 10,
                    }}>
                    <UIImage source={'http://img.zczy56.com/202502131446524632453.png'} style={{ height: 15, width: 15 }} />
                    <TextInput
                        placeholder={'输入保证金订单单号'}
                        style={{ fontSize: 16 }}
                        onChangeText={(text) => {
                            setOrderNum(text);
                        }}
                    />
                </View>
            </View>
        );
    }
}

const styles = StyleSheet.create({
    buttonContainer: {
        flexDirection: 'row',
        justifyContent: 'space-between',
        alignItems: 'center',
        paddingHorizontal: 19,
        height: 64,
        marginTop: 10,
        backgroundColor: '#fff',
    },
    textContainer: {
        position: 'relative',
        width: 162,
        height: 29,
        borderRadius: 15,
        alignItems: 'center',
        justifyContent: 'center',
    },
    lineStyle: {
        width: 8.5,
        height: 1,
        borderBottomWidth: 1,
        borderBottomColor: '#333',
    },
    blackContent: {
        minWidth: 80,
        height: 30,
        backgroundColor: '#f6f7fa',
        borderRadius: 3,
        alignItems: 'center',
        justifyContent: 'center',
        marginLeft: 10,
        marginRight: 10,
    },
    procureTypeViewStyle1: {
        width: '90%',
        height: 1,
        backgroundColor: '#ddd',
        marginTop: 10,
        alignSelf: 'center',
    },
    procureTypeViewStyle2: {
        fontSize: 16,
        color: '#333333',
        marginLeft: 10,
        marginTop: 10,
        fontWeight: 'bold',
    },
    procureTypeViewStyle3: {
        width: '100%',
        justifyContent: 'flex-start',
        flexDirection: 'row',
        alignSelf: 'center',
        marginTop: 10,
        flexWrap: 'wrap',
    },
    rowContainer: {
        width: '100%',
        justifyContent: 'space-evenly',
        flexDirection: 'row',
        alignSelf: 'center',
        marginTop: 10,
        flexWrap: 'wrap',
    },
});
