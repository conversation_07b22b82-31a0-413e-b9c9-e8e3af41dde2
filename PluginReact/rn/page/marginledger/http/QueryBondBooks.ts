// http://wiki.zczy56.com/pages/viewpage.action?pageId=78545862
import NativeHttpRequest from '../../../base/http/NativeHttpRequest';

export class FreezeDetailList {
    rootArray: BondBookDetail[];
    resultCode?: string;
    resultMsg?: string;
}

//查询保证金账户列表
export class BondBookDetail {
    fundMode?: string; //账户类型
    fundModeDesc?: string; //账户类型描述
    bookNo?: string; //账本编号
    bookName?: string; //账本名称
    customerId?: string; //客户代码
    subsidiaryName?: string; //所属平台名称
    subsidiaryId?: string; //所属平台ID

    subsidiaryShortName?: string; //所属平台简称
    depositMoney?: string; //可用余额
    freezeMoney?: string; //冻结余额
}

export const QueryBondBooks = (req: {fundMode?: string}) => {
    return new NativeHttpRequest('pps-app/finance/bond/queryBondBooks').sendRequest<FreezeDetailList>(req);
};
