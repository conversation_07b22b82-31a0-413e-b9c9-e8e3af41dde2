import {Keyboard, ScrollView, StyleSheet, Text, TextInput, TouchableOpacity, View} from 'react-native';
import React from 'react';
import {NativeStackScreenProps} from '@react-navigation/native-stack';
import {ParamListBase} from '@react-navigation/native';
import BaseCommPage from '../../../base/comm/BasePage';
import UITitleView from '../../../base/widget/UITitleView';
import TextUtils from '../../../base/utils/TextUtils';
import {ZRouter} from '../../../base/comm/ZRouter';
import {msg, success} from '../../../base/http/Http';
import {ToastBuilder, WaitDialogBuilder} from '../../../base/comm/UI';
import {transferMoneyRequest} from './requests/SpecialDto';

interface State {
    transferAmount?: string; // 转账金额输入值
    transferError?: string; // 错误提示

    // 转入账户信息
    bookNameIn?: string;
    bookNoIn?: string;
    depositMoneyIn?: string;
    fundModeIn?: string;

    // 转出账户信息
    bookNameFa?: string;
    bookNoFa?: string;
    depositMoneyFa?: string;
    subsidiaryIdFa?: string;
    fundModeFa?: string;
}

/**
 * 注释: 资金转入
 * 时间: 2025/6/26 8:13
 * <AUTHOR>
 */

export default class SpecialFundsTransferredInPage extends BaseCommPage<NativeStackScreenProps<ParamListBase>, State> {
    //签约平台 id
    private subsidiaryId?: string;

    constructor(props: NativeStackScreenProps<ParamListBase>) {
        super(props);
        const params = ZRouter.getParams(this.props);
        this.state = {
            transferAmount: '',
            transferError: '',
            bookNameIn: params.bookName,
            bookNoIn: params.bookNo,
            depositMoneyIn: params.depositMoney,
            fundModeIn: params.fundMode,

            bookNameFa: params.bookNameFa,
            bookNoFa: params.bookNoFa,
            depositMoneyFa: params.depositMoneyFa,
            subsidiaryIdFa: params.subsidiaryIdFa,
            fundModeFa: params.fundModeFa,
        };
        this.subsidiaryId = params.subsidiaryId; // Assuming this is for the current page/context if needed later
    }

    onChangeTextMoney = (txt: string) => {
        // Only allow numbers and one decimal point
        const regex = /^\d*\.?\d{0,2}$/;
        if (!regex.test(txt)) return;

        const amount = parseFloat(txt);
        const isValid = isNaN(amount) || amount <= 0 ? false : true;

        let error = '';
        if (isValid && amount > parseFloat((this.state.depositMoneyFa || '0'))) {
            error = '不可超过转出账户可用金额';
        }

        this.setState({
            transferAmount: txt,
            transferError: error,
        });
    };

    onPressDoTransfer = async () => {
        if (TextUtils.isEmpty(this.state.bookNoIn)) {
            new ToastBuilder().showMsg('转入账户信息缺失！');
            return;
        }
        if (TextUtils.isEmpty(this.state.bookNoFa)) {
            new ToastBuilder().showMsg('转出账户信息缺失！');
            return;
        }
        if (TextUtils.isEmpty(this.state.transferAmount)) {
            new ToastBuilder().showMsg('请输入转账金额！');
            return;
        }
        if (parseFloat(this.state.transferAmount ?? '0') > parseFloat(this.state.depositMoneyFa || '0')) {
            new ToastBuilder().showMsg('不可超过转出账户可用金额！');
            return;
        }

        let waitDilaog = new WaitDialogBuilder().show();

        let params = {
            bookNo: this.state.bookNoFa, // 转出账户编号
            fundMode: this.state.fundModeFa, // 转出账本类型

            targetBookNo: this.state.bookNoIn, // 转入账户编号
            targetFundMode: this.state.fundModeIn, // 转入账户类型
            targetMoney: this.state.transferAmount, // 交易金额
        };
        let req = await transferMoneyRequest(params);
        waitDilaog?.dismiss();
        if (success(req)) {
            new ToastBuilder().showMsg('转账成功！');
            ZRouter.goBack();
        } else {
            new ToastBuilder().showMsg(msg(req));
        }
    };

    render() {
        return (
            <View style={styles.container}>
                <UITitleView title={'资金转入'}/>
                {/* 转入账户 */}
                <View style={styles.accountSection}>
                    <Text style={styles.sectionTitle}>
                        <Text style={styles.transferLabel}>转入</Text>账户
                    </Text>
                    <>
                        <Text
                            style={styles.accountInfo}>账户名称：{this.state.bookNameIn || ''}</Text>
                        <Text
                            style={styles.accountInfo}>账户号：{this.state.bookNoIn || ''}</Text>
                        <Text
                            style={styles.accountInfo}>可用金额(元)：{this.state.depositMoneyIn || ''}</Text>
                    </>
                </View>
                {/* 转出账户 */}
                <View style={styles.applicationSection}>
                    <Text style={styles.sectionTitle}>
                        <Text style={styles.withdrawalLabel}>转出</Text>账户
                    </Text>
                    <>
                        <Text
                            style={styles.accountInfo}>账户名称：{this.state.bookNameFa || ''}</Text>
                        <Text
                            style={styles.accountInfo}>账户号：{this.state.bookNoFa || ''}</Text>
                        <Text
                            style={styles.accountInfo}>可用金额(元)：{this.state.depositMoneyFa || ''}</Text>
                    </>
                </View>

                {/* 本次申请 */}
                <View style={styles.applicationSection}>
                    <Text style={styles.sectionTitleBold}>本次申请</Text>
                    <Text style={styles.amountLabel}>转账金额(元)</Text>
                    <View style={styles.amountInputContainer}>
                        <Text style={styles.currencySymbol}>¥</Text>
                        <TextInput style={styles.amountInput} placeholder="请输入" keyboardType="numeric"
                                   value={this.state.transferAmount} onChangeText={this.onChangeTextMoney}/>
                    </View>
                    {TextUtils.isNoEmpty(this.state.transferError) ?
                        <Text style={styles.errorText}>{this.state.transferError}</Text> :
                        <Text style={styles.errorText}>不可超过转出账户可用金额</Text>}
                </View>
                <View style={styles.flexSpacer}/>
                <View style={styles.footerButtons}>
                    <Text style={styles.confirmButton} onPress={this.onPressDoTransfer}>
                        确定
                    </Text>
                </View>
            </View>
        );
    }
}

const styles = StyleSheet.create({
    container: {
        flex: 1,
        backgroundColor: '#EFF0F3',
    },
    accountSection: {
        marginTop: 10,
        backgroundColor: '#fff',
        paddingLeft: 14,
        paddingRight: 14,
        paddingTop: 10,
        paddingBottom: 10,
    },
    sectionTitle: {
        color: '#333',
        fontSize: 17,
    },
    sectionTitleBold: {
        color: '#333',
        fontSize: 17,
        fontWeight: 'bold',
    },
    transferLabel: {
        color: '#24AE36',
    },
    withdrawalLabel: {
        color: '#FF2525',
    },
    inputContainer: {
        flexDirection: 'row',
        backgroundColor: '#EFF0F3',
        marginTop: 7,
        borderRadius: 4,
        alignItems: 'center',
        paddingRight: 10,
        paddingLeft: 10,
    },
    textInput: {
        height: 40,
        flex: 1,
    },
    searchText: {
        marginLeft: 5,
        color: '#5086FC',
        fontSize: 14,
    },

    accountInfo: {
        marginTop: 8,
        color: '#333',
        fontSize: 13,
    },
    applicationSection: {
        marginTop: 1,
        backgroundColor: '#fff',
        paddingLeft: 14,
        paddingRight: 14,
        paddingTop: 10,
        paddingBottom: 10,
    },
    amountLabel: {
        color: '#999',
        fontSize: 14,
        marginTop: 3,
    },
    amountInputContainer: {
        flexDirection: 'row',
        alignItems: 'center',
    },
    currencySymbol: {
        color: '#333',
        fontSize: 27,
        fontWeight: 'bold',
    },
    amountInput: {
        marginLeft: 5,
    },
    errorText: {
        color: 'red',
    },
    flexSpacer: {
        flex: 1,
    },
    footerButtons: {
        flexDirection: 'row',
        backgroundColor: '#fff',
        paddingLeft: 14,
        paddingRight: 14,
        paddingTop: 10,
        paddingBottom: 10,
    },
    cancelButton: {
        color: '#fff',
        fontSize: 17,
        backgroundColor: '#CCCCCC',
        borderRadius: 6,
        flexGrow: 1,
        textAlign: 'center',
        padding: 12,
        marginRight: 8,
    },
    confirmButton: {
        color: '#fff',
        fontSize: 17,
        backgroundColor: '#5086FC',
        borderRadius: 6,
        flexGrow: 1,
        textAlign: 'center',
        padding: 12,
        marginLeft: 8,
    },
    dropdownContainer: {
        position: 'absolute',
        left: 15,
        right: 15,
        zIndex: 1000,
        backgroundColor: '#fff',
        borderWidth: 1,
        borderColor: '#ccc',
        borderRadius: 5,
        maxHeight: 200,
        overflow: 'scroll',
        paddingBottom: 10,
    },
    dropdownItem: {
        color: '#333',
        fontSize: 14,
        paddingHorizontal: 10,
        paddingVertical: 5,
    },
});
