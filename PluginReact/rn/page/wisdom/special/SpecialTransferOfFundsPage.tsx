import {Keyboard, ScrollView, StyleSheet, Text, TextInput, TouchableOpacity, View} from 'react-native';
import React from 'react';
import {NativeStackScreenProps} from '@react-navigation/native-stack';
import {ParamListBase} from '@react-navigation/native';
import BaseCommPage from '../../../base/comm/BasePage';
import UITitleView from '../../../base/widget/UITitleView';
import TextUtils from '../../../base/utils/TextUtils';
import {ZRouter} from '../../../base/comm/ZRouter';
import {msg, success} from '../../../base/http/Http';
import {ToastBuilder, WaitDialogBuilder} from '../../../base/comm/UI';
import {queryTransferBookNameDictLists, RspAccount, transferMoneyRequest} from './requests/SpecialDto';

interface State {
    transferAccountInput?: string; // 输入的账户名称（转入）
    withdrawalAccountInput?: string; // 输入的账户名称（转出）
    searchResults?: RspAccount[]; // 转入or转出账户搜索结果
    selectedTransferAccount?: RspAccount; //选择账户（转入）
    selectedWithdrawalAccount?: RspAccount; //选择账户（转出）

    transferAmount?: string; // 转账金额输入值
    transferError?: string; // 错误提示
}

/**
 * 注释: 资金划拨
 * 时间: 2025/6/18 9:07
 * <AUTHOR>
 */
export default class SpecialTransferOfFundsPage extends BaseCommPage<NativeStackScreenProps<ParamListBase>, State> {
    private searchTimeout: NodeJS.Timeout | null = null;
    private searchType = '';
    private transferTop = 1;
    private withdrawalTop = 1;
    //签约平台 id
    private subsidiaryId?: string;

    constructor(props) {
        super(props);
        this.state = {
            transferAmount: '',
            transferError: '',
        };
        this.subsidiaryId = ZRouter.getParams(this.props).subsidiaryId;
    }

    onAccountInputChange = (type: 'transfer' | 'withdrawal', text: string) => {
        this.searchType = type;
        this.setState({
            [type === 'transfer' ? 'transferAccountInput' : 'withdrawalAccountInput']: text,
            [type === 'transfer' ? 'selectedTransferAccount' : 'selectedWithdrawalAccount']: undefined,
            transferError: '',
        });

        if (this.searchTimeout) {
            clearTimeout(this.searchTimeout);
        }

        if (TextUtils.isEmpty(text)) {
            this.setState({searchResults: undefined});
            return;
        }

        this.searchTimeout = setTimeout(async () => {
            // 调用接口获取模糊匹配数据
            queryTransferBookNameDictLists(this.subsidiaryId)
                .then((res) => {
                    if (success(res)) {
                        this.setState({searchResults: res.data.rootArray});
                    }
                })
                .catch((err) => {});
        }, 300);
    };
    onChangeTextMoney = (txt: string) => {
        // Only allow numbers and one decimal point
        const regex = /^\d*\.?\d{0,2}$/;
        if (!regex.test(txt)) return;

        const amount = parseFloat(txt);
        const isValid = isNaN(amount) || amount <= 0 ? false : true;

        let error = '';
        if (isValid && amount > parseFloat((this.state.selectedWithdrawalAccount?.balanceMoney || '0'))) {
            error = '转账金额超过转出账户可用金额！';
        }

        this.setState({
            transferAmount: txt,
            transferError: error,
        });
    };

    renderAccountDropdown = (results: RspAccount[]) => {
        if (!results.length) return null;
        // Keyboard.dismiss(); // 隐藏软键盘
        const onSelect = (account: RspAccount) => {
            this.setState({
                [this.searchType === 'transfer' ? 'selectedTransferAccount' : 'selectedWithdrawalAccount']: account,
                [this.searchType === 'transfer' ? 'transferAccountInput' : 'withdrawalAccountInput']: account.bookName,
                transferError: '',
                searchResults: undefined,
            });
        };

        let top = this.searchType === 'transfer' ? this.transferTop : this.state.selectedTransferAccount ? this.withdrawalTop + 50 : this.withdrawalTop;
        return (
            <TouchableOpacity
                activeOpacity={1}
                style={{
                    position: 'absolute',
                    top: 0,
                    bottom: 0,
                    left: 0,
                    right: 0,
                    backgroundColor: 'transparent',
                }}
                onPress={() => this.setState({searchResults: undefined})}>
                <ScrollView style={[styles.dropdownContainer, {top: top}]}>
                    {results.map((account) => (
                        <TouchableOpacity key={account.bookNo} onPress={() => onSelect(account)}>
                            <Text style={styles.dropdownItem}>{account.bookName}</Text>
                        </TouchableOpacity>
                    ))}
                </ScrollView>
            </TouchableOpacity>
        );
    };
    handleInputLayout = (event: any) => {
        // 获取距离顶部的距离，定位遮盖层位置用
        event.target.measureInWindow((x: number, y: number, width: number, height: number) => {
            this.transferTop = y + (height * 3) / 2 + 10; // 下拉框显示在输入框下方
        });
    };
    handleInputLayout2 = (event: any) => {
        // 获取距离顶部的距离，定位遮盖层位置用
        event.target.measureInWindow((x: number, y: number, width: number, height: number) => {
            this.withdrawalTop = y + (height * 3) / 2 + 10; // 下拉框显示在输入框下方
        });
    };

    onPressDoTransfer = async () => {
        if (TextUtils.isEmpty(this.state.selectedTransferAccount?.bookNo)) {
            new ToastBuilder().showMsg('请选择转入账户！');
            return;
        }
        if (TextUtils.isEmpty(this.state.selectedWithdrawalAccount?.bookNo)) {
            new ToastBuilder().showMsg('请选择转出账户！');
            return;
        }
        if (TextUtils.isEmpty(this.state.transferAmount)) {
            new ToastBuilder().showMsg('请输入转账金额！');
            return;
        }
        if (parseFloat(this.state.transferAmount ?? '0') > parseFloat(this.state.selectedWithdrawalAccount?.balanceMoney || '0')) {
            new ToastBuilder().showMsg('转出账户余额不足！');
            return;
        }

        let waitDilaog = new WaitDialogBuilder().show();

        let params = {
            bookNo: this.state.selectedWithdrawalAccount?.bookNo,//  转出户
            fundMode:  this.state.selectedWithdrawalAccount?.fundMode,//  转出账本类型

            targetBookNo: this.state.selectedTransferAccount?.bookNo,  // 转入户
            targetFundMode: this.state.selectedTransferAccount?.fundMode,//  转入账户类型  
            targetMoney: this.state.transferAmount//交易金额
        };
        let req = await transferMoneyRequest(params);
        waitDilaog?.dismiss();
        if (success(req)) {
            new ToastBuilder().showMsg('转账成功！');
            ZRouter.goBack();
        } else {
            new ToastBuilder().showMsg(msg(req));
        }
    };

    onSearchTransfer =  () => { 
        if (TextUtils.isEmpty(this.state.transferAccountInput)) { 
            this.showToast('请输入转入账户！');
            return;
        }
        this.onAccountInputChange('transfer', this.state.transferAccountInput??'')
    }
    onSearchWithdrawal =  () => { 
        if (TextUtils.isEmpty(this.state.withdrawalAccountInput)) { 
            this.showToast('请输入转出账户！');
            return;
        }
        this.onAccountInputChange('withdrawal', this.state.withdrawalAccountInput??'')
    }
    render() {
        return (
            <View style={styles.container}>
                <UITitleView title={'资金划拨申请'} />
                {/* 转入账户 */}
                <View style={styles.accountSection}>
                    <Text style={styles.sectionTitle}>
                        <Text style={styles.transferLabel}>转入</Text>账户
                    </Text>
                    <View style={styles.inputContainer} onLayout={this.handleInputLayout}>
                        <TextInput
                            blurOnSubmit={false}
                            returnKeyType="done"
                            onSubmitEditing={Keyboard.dismiss} // 提交时隐藏键盘
                            style={styles.textInput}
                            placeholder="请输入账户名称搜索"
                            value={this.state.transferAccountInput}
                            onChangeText={(text) => this.onAccountInputChange('transfer', text)}
                        />
                        <Text style={styles.searchText} onPress={this.onSearchTransfer}>搜索</Text>
                    </View>

                    {this.state.selectedTransferAccount && (
                        <>
                            <Text style={styles.accountInfo}>账户号：{this.state.selectedTransferAccount?.bookNo || ''}</Text>
                            <Text style={styles.accountInfo}>可用金额(元)：{this.state.selectedTransferAccount?.balanceMoney || ''}</Text>
                        </>
                    )}
                </View>
                {/* 转出账户 */}
                <View style={styles.applicationSection}>
                    <Text style={styles.sectionTitle}>
                        <Text style={styles.withdrawalLabel}>转出</Text>账户
                    </Text>
                    <View style={styles.inputContainer} onLayout={this.handleInputLayout2}>
                        <TextInput
                            blurOnSubmit={false}
                            returnKeyType="done"
                            onSubmitEditing={Keyboard.dismiss} // 提交时隐藏键盘
                            style={styles.textInput}
                            placeholder="请输入账户名称搜索"
                            value={this.state.withdrawalAccountInput}
                            onChangeText={(text) => this.onAccountInputChange('withdrawal', text)}
                        />
                        <Text style={styles.searchText} onPress={this.onSearchWithdrawal}>搜索</Text>
                    </View>

                    {this.state.selectedWithdrawalAccount && (
                        <>
                            <Text style={styles.accountInfo}>账户号：{this.state.selectedWithdrawalAccount?.bookNo || ''}</Text>
                            <Text style={styles.accountInfo}>可用金额(元)：{this.state.selectedWithdrawalAccount?.balanceMoney || ''}</Text>
                        </>
                    )}
                </View>

                {/* 本次申请 */}
                <View style={styles.applicationSection}>
                    <Text style={styles.sectionTitleBold}>本次申请</Text>
                    <Text style={styles.amountLabel}>转账金额(元)：</Text>
                    <View style={styles.amountInputContainer}>
                        <Text style={styles.currencySymbol}>¥</Text>
                        <TextInput style={styles.amountInput} placeholder="请输入" keyboardType="numeric" value={this.state.transferAmount} onChangeText={this.onChangeTextMoney} />
                    </View>
                    {TextUtils.isNoEmpty(this.state.transferError) && <Text style={styles.errorText}>{this.state.transferError}</Text>}
                </View>
                <View style={styles.flexSpacer} />
                <View style={styles.footerButtons}>
                    <Text style={styles.cancelButton} onPress={() => ZRouter.goBack()}>
                        取消
                    </Text>
                    <Text style={styles.confirmButton} onPress={this.onPressDoTransfer}>
                        确定
                    </Text>
                </View>

                {/* 转入转出账号下拉选择 */}
                {this.state.searchResults && this.renderAccountDropdown(this.state.searchResults)}
            </View>
        );
    }
}

const styles = StyleSheet.create({
    container: {
        flex: 1,
        backgroundColor: '#EFF0F3',
    },
    accountSection: {
        marginTop: 10,
        backgroundColor: '#fff',
        paddingLeft: 14,
        paddingRight: 14,
        paddingTop: 10,
        paddingBottom: 10,
    },
    sectionTitle: {
        color: '#333',
        fontSize: 17,
    },
    sectionTitleBold: {
        color: '#333',
        fontSize: 17,
        fontWeight: 'bold',
    },
    transferLabel: {
        color: '#24AE36',
    },
    withdrawalLabel: {
        color: '#FF2525',
    },
    inputContainer: {
        flexDirection: 'row',
        backgroundColor: '#EFF0F3',
        marginTop: 7,
        borderRadius: 4,
        alignItems: 'center',
        paddingRight: 10,
        paddingLeft: 10,
    },
    textInput: {
        height: 40,
        flex: 1,
    },
    searchText: {
        marginLeft: 5,
        color: '#5086FC',
        fontSize: 14,
    },

    accountInfo: {
        marginTop: 8,
        color: '#333',
        fontSize: 13,
    },
    applicationSection: {
        marginTop: 1,
        backgroundColor: '#fff',
        paddingLeft: 14,
        paddingRight: 14,
        paddingTop: 10,
        paddingBottom: 10,
    },
    amountLabel: {
        color: '#999',
        fontSize: 14,
        marginTop: 3,
    },
    amountInputContainer: {
        flexDirection: 'row',
        alignItems: 'center',
    },
    currencySymbol: {
        color: '#333',
        fontSize: 27,
        fontWeight: 'bold',
    },
    amountInput: {
        marginLeft: 5,
    },
    errorText: {
        color: 'red',
    },
    flexSpacer: {
        flex: 1,
    },
    footerButtons: {
        flexDirection: 'row',
        backgroundColor: '#fff',
        paddingLeft: 14,
        paddingRight: 14,
        paddingTop: 10,
        paddingBottom: 10,
    },
    cancelButton: {
        color: '#fff',
        fontSize: 17,
        backgroundColor: '#CCCCCC',
        borderRadius: 6,
        flexGrow: 1,
        textAlign: 'center',
        padding: 12,
        marginRight: 8,
    },
    confirmButton: {
        color: '#fff',
        fontSize: 17,
        backgroundColor: '#5086FC',
        borderRadius: 6,
        flexGrow: 1,
        textAlign: 'center',
        padding: 12,
        marginLeft: 8,
    },
    dropdownContainer: {
        position: 'absolute',
        left: 15,
        right: 15,
        zIndex: 1000,
        backgroundColor: '#fff',
        borderWidth: 1,
        borderColor: '#ccc',
        borderRadius: 5,
        maxHeight: 200,
        overflow: 'scroll',
        paddingBottom: 10,
    },
    dropdownItem: {
        color: '#333',
        fontSize: 14,
        paddingHorizontal: 10,
        paddingVertical: 5,
    },
});
