import NativeHttpRequest from '../../../../base/http/NativeHttpRequest';
import PageList from '../../../../base/http/PageList';
import {ResultData} from '../../../../base/http/ResultData';

/**
 * 资金划拨账户查询
 * wiki ：http://wiki.zczy56.com/pages/viewpage.action?pageId=********
 * 对接人 ：张晴木
 * @param subsidiaryId 签约平台 id
 * @returns {Promise<any>}
 */
export const queryTransferBookNameDictLists = (subsidiaryId?: string) => {
    return new NativeHttpRequest('pps-app/deptAccount/queryTransferBookNameDictLists').sendRequest<PageList<RspAccount>>({subsidiaryId: subsidiaryId});
};

export class RspAccount {
    fundMode?: string; // 账户类型
    bookNo?: string; // 账户编号
    bookName?: string; // 账户名称
    depositMoney?: string; // 账户可用金额
    balanceMoney?: string; // 账户余额【可用 + 授信】
}

/**
 *    author : Ssp
 *    e-mail : <EMAIL>
 *    date   : 2020/3/514:45
 *    desc   : 划拨资金-转出
 *    version: 1.0
 */
export const transferMoneyRequest = (req: {
    bookNo?: string; //  转出户
    fundMode?: string; //  转出账本类型
    targetBookNo?: string; // 转入户
    targetFundMode?: string; //  转入账户类型
    targetMoney?: string; //交易金额
}) => {
    return new NativeHttpRequest('pps-app/customerCapital/transferMoney').sendRequest<ResultData>(req);
};
