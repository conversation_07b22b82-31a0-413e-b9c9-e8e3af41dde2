package com.zczy.rn;

import android.app.Application;

import androidx.annotation.NonNull;
import androidx.annotation.Nullable;

import com.BV.LinearGradient.LinearGradientPackage;
import com.dylanvann.fastimage.FastImageViewPackage;
import com.facebook.hermes.reactexecutor.HermesExecutorFactory;
import com.facebook.react.ReactPackage;
import com.facebook.react.bridge.JavaScriptExecutorFactory;
import com.facebook.react.bridge.NativeModule;
import com.facebook.react.bridge.ReactApplicationContext;
import com.facebook.react.shell.MainReactPackage;
import com.facebook.react.uimanager.ViewManager;
import com.henninghall.date_picker.DatePickerPackage;
import com.reactnativecommunity.clipboard.ClipboardPackage;
import com.reactnativepagerview.PagerViewPackage;
import com.shopify.reactnative.flash_list.ReactNativeFlashListPackage;
import com.swmansion.gesturehandler.RNGestureHandlerPackage;
import com.swmansion.rnscreens.RNScreensPackage;
import com.th3rdwave.safeareacontext.SafeAreaContextPackage;
import com.zczy.version.sdk.rn.ZReactNativeHost;


import java.util.ArrayList;
import java.util.Collections;
import java.util.List;


public class ZczyReactNativeHost extends ZReactNativeHost {

    public ZczyReactNativeHost(Application application) {
        super(application);
    }

    @Override
    public boolean getUseDeveloperSupport() {
        return BuildConfig.DEBUG;
    }

    @Override
    protected String getJSMainModuleName() {
        return "index";
    }


    @Nullable
    @Override
    protected JavaScriptExecutorFactory getJavaScriptExecutorFactory() {
        return new HermesExecutorFactory();
    }

    @Override
    public List<ReactPackage> getPackages() {

        List<ReactPackage> packages = new ArrayList<>();
        packages.add(new MainReactPackage());
        packages.add(new RNScreensPackage());
        packages.add(new RNGestureHandlerPackage());
        packages.add(new SafeAreaContextPackage());
        packages.add(new PagerViewPackage());
        packages.add(new FastImageViewPackage());
        packages.add(new ReactNativeFlashListPackage());
        packages.add(new LinearGradientPackage());
        packages.add(new DatePickerPackage());
        packages.add(new ClipboardPackage());
        packages.add(new ReactPackage() {
            @NonNull
            @Override
            public List<NativeModule> createNativeModules(@NonNull ReactApplicationContext reactContext) {
                List<NativeModule> nativeModules = new ArrayList<>();
                OpenNativeModule openNativeModule = new OpenNativeModule(reactContext);
                nativeModules.add(openNativeModule);
                return nativeModules;
            }

            @NonNull
            @Override
            public List<ViewManager> createViewManagers(@NonNull ReactApplicationContext reactContext) {
                return Collections.emptyList();
            }
        });
        return packages;
    }

}
