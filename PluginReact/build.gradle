apply plugin: 'com.android.library'


project.ext.react = [
        enableHermes: true,  // clean and rebuild if changing
]

android {
    namespace "com.zczy.rn"
    compileSdkVersion config.compileSdkVersion
    defaultConfig {
        minSdkVersion config.minSdkVersion
        targetSdkVersion config.targetSdkVersion
        versionCode 1
        versionName '1.0.0'
    }

    buildTypes {
        release {
            minifyEnabled false
            proguardFiles getDefaultProguardFile('proguard-android-optimize.txt'), 'proguard-rules.pro'
        }
    }
    sourceSets {
        main {
            assets.srcDirs = ["./assets","./src/main/assets"]
        }
    }
    compileOptions {
        sourceCompatibility JavaVersion.VERSION_1_8
        targetCompatibility JavaVersion.VERSION_1_8
    }
}

dependencies {
    implementation project(path: ':LibCOMM')

    implementation("com.facebook.react:hermes-android")
    api("com.facebook.react:react-android")
    implementation("androidx.swiperefreshlayout:swiperefreshlayout:1.0.0")

    implementation project(':react-native-screens')
    implementation project(':react-native-gesture-handler')
    implementation project(':react-native-safe-area-context')
    implementation project(':react-native-pager-view')
    implementation project(':react-native-fast-image')
    implementation project(':react-native-flash-list')
    implementation project(':react-native-linear-gradient')
    implementation project(':react-native-date-picker')
    implementation project(':react-native-clipboard')
}

