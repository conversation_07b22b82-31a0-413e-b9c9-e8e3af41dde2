{
  "compilerOptions": {
    "target": "es2017",
    "jsx": "preserve",
    "strict": false,
    "noImplicitAny": false,
    "moduleResolution": "node",
    "baseUrl": "./pages",
    "allowSyntheticDefaultImports": true,
    "experimentalDecorators": true,
    "emitDecoratorMetadata": true,
    "esModuleInterop": true,
    "removeComments": false,
    "isolatedModules": true,
    //开启严格空安全检查
    "strictNullChecks": true,
    // "allowJs": true,// 设置是否对js文件进行编译，默认为false
    "checkJs": false
  },
  "exclude": [
    "./node_modules",
    "./babel.config.js",
    "./metro.config.js",
    "./index.js"
  ]
}
