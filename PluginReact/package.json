{"name": "ZczyHZRN", "version": "0.0.1", "private": true, "scripts": {"adb-reverse": "adb reverse tcp:8081 tcp:8081", "start": "react-native start", "lint": "eslint .", "update-version": "node scripts/updateVersion.js", "bundle-debug": "react-native bundle --platform android --dev true --reset-cache --entry-file index.js --bundle-output ./src/main/assets/index.android.bundle --assets-dest ./src/main/res/", "bundle-release": "react-native bundle --platform android --dev false --reset-cache --entry-file index.js --bundle-output ./src/main/assets/index.android.bundle --assets-dest ./src/main/res/ --sourcemap-output ./src/main/assets/index.android.bundle.map"}, "dependencies": {"@react-native-clipboard/clipboard": "1.16.2", "@react-native-picker/picker": "2.7.5", "@react-navigation/bottom-tabs": "6.6.1", "@react-navigation/material-top-tabs": "6.6.14", "@react-navigation/native": "6.1.18", "@react-navigation/native-stack": "6.11.0", "@shopify/flash-list": "1.7.2", "axios": "^1.7.7", "date-fns": "4.1.0", "json-bigint": "1.0.0", "react": "18.1.0", "dayjs": "1.11.13", "react-native": "0.71.10", "crypto-js": "4.2.0", "react-native-dates-picker": "0.0.9", "react-native-date-picker": "5.0.8", "react-native-fast-image": "8.6.3", "react-native-gesture-handler": "2.19.0", "react-native-modal": "13.0.1", "react-native-modal-datetime-picker": "^17.1.0", "react-native-pager-view": "6.3.0", "react-native-safe-area-context": "4.14.0", "react-native-screens": "3.25.0", "react-native-tab-view": "4.0.1", "react-native-toast-message": "2.2.1", "reflect-metadata": "0.1.13", "react-native-linear-gradient": "2.8.2", "react-freeze": "1.0.3", "recyclerlistview": "4.2.0", "rmc-date-picker": "6.0.10", "class-transformer": "0.5.1", "hoist-non-react-statics": "3.0.0", "react-native-image-picker": "7.1.2", "react-native-image-zoom-viewer": "3.0.1", "react-native-permissions": "4.1.5", "use-subscription": "1.8.2", "xdate": "^0.8.0", "react-native-swipe-gestures": "^1.0.5", "memoize-one": "^5.2.1"}, "devDependencies": {"@babel/core": "^7.12.9", "@babel/runtime": "^7.12.5", "@react-native-community/eslint-config": "^2.0.0", "@types/react": "18.2.75", "@types/react-native": "0.70.19", "babel-jest": "^26.6.3", "eslint": "^7.32.0", "metro-react-native-babel-preset": "0.72.4", "prettier": "2.8.8", "typescript": "4.9.5", "@react-native-community/cli": "9.3.2", "@react-native-community/cli-hermes": "9.3.4", "@react-native-community/cli-platform-android": "9.3.1", "@react-native-community/cli-platform-ios": "9.3.0", "@react-native-community/cli-plugin-metro": "9.3.3"}, "engines": {"node": ">=16"}}