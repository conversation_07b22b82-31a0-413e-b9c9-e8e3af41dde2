apply plugin: 'com.android.library'

android {
    compileSdkVersion config.compileSdkVersion

    defaultConfig {
        minSdkVersion config.minSdkVersion
        targetSdkVersion config.targetSdkVersion
        consumerProguardFiles "consumer-rules.pro"

        javaCompileOptions {
            annotationProcessorOptions {
                arguments = [AROUTER_MODULE_NAME : 'aicall']
            }
        }
    }

    buildTypes {
        debug {
            minifyEnabled false
            proguardFiles getDefaultProguardFile('proguard-android-optimize.txt'), 'proguard-rules.pro'
        }
        release {
            minifyEnabled false
            proguardFiles getDefaultProguardFile('proguard-android-optimize.txt'), 'proguard-rules.pro'
        }
    }
    compileOptions {
        sourceCompatibility JavaVersion.VERSION_1_8
        targetCompatibility JavaVersion.VERSION_1_8
    }
}

dependencies {
    implementation project(path: ':LibCOMM')

    implementation 'com.orhanobut:dialogplus:1.11@aar'
    implementation 'io.github.muddz:styleabletoast:2.4.0'
    implementation 'com.aliyun.aio:AliVCSDK_ARTC:7.4.0'                  //AI通话功能1
    implementation 'com.aliyun.auikits.android:ARTCAICallKit:2.4.0' //AI通话功能2
    implementation 'com.aliyun.sdk.android:AliVCInteractionMessage:1.7.0'
    implementation  'com.squareup.okhttp3:logging-interceptor:4.8.1'
}

