<?xml version="1.0" encoding="utf-8"?>
<LinearLayout xmlns:android="http://schemas.android.com/apk/res/android"
    xmlns:app="http://schemas.android.com/apk/res-auto"
    xmlns:tools="http://schemas.android.com/tools"
    android:layout_width="match_parent"
    android:layout_height="match_parent"
    android:fitsSystemWindows="true"
    android:background="#EFF0F3"
    android:orientation="vertical">

    <LinearLayout
        android:layout_width="match_parent"
        android:layout_height="wrap_content"
        android:layout_marginTop="8dp"
        android:layout_marginLeft="10dp"
        android:layout_marginRight="10dp"
        android:paddingBottom="10dp"
        android:background="@drawable/ffffff_10_bg"
        android:orientation="vertical">

        <LinearLayout
            android:layout_width="match_parent"
            android:layout_height="wrap_content"
            android:paddingTop="13dp"
            android:paddingLeft="15dp"
            android:paddingRight="15dp">

            <ImageView
                android:layout_width="wrap_content"
                android:layout_height="wrap_content"
                android:background="@drawable/ic_plan"/>

            <TextView
                android:id="@+id/tv_postion"
                android:layout_width="0dp"
                android:layout_height="wrap_content"
                android:layout_marginLeft="10dp"
                android:layout_weight="1"
                android:textColor="#333333"
                android:textSize="16dp"
                tools:text="计划-1"/>

            <ImageView
                android:id="@+id/iv_item_delete"
                android:layout_width="wrap_content"
                android:layout_height="wrap_content"
                android:layout_gravity="right"
                android:background="@drawable/ic_delete"/>

        </LinearLayout>

        <TextView
            android:id="@+id/tv_view1"
            android:layout_width="match_parent"
            android:layout_height="1dp"
            android:layout_marginTop="10dp"
            android:background="@drawable/shape_dotted_line"
            app:layout_constraintTop_toBottomOf="@+id/tv_process_has_cast" />

        <LinearLayout
            android:layout_width="match_parent"
            android:layout_height="wrap_content"
            android:orientation="horizontal"
            android:layout_marginTop="8dp"
            android:paddingLeft="15dp"
            android:paddingRight="15dp">

            <TextView
                android:layout_width="wrap_content"
                android:layout_height="wrap_content"
                android:text="发货计划日 "
                android:textColor="#666666"
                android:textSize="15sp" />

            <TextView
                android:layout_width="wrap_content"
                android:layout_height="wrap_content"
                android:text="*"
                android:textColor="#FE4C4C"
                android:textSize="15sp" />

            <TextView
                android:id="@+id/tv_plan_day"
                android:layout_width="0dp"
                android:layout_height="wrap_content"
                android:layout_marginLeft="15dp"
                android:drawableRight="@drawable/iv_select_icon"
                android:drawablePadding="6dp"
                android:gravity="right"
                android:layout_weight="1"
                android:hint="请选择"
                android:maxLines="1"
                android:singleLine="true"
                android:textColor="#333333"
                android:textColorHint="#999999"
                android:textSize="15sp" />

        </LinearLayout>

        <TextView
            android:id="@+id/tv_view2"
            android:layout_width="match_parent"
            android:layout_height="1dp"
            android:layout_marginTop="10dp"
            android:background="@drawable/shape_dotted_line"
            app:layout_constraintTop_toBottomOf="@+id/tv_process_has_cast" />

        <LinearLayout
            android:layout_width="match_parent"
            android:layout_height="wrap_content"
            android:orientation="horizontal"
            android:layout_marginTop="8dp"
            android:paddingLeft="15dp"
            android:paddingRight="15dp">

            <TextView
                android:layout_width="wrap_content"
                android:layout_height="wrap_content"
                android:text="每日计划发货 "
                android:textColor="#666666"
                android:textSize="15sp" />

            <TextView
                android:layout_width="wrap_content"
                android:layout_height="wrap_content"
                android:text="*"
                android:textColor="#FE4C4C"
                android:textSize="15sp" />

            <EditText
                android:id="@+id/et_plan_num"
                android:layout_width="0dp"
                android:layout_height="wrap_content"
                android:layout_marginLeft="15dp"
                android:drawablePadding="6dp"
                android:gravity="right"
                android:hint="请输入"
                android:layout_weight="1"
                android:maxLength="128"
                android:maxLines="20"
                android:background="@null"
                android:singleLine="true"
                android:textColor="#333333"
                android:textColorHint="#999999"
                android:textSize="15sp" />

            <ImageView
                android:id="@+id/iv_delete"
                android:layout_width="wrap_content"
                android:layout_height="wrap_content"
                android:src="@drawable/delete_item"
                android:visibility="gone"
                android:padding="4dp"/>

            <TextView
                android:id="@+id/tv_weight_unit"
                android:layout_width="wrap_content"
                android:layout_height="wrap_content"
                tools:text="吨"
                android:textSize="15dp"
                android:layout_marginLeft="10dp"
                android:textColor="#333333"/>

        </LinearLayout>

        <TextView
            android:id="@+id/tv_tips"
            android:layout_width="wrap_content"
            android:layout_height="wrap_content"
            android:layout_gravity="right"
            android:layout_marginTop="7dp"
            android:layout_marginRight="15dp"
            android:textSize="13dp"
            android:textColor="#C2C2C2"
            tools:text="每日承运不得超过40吨"/>

    </LinearLayout>


</LinearLayout>