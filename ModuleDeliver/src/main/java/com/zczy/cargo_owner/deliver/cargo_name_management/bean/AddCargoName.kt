package com.zczy.cargo_owner.deliver.cargo_name_management.bean

import com.zczy.comm.http.entity.BaseNewRequest
import com.zczy.comm.http.entity.BaseRsp
import com.zczy.comm.http.entity.ResultData

/**
 *描述：新增货品名称
 *auth:宋双朋
 *time:2024/6/18 17:18
 */
data class ReqAddCargoName(
    var inputBaseName: String? = null,//	货物名称(多个逗号分割)
    var cargoUrlDtoList: MutableList<ReqAddCargoNameV1>? = null,//需要传入资质信息货物
) : BaseNewRequest<BaseRsp<AddCargoNameRsp>>("oms-app/goodsController/addCargoBaseName")

class ReqAddCargoNameV1(
    var baseName: String? = null, // 货物名称
    var urls: MutableList<String>? = null, // 	图片集合
)

data class AddCargoNameRsp(
    val msg: String
) : ResultData()

class RxBusAddCargoNameEvent(
    var success: Boolean = false
)

/**
 *描述：货物品名编辑
 *auth:宋双朋
 *time:2024/6/18 17:20
 */

class ReqEditBaseName(
    var baseName: String? = null, // 货物名称
    var id: String? = null, // 货物id
    var urls: MutableList<String>? = null, // 货物资质图片
) : BaseNewRequest<BaseRsp<ResultData>>("oms-app/goodsController/editBaseName")

class ReqReCommit(
    var id: String? = null, // 货物id
) : BaseNewRequest<BaseRsp<ResultData>>("oms-app/goodsController/reCommit")

/**
 *  desc: 货主货物详情
 *  user: ssp
 *  time: 2024/7/8 11:28
 */

class ReqGetCargoInfo(
    var cargoConsignorId: String? = null, // 货物id
) : BaseNewRequest<BaseRsp<RspGetCargoInfo>>("oms-app/goodsController/getCargoInfo")

data class RspGetCargoInfo(
    val id: String? = null, //货物id
    val baseName: String? = null,//货物名称
    val createdTime: String? = null, //创建时间   前台输入的搜索条件
    val offenUseingFlag: String? = null,//是否常用 0-否 1-是
    val specialFlag: String? = null,//是否是特殊品 0:否 1:是
    val isChemistry: String? = null,//是否是化学品 0:否 1:是
    val state: String? = null,// 1.审核通过 2.审核不通过 3.待审核
    val specialGoodsPicUrls: MutableList<String>? = null,// 货物审核信息图片（CO-4709 【水运】货主端增加上传特殊品资质渠道）
) : ResultData()

/**
 *描述：货主新增货物校验是否特殊/化学品
 *auth:宋双朋
 *time:2024/6/18 17:20
 */

class ReqCheckCargoType(
    var checkCargoTypeDtos: MutableList<ReqCheckCargoTypeV1>? = null, // 货物名称
) : BaseNewRequest<BaseRsp<RspCheckCargoType>>("oms-app/goodsController/checkCargoType")

class ReqCheckCargoTypeV1(
    var baseName: String? = null,// 	货物名称
)

class RspCheckCargoType(
    val uniqueList: MutableList<RspCheckCargoTypeV1>? = null,//特殊品/化学品 货物集合
) : ResultData()

class RspCheckCargoTypeV1(
    val baseName: String? = null, //
    val id: String? = null, //
    val requestCode: Int = 0,//请求码
)