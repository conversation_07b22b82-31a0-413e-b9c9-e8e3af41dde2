package com.zczy.cargo_owner.deliver.addorder.widget

import android.annotation.SuppressLint
import android.content.Context
import androidx.fragment.app.FragmentActivity
import android.text.*
import android.text.style.ForegroundColorSpan
import android.util.AttributeSet
import android.util.Log
import android.view.View
import android.view.View.OnClickListener
import android.widget.LinearLayout
import androidx.core.content.ContextCompat
import com.zczy.cargo_owner.deliver.R
import com.zczy.cargo_owner.deliver.addorder.bean.batch.HugeOrderInfo
import com.zczy.cargo_owner.deliver.addorder.bean.normal.OrderInfo
import com.zczy.cargo_owner.deliver.addorder.dialog.DeliverOrderOilDialog
import com.zczy.cargo_owner.deliver.addorder.req.RspQueryMobileWholeBagHugeOrderCommonInfo
import com.zczy.cargo_owner.deliver.addorder.req.comm.Rsp23QueryMobileOrderCommonInfo
import com.zczy.cargo_owner.deliver.addorder.req.comm.Rsp24QueryMobileHugeOrderCommonInfo
import com.zczy.cargo_owner.deliver.addorder.req.comm.RspOilGasConfigInfo
import com.zczy.comm.utils.ex.isTrue
import com.zczy.comm.utils.ex.setVisible
import com.zczy.comm.utils.getResColor
import com.zczy.comm.utils.json.toJson
import com.zczy.comm.widget.dialog.ChooseDialogV1
import com.zczy.comm.widget.inputv2.InputViewCheckV2
import kotlinx.android.synthetic.main.deliver_oil_card_view.view.*

/**
 * @description
 * <AUTHOR> 韩正亚
 * @company 中储智运科技股份有限公司
 * @copyright （版权）中储南京智慧物流科技有限公司所有
 * @since 2019-07-11
 */
class OilCardView @JvmOverloads constructor(
    context: Context,
    attrs: AttributeSet? = null,
    defStyleAttr: Int = 0
) : LinearLayout(context, attrs, defStyleAttr) {

    private var viewData = ViewData()
    private var commonData = CommonData()

    var listener: Listener? = null
    var showRuleBlock: () -> Unit = {

    }
    private var showRule: Boolean = false

    init {
        View.inflate(context, R.layout.deliver_oil_card_view, this)
        orientation = VERTICAL
        setBackgroundColor(getResColor(R.color.white))
        initView()
    }

    private fun initView() {
        setShowRule()
        check_support_sd_oil_card.setListener(object : InputViewCheckV2.Listener() {
            override fun onClickCheck(
                viewId: Int,
                view: InputViewCheckV2,
                check: Int,
                radioStr: String
            ): Boolean {
                when (check) {
                    InputViewCheckV2.LEFT -> {
                        viewData.supportSdOilCardFlag = "1"
                        if (commonData.orderModel == "0" && (commonData.whetherSupportOilFixedCreditFlag == "1" || commonData.whetherSupportGasFixedCreditFlag == "1")) {
                            viewData.oilCalculateType = "0"
                        } else {
                            viewData.oilCalculateType = "1"
                        }
                    }

                    InputViewCheckV2.RIGHT -> {
                        viewData.supportSdOilCardFlag = "0"
                    }
                }
                viewData.warning = false
                refreshViewV2()
                listener?.onDataChange(viewData)
                return true
            }

            override fun onClickTitleRight(viewId: Int, view: InputViewCheckV2) {
                super.onClickTitleRight(viewId, view)
                showRuleBlock()
            }
        })
        val percentListener = OnClickListener {
            when (it.id) {
                // 油品计算方式：0 默认,1 按比例计算油品,2 按固定额度计算油品
                R.id.check_percent,
                R.id.tv_oil_percent,
                R.id.tv_ges_percent -> {
                    viewData.oilCalculateType = "1"
                    if (viewData.supportSdOilCardFlag == "") {
                        viewData.supportSdOilCardFlag = "1"
                    }
                }

                R.id.check_fixed,
                R.id.tv_gas_fixed,
                R.id.tv_fixed -> {
                    if (listener?.onClickFixed() == true) {
                        viewData.oilCalculateType = "2"
                        if (viewData.supportSdOilCardFlag == "") {
                            viewData.supportSdOilCardFlag = "1"
                        }
                    }
                }
            }
            viewData.warning = false
            refreshViewV2()
            listener?.onDataChange(viewData)
        }
        check_percent.setOnClickListener(percentListener)
        tv_oil_percent.setOnClickListener(percentListener)
        tv_ges_percent.setOnClickListener(percentListener)
        check_fixed.setOnClickListener(percentListener)
        tv_fixed.setOnClickListener(percentListener)
        tv_gas_fixed.setOnClickListener(percentListener)

        btn_oil_percent.setOnClickListener {
            if (commonData.oilConfig == "3") {
                return@setOnClickListener
            }
            val c = context
            if (c is FragmentActivity) {
                val percent =
                    if (viewData.oilPercent.isEmpty()) commonData.oilPercent else viewData.oilPercent
                // 油比例 点击
                ChooseDialogV1.instance(commonData.oilPercents?.map { it } ?: emptyList())
                    .setTitle("选择油品比例")
                    .setChoose(percent)
                    .setFlatMap { "${this}%" }
                    .setClick { s, _ ->
                        viewData.oilPercent = s
                        viewData.warning = false
                        refreshViewV2()
                        listener?.onDataChange(viewData)
                    }
                    .show(c)
            }
        }

        btn_ges_percent.setOnClickListener {
            if (commonData.oilConfig == "3") {
                return@setOnClickListener
            }
            val c = context
            if (c is FragmentActivity) {
                val percent =
                    if (viewData.gasPercent.isEmpty()) commonData.gasPercent else viewData.gasPercent
                // 油比例 点击
                ChooseDialogV1.instance(commonData.gasPercents?.map { it } ?: emptyList())
                    .setTitle("选择气品比例")
                    .setChoose(percent)
                    .setFlatMap { "${this}%" }
                    .setClick { s, _ ->
                        viewData.gasPercent = s
                        viewData.warning = false
                        refreshViewV2()
                        listener?.onDataChange(viewData)
                    }
                    .show(c)
            }
        }

        ed_fixed.addTextChangedListener(object : TextWatcher {
            override fun afterTextChanged(s: Editable) {
                viewData.warning = false
                val trim = s.toString().trim()
                viewData.oilFixedCredit = trim
                listener?.onFillFixedAmount(trim)
                listener?.onDataChange(viewData)
            }

            override fun beforeTextChanged(s: CharSequence, start: Int, count: Int, after: Int) {

            }

            override fun onTextChanged(s: CharSequence, start: Int, before: Int, count: Int) {
            }

        })

        ed_gas_fixed.addTextChangedListener(object : TextWatcher {
            override fun afterTextChanged(s: Editable) {
                viewData.warning = false
                val trim = s.toString().trim()
                viewData.gasFixedCredit = trim
                listener?.onGasFixedAmount(trim)
                listener?.onDataChange(viewData)
            }

            override fun beforeTextChanged(s: CharSequence, start: Int, count: Int, after: Int) {

            }

            override fun onTextChanged(s: CharSequence, start: Int, before: Int, count: Int) {
            }

        })
    }

    fun refreshNormalData(
        orderInfo: OrderInfo,
        orderCommonInfo: Rsp23QueryMobileOrderCommonInfo,
        rspOilGasConfigInfo: RspOilGasConfigInfo,
        isInterfaceBack: Boolean = false
    ) {
        val oilGasConfigSwitch = rspOilGasConfigInfo.oilGasConfigSwitch
        commonData = CommonData(
            orderModel = orderInfo.orderModel,
            oilConfig = orderCommonInfo.oilConfig,
            oilPercent = if (oilGasConfigSwitch.isTrue) {
                rspOilGasConfigInfo.oilPercent.ifEmpty { orderCommonInfo.oilPercent }
            } else {
                orderCommonInfo.oilPercent
            },
            gasPercent = if (oilGasConfigSwitch.isTrue) {
                rspOilGasConfigInfo.gasPercent.ifEmpty { orderCommonInfo.gasPercent }
            } else {
                orderCommonInfo.gasPercent
            },
            oilPercents = orderCommonInfo.oilPercents,
            gasPercents = orderCommonInfo.gasPercents,
            whetherSupportOilFixedCreditFlag = orderCommonInfo.whetherSupportOilFixedCreditFlag,
            whetherSupportGasFixedCreditFlag = orderCommonInfo.whetherSupportGasFixedCreditFlag,
            oilFixedCreditDefault = if (oilGasConfigSwitch.isTrue) {
                rspOilGasConfigInfo.oilFixedCredit.ifEmpty { orderCommonInfo.oilFixedCreditDefault }
            } else {
                orderCommonInfo.oilFixedCreditDefault
            },
            gasFixedCreditDefault = if (oilGasConfigSwitch.isTrue) {
                rspOilGasConfigInfo.gasFixedCredit.ifEmpty { orderCommonInfo.gasFixedCreditDefault }
            } else {
                orderCommonInfo.gasFixedCreditDefault
            },
            whetherShowSdOilCard = orderCommonInfo.whetherShowSdOilCard,
        )
        viewData = ViewData(
            warning = false,
            supportSdOilCardFlag = orderInfo.supportSdOilCardFlag,
            oilCalculateType = orderInfo.oilCalculateType,
            oilPercent = if (oilGasConfigSwitch.isTrue) {
                if (isInterfaceBack) {
                    rspOilGasConfigInfo.oilPercent.ifEmpty { orderCommonInfo.oilPercent }
                } else {
                    orderInfo.oilPercent.ifEmpty { orderCommonInfo.oilPercent }
                }
            } else {
                orderInfo.oilPercent.ifEmpty { orderCommonInfo.oilPercent }
            },
            gasPercent = if (oilGasConfigSwitch.isTrue) {
                if (isInterfaceBack) {
                    rspOilGasConfigInfo.gasPercent.ifEmpty { orderCommonInfo.gasPercent }
                } else {
                    orderInfo.gasPercent.ifEmpty { orderCommonInfo.gasPercent }
                }
            } else {
                orderInfo.gasPercent.ifEmpty { orderCommonInfo.gasPercent }
            },
            oilFixedCredit = if (oilGasConfigSwitch.isTrue) {
                if (isInterfaceBack) {
                    rspOilGasConfigInfo.oilFixedCredit.ifEmpty { orderInfo.oilFixedCredit }
                } else {
                    orderInfo.oilFixedCredit
                }
            } else {
                orderInfo.oilFixedCredit
            },
            gasFixedCredit = if (oilGasConfigSwitch.isTrue) {
                if (isInterfaceBack) {
                    rspOilGasConfigInfo.gasFixedCredit.ifEmpty { orderInfo.gasFixedCredit }
                } else {
                    orderInfo.gasFixedCredit
                }
            } else {
                orderInfo.gasFixedCredit
            }
        )
        refreshViewV2(isInterfaceBack = isInterfaceBack)
    }

    fun refreshHugeData(
        orderInfo: HugeOrderInfo,
        orderCommonInfo: Rsp24QueryMobileHugeOrderCommonInfo,
        rspOilGasConfigInfo: RspOilGasConfigInfo,
        isInterfaceBack: Boolean = false
    ) {
        val oilGasConfigSwitch = rspOilGasConfigInfo.oilGasConfigSwitch
        commonData = CommonData(
            orderModel = orderInfo.orderModel,
            oilConfig = orderCommonInfo.oilConfig,
            oilPercent = if (oilGasConfigSwitch.isTrue) {
                rspOilGasConfigInfo.oilPercent.ifEmpty { orderCommonInfo.oilPercent }
            } else {
                orderCommonInfo.oilPercent
            },
            gasPercent = if (oilGasConfigSwitch.isTrue) {
                rspOilGasConfigInfo.gasPercent.ifEmpty { orderCommonInfo.gasPercent }
            } else {
                orderCommonInfo.gasPercent
            },
            oilPercents = orderCommonInfo.oilPercents,
            gasPercents = orderCommonInfo.gasPercents,
            whetherSupportOilFixedCreditFlag = orderCommonInfo.whetherSupportOilFixedCreditFlag,
            whetherSupportGasFixedCreditFlag = orderCommonInfo.whetherSupportGasFixedCreditFlag,
            oilFixedCreditDefault = if (oilGasConfigSwitch.isTrue) {
                rspOilGasConfigInfo.oilFixedCredit.ifEmpty { orderCommonInfo.oilFixedCreditDefault }
            } else {
                orderCommonInfo.oilFixedCreditDefault
            },
            gasFixedCreditDefault = if (oilGasConfigSwitch.isTrue) {
                rspOilGasConfigInfo.gasFixedCredit.ifEmpty { orderCommonInfo.gasFixedCreditDefault }
            } else {
                orderCommonInfo.gasFixedCreditDefault
            },
            whetherShowSdOilCard = orderCommonInfo.whetherShowSdOilCard,
        )
        viewData = ViewData(
            warning = false,
            supportSdOilCardFlag = orderInfo.supportSdOilCardFlag,
            oilCalculateType = orderInfo.oilCalculateType,
            oilPercent = if (oilGasConfigSwitch.isTrue) {
                if (isInterfaceBack) {
                    rspOilGasConfigInfo.oilPercent.ifEmpty { orderCommonInfo.oilPercent }
                } else {
                    orderInfo.oilPercent
                }
            } else {
                orderCommonInfo.oilPercent
            },
            gasPercent = if (oilGasConfigSwitch.isTrue) {
                if (isInterfaceBack) {
                    rspOilGasConfigInfo.gasPercent.ifEmpty { orderCommonInfo.gasPercent }
                } else {
                    orderInfo.gasPercent
                }
            } else {
                orderCommonInfo.gasPercent
            },
            oilFixedCredit = if (oilGasConfigSwitch.isTrue) {
                rspOilGasConfigInfo.oilFixedCredit.ifEmpty { orderInfo.oilFixedCredit }
            } else {
                orderInfo.oilFixedCredit
            },
            gasFixedCredit = if (oilGasConfigSwitch.isTrue) {
                rspOilGasConfigInfo.gasFixedCredit.ifEmpty { orderInfo.gasFixedCredit }
            } else {
                orderInfo.gasFixedCredit
            }
        )
        refreshViewV2()
    }

    fun refreshHugeData(
        orderInfo: HugeOrderInfo,
        orderCommonInfo: RspQueryMobileWholeBagHugeOrderCommonInfo
    ) {
        commonData = CommonData(
            orderModel = orderInfo.orderModel,
            oilConfig = orderCommonInfo.oilConfig,
            oilPercent = orderCommonInfo.oilPercent,
            gasPercent = orderCommonInfo.gasPercent,
            oilPercents = orderCommonInfo.oilPercents,
            gasPercents = orderCommonInfo.gasPercents,
            whetherSupportOilFixedCreditFlag = orderCommonInfo.whetherSupportOilFixedCreditFlag,
            whetherSupportGasFixedCreditFlag = orderCommonInfo.whetherSupportGasFixedCreditFlag,
            oilFixedCreditDefault = orderCommonInfo.oilFixedCreditDefault,
            gasFixedCreditDefault = orderCommonInfo.gasFixedCreditDefault,
            whetherShowSdOilCard = orderCommonInfo.whetherShowSdOilCard,
        )
        viewData = ViewData(
            warning = false,
            supportSdOilCardFlag = orderInfo.supportSdOilCardFlag,
            oilCalculateType = orderInfo.oilCalculateType,
            oilPercent = orderInfo.oilPercent.ifEmpty { orderCommonInfo.oilPercent },
            gasPercent = orderInfo.gasPercent.ifEmpty { orderCommonInfo.gasPercent },
            oilFixedCredit = orderInfo.oilFixedCredit,
            gasFixedCredit = orderInfo.gasFixedCredit,
        )
        refreshViewV2()
    }

    @SuppressLint("SetTextI18n")
    private fun refreshViewV2(isInterfaceBack: Boolean = false) {
        Log.e("XXXXX", "viewData = ${viewData.toJson()} \n commonData = ${commonData.toJson()}")
        // -- 错误标示
        check_support_sd_oil_card.setWarning(viewData.warning)
        if (viewData.warning) {
            setBackgroundColor(getResColor(R.color.red_warning))
        } else {
            setBackgroundColor(getResColor(R.color.white))
        }
        when (commonData.whetherShowSdOilCard) {
            "1" -> {
                setVisible(true)
            }

            else -> {
                viewData.supportSdOilCardFlag = "0"
                setVisible(false)
                return
            }
        }
        ed_fixed.hint = "不能超过不含税价${commonData.oilPercent}%"
        ed_gas_fixed.hint = "不能超过不含税价${commonData.gasPercent}%"
        // -- 是否支持油卡 选项
        // “油/气品”配置：0 不支持油品,1 可选油品,2 强制油品--允许修改，3强制油品--不允许修改
        when (commonData.oilConfig) {
            "3" -> {
                check_support_sd_oil_card.setCanClick(false)
                ed_fixed.isEnabled = false
                ed_gas_fixed.isEnabled = false
                viewData.supportSdOilCardFlag = "1"
                viewData.oilCalculateType=when(viewData.oilCalculateType){
                    "0"->{
                        "1"
                    }else->{
                        viewData.oilCalculateType
                    }
                }
            }

            "2" -> {
                check_support_sd_oil_card.setCanClick(false)
            }

            "1" -> {
                check_support_sd_oil_card.setCanClick(true)
            }

            "0" -> {
                check_support_sd_oil_card.setCanClick(true)
            }

            else -> {
                check_support_sd_oil_card.setCanClick(false)
            }
        }
        // 是否包含油品：0 否,1 是
        when (viewData.supportSdOilCardFlag) {
            "0" -> {
                //勾选不支持 油气品
                check_support_sd_oil_card.check = InputViewCheckV2.RIGHT
                ll_support_view.setVisible(false)
                viewData.supportSdOilCardFlag = "0"
            }

            else -> {
                //勾选支持 油气品
                check_support_sd_oil_card.check = InputViewCheckV2.LEFT
                ll_support_view.setVisible(true)
            }
        }
        // 比例
        // -- 选择框
        when (commonData.orderModel) {
            "0" -> {
                //抢单
                when {
                    commonData.whetherSupportOilFixedCreditFlag.isTrue || commonData.whetherSupportGasFixedCreditFlag.isTrue -> {
                        //支持油品固额或者支持汽品固额 显示才有选择框
                        check_percent.setVisible(true)
                        check_percent.isSelected = viewData.oilCalculateType == "1"
                    }

                    else -> {
                        check_percent.setVisible(false)
                    }
                }
            }
        }
        // 固定额度
        when (commonData.orderModel) {
            "0" -> {
                when {
                    commonData.whetherSupportOilFixedCreditFlag.isTrue -> {
                        ll_fixed.setVisible(true)
                    }

                    commonData.whetherSupportGasFixedCreditFlag.isTrue -> {
                        ll_fixed.setVisible(true)
                    }

                    else -> {
                        ll_fixed.setVisible(false)
                    }
                }
                when (viewData.oilCalculateType) {
                    "2" -> {
                        check_fixed.isSelected = true
                        when (commonData.whetherSupportOilFixedCreditFlag) {
                            "1" -> {
                                //支持固定油品额度
                                ly_oil_content.setVisible(true)
                                val fixed = if (isInterfaceBack) {
                                    commonData.oilFixedCreditDefault
                                } else {
                                    viewData.oilFixedCredit.ifEmpty { commonData.oilFixedCreditDefault }
                                }
                                ed_fixed.setText(fixed)
                                tv_fixed.setTextColor(getResColor(R.color.text_33))
                            }

                            else -> {
                                ly_oil_content.setVisible(false)
                            }
                        }
                        when (commonData.whetherSupportGasFixedCreditFlag) {
                            "1" -> {
                                //支持固定气品额度
                                ly_gas_content.setVisible(true)
                                val fixed = if (isInterfaceBack) {
                                    commonData.gasFixedCreditDefault
                                } else {
                                    viewData.gasFixedCredit.ifEmpty { commonData.gasFixedCreditDefault }
                                }
                                ed_gas_fixed.setText(fixed)
                                tv_gas_fixed.setTextColor(getResColor(R.color.text_33))
                            }

                            else -> {
                                ly_gas_content.setVisible(false)
                            }
                        }
                    }

                    else -> {
                        check_fixed.isSelected = false
                        tv_fixed.setTextColor(getResColor(R.color.text_99))
                        tv_gas_fixed.setTextColor(getResColor(R.color.text_99))
                    }
                }
            }

            else -> {
                ll_fixed.setVisible(false)
            }
        }
        // -- 油比例
        when (viewData.oilCalculateType) {
            "1" -> {
                tv_oil_percent.setTextColor(getResColor(R.color.text_33))
                tv_ges_percent.setTextColor(getResColor(R.color.text_33))
                btn_oil_percent.setVisible(true)
                btn_ges_percent.setVisible(true)
            }

            else -> {
                tv_oil_percent.setTextColor(getResColor(R.color.text_99))
                tv_ges_percent.setTextColor(getResColor(R.color.text_99))
                btn_oil_percent.setVisible(false)
                btn_ges_percent.setVisible(false)
            }
        }
        when {
            viewData.oilPercent.isNotEmpty() && viewData.oilPercent != "0" -> {
                btn_oil_percent.text = viewData.oilPercent + "%"
            }

            commonData.oilPercent.isNotEmpty() && commonData.oilPercent != "0" -> {
                btn_oil_percent.text = commonData.oilPercent + "%"
            }

            else -> {
                btn_oil_percent.text = ""
            }
        }
        // -- 气比例
        // -- 选择框
        when {
            viewData.gasPercent.isNotEmpty() && viewData.gasPercent != "0" -> {
                btn_ges_percent.text = viewData.gasPercent + "%"
            }

            commonData.gasPercent.isNotEmpty() && commonData.gasPercent != "0" -> {
                btn_ges_percent.text = commonData.gasPercent + "%"
            }

            else -> {
                btn_ges_percent.text = ""
            }
        }
    }

    fun setShowRule(show: Boolean = false) {
        showRule = show
        if (!showRule) {
            check_support_sd_oil_card.setTitleRightImg(null)
        } else {
            check_support_sd_oil_card.setTitleRightImg(
                ContextCompat.getDrawable(
                    context,
                    R.drawable.base_info
                )
            )
        }
    }

    fun refreshOilGasPreferential(money: String?) {
        if (TextUtils.equals("0", money)) {
            tvOilGasPreferential.setVisible(false)
            return
        }
        if (money.isNullOrEmpty()) {
            tvOilGasPreferential.setVisible(false)
            return
        }
        //最高可奖励现金80.02元配油奖励用于抵扣运费，以实际到账为准
        val ssb = SpannableStringBuilder()
        val ss1 = SpannableString("最高可奖励现金")
        val ss2 = SpannableString(money + "元配油奖励")
        ss2.setSpan(
            ForegroundColorSpan(getResColor(R.color.color_ff5f1e)),
            0,
            ss2.length,
            Spanned.SPAN_EXCLUSIVE_EXCLUSIVE
        )
        val ss3 = SpannableString("用于抵扣运费，以实际到账为准")
        ssb.append(ss1)
            .append(ss2)
            .append(ss3)
        tvOilGasPreferential.text = ssb
        tvOilGasPreferential.setVisible(true)

    }

    fun setWarning1(warning: Boolean) {
        check_support_sd_oil_card.setWarning(warning)
        if (warning) {
            setBackgroundColor(getResColor(R.color.red_warning))
        } else {
            setBackgroundColor(getResColor(R.color.white))
        }
    }

    data class ViewData(
        var warning: Boolean = false,
        /** 是否包含油品：0 否,1 是 */
        var supportSdOilCardFlag: String = "0",
        /** 油品计算方式：0 默认,1 按比例计算油品,2 按固定额度计算油品 */
        var oilCalculateType: String = "0",
        /** 油品比例 */
        var oilPercent: String = "",
        /** 汽品比例 */
        var gasPercent: String = "",
        //油品固定额度
        var oilFixedCredit: String = "",
        //气品固定额度
        var gasFixedCredit: String = ""
    )

    private data class CommonData(
        /** “油/气品”配置：0 不支持油品,1 可选油品,2 强制油/气--允许修改,3 强制油/气--不允许修改*/
        var oilConfig: String = "",
        /** 订单类型：0 抢单,1 议价(不显示 固定额度 ) */
        var orderModel: String = "",
        /** “油品比例”配置项 */
        var oilPercent: String = "",
        /** “汽品比例"配置项 */
        var gasPercent: String = "",
        /** 发布固定油品比例配置集合 */
        var oilPercents: List<String>? = null,
        /** 发布固定汽品比例配置集合 */
        var gasPercents: List<String>? = null,
        /** 货主是否支持固定油品额度：1 支持，0 不支持 */
        var whetherSupportOilFixedCreditFlag: String = "",
        /** 油品固定额度默认值 */
        var oilFixedCreditDefault: String = "",
        /**货主是否支持固定气品额度：1 支持，0 不支持*/
        var whetherSupportGasFixedCreditFlag: String = "",
        /** 气品固定额度默认值 */
        var gasFixedCreditDefault: String = "",
        /** 是否德达油卡 1 是 0 否*/
        var whetherDedaUserId: String = "",
        /** 是否展示油气品模块 1展示 0 不展示 */
        var whetherShowSdOilCard: String = "",
    )

    interface Listener {
        fun onDataChange(data: ViewData)
        fun onFillFixedAmount(fillFixedAmount: String)
        fun onGasFixedAmount(fillFixedAmount: String)
        fun onClickFixed(): Boolean
    }
}