package com.zczy.cargo_owner.deliver.addorder.dialog

import android.os.Bundle
import android.view.View
import com.zczy.cargo_owner.deliver.R
import com.zczy.comm.ui.BaseDialog
import kotlinx.android.synthetic.main.deliver_new_goods_time_dialog.view.*

/**
 * PS: 定价模式说明
 * Created by sdx on 2019/2/15.
 */
class DeliverNewGoodsModelDialog : BaseDialog() {
    override fun getDialogTag(): String = "DeliverNewGoodsTimeDialog"

    override fun getDialogLayout(): Int = R.layout.deliver_new_goods_model_dialog

    override fun bindView(view: View, bundle: Bundle?) {
        reAdjustView(35, 0)
        initView(view)
    }

    private fun initView(view: View) {
        view.btn_right.setOnClickListener {
            dismiss()
        }
    }
}