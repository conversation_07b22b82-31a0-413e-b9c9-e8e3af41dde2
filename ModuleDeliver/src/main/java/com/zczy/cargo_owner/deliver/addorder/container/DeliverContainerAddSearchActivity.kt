package com.zczy.cargo_owner.deliver.addorder.container

import android.app.Activity
import android.content.Intent
import android.os.Bundle
import android.view.View
import com.chad.library.adapter.base.BaseQuickAdapter
import com.chad.library.adapter.base.listener.OnItemClickListener
import com.sfh.lib.mvvm.annotation.LiveDataMatch
import com.zczy.cargo_owner.deliver.R
import com.zczy.cargo_owner.deliver.addorder.container.adapter.DeliverContainerAddSearchAdapter
import com.zczy.cargo_owner.deliver.addorder.container.model.DeliverContainerAddSearchModel
import com.zczy.cargo_owner.deliver.addorder.container.req.ReqQueryContainerPageList
import com.zczy.cargo_owner.deliver.addorder.container.req.RspDeliverContainerList
import com.zczy.comm.http.entity.PageList
import com.zczy.comm.ui.BaseActivity
import com.zczy.comm.utils.dp2px
import com.zczy.comm.widget.pulltorefresh.CommEmptyView
import kotlinx.android.synthetic.main.deliver_container_add_search_activity.*

/**
 * 功能描述: 新增集装箱
 * <AUTHOR>
 * @date 2022/2/24-15:21
 */

class DeliverContainerAddSearchActivity : BaseActivity<DeliverContainerAddSearchModel>() {

    private val mAdapter = DeliverContainerAddSearchAdapter()
    private var searchTxt: String? = null

    companion object {
        const val DELIVER_SWITCH_DATA = "deliver_switch_data"
        const val DELIVER_SWITCH_REQUEST_CODE = 0X100

        @JvmStatic
        fun jumpUi(activity: Activity, requestCode: Int) {
            val intent = Intent(activity, DeliverContainerAddSearchActivity::class.java)
            activity.startActivityForResult(intent, requestCode)
        }

        @JvmStatic
        fun receiptData(data: Intent?): RspDeliverContainerList? {
            return data?.getParcelableExtra(DELIVER_SWITCH_DATA)
        }
    }

    override fun getLayout(): Int {
        return R.layout.deliver_container_add_search_activity
    }

    private val onItemClickListener = object : OnItemClickListener() {
        override fun onSimpleItemClick(adapter: BaseQuickAdapter<*, *>, view: View, position: Int) {


        }

        override fun onItemChildClick(adapter: BaseQuickAdapter<*, *>, view: View, position: Int) {
            super.onItemChildClick(adapter, view, position)
            when (view.id) {
                R.id.imgSelect -> {
                    mAdapter.setSwitchItem(position)
                }
            }
        }
    }

    override fun bindView(bundle: Bundle?) {
        search_bar.setHint("集装箱名称")
        search_bar.setSearchEmpty(true)
        search_bar.onSearchBlock = {
            searchTxt = it
            val reqQueryContainerPageList = ReqQueryContainerPageList(
                containerName = searchTxt ?: "",
                containerStatus = "0",
                nowPage = 1,
                pageSize = 10
            )
            viewModel?.queryList(reqQueryContainerPageList)
        }
        search_bar.onClearBlock = {
            searchTxt = ""
            val reqQueryContainerPageList = ReqQueryContainerPageList(
                containerName = searchTxt ?: "",
                containerStatus = "0",
                nowPage = 1,
                pageSize = 10
            )
            viewModel?.queryList(reqQueryContainerPageList)
        }
        tvBtn.setOnClickListener {
            intent.putExtra(DELIVER_SWITCH_DATA, mAdapter.getSwitchItem())
            setResult(Activity.RESULT_OK, intent)
            finish()
        }
        refresh_deliver.apply {
            addItemDecorationSize(dp2px(0f))
            setAdapter(mAdapter, true)
            setEmptyView(CommEmptyView.creatorDef(context))
            onAutoRefresh()
            setOnLoadListener2 { nowPage ->
                val reqQueryContainerPageList = ReqQueryContainerPageList(
                    containerName = searchTxt ?: "",
                    containerStatus = "0",
                    nowPage = nowPage,
                    pageSize = 10
                )
                viewModel?.queryList(reqQueryContainerPageList)
            }
            addOnItemListener(onItemClickListener)
        }
    }

    override fun initData() {

    }

    @LiveDataMatch(tag = "集装箱列表查询接口")
    open fun onQuerySuccess(data: PageList<RspDeliverContainerList>?) {
        data?.apply {
            refresh_deliver.onRefreshCompale(this)
        }
        if (data == null) {
            refresh_deliver.onLoadMoreFail()
        }
    }
}