package com.zczy.cargo_owner.deliver.addorder.container

import android.app.Activity
import android.content.Intent
import android.os.Bundle
import androidx.recyclerview.widget.LinearLayoutManager
import android.view.View
import com.chad.library.adapter.base.BaseQuickAdapter
import com.chad.library.adapter.base.listener.OnItemClickListener
import com.sfh.lib.event.RxBusEventManager
import com.zczy.cargo_owner.deliver.R
import com.zczy.cargo_owner.deliver.addorder.container.DeliverContainerSelectActivity.Companion.EXTRA_INDEX_INT
import com.zczy.cargo_owner.deliver.addorder.container.adapter.DeliverContainerListAdapterV1
import com.zczy.cargo_owner.deliver.addorder.container.model.DeliverContainerListModelV1
import com.zczy.cargo_owner.deliver.addorder.container.req.RxBusAddContainerData
import com.zczy.cargo_owner.deliver.addorder.req.container.ContainerList
import com.zczy.cargo_owner.deliver.addorder.ui.DeliverGoodsContainerDetailsActivity
import com.zczy.comm.utils.dp2px
import com.zczy.comm.utils.ex.yes
import com.zczy.comm.widget.itemdecoration.SpaceItemDecoration
import kotlinx.android.synthetic.main.deliver_container_list_activity_v1.*

/**
 * 功能描述: 集装箱列表修改
 * <AUTHOR>
 * @date 2022/3/2-10:20
 */

class DeliverContainerListActivityV1 : BaseActivityV1<DeliverContainerListModelV1>() {

    private val mAdapter = DeliverContainerListAdapterV1()
    private val list = arrayListOf<ContainerList>()
    private val seletList = arrayListOf<ContainerList>()
    private var switchItem: ContainerList? = null

    companion object {

        const val DELIVER_SWITCH_DATA = "deliver_switch_data"
        const val DELIVER_SWITCH_DATA_ITEM = "deliver_switch_data_item"

        @JvmStatic
        fun jumpUi(
            activity: Activity,
            requestCode: Int,
            switchItem: ContainerList?,
            list: ArrayList<ContainerList>,
            index: Int
        ) {
            val intent = Intent(activity, DeliverContainerListActivityV1::class.java)
            intent.putExtra(DELIVER_SWITCH_DATA_ITEM, switchItem)
            intent.putExtra(DELIVER_SWITCH_DATA, list)
            intent.putExtra(EXTRA_INDEX_INT, index)
            activity.startActivityForResult(intent, requestCode)
        }

        @JvmStatic
        fun receiptDataV1(data: Intent?): ArrayList<ContainerList>? {
            return data?.getParcelableArrayListExtra<ContainerList>(DELIVER_SWITCH_DATA)
        }

        @JvmStatic
        fun receiptDataV2(data: Intent?): ContainerList? {
            return data?.getParcelableExtra(DELIVER_SWITCH_DATA_ITEM)
        }

        fun obtainIndex(intent: Intent?): Int {
            return intent?.getIntExtra(EXTRA_INDEX_INT, -1) ?: -1
        }

    }

    override val layout: Int
        get() = R.layout.deliver_container_list_activity_v1

    private val onItemClickListener = object : OnItemClickListener() {
        override fun onSimpleItemClick(adapter: BaseQuickAdapter<*, *>, view: View, position: Int) {

        }

        override fun onItemChildClick(adapter: BaseQuickAdapter<*, *>, view: View, position: Int) {
            super.onItemChildClick(adapter, view, position)
            when (view.id) {
                R.id.tvTrash -> {
                    //删除
                    mAdapter.remove(position)
                    refreshTopItemV2()
                }
            }
        }
    }

    override fun bindView(bundle: Bundle?) {
        bindClickEvent(tvEditContainer)
        bindClickEvent(tvAddContainer)
        bindClickEvent(tvSaveContainer)
        recyclerView.apply {
            layoutManager =
                androidx.recyclerview.widget.LinearLayoutManager(this@DeliverContainerListActivityV1)
            addItemDecoration(SpaceItemDecoration(dp2px(0f)))
            mAdapter.bindToRecyclerView(recyclerView)
            adapter = mAdapter
            addOnItemTouchListener(onItemClickListener)
        }
    }

    override fun onSingleClick(v: View) {
        when (v.id) {
            R.id.tvEditContainer -> {
                //返回集装箱列表选择页
                DeliverContainerSelectActivity.jumpUi(
                    activity = this@DeliverContainerListActivityV1,
                    requestCode = DeliverGoodsContainerDetailsActivity.REQUEST_GOODS_CONTAINER,
                    index = obtainIndex(intent)
                )
                finish()
            }
            R.id.tvAddContainer -> {
                //继续添加集装箱内容
                mAdapter.addData(
                    mAdapter.data.size,
                    ContainerList(
                        containerId = switchItem?.containerId ?: "",
                        containerName = switchItem?.containerName ?: ""
                    )
                )
                refreshTopItemV1()
            }
            R.id.tvSaveContainer -> {
                //保存当前集装箱数据
                checkAll().yes {
                    RxBusEventManager.postEvent(
                        RxBusAddContainerData(
                            data1 = seletList,
                            data2 = switchItem,
                            data3 = obtainIndex(intent)
                        )
                    )
                    finish()
                }
            }
        }
    }

    private fun checkAll(): Boolean {
        seletList.clear()
        mAdapter.data.forEachIndexed { index, item ->
            val empty = when {
                item.containerNo.isEmpty() -> {
                    showDialogToast("箱号内容不能为空!")
                    true
                }
                item.containerUnitWeight.isEmpty() -> {
                    showDialogToast("重量不能为空!")
                    true
                }
                else -> {
                    false
                }
            }
            empty.yes {
                recyclerView.scrollToPosition(index)
                return false
            }
            seletList.add(item)
        }
        return true
    }

    private fun refreshTopItemV1() {
        if (mAdapter.data.size == 2) {
            mAdapter.notifyItemChanged(0, mAdapter.data[0])
        }
    }

    private fun refreshTopItemV2() {
        if (mAdapter.data.size <= 1) {
            mAdapter.notifyItemChanged(0, mAdapter.data[0])
        }
    }

    override fun initData() {
        receiptDataV1(intent)?.let { list.addAll(it) }
        switchItem = receiptDataV2(intent)
        if (list.isEmpty()) {
            list.add(switchItem ?: ContainerList())
        }
        mAdapter.setNewData(list)
        switchItem?.apply {
            tvContainerName.text = containerName
        }
    }

}