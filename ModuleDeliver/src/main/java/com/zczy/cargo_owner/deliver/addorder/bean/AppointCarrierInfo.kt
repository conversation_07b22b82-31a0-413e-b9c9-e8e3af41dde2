package com.zczy.cargo_owner.deliver.addorder.bean

import com.example.libaes.aesUtils.AESUtils
import com.zczy.cargo_owner.deliver.DeliverConstant

data class AppointCarrierInfo(
    var userId: String = "", // 指定承运方用户id
    var mobile: String = "", // 指定承运方手机号
    var userName: String = "", // 指定承运方姓名
    var userType: String = "", // 指定承运方用户类型
    var inBlackList: String = "", // 是否是黑名单 0否 1是
    var isDelete: String = "", // 是否删除  0否 1是
    var ifExist: String = "", // 指定承运方是否关联货主
    var recommendLevel: String = "" //推荐星级
)

fun Iterable<AppointCarrierInfo>.formatStr(): String {
    val sb = StringBuilder()
    forEach {
        sb.append(",")
        sb.append(it.userName)
    }

    if (sb.isNotEmpty()) {
        sb.delete(0, 1)
    }
    return sb.toString()
}

fun AppointCarrierInfo.hidePhoneNum(): String? {
    val decrypt = AESUtils.decrypt(mobile, DeliverConstant.HZ_AES_MOBILE_DECRYPT)
    val length = decrypt.length
    return if (length == 11)
        decrypt.replaceRange(length - 8, length - 4, "****")
    else
        decrypt
}