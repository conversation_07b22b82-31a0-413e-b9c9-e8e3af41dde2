package com.zczy.cargo_owner.deliver.main.ui

import android.app.Activity
import android.content.Context
import android.content.Intent
import android.os.Bundle
import android.text.TextUtils
import android.view.View
import com.aliyun.auikits.aicall.controller.ARTCAICallController
import com.chad.library.adapter.base.BaseQuickAdapter
import com.chad.library.adapter.base.listener.OnItemClickListener
import com.sfh.lib.mvvm.annotation.LiveDataMatch
import com.sfh.lib.mvvm.service.BaseViewModel
import com.zczy.cargo_owner.deliver.R
import com.zczy.cargo_owner.deliver.addorder.DeliverAddOrderMainActivity
import com.zczy.cargo_owner.deliver.addorder.JumpNewGoodsData
import com.zczy.cargo_owner.deliver.drafts.ui.DeliverDraftsEditActivity
import com.zczy.cargo_owner.deliver.main.adapter.DeliverFastAdapter
import com.zczy.cargo_owner.deliver.main.model.DeliverFastModel
import com.zczy.cargo_owner.deliver.main.req.RspQueryQuickReleaseItem
import com.zczy.cargo_owner.deliver.main.widget.DeliverEmptyView
import com.zczy.cargo_owner.libcomm.config.SubUserAuthUtils
import com.zczy.cargo_owner.libcomm.utils.getChildPermission
import com.zczy.cargo_owner.order.detail.req.ReqQuerySingleDictConfig
import com.zczy.comm.http.entity.PageList
import com.zczy.comm.ui.BaseFragment
import com.zczy.comm.utils.dp2px
import com.zczy.comm.utils.ex.setVisible
import com.zczy.comm.widget.pulltorefresh.OnLoadingListener
import kotlinx.android.synthetic.main.deliver_main_part_fragment.refresh_more_layout
import kotlinx.android.synthetic.main.deliver_main_part_fragment.view_btn_ai_deliver
import kotlinx.android.synthetic.main.deliver_main_part_fragment.view_btn_deliver
import kotlinx.android.synthetic.main.deliver_main_part_fragment.view_has_permission
import kotlinx.android.synthetic.main.deliver_main_part_fragment.view_no_permission

/**
 * sdx
 * 主页的发货 快速发货 fragment
 */
open class DeliverFastNormalFragment : BaseFragment<DeliverFastModel>() {

    private val mAdapter = DeliverFastAdapter()

    override fun getLayout(): Int = R.layout.deliver_main_part_fragment

    override fun bindView(view: View, bundle: Bundle?) {
        initList()
        initBtn()
        view_btn_deliver.setVisible(false)
        view_btn_ai_deliver.setVisible(false)
    }

    override fun initData() {
        // 子账号权限
        if (SubUserAuthUtils.isChild()) {
            if (SubUserAuthUtils.get().moCommonOrder.getChildPermission().isEmpty()) {
                view_no_permission.setVisible(false)
                view_has_permission.setVisible(true)
                refresh_more_layout.onAutoRefresh()
            } else {
                view_no_permission.setVisible(true)
                view_has_permission.setVisible(false)
            }
        } else {
            view_no_permission.setVisible(false)
            view_has_permission.setVisible(true)
            refresh_more_layout.onAutoRefresh()
        }
        val req = ReqQuerySingleDictConfig()
        req.dictCode = "CONSIGNOR_APP_SUPPORT_AI_ORDER"
        this.getViewModel(BaseViewModel::class.java).execute(req) {
            if (it.success()) {
                //AI 发货
                view_btn_ai_deliver.setVisible(TextUtils.equals("是", it.data?.value))
            } else {
                view_btn_ai_deliver.setVisible(false)
            }
        }
    }

    override fun onActivityResult(requestCode: Int, resultCode: Int, data: Intent?) {
        super.onActivityResult(requestCode, resultCode, data)
        if (resultCode == Activity.RESULT_OK) {
            when (requestCode) {
                REQUEST_RE -> {
                    initData()
                }
            }
        }
    }

    private fun initList() {
        refresh_more_layout.apply {
            addItemDecorationSize(dp2px(7f))
            setAdapter(mAdapter, false)
            val empty = DeliverEmptyView(context)
            empty.onClickDeliver = this@DeliverFastNormalFragment::onClickDeliver
            empty.setMessage("完成订单后可快速发货")
            setEmptyView(empty)
            addOnItemListener(onItemClickListener)
            setOnLoadListener(object : OnLoadingListener {
                override fun onRefreshUI(nowPage: Int) {
                    viewModel?.queryList(nowPage)
                }

                override fun onLoadMoreUI(nowPage: Int) {
                    viewModel?.queryList(nowPage)
                }
            })
        }
    }

    private fun initBtn() {
        view_btn_deliver.setOnClickListener { onClickDeliver() }
        view_btn_ai_deliver.setOnClickListener {
            // AI 发货
            ARTCAICallController.launchCallActivity(activity){
                AiResultForPublishActivity.start(context,it.first,it.second)
            }
        }
    }

    private var lastTime = 0L;

    private fun onClickDeliver() {
        val nowTime = System.currentTimeMillis()
        if (nowTime - lastTime < 500) {
            lastTime = nowTime
            return
        }
        lastTime = nowTime
        // 查询用户是否被冻结
        viewModel?.checkUserIsFreeze(
            unFreeze = {
                DeliverAddOrderMainActivity.start(this)
            })
    }

    private val onItemClickListener = object : OnItemClickListener() {
        override fun onSimpleItemClick(adapter: BaseQuickAdapter<*, *>, view: View, position: Int) {}

        override fun onItemChildClick(adapter: BaseQuickAdapter<*, *>, view: View, position: Int) {
            super.onItemChildClick(adapter, view, position)
            val item = adapter.getItem(position)
            if (item is RspQueryQuickReleaseItem) {
                when (view.id) {
                    // 再来一单
                    R.id.btn_again -> {
                        // 查询用户是否被冻结
                        viewModel?.checkUserIsFreeze(
                            unFreeze = {
                                val data = JumpNewGoodsData(
                                    orderId = item.orderId,
                                    specifyFlag = item.specifyFlag,
                                    pageState = JumpNewGoodsData.PageState.重新发布,
                                    orderType = "0"
                                )
                                DeliverDraftsEditActivity.start(
                                    this@DeliverFastNormalFragment,
                                    data,
                                    "发布货源",
                                    REQUEST_RE
                                )
                            })
                    }
                }
            }
        }
    }

    @LiveDataMatch
    open fun onQueryListSuccess(data: PageList<RspQueryQuickReleaseItem>?) {
        if (data?.nowPage == 1 && data.rootArray?.isNotEmpty() == true) {
            view_btn_deliver.setVisible(true)
        }
        refresh_more_layout.onRefreshCompale(data)
    }

    companion object {
        private const val REQUEST_RE = 0x44

        @JvmStatic
        fun instance(context: Context): DeliverFastNormalFragment {
            val data = Bundle()
            return androidx.fragment.app.Fragment.instantiate(context, DeliverFastNormalFragment::class.java.name, data) as DeliverFastNormalFragment
        }
    }
}
