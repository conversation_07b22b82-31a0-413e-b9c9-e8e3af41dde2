package com.zczy.cargo_owner.deliver.addorder.container

import android.content.Context
import android.content.Intent
import android.os.Bundle
import androidx.fragment.app.Fragment
import com.flyco.tablayout.listener.CustomTabEntity
import com.sfh.lib.mvvm.service.BaseViewModel
import com.zczy.cargo_owner.deliver.R
import com.zczy.cargo_owner.deliver.addorder.container.fragment.DeliverContainerListFragment
import com.zczy.comm.ui.BaseActivity
import com.zczy.comm.widget.tablayout.CommonTabEntity
import kotlinx.android.synthetic.main.deliver_container_list_activity.*

/**
 * 功能描述: 集装箱列表
 * <AUTHOR>
 * @date 2022/2/23-10:20
 */

class DeliverContainerListActivity : BaseActivity<BaseViewModel>() {
    private val fragments = ArrayList<androidx.fragment.app.Fragment>()
    private val tabEntities: ArrayList<CustomTabEntity> = arrayListOf()

    companion object {

        @JvmStatic
        fun jumpUi(context: Context) {
            val intent = Intent(context, DeliverContainerListActivity::class.java)
            context.startActivity(intent)
        }
    }

    override fun getLayout(): Int {
        return R.layout.deliver_container_list_activity
    }

    override fun bindView(bundle: Bundle?) {
        ivBack.setOnClickListener {
            finish()
        }
        ivAdd.setOnClickListener {
            //新增集装箱
            DeliverContainerAddActivity.jumpUi(this@DeliverContainerListActivity)
        }
        ivSearch.setOnClickListener {
            //搜索集装箱
            DeliverContainerSearchActivity.jumpUi(this@DeliverContainerListActivity)
        }
        tabEntities.add(CommonTabEntity("可用"))
        tabEntities.add(CommonTabEntity("禁用"))
        fragments.add(DeliverContainerListFragment.instanceFragment(DeliverContainerListFragment.CONTAINER_TYPE_V1))
        fragments.add(DeliverContainerListFragment.instanceFragment(DeliverContainerListFragment.CONTAINER_TYPE_V2))
        common_tab_layout.setTabData(tabEntities, this, R.id.container, fragments)
    }

    override fun initData() {

    }
}