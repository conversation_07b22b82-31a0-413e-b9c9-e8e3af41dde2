package com.zczy.cargo_owner.deliver.addorder.widget

import android.content.Context
import android.text.SpannableString
import android.text.SpannableStringBuilder
import android.text.Spanned
import android.text.style.ForegroundColorSpan
import androidx.constraintlayout.widget.ConstraintLayout
import android.util.AttributeSet
import android.view.LayoutInflater
import com.jakewharton.rxbinding2.widget.RxTextView
import com.zczy.cargo_owner.deliver.R
import com.zczy.cargo_owner.deliver.addorder.util.toast
import com.zczy.comm.utils.CommUtils
import com.zczy.comm.utils.ex.setVisible
import com.zczy.comm.utils.ex.toDoubleRoundDown
import com.zczy.comm.utils.getResColor
import io.reactivex.android.schedulers.AndroidSchedulers
import io.reactivex.disposables.Disposable
import kotlinx.android.synthetic.main.deliver_add_order_cargo_money_view.view.*
import java.util.concurrent.TimeUnit

/**
 * PS: 发货 view
 * Created by sdx on 2019/3/7.
 */
class AddOrderCargoMoneyView : ConstraintLayout {

    var listener: Listener? = null
    private var disposable: Disposable? = null

    private var filterFlag = true

    constructor(context: Context) : super(context)

    constructor(context: Context, attrs: AttributeSet?) : super(context, attrs)

    constructor(context: Context, attrs: AttributeSet?, defStyleAttr: Int) : super(
        context,
        attrs,
        defStyleAttr
    )

    init {
        LayoutInflater.from(context).inflate(R.layout.deliver_add_order_cargo_money_view, this)
        initView()
        setEditEnable(false)
    }

    private fun initView() {
        child_edit_cargo_money_click.setOnClickListener {
            listener?.onClick()
        }
        child_edit_cargo_money.setTypeNum()
        CommUtils.setEditTextInputType(child_edit_cargo_money.editText, 2)

        disposable = RxTextView.textChanges(child_edit_cargo_money.editText)
            .map(CharSequence::toString)
            .skip(1)
            .filter {
                filterFlag
            }
            .map {
                var s = it
                try {
                    when {
                        it.toDoubleRoundDown() >= 5000000.00 -> {
                            toast("超过最高值,请检查后再输入!")
                            child_edit_cargo_money.setWarning(true)
                            s = ""
                        }

                        it.toDoubleRoundDown() <= 100.00 -> {
                            toast("货值需大于100元，请重新输入！")
                            child_edit_cargo_money.setWarning(true)
                            s = ""
                        }

                        else -> {
                            child_edit_cargo_money.setWarning(false)
                        }
                    }
                } catch (e: Exception) {
                    e.printStackTrace()
                }
                s
            }
            .debounce(100, TimeUnit.MILLISECONDS)
            .observeOn(AndroidSchedulers.mainThread())
            .subscribe {
                listener?.onTextChanged(it)
            }
    }

    override fun onDetachedFromWindow() {
        super.onDetachedFromWindow()
        disposable?.dispose()
    }

    fun setEditEnable(enable: Boolean) {
        child_edit_cargo_money_click.setVisible(!enable)
        child_edit_cargo_money.editText.isEnabled = enable
    }

    fun setData(cargoMoney: String) {
        filterFlag = false
        if (cargoMoney != child_edit_cargo_money.content) {
            child_edit_cargo_money.content = cargoMoney
            child_edit_cargo_money.editText.setSelection(cargoMoney.length)
        }
        child_edit_cargo_money.setTitle2("")
        filterFlag = true
    }

    fun setWarning(b: Boolean) {
        child_edit_cargo_money.setWarning(b)
    }

    fun getPolicyMoney(): String {
        return child_edit_cargo_money.content
    }

    fun setTitle2(averageValue: String?) {
        if (averageValue.isNullOrEmpty()) {
            child_edit_cargo_money.setTitle2("")
        } else {
            child_edit_cargo_money.setTitle2("参考货值：" + averageValue + "元")
        }
    }

    fun setTitleV1() {
        child_edit_cargo_money.title = "运单货值"
    }

    interface Listener {
        fun onClick()
        fun onTextChanged(s: String)
    }

}