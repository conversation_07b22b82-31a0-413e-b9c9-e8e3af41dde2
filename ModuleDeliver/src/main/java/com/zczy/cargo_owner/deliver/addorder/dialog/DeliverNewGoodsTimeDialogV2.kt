package com.zczy.cargo_owner.deliver.addorder.dialog

import android.graphics.Color
import android.os.Bundle
import android.text.SpannableString
import android.text.SpannableStringBuilder
import android.text.Spanned
import android.text.style.ForegroundColorSpan
import android.view.View
import com.zczy.cargo_owner.deliver.R
import com.zczy.comm.ui.BaseDialog
import com.zczy.comm.utils.ex.setVisible
import kotlinx.android.synthetic.main.deliver_new_goods_time_dialog.*

/**
 * PS:
 * Created by sdx on 2019/2/15.
 */
class DeliverNewGoodsTimeDialogV2 : BaseDialog() {
    private var mData: DeliverNewGoodsTimeData? = null

    override fun getDialogTag(): String = "DeliverNewGoodsTimeDialog"

    override fun getDialogLayout(): Int = R.layout.deliver_new_goods_time_dialog

    override fun bindView(view: View, bundle: Bundle?) {
        reAdjustView(35, 0)
        initView(view)
    }

    private fun initView(view: View) {
        tv_content_1.text = getContent1()
        btn_right.setOnClickListener {
            dismiss()
        }
    }

    private fun getContent1(): SpannableStringBuilder {
        val data = mData
        val sb = SpannableStringBuilder()
        val span = SpannableString("最早到场装货时间需在当前时间1小时之后，")
        span.setSpan(ForegroundColorSpan(Color.parseColor("#FF5F1E")), 0, span.length, Spanned.SPAN_INCLUSIVE_EXCLUSIVE)
        sb.append(span)
        if (data != null) {
            val span2 = SpannableString("最晚到场装货时间需在当前时间至少${data.validity}小时之后，")
            span2.setSpan(ForegroundColorSpan(Color.parseColor("#FF5F1E")), 0, span2.length, Spanned.SPAN_INCLUSIVE_EXCLUSIVE)
            sb.append(span2)
        } else {
            tv_title_1.setVisible(false)
        }
        sb.append("以保障承运方有足够的时间到装货地装货；")
        return sb
    }


    fun setData(data: DeliverNewGoodsTimeData): DeliverNewGoodsTimeDialogV2 {
        this.mData = data
        return this
    }
}