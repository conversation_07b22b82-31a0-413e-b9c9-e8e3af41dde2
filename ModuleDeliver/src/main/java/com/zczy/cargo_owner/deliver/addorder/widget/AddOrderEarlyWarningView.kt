package com.zczy.cargo_owner.deliver.addorder.widget

import android.content.Context
import android.graphics.Color
import android.text.Editable
import android.text.TextUtils
import android.text.TextWatcher
import android.util.AttributeSet
import android.view.LayoutInflater
import android.widget.LinearLayout
import com.zczy.cargo_owner.deliver.R
import com.zczy.comm.utils.CommUtils
import com.zczy.comm.utils.ex.setVisible
import com.zczy.comm.widget.inputv2.InputViewCheckV2
import kotlinx.android.synthetic.main.deliver_add_order_early_warning_view.view.*

/**
 * 功能描述: 余量预警
 * <AUTHOR>
 * @date 2022/1/28-14:41
 */

class AddOrderEarlyWarningView : LinearLayout {

    var onCheckListener: OnCheckListener? = null
    private var viewData = ViewData()

    constructor(context: Context) : super(context)

    constructor(context: Context, attrs: AttributeSet?) : super(context, attrs)

    constructor(context: Context, attrs: AttributeSet?, defStyleAttr: Int) : super(
        context,
        attrs,
        defStyleAttr
    )

    init {
        orientation = VERTICAL
        setBackgroundColor(Color.WHITE)
        LayoutInflater.from(context).inflate(R.layout.deliver_add_order_early_warning_view, this)

        input_early_warning.setListener(object : InputViewCheckV2.Listener() {
            override fun onClickCheck(
                viewId: Int,
                view: InputViewCheckV2,
                check: Int,
                radioStr: String
            ): Boolean {
                if (TextUtils.isEmpty(viewData.upperLimitValue)) {
                    onCheckListener?.onMsgAlert("请填写货物总量!")
                    return false
                }
                when (check) {
                    InputViewCheckV2.LEFT -> {
                        onCheckListener?.onChecked("1")
                        edit_early_warning.setVisible(true)
                    }

                    InputViewCheckV2.RIGHT -> {
                        onCheckListener?.onChecked("0")
                        edit_early_warning.setVisible(false)
                    }
                }
                return true
            }
        })
        CommUtils.setEditTextInputType(edit_early_warning.editText, 4)
        edit_early_warning.setTypeNum()
        edit_early_warning.editText.addTextChangedListener(object : TextWatcher {
            override fun beforeTextChanged(s: CharSequence?, start: Int, count: Int, after: Int) {

            }

            override fun onTextChanged(s: CharSequence?, start: Int, before: Int, count: Int) {
                val checkEarlyWarning = checkEarlyWarning(s.toString().trim())
                onCheckListener?.onEditWarningSuccess(
                    checkEarlyWarning,
                    s.toString().trim()
                )
                showTvEarlyWarningMsg(checkEarlyWarning)
            }

            override fun afterTextChanged(s: Editable?) {

            }
        })
    }

    fun checkEarlyWarning(s: String?): String {
        var earlyWarning = s.toString()
        if (TextUtils.equals(".", earlyWarning) || earlyWarning.isEmpty()) {
            earlyWarning = "0.00"
        }
        val toDoubleA = earlyWarning.toDouble()
        var a = viewData.floorConfig
        if (a.isEmpty()) {
            a = "0.00"
        }
        val toDoubleB = a.toDouble()
        var b = viewData.upperLimitValue
        if (TextUtils.equals(".",b) || b.isEmpty()) {
            b = "0.00"
        }
        val toDoubleC = b.toDouble()
        return when {
            toDoubleA <= toDoubleB -> {
                "请输入大于下限" + viewData.floorConfig + "吨，小于总量的值" + viewData.upperLimitValue
            }

            toDoubleA >= toDoubleC -> {
                "请输入大于下限" + viewData.floorConfig + "吨，小于总量的值" + viewData.upperLimitValue
            }

            else -> {
                ""
            }
        }
    }

    fun refreshData(needWarningFlag: String, warningWeight: String, floorConfig: String) {
        viewData.needWarningFlag = needWarningFlag
        viewData.warningWeight = warningWeight
        viewData.floorConfig = floorConfig
    }

    fun refreshUpperLimitValue(upperLimitValue: String) {
        viewData.upperLimitValue = upperLimitValue
        refreshView()
        checkEarlyWarning(viewData.warningWeight)
    }

    private fun refreshView() {
        if (TextUtils.isEmpty(viewData.warningWeight) && TextUtils.isEmpty(viewData.needWarningFlag)) {
            //发新货的余量预警
            edit_early_warning.setVisible(false)
            tvEarlyWarningMsg.setVisible(false)
            input_early_warning.setVisible(true)
            input_early_warning.check = InputViewCheckV2.RIGHT
            onCheckListener?.onChecked("0")
        } else {
            if (TextUtils.equals(viewData.needWarningFlag, "1")) {
                //支持余量预警
                edit_early_warning.setVisible(true)
                input_early_warning.setVisible(true)
                input_early_warning.check = InputViewCheckV2.LEFT
                edit_early_warning.editText.setText(viewData.warningWeight)
                onCheckListener?.onChecked("1")
            } else {
                //不支持余量预警
                edit_early_warning.setVisible(false)
                tvEarlyWarningMsg.setVisible(false)
                input_early_warning.setVisible(true)
                input_early_warning.check = InputViewCheckV2.RIGHT
                onCheckListener?.onChecked("0")
            }
        }
    }

    private fun showTvEarlyWarningMsg(s: String) {
        if (TextUtils.isEmpty(s)) {
            tvEarlyWarningMsg.setVisible(false)
            tvEarlyWarningMsg.text = ""
        } else {
            tvEarlyWarningMsg.setVisible(true)
            tvEarlyWarningMsg.text = s
        }
    }

    data class ViewData(
        var needWarningFlag: String = "",//是否需要余量预警:0 否,1 是
        var floorConfig: String = "",//下限值
        var upperLimitValue: String = "",//上限值
        var warningWeight: String = "0"//预警值
    )

    interface OnCheckListener {
        fun onChecked(needWarningFlag: String)
        fun onMsgAlert(warningMsg: String)
        fun onEditWarningSuccess(msg: String, s: String)
    }
}