package com.zczy.cargo_owner.deliver.cargo_name_management

import android.content.Context
import android.content.Intent
import android.os.Bundle
import androidx.recyclerview.widget.ItemTouchHelper
import com.chad.library.adapter.base.callback.ItemDragAndSwipeCallback
import com.sfh.lib.mvvm.annotation.LiveDataMatch
import com.zczy.cargo_owner.deliver.R
import com.zczy.cargo_owner.deliver.cargo_name_management.adapter.CargoNameSortAdapter
import com.zczy.cargo_owner.deliver.cargo_name_management.bean.CargoNameItemBean
import com.zczy.cargo_owner.deliver.cargo_name_management.bean.SortCargoName
import com.zczy.cargo_owner.deliver.cargo_name_management.model.CargoNameManagementModel
import com.zczy.comm.http.entity.PageList
import com.zczy.comm.ui.BaseActivity
import com.zczy.comm.utils.dp2px
import com.zczy.comm.widget.pulltorefresh.CommEmptyView
import com.zczy.comm.widget.pulltorefresh.OnLoadingListener
import kotlinx.android.synthetic.main.deliver_sort_cargo_name.*


/**
 * @description
 * <AUTHOR> 韩正亚
 * @company 中储智运科技股份有限公司
 * @copyright （版权）中储南京智慧物流科技有限公司所有
 * @since 3/18/20 10:49
 */
class SortCargoNameActivity : BaseActivity<CargoNameManagementModel>() {

    private val adapter = CargoNameSortAdapter()

    override fun getLayout(): Int {
        return R.layout.deliver_sort_cargo_name
    }

    override fun bindView(bundle: Bundle?) {

        toolbar.setRightOnClickListener {
            val commonlyUsedCargoNameList = mutableListOf<CargoNameItemBean>()
            val uncommonlyUsedCargoNameList = mutableListOf<CargoNameItemBean>()

            adapter.data.map {
                if (it.offenUseingFlag == "1") {
                    commonlyUsedCargoNameList.add(it)
                } else {
                    uncommonlyUsedCargoNameList.add(it)
                }
            }

            val commonlyUsedIds = constructingParameters(commonlyUsedCargoNameList)
            val uncommonlyUsedIds = constructingParameters(uncommonlyUsedCargoNameList)

            viewModel?.sortCargoNameList(commonlyUsedIds = commonlyUsedIds, uncommonlyUsedIds = uncommonlyUsedIds)
        }

        swipeRefreshMoreLayout.setAdapter(adapter, true)
        swipeRefreshMoreLayout.setEmptyView(CommEmptyView.creatorDef(this))
        swipeRefreshMoreLayout.setOnLoadListener(object : OnLoadingListener {
            override fun onRefreshUI(nowPage: Int) {
                viewModel?.queryCargoNameList(CargoNameFragment.STATUS_ALL, nowPage)
            }

            override fun onLoadMoreUI(nowPage: Int) {
                viewModel?.queryCargoNameList(CargoNameFragment.STATUS_ALL, nowPage)
            }
        })
        swipeRefreshMoreLayout.addItemDecorationSize(dp2px(7f))

        val itemDragAndSwipeCallback = ItemDragAndSwipeCallback(adapter)
        val itemTouchHelper = ItemTouchHelper(itemDragAndSwipeCallback)
        itemTouchHelper.attachToRecyclerView(swipeRefreshMoreLayout.recyclerView)
        // 开启拖拽
        adapter.enableDragItem(itemTouchHelper)
//        adapter.setOnItemDragListener(onItemDragListener)
        swipeRefreshMoreLayout.onAutoRefresh()
    }

    override fun initData() {

    }

    private fun constructingParameters(cargoNameList: List<CargoNameItemBean>): MutableList<SortCargoName> {
        val ids = mutableListOf<SortCargoName>()
        cargoNameList.mapIndexed { index, cargoNameItemBean ->
            val serverIndex = index + 1
            ids.add(SortCargoName(cargoNameItemBean.id, cargoNameItemBean.baseName, serverIndex))
        }
        return ids
    }

/*    private val onItemDragListener = object : OnItemDragListener {
        override fun onItemDragMoving(source: RecyclerView.ViewHolder?, from: Int, target: RecyclerView.ViewHolder?, to: Int) {

        }

        override fun onItemDragStart(viewHolder: RecyclerView.ViewHolder?, pos: Int) {

        }

        override fun onItemDragEnd(viewHolder: RecyclerView.ViewHolder?, pos: Int) {

        }
    }*/

    @LiveDataMatch
    open fun queryCargoNameList(data: PageList<CargoNameItemBean>?) {
        swipeRefreshMoreLayout.onRefreshCompale(data)
    }

    companion object {
        @JvmStatic
        fun start(context: Context?) {
            context ?: return
            val intent = Intent(context, SortCargoNameActivity::class.java)
            context.startActivity(intent)
        }
    }
}