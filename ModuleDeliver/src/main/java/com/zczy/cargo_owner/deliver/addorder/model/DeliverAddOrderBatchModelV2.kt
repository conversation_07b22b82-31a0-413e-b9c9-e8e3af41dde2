package com.zczy.cargo_owner.deliver.addorder.model

import com.sfh.lib.exception.HandleException
import com.sfh.lib.mvvm.service.BaseViewModel
import com.sfh.lib.rx.IResult
import com.sfh.lib.ui.dialog.DialogBuilder
import com.zczy.cargo_owner.deliver.addorder.JumpNewGoodsData
import com.zczy.cargo_owner.deliver.addorder.bean.OrderAddressInfo
import com.zczy.cargo_owner.deliver.addorder.bean.batch.policyTimesValue
import com.zczy.cargo_owner.deliver.addorder.req.batch.JumpHugeData
import com.zczy.cargo_owner.deliver.addorder.req.batch.ReqAddHugeOrderForSeniorConsignor
import com.zczy.cargo_owner.deliver.addorder.req.batch.ReqJumpToMobileSeniorConsignorUpdateHugeOrder
import com.zczy.cargo_owner.deliver.addorder.req.batch.ReqQueryMobileDepositInfoForHugeOrder
import com.zczy.cargo_owner.deliver.addorder.req.batch.formatOrderAddressInfo
import com.zczy.cargo_owner.deliver.addorder.req.batch.setData
import com.zczy.cargo_owner.deliver.addorder.req.batch.setJumpData
import com.zczy.cargo_owner.deliver.addorder.req.comm.Req10QueryOrderReceipt
import com.zczy.cargo_owner.deliver.addorder.req.comm.Req18QueryHugeOrderPolicyInfo
import com.zczy.cargo_owner.deliver.addorder.req.comm.Req22QueryPolicyTipsFlagByCargoName
import com.zczy.cargo_owner.deliver.addorder.req.comm.Req24QueryMobileHugeOrderCommonInfo
import com.zczy.cargo_owner.deliver.addorder.req.comm.Req37QueryOilRewardInfoByPublish
import com.zczy.cargo_owner.deliver.addorder.req.comm.Req53CheckAddressIsInRiskArea
import com.zczy.cargo_owner.deliver.addorder.req.comm.ReqCheckVehicleMonthAmountControl
import com.zczy.cargo_owner.deliver.addorder.req.comm.ReqOilGasConfigInfo
import com.zczy.cargo_owner.deliver.addorder.req.comm.ReqQueryCargoAverage
import com.zczy.cargo_owner.deliver.addorder.req.comm.ReqQueryChildArray
import com.zczy.cargo_owner.deliver.addorder.req.comm.ReqQueryPersonPolicy
import com.zczy.cargo_owner.deliver.addorder.req.comm.ReqQueryPresetConfigWithAddress
import com.zczy.cargo_owner.deliver.addorder.req.comm.ReqQueryRecommendVehicleType
import com.zczy.cargo_owner.deliver.addorder.req.comm.ReqQueryTonRuleByCargoName
import com.zczy.cargo_owner.deliver.addorder.req.comm.RspOilGasConfigInfo
import com.zczy.cargo_owner.deliver.addorder.req.comm.setData
import com.zczy.cargo_owner.deliver.batch.req.ReqHelpMatchVehicle
import com.zczy.comm.http.entity.BaseRsp
import com.zczy.comm.http.entity.ResultData
import io.reactivex.Observable
import io.reactivex.Observer
import io.reactivex.android.schedulers.AndroidSchedulers
import io.reactivex.disposables.Disposable
import io.reactivex.schedulers.Schedulers

/**
 * PS:
 * Created by sdx on 2019/2/13.
 */
class DeliverAddOrderBatchModelV2 : BaseViewModel() {
    /**
     * 发新货 普通货 初始化 网络请求
     */
    fun doAddBatchOrderInitRequestV2() {
        execute(
            true,
            Req24QueryMobileHugeOrderCommonInfo()
        ) { t ->
            if (t.success()) {
                //油气品默认值查询接口
                oilGasConfigInfo(
                    req = ReqOilGasConfigInfo(
                        deliverCity = t.data?.deliverybean?.consCity,
                        deliverCoordinateX = t.data?.deliverybean?.consCoordinateX,
                        deliverCoordinateY = t.data?.deliverybean?.consCoordinateY,
                        deliverDis = t.data?.deliverybean?.consArea,
                        deliverPlace = t.data?.deliverybean?.consDetailAddr,
                        deliverPro = t.data?.deliverybean?.consProvince,
                        despatchCity = t.data?.despatchbean?.consCity,
                        despatchCoordinateX = t.data?.despatchbean?.consCoordinateX,
                        despatchCoordinateY = t.data?.despatchbean?.consCoordinateY,
                        despatchDis = t.data?.despatchbean?.consArea,
                        despatchPlace = t.data?.despatchbean?.consDetailAddr,
                        despatchPro = t.data?.despatchbean?.consProvince,
                    )
                ) { mRspOilGasConfigInfo ->
                    setValue("onReq24QueryMobileHugeOrderCommonInfo", t.data, mRspOilGasConfigInfo)
                    queryPresetConfigWithAddress(
                        req = ReqQueryPresetConfigWithAddress(
                            despatchPro = t.data?.despatchbean?.consProvince,
                            despatchCity = t.data?.despatchbean?.consCity,
                            despatchDis = t.data?.despatchbean?.consArea,
                            deliverPro = t.data?.deliverybean?.consProvince,
                            deliverCity = t.data?.deliverybean?.consCity,
                            deliverDis = t.data?.deliverybean?.consArea,
                        )
                    )
                    true
                }
            }
        }
    }

    fun queryPresetConfigWithAddress(req: ReqQueryPresetConfigWithAddress) {
        execute(req) {
            if (it.success()) {
                setValue("queryPresetConfigWithAddressSuccess", it.data)
            } else {
                showToast(it.msg)
            }
        }
    }

    // 批量货补单/批量货草稿箱：跳转到已认证货主修改批量货运单页面
    fun jumpToMobileSeniorConsignorUpdateHugeOrder(mJumpData: JumpNewGoodsData) {
        val jumpHugeData = JumpHugeData()
        Observable.zip(
            Req24QueryMobileHugeOrderCommonInfo().task,
            ReqJumpToMobileSeniorConsignorUpdateHugeOrder().setJumpData(mJumpData).task
        ) { t1, t2 ->
            when {
                !t1.success() -> {
                    throw HandleException(t1.code.toIntOrNull() ?: 0, t1.msg)
                }

                !t2.success() -> {
                    throw HandleException(t2.code.toIntOrNull() ?: 0, t2.msg)
                }
            }
            jumpHugeData.initData = t1.data
            jumpHugeData.jumpData = t2.data
            true
        }
            .flatMap {
                val commaString = jumpHugeData.jumpData?.hugeCargoInfo?.cargoName ?: ""
                Req22QueryPolicyTipsFlagByCargoName().setData(orderAddressInfo = jumpHugeData.jumpData?.formatOrderAddressInfo() ?: OrderAddressInfo(), mCargoNameStr = commaString).task
            }
            .map {
                if (it.success()) {
                    jumpHugeData.policyTipsFlag = it.data?.policyTipsFlag ?: ""
                    // 是否自动购买 1是 0否
                    jumpHugeData.autoBuyFlag = it.data?.autoBuyFlag ?: ""
                    // 投保方式  1 货主自主投保  2 转嫁承运方  3 全部
                    jumpHugeData.policyMode = it.data?.policyMode ?: ""
                    jumpHugeData.openMonthlyServiceFlag = it.data?.openMonthlyServiceFlag ?: ""
                    jumpHugeData.monthlyServiceMsg = it.data?.monthlyServiceMsg ?: ""
                    jumpHugeData.monthlyPolicyMode = it.data?.monthlyPolicyMode ?: ""
                } else {
                    jumpHugeData.policyTipsFlag = "1"
                }
                true
            }
            .subscribeOn(Schedulers.io())
            .observeOn(AndroidSchedulers.mainThread())
            .doOnSubscribe { showLoading(true) }
            .subscribe(object : Observer<Boolean> {
                override fun onSubscribe(d: Disposable) {
                    putDisposable(d)
                }

                override fun onNext(t: Boolean) {
                    //油气品默认值查询接口
                    oilGasConfigInfo(
                        req = ReqOilGasConfigInfo(
                            deliverCity = jumpHugeData.jumpData?.deliveryInfo?.consCity,
                            deliverCoordinateX = jumpHugeData.jumpData?.deliveryInfo?.consCoordinateX,
                            deliverCoordinateY = jumpHugeData.jumpData?.deliveryInfo?.consCoordinateY,
                            deliverDis = jumpHugeData.jumpData?.deliveryInfo?.consArea,
                            deliverPlace = jumpHugeData.jumpData?.deliveryInfo?.consDetailAddr,
                            deliverPro = jumpHugeData.jumpData?.deliveryInfo?.consProvince,
                            despatchCity = jumpHugeData.jumpData?.despatchInfo?.consCity,
                            despatchCoordinateX = jumpHugeData.jumpData?.despatchInfo?.consCoordinateX,
                            despatchCoordinateY = jumpHugeData.jumpData?.despatchInfo?.consCoordinateY,
                            despatchDis = jumpHugeData.jumpData?.despatchInfo?.consArea,
                            despatchPlace = jumpHugeData.jumpData?.despatchInfo?.consDetailAddr,
                            despatchPro = jumpHugeData.jumpData?.despatchInfo?.consProvince,
                        )
                    ) { mRspOilGasConfigInfo ->
                        setValue("onJumpToMobileSeniorConsignorUpdateHugeOrder", jumpHugeData, mRspOilGasConfigInfo)
                        queryPresetConfigWithAddress(
                            req = ReqQueryPresetConfigWithAddress(
                                despatchPro = jumpHugeData.jumpData?.despatchInfo?.consProvince,
                                despatchCity = jumpHugeData.jumpData?.despatchInfo?.consCity,
                                despatchDis = jumpHugeData.jumpData?.despatchInfo?.consArea,
                                deliverPro = jumpHugeData.jumpData?.deliveryInfo?.consProvince,
                                deliverCity = jumpHugeData.jumpData?.deliveryInfo?.consCity,
                                deliverDis = jumpHugeData.jumpData?.deliveryInfo?.consArea,
                            )
                        )
                        true
                    }
                }

                override fun onError(e: Throwable) {
                    showDialogToast(e.message)
                    hideLoading()
                }

                override fun onComplete() {
                    hideLoading()
                }
            })
    }

    //单运单、单车月度交易限额管控校验
    fun checkVehicleMonthAmountControl(req: ReqCheckVehicleMonthAmountControl, onBlock: () -> Unit = { }) {
        execute(req) {
            if (it.success()) {
                onBlock()
            } else {
                val dialogBuilder = DialogBuilder()
                dialogBuilder.message = it.data?.resultMsg
                dialogBuilder.title = "提示"
                dialogBuilder.isHideCancel = false
                dialogBuilder.setOKText("确定")
                dialogBuilder.setOkListener { dialog, _ ->
                    dialog.dismiss()
                    onBlock()
                }
                showDialog(dialogBuilder)
            }
        }
    }

    // 10 查询押回单信息
    fun queryOrderReceipt() {
        execute(
            true,
            Req10QueryOrderReceipt()
        ) { baseRsp ->
            hideLoading()
            if (baseRsp.success()) {
                setValue("onQueryOrderReceiptSuccess", baseRsp.data)
            } else {
                showDialogToast(baseRsp.msg)
            }
        }
    }

    fun checkAddressIsInRiskArea(req: Req53CheckAddressIsInRiskArea, type: String) {
        execute(
            req
        ) { response ->
            if (response.success()) {
                response.data?.type = type
                setValue("checkAddressIsInRiskAreaSuccess", response.data)
            }
        }
    }

    // 10 查询押回单信息
    fun queryOrderReceiptList() {
        execute(
            true,
            Req10QueryOrderReceipt()
        ) { baseRsp ->
            hideLoading()
            if (baseRsp.success()) {
                setValue("onQueryOrderReceiptListSuccess", baseRsp.data)
            } else {
                showDialogToast(baseRsp.msg)
            }
        }
    }

    // 22 根据货物名称查询是否展示保险提示信息标志
    fun queryPolicyTipsFlagByCargoName(orderAddressInfo: OrderAddressInfo, cargoNameStr: String) {
        execute(
            true,
            Req22QueryPolicyTipsFlagByCargoName().setData(orderAddressInfo = orderAddressInfo, mCargoNameStr = cargoNameStr)
        ) { baseRsp ->
            hideLoading()
            if (baseRsp.success()) {
                setValue("onQueryPolicyTipsFlagByCargoName", baseRsp.data)
            } else {
                showDialogToast(baseRsp.msg)
            }
        }
    }

    // 提交之前的检查
    // 2，查询已认证货主保证金相关信息
    fun commitBeforeCheck(mData2: ReqAddHugeOrderForSeniorConsignor, type: String) {
        execute(
            true,
            ReqQueryMobileDepositInfoForHugeOrder().setData(mData2)
        ) { baseRsp ->
            hideLoading()
            if (baseRsp.success()) {
                setValue("onQueryMobileDepositInfoForHugeOrder", baseRsp.data, type)
            } else {
                showDialogToast(baseRsp.msg)
            }
        }
    }

    fun commit(mData: ReqAddHugeOrderForSeniorConsignor, state: JumpNewGoodsData.PageState) {
        /**
         * 操作方式
         * add                  新增页面保存运单
         * publish              新增页面发布运单
         *
         * update               编辑页面保存运单
         * editpublish          编辑页面发布运单
         *
         * reupdate             重新发布页面保存运单
         * republish            重新发布页面发布运单
         *
         * import               导入发布运单
         *
         * addpublish           新增页面保存并发布运单
         * editaddpublish       编辑页面保存并发布运单
         * readdpublish         重新发布页面保存并发布运单
         */
        when (state) {
            JumpNewGoodsData.PageState.新增 -> {
                mData.hugeOrderInfo.operation = "publish"
            }

            JumpNewGoodsData.PageState.编辑 -> {
                mData.hugeOrderInfo.operation = "editpublish"
            }

            JumpNewGoodsData.PageState.重新发布 -> {
                mData.hugeOrderInfo.operation = "republish"
            }

            else -> {}
        }
        mData.hugeOrderInfo.policyTimesValue()
        execute(
            false,
            mData
        ) { baseRsp ->
            hideLoading()
            if (baseRsp.success()) {
                setValue("onCommitSuccess", baseRsp.data?.hugeOrderId)
            } else {
                when (baseRsp.code) {
                    "2222" -> {
                        setValue("onCommitNoMoney", baseRsp.data)
                    }

                    "3031" -> {
                        setValue("onBlackList", baseRsp.data?.resultMsg)
                    }

                    "3032" -> {
                        setValue("onOverDue", baseRsp.data?.resultMsg)
                    }

                    "3033" -> {
                        setValue("onPreHanging", baseRsp.data, "commit")
                    }

                    "3034" -> {
                        setValue("onShowTimeCheck", baseRsp.data?.openPresetHour)
                    }

                    else -> {
                        showDialogToast(baseRsp.msg)
                    }
                }
            }
        }
    }

    //  保存草稿
    fun saveDrafts(mData: ReqAddHugeOrderForSeniorConsignor, state: JumpNewGoodsData.PageState) {
        /**
         * 操作方式
         * add                  新增页面保存运单
         * publish              新增页面发布运单
         *
         * update               编辑页面保存运单
         * editpublish          编辑页面发布运单
         *
         * reupdate             重新发布页面保存运单
         * republish            重新发布页面发布运单
         *
         * import               导入发布运单
         *
         * addpublish           新增页面保存并发布运单
         * editaddpublish       编辑页面保存并发布运单
         * readdpublish         重新发布页面保存并发布运单
         */
        when (state) {
            JumpNewGoodsData.PageState.新增 -> {
                mData.hugeOrderInfo.operation = "add"
            }

            JumpNewGoodsData.PageState.编辑 -> {
                mData.hugeOrderInfo.operation = "update"
            }

            JumpNewGoodsData.PageState.重新发布 -> {
                mData.hugeOrderInfo.operation = "reupdate"
            }

            else -> {}
        }
        mData.hugeOrderInfo.policyTimesValue()
        execute(
            false,
            mData
        ) { baseRsp ->
            hideLoading()
            if (baseRsp.success()) {
                setValue("onSaveDraftsSuccess")
            } else {
                when (baseRsp.data?.resultCode) {
                    "3031" -> {
                        setValue("onBlackList", baseRsp.data?.resultMsg)
                    }

                    "3032" -> {
                        setValue("onOverDue", baseRsp.data?.resultMsg)
                    }

                    "3033" -> {
                        setValue("onPreHanging", baseRsp.data, "saveDrafts")
                    }

                    "3034" -> {
                        setValue("onShowTimeCheck", baseRsp.data?.openPresetHour)
                    }

                    else -> {
                        showDialogToast(baseRsp.msg)
                    }
                }
            }
        }
    }

    fun saveAndCommit(mData: ReqAddHugeOrderForSeniorConsignor, state: JumpNewGoodsData.PageState) {
        /**
         * 操作方式
         * add                  新增页面保存运单
         * publish              新增页面发布运单
         *
         * update               编辑页面保存运单
         * editpublish          编辑页面发布运单
         *
         * reupdate             重新发布页面保存运单
         * republish            重新发布页面发布运单
         *
         * import               导入发布运单
         *
         * addpublish           新增页面保存并发布运单
         * editaddpublish       编辑页面保存并发布运单
         * readdpublish         重新发布页面保存并发布运单
         */
        when (state) {
            JumpNewGoodsData.PageState.新增 -> {
                mData.hugeOrderInfo.operation = "addpublish"
            }

            JumpNewGoodsData.PageState.编辑 -> {
                mData.hugeOrderInfo.operation = "editaddpublish"
            }

            JumpNewGoodsData.PageState.重新发布 -> {
                mData.hugeOrderInfo.operation = "readdpublish"
            }

            else -> {}
        }
        mData.hugeOrderInfo.policyTimesValue()
        execute(
            false,
            mData
        ) { baseRsp ->
            hideLoading()
            if (baseRsp.success()) {
                setValue("saveAndCommitSuccess", baseRsp.data?.hugeOrderId)
            } else {
                when (baseRsp.data?.resultCode) {
                    "3031" -> {
                        setValue("onBlackList", baseRsp.data?.resultMsg)
                    }

                    "3032" -> {
                        setValue("onOverDue", baseRsp.data?.resultMsg)
                    }

                    "3033" -> {
                        setValue("onPreHanging", baseRsp.data, "saveAndcommit")
                    }

                    "3034" -> {
                        setValue("onShowTimeCheck", baseRsp.data?.openPresetHour)
                    }

                    else -> {
                        showDialogToast(baseRsp.msg)
                    }
                }
            }
        }
    }

    fun queryTonRuleByCargoName(req: ReqQueryTonRuleByCargoName) {
        execute(req) {
            if (it.success()) {
                setValue("queryTonRuleByCargoNameSuccess", it.data)
            } else {
                showToast(it.msg)
            }
        }
    }

    fun queryHugeOrderPolicyInfo(req: Req18QueryHugeOrderPolicyInfo) {
        execute(
            req
        ) {
            if (it.success()) {
                setValue("onQueryHugeOrderPolicyInfoV1", it.data)
            } else {
                showDialogToast(it.msg)
            }
        }

    }

    fun queryHugeOrderPolicyInfoV2(req: Req18QueryHugeOrderPolicyInfo) {
        execute(
            req
        ) {
            if (it.success()) {
                setValue("onQueryHugeOrderPolicyInfoV2", it.data)
            } else {
                setValue("onQueryHugeOrderPolicyInfoError", it)
            }
        }

    }

    fun queryHugeOrderPolicyInfoV3(req: Req18QueryHugeOrderPolicyInfo) {
        execute(
            req
        ) {
            if (it.success()) {
                setValue("onQueryHugeOrderPolicyInfoV3", it.data)
            } else {
                showDialogToast(it.msg)
            }
        }

    }

    fun queryOilRewardInfoByPublish(req: Req37QueryOilRewardInfoByPublish) {
        execute(req) {
            if (it.success()) {
                setValue("onQueryOilRewardInfoByPublish", it.data)
            } else {
                showToast(it.msg)
            }
        }
    }

    fun queryCargoAverage(req: ReqQueryCargoAverage) {
        execute(req) {
            if (it.success()) {
                setValue("queryCargoAverageSuccess", it.data?.cargoUnitMoney)
            }
        }
    }

    //油气品默认值查询接口
    fun oilGasConfigInfo(req: ReqOilGasConfigInfo, onSuccessBlock: (data: RspOilGasConfigInfo?) -> Boolean = { false }) {
        execute(req) {
            if (it.success()) {
                val block = onSuccessBlock(it.data?.data)
                if (!block) {
                    setValue("oilGasConfigInfoSuccess", it.data?.data)
                } else {
                    onSuccessBlock(RspOilGasConfigInfo())
                }
            } else {
                onSuccessBlock(RspOilGasConfigInfo())
            }
        }
    }

    fun queryPersonPolicy(req: ReqQueryPersonPolicy) {
        execute(req) {
            if (it.success()) {
                setValue("queryPersonPolicyBatchSuccess", it.data)
            }
        }
    }

    fun queryRecommendVehicleType(req: ReqQueryRecommendVehicleType) {
        execute(req) {
            if (it.success()) {
                setValue("queryRecommendVehicleTypeSuccess", it.data)
            }
        }
    }

    fun queryChildArray(req: ReqQueryChildArray) {
        execute(req) {
            if (it.success()) {
                setValue("queryChildArraySuccess", it.data?.childArray)
            } else {
                showToast(it.msg)
            }
        }
    }

    fun helpMatchVehicle(orderId: String?, dispatchMobile: String, delistConsultMobile: String, dispatchId: String) {
        execute(
            true,
            ReqHelpMatchVehicle(orderId = orderId, dispatchMobile = dispatchMobile, delistConsultMobile = delistConsultMobile, dispatchId = dispatchId),
            object : IResult<BaseRsp<ResultData>> {
                override fun onSuccess(t: BaseRsp<ResultData>) {
                    if (t.success()) {
                        setValue("helpMatchVehicleSuccess")
                    } else {
                        showDialogToast(t.msg)
                    }
                }

                override fun onFail(e: HandleException) {
                    showDialogToast(e.msg)
                }
            })
    }
}