package com.zczy.cargo_owner.deliver.addorder.util


import android.text.TextUtils
import android.widget.Toast
import com.sfh.lib.AppCacheManager

/**
 * Toast工具类
 */

fun toast(msg: String) {
    ToastUtil.showToast(msg)
}

private object ToastUtil {

    private var toast: Toast? = null
    private var oldMsg: String? = null
    private var oneTime: Long = 0
    private var twoTime: Long = 0

    fun showToast(s: String) {
        if (TextUtils.isEmpty(s)) {
            return
        }
        if (toast == null) {
            toast = Toast.makeText(AppCacheManager.getApplication(), s, Toast.LENGTH_SHORT)
            //            toast.setGravity(Gravity.CENTER, 0, 0);
            toast!!.show()
            oneTime = System.currentTimeMillis()
        } else {
            twoTime = System.currentTimeMillis()
            if (s == oldMsg) {
                if (twoTime - oneTime > Toast.LENGTH_SHORT) {
                    toast!!.show()
                }
            } else {
                oldMsg = s
                toast!!.setText(s)
                toast!!.show()
            }
        }
        oneTime = twoTime
    }
}
