package com.zczy.cargo_owner.deliver.cargo_name_management.bean

import com.zczy.comm.http.entity.BaseNewRequest
import com.zczy.comm.http.entity.BaseRsp
import com.zczy.comm.http.entity.ResultData

/**
 *描述：排序
 *auth:宋双朋
 *time:2024/6/19 17:31
 */

data class SortCargoNameList(
    val offenList: List<SortCargoName>,
    val noOffenList: List<SortCargoName>
) : BaseNewRequest<BaseRsp<ResultData>>("mms-app/goodsController/updateCargoConsignorSort")

data class SortCargoName(
    val id: String? = null,
    val message: String? = null,
    val cargoSort: Int
)