package com.zczy.cargo_owner.deliver.cargo_name_management

import android.os.Bundle
import android.view.View
import com.chad.library.adapter.base.BaseQuickAdapter
import com.sfh.lib.event.RxBusEvent
import com.sfh.lib.mvvm.annotation.LiveDataMatch
import com.zczy.cargo_owner.deliver.R
import com.zczy.cargo_owner.deliver.cargo_name_management.adapter.CargoNameListAdapter
import com.zczy.cargo_owner.deliver.cargo_name_management.bean.CargoNameItemBean
import com.zczy.cargo_owner.deliver.cargo_name_management.bean.RxBusAddCargoNameEvent
import com.zczy.cargo_owner.deliver.cargo_name_management.model.CargoNameManagementModel
import com.zczy.comm.http.entity.PageList
import com.zczy.comm.ui.BaseFragment
import com.zczy.comm.utils.dp2px
import com.zczy.comm.utils.toJson
import com.zczy.comm.widget.pulltorefresh.CommEmptyView
import kotlinx.android.synthetic.main.deliver_cargo_name_fragment.swipeRefreshMoreLayout

/**
 *描述：货物名称管理
 *auth:宋双朋
 *time:2024/6/19 15:11
 */

class CargoNameFragment : BaseFragment<CargoNameManagementModel>() {

    private var status = ""
    private val mAdapter = CargoNameListAdapter()

    companion object {
        private const val CARGO_NAME_STATUS = "status"
        const val STATUS_ALL = "4"
        const val STATUS_UNDER_VIEW = "3"
        const val STATUS_PASSED = "1"
        const val STATUS_REJECTED = "2"

        fun newInstance(status: String): CargoNameFragment {
            val args = Bundle()
            args.putString(CARGO_NAME_STATUS, status)
            val fragment = CargoNameFragment()
            fragment.arguments = args
            return fragment
        }
    }

    override fun getLayout(): Int {
        return R.layout.deliver_cargo_name_fragment
    }

    override fun bindView(view: View, bundle: Bundle?) {
        status = arguments?.getString(CARGO_NAME_STATUS) ?: ""
        swipeRefreshMoreLayout.setAdapter(mAdapter, true)
        swipeRefreshMoreLayout.setEmptyView(CommEmptyView.creatorDef(context))
        swipeRefreshMoreLayout.setOnLoadListener2 { nowPage ->
            viewModel?.queryCargoNameList(status, nowPage)
        }
        swipeRefreshMoreLayout.addItemDecorationSize(dp2px(7f))
        swipeRefreshMoreLayout.addOnItemListener(itemListener)
        swipeRefreshMoreLayout.onAutoRefresh()
    }

    override fun initData() {}

    private val itemListener = BaseQuickAdapter.OnItemClickListener { _, _, position ->
        val item = mAdapter.data[position]
        CargoNameDetailActivity.jumpPage(context = <EMAIL>, item.id)
    }

    @LiveDataMatch
    open fun queryCargoNameList(data: PageList<CargoNameItemBean>?) {
        swipeRefreshMoreLayout.onRefreshCompale(data)
    }

    @RxBusEvent(from = "新增编辑")
    open fun onRxAddEditSuccess(data: RxBusAddCargoNameEvent) {
        if (data.success) {
            swipeRefreshMoreLayout.onAutoRefresh()
        }
    }
}