package com.zczy.cargo_owner.deliver.addorder.dialog

import android.annotation.SuppressLint
import android.os.Bundle
import android.text.TextUtils
import android.view.View
import com.zczy.cargo_owner.deliver.R
import com.zczy.comm.ui.BaseDialog
import kotlinx.android.synthetic.main.deliver_order_time_check_dialog_v1.*

/**
 *  desc: WLHY-7527 *预挂单提前预挂时间报错提示优化
 *  user: 宋双朋
 *  time: 2024/9/9 9:27
 */

class DeliverOrderTimeCheckDialogV1(val time: String = "8", var dateLimit: String="", var block: () -> Unit) : BaseDialog() {

    override fun getDialogTag(): String = "DeliverOrderTimeCheckDialogV1"

    override fun getDialogLayout(): Int = R.layout.deliver_order_time_check_dialog_v1

    override fun bindView(view: View, bundle: Bundle?) {
        reAdjustView(35, 0)
        initView(view)
    }

    @SuppressLint("SetTextI18n")
    private fun initView(view: View) {
        if (TextUtils.isEmpty(dateLimit)){
            tvFailReason.text = "您的运单预挂时间不足${time}小时，建议提前挂单!"
        }else{
            tvFailReason.text = "您的运单预挂时间不足${time}小时，建议提前挂单!如需当前时间发布预挂单，装货开始时间需在${dateLimit}之后"
        }

        tvLeft.setOnClickListener {
            dismiss()
        }
        tvRight.setOnClickListener {
            block()
            dismiss()
        }
    }
}