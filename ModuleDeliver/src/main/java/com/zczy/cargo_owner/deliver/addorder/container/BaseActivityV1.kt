package com.zczy.cargo_owner.deliver.addorder.container

import com.sfh.lib.mvvm.service.BaseViewModel
import com.sfh.lib.ui.AbstractLifecycleActivity
import android.os.Bundle
import android.view.WindowManager
import android.content.pm.ActivityInfo
import android.graphics.Color
import com.jakewharton.rxbinding2.view.RxView
import com.zczy.comm.ui.UtilStatus
import androidx.annotation.LayoutRes
import android.view.View
import io.reactivex.Observable
import java.util.concurrent.TimeUnit

/**
 * 统一 状态栏 、 关闭横屏 、 显示关闭键盘
 * 增加 点击事件防抖动
 * sdx 2019/2/12
 */
abstract class BaseActivityV1<VM : BaseViewModel?> : AbstractLifecycleActivity<VM>() {
    protected val TAG = <EMAIL>
    protected var defaultWindowDuration: Long = 500 //默认点击间隔
    override fun onCreate(savedInstanceState: Bundle?) {
        super.onCreate(savedInstanceState)
        init()
        setContentView(layout)
        initStatus()
        bindView(savedInstanceState)
        initData()
    }

    private fun init() {

    }

    private fun bindClickEventOb(v: View): Observable<Any> {
        return RxView.clicks(v).throttleFirst(defaultWindowDuration, TimeUnit.MILLISECONDS)
    }

    protected fun bindClickEvent(v: View) {
        val subscribe = bindClickEventOb(v).subscribe { aVoid: Any? -> onSingleClick(v) }
        putDisposable(subscribe)
    }

    protected fun initStatus() {
        UtilStatus.initStatus(this, Color.WHITE)
    }

    protected open fun onSingleClick(v: View) {}

    @get:LayoutRes
    protected abstract val layout: Int
    protected abstract fun bindView(bundle: Bundle?)
    protected abstract fun initData()
}