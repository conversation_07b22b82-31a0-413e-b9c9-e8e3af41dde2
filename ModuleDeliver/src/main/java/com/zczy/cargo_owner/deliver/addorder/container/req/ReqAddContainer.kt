package com.zczy.cargo_owner.deliver.addorder.container.req

import com.zczy.cargo_owner.deliver.addorder.req.container.ContainerList
import com.zczy.comm.http.entity.BaseNewRequest
import com.zczy.comm.http.entity.BaseRsp
import com.zczy.comm.http.entity.ResultData

/**
 * 功能描述: 货主新增集装箱
 * <AUTHOR>
 * @date 2022/2/25-14:11
 */

class ReqAddContainer(
    var containerId: String? = null, //	是	集装箱id
    var remark: String? = null //	备注
) : BaseNewRequest<BaseRsp<ResultData>>("oms-app/container/insertConsignorContainer")

data class RxBusAddContainer(
    var success: Boolean = false //新增集装箱 true 成功 false 失败
)

data class RxBusAddContainerData(
    var data1: ArrayList<ContainerList> = arrayListOf(),//保存的集装箱箱号数据
    var data2: ContainerList? = null,//集装箱列表选择的集装箱
    var data3: Int = -1 //货物明细选择的条目
)