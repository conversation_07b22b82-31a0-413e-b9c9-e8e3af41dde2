package com.zczy.cargo_owner.deliver.addorder.model

import com.zczy.lib_zstatistics.sdk.ZStatistics
import java.util.*

/**
 *
 * 产品需求:AB-9209	【安卓】【数据埋点】记录货主端APP发货操作时间
 * 接口对接：
 * wiki：
 *
 * <AUTHOR>
 * @company 中储智运科技股份有限公司
 * @copyright （版权）中储智运科技股份有限公司所有
 * @date 2024/11/27
 */
class OnEventUtil {

    companion object {
        var taskID = "";
        var page = "";

        fun initTaskID(pageName: String) {
            taskID = UUID.randomUUID().toString();
            page = pageName;
        }

        fun showDialogEvent(msg: String?) {
            //AB-9209	【安卓】【数据埋点】记录货主端APP发货操作时间
            ZStatistics.onEvent(Companion.page, "showDialog") {
                val requestParam: HashMap<String, String> = HashMap(1)
                requestParam.put("taskID", taskID)
                requestParam.put("msg", msg?:"")
                it.put("requestParam", requestParam)
            }
        }
    }
}

