package com.zczy.cargo_owner.deliver.addorder.adapter

import com.chad.library.adapter.base.BaseItemDraggableAdapter
import com.chad.library.adapter.base.BaseViewHolder
import com.zczy.cargo_owner.deliver.R
import com.zczy.cargo_owner.deliver.addorder.req.RspAddRule

/**
 * 类描述：自动择标纬度
 * 作者：ssp
 * 创建时间：2024/3/29
 */

class DeliverBidRuleOperateAdapter : BaseItemDraggableAdapter<RspAddRule, BaseViewHolder>(R.layout.deliver_bid_rule_operate_item, mutableListOf()) {
    override fun convert(helper: BaseViewHolder, item: RspAddRule) {
        helper.setText(R.id.tvContent, item.text)
    }
}