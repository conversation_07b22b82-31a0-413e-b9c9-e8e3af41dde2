package com.zczy.cargo_owner.deliver.addorder.model

import com.sfh.lib.exception.HandleException
import com.sfh.lib.mvvm.service.BaseViewModel
import com.sfh.lib.rx.IResult
import com.sfh.lib.rx.IResultSuccessNoFail
import com.zczy.cargo_owner.deliver.addorder.req.comm.QueryOneCargoStandard
import com.zczy.cargo_owner.deliver.addorder.req.comm.Req4QueryPackTypeList
import com.zczy.cargo_owner.deliver.addorder.req.comm.RspCargoStandardUnitWeight
import com.zczy.cargo_owner.deliver.addorder.req.comm.RspQueryPackTypeList
import com.zczy.cargo_owner.deliver.addorder.req.container.Req35QueryMobileContainerHugeOrderCommonInfo
import com.zczy.cargo_owner.deliver.addorder.req.container.Rsp35QueryMobileContainerHugeOrderCommonInfo
import com.zczy.comm.http.entity.BaseRsp

/**
 * PS:
 * Created by sdx on 2019/2/15.
 */
class DeliverGoodsDetailsModel : BaseViewModel() {

    // 查询包装集合
    fun queryPackTypeList(init: Boolean, index: Int = -1) {
        execute(!init,
            Req4QueryPackTypeList(),
            object : IResult<BaseRsp<RspQueryPackTypeList?>> {
                override fun onSuccess(t: BaseRsp<RspQueryPackTypeList?>) {
                    hideLoading()
                    if (t.success()) {
                        setValue("queryPackTypeListSuccess", init, index, t.data?.packTypes)
                    } else {
                        if (!init) showDialogToast(t.msg)
                    }
                }

                override fun onFail(e: HandleException) {
                    hideLoading()
                    if (!init) showDialogToast(e.msg)
                }
            })
    }

    // 查询包装集合 集装箱
    fun queryPackTypeListV1(init: Boolean, index: Int = -1) {
        execute(
            !init,
            Req35QueryMobileContainerHugeOrderCommonInfo(),
            object : IResult<BaseRsp<Rsp35QueryMobileContainerHugeOrderCommonInfo>> {
                override fun onSuccess(t: BaseRsp<Rsp35QueryMobileContainerHugeOrderCommonInfo>) {
                    hideLoading()
                    if (t.success()) {
                        setValue("queryPackTypeListSuccess", init, index, t.data?.packTypes)
                    } else {
                        if (!init) showDialogToast(t.msg)
                    }
                }

                override fun onFail(e: HandleException) {
                    hideLoading()
                    if (!init) showDialogToast(e.msg)
                }
            })
    }


    //通过货主货物id和规格型号id 查询规格型号标准件重量
    fun queryWeight(cargoConsignorId: String, standardName: String, position: Int) {
        execute(
            QueryOneCargoStandard(cargoConsignorId, standardName),
            object : IResultSuccessNoFail<BaseRsp<RspCargoStandardUnitWeight>> {
                override fun onSuccess(p: BaseRsp<RspCargoStandardUnitWeight>?) {
                    if (p?.success() ?: false) {
                        setValue(
                            "onCargoStandardSuccess",
                            position,
                            p?.data?.cargoStandardUnitWeight
                        )
                    }
                }
            })
    }

}