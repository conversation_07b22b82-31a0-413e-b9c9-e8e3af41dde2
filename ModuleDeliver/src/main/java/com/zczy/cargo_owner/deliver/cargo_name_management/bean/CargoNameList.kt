package com.zczy.cargo_owner.deliver.cargo_name_management.bean

import com.zczy.comm.http.entity.BaseNewRequest
import com.zczy.comm.http.entity.BaseRsp
import com.zczy.comm.http.entity.PageList

/**
 *描述：查询货主货物名称列表
 *auth:宋双朋
 *time:2024/6/19 15:13
 */

class QueryCargoNameList(
    var state: String? = null,
    var createdTimeStr: String? = null,
    var nowPage: Int = 1,
    var pageSize: Int = 10
) : BaseNewRequest<BaseRsp<PageList<CargoNameItemBean>>>("oms-app/goodsController/queryCargoConsignorPageList")

class SearchCargoNameList(
    var baseName: String? = null,
    var state: String? = null,
) : BaseNewRequest<BaseRsp<PageList<CargoNameItemBean>>>("oms-app/goodsController/queryCargoConsignorPageList")

data class CargoNameItemBean(
    val id: String? = null, //货物id
    val baseName: String? = null,//货物名称
    val createdTime: String? = null, //创建时间   前台输入的搜索条件
    val offenUseingFlag: String? = null,//是否常用 0-否 1-是
    val specialFlag: String? = null,//是否是特殊品 0:否 1:是
    val isChemistry: String? = null,//是否是化学品 0:否 1:是
    val state: String? = null,// 1.审核通过 2.审核不通过 3.待审核
    val specialGoodsPicUrls: MutableList<String>? = null,// 货物审核信息图片（CO-4709 【水运】货主端增加上传特殊品资质渠道）
)