package com.zczy.cargo_owner.deliver.addorder.container.model

import com.sfh.lib.exception.HandleException
import com.sfh.lib.mvvm.service.BaseViewModel
import com.sfh.lib.rx.IResult
import com.zczy.cargo_owner.deliver.addorder.container.req.ReqDeliverContainerListV1
import com.zczy.cargo_owner.deliver.addorder.container.req.RspDeliverContainerList
import com.zczy.comm.http.entity.BaseRsp
import com.zczy.comm.http.entity.PageList

/**
 * 功能描述: 集装箱列表model
 * <AUTHOR>
 * @date 2022/2/23-10:22
 */

class DeliverContainerListModelV1 : BaseViewModel() {

    fun queryList(req: ReqDeliverContainerListV1) {
        execute(req, object : IResult<BaseRsp<PageList<RspDeliverContainerList>>> {
            override fun onSuccess(bean: BaseRsp<PageList<RspDeliverContainerList>>) {
                if (bean.success()) {
                    setValue("onQuerySuccess", initData().data)
                } else {
                    setValue("onQuerySuccess", initData().data)
                }
            }

            override fun onFail(p0: HandleException?) {
                setValue("onQuerySuccess", initData().data)
            }
        })
    }

    fun initData(): BaseRsp<PageList<RspDeliverContainerList>> {
        val baseRsp = BaseRsp<PageList<RspDeliverContainerList>>()
        baseRsp.data = PageList()
        return baseRsp
    }
}