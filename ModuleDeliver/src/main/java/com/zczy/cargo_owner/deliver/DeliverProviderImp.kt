package com.zczy.cargo_owner.deliver

import android.app.Activity
import android.content.Intent
import android.text.TextUtils
import android.widget.Toast
import androidx.fragment.app.Fragment
import com.sfh.lib.ui.AbstractLifecycleActivity
import com.sfh.lib.ui.AbstractLifecycleFragment
import com.zczy.cargo_owner.deliver.addorder.JumpNewGoodsData
import com.zczy.cargo_owner.deliver.addorder.ui.DeliverBidSetActivity
import com.zczy.cargo_owner.deliver.addorder.ui.DeliverGoodsNameActivity
import com.zczy.cargo_owner.deliver.address.DeliverChooseAreaActivity
import com.zczy.cargo_owner.deliver.address.consignor.bean.DeliverAddressLocationData
import com.zczy.cargo_owner.deliver.address.consignor.ui.DeliverAddressLocationActivity
import com.zczy.cargo_owner.deliver.batch.DeliverBatchChooseActivity
import com.zczy.cargo_owner.deliver.batch.DeliverBatchEditActivity
import com.zczy.cargo_owner.deliver.batch.DeliverBatchManageActivity
import com.zczy.cargo_owner.deliver.bean.DeliverMyOrderListData
import com.zczy.cargo_owner.deliver.drafts.req.RspBatchGoodsOrderItem
import com.zczy.cargo_owner.deliver.drafts.ui.DeliverDraftsEditActivity
import com.zczy.cargo_owner.deliver.main.ui.DeliverNormalChooseActivity
import com.zczy.cargo_owner.deliver.main.ui.DeliverNormalChooseActivityV1
import com.zczy.cargo_owner.libcomm.IDeliverProvider
import com.zczy.cargo_owner.libcomm.TDeliverLocationData
import com.zczy.cargo_owner.libcomm.config.SubUserAuthUtils
import com.zczy.cargo_owner.libcomm.utils.getChildPermission
import com.zczy.cargo_owner.order.change.deliverinfo.req.OrderCargo
import com.zczy.comm.data.entity.ECityAddress
import com.zczy.comm.utils.json.toJson
import com.zczy.comm.utils.json.toJsonObject

/**
 * PS:
 * Created by sdx on 2019-06-03.
 */
class DeliverProviderImp : IDeliverProvider {
    override fun openDeliverDraftsEditActivity(
        fragment: Fragment?,
        pOrderId: String?,
        pSpecifyFlag: String?,
        requestCode: Int,
        goodsSource: String?
    ) {
        val moCommonOrder = SubUserAuthUtils.get().moCommonOrder.getChildPermission()
        if (moCommonOrder.isNotEmpty()) {
            if (fragment is AbstractLifecycleFragment<*>) {
                fragment.showDialogToast(moCommonOrder)
            } else {
                Toast.makeText(fragment?.context, moCommonOrder, Toast.LENGTH_SHORT).show()
            }
            return
        }
        val data = JumpNewGoodsData(
            orderId = pOrderId ?: "",
            specifyFlag = pSpecifyFlag ?: "",
            pageState = JumpNewGoodsData.PageState.重新发布,
            orderType = "0"
        )
        DeliverDraftsEditActivity.start(
            fragment = fragment,
            data = data,
            title = "编辑货源",
            requestCode = requestCode,
            nilOrder = TextUtils.equals("2", goodsSource)
        )
    }

    override fun openDeliverDraftsEditActivityV1(
        fragment: Fragment?,
        pOrderId: String?,
        pSpecifyFlag: String?,
        requestCode: Int,
        goodsSource: String?
    ) {
        val moCommonOrder = SubUserAuthUtils.get().moCommonOrder.getChildPermission()
        if (moCommonOrder.isNotEmpty()) {
            if (fragment is AbstractLifecycleFragment<*>) {
                fragment.showDialogToast(moCommonOrder)
            } else {
                Toast.makeText(fragment?.context, moCommonOrder, Toast.LENGTH_SHORT).show()
            }
            return
        }
        val data = JumpNewGoodsData(
            orderId = pOrderId ?: "",
            specifyFlag = pSpecifyFlag ?: "",
            pageState = JumpNewGoodsData.PageState.重新发布,
            orderType = "2"
        )
        DeliverDraftsEditActivity.start(
            fragment = fragment,
            data = data,
            title = "编辑货源",
            requestCode = requestCode,
            nilOrder = false
        )
    }

    override fun openDeliverAiActivity(activity: Activity?, data: String, requestCode: Int) {
        val moCommonOrder = SubUserAuthUtils.get().moCommonOrder.getChildPermission()
        if (moCommonOrder.isNotEmpty()) {
            if (activity is AbstractLifecycleActivity<*>) {
                activity.showDialogToast(moCommonOrder)
            } else {
                Toast.makeText(activity, moCommonOrder, Toast.LENGTH_SHORT).show()
            }
            return
        }
        val jumpNewGoodsData = JumpNewGoodsData()
        if (activity != null) {
            DeliverDraftsEditActivity.startAi(
                activity = activity,
                jumpNewGoodsData = jumpNewGoodsData,
                data = data,
                title = "发布货源",
                requestCode = requestCode,
                ai = true
            )
        }
    }

    override fun openDeliverDraftsEditActivity(activity: Activity?, pOrderId: String, pSpecifyFlag: String, requestCode: Int, goodsSource: String) {
        val moCommonOrder = SubUserAuthUtils.get().moCommonOrder.getChildPermission()
        if (moCommonOrder.isNotEmpty()) {
            if (activity is AbstractLifecycleActivity<*>) {
                activity.showDialogToast(moCommonOrder)
            } else {
                Toast.makeText(activity, moCommonOrder, Toast.LENGTH_SHORT).show()
            }
            return
        }
        val data = JumpNewGoodsData(
            orderId = pOrderId,
            specifyFlag = pSpecifyFlag,
            pageState = JumpNewGoodsData.PageState.重新发布,
            orderType = "0"
        )
        if (activity != null) {
            DeliverDraftsEditActivity.start(
                activity = activity,
                data = data,
                title = "编辑货源",
                requestCode = requestCode,
                nilOrder = TextUtils.equals("2", goodsSource)
            )
        }
    }

    override fun openDeliverDraftsEditActivityV1(activity: Activity?, pOrderId: String, pSpecifyFlag: String, requestCode: Int) {
        val moCommonOrder = SubUserAuthUtils.get().moCommonOrder.getChildPermission()
        if (moCommonOrder.isNotEmpty()) {
            if (activity is AbstractLifecycleActivity<*>) {
                activity.showDialogToast(moCommonOrder)
            } else {
                Toast.makeText(activity, moCommonOrder, Toast.LENGTH_SHORT).show()
            }
            return
        }
        val data = JumpNewGoodsData(
            orderId = pOrderId,
            specifyFlag = pSpecifyFlag,
            pageState = JumpNewGoodsData.PageState.重新发布,
            orderType = "2"
        )
        if (activity != null) {
            DeliverDraftsEditActivity.start(
                activity = activity,
                data = data,
                title = "编辑货源",
                requestCode = requestCode,
                nilOrder = false
            )
        }
    }

    override fun openDeliverDraftsEditActivityV2(
        fragment: Fragment?,
        pOrderId: String,
        detail: String?,
        isHuge: Boolean,
        requestCode: Int,
    ) {
        val moCommonOrder = SubUserAuthUtils.get().moCommonOrder.getChildPermission()
        val moBatchOrder = SubUserAuthUtils.get().moBatchOrder.getChildPermission()
        if (moCommonOrder.isNotEmpty() && !isHuge) {
            if (fragment is AbstractLifecycleFragment<*>) {
                fragment.showDialogToast(moCommonOrder)
            } else {
                Toast.makeText(fragment?.context, moCommonOrder, Toast.LENGTH_SHORT).show()
            }
            return
        }
        if (moBatchOrder.isNotEmpty() && isHuge) {
            if (fragment is AbstractLifecycleFragment<*>) {
                fragment.showDialogToast(moBatchOrder)
            } else {
                Toast.makeText(fragment?.context, moBatchOrder, Toast.LENGTH_SHORT).show()
            }
            return
        }
        val deliverMyOrderListData = detail?.toJsonObject(DeliverMyOrderListData::class.java)
        deliverMyOrderListData?.let {
            val huge = if (isHuge) {
                "1"
            } else {
                "0"
            }
            val data = JumpNewGoodsData(
                orderId = pOrderId,
                specifyFlag = it.specifyFlag,
                pageState = JumpNewGoodsData.PageState.重新发布,
                orderType = huge
            )
            DeliverDraftsEditActivity.start(
                fragment = fragment,
                data = data,
                title = "编辑货源",
                requestCode = requestCode,
                nilOrder = TextUtils.equals("2", it.goodsSource)
            )
        }
    }

    override fun openDeliverDraftsEditActivityV3(fragment: Fragment?, pOrderId: String, detail: String?, requestCode: Int) {
        val moCommonOrder = SubUserAuthUtils.get().moCommonOrder.getChildPermission()
        if (moCommonOrder.isNotEmpty()) {
            if (fragment is AbstractLifecycleFragment<*>) {
                fragment.showDialogToast(moCommonOrder)
            } else {
                Toast.makeText(fragment?.context, moCommonOrder, Toast.LENGTH_SHORT).show()
            }
            return
        }

        val deliverMyOrderListData = detail?.toJsonObject(DeliverMyOrderListData::class.java)
        deliverMyOrderListData?.let {
            val data = JumpNewGoodsData(
                orderId = pOrderId,
                specifyFlag = it.specifyFlag,
                pageState = JumpNewGoodsData.PageState.重新发布,
                orderType = "2"
            )
            DeliverDraftsEditActivity.start(
                fragment = fragment,
                data = data,
                title = "编辑货源",
                requestCode = requestCode,
                nilOrder = false
            )
        }
    }

    override fun openDeliverBatchEditActivity(fragment: Fragment?, data: String, requestCode: Int) {
        DeliverBatchEditActivity.start(
            fragment,
            data.toJsonObject(RspBatchGoodsOrderItem::class.java)
                ?: RspBatchGoodsOrderItem(),
            requestCode
        )
    }

    override fun openDeliverNormalChooseActivity(fragment: Fragment, data: String, requestCode: Int) {
        DeliverNormalChooseActivity.start(
            fragment, data.toJsonObject(DeliverMyOrderListData::class.java)
                ?: DeliverMyOrderListData(), requestCode
        )
    }

    override fun openDeliverBatchChooseActivity(fragment: Fragment, data: String, requestCode: Int) {
        DeliverBatchChooseActivity.start(
            fragment, data.toJsonObject(RspBatchGoodsOrderItem::class.java)
                ?: RspBatchGoodsOrderItem(), requestCode
        )
    }

    override fun openDeliverNormalChooseActivityV1(fragment: Fragment, data: String, isBatch: Boolean, requestCode: Int) {
        DeliverNormalChooseActivityV1.start(fragment, data, isBatch, requestCode)
    }

    override fun obtainDataDeliverAddressLocationActivity(intent: Intent?): TDeliverLocationData {
        val locationData = DeliverAddressLocationActivity.obtainData(intent)
        return locationData.toJson().toJsonObject(TDeliverLocationData::class.java)
            ?: TDeliverLocationData()
    }

    override fun openDeliverAddressLocationActivity(fragment: Fragment, data: TDeliverLocationData, requestCode: Int) {
        val locationData = data.toJson().toJsonObject(DeliverAddressLocationData::class.java)
            ?: DeliverAddressLocationData()
        DeliverAddressLocationActivity.start(fragment, locationData, requestCode)
    }

    override fun obtainResultDeliverChooseAreaActivity(intent: Intent?): List<ECityAddress> {
        return DeliverChooseAreaActivity.obtainResult(intent)
    }

    override fun openDeliverChooseAreaActivity(
        fragment: Fragment,
        title: String,
        data: ECityAddress,
        requestCode: Int
    ) {
        DeliverChooseAreaActivity.start(fragment, title, data, requestCode)
    }

    override fun openDeliverChooseAreaActivity(
        activity: Activity,
        title: String,
        isMultiple: Boolean,
        data: List<ECityAddress>,
        requestCode: Int
    ) {
        DeliverChooseAreaActivity.start(activity = activity, title = title, requestCode = requestCode, data = data, isMultiple = isMultiple)
    }

    override fun obtainDataDeliverGoodsNameActivity(intent: Intent?): String {
        return DeliverGoodsNameActivity.obtainData(intent)
            .let { OrderCargo(cargoName = it.baseName, cargoId = it.id) }
            .toJson()
    }

    override fun openDeliverGoodsNameActivity(fragment: Fragment, state: Boolean, requestCode: Int) {
        DeliverGoodsNameActivity.start(fragment = fragment, statePass = state, requestCode = requestCode)
    }

    override fun openDeliverBidSetActivity(fragment: Fragment?) {
        DeliverBidSetActivity.start(fragment = fragment)
    }

    override fun openDeliverBatchManageActivity(fragment: Fragment?) {
        DeliverBatchManageActivity.start(fragment?.context)
    }
}