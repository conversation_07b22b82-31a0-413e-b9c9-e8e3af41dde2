package com.zczy.cargo_owner.deliver.addorder.req.comm

import com.zczy.cargo_owner.deliver.addorder.dialog.DeliverNewGoodsTimeData
import com.zczy.cargo_owner.deliver.bean.DeliverAddressData
import com.zczy.cargo_owner.order.confirm.bean.BatchConfirmItemDetailBean
import com.zczy.cargo_owner.order.confirm.bean.getReasonValue
import com.zczy.cargo_owner.order.confirm.bean.getTypeValue
import com.zczy.comm.SpannableHepler
import com.zczy.comm.http.entity.BaseNewRequest
import com.zczy.comm.http.entity.BaseRsp
import com.zczy.comm.http.entity.ResultData


/**
 * PS: 23.手机端接口：查询查询普通货所需会员公共信息
 * http://wiki.zczy56.com/pages/viewpage.action?pageId=12354154
 * Created by sdx on 2019-05-16.
 */
class Req23QueryMobileOrderCommonInfo(
    var ltcOrderFlag: String = "0" //1 是 0 否 不传为否
) : BaseNewRequest<BaseRsp<Rsp23QueryMobileOrderCommonInfo>>
    ("oms-app/order/addOuterCommonOrder/queryMobileOrderCommonInfo")

class Req23QueryMobileOrderCommonInfo2 : BaseNewRequest<BaseRsp<Rsp23QueryMobileOrderCommonInfo>>
    ("oms-app/order/addOuterCommonOrder/queryMobileTenderOrderCommonInfo")


data class Rsp23QueryMobileOrderCommonInfo(
    /** 紧急联系人 */
    var contactName: String = "",
    /** 紧急联系人手机 */
    var contactPhone: String = "",
    /** 发货信息 */
    var despatchbean: DeliverAddressData? = null,
    /** 收货信息  */
    var deliverybean: DeliverAddressData? = null,
    /** 货主是否支持原合作运力范围交易：0 否，1 是 */
    var appointCarrierSwitch: String = "",
    /** 是否允许全平台运力模式下关联承运方：0 否，1 是 */
    var supportSocialAppointFlag: String = "",
    /** 指定承运方最大数量*/
    var appointCarrierMaxMumber: String = "0",
    /** 指定运力组承运方最大数量*/
    var appointGroupMaxMumber: String = "0",
    /** 指定加盟运力最大数量*/
    var appointStaffMaxMumber: String = "0",
    /** 当前时间到装货开始时间 */
    var validity: String = "",
    /** 装货有效开始时间 */
    var effectiveStartTime: String = "",
    /** 装货有效结束时间 */
    var effectiveEndTime: String = "",
    /** 货主是否开启押回单功能：0 否，1 是 */
    var whetherShowBackOrderChoose: String = "",
    /** “油/气品”配置：0 不支持油/气,1 可选油/气,2 强制油/气--允许修改,3 强制油/气--不允许修改 */
    var oilConfig: String = "",
    /** 是否展示油气品模块 1展示 0 不展示 */
    var whetherShowSdOilCard: String = "",
    /** “油品比例”配置项 */
    var oilPercent: String = "",
    /** “汽品比例"配置项 */
    var gasPercent: String = "",
    /** 发布固定油品比例配置集合 */
    var oilPercents: List<String>? = null,
    /** 发布固定汽品比例配置集合 */
    var gasPercents: List<String>? = null,
    /** 货主是否支持固定油品额度：1 支持，0 不支持 */
    var whetherSupportOilFixedCreditFlag: String = "",
    /** 油品固定额度默认值 */
    var oilFixedCreditDefault: String = "",

    /**货主是否支持固定气品额度：1 支持，0 不支持*/
    var whetherSupportGasFixedCreditFlag: String = "",
    var gasFixedCreditDefault: String = "",

    /** 货主赠保额度 */
    var giftMoney: String = "0",
    /** 普通货重货最大吨位 */

    var maxOrderWeight: String = "200",
    /** 普通货泡货最大体积 */
    var maxOrderVolume: String = "400",
    /** 普通货批量发单限制数量 */
    var batchPublishLimit: String = "20",
    /** 装卸货要求集合 */
    var promptTypeList: ArrayList<String>? = null,
    /** 是否解除普通货竞价至少1小时限制:0 否,1 是 */
    var releaseExpectTimeFlag: String = "",
    // 上次的咨询电话
    var latestDelistConsultMobile: String = "",
    // 	是否支持投标议价模式 0否 1是
    var bargainBidSupportFlag: String = "",
    // 	3430_徐钢实现运力开放_普通货议价自动成交
    var supportAutoDealFlag: String = "",
    var ruleNameJSONArray: List<OrderCommonRuleItem>? = null,
    //是否参加优选专区：0 否,1 是
    var supportPriorSelectFlag: String = "",
    //发布运单支持填写运费不含税价：0 否,1 是
    var supportConsignorNoTaxMoneyFlag: String = "",
    var settleRate: String? = "",
    var despatchStartStr: String = "",    //最早到场装货时间
    var despatchEndStr: String = "",    //最晚到场装货时间
    var receiveDateStr: String = "",    //收货时间
    var cargoStandardUnitShowFlag: String = "",    //配置项货物明细件数输入项是否显示：1 显示 否则不显示
    var whetherDedaUserId: String = "",      //是否德达油卡 1 是 0 否
    var whetherShowTonRule: String = "",      //是否展示亏涨吨扣款:0 否,1 是
    var forceNonePolicyFlag: String = "1",      //是否强制不购买保险 0否1是
    var showOrderMonitor: String = "0", // 是否定制化监控：0 否,1 是
    var overTonConfigValue: String = "0", //超载管控吨位
    var contractName: String = "", //电子合同名称
    var whetherAllowReceiptMoneyBeyondThousand: String = "", //是否允许在回单押金超过1000元:0 否，1 是
    var autoPushCarrierPrice: String = "", // 2的时候才展示填写
    var latestSettleBasisType: String = "", // 最近一单结算依据
    var selfCommentSelectFlag: String = "", // 自定义编号是否下拉 0 否 1是
    var latestFreightType: String = "", // 最近一单计价方式：0 包车价(默认),1 单价
    var receiptType: String = "", // 押回单模式：1 固额模式，2 比例模式
    var receiptMaxFixed: String = "", // 押金金额 最大固额
    var receiptMaxRatio: String = "", // 押金金额 最大比例
    var bulkConsignorFlag: String = "0", // 是否大件货主：0 否，1 是
    var consignorPresetConfigEnable: String = "", // 是否展示 申请发布预挂单 1 是 0 否
    var presetType: String = "",// 预挂单类型   1 委托函 2 开放函
    var consignorNoPresetConfig: String = "",// 1 非预挂单 选择VIP运力进行弹窗提醒
    var showCalculation: String = "",// 是否展示按理计重量结算 1展示 0不展示
    var consignorPriceFlag: String = "",// 货主价置灰or必填配置：0 置灰  1必填
    var consignorDeductionFlag: String = "",// 是否支持回单确认时加/扣承运人运费  0否 1是
    var backOrderIgnoreChangeSwitch: String = "",// 是否支持回单确认角分抹零 0否 1是
    var driverNeedsToConsultPickTheTicketSwitch: String = "", //是否需要司机咨询摘单 1是 0否
    var showDisplayFlag: String = "", //showDisplayFlag   是否战略物资    0否/1是
    var strategicMaterialSwitch: String = "", //strategicMaterialSwitch  配置是否打开  0否/1是
    var availableBalanceTipsFlag: String = "",// 0//可用余额不足提示 默认 0  1 不提示  1 提示
    var availableBalanceTips: String = "",// “您的可发单金额为:0.00元。当前可发单金额不足，无法发布运单，请及时转入运费。”
    var readJustPriceRuleFlag: String = "",//是否显示调价规则
    var proxySettleRate: String = "",//代开时结算税率
    var showRecommendVehicle: String? = null, //  // 1：展示推荐车型 0：不展示推荐车型
    var recommendVehicleList: MutableList<String> = mutableListOf(), // 推荐车型
    var depositConfigValue: String = "",//1:极速好货免收司机定金 2:极速好货收取司机订金仅可退 3:极速好货收取司机订金允许可退或不可退
) : ResultData()

fun BatchConfirmItemDetailBean.showJiaKouRule(): String {
    val sb = SpannableHepler()
    consignorDeductionObj?.deductionDetailArray?.forEach {
        sb.append(SpannableHepler.Txt(it.getReasonValue()))
        sb.append(SpannableHepler.Txt("，"))
        sb.append(SpannableHepler.Txt(it.getTypeValue()))
        sb.append(SpannableHepler.Txt("，"))
        sb.append(SpannableHepler.Txt(it.money))
        sb.append(SpannableHepler.Txt("\n"))
    }
    return sb.builder().toString()
}

data class OrderCommonRuleItem(
    var ruleId: String = "",
    var ruleName: String = ""
)

fun Rsp23QueryMobileOrderCommonInfo.showLatestSettleBasisType(): String {
    return when (latestSettleBasisType) {
        "1" -> {
            "按发货磅单结算"
        }

        "2" -> {
            "按收货磅单结算"
        }

        "3" -> {
            "按收发货磅单中较小吨位值结算"
        }

        else -> {
            ""
        }
    }
}

fun Rsp23QueryMobileOrderCommonInfo.getDeliverNewGoodsTimeData(): DeliverNewGoodsTimeData {
    return DeliverNewGoodsTimeData(
        validity = validity,
        effectiveStartTime = effectiveStartTime,
        effectiveEndTime = effectiveEndTime
    )
}

fun Rsp23QueryMobileOrderCommonInfo.formatRuleNameJSONArrayV1(): List<OrderCommonRuleItem> {
    return mutableListOf<OrderCommonRuleItem>().apply {
        addAll(ruleNameJSONArray ?: emptyList())
    }
}

fun Rsp23QueryMobileOrderCommonInfo.formatRuleNameJSONArray(): List<OrderCommonRuleItem> {
    return mutableListOf(OrderCommonRuleItem(ruleId = "-101", ruleName = "无")).apply {
        addAll(ruleNameJSONArray ?: emptyList())
    }
}

fun Rsp23QueryMobileOrderCommonInfo.overTonConfig(): Boolean {
    return (overTonConfigValue.toDoubleOrNull() ?: 0.0) > 0.00
}
