package com.zczy.cargo_owner.deliver.addorder.container.model

import com.sfh.lib.exception.HandleException
import com.sfh.lib.mvvm.service.BaseViewModel
import com.sfh.lib.rx.IResult
import com.zczy.cargo_owner.deliver.addorder.container.req.ReqDeliverContainerList
import com.zczy.cargo_owner.deliver.addorder.container.req.RspDeliverContainerList
import com.zczy.comm.http.entity.BaseRsp
import com.zczy.comm.http.entity.PageList

/**
 * 功能描述: 集装箱列表model
 * <AUTHOR>
 * @date 2022/2/23-10:22
 */

class DeliverContainerListModel : BaseViewModel() {

    fun queryList(req: ReqDeliverContainerList) {
        execute(req, object : IResult<BaseRsp<PageList<RspDeliverContainerList>>> {
            override fun onSuccess(bean: BaseRsp<PageList<RspDeliverContainerList>>) {
                if (bean.success()) {
                    setValue("onQuerySuccess", bean.data)
                } else {
                    setValue("onQuerySuccess", null)
                }
            }

            override fun onFail(p0: HandleException?) {
                setValue("onQuerySuccess", null)
            }
        })
    }
}