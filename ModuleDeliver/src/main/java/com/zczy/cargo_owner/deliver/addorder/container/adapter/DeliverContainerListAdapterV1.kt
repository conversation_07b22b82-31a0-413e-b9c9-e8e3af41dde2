package com.zczy.cargo_owner.deliver.addorder.container.adapter

import android.view.ViewGroup
import com.chad.library.adapter.base.BaseQuickAdapter
import com.chad.library.adapter.base.BaseViewHolder
import com.zczy.cargo_owner.deliver.R
import com.zczy.cargo_owner.deliver.addorder.req.container.ContainerList
import com.zczy.comm.utils.CommUtils
import com.zczy.comm.widget.inputv2.InputViewEdit

/**
 * 功能描述: 集装箱列表
 * <AUTHOR>
 * @date 2022/2/23-10:54
 */

class DeliverContainerListAdapterV1 :
    BaseQuickAdapter<ContainerList, BaseViewHolder>(R.layout.deliver_container_list_item_v1) {
    override fun onCreateViewHolder(parent: ViewGroup, viewType: Int): BaseViewHolder {
        return super.onCreateViewHolder(parent, viewType).apply {
            val inputContainerWeight = getView<InputViewEdit>(R.id.inputContainerWeight)
            CommUtils.setEditTextInputType(inputContainerWeight.editText, 4)
            inputContainerWeight.setTypeNum()
            val inputContainerName = getView<InputViewEdit>(R.id.inputContainerName)
            val editListener = object : InputViewEdit.Listener() {
                override fun onTextChanged(viewId: Int, view: InputViewEdit, s: String) {
                    val tag = view.tag
                    if (tag is ContainerList) {
                        when (viewId) {
                            R.id.inputContainerName -> {
                                tag.containerNo = s
                            }
                            R.id.inputContainerWeight -> {
                                tag.containerUnitWeight = s
                            }
                        }
                    }
                }
            }
            inputContainerName.setListener(editListener)
            inputContainerWeight.setListener(editListener)
        }
    }

    override fun convert(helper: BaseViewHolder?, item: ContainerList?) {
        helper?.let {
            if (mData.size <= 1) {
                //只有一个规格的时候 不予许删除
                it.setGone(R.id.tvTrash, false)
            } else {
                //存在多个的时候可以删除
                it.setGone(R.id.tvTrash, true)
            }
            val inputContainerName = it.getView<InputViewEdit>(R.id.inputContainerName)
            inputContainerName.tag = item
            val inputContainerWeight = it.getView<InputViewEdit>(R.id.inputContainerWeight)
            inputContainerWeight.tag = item
            item?.apply {
                inputContainerName.content = item.containerNo
                inputContainerWeight.content = item.containerUnitWeight
            }
            it.addOnClickListener(R.id.tvTrash)
        }
    }
}