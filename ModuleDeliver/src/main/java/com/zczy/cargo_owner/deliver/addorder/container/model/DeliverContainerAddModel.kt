package com.zczy.cargo_owner.deliver.addorder.container.model

import com.sfh.lib.mvvm.service.BaseViewModel
import com.sfh.lib.ui.dialog.DialogBuilder
import com.zczy.cargo_owner.deliver.addorder.container.req.ReqAddContainer

/**
 * 功能描述: 新增集装箱
 * <AUTHOR>
 * @date 2022/2/25-14:10
 */

class DeliverContainerAddModel : BaseViewModel() {

    fun addContainer(req: ReqAddContainer) {
        execute(req) {
            if (it.success()) {
                setValue("onAddSuccess")
            } else {
                val dialogBuilder = DialogBuilder()
                dialogBuilder.message = it.msg
                dialogBuilder.isHideCancel = true
                showDialog(dialogBuilder)
            }
        }
    }
}