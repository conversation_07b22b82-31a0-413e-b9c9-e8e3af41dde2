package com.zczy.cargo_owner.deliver.addorder.bean

// 指定承运方信息
data class AppointCarrierInfoStr(
    var appointCarrierMobileListStr: String = "", // 指定承运方手机号字符串集合
    var appointCarrierUserIdListStr: String = "", // 指定承运方用户id字符串集合
    var appointCarrierNameListStr: String = "", // 指定承运方用户名称字符串集合
    var appointCarrierUserTypeListStr: String = "", // 指定承运方用户类型字符串集合
    var appointCarrierIfExistListStr: String = "", // 指定承运方是否关联货主字符串集合
    var appointCarrierRelationTypeListStr: String = "",// 指定承运方 0道路运输证缺失、1黑名单司机
    var appointCarrierIsDeleteListStr: String = "",// 指定承运方 1删除
    var appointCarrierRecommendLevelListStr: String = "",
    var appointCarrierTypeListStr: String = "",
)

// 指定承运方信息-极速好货
data class FastAppointCarrierInfoStr(
    var appointCarrierUserIdListStr: String = "", // 指定承运人id
    var appointCarrierMobileListStr: String = "", // 手机号
    var appointCarrierNameListStr: String = "", // 指定承运人姓名
    var appointCarrierTypeListStr: String = "", // 指定承运人类型 2 高级承运人,3 承运商,10 车老板'
    var appointCarrierIfExistListStr: String = "", // 指定承运方是否关联货主 0 否 1:是
)