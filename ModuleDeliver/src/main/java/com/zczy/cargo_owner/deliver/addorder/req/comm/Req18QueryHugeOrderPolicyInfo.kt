package com.zczy.cargo_owner.deliver.addorder.req.comm

import com.zczy.cargo_owner.deliver.addorder.bean.OrderPolicyInfo
import com.zczy.comm.http.entity.BaseNewRequest
import com.zczy.comm.http.entity.BaseRsp
import com.zczy.comm.http.entity.ResultData

/**
 * PS: 18.PC端和手机端接口：批量货：根据货物名称，货值，赠保额度，批量货保障服务费倍数：查询保障服务相关信息
 * Created by sdx on 2019/3/8.
 */
data class Req18QueryHugeOrderPolicyInfo(
    var cargoNameStr: String = "", //货物名称字符串集合(多货以逗号分隔)
    var orderModel: String = "",
    var giftMoney: String = "", //赠保额度
    var policyTimes: String = "", //批量货保障服务费倍数
    var changePolicyToCarrier: String = "", // 是否转嫁保险， 1是
    var weight: String = "",// 总吨位
    var despatchPro: String? = null, //启运地省
    var despatchCity: String? = null, //启运地市
    var despatchDis: String? = null, // 启运地区
    var despatchPlace: String? = null, // 启运地详细地址
    var despatchCoordinateX: String? = null, //启运地坐标X
    var despatchCoordinateY: String? = null, //启运地坐标Y
    var deliverPro: String? = null, //目的地省
    var deliverCity: String? = null, // 目的地市
    var deliverDis: String? = null, //目的地区
    var deliverPlace: String? = null, // 目的地详细地址
    var deliverCoordinateX: String? = null, // 目的地坐标X
    var deliverCoordinateY: String? = null, //目的地坐标Y
    var unit: String? = null,//1 重货，2 泡货。
    var cargoMoney: String = "", //单车货值
    var totalMoney: String = "", //货主价总价（和原本保持一致即可）
    var vehicleWeight: String? = null, //单车吨位（每车指导承运数量）
) : BaseNewRequest<BaseRsp<RspQueryHugeOrderPolicyInfo>>("oms-app/order/addOuterCommonOrder/queryHugeOrderPolicyInfo")

data class RspQueryHugeOrderPolicyInfo(
    var supportPolicyFlag: String = "", // 是否支持购买货物保障服务：1 是，0 否
    var specialCargo: String = "", // 是否特殊货物: 1是，0否
    var isSpecialConfig: String = "", //是否特殊定价:1 是,0 否
    var hugeOrderPolicyInfo: OrderPolicyInfo = OrderPolicyInfo(),
) : ResultData()