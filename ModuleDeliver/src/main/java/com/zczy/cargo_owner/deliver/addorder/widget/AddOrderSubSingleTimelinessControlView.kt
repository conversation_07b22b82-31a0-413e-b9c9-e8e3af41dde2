package com.zczy.cargo_owner.deliver.addorder.widget

import android.content.Context
import android.graphics.Color
import androidx.constraintlayout.widget.ConstraintLayout
import android.text.InputType
import android.util.AttributeSet
import android.view.LayoutInflater
import com.zczy.cargo_owner.deliver.R
import com.zczy.comm.widget.inputv2.InputViewEdit
import kotlinx.android.synthetic.main.deliver_add_order_sub_single_timeliness_control_view.view.inputSubSingleShippingTimeliness

/**
 *@Desc 子单时效控制
 *@User ssp
 *@Date 2023/8/7-16:24
 */

class AddOrderSubSingleTimelinessControlView :
    ConstraintLayout {

    var block1: (s: String) -> Unit = {

    }
    var block2: () -> Unit = {

    }

    constructor(context: Context) : super(context)

    constructor(context: Context, attrs: AttributeSet?) : super(context, attrs)

    constructor(context: Context, attrs: AttributeSet?, defStyleAttr: Int) : super(context, attrs, defStyleAttr)

    init {
        setBackgroundColor(Color.WHITE)
        LayoutInflater.from(context).inflate(R.layout.deliver_add_order_sub_single_timeliness_control_view, this)
        initView()
    }

    private fun initView() {
        inputSubSingleShippingTimeliness.setInputType(InputType.TYPE_CLASS_NUMBER)
        inputSubSingleShippingTimeliness.setMaxLength(2)
        inputSubSingleShippingTimeliness.setListener(object : InputViewEdit.Listener() {
            override fun onTextChanged(viewId: Int, view: InputViewEdit, s: String) {
                block1(s)
            }

            override fun onClickTitleRight(viewId: Int, view: InputViewEdit) {
                super.onClickTitleRight(viewId, view)
                block2()
            }
        })
    }

    fun setData(s: String) {
        inputSubSingleShippingTimeliness.content = s
    }
}