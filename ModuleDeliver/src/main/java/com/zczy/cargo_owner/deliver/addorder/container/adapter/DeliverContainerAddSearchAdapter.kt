package com.zczy.cargo_owner.deliver.addorder.container.adapter

import android.text.TextUtils
import android.widget.ImageView
import com.chad.library.adapter.base.BaseQuickAdapter
import com.chad.library.adapter.base.BaseViewHolder
import com.zczy.cargo_owner.deliver.R
import com.zczy.cargo_owner.deliver.addorder.container.req.RspDeliverContainerList

/**
 * 功能描述: 集装箱列表
 * <AUTHOR>
 * @date 2022/2/23-10:54
 */

class DeliverContainerAddSearchAdapter :
    BaseQuickAdapter<RspDeliverContainerList, BaseViewHolder>(R.layout.deliver_container_add_search_item) {

    private var switchItem: RspDeliverContainerList = RspDeliverContainerList()

    override fun convert(helper: BaseViewHolder?, item: RspDeliverContainerList?) {
        helper?.let {
            item?.apply {
                it.setText(R.id.tvName, containerName)
                    .setText(R.id.tvSpecification, "规格：$containerStandard")
                    .setText(R.id.tvWeight, "配货毛重：$containerWeight")
                if (TextUtils.equals(containerId, switchItem.containerId)) {
                    it.getView<ImageView>(R.id.imgSelect).isSelected = true
                    it.setBackgroundRes(R.id.clBg, R.drawable.file_f5f5f5_radius)
                } else {
                    it.getView<ImageView>(R.id.imgSelect).isSelected = false
                    it.setBackgroundRes(R.id.clBg, R.drawable.file_ffffff_corners)
                }
                it.addOnClickListener(R.id.imgSelect)
            }
        }
    }

    fun setSwitchItem(position: Int) {
        when (switchItem.position) {
            -1 -> {
                //第一次点击选择 直接替换当前选择数据
                switchItem = mData[position]
                switchItem.position = position
                notifyItemChanged(position, switchItem)
            }
            else -> {
                if (switchItem.position != position) {
                    //更换了选择
                    val tempPosition = switchItem.position
                    switchItem = RspDeliverContainerList()
                    notifyItemChanged(tempPosition, tempPosition)
                    switchItem = data[position]
                    switchItem.position = position
                    notifyItemChanged(position, switchItem)
                }
            }
        }
    }

    fun getSwitchItem(): RspDeliverContainerList {
        return switchItem
    }
}