package com.zczy.cargo_owner.deliver.addorder.widget

import android.content.Context
import android.graphics.Color
import android.text.InputType
import android.text.SpannableString
import android.text.SpannableStringBuilder
import android.text.Spanned
import android.text.style.ForegroundColorSpan
import android.util.AttributeSet
import android.view.LayoutInflater
import android.widget.LinearLayout
import androidx.core.content.ContextCompat
import com.zczy.cargo_owner.deliver.R
import com.zczy.cargo_owner.deliver.addorder.bean.OrderReceiptInfo
import com.zczy.cargo_owner.deliver.addorder.bean.formatAddress
import com.zczy.cargo_owner.deliver.addorder.req.batch.ReqAddHugeOrderForSeniorConsignor
import com.zczy.cargo_owner.deliver.addorder.req.comm.Rsp23QueryMobileOrderCommonInfo
import com.zczy.cargo_owner.deliver.addorder.req.comm.Rsp24QueryMobileHugeOrderCommonInfo
import com.zczy.cargo_owner.deliver.addorder.req.nil.ReqAddLTCOrderForSeniorConsignor
import com.zczy.cargo_owner.deliver.addorder.req.normal.ReqAddOrderForSeniorConsignor
import com.zczy.comm.SpannableHepler
import com.zczy.comm.utils.ex.isTrue
import com.zczy.comm.utils.ex.setVisible
import com.zczy.comm.widget.inputv2.InputViewCheckV2
import com.zczy.comm.widget.inputv2.InputViewClick
import kotlinx.android.synthetic.main.deliver_add_order_deposit_view.view.clSelectLeft
import kotlinx.android.synthetic.main.deliver_add_order_deposit_view.view.clSelectRight
import kotlinx.android.synthetic.main.deliver_add_order_deposit_view.view.input_receipt
import kotlinx.android.synthetic.main.deliver_add_order_deposit_view.view.input_receipt_address
import kotlinx.android.synthetic.main.deliver_add_order_deposit_view.view.input_receipt_money
import kotlinx.android.synthetic.main.deliver_add_order_deposit_view.view.input_receipt_money_v1
import kotlinx.android.synthetic.main.deliver_add_order_deposit_view.view.input_receipt_v1
import kotlinx.android.synthetic.main.deliver_add_order_deposit_view.view.ivSelectLeft
import kotlinx.android.synthetic.main.deliver_add_order_deposit_view.view.ivSelectRight
import kotlinx.android.synthetic.main.deliver_add_order_deposit_view.view.tvSelectLeft
import kotlinx.android.synthetic.main.deliver_add_order_deposit_view.view.tvSelectRight

/**
 * 功能描述: 回单押金金额
 * <AUTHOR>
 * @date 2023/4/20-19:18
 */

class AddOrderDepositView : LinearLayout {
    var click1: () -> Unit = {

    }
    var click2: () -> Unit = {

    }
    var click3: () -> Unit = {

    }
    var click4: () -> Unit = {

    }
    var click5: () -> Unit = {

    }
    var click6: () -> Unit = {

    }
    var input1: (money: String) -> String = {
        ""
    }
    var input2: (money: String) -> String = {
        ""
    }

    companion object {
        const val SELECT_MODE = ""
        const val SELECT_MODE_1 = "1"
        const val SELECT_MODE_2 = "2"
    }

    constructor(context: Context) : super(context)

    constructor(context: Context, attrs: AttributeSet?) : super(context, attrs)

    constructor(context: Context, attrs: AttributeSet?, defStyleAttr: Int) : super(context, attrs, defStyleAttr)

    init {
        orientation = VERTICAL
        setBackgroundColor(Color.WHITE)
        LayoutInflater.from(context).inflate(R.layout.deliver_add_order_deposit_view, this)
        initView()
    }

    private fun initView() {
        // 是否押回单
        input_receipt.setListener(object : InputViewCheckV2.Listener() {
            override fun onClickCheck(
                viewId: Int,
                view: InputViewCheckV2,
                check: Int,
                radioStr: String
            ): Boolean {
                when (check) {
                    InputViewCheckV2.LEFT -> {
                        // 查询押回单信息
                        click1()
                        return false
                    }

                    InputViewCheckV2.RIGHT -> {
                        // 清空数据
                        click2()
                        return false
                    }
                }
                return true
            }

            override fun onClickTitleRight(viewId: Int, view: InputViewCheckV2) {
                super.onClickTitleRight(viewId, view)
                click3()
            }
        })
        clSelectLeft.setOnClickListener {
            // 固额模式
            click5()
            setSelectMode(mode = SELECT_MODE_1)
        }
        clSelectRight.setOnClickListener {
            // 比例模式
            click6()
            setSelectMode(mode = SELECT_MODE_2)
        }
        // 回单押金金额
        input_receipt_money.setInputType(InputType.TYPE_CLASS_NUMBER)
        input_receipt_money.setListener(object : InputViewEditV2.Listener() {
            override fun onTextChanged(viewId: Int, view: InputViewEditV2, s: String) {
                val receiptMaxFixed = input1(s)
                setInputReceiptMoneyWarning(receiptMaxFixed)
            }
        })
        // 回单押金金额
        input_receipt_money_v1.setInputType(InputType.TYPE_CLASS_NUMBER)
        input_receipt_money_v1.setListener(object : InputViewEditV2.Listener() {
            override fun onTextChanged(viewId: Int, view: InputViewEditV2, s: String) {
                val receiptMaxRatio = input2(s)
                setInputReceiptMoneyV1Warning(receiptMaxRatio)
            }
        })
        // 回单收件地址
        input_receipt_address.setListener(object : InputViewClick.Listener() {
            override fun onClick(viewId: Int, view: InputViewClick, content: String) {
                click4()
            }
        })
    }

    private fun setSelectMode(mode: String = "") {
        // 1 固额模式 2 比例模式
        when (mode) {
            SELECT_MODE_1 -> {
                ivSelectLeft.isSelected = true
                tvSelectLeft.text = SpannableHepler().append(SpannableHepler.Txt("固额模式", "#3D85FF")).builder()
                ivSelectRight.isSelected = false
                tvSelectRight.text = SpannableHepler().append(SpannableHepler.Txt("比例模式", "#666666")).builder()
            }

            SELECT_MODE_2 -> {
                ivSelectLeft.isSelected = false
                tvSelectLeft.text = SpannableHepler().append(SpannableHepler.Txt("固额模式", "#666666")).builder()
                ivSelectRight.isSelected = true
                tvSelectRight.text = SpannableHepler().append(SpannableHepler.Txt("比例模式", "#3D85FF")).builder()
            }

            else -> {
                ivSelectLeft.isSelected = false
                tvSelectLeft.text = SpannableHepler().append(SpannableHepler.Txt("固额模式", "#666666")).builder()
                ivSelectRight.isSelected = false
                tvSelectRight.text = SpannableHepler().append(SpannableHepler.Txt("比例模式", "#666666")).builder()
            }
        }
    }

    fun setWarningV1(warning: Boolean) {
        input_receipt.setWarning(warning)
    }

    fun setWarningV2(warning: Boolean) {
        input_receipt_address.setWarning(warning)
    }

    fun setWarningV3(warning: Boolean) {
        input_receipt_money.setWarning(warning)
    }

    fun setWarningV4(warning: Boolean) {
        input_receipt_money_v1.setWarning(warning)
    }

    fun setInputReceiptMoneyWarning(receiptMaxFixed: String) {

        val builder = SpannableStringBuilder()
        val spannableString = SpannableString("请填写正整数；金额不允许超过${receiptMaxFixed}")
        spannableString.setSpan(
            ForegroundColorSpan(Color.parseColor("#FF602E")),
            0,
            spannableString.length,
            Spanned.SPAN_EXCLUSIVE_EXCLUSIVE
        )
        builder.append(spannableString)
        input_receipt_money.setTitleSB2(builder)
        input_receipt_money.title2.setCompoundDrawablesWithIntrinsicBounds(ContextCompat.getDrawable(context, R.drawable.order_info_red), null, null, null)
        input_receipt_money.title2.setVisible(receiptMaxFixed.isNotEmpty())
    }

    fun setInputReceiptMoneyV1Warning(receiptMaxRatio: String) {
        val builder = SpannableStringBuilder()
        val spannableString = SpannableString("请填写正整数；比例不允许超过${receiptMaxRatio}%")
        spannableString.setSpan(
            ForegroundColorSpan(Color.parseColor("#FF602E")),
            0,
            spannableString.length,
            Spanned.SPAN_EXCLUSIVE_EXCLUSIVE
        )
        builder.append(spannableString)
        input_receipt_money_v1.setTitleSB2(builder)
        input_receipt_money_v1.title2.setCompoundDrawablesWithIntrinsicBounds(ContextCompat.getDrawable(context, R.drawable.order_info_red), null, null, null)
        input_receipt_money_v1.title2.setVisible(receiptMaxRatio.isNotEmpty())
    }

    fun refreshNormalReceiptView(mData: ReqAddOrderForSeniorConsignor, mOrderCommonInfo: Rsp23QueryMobileOrderCommonInfo) {
        // 货主是否开启押回单功能：0 否，1 是
        if (mData.orderInfo.modeType.isTrue) {
            <EMAIL>(false)
        } else if (mOrderCommonInfo.whetherShowBackOrderChoose.isTrue) {
            <EMAIL>(true)
            when (mData.orderInfo.orderModel) {
                "1" -> {
                    //议价
                    input_receipt_v1.setVisible(false)
                    input_receipt_money_v1.setVisible(false)
                    when (mData.orderInfo.receiptFlag) {
                        // 是否押回单：0 否,1 是
                        "0" -> {
                            input_receipt.check = InputViewCheckV2.RIGHT
                            input_receipt_money.setVisible(false)
                            input_receipt_address.setVisible(false)
                        }

                        "1" -> {
                            input_receipt.check = InputViewCheckV2.LEFT
                            input_receipt_money.setVisible(true)
                            input_receipt_address.setVisible(true)
                            input_receipt_money.content = mData.orderReceiptInfo.receiptMoney
                            input_receipt_address.content = mData.orderReceiptInfo.formatAddress()
                        }

                        else -> {
                            input_receipt.check = InputViewCheckV2.NONE
                            input_receipt_money.setVisible(false)
                            input_receipt_address.setVisible(false)
                        }
                    }
                }

                else -> {
                    //抢单
                    when (mData.orderInfo.receiptFlag) {
                        // 是否押回单：0 否,1 是
                        "0" -> {
                            input_receipt.check = InputViewCheckV2.RIGHT
                            input_receipt_v1.setVisible(false)
                            input_receipt_money.setVisible(false)
                            input_receipt_money_v1.setVisible(false)
                            input_receipt_address.setVisible(false)
                        }

                        "1" -> {
                            input_receipt.check = InputViewCheckV2.LEFT
                            input_receipt_v1.setVisible(true)
                            input_receipt_address.setVisible(true)
                            input_receipt_address.content = mData.orderReceiptInfo.formatAddress()
                            when (mData.orderReceiptInfo.receiptType) {
                                "1" -> {
                                    //固额模式
                                    setSelectMode(mode = SELECT_MODE_1)
                                    input_receipt_money.setVisible(true)
                                    input_receipt_money_v1.setVisible(false)
                                    input_receipt_money.content = mData.orderReceiptInfo.receiptMoney
                                }

                                "2" -> {
                                    //比例模式
                                    setSelectMode(mode = SELECT_MODE_2)
                                    input_receipt_money.setVisible(false)
                                    input_receipt_money_v1.setVisible(true)
                                    input_receipt_money_v1.content = mData.orderReceiptInfo.receiptMoney
                                }

                                else -> {
                                    setSelectMode(mode = SELECT_MODE)
                                    input_receipt_money.setVisible(false)
                                    input_receipt_money_v1.setVisible(false)
                                }
                            }
                        }

                        else -> {
                            input_receipt.check = InputViewCheckV2.NONE
                            input_receipt_v1.setVisible(false)
                            input_receipt_money.setVisible(false)
                            input_receipt_money_v1.setVisible(false)
                        }
                    }
                }
            }
        } else {
            <EMAIL>(false)
            mData.orderInfo.receiptFlag = "0"
            mData.orderReceiptInfo = OrderReceiptInfo()
        }
    }

    fun refreshNilReceiptView(mData: ReqAddLTCOrderForSeniorConsignor, mOrderCommonInfo: Rsp23QueryMobileOrderCommonInfo) {
        // 货主是否开启押回单功能：0 否，1 是
        if (mOrderCommonInfo.whetherShowBackOrderChoose.isTrue) {
            <EMAIL>(true)
            when (mData.orderInfo.orderModel) {
                "1" -> {
                    //议价
                    input_receipt_v1.setVisible(false)
                    input_receipt_money_v1.setVisible(false)
                    when (mData.orderInfo.receiptFlag) {
                        // 是否押回单：0 否,1 是
                        "0" -> {
                            input_receipt.check = InputViewCheckV2.RIGHT
                            input_receipt_money.setVisible(false)
                            input_receipt_address.setVisible(false)
                        }

                        "1" -> {
                            input_receipt.check = InputViewCheckV2.LEFT
                            input_receipt_money.setVisible(true)
                            input_receipt_address.setVisible(true)
                            input_receipt_money.content = mData.orderReceiptInfo.receiptMoney
                            input_receipt_address.content = mData.orderReceiptInfo.formatAddress()
                        }

                        else -> {
                            input_receipt.check = InputViewCheckV2.NONE
                            input_receipt_money.setVisible(false)
                            input_receipt_address.setVisible(false)
                        }
                    }
                }

                else -> {
                    //抢单
                    when (mData.orderInfo.receiptFlag) {
                        // 是否押回单：0 否,1 是
                        "0" -> {
                            input_receipt.check = InputViewCheckV2.RIGHT
                            input_receipt_v1.setVisible(false)
                            input_receipt_money.setVisible(false)
                            input_receipt_money_v1.setVisible(false)
                            input_receipt_address.setVisible(false)
                        }

                        "1" -> {
                            input_receipt.check = InputViewCheckV2.LEFT
                            input_receipt_v1.setVisible(true)
                            input_receipt_address.setVisible(true)
                            input_receipt_address.content = mData.orderReceiptInfo.formatAddress()
                            when (mData.orderReceiptInfo.receiptType) {
                                "1" -> {
                                    //固额模式
                                    setSelectMode(mode = SELECT_MODE_1)
                                    input_receipt_money.setVisible(true)
                                    input_receipt_money_v1.setVisible(false)
                                    input_receipt_money.content = mData.orderReceiptInfo.receiptMoney
                                }

                                "2" -> {
                                    //比例模式
                                    setSelectMode(mode = SELECT_MODE_2)
                                    input_receipt_money.setVisible(false)
                                    input_receipt_money_v1.setVisible(true)
                                    input_receipt_money_v1.content = mData.orderReceiptInfo.receiptMoney
                                }

                                else -> {
                                    setSelectMode(mode = SELECT_MODE)
                                    input_receipt_money.setVisible(false)
                                    input_receipt_money_v1.setVisible(false)
                                }
                            }
                        }

                        else -> {
                            input_receipt.check = InputViewCheckV2.NONE
                            input_receipt_v1.setVisible(false)
                            input_receipt_money.setVisible(false)
                            input_receipt_money_v1.setVisible(false)
                        }
                    }
                }
            }
        } else {
            <EMAIL>(false)
            mData.orderInfo.receiptFlag = "0"
            mData.orderReceiptInfo = OrderReceiptInfo()
        }
    }

    fun refreshBatchReceiptView(mData: ReqAddHugeOrderForSeniorConsignor, mOrderCommonInfo: Rsp24QueryMobileHugeOrderCommonInfo) {
        // 货主是否开启押回单功能：0 否，1 是
        if (mOrderCommonInfo.whetherShowBackOrderChoose.isTrue) {
            <EMAIL>(true)
            when (mData.hugeOrderInfo.orderModel) {
                "1" -> {
                    //议价
                    input_receipt_v1.setVisible(false)
                    input_receipt_money_v1.setVisible(false)
                    when (mData.hugeOrderInfo.receiptFlag) { // 是否押回单：0 否,1 是
                        "0" -> {
                            input_receipt.check = InputViewCheckV2.RIGHT
                            input_receipt_money.setVisible(false)
                            input_receipt_address.setVisible(false)
                        }

                        "1" -> {
                            input_receipt.check = InputViewCheckV2.LEFT
                            input_receipt_money.setVisible(true)
                            input_receipt_address.setVisible(true)
                            input_receipt_money.content = mData.hugeOrderReceiptInfo.receiptMoney
                            input_receipt_address.content = mData.hugeOrderReceiptInfo.formatAddress()
                        }

                        else -> {
                            input_receipt.check = InputViewCheckV2.NONE
                            input_receipt_money.setVisible(false)
                            input_receipt_address.setVisible(false)
                        }
                    }
                }

                else -> {
                    //抢单
                    when (mData.hugeOrderInfo.receiptFlag) { // 是否押回单：0 否,1 是
                        "0" -> {
                            input_receipt.check = InputViewCheckV2.RIGHT
                            input_receipt_v1.setVisible(false)
                            input_receipt_money.setVisible(false)
                            input_receipt_money_v1.setVisible(false)
                            input_receipt_address.setVisible(false)
                        }

                        "1" -> {
                            input_receipt.check = InputViewCheckV2.LEFT
                            input_receipt_v1.setVisible(true)
                            input_receipt_address.setVisible(true)
                            input_receipt_address.content = mData.hugeOrderReceiptInfo.formatAddress()
                            when (mData.hugeOrderReceiptInfo.receiptType) {
                                "1" -> {
                                    //固额模式
                                    setSelectMode(mode = SELECT_MODE_1)
                                    input_receipt_money.setVisible(true)
                                    input_receipt_money_v1.setVisible(false)
                                    input_receipt_money.content = mData.hugeOrderReceiptInfo.receiptMoney
                                }

                                "2" -> {
                                    //比例模式
                                    setSelectMode(mode = SELECT_MODE_2)
                                    input_receipt_money.setVisible(false)
                                    input_receipt_money_v1.setVisible(true)
                                    input_receipt_money_v1.content = mData.hugeOrderReceiptInfo.receiptMoney
                                }

                                else -> {
                                    setSelectMode(mode = SELECT_MODE)
                                    input_receipt_money.setVisible(false)
                                    input_receipt_money_v1.setVisible(false)
                                }
                            }
                        }

                        else -> {
                            input_receipt.check = InputViewCheckV2.NONE
                            input_receipt_v1.setVisible(false)
                            input_receipt_money.setVisible(false)
                            input_receipt_money_v1.setVisible(false)
                        }
                    }
                }
            }
        } else {
            <EMAIL>(false)
            mData.hugeOrderInfo.receiptFlag = "0"
            mData.hugeOrderReceiptInfo = OrderReceiptInfo()
        }
    }
}