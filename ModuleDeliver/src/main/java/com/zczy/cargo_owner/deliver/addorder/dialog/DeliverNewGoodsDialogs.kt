package com.zczy.cargo_owner.deliver.addorder.dialog

import android.content.Context
import android.graphics.Color
import android.view.Gravity
import androidx.fragment.app.Fragment
import com.sfh.lib.ui.dialog.DialogBuilder
import com.zczy.cargo_owner.deliver.R
import com.zczy.cargo_owner.deliver.addorder.req.comm.OrderCommonRuleItem
import com.zczy.comm.TimePickerUtilV1
import com.zczy.comm.utils.ex.YYYY_MM_DD
import com.zczy.comm.utils.ex.YYYY_MM_DD_HH_MM
import com.zczy.comm.utils.ex.getFormatTime
import com.zczy.comm.widget.dialog.ChooseDialogV1
import com.zczy.comm.widget.pickerview.CustomTopBar
import com.zczy.comm.widget.pickerview.TimePickerUtil
import com.zczy.comm.widget.pickerview.picker.BasePicker
import com.zczy.comm.widget.pickerview.picker.TimePicker
import com.zczy.comm.widget.pickerview.widget.DefaultCenterDecoration
import com.zczy.comm.widget.pickerview.widget.PickerView
import java.util.Calendar

/**
 * PS:
 * Created by sdx on 2019/2/15.
 */

/**
 * 装货开始时间需要在当前时间1小时之后
 */
class DeliverNewGoodsNormalDialog {
    companion object {
        /**
         * 选择时间
         */
        @JvmStatic
        fun chooseTimeDialog(
            context: Context?, title: String,
            data: Calendar,
            timePickerType:Int?= (TimePicker.TYPE_DATE or TimePicker.TYPE_HOUR or TimePicker.TYPE_MINUTE),
            block: (String) -> Unit
        ) {
            if (context == null) return
            TimePickerUtilV1.showComm(context = context, title = title, timePickerType = timePickerType, selectedDate = data.timeInMillis) { d ->
                block(d.getFormatTime(YYYY_MM_DD_HH_MM))
            }
        }
        /**
         * 选择时间
         */
        @JvmStatic
        fun chooseTimeDialogV2(
            context: Context?, title: String,
            data: Calendar?,
            timePickerType:Int?= (TimePicker.TYPE_DATE or TimePicker.TYPE_HOUR or TimePicker.TYPE_MINUTE),
            block: (String) -> Unit
        ) {
            if (context == null) return
            TimePickerUtilV1.showCommV2(context = context, title = title, timePickerType = timePickerType, data = data) { d ->
                block(d.getFormatTime(YYYY_MM_DD_HH_MM))
            }
        }

        /**
         * 选择时间
         */
        @JvmStatic
        fun chooseTimeDialogV1(context: Context?, title: String, data: Calendar, block: (String) -> Unit) {
            if (context == null) return

            TimePickerUtilV1.showComm(context, title, data.timeInMillis) { d ->
                block(d.getFormatTime(YYYY_MM_DD_HH_MM))
            }
        }

        @JvmStatic
        fun chooseTimeYMD(context: Context?, title: String, data: Calendar, block: (String) -> Unit) {
            if (context == null) return

            PickerView.sOutTextSize = 17
            PickerView.sCenterTextSize = 18
            PickerView.sCenterColor = Color.BLACK
            PickerView.sOutColor = Color.parseColor("#A3A3A3")
            PickerView.sDefaultVisibleItemCount = 5
            BasePicker.sDefaultPickerBackgroundColor = Color.WHITE
            DefaultCenterDecoration.sDefaultLineColor = Color.TRANSPARENT

            val now = data.timeInMillis

            val mTimePicker = TimePicker.Builder(context, TimePicker.TYPE_YEAR or TimePicker.TYPE_MONTH or TimePicker.TYPE_DAY
            ) { _, date ->
                block(date.getFormatTime(YYYY_MM_DD))
            }
                // 设置时间区间
                .setRangDate(1526361240000L, 1893563460000L)
                // 设置选中时间
                .setSelectedDate(now)
                // 设置 Formatter
                .setFormatter(object : TimePicker.DefaultFormatter() {
                    // 自定义Formatter显示去年，今年，明年
                    override fun format(picker: TimePicker, type: Int, position: Int, num: Long): CharSequence {
                        return when (type) {
                            TimePicker.TYPE_MIXED_DATE -> {
                                val calendar = Calendar.getInstance()
                                calendar.timeInMillis = num
                                calendar.time.getFormatTime(YYYY_MM_DD)
                            }

                            else -> super.format(picker, type, position, num)
                        }
                    }
                }).create()

            val topBar = CustomTopBar(mTimePicker.rootLayout)
            topBar.titleView.text = title
            topBar.setDividerHeight(0f).setDividerColor(Color.parseColor("#eeeeee"))
            mTimePicker.topBar = topBar
            mTimePicker.show()
        }

        @JvmStatic
        fun cancelDialog(block: () -> Unit): DialogBuilder {
            return DialogBuilder()
                .setTitle("提示")
                .setMessage("是否放弃发布？")
                .setGravity(Gravity.CENTER)
                .setOKTextColor("确定", R.color.text_blue)
                .setOkListener { dialogInterface, _ ->
                    dialogInterface.dismiss()
                    block()
                }
        }

        @JvmStatic
        fun settleBasisTypeDialog(
            fragment: Fragment,
            showCalculation: Boolean = false,
            block: (String) -> Unit
        ) {
            val list = mutableListOf<String>()
            // 结算依据：1 按发货磅单结算  2 按收货磅单结算  默认2
            if (showCalculation) {
                list.add("按发货磅单结算")
                list.add("按收货磅单结算")
                list.add("按收发货磅单中较小吨位值结算")
                list.add("按理计重量结算")
            } else {
                list.add("按发货磅单结算")
                list.add("按收货磅单结算")
                list.add("按收发货磅单中较小吨位值结算")
            }
            ChooseDialogV1.instance(list)
                .setTitle("请选择结算依据")
                .setChoose("")
                .setClick { s, _ ->
                    // 结算依据：1 按发货磅单结算  2 按收货磅单结算  默认2
                    when (s) {
                        list[0] -> {
                            block("1")
                        }

                        list[1] -> {
                            block("2")
                        }

                        list[2] -> {
                            block("3")
                        }

                        list[3] -> {
                            block("4")
                        }
                    }
                }
                .show(fragment)
        }

        @JvmStatic
        fun ruleDialog(
            fragment: Fragment,
            list: List<OrderCommonRuleItem>?,
            rule: OrderCommonRuleItem,
            block: (OrderCommonRuleItem) -> Unit
        ) {
            // 结算依据：1 按发货磅单结算  2 按收货磅单结算  默认2

            ChooseDialogV1.instance(list ?: emptyList())
                .setTitle("自动成交规则")
                .setChoose(rule)
                .setFlatMap { ruleName }
                .setFlatEquals { i1, i2 ->
                    i1 == i2
                }
                .setClick { s, _ ->
                    block(s)
                }
                .show(fragment)
        }
    }
}