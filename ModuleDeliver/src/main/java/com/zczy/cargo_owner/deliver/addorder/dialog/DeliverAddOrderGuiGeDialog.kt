package com.zczy.cargo_owner.deliver.addorder.dialog

import android.os.Bundle
import android.view.View
import android.widget.Toast
import com.sfh.lib.utils.UtilTool
import com.zczy.cargo_owner.deliver.R
import com.zczy.comm.ui.BaseDialog
import com.zczy.comm.utils.ex.toDoubleRoundDown
import kotlinx.android.synthetic.main.deliver_add_order_huowu_guige_dialog.btn_clear
import kotlinx.android.synthetic.main.deliver_add_order_huowu_guige_dialog.ed_chang
import kotlinx.android.synthetic.main.deliver_add_order_huowu_guige_dialog.ed_gao
import kotlinx.android.synthetic.main.deliver_add_order_huowu_guige_dialog.ed_kuan
import kotlinx.android.synthetic.main.deliver_add_order_huowu_guige_dialog.tv_cancel
import kotlinx.android.synthetic.main.deliver_add_order_huowu_guige_dialog.tv_ok

class DeliverAddOrderGuiGeDialog : BaseDialog() {

    private var cargoLength: String = ""
    private var cargoWidth: String = ""
    private var cargoHeight: String = ""

    private var listener: (String, String, String) -> Unit = { _, _, _ -> }
    override fun getDialogTag(): String = "DeliverAddOrderGuiGeDialog"

    override fun getDialogLayout(): Int = R.layout.deliver_add_order_huowu_guige_dialog

    override fun bindView(view: View, bundle: Bundle?) {
        reAdjustView(0, 0)
        initView(view)
    }

    override fun getDialogType(): DialogType {
        return DialogType.bottom
    }

    private fun initView(view: View) {

        UtilTool.setEditTextInputSize(ed_chang, 2)
        UtilTool.setEditTextInputSize(ed_kuan, 2)
        UtilTool.setEditTextInputSize(ed_gao, 2)

        ed_chang.setText(cargoLength)
        ed_kuan.setText(cargoWidth)
        ed_gao.setText(cargoHeight)

        btn_clear.setOnClickListener {
            ed_chang.setText("")
            ed_kuan.setText("")
            ed_gao.setText("")
        }

        tv_cancel.setOnClickListener {
            dismiss()
        }

        tv_ok.setOnClickListener {
            val change = ed_chang.text.toString().trim()
            val kuan = ed_kuan.text.toString().trim()
            val gao = ed_gao.text.toString().trim()
            if (change.isEmpty()) {
                Toast.makeText(context, "请输入长", Toast.LENGTH_LONG).show()
                return@setOnClickListener
            }
            val changeDouble = change.toDoubleRoundDown()
            if (changeDouble > 99.99 || changeDouble < 0.01) {
                Toast.makeText(context, "请输入0.01-99.99内的数值", Toast.LENGTH_LONG).show()
                return@setOnClickListener
            }
            if (kuan.isEmpty()) {
                Toast.makeText(context, "请输入宽", Toast.LENGTH_LONG).show()
            }
            val kuanDouble = kuan.toDoubleRoundDown()
            if (kuanDouble > 99.99 || kuanDouble < 0.01) {
                Toast.makeText(context, "请输入0.01-99.99内的数值", Toast.LENGTH_LONG).show()
                return@setOnClickListener
            }
            if (gao.isEmpty()) {
                Toast.makeText(context, "请输入高", Toast.LENGTH_LONG).show()
            }
            val gaoDouble = gao.toDoubleRoundDown()
            if (gaoDouble > 99.99 || gaoDouble < 0.01) {
                Toast.makeText(context, "请输入0.01-99.99内的数值", Toast.LENGTH_LONG).show()
                return@setOnClickListener
            }
            listener(change, kuan, gao)
            dismiss()
        }
    }

    fun setListener(listener: (String, String, String) -> Unit): DeliverAddOrderGuiGeDialog {
        this.listener = listener
        return this
    }

    fun setData(cargoLength: String, cargoWidth: String, cargoHeight: String): DeliverAddOrderGuiGeDialog {
        this.cargoLength = cargoLength
        this.cargoWidth = cargoWidth
        this.cargoHeight = cargoHeight
        return this
    }
}