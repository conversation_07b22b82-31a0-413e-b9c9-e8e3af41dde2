package com.zczy.cargo_owner.deliver.addorder.widget

import android.annotation.SuppressLint
import android.content.Context
import android.graphics.Color
import android.text.TextUtils
import android.util.AttributeSet
import android.view.LayoutInflater
import android.widget.ImageView
import android.widget.LinearLayout
import android.widget.TextView
import androidx.constraintlayout.widget.ConstraintLayout
import com.bigkoo.pickerview.builder.OptionsPickerBuilder
import com.bigkoo.pickerview.view.OptionsPickerView
import com.contrarywind.view.WheelView
import com.zczy.cargo_owner.deliver.R
import com.zczy.comm.SpannableHepler
import com.zczy.comm.TimePickerUtilV1
import com.zczy.comm.TimePickerUtilV1.YYYY
import com.zczy.comm.TimePickerUtilV1.YYYY_MM_DD_HH
import com.zczy.comm.utils.ex.YYYY_MM_DD_HH_MM
import com.zczy.comm.utils.ex.getFormatTime
import com.zczy.comm.utils.ex.setVisible
import com.zczy.comm.utils.ex.toCalendar
import com.zczy.comm.widget.inputv2.InputViewClick
import kotlinx.android.synthetic.main.deliver_add_order_time_view.view.input_end_time
import kotlinx.android.synthetic.main.deliver_add_order_time_view.view.input_start_time
import kotlinx.android.synthetic.main.deliver_add_order_time_view.view.tvEffectiveTime
import java.util.Calendar
import java.util.Date

/**
 * PS: 发货 装货时间 view
 * Created by sdx on 2019/3/7.
 */
class AddOrderTimeView : ConstraintLayout {

    var listener: Listener? = null

    constructor(context: Context) : super(context)

    constructor(context: Context, attrs: AttributeSet?) : super(context, attrs)

    constructor(context: Context, attrs: AttributeSet?, defStyleAttr: Int) : super(context, attrs, defStyleAttr)

    init {
        setBackgroundColor(Color.WHITE)
        LayoutInflater.from(context).inflate(R.layout.deliver_add_order_time_view, this)
        initView()
    }

    private fun initView() {
        input_start_time.setListener(object : InputViewClick.Listener() {
            override fun onClick(viewId: Int, view: InputViewClick, content: String) {
                TimePickerUtilV1.showCommV1(context = context, title = "装货开始时间", mType = "1") { date: Date? ->
                    input_start_time.content = date.getFormatTime(YYYY_MM_DD_HH) + ":00"
                    //自动带出 结束时间
                    val calendar = date?.toCalendar()
                    calendar?.add(Calendar.HOUR, 4)
                    input_end_time.content = calendar?.time.getFormatTime(YYYY_MM_DD_HH) + ":00"
                    listener?.onClickTime(view.content, input_end_time.content)
                }
            }
        })
        input_end_time.setListener(object : InputViewClick.Listener() {
            override fun onClick(viewId: Int, view: InputViewClick, content: String) {
                TimePickerUtilV1.showCommV1(context = context, title = "装货结束时间", mType = "2") { date: Date? ->
                    input_end_time.content = date.getFormatTime(YYYY_MM_DD_HH) + ":00"
                    listener?.onClickTime(input_start_time.content, view.content)
                }
            }
        })
    }

    @SuppressLint("SetTextI18n")
    fun setTime(startTime: String, endTime: String) {
        if (startTime.isEmpty() || endTime.isEmpty()) {
            input_start_time.content = ""
            input_end_time.content = ""
            tvEffectiveTime.setVisible(false)
        } else {
            input_start_time.content = startTime
            input_end_time.content = endTime
            tvEffectiveTime.text = "信息有效期  " + endTime.toCalendar(YYYY_MM_DD_HH_MM)?.apply {
                add(Calendar.HOUR, -1)
            }?.time.getFormatTime(YYYY_MM_DD_HH_MM)
            tvEffectiveTime.setVisible(true)
        }
    }

    fun setWarning(warning: Boolean) {
        if (warning) {
            setBackgroundResource(R.color.red_warning)
        } else {
            setBackgroundResource(R.color.white)
        }
    }

    interface Listener {
        fun onClickInfo()
        fun onClickTime(startTime: String, endTime: String)
    }
}