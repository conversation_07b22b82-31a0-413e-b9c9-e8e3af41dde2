package com.zczy.cargo_owner.deliver.cargo_name_management.bean

import com.zczy.comm.http.entity.BaseNewRequest
import com.zczy.comm.http.entity.BaseRsp
import com.zczy.comm.http.entity.ResultData

/**
 * @description
 * <AUTHOR> 韩正亚
 * @company 中储智运科技股份有限公司
 * @copyright （版权）中储南京智慧物流科技有限公司所有
 * @since 3/23/20 17:08
 */
data class DelCargoName(val ids: Array<CargoNameID>) : BaseNewRequest<BaseRsp<ResultData>>("mms-app/goodsController/batchDelete") {
    override fun equals(other: Any?): Boolean {
        if (this === other) return true
        if (javaClass != other?.javaClass) return false

        other as DelCargoName

        if (!ids.contentEquals(other.ids)) return false

        return true
    }

    override fun hashCode(): Int {
        return ids.contentHashCode()
    }
}

data class CargoNameID(val id: String? = null)