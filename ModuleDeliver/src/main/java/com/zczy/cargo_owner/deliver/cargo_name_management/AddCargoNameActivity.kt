package com.zczy.cargo_owner.deliver.cargo_name_management

import android.content.Context
import android.content.Intent
import android.os.Bundle
import android.text.Html
import android.text.TextUtils
import com.sfh.lib.event.RxBusEventManager
import com.sfh.lib.mvvm.annotation.LiveDataMatch
import com.sfh.lib.rx.ui.UtilRxView
import com.zczy.cargo_owner.deliver.R
import com.zczy.cargo_owner.deliver.cargo_name_management.bean.ReqAddCargoName
import com.zczy.cargo_owner.deliver.cargo_name_management.bean.ReqAddCargoNameV1
import com.zczy.cargo_owner.deliver.cargo_name_management.bean.ReqCheckCargoType
import com.zczy.cargo_owner.deliver.cargo_name_management.bean.ReqCheckCargoTypeV1
import com.zczy.cargo_owner.deliver.cargo_name_management.bean.RxBusAddCargoNameEvent
import com.zczy.cargo_owner.deliver.cargo_name_management.model.CargoNameManagementModel
import com.zczy.cargo_owner.deliver.cargo_name_management.widget.AddCargoNameImageView
import com.zczy.comm.ui.BaseActivity
import com.zczy.comm.utils.ex.isNull
import com.zczy.comm.utils.ex.setVisible
import com.zczy.comm.utils.imageselector.ImagePreviewActivity
import com.zczy.comm.utils.imageselector.ImageSelector
import kotlinx.android.synthetic.main.deliver_add_cargo_name.btnSaveCargoName
import kotlinx.android.synthetic.main.deliver_add_cargo_name.etCargoName
import kotlinx.android.synthetic.main.deliver_add_cargo_name.imageContainer
import kotlinx.android.synthetic.main.deliver_add_cargo_name.llAddCargoName
import kotlinx.android.synthetic.main.deliver_add_cargo_name.txtAddCargoNameTips

/**
 *描述：新增货物名称
 *auth:宋双朋
 *time:2024/6/18 15:17
 */

class AddCargoNameActivity : BaseActivity<CargoNameManagementModel>() {

    companion object {

        @JvmStatic
        fun start(context: Context?) {
            context ?: return
            val intent = Intent(context, AddCargoNameActivity::class.java)
            context.startActivity(intent)
        }
    }

    override fun getLayout(): Int {
        return R.layout.deliver_add_cargo_name
    }

    override fun initData() {
        UtilRxView.afterTextChangeEvents(etCargoName, 1000) { inputStr ->
            if (inputStr.isNotEmpty()) {
                //这是英文逗号
                if (inputStr[inputStr.length - 1] == ',') {
                    etCargoName.setText(inputStr.subSequence(0, inputStr.length - 1))
                    etCargoName.setSelection(etCargoName.text.toString().length)
                }
            }
            checkCargoName()
        }

        btnSaveCargoName.setOnClickListener {
            val cargoNameStr = etCargoName.text.toString()
            if (cargoNameStr.isNotBlank()) {
                //这是中文逗号
                val splitCargoNameStr = cargoNameStr.split("，")
                if (splitCargoNameStr.size > 100) {
                    showToast("货物名称不能超过100个")
                } else {
                    val req = ReqAddCargoName()
                    req.inputBaseName = cargoNameStr
                    req.cargoUrlDtoList = mutableListOf()
                    for (index in 0 until imageContainer.childCount) {
                        val imgView = imageContainer.getChildAt(index)
                        if (imgView is AddCargoNameImageView) {
                            val reqChild = ReqAddCargoNameV1()
                            reqChild.baseName = imgView.getTitleName()
                            reqChild.urls = imgView.getUrlList()
                            req.cargoUrlDtoList?.add(reqChild)
                        }
                    }
                    viewModel?.addCargoName(req)
                }
            }
        }
    }

    private fun checkCargoName() {
        val cargoNameStr = etCargoName.text.toString()
        val split = cargoNameStr.split("，")
        val cargoNameList = split.map { ReqCheckCargoTypeV1(baseName = it) }.toMutableList()
        getViewModel(CargoNameManagementModel::class.java).checkCargoType(
            req = ReqCheckCargoType(checkCargoTypeDtos = cargoNameList)
        ) { cargoList ->
            runOnUiThread {
                llAddCargoName.setVisible(cargoList.isNotEmpty())
                if (cargoList.isNotEmpty()) {
                    //需要上传特殊品照片
                    dealData(cargoList.mapIndexed { index, item ->
                        AddCargoNameImageView(context = this@AddCargoNameActivity)
                            .setData(item.baseName ?: "")
                            .setImgBlockClick { up, view ->
                                if (up) {
                                    //上传图片
                                    ImageSelector.open(
                                        this@AddCargoNameActivity,
                                        view.maxCount - view.imgList.size,
                                        true,
                                        index
                                    )
                                } else {
                                    //查看大图
                                    ImagePreviewActivity.start(
                                        activity = this@AddCargoNameActivity,
                                        imageData = view.imgList,
                                        0
                                    )
                                }
                                index
                            }
                    }.toMutableList())
                } else {
                    imageContainer.removeAllViews()
                }
            }
        }
    }

    private fun dealData(listB: MutableList<AddCargoNameImageView>) {
        //获取当前数据
        val listA = mutableListOf<AddCargoNameImageView>()
        for (index in 0 until imageContainer.childCount) {
            val imgView = imageContainer.getChildAt(index)
            if (imgView is AddCargoNameImageView) {
                listA.add(imgView)
            }
        }
        //清除当前数据
        imageContainer.removeAllViews()
        //查找需要添加的view
        val list1 = mutableListOf<AddCargoNameImageView>()
        listB.forEach { item ->
            val find = listA.find { ob -> TextUtils.equals(item.getTitleName(), ob.getTitleName()) }
            if (find.isNull) {
                //没有找到 需要添加
                list1.add(item)
            }
        }
        //需要移除的view
        val list2 = mutableListOf<AddCargoNameImageView>()
        listA.forEach { item ->
            val find = listB.find { ob -> TextUtils.equals(item.getTitleName(), ob.getTitleName()) }
            if (find.isNull) {
                //没有找到 需要添加
                list2.add(item)
            }
        }
        //移除需要移除的view
        listA.removeAll { it in list2 }
        listA.addAll(list1)
        imageContainer.removeAllViews()
        //向容器中添加view
        listA.forEach { view ->
            imageContainer.addView(view)
        }
    }

    override fun bindView(bundle: Bundle?) {
        txtAddCargoNameTips.text = Html.fromHtml(getString(R.string.deliver_add_cargo_name_tips))
    }

    override fun onActivityResult(requestCode: Int, resultCode: Int, data: Intent?) {
        super.onActivityResult(requestCode, resultCode, data)
        for (index in 0 until imageContainer.childCount) {
            val imgView = imageContainer.getChildAt(index)
            if (imgView is AddCargoNameImageView) {
                if (imgView.getRequestCode() == requestCode) {
                    val list = ImageSelector.obtainPathResult(data)
                    if (list.isNullOrEmpty()) {
                        return
                    }
                    viewModel?.upFile(list) { url ->
                        runOnUiThread {
                            imgView.setImgUrl(url)
                        }
                    }
                }
            }
        }
    }

    @LiveDataMatch
    open fun addCargoNameSuccess() {
        showToast("新增成功")
        RxBusEventManager.postEvent(RxBusAddCargoNameEvent(success = true))
        finish()
    }
}