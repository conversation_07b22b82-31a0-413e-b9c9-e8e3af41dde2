package com.zczy.cargo_owner.deliver.cargo_name_management

import android.annotation.SuppressLint
import android.app.Activity
import android.content.Context
import android.content.Intent
import android.os.Bundle
import android.view.View
import com.sfh.lib.event.RxBusEventManager
import com.sfh.lib.mvvm.annotation.LiveDataMatch
import com.zczy.cargo_owner.deliver.R
import com.zczy.cargo_owner.deliver.cargo_name_management.bean.CargoNameItemBean
import com.zczy.cargo_owner.deliver.cargo_name_management.bean.ReqEditBaseName
import com.zczy.cargo_owner.deliver.cargo_name_management.bean.ReqGetCargoInfo
import com.zczy.cargo_owner.deliver.cargo_name_management.bean.RspGetCargoInfo
import com.zczy.cargo_owner.deliver.cargo_name_management.bean.RxBusAddCargoNameEvent
import com.zczy.cargo_owner.deliver.cargo_name_management.model.CargoNameManagementModel
import com.zczy.comm.data.entity.EImage
import com.zczy.comm.http.entity.ResultData
import com.zczy.comm.ui.BaseActivity
import com.zczy.comm.utils.ex.isTrue
import com.zczy.comm.utils.ex.setVisible
import com.zczy.comm.utils.imageselector.ImagePreviewActivity
import com.zczy.comm.utils.imageselector.ImageSelector
import kotlinx.android.synthetic.main.deliver_cargo_name_detail.addCargoNameImageView
import kotlinx.android.synthetic.main.deliver_cargo_name_detail.bottomView
import kotlinx.android.synthetic.main.deliver_cargo_name_detail.tvCargoName
import kotlinx.android.synthetic.main.deliver_cargo_name_detail.tvRight

/**
 *描述：货品详情
 *auth:宋双朋
 *time:2024/6/19 16:11
 */

@SuppressLint("SetTextI18n")
class CargoNameDetailActivity : BaseActivity<CargoNameManagementModel>() {

    private val cargoConsignorId by lazy { intent.getStringExtra(CARGO_CONSIGNOR_ID) ?: "" }
    private var mCargoNameItemBean: RspGetCargoInfo? = null

    companion object {
        const val CARGO_CONSIGNOR_ID = "cargoConsignorId"

        @JvmStatic
        fun jumpPage(context: Context?, cargoConsignorId: String?) {
            val intent = Intent(context, CargoNameDetailActivity::class.java)
            intent.putExtra(CARGO_CONSIGNOR_ID, cargoConsignorId)
            context?.startActivity(intent)
        }
    }

    override fun getLayout(): Int {
        return R.layout.deliver_cargo_name_detail
    }

    override fun bindView(bundle: Bundle?) {
        bindClickEvent(tvRight)
    }

    override fun initData() {
        getViewModel(CargoNameManagementModel::class.java).getCargoInfo(
            req = ReqGetCargoInfo(cargoConsignorId = cargoConsignorId)
        ) { cargoNameItemBean ->
            runOnUiThread {
                mCargoNameItemBean = cargoNameItemBean
                tvCargoName.text = cargoNameItemBean.baseName ?: ""
                val list = arrayListOf<EImage>()
                cargoNameItemBean.specialGoodsPicUrls?.forEach {
                    list.add(EImage(imageId = it))
                }
                bottomView.setVisible(false)
                addCargoNameImageView.setData("资质证明")
                    .setCanDelete(false)
                    .setCanSelect(false)
                    .setCanClear(false)
                    .setVisible(cargoNameItemBean.isChemistry.isTrue || cargoNameItemBean.specialFlag.isTrue)
                when (cargoNameItemBean.state) {
                    "1" -> {
                        // 1.审核通过
                        bottomView.setVisible(true)
                        tvRight.text = "删除货品"
                        addCargoNameImageView.setCanDelete(false)
                            .setCanSelect(false)
                            .setCanClear(false)
                            .setImgVisible(list.isNotEmpty())
                            .setMaxCount(list.size)
                            .setImgBlockClick { _, view ->
                                //查看大图
                                ImagePreviewActivity.start(
                                    activity = this@CargoNameDetailActivity,
                                    imageData = view.imgList,
                                    0
                                )
                                1
                            }
                            .setUrlList(imgList = list)
                    }

                    "3" -> {
                        //  3.待审核
                        bottomView.setVisible(false)
                        addCargoNameImageView.setCanDelete(false)
                            .setCanSelect(false)
                            .setCanClear(false)
                            .setImgVisible(list.isNotEmpty())
                            .setMaxCount(list.size)
                            .setImgBlockClick { _, view ->
                                //查看大图
                                ImagePreviewActivity.start(
                                    activity = this@CargoNameDetailActivity,
                                    imageData = view.imgList,
                                    0
                                )
                                1
                            }
                            .setUrlList(imgList = list)
                    }

                    "2" -> {
                        //驳回
                        bottomView.setVisible(cargoNameItemBean.isChemistry.isTrue || cargoNameItemBean.specialFlag.isTrue)
                        tvRight.text = "提交改动"
                        addCargoNameImageView.setCanDelete(true)
                            .setCanSelect(true)
                            .setCanClear(true)
                            .setImgBlockClick { up, view ->
                                if (up) {
                                    //上传图片
                                    ImageSelector.open(
                                        this@CargoNameDetailActivity,
                                        view.maxCount - view.imgList.size,
                                        true,
                                        1
                                    )
                                } else {
                                    //查看大图
                                    ImagePreviewActivity.start(
                                        activity = this@CargoNameDetailActivity,
                                        imageData = view.imgList,
                                        0
                                    )
                                }
                                1
                            }
                            .setUrlList(imgList = list)
                    }
                }

            }
        }
    }

    override fun onSingleClick(v: View) {
        super.onSingleClick(v)
        when (v.id) {
            R.id.tvRight -> {
                when (tvRight.text.toString()) {
                    "删除货品" -> {
                        viewModel?.delCargoName(id = cargoConsignorId)
                    }

                    "提交改动" -> {
                        viewModel?.editBaseName(
                            req = ReqEditBaseName(
                                baseName = mCargoNameItemBean?.baseName,
                                id = cargoConsignorId,
                                urls = if (mCargoNameItemBean?.isChemistry.isTrue || mCargoNameItemBean?.specialFlag.isTrue) {
                                    addCargoNameImageView.getUrlList()
                                } else {
                                    null
                                },
                            )
                        ) {
                            runOnUiThread {
                                RxBusEventManager.postEvent(RxBusAddCargoNameEvent(success = true))
                                finish()
                            }
                        }
                    }
                }
            }
        }
    }

    override fun onActivityResult(requestCode: Int, resultCode: Int, data: Intent?) {
        super.onActivityResult(requestCode, resultCode, data)
        if (addCargoNameImageView.getRequestCode() == requestCode) {
            val list = ImageSelector.obtainPathResult(data)
            if (list.isNullOrEmpty()) {
                return
            }
            viewModel?.upFile(list) { url ->
                runOnUiThread {
                    addCargoNameImageView.setImgUrl(url)
                }
            }
        }
    }

    @LiveDataMatch
    open fun delCargoNameSuccess(data: ResultData?) {
        showToast(data?.resultMsg)
        setResult(Activity.RESULT_OK)
        finish()
    }
}