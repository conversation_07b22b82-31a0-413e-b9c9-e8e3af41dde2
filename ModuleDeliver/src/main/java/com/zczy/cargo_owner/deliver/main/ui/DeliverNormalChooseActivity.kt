package com.zczy.cargo_owner.deliver.main.ui

import android.app.Activity
import android.content.Intent
import android.os.Bundle
import android.text.TextUtils
import android.util.Log
import android.view.View
import com.chad.library.adapter.base.BaseQuickAdapter
import com.chad.library.adapter.base.listener.OnItemClickListener
import com.sfh.lib.mvvm.annotation.LiveDataMatch
import com.zczy.cargo_owner.deliver.R
import com.zczy.cargo_owner.deliver.bean.DeliverMyOrderListData
import com.zczy.cargo_owner.deliver.main.adapter.DeliverNormalChooseAdapterV2
import com.zczy.cargo_owner.deliver.main.model.DeliverNormalChooseModel
import com.zczy.cargo_owner.deliver.main.req.PageListCarrierBiddingOrder
import com.zczy.cargo_owner.deliver.main.req.RspQueryCarrierBiddingOrderItem
import com.zczy.comm.ui.BaseActivity
import com.zczy.comm.utils.dp2px
import com.zczy.comm.utils.json.toJson
import com.zczy.comm.utils.json.toJsonObject
import com.zczy.comm.widget.pulltorefresh.OnLoadingListener
import kotlinx.android.synthetic.main.deliver_normal_choose_activity2.btn_commit
import kotlinx.android.synthetic.main.deliver_normal_choose_activity2.lc_tendering_view
import kotlinx.android.synthetic.main.deliver_normal_choose_activity2.recycler_view
import kotlinx.android.synthetic.main.deliver_normal_choose_activity2.tv_month12
import kotlinx.android.synthetic.main.deliver_normal_choose_activity2.tv_tendering_size
import kotlinx.android.synthetic.main.deliver_normal_choose_activity2.tv_tendering_time
import kotlinx.android.synthetic.main.deliver_normal_choose_activity2.tv_tendering_type
import kotlinx.android.synthetic.main.deliver_normal_choose_activity2.tv_zczy_month12

/**
 * PS:我的发布 （普通货） 选择承运方
 * Created by sdx on 2019/3/5.
 */
open class DeliverNormalChooseActivity : BaseActivity<DeliverNormalChooseModel>() {

    private val eData by lazy { obtainData(intent) }

    private val mAdapter = DeliverNormalChooseAdapterV2()

    companion object {

        private const val EXTRA_DATA = "extra_data"
        private const val REQ_SUCCESS = 0x23

        @JvmStatic
        fun start(
            fragment: androidx.fragment.app.Fragment,
            data: DeliverMyOrderListData,
            requestCode: Int
        ) {
            val intent = Intent(fragment.context, DeliverNormalChooseActivity::class.java)
            Log.e("XXXX", data.toJson())
            intent.putExtra(EXTRA_DATA, data.toJson())
            fragment.startActivityForResult(intent, requestCode)
        }

        private fun obtainData(intent: Intent?): DeliverMyOrderListData {
            return intent?.getStringExtra(EXTRA_DATA)
                ?.toJsonObject(DeliverMyOrderListData::class.java)
                ?: DeliverMyOrderListData()
        }
    }

    override fun getLayout(): Int = R.layout.deliver_normal_choose_activity2

    override fun bindView(bundle: Bundle?) {
        recycler_view.apply {
            addItemDecorationSize(dp2px(7f))
            setAdapter(mAdapter, true)
            addOnItemListener(onItemClickListener)
            setOnLoadListener(object : OnLoadingListener {
                override fun onRefreshUI(nowPage: Int) {
                    viewModel?.queryList(nowPage, eData.orderId)
                }

                override fun onLoadMoreUI(nowPage: Int) {
                    viewModel?.queryList(nowPage, eData.orderId)
                }
            })
        }
        lc_tendering_view?.visibility = View.GONE
        bindClickEvent(btn_commit)
    }

    override fun initData() {
        recycler_view.onAutoRefresh()
    }

    override fun onSingleClick(v: View) {
        super.onSingleClick(v)
        when (v.id) {
            R.id.btn_commit -> {
                val selectData = mAdapter.selectData
                if (selectData == null) {
                    showDialogToast("请选择承运人的报价")
                    return
                }
                viewModel?.queryConsignorDepositInfo(selectData.expectId, eData.orderId)
            }
        }
    }

    override fun onActivityResult(requestCode: Int, resultCode: Int, data: Intent?) {
        super.onActivityResult(requestCode, resultCode, data)
        if (resultCode == Activity.RESULT_OK) {
            when (requestCode) {
                REQ_SUCCESS -> {
                    setResult(Activity.RESULT_OK)
                    finish()
                }
            }
        }
    }

    private val onItemClickListener = object : OnItemClickListener() {
        override fun onSimpleItemClick(adapter: BaseQuickAdapter<*, *>, view: View, position: Int) {
            val item = adapter.getItem(position)
            if (item is RspQueryCarrierBiddingOrderItem) {
                if (mAdapter.selectData?.expectId == item.expectId) {
                    mAdapter.selectData = null
                } else {
                    mAdapter.selectData = item
                }
                adapter.notifyDataSetChanged()
            }
        }
    }

    @LiveDataMatch
    open fun onQueryListSuccess(data: PageListCarrierBiddingOrder?) {
        recycler_view.onRefreshCompale(data)

        if (TextUtils.equals("1", data?.bargainBidSupportFlag)) {
            lc_tendering_view?.visibility = View.VISIBLE
            tv_tendering_size?.text = "投标数：" + data?.expectCountNum
            tv_tendering_type?.text =
                "择标方式：" + (if (TextUtils.equals("1", data?.bargainBidType)) "手动择标" else "自动择标")

            val remainDate = (data?.validityTime?.toLongOrNull() ?: 0L ) - System.currentTimeMillis()
            tv_tendering_time?.start(if (remainDate >0 ) remainDate else 0)

            //0 包车价,1 单价
            if (TextUtils.equals("0", data?.freightType)) {
                tv_month12?.text = data?.customerRecentlyAveragePrice + "元"
                tv_zczy_month12?.text = data?.platformRecentlyAveragePrice + "元"
            } else {
                var cargoCategoryTxt =
                    if (TextUtils.equals("2", data?.cargoCategory)) "方" else "吨"//	1：重货(吨)，2：泡货(方)
                tv_month12?.text = data?.customerRecentlyAveragePrice + "元/" + cargoCategoryTxt
                tv_zczy_month12?.text = data?.platformRecentlyAveragePrice + "元/" + cargoCategoryTxt
            }

        } else {
            lc_tendering_view?.visibility = View.GONE
        }
    }

    @LiveDataMatch
    open fun onConsignorSelectCarrier() {
        DeliverNormalChooseSuccessActivity.start(this, REQ_SUCCESS)
    }
}