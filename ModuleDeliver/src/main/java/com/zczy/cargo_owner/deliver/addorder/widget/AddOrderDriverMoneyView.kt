package com.zczy.cargo_owner.deliver.addorder.widget

import android.content.Context
import android.graphics.Color
import android.util.AttributeSet
import android.view.LayoutInflater
import android.widget.LinearLayout
import com.sfh.lib.rx.ui.UtilRxView
import com.zczy.cargo_owner.deliver.R
import com.zczy.cargo_owner.deliver.addorder.bean.normal.OrderInfo
import com.zczy.cargo_owner.deliver.addorder.req.comm.Rsp23QueryMobileOrderCommonInfo
import com.zczy.comm.utils.ex.setVisible
import com.zczy.comm.utils.setInVisible
import kotlinx.android.synthetic.main.deliver_add_order_driver_money_view.view.driverMoney
import kotlinx.android.synthetic.main.deliver_add_order_driver_money_view.view.img_rb_1
import kotlinx.android.synthetic.main.deliver_add_order_driver_money_view.view.img_rb_2
import kotlinx.android.synthetic.main.deliver_add_order_driver_money_view.view.view_rb_1
import kotlinx.android.synthetic.main.deliver_add_order_driver_money_view.view.view_rb_2
import kotlinx.android.synthetic.main.deliver_add_order_driver_money_view.view.tv_title_2

/**
 *  desc: 订金*（司机付 ）
 *  user: ssp
 *  time: 2025/3/25 16:48
 */
class AddOrderDriverMoneyView : LinearLayout {

    companion object {
        const val LEFT: Int = 1
        const val RIGHT: Int = 2
        const val NONE: Int = 3
    }

    private var mCheck = NONE
    private var onClickBlock: (check: Int) -> Boolean = {
        false
    }
    private var onMoneyChangeBlock: (money: String) -> Unit = {
    }

    constructor(context: Context) : super(context)

    constructor(context: Context, attrs: AttributeSet?) : super(context, attrs)

    constructor(context: Context, attrs: AttributeSet?, defStyleAttr: Int) : super(
        context,
        attrs,
        defStyleAttr
    )

    init {
        orientation = VERTICAL
        setBackgroundColor(Color.WHITE)
        LayoutInflater.from(context).inflate(R.layout.deliver_add_order_driver_money_view, this)
        UtilRxView.clicks(view_rb_1, 500) {
            val b = onClickBlock(LEFT)
            if (b) {
                setCheck(LEFT)
            }
        }
        UtilRxView.clicks(view_rb_2, 500) {
            val b = onClickBlock(RIGHT)
            if (b) {
                setCheck(RIGHT)
            }
        }
        UtilRxView.textChanges(driverMoney, 500) {
            if (it.isEmpty()) {
                tv_title_2.setInVisible(false)
            } else {
                val money = it.toString().toDoubleOrNull() ?: 0.00
                when {
                    money > 1000 -> {
                        tv_title_2.setInVisible(true)
                    }

                    money < 20 -> {
                        tv_title_2.setInVisible(true)
                    }

                    else -> {
                        tv_title_2.setInVisible(false)
                    }
                }
            }
            if (driverMoney.isFocused) {
                onMoneyChangeBlock(it.toString())
            }
        }
    }

    fun setClickBlock(onClickBlock: (check: Int) -> Boolean) {
        this.onClickBlock = onClickBlock
    }

    fun setMoneyChangeBlock(onMoneyChangeBlock: (money: String) -> Unit) {
        this.onMoneyChangeBlock = onMoneyChangeBlock
    }

    private fun setCheck(check: Int) {
        mCheck = check
        img_rb_1.isSelected = check == LEFT
        img_rb_2.isSelected = check == RIGHT
    }

    fun initConfig(orderCommonInfo: Rsp23QueryMobileOrderCommonInfo) {
        when (orderCommonInfo.depositConfigValue) {
            "1" -> {
                //不展示订金模块
                this.setVisible(false)
                onClickBlock(NONE)
                view_rb_1.isEnabled = false
                view_rb_2.isEnabled = false
            }

            "2" -> {
                //默认可退
                this.setVisible(true)
                view_rb_2.setVisible(false)
                onClickBlock(LEFT)
                view_rb_1.isEnabled = false
                view_rb_2.isEnabled = false
            }

            "3" -> {
                //可选配置
                this.setVisible(true)
                //默认退还
                onClickBlock(LEFT)
                view_rb_2.setVisible(true)
                view_rb_1.isEnabled = true
                view_rb_2.isEnabled = true
            }
        }
    }

    //普通货抹零
    fun setData(orderInfo: OrderInfo) {
        driverMoney.setText(orderInfo.depositAmount)
        when (orderInfo.depositType) {
            "1" -> {
                setCheck(LEFT)
            }

            "2" -> {
                setCheck(RIGHT)
            }

            "3" -> {
                setCheck(NONE)
            }
        }
    }

}