package com.zczy.cargo_owner.deliver.batch.widget;


import android.content.Context;
import android.graphics.Color;
import android.text.Editable;
import android.text.TextUtils;
import android.text.TextWatcher;
import android.util.AttributeSet;
import android.view.View;
import android.widget.EditText;
import android.widget.ImageView;
import android.widget.LinearLayout;
import android.widget.ProgressBar;
import android.widget.TextView;

import androidx.annotation.NonNull;
import androidx.annotation.Nullable;

import com.zczy.cargo_owner.deliver.R;
import com.zczy.cargo_owner.deliver.addorder.util.NumberUtils;
import com.zczy.cargo_owner.deliver.batch.req.EModifyPlan;

public class DeliverEditGoodsPlanCarView extends LinearLayout {

    public TextView tv_postion;
    public TextView tv_day;
    public LinearLayout ll_plan_car;
    public TextView tv_subtraction_num;
    public EditText et_car_num;
    public TextView tv_add_num;
    public TextView tv_car_num;
    public LinearLayout ll_plan_num;
    public EditText et_weight;
    public ImageView iv_delete;
    public TextView tv_plan_weight;
    public TextView tv_tips;
    public ProgressBar pb_weight;
    public TextView tv_weight;
    public TextView tv_cancle;

    private EModifyPlan data;
    OnClick onClick;
    public TextView tv_weight_unit;

    private String limitWeigt;
    private String unit;

    public void setLimit(String limitWeigt,String unit){
        this.limitWeigt = "";
        this.unit = unit;
    }

    public interface OnClick{
        void onsetWeight(String weight);
        void onCancel(EModifyPlan data, DeliverEditGoodsPlanCarView view);
    }

    public void setOnclickListener(OnClick onclickListener){
        this.onClick = onclickListener;
    }
    public EModifyPlan getData(){
        return data;
    }

    public DeliverEditGoodsPlanCarView(@NonNull Context context) {
        super(context);
        init(null);
    }

    void init(@Nullable AttributeSet attrs) {
        View.inflate(getContext(), R.layout.deliver_edit_goods_plan_car_view, this);
        tv_postion = findViewById(R.id.tv_postion);
        tv_day = findViewById(R.id.tv_day);
        ll_plan_car = findViewById(R.id.ll_plan_car);
        tv_subtraction_num = findViewById(R.id.tv_subtraction_num);
        et_car_num = findViewById(R.id.et_car_num);
        tv_add_num = findViewById(R.id.tv_add_num);
        tv_car_num = findViewById(R.id.tv_car_num);
        ll_plan_num = findViewById(R.id.ll_plan_num);
        et_weight = findViewById(R.id.et_weight);
        iv_delete = findViewById(R.id.iv_delete);
        tv_tips = findViewById(R.id.tv_tips);
        pb_weight = findViewById(R.id.pb_weight);
        tv_weight = findViewById(R.id.tv_weight);
        tv_cancle = findViewById(R.id.tv_cancle);
        tv_plan_weight = findViewById(R.id.tv_plan_weight);
        tv_weight_unit = findViewById(R.id.tv_weight_unit);

    }

    public void setData(EModifyPlan data, int postion){
        this.data = data;
        tv_postion.setText("计划-"+postion);
        tv_day.setText("发货计划日: " + data.planDay);
        tv_weight_unit.setText((TextUtils.isEmpty(unit)?"":unit));

        if(!TextUtils.equals(data.isModifyFlag,"1")){
            ll_plan_car.setVisibility(VISIBLE);
            tv_car_num.setVisibility(GONE);
            et_car_num.setText(NumberUtils.subZeroAndDot(data.planWeight));
            et_car_num.addTextChangedListener(new TextWatcher() {
                @Override
                public void beforeTextChanged(CharSequence s, int start, int count, int after) {

                }

                @Override
                public void onTextChanged(CharSequence s, int start, int before, int count) {

                }

                @Override
                public void afterTextChanged(Editable s) {
                    data.planWeight = s.toString();
                }
            });
            tv_subtraction_num.setOnClickListener(new OnClickListener() {
                @Override
                public void onClick(View v) {
                    if(NumberUtils.isNum(et_car_num.getText().toString())){
                        Integer num = Integer.parseInt(et_car_num.getText().toString()) - 1;
                        et_car_num.setText(num+"");
                    }
                }
            });

            tv_add_num.setOnClickListener(new OnClickListener() {
                @Override
                public void onClick(View v) {
                    if(NumberUtils.isNum(et_car_num.getText().toString())){
                        Integer num = Integer.parseInt(et_car_num.getText().toString()) + 1;
                        et_car_num.setText(num+"");
                    }
                }
            });


        } else {
            ll_plan_car.setVisibility(GONE);
            tv_car_num.setVisibility(VISIBLE);
            tv_car_num.setText(NumberUtils.subZeroAndDot(data.planWeight)+"车");
        }

        if(TextUtils.isEmpty(data.dealWeight)){
            ll_plan_num.setVisibility(VISIBLE);
            tv_plan_weight.setVisibility(GONE);
            tv_tips.setVisibility(VISIBLE);
            if(postion == 1){
                et_weight.setEnabled(true);
                if(TextUtils.isEmpty(data.unitWeight)){
                    iv_delete.setVisibility(GONE);
                } else {
                    iv_delete.setVisibility(VISIBLE);
                }
            } else {
                et_weight.setEnabled(false);
                iv_delete.setVisibility(GONE);
            }
            et_weight.setText(data.unitWeight);
            et_weight.addTextChangedListener(new TextWatcher() {
                @Override
                public void beforeTextChanged(CharSequence s, int start, int count, int after) {

                }

                @Override
                public void onTextChanged(CharSequence s, int start, int before, int count) {

                }

                @Override
                public void afterTextChanged(Editable s) {
                    if(TextUtils.isEmpty(s.toString())){
                        iv_delete.setVisibility(GONE);
                    } else {
                        if(postion == 1){
                            iv_delete.setVisibility(VISIBLE);
                        } else {
                            iv_delete.setVisibility(GONE);
                        }
                    }
                    data.unitWeight = s.toString();
                    if(onClick!=null && postion == 1){
                        onClick.onsetWeight(s.toString());
                    }
                    if(!TextUtils.isEmpty(s.toString())){
                        if(NumberUtils.isNum(s.toString()) && !TextUtils.isEmpty(limitWeigt)){
                            try {
                                if(Double.valueOf(s.toString())>Double.valueOf(limitWeigt)){
                                    tv_tips.setText("已超出每日承运最大量"+limitWeigt+(TextUtils.isEmpty(unit)?"":unit));
                                    tv_tips.setTextColor(Color.parseColor("#F2481C"));
                                } else {
                                    tv_tips.setText("注：每日承运不得超过"+limitWeigt+(TextUtils.isEmpty(unit)?"":unit));
                                    tv_tips.setTextColor(Color.parseColor("#C2C2C2"));
                                }
                            } catch (Exception e) {
                                e.printStackTrace();
                            }
                        }
                    }
                }
            });
        } else {
            ll_plan_num.setVisibility(GONE);
            tv_plan_weight.setVisibility(VISIBLE);
            tv_plan_weight.setText(data.unitWeight);
            tv_tips.setVisibility(GONE);
        }

        pb_weight.setMax(TextUtils.isEmpty(data.planWeight) ? 0 : Integer.valueOf(NumberUtils.multiply45(data.planWeight,"0")));
        pb_weight.setProgress(TextUtils.isEmpty(data.dealWeight) ? 0 : Integer.valueOf(NumberUtils.multiply45(data.dealWeight,"0")));
        tv_weight.setText(TextUtils.isEmpty(data.dealWeight) ? "暂无成交" : "已成交"+NumberUtils.multiply(data.dealWeight,data.unitWeight)+(TextUtils.isEmpty(unit)?"":unit)+"/该计划总量"+NumberUtils.multiply(data.planWeight,data.unitWeight)+(TextUtils.isEmpty(unit)?"":unit));
        if(!TextUtils.equals(data.isCancelFlag,"1")){
            tv_cancle.setVisibility(VISIBLE);
            tv_cancle.setOnClickListener(new OnClickListener() {
                @Override
                public void onClick(View v) {

                    onClick.onCancel(data,DeliverEditGoodsPlanCarView.this);

                }
            });
        } else {
            tv_cancle.setVisibility(GONE);
        }
    }

}
