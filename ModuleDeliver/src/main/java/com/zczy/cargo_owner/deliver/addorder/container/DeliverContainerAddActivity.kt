package com.zczy.cargo_owner.deliver.addorder.container

import android.annotation.SuppressLint
import android.content.Context
import android.content.Intent
import android.os.Bundle
import android.text.Editable
import android.text.InputFilter
import android.text.TextWatcher
import com.sfh.lib.event.RxBusEventManager
import com.sfh.lib.mvvm.annotation.LiveDataMatch
import com.sfh.lib.ui.dialog.DialogBuilder
import com.zczy.cargo_owner.deliver.R
import com.zczy.cargo_owner.deliver.addorder.container.model.DeliverContainerAddModel
import com.zczy.cargo_owner.deliver.addorder.container.req.ReqAddContainer
import com.zczy.cargo_owner.deliver.addorder.container.req.RspDeliverContainerList
import com.zczy.cargo_owner.deliver.addorder.container.req.RxBusAddContainer
import com.zczy.comm.ui.BaseActivity
import com.zczy.comm.utils.ex.isNull
import com.zczy.comm.widget.inputv2.InputViewClick
import kotlinx.android.synthetic.main.deliver_container_add_activity.*

/**
 * 功能描述: 新增集装箱
 * <AUTHOR>
 * @date 2022/2/24-15:21
 */

class DeliverContainerAddActivity : BaseActivity<DeliverContainerAddModel>() {
    private var rspDeliverContainerList: RspDeliverContainerList? = null

    companion object {

        @JvmStatic
        fun jumpUi(context: Context) {
            val intent = Intent(context, DeliverContainerAddActivity::class.java)
            context.startActivity(intent)
        }
    }

    override fun getLayout(): Int {
        return R.layout.deliver_container_add_activity
    }

    override fun bindView(bundle: Bundle?) {
        noteTv.filters = arrayOf<InputFilter>(InputFilter.LengthFilter(50))
        noteTv.addTextChangedListener(object : TextWatcher {
            @SuppressLint("SetTextI18n")
            override fun afterTextChanged(s: Editable) {
                if (s.length > 50) {
                    return
                }
                sizeTv.text = "${s.length}/50"
            }

            override fun beforeTextChanged(s: CharSequence?, start: Int, count: Int, after: Int) {
            }

            override fun onTextChanged(s: CharSequence?, start: Int, before: Int, count: Int) {
            }
        })
        inputContainerName.setListener(object : InputViewClick.Listener() {
            override fun onClick(viewId: Int, view: InputViewClick, content: String) {
                DeliverContainerAddSearchActivity.jumpUi(
                    this@DeliverContainerAddActivity,
                    DeliverContainerAddSearchActivity.DELIVER_SWITCH_REQUEST_CODE
                )
            }
        })
        tvBtn.setOnClickListener {
            // 提交新增集装箱数据
            if (rspDeliverContainerList.isNull) {
                //提示选择
                val dialogBuilder = DialogBuilder()
                dialogBuilder.title = "温馨提示"
                dialogBuilder.message = "请选择集装箱"
                dialogBuilder.isHideCancel = true
                showDialog(dialogBuilder)
                return@setOnClickListener
            }
            viewModel?.addContainer(
                ReqAddContainer(
                    containerId = rspDeliverContainerList?.containerId ?: "",
                    remark = noteTv.text.toString().trim()
                )
            )
        }
    }

    @LiveDataMatch(tag = "新增集装箱")
    open fun onAddSuccess() {
        RxBusEventManager.postEvent(RxBusAddContainer(success = true))
        finish()
    }

    override fun onActivityResult(requestCode: Int, resultCode: Int, data: Intent?) {
        super.onActivityResult(requestCode, resultCode, data)
        rspDeliverContainerList = DeliverContainerAddSearchActivity.receiptData(data)
        rspDeliverContainerList?.apply {
            inputContainerName.content = containerName ?: ""
            inputSpecification.content = containerStandard ?: ""
            inputGrossWeight.content = containerWeight ?: ""
            tvBtn.isEnabled = true
        }
        if (rspDeliverContainerList == null) {
            inputContainerName.content = ""
            inputSpecification.content = ""
            inputGrossWeight.content = ""
            tvBtn.isEnabled = false
        }
    }

    override fun initData() {

    }
}