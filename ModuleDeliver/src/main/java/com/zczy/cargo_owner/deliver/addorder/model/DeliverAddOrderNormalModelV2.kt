package com.zczy.cargo_owner.deliver.addorder.model

import android.text.TextUtils
import com.sfh.lib.exception.HandleException
import com.sfh.lib.mvvm.service.BaseViewModel
import com.sfh.lib.rx.IResult
import com.sfh.lib.rx.IResultSuccessNoFail
import com.sfh.lib.ui.dialog.DialogBuilder
import com.zczy.cargo_owner.deliver.addorder.JumpNewGoodsData
import com.zczy.cargo_owner.deliver.addorder.bean.CargoInfo
import com.zczy.cargo_owner.deliver.addorder.bean.OrderAddressInfo
import com.zczy.cargo_owner.deliver.addorder.req.ReqGetPendingOrderIds
import com.zczy.cargo_owner.deliver.addorder.req.batch.RspAddResult
import com.zczy.cargo_owner.deliver.addorder.req.comm.Req10QueryOrderReceipt
import com.zczy.cargo_owner.deliver.addorder.req.comm.Req16CheckAdvanceInfo
import com.zczy.cargo_owner.deliver.addorder.req.comm.Req17QueryOrderPolicyInfo
import com.zczy.cargo_owner.deliver.addorder.req.comm.Req22QueryPolicyTipsFlagByCargoName
import com.zczy.cargo_owner.deliver.addorder.req.comm.Req23QueryMobileOrderCommonInfo
import com.zczy.cargo_owner.deliver.addorder.req.comm.Req33QueryCargoDespatchOrDeliveryInfo
import com.zczy.cargo_owner.deliver.addorder.req.comm.Req37QueryOilRewardInfoByPublish
import com.zczy.cargo_owner.deliver.addorder.req.comm.Req53CheckAddressIsInRiskArea
import com.zczy.cargo_owner.deliver.addorder.req.comm.ReqCargoWeightExceptionCheck
import com.zczy.cargo_owner.deliver.addorder.req.comm.ReqCheckVehicleMonthAmountControl
import com.zczy.cargo_owner.deliver.addorder.req.comm.ReqOilGasConfigInfo
import com.zczy.cargo_owner.deliver.addorder.req.comm.ReqPublishNotice
import com.zczy.cargo_owner.deliver.addorder.req.comm.ReqQueryBulkConsignorWhiteAndLine
import com.zczy.cargo_owner.deliver.addorder.req.comm.ReqQueryCargoAverage
import com.zczy.cargo_owner.deliver.addorder.req.comm.ReqQueryChildArray
import com.zczy.cargo_owner.deliver.addorder.req.comm.ReqQueryPersonPolicy
import com.zczy.cargo_owner.deliver.addorder.req.comm.ReqQueryPresetConfigWithAddress
import com.zczy.cargo_owner.deliver.addorder.req.comm.ReqQueryRecommendVehicleType
import com.zczy.cargo_owner.deliver.addorder.req.comm.ReqQueryTonRuleByCargoName
import com.zczy.cargo_owner.deliver.addorder.req.comm.Rsp23QueryMobileOrderCommonInfo
import com.zczy.cargo_owner.deliver.addorder.req.comm.RspOilGasConfigInfo
import com.zczy.cargo_owner.deliver.addorder.req.comm.setData
import com.zczy.cargo_owner.deliver.addorder.req.comm.setReqAddOrderForSeniorConsignor
import com.zczy.cargo_owner.deliver.addorder.req.normal.JumpNormalData
import com.zczy.cargo_owner.deliver.addorder.req.normal.Req55GetSuccessImportNum
import com.zczy.cargo_owner.deliver.addorder.req.normal.ReqAddOrderForSeniorConsignor
import com.zczy.cargo_owner.deliver.addorder.req.normal.ReqJumpToMobileSeniorConsignorUpdateOrder
import com.zczy.cargo_owner.deliver.addorder.req.normal.ReqQueryMobileDepositInfoForOrder
import com.zczy.cargo_owner.deliver.addorder.req.normal.Rsp55GetSuccessImportNum
import com.zczy.cargo_owner.deliver.addorder.req.normal.RspQueryMobileDepositInfoForOrder
import com.zczy.cargo_owner.deliver.addorder.req.normal.formatOrderAddressInfo
import com.zczy.cargo_owner.deliver.addorder.req.normal.setDataV2
import com.zczy.cargo_owner.deliver.addorder.req.normal.setJumpData
import com.zczy.comm.CommServer
import com.zczy.comm.file.IFileServer
import com.zczy.comm.http.entity.BaseRsp
import com.zczy.comm.utils.ex.isTrue
import com.zczy.comm.utils.ex.toCommaString
import io.reactivex.Observable
import io.reactivex.Observer
import io.reactivex.android.schedulers.AndroidSchedulers
import io.reactivex.disposables.Disposable
import io.reactivex.schedulers.Schedulers
import java.io.File
import java.util.concurrent.TimeUnit

/**
 * PS:
 * Created by sdx on 2019/2/13.
 */
class DeliverAddOrderNormalModelV2 : BaseViewModel() {

    /**
     * 发新货 普通货 初始化 网络请求
     */
    fun doNewGoodsInitRequestV2() {
        execute(
            true,
            Req23QueryMobileOrderCommonInfo()
        ) { t ->
            if (t.success()) {
                //油气品默认值查询接口
                oilGasConfigInfo(
                    req = ReqOilGasConfigInfo(
                        deliverCity = t.data?.deliverybean?.consCity,
                        deliverCoordinateX = t.data?.deliverybean?.consCoordinateX,
                        deliverCoordinateY = t.data?.deliverybean?.consCoordinateY,
                        deliverDis = t.data?.deliverybean?.consArea,
                        deliverPlace = t.data?.deliverybean?.consDetailAddr,
                        deliverPro = t.data?.deliverybean?.consProvince,
                        despatchCity = t.data?.despatchbean?.consCity,
                        despatchCoordinateX = t.data?.despatchbean?.consCoordinateX,
                        despatchCoordinateY = t.data?.despatchbean?.consCoordinateY,
                        despatchDis = t.data?.despatchbean?.consArea,
                        despatchPlace = t.data?.despatchbean?.consDetailAddr,
                        despatchPro = t.data?.despatchbean?.consProvince,
                    )
                ) { mRspOilGasConfigInfoV1 ->
                    setValue("onReq23QueryMobileOrderCommonInfo", t.data, mRspOilGasConfigInfoV1)
                    queryPresetConfigWithAddress(
                        req = ReqQueryPresetConfigWithAddress(
                            despatchPro = t.data?.despatchbean?.consProvince,
                            despatchCity = t.data?.despatchbean?.consCity,
                            despatchDis = t.data?.despatchbean?.consArea,
                            deliverPro = t.data?.deliverybean?.consProvince,
                            deliverCity = t.data?.deliverybean?.consCity,
                            deliverDis = t.data?.deliverybean?.consArea,
                        )
                    )
                    true
                }
            } else {
                showDialogToast(t.msg)
            }
        }
    }

    fun queryPresetConfigWithAddress(req: ReqQueryPresetConfigWithAddress) {
        execute(req) {
            if (it.success()) {
                setValue("queryPresetConfigWithAddressSuccess", it.data)
            } else {
                showToast(it.msg)
            }
        }
    }

    fun queryBulkConsignorWhiteAndLine(req: ReqQueryBulkConsignorWhiteAndLine) {
        execute(req) {
            if (it.success()) {
                setValue("queryBulkConsignorWhiteAndLineSuccess", it.data)
            } else {
                showToast(it.msg)
            }
        }
    }

    //油气品默认值查询接口
    fun oilGasConfigInfo(req: ReqOilGasConfigInfo, onSuccessBlock: (data: RspOilGasConfigInfo?) -> Boolean = { false }) {
        execute(req) {
            if (it.success()) {
                val block = onSuccessBlock(it.data?.data)
                if (!block) {
                    setValue("oilGasConfigInfoSuccess", it.data?.data)
                } else {
                    onSuccessBlock(RspOilGasConfigInfo())
                }
            } else {
                onSuccessBlock(RspOilGasConfigInfo())
            }
        }
    }

    //单运单、单车月度交易限额管控校验
    fun checkVehicleMonthAmountControl(req: ReqCheckVehicleMonthAmountControl, onBlock: () -> Unit = { }) {
        execute(req) {
            if (it.success()) {
                onBlock()
            } else {
                val dialogBuilder = DialogBuilder()
                dialogBuilder.message = it.data?.resultMsg
                dialogBuilder.title = "提示"
                dialogBuilder.isHideCancel = false
                dialogBuilder.setOKText("确定")
                dialogBuilder.setOkListener { dialog, _ ->
                    dialog.dismiss()
                    onBlock()
                }
                showDialog(dialogBuilder)
            }
        }
    }

    //普通货计划吨位与历史结算吨位比较校验
    fun cargoWeightExceptionCheck(req: ReqCargoWeightExceptionCheck, onBlock: () -> Unit = { }) {
        execute(req) {
            if (it.success()) {
                when (it.data?.weightCheckFlag) {
                    "1" -> {
                        val dialogBuilder = DialogBuilder()
                        dialogBuilder.message = "计划吨位与历史结算吨位差异过大，请核实是否填写正确"
                        dialogBuilder.title = "提示"
                        dialogBuilder.isHideCancel = false
                        dialogBuilder.setOKText("确定")
                        dialogBuilder.setOkListener { dialog, _ ->
                            dialog.dismiss()
                            onBlock()
                        }
                        showDialog(dialogBuilder)
                    }

                    else -> {
                        onBlock()
                    }
                }
            } else {
                val dialogBuilder = DialogBuilder()
                dialogBuilder.message = it.data?.resultMsg
                dialogBuilder.title = "提示"
                dialogBuilder.isHideCancel = true
                dialogBuilder.setOKText("确定")
                dialogBuilder.setOkListener { dialog, _ ->
                    dialog.dismiss()
                }
                showDialog(dialogBuilder)
            }
        }
    }

    fun queryCargoDespatchOrDeliveryInfo33(outOrderNumber: String) {
        execute(
            true,
            Req33QueryCargoDespatchOrDeliveryInfo(outOrderNumber = outOrderNumber)
        ) { t ->
            if (t.success()) {
                setValue("onReq33QueryCargoDespatchOrDeliveryInfo", t.data)
            } else {
                showDialogToast(t.msg)
            }
        }
    }

    // 普通货草稿箱：跳转到已认证货主修改普通货运单页面
    fun jumpToMobileSeniorConsignorUpdateOrder(mJumpData: JumpNewGoodsData) {
        val jumpNormalData = JumpNormalData()
        Observable.zip(
            Req23QueryMobileOrderCommonInfo().task,
            ReqJumpToMobileSeniorConsignorUpdateOrder().setJumpData(mJumpData).task
        ) { t1, t2 ->
            when {
                !t1.success() -> {
                    throw HandleException(t1.code.toIntOrNull() ?: 0, t1.msg)
                }

                !t2.success() -> {
                    throw HandleException(t2.code.toIntOrNull() ?: 0, t2.msg)
                }
            }
            jumpNormalData.initData = t1.data
            jumpNormalData.jumpData = t2.data
            true
        }
            .flatMap {
                val commaString =
                    jumpNormalData.jumpData?.cargoList?.toCommaString { it2 -> it2.cargoName }
                        ?: ""
                Req22QueryPolicyTipsFlagByCargoName().setData(orderAddressInfo = jumpNormalData.jumpData?.formatOrderAddressInfo() ?: OrderAddressInfo(), mCargoNameStr = commaString).task
            }
            .map {
                if (it.success()) {
                    jumpNormalData.policyTipsFlag = it.data?.policyTipsFlag ?: ""
                    // 是否自动购买 1是 0否
                    jumpNormalData.autoBuyFlag = it.data?.autoBuyFlag ?: ""
                    // 投保方式  1 货主自主投保  2 转嫁承运方  3 全部
                    jumpNormalData.policyMode = it.data?.policyMode ?: ""
                    jumpNormalData.openMonthlyServiceFlag = it.data?.openMonthlyServiceFlag ?: ""
                    jumpNormalData.monthlyServiceMsg = it.data?.monthlyServiceMsg ?: ""
                    jumpNormalData.monthlyPolicyMode = it.data?.monthlyPolicyMode ?: ""
                } else {
                    jumpNormalData.policyTipsFlag = "1"
                }
                true
            }
            .subscribeOn(Schedulers.io())
            .observeOn(AndroidSchedulers.mainThread())
            .doOnSubscribe { showLoading(true) }
            .subscribe(object : Observer<Boolean> {
                override fun onSubscribe(d: Disposable) {
                    putDisposable(d)
                }

                override fun onNext(t: Boolean) {
                    //油气品默认值查询接口
                    oilGasConfigInfo(
                        req = ReqOilGasConfigInfo(
                            deliverCity = jumpNormalData.jumpData?.deliveryInfo?.consCity,
                            deliverCoordinateX = jumpNormalData.jumpData?.deliveryInfo?.consCoordinateX,
                            deliverCoordinateY = jumpNormalData.jumpData?.deliveryInfo?.consCoordinateY,
                            deliverDis = jumpNormalData.jumpData?.deliveryInfo?.consArea,
                            deliverPlace = jumpNormalData.jumpData?.deliveryInfo?.consDetailAddr,
                            deliverPro = jumpNormalData.jumpData?.deliveryInfo?.consProvince,
                            despatchCity = jumpNormalData.jumpData?.despatchInfo?.consCity,
                            despatchCoordinateX = jumpNormalData.jumpData?.despatchInfo?.consCoordinateX,
                            despatchCoordinateY = jumpNormalData.jumpData?.despatchInfo?.consCoordinateY,
                            despatchDis = jumpNormalData.jumpData?.despatchInfo?.consArea,
                            despatchPlace = jumpNormalData.jumpData?.despatchInfo?.consDetailAddr,
                            despatchPro = jumpNormalData.jumpData?.despatchInfo?.consProvince,
                        )
                    ) { mRspOilGasConfigInfo ->
                        setValue("onJumpToMobileSeniorConsignorUpdateOrder", jumpNormalData, mRspOilGasConfigInfo)
                        queryPresetConfigWithAddress(
                            req = ReqQueryPresetConfigWithAddress(
                                despatchPro = jumpNormalData.jumpData?.despatchInfo?.consProvince,
                                despatchCity = jumpNormalData.jumpData?.despatchInfo?.consCity,
                                despatchDis = jumpNormalData.jumpData?.despatchInfo?.consArea,
                                deliverPro = jumpNormalData.jumpData?.deliveryInfo?.consProvince,
                                deliverCity = jumpNormalData.jumpData?.deliveryInfo?.consCity,
                                deliverDis = jumpNormalData.jumpData?.deliveryInfo?.consArea,
                            )
                        )
                        true
                    }
                }

                override fun onError(e: Throwable) {
                    showDialogToast(e.message)
                    hideLoading()
                }

                override fun onComplete() {
                    hideLoading()
                }
            })
    }

    // 10 查询押回单信息
    fun queryOrderReceipt() {
        execute(
            true,
            Req10QueryOrderReceipt()
        ) { baseRsp ->
            hideLoading()
            if (baseRsp.success()) {
                setValue("onQueryOrderReceiptSuccess", baseRsp.data)
            } else {
                showDialogToast(baseRsp.msg)
            }
        }
    }

    // 10 查询押回单信息 金额list
    fun queryOrderReceiptList() {
        execute(
            true,
            Req10QueryOrderReceipt()
        ) { baseRsp ->
            hideLoading()
            if (baseRsp.success()) {
                setValue("onQueryOrderReceiptListSuccess", baseRsp.data)
            } else {
                showDialogToast(baseRsp.msg)
            }
        }
    }

    // 17 普通货：根据货物名称，审核状态，货值，赠保额度：查询保障服务相关信息
    fun queryOrderPolicyInfo(
        cargoList: List<CargoInfo>,
        giftMoney: String,
        cargoMoney: String,
        totalMoney: String,
        weight: String,
        changePolicyToCarrier: String,
        orderModel: String,
        orderAddressInfo: OrderAddressInfo,
        modeType: String,
    ) {
        if (TextUtils.equals("1", modeType)) {
            //代开专票不请求
            return
        }
        execute(
            Req17QueryOrderPolicyInfo(
                giftMoney = giftMoney,
                orderModel = orderModel,
                cargoNameStr = cargoList.toCommaString { it.cargoName },
                cargoMoney = cargoMoney,
                totalMoney = totalMoney,
                weight = weight,
                policyType = when (changePolicyToCarrier) {
                    "1" -> {
                        "5"
                    }

                    else -> {
                        "1"
                    }
                },
                changePolicyToCarrier = changePolicyToCarrier,
                despatchPro = orderAddressInfo.despatchPro,
                despatchCity = orderAddressInfo.despatchCity,
                despatchDis = orderAddressInfo.despatchDis,
                despatchPlace = orderAddressInfo.despatchPlace,
                despatchCoordinateX = orderAddressInfo.despatchCoordinateX,
                despatchCoordinateY = orderAddressInfo.despatchCoordinateY,
                deliverPro = orderAddressInfo.deliverPro,
                deliverCity = orderAddressInfo.deliverCity,
                deliverDis = orderAddressInfo.deliverDis,
                deliverPlace = orderAddressInfo.deliverPlace,
                deliverCoordinateX = orderAddressInfo.deliverCoordinateX,
                deliverCoordinateY = orderAddressInfo.deliverCoordinateY
            )
        ) { baseRsp ->
            if (baseRsp.success()) {
                setValue("onQueryOrderPolicyInfo", baseRsp.data, cargoMoney)
            } else {
                showDialogToast(baseRsp.msg)
            }
        }
    }

    // 17 普通货：根据货物名称，审核状态，货值，赠保额度：查询保障服务相关信息
    fun queryOrderPolicyInfoChangeModel(
        cargoList: List<CargoInfo>,
        giftMoney: String,
        cargoMoney: String,
        totalMoney: String,
        weight: String,
        changePolicyToCarrier: String,
        orderModel: String,
        orderAddressInfo: OrderAddressInfo,
        modeType: String,
    ) {
        if (TextUtils.equals("1", modeType)) {
            //代开专票不请求
            return
        }
        execute(
            Req17QueryOrderPolicyInfo(
                giftMoney = giftMoney,
                orderModel = orderModel,
                cargoNameStr = cargoList.toCommaString { it.cargoName },
                cargoMoney = cargoMoney,
                totalMoney = totalMoney,
                weight = weight,
                policyType = when (changePolicyToCarrier) {
                    "1" -> {
                        "5"
                    }

                    else -> {
                        "1"
                    }
                },
                changePolicyToCarrier = changePolicyToCarrier,
                despatchPro = orderAddressInfo.despatchPro,
                despatchCity = orderAddressInfo.despatchCity,
                despatchDis = orderAddressInfo.despatchDis,
                despatchPlace = orderAddressInfo.despatchPlace,
                despatchCoordinateX = orderAddressInfo.despatchCoordinateX,
                despatchCoordinateY = orderAddressInfo.despatchCoordinateY,
                deliverPro = orderAddressInfo.deliverPro,
                deliverCity = orderAddressInfo.deliverCity,
                deliverDis = orderAddressInfo.deliverDis,
                deliverPlace = orderAddressInfo.deliverPlace,
                deliverCoordinateX = orderAddressInfo.deliverCoordinateX,
                deliverCoordinateY = orderAddressInfo.deliverCoordinateY
            ),
            IResultSuccessNoFail { baseRsp ->
                if (baseRsp.success()) {
                    setValue("onQueryOrderPolicyInfo", baseRsp.data, cargoMoney)
                }
            })
    }

    //转嫁查询气泡提示
    fun queryOrderPolicyInfoChangeModelV1(
        cargoList: List<CargoInfo>,
        giftMoney: String,
        cargoMoney: String,
        totalMoney: String,
        weight: String,
        changePolicyToCarrier: String,
        orderModel: String,
        orderAddressInfo: OrderAddressInfo,
        modeType: String,
    ) {
        if (TextUtils.equals("1", modeType)) {
            //代开专票不请求
            return
        }
        execute(
            Req17QueryOrderPolicyInfo(
                giftMoney = giftMoney,
                orderModel = orderModel,
                cargoNameStr = cargoList.toCommaString { it.cargoName },
                cargoMoney = cargoMoney,
                totalMoney = totalMoney,
                weight = weight,
                policyType = when (changePolicyToCarrier) {
                    "1" -> {
                        "5"
                    }

                    else -> {
                        "1"
                    }
                },
                changePolicyToCarrier = changePolicyToCarrier,
                despatchPro = orderAddressInfo.despatchPro,
                despatchCity = orderAddressInfo.despatchCity,
                despatchDis = orderAddressInfo.despatchDis,
                despatchPlace = orderAddressInfo.despatchPlace,
                despatchCoordinateX = orderAddressInfo.despatchCoordinateX,
                despatchCoordinateY = orderAddressInfo.despatchCoordinateY,
                deliverPro = orderAddressInfo.deliverPro,
                deliverCity = orderAddressInfo.deliverCity,
                deliverDis = orderAddressInfo.deliverDis,
                deliverPlace = orderAddressInfo.deliverPlace,
                deliverCoordinateX = orderAddressInfo.deliverCoordinateX,
                deliverCoordinateY = orderAddressInfo.deliverCoordinateY
            ),
            IResultSuccessNoFail { baseRsp ->
                if (baseRsp.success()) {
                    setValue("onQueryOrderPolicyInfoV1", baseRsp.data)
                }
            })
    }

    // 17 普通货：根据货物名称，审核状态，货值，赠保额度：查询保障服务相关信息
    fun queryOrderPolicyInfoDialog(
        cargoList: List<CargoInfo>,
        giftMoney: String,
        cargoMoney: String,
        totalMoney: String,
        weight: String,
        changePolicyToCarrier: String,
        orderModel: String,
        orderAddressInfo: OrderAddressInfo,
        modeType: String,
    ) {
        if (TextUtils.equals("1", modeType)) {
            return
        }
        execute(
            true,
            Req17QueryOrderPolicyInfo(
                giftMoney = giftMoney,
                orderModel = orderModel,
                cargoNameStr = cargoList.toCommaString { it.cargoName },
                cargoMoney = cargoMoney,
                totalMoney = totalMoney,
                weight = weight,
                policyType = when (changePolicyToCarrier) {
                    "1" -> {
                        "5"
                    }

                    else -> {
                        "1"
                    }
                },
                changePolicyToCarrier = changePolicyToCarrier,
                despatchPro = orderAddressInfo.despatchPro,
                despatchCity = orderAddressInfo.despatchCity,
                despatchDis = orderAddressInfo.despatchDis,
                despatchPlace = orderAddressInfo.despatchPlace,
                despatchCoordinateX = orderAddressInfo.despatchCoordinateX,
                despatchCoordinateY = orderAddressInfo.despatchCoordinateY,
                deliverPro = orderAddressInfo.deliverPro,
                deliverCity = orderAddressInfo.deliverCity,
                deliverDis = orderAddressInfo.deliverDis,
                deliverPlace = orderAddressInfo.deliverPlace,
                deliverCoordinateX = orderAddressInfo.deliverCoordinateX,
                deliverCoordinateY = orderAddressInfo.deliverCoordinateY
            )
        ) { baseRsp ->
            if (baseRsp.success()) {
                setValue("onQueryOrderPolicyInfoDialog", baseRsp.data)
            } else {
                showDialogToast(baseRsp.msg)
            }
        }
    }


    // 16.PC端和手机端接口：校验增值服务信息
    fun checkAdvanceInfo(data: ReqAddOrderForSeniorConsignor) {
        execute(
            true,
            Req16CheckAdvanceInfo().apply { setReqAddOrderForSeniorConsignor(data) }
        ) { baseRsp ->
            hideLoading()
            if (baseRsp.success()) {
                setValue("onCheckAdvanceInfo", baseRsp.data)
            } else {
                showDialogToast(baseRsp.msg)
            }
        }
    }

    // 22 根据货物名称查询是否展示保险提示信息标志
    fun queryPolicyTipsFlagByCargoName(data: ReqAddOrderForSeniorConsignor) {
        // 根据货物名称查询是否展示保险提示信息标志
        val cargoNameStr = data.cargoList.toCommaString { it.cargoName }
        if (cargoNameStr.isEmpty()) {
            return
        }
        if (data.orderInfo.modeType.isTrue) {
            //专票不查保险提示
            return
        }
        execute(
            true,
            Req22QueryPolicyTipsFlagByCargoName().setData(orderAddressInfo = data.orderAddressInfo, mCargoNameStr = cargoNameStr)
        ) { baseRsp ->
            hideLoading()
            if (baseRsp.success()) {
                setValue("onQueryPolicyTipsFlagByCargoName", baseRsp.data)
            } else {
                showDialogToast(baseRsp.msg)
            }
        }
    }

    // 提交之前的检查
    // 2，查询已认证货主保证金相关信息
    fun commitBeforeCheck(
        mData2: ReqAddOrderForSeniorConsignor,
        orderCommonInfo: Rsp23QueryMobileOrderCommonInfo,
        type: String
    ) {
        if (TextUtils.equals(mData2.orderInfo.modeType, "1")) {
            //代开专票不查货主保证金校验接口
            setValue("onQueryMobileDepositInfoForOrder", RspQueryMobileDepositInfoForOrder(), type)
        } else {
            execute(
                true,
                ReqQueryMobileDepositInfoForOrder().setDataV2(mData2, orderCommonInfo, type = type)
            ) { baseRsp ->
                hideLoading()
                if (baseRsp.success()) {
                    setValue("onQueryMobileDepositInfoForOrder", baseRsp.data, type)
                } else {
                    showDialogToast(baseRsp.msg)
                    OnEventUtil.showDialogEvent(baseRsp.msg)
                }
            }
        }
    }

    fun commit(mData: ReqAddOrderForSeniorConsignor, state: JumpNewGoodsData.PageState) {
        /**
         * 操作方式
         * add                  新增页面保存运单
         * publish              新增页面发布运单
         *
         * update               编辑页面保存运单
         * editpublish          编辑页面发布运单
         *
         * reupdate             重新发布页面保存运单
         * republish            重新发布页面发布运单
         *
         * import               导入发布运单
         *
         * addpublish           新增页面保存并发布运单
         * editaddpublish       编辑页面保存并发布运单
         * readdpublish         重新发布页面保存并发布运单
         */
        when (state) {
            JumpNewGoodsData.PageState.新增 -> {
                mData.orderInfo.operation = "publish"
            }

            JumpNewGoodsData.PageState.编辑 -> {
                mData.orderInfo.operation = "editpublish"
            }

            JumpNewGoodsData.PageState.重新发布 -> {
                mData.orderInfo.operation = "republish"
            }

            else -> {}
        }

        execute(
            false,
            mData,
            object : IResult<BaseRsp<RspAddResult>> {
                override fun onSuccess(baseRsp: BaseRsp<RspAddResult>) {
                    hideLoading()
                    if (baseRsp.success()) {
                        if (mData.orderInfo.batchPublishNum.isTrue) {
                            setValue("onCommitSuccess")
                        } else {
                            getSuccessImportNum()
                        }
                    } else {
                        when (val code = baseRsp.code) {
                            "3027" -> {
                                getPendingOrderIds("1", baseRsp.msg, code)
                            }

                            "3028" -> {
                                getPendingOrderIds("2", baseRsp.msg, code)
                            }

                            "3029" -> {
                                getPendingOrderIds("3", baseRsp.msg, code)
                            }

                            "2222" -> {
                                setValue("onCommitNoMoney", baseRsp.data)
                            }

                            "3031" -> {
                                setValue("onBlackList", baseRsp.data?.resultMsg)
                            }

                            "3032" -> {
                                setValue("onOverDue", baseRsp.msg)
                            }

                            "3033" -> {
                                setValue("onPreHanging", baseRsp.data, "commit")
                            }

                            "3034" -> {
                                setValue("onShowTimeCheck", baseRsp.data?.openPresetHour)
                            }

                            else -> {
                                showDialogToast(baseRsp.msg)
                                OnEventUtil.showDialogEvent(baseRsp.msg)
                            }
                        }
                    }
                }

                override fun onFail(p0: HandleException?) {
                    if (TextUtils.equals("10005", p0?.msg)) {
                        setValue("onCommitSuccess")
                    }
                }
            })
    }

    fun getPendingOrderIds(type: String, msg: String, code: String) {
        execute(
            ReqGetPendingOrderIds(type = type)
        ) { response ->
            if (response.success()) {
                response.data?.msg = msg
                response.data?.code = code
                setValue("onGetPendingOrderIdsSuccess", response.data)
            }
        }
    }

    fun checkAddressIsInRiskArea(req: Req53CheckAddressIsInRiskArea, type: String) {
        execute(
            req
        ) { response ->
            if (response.success()) {
                response.data?.type = type
                setValue("checkAddressIsInRiskAreaSuccess", response.data)
            }
        }
    }

    fun doCheck() {

    }

    //  保存草稿
    fun saveDrafts(mData: ReqAddOrderForSeniorConsignor, state: JumpNewGoodsData.PageState) {
        /**
         * 操作方式
         * add                  新增页面保存运单
         * publish              新增页面发布运单
         *
         * update               编辑页面保存运单
         * editpublish          编辑页面发布运单
         *
         * reupdate             重新发布页面保存运单
         * republish            重新发布页面发布运单
         *
         * import               导入发布运单
         *
         * addpublish           新增页面保存并发布运单
         * editaddpublish       编辑页面保存并发布运单
         * readdpublish         重新发布页面保存并发布运单
         */
        when (state) {
            JumpNewGoodsData.PageState.新增 -> {
                mData.orderInfo.operation = "add"
            }

            JumpNewGoodsData.PageState.编辑 -> {
                mData.orderInfo.operation = "update"
            }

            JumpNewGoodsData.PageState.重新发布 -> {
                mData.orderInfo.operation = "reupdate"
            }

            else -> {}
        }
        execute(
            false,
            mData
        ) { baseRsp ->
            hideLoading()
            if (baseRsp.success()) {
                setValue("onSaveDraftsSuccess")
            } else {
                when (baseRsp.code) {
                    "3031" -> {
                        setValue("onBlackList", baseRsp.data?.resultMsg)
                    }

                    "3032" -> {
                        setValue("onOverDue", baseRsp.msg)
                    }

                    "3033" -> {
                        setValue("onPreHanging", baseRsp.data, "saveDrafts")
                    }

                    "3034" -> {
                        setValue("onShowTimeCheck", baseRsp.data?.openPresetHour)
                    }

                    else -> {
                        showDialogToast(baseRsp.msg)
                    }
                }
            }
        }
    }

    //  保存并发布
    fun saveAndCommit(mData: ReqAddOrderForSeniorConsignor, state: JumpNewGoodsData.PageState) {
        /**
         * 操作方式
         * add                  新增页面保存运单
         * publish              新增页面发布运单
         *
         * update               编辑页面保存运单
         * editpublish          编辑页面发布运单
         *
         * reupdate             重新发布页面保存运单
         * republish            重新发布页面发布运单
         *
         * import               导入发布运单
         *
         * addpublish           新增页面保存并发布运单
         * editaddpublish       编辑页面保存并发布运单
         * readdpublish         重新发布页面保存并发布运单
         */
        when (state) {
            JumpNewGoodsData.PageState.新增 -> {
                mData.orderInfo.operation = "addpublish"
            }

            JumpNewGoodsData.PageState.编辑 -> {
                mData.orderInfo.operation = "editaddpublish"
            }

            JumpNewGoodsData.PageState.重新发布 -> {
                mData.orderInfo.operation = "readdpublish"
            }

            else -> {}
        }
        execute(
            false,
            mData,
            object : IResult<BaseRsp<RspAddResult>> {
                override fun onSuccess(baseRsp: BaseRsp<RspAddResult>) {
                    if (baseRsp.success()) {
                        if (mData.orderInfo.batchPublishNum.isTrue) {
                            setValue("onSaveDraftsSuccess")
                        } else {
                            getSuccessImportNum()
                        }
                    } else {
                        when (baseRsp.code) {
                            "3031" -> {
                                setValue("onBlackList", baseRsp.data?.resultMsg)
                            }

                            "3032" -> {
                                setValue("onOverDue", baseRsp.msg)
                            }

                            "3033" -> {
                                setValue("onPreHanging", baseRsp.data, "saveAndCommit")
                            }

                            "3034" -> {
                                setValue("onShowTimeCheck", baseRsp.data?.openPresetHour)
                            }

                            else -> {
                                showDialogToast(baseRsp.msg)
                            }
                        }
                    }
                }

                override fun onFail(p0: HandleException?) {
                    if (TextUtils.equals("10005", p0?.msg)) {
                        setValue("onCommitSuccess")
                    }
                }
            }
        )
    }

    fun queryTonRuleByCargoName(req: ReqQueryTonRuleByCargoName) {
        execute(req) {
            if (it.success()) {
                setValue("queryTonRuleByCargoNameSuccess", it.data)
            } else {
                showToast(it.msg)
            }
        }
    }

    fun queryOilRewardInfoByPublish(req: Req37QueryOilRewardInfoByPublish) {
        execute(req) {
            if (it.success()) {
                setValue("onQueryOilRewardInfoByPublish", it.data)
            } else {
                showToast(it.msg)
            }
        }
    }

    /**
     * 货主汛期运输安全通知
     */
    fun queryPublishNotice(req: ReqPublishNotice, type: String) {
        execute(req) {
            if (it.success()) {
                setValue("onQueryPublishNotice", it.data, type, req.confirmFlag ?: "")
            } else {
                showToast(it.msg)
            }
        }
    }

    fun getSuccessImportNum() {
        var observable: Disposable? = null
        observable = Observable.interval(1000, TimeUnit.MILLISECONDS)
            .subscribeOn(Schedulers.io())
            .observeOn(AndroidSchedulers.mainThread())
            .subscribe {
                execute(Req55GetSuccessImportNum(), object : IResult<BaseRsp<Rsp55GetSuccessImportNum>> {
                    override fun onSuccess(sendRequest: BaseRsp<Rsp55GetSuccessImportNum>) {
                        if (sendRequest.success()) {
                            val data = sendRequest.data
                            if (TextUtils.equals(data?.finishFlag, "0")) {
                                //发布未结束
                                setValue("onGetSuccessImportNum", data?.successImportNum)
                            } else {
                                //发布结束包括异常结束
                                observable?.dispose()
                                setValue("onGetSuccessImportNum", "-1")
                            }
                        } else {
                            //发布结束包括异常结束
                            setValue("onGetSuccessImportNum", "-1")
                            observable?.dispose()
                        }
                    }

                    override fun onFail(p0: HandleException?) {
                        //发布结束包括异常结束
                        setValue("onGetSuccessImportNum", "-1")
                        observable?.dispose()
                    }

                })
            }
    }

    fun queryCargoAverage(req: ReqQueryCargoAverage) {
        execute(req) {
            if (it.success()) {
                setValue("queryCargoAverageSuccess", it.data?.cargoUnitMoney)
            }
        }
    }

    fun queryPersonPolicy(req: ReqQueryPersonPolicy) {
        execute(req) {
            if (it.success()) {
                setValue("queryPersonPolicyNormalSuccess", it.data)
            }
        }
    }

    /***
     * 上传文件
     * 响应方法：onaddViolateSuccess，onFileFailure
     * @param file
     */
    fun upFile(file: List<String>) {
        for (p in file) {
            this.upFile(p)
        }
    }

    private fun upFile(file: String) {
        val disposable = CommServer.getFileServer()
            .update(File(file), object : IFileServer.OnFileUploaderListener {
                override fun onSuccess(tag: File, url: String) {
                    setValue("onFileSuccess", tag, url)
                }

                override fun onFailure(tag: File, error: String) {
                    showToast(error)
                    setValue("onFileFailure", tag, error)
                }
            })
        this.putDisposable(disposable)
    }

    fun queryRecommendVehicleType(req: ReqQueryRecommendVehicleType) {
        execute(req) {
            if (it.success()) {
                setValue("queryRecommendVehicleTypeSuccess", it.data)
            }
        }
    }

    fun queryChildArray(req: ReqQueryChildArray) {
        execute(req) {
            if (it.success()) {
                setValue("queryChildArraySuccess", it.data?.childArray)
            } else {
                showToast(it.msg)
            }
        }
    }
}