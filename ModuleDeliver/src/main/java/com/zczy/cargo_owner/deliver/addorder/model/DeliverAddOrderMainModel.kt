package com.zczy.cargo_owner.deliver.addorder.model

import android.text.TextUtils
import com.sfh.lib.event.RxBusEvent
import com.sfh.lib.exception.HandleException
import com.sfh.lib.mvvm.service.BaseViewModel
import com.zczy.cargo_owner.libcomm.event.EventNewGoodsSuccess
import com.zczy.cargo_owner.deliver.addorder.req.ReqQueryMemberConfigV1
import com.zczy.cargo_owner.deliver.addorder.req.comm.ReqTypeMapV1
import com.zczy.cargo_owner.libcomm.req.ReqQueryAcctSubConfigBusinessType
import com.zczy.cargo_owner.libcomm.req.ReqShowLessThanCarloadFreight
import com.zczy.cargo_owner.libcomm.req.showFastTrack
import com.zczy.comm.CommServer
import com.zczy.comm.utils.ex.isNotNull
import com.zczy.comm.utils.ex.isTrue
import io.reactivex.Observable
import io.reactivex.Observer
import io.reactivex.android.schedulers.AndroidSchedulers
import io.reactivex.disposables.Disposable
import io.reactivex.schedulers.Schedulers

/**
 * PS:
 * Created by sdx on 2019/2/13.
 */
class DeliverAddOrderMainModel : BaseViewModel() {
    override fun eventOnOff(): Boolean = true

    @RxBusEvent(from = "发新货成功")
    open fun onEventNewGoodsSuccess(data: EventNewGoodsSuccess) {
        setValue("onEventNewGoodsSuccess")
    }

    /**
     * 查询类型字典
     *
     * @param reqTypeMap
     */
    fun queryTypeMapV1(reqTypeMap: ReqTypeMapV1) {
        this.execute(
            reqTypeMap
        ) { result ->
            if (result.success()) {
                setValue("onQueryTypeMapSuccessV1", result.data)
            } else {
                setValue("onQueryTypeMapErrorV1")
            }
        }
    }

    fun queryDisPositionData() {
        val deliverDispositionData = DeliverDispositionData()
        val login = CommServer.getUserServer().login
        Observable.zip(
            ReqQueryMemberConfigV1(userId = login.userId).task,
            ReqShowLessThanCarloadFreight(userId = login.userId).task,
            ReqQueryAcctSubConfigBusinessType(userId = login.userId).task,
            ReqTypeMapV1(dictCode = "CONTAINER_USER_IDS").task,
        ) { t1, t2, t3, t4 ->
            when {
                !t1.success() -> {
                    throw HandleException(t1.code.toIntOrNull() ?: 0, t1.msg)
                }

                !t2.success() -> {
                    throw HandleException(t2.code.toIntOrNull() ?: 0, t2.msg)
                }

                !t3.success() -> {
                    throw HandleException(t3.code.toIntOrNull() ?: 0, t3.msg)
                }

                !t4.success() -> {
                    throw HandleException(t4.code.toIntOrNull() ?: 0, t4.msg)
                }
            }
            deliverDispositionData.wholePackageHugeSwitch = t1.data?.wholePackageHugeSwitch?.isTrue ?: false
            deliverDispositionData.showLessThanCarloadFreight = t2.data?.showLessThanCarloadFreight?.isTrue ?: false
            deliverDispositionData.showFastTrack = t3.data?.showFastTrack() ?: false
            t4.data?.records?.let { records ->
                CommServer.getUserServer().login?.let { login ->
                    if (records.size <= 0) {
                        deliverDispositionData.showContainer = false
                    } else {
                        records.forEach { item ->
                            val find = item.value?.split(",")?.find { sp ->
                                TextUtils.equals(sp, login.userId)
                            }
                            if (find.isNotNull && !deliverDispositionData.showContainer) {
                                deliverDispositionData.showContainer = true
                            }
                        }
                    }
                }
            }
            true
        }.subscribeOn(Schedulers.io())
            .observeOn(AndroidSchedulers.mainThread())
            .doOnSubscribe { showLoading(true) }
            .subscribe(object : Observer<Boolean> {
                override fun onSubscribe(d: Disposable) {
                    putDisposable(d)
                }

                override fun onNext(t: Boolean) {
                    setValue("onQueryDisPositionDataSuccess", deliverDispositionData)
                }

                override fun onError(e: Throwable) {
                    showDialogToast(e.message)
                    hideLoading()
                }

                override fun onComplete() {
                    hideLoading()
                }
            })
    }

    class DeliverDispositionData(
        var showLessThanCarloadFreight: Boolean = false, //是否配置零担货
        var showContainer: Boolean = false, //是否配置集装箱货
        var wholePackageHugeSwitch: Boolean = false, //是否配置整包批量货
        var showFastTrack: Boolean = false, //是否配置极速好货
    )

}