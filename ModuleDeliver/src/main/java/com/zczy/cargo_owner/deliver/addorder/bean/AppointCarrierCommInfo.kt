package com.zczy.cargo_owner.deliver.addorder.bean

import java.io.Serializable


/**
 * Created by: ssp
 * Date: 2020/6/1 17:24
 * Description:  指定承运方相关限制条件
 */

class AppointCarrierCommInfo : Serializable {
    var appointCarrierMaxMumber: String = "0"
    var appointGroupMaxMumber: String = "0"
    var appointStaffMaxMumber: String = "0"
    var supportSocialAppointFlag: Boolean = false
    var selectAppointFlag: Boolean = false
    var appointCarrierSwitch: Boolean = false
}