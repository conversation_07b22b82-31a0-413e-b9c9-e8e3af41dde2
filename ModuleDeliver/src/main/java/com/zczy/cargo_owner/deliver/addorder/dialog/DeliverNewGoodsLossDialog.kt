package com.zczy.cargo_owner.deliver.addorder.dialog

import android.os.Bundle
import android.text.TextUtils
import android.view.View
import android.widget.ImageView
import com.chad.library.adapter.base.BaseQuickAdapter
import com.chad.library.adapter.base.BaseViewHolder
import com.zczy.cargo_owner.deliver.R
import com.zczy.cargo_owner.deliver.addorder.req.comm.OrderCommonTonRuleItem
import com.zczy.cargo_owner.libcomm.config.HttpConfig
import com.zczy.comm.ui.BaseDialog
import com.zczy.comm.utils.dp2px
import com.zczy.comm.utils.ex.isTrue
import com.zczy.comm.widget.itemdecoration.SpaceItemDecoration
import com.zczy.comm.x5.X5WebActivity
import kotlinx.android.synthetic.main.deliver_new_goods_loss_dialog.recyclerView
import kotlinx.android.synthetic.main.deliver_new_goods_loss_dialog.tvClose
import kotlinx.android.synthetic.main.deliver_new_goods_loss_dialog.tv_sure

/**
 *  user: ssp
 *  time: 2021/5/8 17:07
 *  desc: 亏涨吨规则
 */

class DeliverNewGoodsLossDialog : BaseDialog() {
    private var mAdapter: DeliverNewGoodsLossAdapter? = DeliverNewGoodsLossAdapter()
    private var listener: OnSelectTonRuleListener? = null
    override fun getDialogTag(): String = "DeliverNewGoodsTimeDialog"

    override fun getDialogLayout(): Int = R.layout.deliver_new_goods_loss_dialog

    override fun bindView(view: View, bundle: Bundle?) {
        reAdjustView(0, 0)
        initView()
    }

    override fun getDialogType(): DialogType {
        return DialogType.bottom
    }

    private fun initView() {
        mAdapter?.apply {
            bindToRecyclerView(recyclerView)
            onItemChildClickListener = BaseQuickAdapter.OnItemChildClickListener { adapter, view, position ->
                val item = mAdapter?.data?.get(position)
                when (view.id) {
                    R.id.view_check_1 -> {
                        mAdapter?.setTonRuleId(item?.tonRuleId)
                    }

                    R.id.tv_detail -> {
                        // 亏涨吨详情
                        X5WebActivity.startNoTitleContentUI(context, HttpConfig.getWebUrl() + "/form_h5/h5_inner/index.html?_t=${System.currentTimeMillis()}#/lossRiseRuleNew?ruleId=${item?.tonRuleId}")
                    }
                }
            }
        }
        recyclerView.apply {
            layoutManager =
                androidx.recyclerview.widget.LinearLayoutManager(context)
            adapter = mAdapter
            addItemDecoration(SpaceItemDecoration(dp2px(2f)))
        }
        tv_sure.setOnClickListener {
            listener?.onSelectTonRule(mAdapter?.getCheckedData())
            mAdapter = null
            dismiss()
        }
        tvClose.setOnClickListener {
            dismiss()
        }
    }

    /**
     * 设置列表数据
     */
    fun setTonRuleData(data: MutableList<OrderCommonTonRuleItem>) {
        mAdapter?.setNewData(data)
    }

    fun setTonRuleId(tonRuleId: String?) {
        mAdapter?.setTonRuleId(tonRuleId)
    }

    class DeliverNewGoodsLossAdapter :
        BaseQuickAdapter<OrderCommonTonRuleItem, BaseViewHolder>(R.layout.deliver_new_goods_loss_item) {
        //之前是否有选择
        private var tonRuleId: String? = ""
        private var checkedData: OrderCommonTonRuleItem? = null
        private var cancelChceked: Boolean = false
        override fun convert(helper: BaseViewHolder?, item: OrderCommonTonRuleItem?) {
            helper?.apply {
                item?.let {
                    //选择亏涨吨规则
                    addOnClickListener(R.id.view_check_1)
                    //进入亏涨吨详情
                    addOnClickListener(R.id.tv_detail)
                    setText(R.id.tv_name, it.tonRuleName)
                    when {
                        TextUtils.isEmpty(tonRuleId) -> {
                            //未做选择 查找默认的选择
                            when {
                                it.tonRuleDefaultFlag.isTrue -> {
                                    if (!cancelChceked) {
                                        //表示后台返回默认选择项
                                        setCheckedData(item)
                                        getView<ImageView>(R.id.view_check_1).isSelected = true
                                    } else {
                                        getView<ImageView>(R.id.view_check_1).isSelected = false
                                    }
                                }

                                else -> {
                                    getView<ImageView>(R.id.view_check_1).isSelected = false
                                }
                            }
                        }

                        else -> {
                            when (it.tonRuleId) {
                                tonRuleId -> {
                                    setCheckedData(item)
                                    getView<ImageView>(R.id.view_check_1).isSelected = true
                                }

                                else -> {
                                    getView<ImageView>(R.id.view_check_1).isSelected = false
                                }
                            }
                        }
                    }
                }
            }
        }

        fun setTonRuleId(tonRuleId: String?) {
            val tonRuleId1 = checkedData?.tonRuleId ?: ""
            if (TextUtils.equals(tonRuleId, tonRuleId1)) {
                //选择了相同项 反选
                this.tonRuleId = ""
                this.checkedData = null
                cancelChceked = true
            } else {
                this.tonRuleId = tonRuleId
            }
            notifyDataSetChanged()
        }

        private fun setCheckedData(data: OrderCommonTonRuleItem) {
            checkedData = data
        }

        fun getCheckedData(): OrderCommonTonRuleItem? {
            return checkedData
        }
    }

    fun setOnSelectTonRuleListener(listeners: OnSelectTonRuleListener) {
        this.listener = listeners
    }

    interface OnSelectTonRuleListener {
        fun onSelectTonRule(data: OrderCommonTonRuleItem?)
    }
}