package com.zczy.cargo_owner.deliver.addorder.dialog

import android.os.Bundle
import android.view.View
import com.zczy.cargo_owner.deliver.R
import com.zczy.cargo_owner.deliver.addorder.widget.AddOrderSelectVehicleChildView
import com.zczy.cargo_owner.deliver.bean.DeliverVehicleTypeData
import com.zczy.cargo_owner.deliver.bean.RspDeliverBeanInfo
import com.zczy.comm.ui.BaseDialog
import kotlinx.android.synthetic.main.deliver_order_recommended_car_models_dialog.iv_close
import kotlinx.android.synthetic.main.deliver_order_recommended_car_models_dialog.tvSure

/**
 *  desc: 推荐车型
 *  user: ssp
 *  time: 2025/4/19 14:20
 */

class DeliverOrderRecommendedCarModelsDialog(var recommendVehicleList: MutableList<String> = mutableListOf(), var onBlock: (data: DeliverVehicleTypeData) -> Unit = {}) : BaseDialog() {
    private var mData = DeliverVehicleTypeData()
    private var viewType: AddOrderSelectVehicleChildView<RspDeliverBeanInfo>? = null
    private val typeListener: AddOrderSelectVehicleChildView.Listener<RspDeliverBeanInfo> =
        object : AddOrderSelectVehicleChildView.Listener<RspDeliverBeanInfo> {
            override fun onSelectItem(item: MutableList<RspDeliverBeanInfo>) {

            }
        }

    override fun getDialogTag(): String = "DeliverOrderRecommendedCarModelsDialog"

    override fun getDialogLayout(): Int = R.layout.deliver_order_recommended_car_models_dialog

    override fun bindView(view: View, bundle: Bundle?) {
        reAdjustView(35, 0)
        initView(view)
    }

    private fun initView(view: View) {
        viewType = view.findViewById(R.id.view_child_type)
        viewType?.apply {
            setTitle("车型要求")
            listener = object : AddOrderSelectVehicleChildView.Listener<RspDeliverBeanInfo> {
                override fun onSelectItem(item: MutableList<RspDeliverBeanInfo>) {
                    mData.vehicleType = item
                }
            }

            formatBlock = {
                it?.value ?: ""
            }
            val list = recommendVehicleList.map { RspDeliverBeanInfo(value = it) }
            setNewData(list)
        }
        iv_close.setOnClickListener { dismiss() }
        tvSure.setOnClickListener {
            onBlock(mData)
        }
    }
}