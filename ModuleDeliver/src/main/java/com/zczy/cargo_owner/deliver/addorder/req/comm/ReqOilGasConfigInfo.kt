package com.zczy.cargo_owner.deliver.addorder.req.comm

import com.zczy.comm.http.entity.BaseNewRequest
import com.zczy.comm.http.entity.BaseRsp
import com.zczy.comm.http.entity.ResultData

/**
 *  desc: 货主端油气配置优化 / 新增油气品默认值查询接口
 *  user: 宋双朋
 *  time: 2024/10/8 14:13
 */
class ReqOilGasConfigInfo(
    var deliverCity: String? = null, //目的城市
    var deliverCoordinateX: String? = null, //目的坐标
    var deliverCoordinateY: String? = null, //目的坐标
    var deliverDis: String? = null, //目的区
    var deliverPlace: String? = null, //目的详细地址
    var deliverPro: String? = null, //目的省
    var despatchCity: String? = null, //启运城市
    var despatchCoordinateX: String? = null, //启运坐标
    var despatchCoordinateY: String? = null, //启运坐标
    var despatchDis: String? = null, //启运区
    var despatchPlace: String? = null, //启运详细地址
    var despatchPro: String? = null, //启运省
) : BaseNewRequest<BaseRsp<RspOilGasConfigInfoV1>>("oms-app/order/consignor/config/oilGasConfigInfo")

data class RspOilGasConfigInfoV1(
    val data: RspOilGasConfigInfo? = null,
) : ResultData()

data class RspOilGasConfigInfo(
    val gasFixedCredit: String = "",//汽品固定额度
    val gasPercent: String = "",//汽品比例
    val oilFixedCredit: String = "",//油品固定额度
    val oilGasConfigSwitch: String = "",//油气配置开关  0 关,1 开
    val oilPercent: String = "",//油品固定额度
    val shortBargeFlag: String? = null//是否短途：0 否,1 是
)


