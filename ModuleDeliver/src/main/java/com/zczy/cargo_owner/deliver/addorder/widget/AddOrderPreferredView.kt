package com.zczy.cargo_owner.deliver.addorder.widget

import android.content.Context
import android.graphics.Color
import android.util.AttributeSet
import android.view.LayoutInflater
import android.widget.LinearLayout
import com.zczy.cargo_owner.deliver.R
import com.zczy.cargo_owner.deliver.addorder.bean.batch.HugeOrderInfo
import com.zczy.cargo_owner.deliver.addorder.bean.normal.OrderInfo
import com.zczy.cargo_owner.deliver.addorder.req.comm.Rsp23QueryMobileOrderCommonInfo
import com.zczy.cargo_owner.deliver.addorder.req.comm.Rsp24QueryMobileHugeOrderCommonInfo
import com.zczy.comm.utils.ex.isTrue
import com.zczy.comm.utils.ex.setVisible
import com.zczy.comm.widget.inputv2.InputViewCheckV2
import com.zczy.comm.widget.inputv2.InputViewCheckV2.LEFT
import com.zczy.comm.widget.inputv2.InputViewCheckV2.RIGHT
import io.reactivex.annotations.NonNull
import kotlinx.android.synthetic.main.deliver_add_order_preferred_view.view.input_order_preferred

/**
 *  user: ssp
 *  time: 2020/11/10 16:38
 *  desc: 发货优选
 */

class AddOrderPreferredView : LinearLayout {

    private var viewData = ViewData()
    private var onCheckListener: OnCheckListener? = null

    constructor(context: Context) : super(context)

    constructor(context: Context, attrs: AttributeSet?) : super(context, attrs)

    constructor(context: Context, attrs: AttributeSet?, defStyleAttr: Int) : super(context, attrs, defStyleAttr)

    init {
        orientation = VERTICAL
        setBackgroundColor(Color.WHITE)
        LayoutInflater.from(context).inflate(R.layout.deliver_add_order_preferred_view, this)
        refreshView()
    }

    fun refreshNormalData(orderInfo: OrderInfo, orderCommonInfo: Rsp23QueryMobileOrderCommonInfo) {

        viewData = ViewData(
            supportSocialAppointFlag = orderCommonInfo.supportSocialAppointFlag,
            appointCarrierSwitch = orderCommonInfo.appointCarrierSwitch,
            specifyFlag = orderInfo.specifyFlag,
            selectAppointFlag = orderInfo.selectAppointFlag,
            supportPriorSelectFlag = orderCommonInfo.supportPriorSelectFlag,
            priorSelectFlag = orderInfo.priorSelectFlag,
            pbPresetSelectFlag = orderInfo.pbPresetSelectFlag,
        )
        refreshView()
    }

    private fun getCheckState(): Boolean {
        return when (input_order_preferred.check) {
            LEFT -> {
                true
            }

            else -> {
                false
            }
        }
    }

    fun refreshNormalData(hugeOrderInfo: HugeOrderInfo, orderCommonInfo: Rsp24QueryMobileHugeOrderCommonInfo) {
        viewData = ViewData(
            supportSocialAppointFlag = orderCommonInfo.supportSocialAppointFlag,
            appointCarrierSwitch = orderCommonInfo.appointCarrierSwitch,
            specifyFlag = hugeOrderInfo.specifyFlag,
            selectAppointFlag = hugeOrderInfo.selectAppointFlag,
            supportPriorSelectFlag = orderCommonInfo.supportPriorSelectFlag,
            priorSelectFlag = hugeOrderInfo.priorSelectFlag,
            pbPresetSelectFlag = hugeOrderInfo.pbPresetSelectFlag,
        )
        refreshView()
    }

    private fun refreshView() {
        if (!viewData.supportPriorSelectFlag.isTrue) {
            //返回不为1
            this.setVisible(false)
        } else {
            //返回为1
            when {
                viewData.pbPresetSelectFlag.isTrue -> {
                    // 普通货支持VIP 否  是否支持全平台 否 是否优选专区 是
                    this.setVisible(false)
                    viewData.priorSelectFlag = "0"
                }

                !viewData.appointCarrierSwitch.isTrue && !viewData.supportSocialAppointFlag.isTrue -> {
                    // 普通货支持VIP 否  是否支持全平台 否 是否优选专区 是
                    this.setVisible(true)
                }

                !viewData.appointCarrierSwitch.isTrue && viewData.supportSocialAppointFlag.isTrue -> {
                    // 普通货支持VIP 否  是否支持全平台 是 是否优选专区 是
                    setPreferredVisibility()
                }

                viewData.appointCarrierSwitch.isTrue && viewData.supportSocialAppointFlag.isTrue -> {
                    // 普通货支持VIP 是  是否支持全平台 是 是否优选专区 是
                    setPreferredVisibility()
                }

                viewData.appointCarrierSwitch.isTrue && !viewData.supportSocialAppointFlag.isTrue -> {
                    // 普通货支持VIP 是  是否支持全平台 否 是否优选专区 是
                    setPreferredVisibility()
                }
            }
        }

        when (viewData.priorSelectFlag) {
            "0" -> {
                input_order_preferred.check = RIGHT
            }

            "1" -> {
                input_order_preferred.check = LEFT
            }
        }

    }

    private fun setPreferredVisibility() {
        if (viewData.selectAppointFlag.isTrue) {
            //勾选了VIP运力池为是
            this.setVisible(false)
        } else {
            this.setVisible(true)
        }
    }

    data class ViewData(
        var id: Int = 0,
        var supportSocialAppointFlag: String = "",
        var selectAppointFlag: String = "0",
        var specifyFlag: String = "0",
        var supportPriorSelectFlag: String = "0",
        var priorSelectFlag: String = "0",
        var appointCarrierSwitch: String = "",
        var pbPresetSelectFlag: String = "",//是否预挂单
    )

    fun setListeners(@NonNull listener: InputViewCheckV2.Listener) {
        input_order_preferred.setListener(listener)
    }

    fun setOnCheckListener(onCheckListener: OnCheckListener?) {
        this.onCheckListener = onCheckListener

    }

    interface OnCheckListener {
        fun onChecked(boolean: Boolean)
    }

}