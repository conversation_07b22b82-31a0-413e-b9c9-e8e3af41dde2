package com.zczy.cargo_owner.deliver.addorder.widget

import android.annotation.SuppressLint
import android.content.Context
import android.util.AttributeSet
import android.view.View
import android.widget.LinearLayout
import com.zczy.cargo_owner.deliver.R
import com.zczy.comm.utils.ex.setVisible
import com.zczy.comm.utils.getResColor

/**
 *  user: ssp
 *  time: 2021/4/29 14:38
 *  desc: 德达油品专用
 */

class OilCardViewDeDaV2 @JvmOverloads constructor(
    context: Context,
    attrs: AttributeSet? = null,
    defStyleAttr: Int = 0
) : LinearLayout(context, attrs, defStyleAttr) {

    private var viewData = ViewData()

    var listenerDeDa: Listener? = null

    init {
        View.inflate(context, R.layout.deliver_oil_card_view_de_da_v2, this)
        orientation = VERTICAL
        setBackgroundColor(getResColor(R.color.white))
    }

    @SuppressLint("SetTextI18n")
    fun refreshViewV2() {
        viewData.gasPercent = "26"
        viewData.oilPercent = "15"
        viewData.oilCalculateType = "1"
        viewData.supportSdOilCardFlag = "1"
        viewData.whetherDedaUserId = "1"
        listenerDeDa?.onDataChange(viewData)
        setVisible(true)
    }

    data class ViewData(
        /** 油品计算方式：0 默认,1 按比例计算油品,2 按固定额度计算油品 */
        var oilCalculateType: String = "",
        /** 是否包含油品：0 否,1 是 */
        var supportSdOilCardFlag: String = "",
        /** 油品比例 */
        var oilPercent: String = "",
        /** 汽品比例 */
        var gasPercent: String = "",
        /** 是否德达油卡 1 是 0 否*/
        var whetherDedaUserId: String = "1"
    )

    interface Listener {
        fun onDataChange(data: ViewData)
    }
}