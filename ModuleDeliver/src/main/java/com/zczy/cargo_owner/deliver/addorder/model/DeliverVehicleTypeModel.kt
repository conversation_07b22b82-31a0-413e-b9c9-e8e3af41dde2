package com.zczy.cargo_owner.deliver.addorder.model

import com.sfh.lib.mvvm.service.BaseViewModel
import com.zczy.cargo_owner.deliver.addorder.req.comm.Req19QueryVehicleLengthList
import com.zczy.cargo_owner.deliver.addorder.req.comm.Req20QueryVehicleTypeList
import com.zczy.cargo_owner.deliver.addorder.req.comm.RspQueryVehicleLengthList
import com.zczy.cargo_owner.deliver.addorder.req.comm.RspQueryVehicleTypeList
import com.zczy.cargo_owner.deliver.bean.RspDeliverBeanInfo
import com.zczy.comm.http.entity.BaseRsp
import io.reactivex.Observable
import io.reactivex.Observer
import io.reactivex.android.schedulers.AndroidSchedulers
import io.reactivex.disposables.Disposable
import io.reactivex.functions.BiFunction
import io.reactivex.schedulers.Schedulers

/**
 * PS:
 * Created by sdx on 2019/2/15.
 */
class DeliverVehicleTypeModel : BaseViewModel() {

    class VehicleLengthTypeList(
            var vehicleLengths: MutableList<RspDeliverBeanInfo> = mutableListOf(),
            var vehicleTypes: MutableList<RspDeliverBeanInfo> = mutableListOf()
    )

    fun queryVehicleList() {
        // 车长接口
        val length = Req19QueryVehicleLengthList().task
        // 车型接口
        val type = Req20QueryVehicleTypeList().task
        // zip
        Observable.zip(length, type,
                BiFunction<BaseRsp<RspQueryVehicleLengthList?>, BaseRsp<RspQueryVehicleTypeList?>, VehicleLengthTypeList>
                { reqLength, reqType ->
                    when {
                        // 车长接口 成功校验
                        !reqLength.success() -> {
                            throw  RuntimeException(reqLength.code+ reqLength.msg)
                        }
                        // 车型接口 成功校验
                        !reqType.success() -> {
                            throw  RuntimeException(reqType.code+ reqType.msg)
                        }
                    }
                    return@BiFunction VehicleLengthTypeList(
                            vehicleLengths = reqLength.data?.vehicleLengths ?: mutableListOf(),
                            vehicleTypes = reqType.data?.vehicleTypes ?: mutableListOf())
                })
                .subscribeOn(Schedulers.io())
                .observeOn(AndroidSchedulers.mainThread())
                .doOnSubscribe { showLoading(true) }
                .subscribe(object : Observer<VehicleLengthTypeList> {
                    override fun onSubscribe(d: Disposable) {
                        putDisposable(d)
                    }

                    override fun onNext(t: VehicleLengthTypeList) {
                        setValue("queryVehicleListSuccess", t)
                        hideLoading()
                    }

                    override fun onError(e: Throwable) {
                        showDialogToast(e.message)
                        hideLoading()
                    }

                    override fun onComplete() {
                    }
                })
    }
}