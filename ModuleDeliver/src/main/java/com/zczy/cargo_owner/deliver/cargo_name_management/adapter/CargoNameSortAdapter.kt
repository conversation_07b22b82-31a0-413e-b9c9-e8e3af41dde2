package com.zczy.cargo_owner.deliver.cargo_name_management.adapter

import com.chad.library.adapter.base.BaseItemDraggableAdapter
import com.chad.library.adapter.base.BaseViewHolder
import com.zczy.cargo_owner.deliver.R
import com.zczy.cargo_owner.deliver.cargo_name_management.bean.CargoNameItemBean

/**
 * @description
 * <AUTHOR> 韩正亚
 * @company 中储智运科技股份有限公司
 * @copyright （版权）中储南京智慧物流科技有限公司所有
 * @since 3/18/20 10:41
 */
class CargoNameSortAdapter
    : BaseItemDraggableAdapter<CargoNameItemBean, BaseViewHolder>(R.layout.deliver_item_cargo_name, null) {

    override fun convert(helper: BaseViewHolder?, item: CargoNameItemBean?) {
        helper?.setText(R.id.cargoName, item?.baseName)
    }

}