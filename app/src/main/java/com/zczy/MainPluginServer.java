package com.zczy;

import android.app.Activity;
import android.content.Context;
import android.content.Intent;

import androidx.fragment.app.Fragment;
import androidx.fragment.app.FragmentActivity;

import com.tencent.mm.opensdk.modelbiz.WXLaunchMiniProgram;
import com.tencent.mm.opensdk.openapi.IWXAPI;
import com.tencent.mm.opensdk.openapi.WXAPIFactory;
import com.zczy.cargo_owner.Constant;
import com.zczy.cargo_owner.deliver.drafts.ui.WebActivityHomeBanner;
import com.zczy.cargo_owner.freight.activity.FreightIntroduceActivity;
import com.zczy.cargo_owner.home.HomeActivity;
import com.zczy.cargo_owner.home.dialog.ServerPhoneDialog;
import com.zczy.cargo_owner.home.fragment.ShareDialogActivity;
import com.zczy.cargo_owner.home.onlinecall.OnLineCallActivity;
import com.zczy.cargo_owner.invoice.activity.InvoiceCenterActivity;
import com.zczy.cargo_owner.libcomm.config.HttpConfig;
import com.zczy.cargo_owner.order.detail.WaybillDetailStatueActivity;
import com.zczy.cargo_owner.order.express.scan.HWScanActivity;
import com.zczy.cargo_owner.tickling.UserTicklingAddActivity;
import com.zczy.cargo_owner.user.UserScanLoginActivity;
import com.zczy.cargo_owner.user.certification.CertificationUtils;
import com.zczy.cargo_owner.user.coupon.CouponSelectListActivity;
import com.zczy.cargo_owner.user.evaluate.EvaluateActivityV1;
import com.zczy.cargo_owner.user.evaluate.EvaluateDetailActivityV1;
import com.zczy.cargo_owner.user.exception.WaybillProveInfoSubmitActivity;
import com.zczy.comm.pluginserver.AMainServer;
import com.zczy.comm.pluginserver.bean.SelectNavigationData;
import com.zczy.lib_zstatistics.sdk.ZStatistics;
import com.zczy.rn.ReactNativeMainActivity;

import java.util.ArrayList;

public class MainPluginServer extends AMainServer {
    @Override
    public void openLogin(Context context) {

    }

    @Override
    public void openRegister(Context context) {

    }

    @Override
    public void changeMenu(Context context, String type) {
        HomeActivity.changeMenu(context, type);
    }

    @Override
    public void openLineServerHaveParams(Context context, String params) {
        OnLineCallActivity.start(context, params);
        ZStatistics.onViewClick(context, "tv_openLineServer");
    }

    @Override
    public void userAuthent(Context context) {
        //用户认证
        CertificationUtils.hzCertification(context);
    }

    @Override
    public void jumpToOrder(Context context) {

    }

    @Override
    public void jumpToHome(Context context) {

    }

    @Override
    public void updateLocation() {

    }

    @Override
    public void readText(String msgText) {

    }

    @Override
    public void jumpAddDriver(Context context) {

    }

    @Override
    public void jumpEvaluate(Context context, String orderId) {
        EvaluateActivityV1.start(context, orderId);
    }

    @Override
    public void jumpEvaluateDetails(Context context, String orderId) {
        EvaluateDetailActivityV1.start(context, orderId);
    }

    @Override
    public void jumpOnTracking(Context context, String orderId) {

    }

    @Override
    public void openLineServer(Context context) {
        OnLineCallActivity.start(context, "");
    }

    @Override
    public void showLineServerPhone(FragmentActivity activity) {
        ServerPhoneDialog.showDialogUI(activity);
    }

    @Override
    public void liveAuto(Context context, ILiveAutoListenter autoListenter) {

    }

    @Override
    public void jumpMessageMain(Activity context) {

    }

    @Override
    public void jumpMessageMain(Fragment fragment, int requestCode) {

    }

    @Override
    public void jumpRouteLine(Context context, String orderId, String routeDetails) {

    }

    @Override
    public void jumpTrainActivity(Context context) {

    }

    @Override
    public void selectNavigationTypePop(Activity context, SelectNavigationData data) {

    }

    @Override
    public void jumpShareDialog(Context context, String title, String url) {
        ShareDialogActivity.startContentUI(context, title, url);
    }

    @Override
    public void jumpShareDialog(Context context, String title, String content, String url) {
        ShareDialogActivity.startContentUI(context, title, content, url);
    }

    @Override
    public void jumpShareDialogHideMsg(Context context, String title, String url) {
        ShareDialogActivity.startContentUIHideMsg(context, title, url);
    }

    @Override
    public void jumpShareDialogHideMsgImage(Context context, String title, String imagePath) {
        ShareDialogActivity.startContentUIHideMsgImage(context, title, imagePath);
    }

    @Override
    public void openScanActivity(Context context) {
        UserScanLoginActivity.start(context);
    }

    @Override
    public void openWaybillExceptionActivity(Context context) {

    }

    @Override
    public void openWaybillProveInfoSubmitActivity(Activity activity, String monitorId, int requestCode) {
        WaybillProveInfoSubmitActivity.startUI(activity, monitorId, requestCode);
    }

    /*跳转司机管理*/
    @Override
    public void jumpWebActivityHomeBanne(Activity context, String bannerLink, String orderId) {
        WebActivityHomeBanner.startContentUI(context, bannerLink, "结算凭证单", orderId);
    }

    @Override
    public void openCouponSelectList(Fragment fragment, int requestCode, String freight, String orderId, String orderTime, ArrayList<String> noUserCouponIdList, String userCouponId) {
        CouponSelectListActivity.jumpPage(fragment, requestCode, freight, orderId, orderTime, noUserCouponIdList, userCouponId);
    }

    @Override
    public void openCouponSelectList(Activity activity, int requestCode, String freight, String orderId, String orderTime, ArrayList<String> noUserCouponIdList, String userCouponId) {
        CouponSelectListActivity.jumpPage(activity, requestCode, freight, orderId, orderTime, noUserCouponIdList, userCouponId);
    }

    @Override
    public void openScanActivity(Activity context, int request) {
        Intent intent = new Intent(context, HWScanActivity.class);
        context.startActivityForResult(intent, request);
    }

    @Override
    public void openRNActivity(Activity activity, String page) {
        ReactNativeMainActivity.start(activity, page);
    }

    @Override
    public void openRNActivity(Activity activity, String page, String data) {
        ReactNativeMainActivity.start(activity, page, data);
    }

    @Override
    public void openRNActivity(Fragment fragment, String page, String data) {
        ReactNativeMainActivity.start(fragment, page, data);
    }

    @Override
    public void openRNActivity(Fragment fragment, String page, String data, int request) {
        ReactNativeMainActivity.start(fragment, page, data, request);
    }

    @Override
    public void openRNActivity(Activity activity, String page, String data, int request) {
        ReactNativeMainActivity.start(activity, page, data, request);
    }

    @Override
    public void openInvoiceCenterActivity(Activity activity) {
        InvoiceCenterActivity.jumpPage(activity);
    }

    @Override
    public void openWaybillDetail(Activity activity, String orderId) {
        WaybillDetailStatueActivity.start(activity, orderId);
    }

    @Override
    public void openWeiXinChat(Context activity, String path) {
        IWXAPI api = WXAPIFactory.createWXAPI(activity, Constant.WECHAT_APPID);
        WXLaunchMiniProgram.Req req = new WXLaunchMiniProgram.Req();
        req.userName = "gh_171fe8ef4ec4"; // 填小程序原始id
        req.path = path;
        req.miniprogramType = HttpConfig.isDeBug() ? WXLaunchMiniProgram.Req.MINIPROGRAM_TYPE_PREVIEW : WXLaunchMiniProgram.Req.MINIPTOGRAM_TYPE_RELEASE;// 可选打开 开发版，体验版和正式版
        api.sendReq(req);
    }

    @Override
    public void openUserTicklingAddActivity(Fragment fragment, String name, String phone, String orderId, String label, String from) {
        UserTicklingAddActivity.start(fragment, name, phone, orderId, label, from, 0x01);
    }

    @Override
    public void openUserTicklingAddActivity(Activity activity, String orderId, String label, String from) {
        UserTicklingAddActivity.start(activity, orderId, label, from, 0x01);
    }

    @Override
    public void openFreightIntroduceActivity(Context context) {
        FreightIntroduceActivity.jumpPage(context);
    }
}
