package com.zczy

import android.app.Application
import com.didichuxing.doraemonkit.DoKit
import com.didichuxing.doraemonkit.kit.AbstractKit
import com.zczy.comm.x5.X5WebActivity
import com.zczy.libtools.kits.AppSettingKit
import com.zczy.libtools.kits.NetWorkProxyKit
import com.zczy.libtools.kits.NetWorkSettingKit
import com.zczy.libtools.kits.RouterTestKit
import com.zczy.libtools.kits.WifiSettingKit

/**
 * 注释：多啦A梦管理类
 * 时间：2021/4/9 0009 16:31
 * 作者：郭翰林
 */
object DoraemonManager {
    /**
     * 初始化多啦A梦
     */
    @JvmStatic
    fun initDoKit(context: Application) {
        DoKit.Builder(context)
            .customKits(getKits())
            //H5任意门回调
            .webDoorCallback { currentContext, url ->
                X5WebActivity.startNoTitleContentUI(
                    currentContext,
                    url
                )
            }
            .disableUpload()
            .debug(false)
            .build()
    }

    /**
     * 注释：自定义
     * 时间：2021/7/6 0006 17:42
     * 作者：郭翰林
     */
    fun getKits(): MutableList<AbstractKit> {
        val kits = mutableListOf<AbstractKit>()
        //App设置
        kits.add(AppSettingKit())
        //Wifi设置
        kits.add(WifiSettingKit())
        //环境切换
        kits.add(NetWorkSettingKit())
        //网络代理设置
        kits.add(NetWorkProxyKit())
        //测试页面
        kits.add(RouterTestKit())
        return kits
    }


}