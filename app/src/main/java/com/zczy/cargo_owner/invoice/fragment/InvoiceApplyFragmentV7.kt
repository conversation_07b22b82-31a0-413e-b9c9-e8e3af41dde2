package com.zczy.cargo_owner.invoice.fragment

import android.annotation.SuppressLint
import android.os.Bundle
import android.text.TextUtils
import android.view.View
import com.sfh.lib.mvvm.annotation.LiveDataMatch
import com.sfh.lib.mvvm.service.BaseViewModel
import com.sfh.lib.ui.dialog.DialogBuilder
import com.zczy.cargo_owner.R
import com.zczy.cargo_owner.invoice.activity.InvoiceWebActivity
import com.zczy.cargo_owner.invoice.dialog.InvoiceInputRuleNameDialog
import com.zczy.cargo_owner.invoice.model.InvoiceModel
import com.zczy.cargo_owner.invoice.req.ReqAddInvoiceRule
import com.zczy.cargo_owner.invoice.req.ReqDoInvoiceReapplyPreview
import com.zczy.cargo_owner.invoice.req.RspCommData
import com.zczy.cargo_owner.invoice.req.RspQueryApplyList
import com.zczy.cargo_owner.invoice.req.RspQueryCollectInfo
import com.zczy.cargo_owner.invoice.req.RspQueryInvoiceFeeInfo
import com.zczy.cargo_owner.invoice.req.RspQueryInvoiceRuleInfo
import com.zczy.cargo_owner.invoice.wight.InputViewCheckV6
import com.zczy.cargo_owner.libcomm.config.HttpConfig
import com.zczy.comm.ui.BaseFragment
import com.zczy.comm.utils.ex.isNotNull
import com.zczy.comm.utils.ex.isNull
import com.zczy.comm.utils.ex.isTrue
import com.zczy.comm.utils.ex.setVisible
import com.zczy.comm.widget.dialog.ChooseDialogV1
import com.zczy.comm.widget.inputv2.InputViewClick
import kotlinx.android.synthetic.main.invoice_apply_fragment_v7.editWithCountView
import kotlinx.android.synthetic.main.invoice_apply_fragment_v7.inputView1
import kotlinx.android.synthetic.main.invoice_apply_fragment_v7.inputView2
import kotlinx.android.synthetic.main.invoice_apply_fragment_v7.inputView3
import kotlinx.android.synthetic.main.invoice_apply_fragment_v7.invoiceCustomizeView
import kotlinx.android.synthetic.main.invoice_apply_fragment_v7.ivInvoiceCustomize
import kotlinx.android.synthetic.main.invoice_apply_fragment_v7.ivInvoiceLeft
import kotlinx.android.synthetic.main.invoice_apply_fragment_v7.ivInvoiceRight
import kotlinx.android.synthetic.main.invoice_apply_fragment_v7.tvLeft
import kotlinx.android.synthetic.main.invoice_apply_fragment_v7.tvName1
import kotlinx.android.synthetic.main.invoice_apply_fragment_v7.tvName2
import kotlinx.android.synthetic.main.invoice_apply_fragment_v7.tvName3
import kotlinx.android.synthetic.main.invoice_apply_fragment_v7.tvName4
import kotlinx.android.synthetic.main.invoice_apply_fragment_v7.tvRight
import kotlinx.android.synthetic.main.invoice_apply_fragment_v7.viewLayout1
import java.util.Calendar

/**
 *  desc: 开票申请-重新申请
 *  user: 宋双朋
 *  time: 2024/9/14 9:48
 */
@SuppressLint("SetTextI18n")
class InvoiceApplyFragmentV7 : BaseFragment<BaseViewModel>() {

    private var mRspQueryApplyList: RspQueryApplyList = RspQueryApplyList()
    private var mRspQueryDefaultInvoiceRuleInfo: RspQueryInvoiceRuleInfo? = null
    private var mRspQueryCollectInfo: RspQueryCollectInfo? = null
    private var mRspQueryDefaultInvoiceRuleInfoTemp: RspQueryInvoiceRuleInfo? = null
    private var ruleInfoList: MutableList<RspQueryInvoiceRuleInfo> = mutableListOf()
    private var onLastStep: () -> Unit = {

    }
    private var taxArr: MutableList<RspQueryInvoiceFeeInfo> = mutableListOf()

    companion object {

        fun newInstance(): InvoiceApplyFragmentV7 {
            val fragment = InvoiceApplyFragmentV7()
            val bundle = Bundle()
            fragment.arguments = bundle
            return fragment
        }
    }

    override fun getLayout(): Int {
        return R.layout.invoice_apply_fragment_v7
    }

    override fun initData() {
    }

    override fun bindView(view: View, bundle: Bundle?) {
        invoiceCustomizeView.fragment = this@InvoiceApplyFragmentV7
        inputView2.setListener(object : InputViewClick.Listener() {
            override fun onClick(viewId: Int, view: InputViewClick, content: String) {
                if (taxArr.size <= 1) {
                    return
                }
                ChooseDialogV1.instance(taxArr)
                    .setFlatMap { taxName ?: "" }
                    .setClick { rspQueryInvoiceFeeInfo, _ ->
                        inputView2.content = rspQueryInvoiceFeeInfo.taxName ?: ""
                        inputView2.tag = rspQueryInvoiceFeeInfo
                    }
                    .show(this@InvoiceApplyFragmentV7)
            }
        })
        inputView3.setListener(object : InputViewClick.Listener() {
            override fun onClick(viewId: Int, view: InputViewClick, content: String) {
                if (ruleInfoList.size < 1) {
                    return
                }
                ChooseDialogV1.instance(ruleInfoList)
                    .setFlatMap { ruleName ?: "" }
                    .setClick { item, _ ->
                        inputView3.content = item.ruleName ?: ""
                        inputView3.tag = item
                        mRspQueryDefaultInvoiceRuleInfo = item
                        if (ivInvoiceLeft.isSelected) {
                            initLeftRule()
                        }
                        if (ivInvoiceRight.isSelected) {
                            initRightRule()
                        }
                    }
                    .show(this@InvoiceApplyFragmentV7)
            }
        })
        invoiceCustomizeView.setBlock { req, msg ->
            if (msg.isNotEmpty()) {
                val dialogBuilder = DialogBuilder()
                dialogBuilder.title = "提示"
                dialogBuilder.message = "请选择开票要求！"
                dialogBuilder.isHideCancel = true
                dialogBuilder.setOKText("知道了")
                showDialog(dialogBuilder)
                return@setBlock
            }
            InvoiceInputRuleNameDialog { ruleName ->
                if (ruleName.isEmpty()) {
                    showToast("请设置规则名称")
                    return@InvoiceInputRuleNameDialog
                }
                req.ruleName = ruleName
                req.feeType = mRspQueryApplyList.feeType
                getViewModel(BaseViewModel::class.java).execute(req) { rsp ->
                    <EMAIL>?.runOnUiThread {
                        showToast(rsp.msg)
                    }
                }
            }.show(this@InvoiceApplyFragmentV7)
        }
        bindClickEvent(tvLeft)
        bindClickEvent(tvRight)
        bindClickEvent(ivInvoiceLeft)
        bindClickEvent(ivInvoiceRight)
        bindClickEvent(ivInvoiceCustomize)
    }

    fun setOnLastStep(onBlock: () -> Unit) {
        this.onLastStep = onBlock
    }

    fun setData(data1: RspQueryApplyList?, data2: RspQueryCollectInfo) {
        mRspQueryApplyList = data1 ?: RspQueryApplyList()
        mRspQueryCollectInfo = data2
        getViewModel(InvoiceModel::class.java).query(
            subsidiaryId = mRspQueryApplyList.subsidiaryId,
            feeType = mRspQueryApplyList.feeType,
        )
    }

    override fun onSingleClick(v: View) {
        super.onSingleClick(v)
        when (v.id) {
            R.id.tvLeft -> {
                onLastStep()
            }

            R.id.tvRight -> {
                //发票预览
                val invoiceType = when (inputView1.check) {
                    InputViewCheckV6.LEFT -> {
                        "6"
                    }

                    InputViewCheckV6.RIGHT -> {
                        "7"
                    }

                    else -> {
                        ""
                    }
                }
                if (invoiceType.isEmpty()) {
                    val dialogBuilder = DialogBuilder()
                    dialogBuilder.title = "提示"
                    dialogBuilder.message = "请选择发票类型！"
                    dialogBuilder.isHideCancel = true
                    dialogBuilder.setOKText("知道了")
                    showDialog(dialogBuilder)
                    return
                }
                val tag1 = inputView2.tag
                var taxName = ""
                var feeInfoId = ""
                if (tag1 is RspQueryInvoiceFeeInfo) {
                    taxName = tag1.taxName ?: ""
                    feeInfoId = tag1.id ?: ""
                }
                if (feeInfoId.isEmpty()) {
                    val dialogBuilder = DialogBuilder()
                    dialogBuilder.title = "提示"
                    dialogBuilder.message = "请选择发票项目！"
                    dialogBuilder.isHideCancel = true
                    dialogBuilder.setOKText("知道了")
                    showDialog(dialogBuilder)
                    return
                }
                val ruleSource = if (ivInvoiceLeft.isSelected) {
                    "2"
                } else if (ivInvoiceRight.isSelected) {
                    "1"
                } else if (ivInvoiceCustomize.isSelected) {
                    "3"
                } else {
                    ""
                }
                if (ruleSource.isEmpty()) {
                    val dialogBuilder = DialogBuilder()
                    dialogBuilder.title = "提示"
                    dialogBuilder.message = "请选择开票要求！"
                    dialogBuilder.isHideCancel = true
                    dialogBuilder.setOKText("知道了")
                    showDialog(dialogBuilder)
                    return
                }
                var ruleId = mRspQueryDefaultInvoiceRuleInfo?.id ?: ""
                if (ivInvoiceLeft.isSelected && ruleId.isEmpty()) {
                    val dialogBuilder = DialogBuilder()
                    dialogBuilder.title = "提示"
                    dialogBuilder.message = "请选择开票格式！"
                    dialogBuilder.isHideCancel = true
                    dialogBuilder.setOKText("知道了")
                    showDialog(dialogBuilder)
                    return
                }
                if (ivInvoiceRight.isSelected) {
                    ruleId = mRspQueryDefaultInvoiceRuleInfo?.id ?: ""
                }
                var mReqAddInvoiceRule: ReqAddInvoiceRule? = null
                var message = ""
                if (ivInvoiceCustomize.isSelected) {
                    invoiceCustomizeView.initRequest(false) { reqAddInvoiceRule, msg ->
                        mReqAddInvoiceRule = reqAddInvoiceRule
                        message = msg
                    }
                    if (message.isNotEmpty()) {
                        val dialogBuilder = DialogBuilder()
                        dialogBuilder.title = "提示"
                        dialogBuilder.message = message
                        dialogBuilder.isHideCancel = true
                        dialogBuilder.setOKText("知道了")
                        showDialog(dialogBuilder)
                        return
                    }
                }
                getViewModel(InvoiceModel::class.java).execute(
                    true,
                    ReqDoInvoiceReapplyPreview(
                        applyId = mRspQueryApplyList.applyId,
                        feeType = mRspQueryApplyList.feeType,
                        subsidiaryId = mRspQueryApplyList.subsidiaryId,
                        buyerName = mRspQueryApplyList.title,
                        addIds = mRspQueryCollectInfo?.addIds,
                        delIds = mRspQueryCollectInfo?.delIds,
                        feeInfoId = feeInfoId,
                        invoiceType = invoiceType,
                        otherRemark = editWithCountView.getContent(),
                        ruleId = ruleId,
                        taxName = taxName,
                        ruleSource = ruleSource,
                        salelistFlag = mReqAddInvoiceRule?.salelistFlag,
                        invoiceSplitFlag = mReqAddInvoiceRule?.invoiceSplitFlag,
                        invoiceSplitCollect = mReqAddInvoiceRule?.invoiceSplitCollect,
                        branchSplitFlag = mReqAddInvoiceRule?.branchSplitFlag,
                        branchSplitCollect = mReqAddInvoiceRule?.branchSplitCollect,
                        specifyMode = mReqAddInvoiceRule?.specifyMode,
                        invoiceRemark = mReqAddInvoiceRule?.invoiceRemark,
                    )
                ) { rsp ->
                    activity?.runOnUiThread {
                        if (rsp.success()) {
                            val url = HttpConfig.getWebUrl() + "/form_h5/h5_inner/index.html?_t=${Calendar.getInstance().timeInMillis}#/invoicePreview?applyId=${rsp.data?.applyId}&feeType=${mRspQueryApplyList.feeType}&invoiceType=${invoiceType}"
                            InvoiceWebActivity.jumpPage(
                                context = <EMAIL>,
                                url = url,
                                applyId = rsp.data?.applyId,
                            )
                        } else {
                            val dialogBuilder = DialogBuilder()
                            dialogBuilder.title = "提示"
                            dialogBuilder.message = rsp.msg
                            dialogBuilder.isHideCancel = true
                            dialogBuilder.setOKText("知道了")
                            showDialog(dialogBuilder)
                        }
                    }
                }
            }

            R.id.ivInvoiceLeft -> {
                ivInvoiceLeft.isSelected = !ivInvoiceLeft.isSelected
                ivInvoiceRight.isSelected = false
                ivInvoiceCustomize.isSelected = false
                mRspQueryDefaultInvoiceRuleInfo = ruleInfoList.find { ob -> ob.defaultAutoCheckFlag.isTrue }
                initLeftRule()
                inputView3.setVisible(true)
                tvName2.setVisible(true)
                viewLayout1.setVisible(true)
                invoiceCustomizeView.setVisible(false)
            }

            R.id.ivInvoiceRight -> {
                ivInvoiceRight.isSelected = !ivInvoiceRight.isSelected
                ivInvoiceLeft.isSelected = false
                ivInvoiceCustomize.isSelected = false
                mRspQueryDefaultInvoiceRuleInfo = mRspQueryDefaultInvoiceRuleInfoTemp
                initRightRule()
                inputView3.setVisible(false)
                tvName2.setVisible(false)
                viewLayout1.setVisible(true)
                invoiceCustomizeView.setVisible(false)
            }

            R.id.ivInvoiceCustomize -> {
                ivInvoiceCustomize.isSelected = !ivInvoiceCustomize.isSelected
                ivInvoiceRight.isSelected = false
                ivInvoiceLeft.isSelected = false
                inputView3.setVisible(false)
                viewLayout1.setVisible(false)
                invoiceCustomizeView.setVisible(true)
            }
        }
    }

    @LiveDataMatch
    open fun onInitDataSuccess(data: RspCommData) {
        //发票类型
        val find1 = data.data1?.find { ob -> TextUtils.equals(ob.invoiceType, "6") }
        inputView1.setLeftVisible(find1.isNotNull)
        val find2 = data.data1?.find { ob -> TextUtils.equals(ob.invoiceType, "7") }
        inputView1.setRightVisible(find2.isNotNull)
        //发票项目
        taxArr = data.data2 ?: mutableListOf()
        if (taxArr.size >= 1) {
            inputView2.content = taxArr[0].taxName ?: ""
            inputView2.tag = taxArr[0]
        }
        //开票要求-常用开票格式
        ruleInfoList = data.data3 ?: mutableListOf()
        //开票要求-无特殊要求
        mRspQueryDefaultInvoiceRuleInfoTemp = data.data4
        ivInvoiceLeft.setVisible(ruleInfoList.isNotEmpty())
        inputView3.setVisible(ruleInfoList.isNotEmpty())
        tvName2.setVisible(ruleInfoList.isNotEmpty())
        if (ruleInfoList.isNotEmpty()) {
            mRspQueryDefaultInvoiceRuleInfo = ruleInfoList.find { ob -> ob.defaultAutoCheckFlag.isTrue }
            if (mRspQueryDefaultInvoiceRuleInfo.isNull) {
                mRspQueryDefaultInvoiceRuleInfo = ruleInfoList[0]
            }
            initLeftRule()
        } else {
            //开票要求-无特殊要求
            mRspQueryDefaultInvoiceRuleInfo = data.data4
            ivInvoiceRight.isSelected = true
            initRightRule()
        }
    }

    private fun initRightRule() {
        val name = when (mRspQueryDefaultInvoiceRuleInfo?.salelistFlag) {
            "1" -> {
                "明细"
            }

            else -> {
                "汇总"
            }
        }
        tvName1.text = "发票内容：${name}"
        tvName3.text = "规格型号：${mRspQueryDefaultInvoiceRuleInfo?.specifyText}"
        tvName4.text = "发票备注：${mRspQueryDefaultInvoiceRuleInfo?.invoiceRemarkText}"
    }

    private fun initLeftRule() {
        if (mRspQueryDefaultInvoiceRuleInfo.isNotNull) {
            ivInvoiceLeft.isSelected = true
            ivInvoiceRight.isSelected = false
            inputView3.content = mRspQueryDefaultInvoiceRuleInfo?.ruleName ?: ""
            inputView3.tag = mRspQueryDefaultInvoiceRuleInfo
            val name = when (mRspQueryDefaultInvoiceRuleInfo?.salelistFlag) {
                "1" -> {
                    "明细"
                }

                else -> {
                    "汇总"
                }
            }
            tvName1.text = "发票内容：${name}"
            tvName2.text = "分票要求：${mRspQueryDefaultInvoiceRuleInfo?.invoiceSplitText}"
            tvName3.text = "规格型号：${mRspQueryDefaultInvoiceRuleInfo?.specifyText}"
            tvName4.text = "发票备注：${mRspQueryDefaultInvoiceRuleInfo?.invoiceRemarkText}"
        }
    }
}