package com.zczy.cargo_owner.invoice.req

import com.zczy.comm.http.entity.BaseNewRequest
import com.zczy.comm.http.entity.BaseRsp
import com.zczy.comm.http.entity.ResultData

/**
 *  desc: 开票申请
 *  user: 宋双朋
 *  time: 2024/9/14 14:36
 */
class ReqQueryCollectInfo(
    var keyIds: String? = null, //	待申请Id拼接（逗号拼接） 非全选情况 必填
    var allCheckFlag: String? = null, //	是否全部全选 0-否 1-是
    var queryType: String? = null, //	1-可申请 2-其他运单
    var subsidiaryId: String? = null, //		开票方
    var buyerName: String? = null, //		受票方
    val businessType: String = "1", //业务类型，传1		1	是
    var feeType: String? = null, //		费用类型	
    var settleType: String? = null, //		结算方式 1 现结、2 账期、3 周期 、4 短周期
    var orderNumber: String? = null, //		关联单号
    var waybillNumbers: String? = null, //		运单号
    var plateNumber: String? = null, //		车牌号
    var orderTitle: String? = null, //		货运信息标题
    var despatchCompany: String? = null, //		发货单位
    var deliverCompany: String? = null, //		收货单位
    var startPlace: String? = null, //		启运地
    var endPlace: String? = null, //		目的地
    var settleTimeS: String? = null, //		结算时间 开始
    var settleTimeE: String? = null, //		结算时间 结束
    var orderPublicTimeS: String? = null, //发单时间 开始
    var orderPublicTimeE: String? = null, //发单时间 结束
    var cargoName: String? = null, //		货物名称
    var remark3: String? = null, //批量货单号
    var delistTimeS: String? = null, //成交时间开始
    var delistTimeE: String? = null, //成交时间结束
    var startTimeS: String? = null, //发货时间开始
    var startTimeE: String? = null, //发货时间结束
    var endTimeS: String? = null, //收货时间开始
    var endTimeE: String? = null, //收货时间结束
    var billingFlag: String? = null, //		是否达到开票条件 0：否；1：是	
    var billNo: String? = null, //		对账单号
    var accountPeriod: String? = null, //		账期
    var billCheckTimeS: String? = null, //		对账时间
    var billCheckTimeE: String? = null, //		对账时间
    var sort: String = "settleTime", //排序字段名 发单时间：orderPublishTime 确认发货时间：startTime  确认收货时间：endTime 结算时间：settleTime(默认)
    var order: String = "desc", //	排序方式 升序：asc降序：desc（默认）
    var orderPublishTime: String? = null, //发单时间
    var startTime: String? = null, //确认发货时间
    var endTime: String? = null, //确认收货时间
    var settleTime: String? = null, //	结算时间
) : BaseNewRequest<BaseRsp<RspQueryCollectInfo>>("ams-app/ams/app/invoice/manage/queryCollectInfo")

fun initReqQueryCollectInfo(data: ReqQueryCanApplyOrderList?): ReqQueryCollectInfo {
    val req = ReqQueryCollectInfo()
    data?.apply {
        req.queryType = queryType
        req.subsidiaryId = subsidiaryId
        req.buyerName = actualPerson
        req.feeType = feeType
        req.settleType = settleType
        req.orderNumber = orderNumber
        req.waybillNumbers = waybillNumbers
        req.plateNumber = plateNumber
        req.orderTitle = orderTitle
        req.despatchCompany = despatchCompany
        req.deliverCompany = deliverCompany
        req.startPlace = startPlace
        req.endPlace = endPlace
        req.settleTimeS = settleTimeS
        req.settleTimeE = settleTimeE
        req.orderPublicTimeS = orderPublicTimeS
        req.orderPublicTimeE = orderPublicTimeE
        req.cargoName = cargoName
        req.remark3 = remark3
        req.delistTimeS = delistTimeS
        req.delistTimeE = delistTimeE
        req.startTimeS = startTimeS
        req.startTimeE = startTimeE
        req.endTimeS = endTimeS
        req.endTimeE = endTimeE
        req.billingFlag = billingFlag
        req.billNo = billNo
        req.accountPeriod = accountPeriod
        req.billCheckTimeS = billCheckTimeS
        req.billCheckTimeE = billCheckTimeE
        req.sort = sort
        req.order = order
        req.orderPublishTime = orderPublishTime
        req.startTime = startTime
        req.endTime = endTime
        req.settleTime = settleTime
    }
    return req
}

fun initReqQueryCollectInfo(data: ReqQueryEditApplyOrderList?): ReqQueryCollectInfoForEdit {
    val req = ReqQueryCollectInfoForEdit()
    data?.apply {
        req.queryType = queryType
        req.applyId = applyId
        req.subsidiaryId = subsidiaryId
        req.buyerName = actualPerson
        req.feeType = feeType
        req.settleType = settleType
        req.orderNumber = orderNumber
        req.waybillNumbers = waybillNumbers
        req.plateNumber = plateNumber
        req.orderTitle = orderTitle
        req.despatchCompany = despatchCompany
        req.deliverCompany = deliverCompany
        req.startPlace = startPlace
        req.endPlace = endPlace
        req.settleTimeS = settleTimeS
        req.settleTimeE = settleTimeE
        req.orderPublicTimeS = orderPublicTimeS
        req.orderPublicTimeE = orderPublicTimeE
        req.cargoName = cargoName
        req.remark3 = remark3
        req.delistTimeS = delistTimeS
        req.delistTimeE = delistTimeE
        req.startTimeS = startTimeS
        req.startTimeE = startTimeE
        req.endTimeS = endTimeS
        req.endTimeE = endTimeE
        req.billingFlag = billingFlag
        req.billNo = billNo
        req.accountPeriod = accountPeriod
        req.billCheckTimeS = billCheckTimeS
        req.billCheckTimeE = billCheckTimeE
        req.sort = sort
        req.order = order
        req.orderPublishTime = orderPublishTime
        req.startTime = startTime
        req.endTime = endTime
        req.settleTime = settleTime
    }
    return req
}

class RspQueryCollectInfo(
    val totalNum: String? = null, //合计条数
    val totalInvoiceMoney: String? = null, //	含税金额合计
    val totalInvoiceMoneyStr: String? = null, //	含税金额合计 千分位格式
    val weight: String? = null, //	货品数量
    val negativeNum: String? = null, //	负数条数
    val negativeInvoiceMoney: String? = null, //	负数含税金额合计
    val negativeInvoiceMoneyStr: String? = null, //	负数含税金额合计 千分位格式
    val keyIds: String? = null, //	待申请Id拼接（逗号拼接），用于在全部全选情况下给预览传参
    val addIds: String? = null, //	新增申请Id拼接（英文逗号拼接），用于给重新预览、重新申请接口传参，注意重新预览、重新申请接口不取页面勾选，取这里数据
    val delIds: String? = null, //	删除申请Id拼接（英文逗号拼接），用于给重新预览、重新申请接口传参，注意重新预览、重新申请接口不取页面勾选，取这里数据
) : ResultData()

