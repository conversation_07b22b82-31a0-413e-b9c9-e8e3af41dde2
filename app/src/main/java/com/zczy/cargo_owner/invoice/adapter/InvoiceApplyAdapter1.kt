package com.zczy.cargo_owner.invoice.adapter

import com.chad.library.adapter.base.BaseQuickAdapter
import com.chad.library.adapter.base.BaseViewHolder
import com.zczy.cargo_owner.R
import com.zczy.cargo_owner.invoice.req.RspQueryCanApplyInvoice

/**
 *  desc: 开票申请-第1步
 *  user: 宋双朋
 *  time: 2024/9/14 9:58
 */
class InvoiceApplyAdapter1 : BaseQuickAdapter<RspQueryCanApplyInvoice, BaseViewHolder>(R.layout.invoice_apply_item_1) {
    override fun convert(helper: BaseViewHolder, item: RspQueryCanApplyInvoice) {
        helper.setText(R.id.tvName, item.feeName)
    }
}