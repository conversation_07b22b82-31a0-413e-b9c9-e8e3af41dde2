package com.zczy.cargo_owner.invoice.fragment

import android.os.Bundle
import android.view.View
import com.sfh.lib.mvvm.service.BaseViewModel
import com.zczy.cargo_owner.R
import com.zczy.cargo_owner.invoice.model.InvoiceModel
import com.zczy.cargo_owner.invoice.req.ReqAddMailAddressInfo
import com.zczy.cargo_owner.invoice.req.ReqModifyMailAddressInfo
import com.zczy.cargo_owner.invoice.req.ReqQueryCustomerInfoDetail
import com.zczy.cargo_owner.invoice.req.RspQueryCustomerInfoDetail
import com.zczy.cargo_owner.order.confirm.bean.isNotNullOrNotEmpty
import com.zczy.comm.ui.BaseFragment
import com.zczy.comm.utils.getResDrawable
import com.zczy.comm.utils.toJsonObject
import kotlinx.android.synthetic.main.invoice_material_fragment_v1.inputView1_4
import kotlinx.android.synthetic.main.invoice_material_fragment_v2.inputView2_1
import kotlinx.android.synthetic.main.invoice_material_fragment_v2.inputView2_2
import kotlinx.android.synthetic.main.invoice_material_fragment_v2.inputView2_3
import kotlinx.android.synthetic.main.invoice_material_fragment_v2.tvSave2

/**
 *  desc: 开票资料-收件信息
 *  user: 宋双朋
 *  time: 2024/9/14 9:48
 */
class InvoiceMaterialFragmentV2 : BaseFragment<BaseViewModel>() {
    private var onNextStep: (showWarning: Boolean) -> Unit = {}
    private var mRspQueryCustomerInfoDetail: RspQueryCustomerInfoDetail? = null

    companion object {
        private const val EXTRA_DATA = "extraData"

        fun newInstance(extraData: String?): InvoiceMaterialFragmentV2 {
            val fragment = InvoiceMaterialFragmentV2()
            val bundle = Bundle()
            bundle.putString(EXTRA_DATA, extraData)
            fragment.arguments = bundle
            return fragment
        }
    }

    override fun getLayout(): Int {
        return R.layout.invoice_material_fragment_v2
    }

    override fun initData() {

    }

    override fun bindView(view: View, bundle: Bundle?) {
        mRspQueryCustomerInfoDetail = arguments?.getString(EXTRA_DATA)?.toJsonObject(RspQueryCustomerInfoDetail::class.java)
        initView()
        bindClickEvent(tvSave2)
    }

    private fun initView() {
        mRspQueryCustomerInfoDetail?.let {
            inputView2_1.content = it.checkUsername ?: ""
            if (it.checkUsername.isNullOrEmpty()) {
                inputView2_1.setWarning(it.checkUsername.isNullOrEmpty())
                inputView2_1.setTitleLeftImg(getResDrawable(R.drawable.base_warning))
            }
            inputView2_2.content = it.checkMobile ?: ""
            if (it.checkMobile.isNullOrEmpty()) {
                inputView2_2.setWarning(it.checkMobile.isNullOrEmpty())
                inputView2_2.setTitleLeftImg(getResDrawable(R.drawable.base_warning))
            }
            inputView2_3.content = it.mailAddress ?: ""
            if (it.mailAddress.isNullOrEmpty()) {
                inputView2_3.setWarning(it.mailAddress.isNullOrEmpty())
                inputView2_3.setTitleLeftImg(getResDrawable(R.drawable.base_warning))
            }
            onNextStep(it.checkUsername.isNullOrEmpty() || it.checkMobile.isNullOrEmpty() || it.mailAddress.isNullOrEmpty())
        }
    }

    fun setOnNextStep(onBlock: (showWarning: Boolean) -> Unit) {
        this.onNextStep = onBlock
    }

    override fun onSingleClick(v: View) {
        super.onSingleClick(v)
        when (v.id) {
            R.id.tvSave2 -> {
                if (mRspQueryCustomerInfoDetail?.mailId.isNotNullOrNotEmpty()) {
                    modify()
                } else {
                    add()
                }
            }
        }
    }

    private fun add() {
        val req = ReqAddMailAddressInfo()
        val checkUsername = inputView2_1.content
        if (checkUsername.isEmpty()) {
            showToast("请填写联系人！")
            return
        }
        req.checkUsername = checkUsername
        val checkMobile = inputView2_2.content
        if (checkMobile.isEmpty()) {
            showToast("请填写联系方式！")
            return
        }
        req.checkMobile = checkMobile
        val mailAddress = inputView2_3.content
        if (mailAddress.isEmpty()) {
            showToast("请填写邮箱地址！")
            return
        }
        req.mailAddress = mailAddress
        req.buyerList = mutableListOf(mRspQueryCustomerInfoDetail?.companyName ?: "")
        getViewModel(InvoiceModel::class.java).execute(req) {
            if (it.success()) {
                showToast(it.msg)
                refreshView()
            } else {
                showToast(it.msg)
            }
        }
    }

    private fun modify() {
        val req = ReqModifyMailAddressInfo()
        req.id = mRspQueryCustomerInfoDetail?.mailId
        val checkUsername = inputView2_1.content
        if (checkUsername.isEmpty()) {
            showToast("请填写联系人！")
            return
        }
        req.checkUsername = checkUsername
        val checkMobile = inputView2_2.content
        if (checkMobile.isEmpty()) {
            showToast("请填写联系方式！")
            return
        }
        req.checkMobile = checkMobile
        val mailAddress = inputView2_3.content
        if (mailAddress.isEmpty()) {
            showToast("请填写邮箱地址！")
            return
        }
        req.mailAddress = mailAddress
        req.mailName = mRspQueryCustomerInfoDetail?.mailName
        getViewModel(InvoiceModel::class.java).execute(req) {
            if (it.success()) {
                showToast(it.msg)
                refreshView()
            } else {
                showToast(it.msg)
            }
        }
    }

    private fun refreshView() {
        getViewModel(InvoiceModel::class.java).execute(ReqQueryCustomerInfoDetail()) {
            if (it.success()) {
                activity?.runOnUiThread {
                    mRspQueryCustomerInfoDetail = it.data
                    initView()
                }
            } else {
                showToast(it.msg)
            }
        }
    }

}