package com.zczy.cargo_owner.invoice.req

import com.zczy.comm.http.entity.BaseNewRequest
import com.zczy.comm.http.entity.BaseRsp
import com.zczy.comm.http.entity.PageList

/**
 *  desc: 申请记录
 *  user: 宋双朋
 *  time: 2024/9/14 14:36
 */
class ReqQueryApplyList(
    var appApplyState: Int = 1, // 1：待审核；2：待开；3：已驳回/已撤回；4：已开
    var nowPage: Int = 1, // 当前页
    var pageSize: Int = 10, // 每页个数
    var createTimeS: String? = null, // 开始时间
    var createTimeE: String? = null, // 结束时间
    var cargoNameExact: String? = null, // 货物名称
    val businessType: String = "1",// 业务类型，汽运传1
) : BaseNewRequest<BaseRsp<PageList<RspQueryApplyList>>>("ams-app/ams/app/invoice/manage/queryApplyList")

class RspQueryApplyList(
    val id: String? = null, // 	申请表 Id
    val applyId: String? = null, // 业务id
    val serialNumber: String? = null, // 	申请流水号（例如FPSQ202405271450552010）
    val invoiceTypeStr: String? = null, // 	发票类型
    val feeName: String? = null, // 费用类型描述
    val oldFlow: String? = null, //
    val feeType: String? = null, //
    val applyState: String? = null, //
    val createTime: String? = null, // 	申请时间
    val tax: String? = null, // 	税率
    val invoiceMoney: String? = null, // 	可开票金额、申请金额
    val invoiceMoneyMico: String? = null, //  可开票金额、申请金额千分位格式
    val orderNum: String? = null, // 运单数、账单数
    val weight: String? = null, // 	数量
    val subsidiaryName: String? = null, // 开票方
    val subsidiaryId: String? = null, // 开票id
    val title: String? = null, // 	受票方
    val applyCheckRemark: String? = null, // 	驳回原因
)