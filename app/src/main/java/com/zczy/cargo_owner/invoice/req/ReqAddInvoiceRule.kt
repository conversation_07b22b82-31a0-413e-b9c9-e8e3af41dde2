package com.zczy.cargo_owner.invoice.req

import com.zczy.comm.http.entity.BaseNewRequest
import com.zczy.comm.http.entity.BaseRsp
import com.zczy.comm.http.entity.ResultData

/**
 *  desc: 保存常用开票规则
 *  user: 宋双朋
 *  time: 2025/1/23 16:50
 */
class ReqAddInvoiceRule(
    val businessType: String = "1",// 业务类型，汽运传1
    val defaultFlag: String = "0",// 	是否默认规则 0 否 1 是 固定传0 即可
    val itemListFlag: String = "0",// 是否要求清单附件 0 否 1 是 固定传0 即可
    var feeType: String? = null,// 费用类型
    var salelistFlag: String? = null,// 发票内容 0 汇总 1 明细
    var invoiceSplitFlag: String? = null,// 分票要求 0 否 1 是
    var invoiceSplitCollect: String? = null,// 分票规则 根据勾选项编码以逗号拼接， 编码见下方枚举 InvoiceSplit
    var branchSplitFlag: String? = null,// 分行要求 0 否 1 是
    var branchSplitCollect: String? = null,// 分行规则 根据勾选项编码以逗号拼接， 编码见下方枚举 InvoiceSplit
    var specifyMode: String = "",// 规格型号 空值即空串 勾选项为分票、分行规则时，与 invoiceSplitCollect、branchSplitCollect 传值一致 勾选项非分票、分行规则时， 根据勾选项编码以逗号拼接， 编码见下方枚举 InvoiceSplit
    var invoiceRemark: String? = null,// 发票备注 根据勾选项编码以逗号拼接， 编码见下方枚举 InvoiceRemark
    var ruleName: String? = null,// 规则名称
) : BaseNewRequest<BaseRsp<ResultData>>("ams-app/ams/app/invoice/manage/addInvoiceRule")

class InvoiceSplit(
    var invoiceSplitCode: String = "",//编码
    var invoiceSplitName: String = "",//名称
    var invoiceSplitEnumerateName: String = "",//枚举名称
)

fun initInvoiceSplit(chooseList: MutableList<InvoiceSplit>): MutableList<InvoiceSplit> {
    val list = mutableListOf<InvoiceSplit>()
    list.addAll(chooseList)
    list.addAll(mutableListOf(InvoiceSplit(invoiceSplitCode = "1001", invoiceSplitName = "货物名称", invoiceSplitEnumerateName = "CARGE_NAME")).filter { ob ->
        list.find { sd -> ob.invoiceSplitCode == sd.invoiceSplitCode } == null
    })
    list.addAll(mutableListOf(InvoiceSplit(invoiceSplitCode = "1002", invoiceSplitName = "起始地", invoiceSplitEnumerateName = "START_CITY")).filter { ob ->
        list.find { sd -> ob.invoiceSplitCode == sd.invoiceSplitCode } == null
    })
    list.addAll(mutableListOf(InvoiceSplit(invoiceSplitCode = "1003", invoiceSplitName = "目的地", invoiceSplitEnumerateName = "END_CITY")).filter { ob ->
        list.find { sd -> ob.invoiceSplitCode == sd.invoiceSplitCode } == null
    })
    list.addAll(mutableListOf(InvoiceSplit(invoiceSplitCode = "1004", invoiceSplitName = "发货单位", invoiceSplitEnumerateName = "DESPATCH_COMPANY")).filter { ob ->
        list.find { sd -> ob.invoiceSplitCode == sd.invoiceSplitCode } == null
    })
    list.addAll(mutableListOf(InvoiceSplit(invoiceSplitCode = "1005", invoiceSplitName = "收货单位", invoiceSplitEnumerateName = "DELIVER_COMPANY")).filter { ob ->
        list.find { sd -> ob.invoiceSplitCode == sd.invoiceSplitCode } == null
    })
    return list
}

fun initInvoiceRemark(): MutableList<InvoiceSplit> {
    val list = mutableListOf<InvoiceSplit>()
    list.addAll(initInvoiceDefaultRemark())
    val list1 = mutableListOf(
        InvoiceSplit(invoiceSplitCode = "1004", invoiceSplitName = "发货单位", invoiceSplitEnumerateName = "DESPATCH_COMPANY"),
        InvoiceSplit(invoiceSplitCode = "1005", invoiceSplitName = "收货单位", invoiceSplitEnumerateName = "DELIVER_COMPANY"),
        InvoiceSplit(invoiceSplitCode = "3001", invoiceSplitName = "运距", invoiceSplitEnumerateName = "DISTANCE"),
        InvoiceSplit(invoiceSplitCode = "1007", invoiceSplitName = "合计数量", invoiceSplitEnumerateName = "TOTAL_WEIGHT")
    )
    list.addAll(list1)
    return list
}

fun initInvoiceDefaultRemark(): MutableList<InvoiceSplit> {
    val list = mutableListOf(
        InvoiceSplit(invoiceSplitCode = "1001", invoiceSplitName = "货物名称", invoiceSplitEnumerateName = "CARGE_NAME"),
        InvoiceSplit(invoiceSplitCode = "1006", invoiceSplitName = "起止地", invoiceSplitEnumerateName = "START_END_CITY"),
        InvoiceSplit(invoiceSplitCode = "2001", invoiceSplitName = "车牌号", invoiceSplitEnumerateName = "PLATE_NUMBER"),
        InvoiceSplit(invoiceSplitCode = "2002", invoiceSplitName = "车型", invoiceSplitEnumerateName = "VEHICLE_TYPE")
    )
    return list
}
