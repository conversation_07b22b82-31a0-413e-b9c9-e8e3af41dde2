package com.zczy.cargo_owner.invoice.activity

import android.content.Context
import android.content.Intent
import android.os.Bundle
import android.view.View
import com.sfh.lib.mvvm.service.BaseViewModel
import com.sfh.lib.rx.IResultSuccessNoFail
import com.sfh.lib.ui.dialog.DialogBuilder
import com.zczy.cargo_owner.R
import com.zczy.cargo_owner.deliver.addorder.container.BaseActivityV1
import com.zczy.cargo_owner.invoice.model.InvoiceModel
import com.zczy.cargo_owner.invoice.req.ReqCheckApplyPermission
import com.zczy.cargo_owner.invoice.req.ReqCheckPackQueryPermission
import com.zczy.cargo_owner.invoice.req.ReqGetNPSQuestionnaire
import com.zczy.cargo_owner.invoice.req.ReqQueryCustomerInfoDetail
import com.zczy.cargo_owner.invoice.req.ReqQueryPermission
import com.zczy.cargo_owner.invoice.req.RspNPSQuestionnaire
import com.zczy.cargo_owner.invoice.req.RspQueryCustomerInfoDetail
import com.zczy.comm.CommServer
import com.zczy.comm.http.entity.BaseRsp
import com.zczy.comm.pluginserver.AMainServer
import com.zczy.comm.utils.ex.isTrue
import com.zczy.comm.utils.ex.setVisible
import com.zczy.comm.utils.toJson
import kotlinx.android.synthetic.main.invoice_center_activty.*

/**
 *  desc: 发票中心
 *  user: 宋双朋
 *  time: 2024/9/14 8:40
 */
class InvoiceCenterActivity : BaseActivityV1<BaseViewModel>() {

    private var mRspQueryCustomerInfoDetail: RspQueryCustomerInfoDetail? = null

    companion object {
        @JvmStatic
        fun jumpPage(context: Context?) {
            val intent = Intent(context, InvoiceCenterActivity::class.java)
            context?.startActivity(intent)
        }
    }

    override val layout: Int
        get() = R.layout.invoice_center_activty

    override fun bindView(bundle: Bundle?) {
        bindClickEvent(view1)
        bindClickEvent(view2)
        bindClickEvent(view3)
        bindClickEvent(view4)
        bindClickEvent(view5)
        bindClickEvent(view6)
        checkPermissionForView6()
    }

    override fun onSingleClick(v: View) {
        super.onSingleClick(v)
        when (v.id) {
            R.id.view1 -> {
                //开票资料
                InvoiceMaterialActivity.jumpPage(
                    context = this@InvoiceCenterActivity,
                    extraData = mRspQueryCustomerInfoDetail.toJson()
                )
            }

            R.id.view2 -> {
                //开票申请
                getViewModel(InvoiceModel::class.java).execute(ReqCheckApplyPermission()) {
                    runOnUiThread {
                        if (it.success()) {
                            if (mRspQueryCustomerInfoDetail?.isPerfertFlag.isTrue) {
                                InvoiceApplyActivity.jumpPage(context = this@InvoiceCenterActivity)
                            } else {
                                val dialogBuilder = DialogBuilder()
                                dialogBuilder.title = "提示"
                                dialogBuilder.message = "请先完善开票资料"
                                dialogBuilder.isHideCancel = true
                                dialogBuilder.setOKText("去完善")
                                dialogBuilder.setOkListener { dialog, _ ->
                                    dialog.dismiss()
                                    InvoiceMaterialActivity.jumpPage(
                                        context = this@InvoiceCenterActivity,
                                        extraData = mRspQueryCustomerInfoDetail.toJson()
                                    )
                                }
                                showDialog(dialogBuilder)
                            }
                        } else {
                            val dialog = DialogBuilder()
                            dialog.title = "提示"
                            dialog.message = it.msg
                            dialog.isHideCancel = true
                            dialog.setOKText("确定")
                            showDialog(dialog)
                        }
                    }
                }
            }

            R.id.view3 -> {
                //申请记录
                InvoiceRecordActivity.jumpPage(context = this@InvoiceCenterActivity)
            }

            R.id.view4 -> {
                //发票管家
                getViewModel(InvoiceModel::class.java).execute(ReqCheckPackQueryPermission()) {
                    runOnUiThread {
                        if (it.success()) {
                            InvoiceManagerActivity.jumpPage(context = this@InvoiceCenterActivity)
                        } else {
                            val dialog = DialogBuilder()
                            dialog.title = "提示"
                            dialog.message = it.msg
                            dialog.isHideCancel = true
                            dialog.setOKText("确定")
                            showDialog(dialog)
                        }
                    }
                }
            }

            R.id.view5 -> {
                //问卷调查
                InvoiceNpsActivity.jumpPage(context = this@InvoiceCenterActivity)
            }

            R.id.view6 -> {

                AMainServer.getPluginServer().openRNActivity(this, "IssueSpecialTicketPage")
            }

        }
    }

    override fun initData() {
    }

    override fun onResume() {
        super.onResume()
        getViewModel(InvoiceModel::class.java).execute(ReqQueryCustomerInfoDetail()) {
            if (it.success()) {
                runOnUiThread {
                    mRspQueryCustomerInfoDetail = it.data
                    tvState.setVisible(!mRspQueryCustomerInfoDetail?.isPerfertFlag.isTrue)
                }
            }
        }

        initNpsData()
        checkPermissionForView6()
    }

    private fun checkPermissionForView6() {
        getViewModel(InvoiceModel::class.java).execute(ReqQueryPermission(
            userId = CommServer.getUserServer()?.login?.userId
        )) { // 假设ReqCheckSomePermission是你的权限检查请求
            runOnUiThread {
                if (it.success() && it.data?.proxySettleRate == true) {
                    view6.setVisible(true)
                } else {
                    view6.setVisible(false)
                }
            }
        }
    }

    private fun initNpsData() {
        view5.setVisible(false)
        this.getViewModel(BaseViewModel::class.java).execute(
            ReqGetNPSQuestionnaire(),
            object : IResultSuccessNoFail<BaseRsp<RspNPSQuestionnaire>> {
                @Throws(Exception::class)
                override fun onSuccess(rspNPSQuestionnaireBaseRsp: BaseRsp<RspNPSQuestionnaire>) {
                    if (rspNPSQuestionnaireBaseRsp.success() && rspNPSQuestionnaireBaseRsp.data != null) {
                        view5.setVisible(true)
                    }
                }
            })
    }
}