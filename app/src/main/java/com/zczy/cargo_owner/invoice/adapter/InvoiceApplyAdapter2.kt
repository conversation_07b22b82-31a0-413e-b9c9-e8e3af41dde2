package com.zczy.cargo_owner.invoice.adapter

import android.annotation.SuppressLint
import android.text.TextUtils
import android.widget.ImageView
import com.chad.library.adapter.base.BaseQuickAdapter
import com.chad.library.adapter.base.BaseViewHolder
import com.zczy.cargo_owner.R
import com.zczy.cargo_owner.invoice.fragment.InvoiceRecordFragment.Companion.TAB_TYPE_1
import com.zczy.cargo_owner.invoice.fragment.InvoiceRecordFragment.Companion.TAB_TYPE_3
import com.zczy.cargo_owner.invoice.req.RspQueryApplyList
import com.zczy.cargo_owner.invoice.req.RspQueryCanApplyInvoice
import com.zczy.cargo_owner.invoice.req.RspQuerySubsidiaryWithOrderToApply
import com.zczy.cargo_owner.order.confirm.bean.isNotNullOrNotEmpty
import com.zczy.comm.utils.ex.isNotNull
import com.zczy.comm.utils.ex.isNull

/**
 *  desc: 开票申请-第2步
 *  user: 宋双朋
 *  time: 2024/9/14 9:58
 */
class InvoiceApplyAdapter2 : BaseQuickAdapter<RspQuerySubsidiaryWithOrderToApply, BaseViewHolder>(R.layout.invoice_apply_item_2) {
    var checkItem: RspQuerySubsidiaryWithOrderToApply = RspQuerySubsidiaryWithOrderToApply()
    override fun convert(helper: BaseViewHolder, item: RspQuerySubsidiaryWithOrderToApply) {
        helper.setText(R.id.tvName, item.subsidiaryName)
        helper.getView<ImageView>(R.id.ivCheck).isSelected = TextUtils.equals(item.subsidiaryId, checkItem.subsidiaryId)
    }

    @SuppressLint("NotifyDataSetChanged")
    fun setCheckItem(position: Int) {
        checkItem = mData[position]
        notifyDataSetChanged()
    }
}