package com.zczy.cargo_owner.invoice.wight;

import android.content.Context;
import android.content.res.TypedArray;
import android.text.TextUtils;
import android.util.AttributeSet;
import android.widget.ImageView;
import android.widget.LinearLayout;
import android.widget.TextView;

import androidx.annotation.NonNull;
import androidx.annotation.Nullable;

import com.zczy.cargo_owner.R;
import com.zczy.comm.utils.ex.ViewUtil;
import com.zczy.comm.widget.inputv2.BaseInputView;
import com.zczy.comm.widget.inputv2.BaseListener;

/**
 * ps: 单选操作 点了后不是自动选中，有逻辑操作 Created by sdx on 2019/02/01
 */
public class InputViewCheckV6 extends BaseInputView<InputViewCheckV6.Listener> {

    public static final int LEFT = 1;
    public static final int RIGHT = 2;
    public static final int NONE = 3;

    private ImageView mImgLeft;
    private ImageView mImgRight;

    private TextView mTvLeft;
    private TextView mTvRight;
    private LinearLayout view_rb_1;
    private LinearLayout view_rb_2;

    private String mLeftStr;
    private String mRightStr;

    private boolean mLeftDef;
    private boolean mRightDef;

    /**
     * 能否再次点击
     */
    private boolean reClickFlag = false;

    private int mCheck = NONE;
    /**
     * 是否可以点击切换
     */
    private boolean canClick = true;

    public InputViewCheckV6(Context context) {
        super(context);
    }

    public InputViewCheckV6(Context context, @Nullable AttributeSet attrs) {
        super(context, attrs);
    }

    public InputViewCheckV6(Context context, @Nullable AttributeSet attrs, int defStyleAttr) {
        super(context, attrs, defStyleAttr);
    }

    @Override
    public int getInflateLayout() {
        return R.layout.base_ui_input_view_6_check_v6;
    }

    @Override
    public void initAttrs(@Nullable AttributeSet attrs) {
        if (attrs != null) {
            TypedArray a = getContext().obtainStyledAttributes(attrs, R.styleable.InputViewCheckV2);
            String leftStr = a.getString(R.styleable.InputViewCheckV2_input_check_left_str);
            String rightStr = a.getString(R.styleable.InputViewCheckV2_input_check_right_str);
            mLeftDef = a.getBoolean(R.styleable.InputViewCheckV2_input_check_left_def, false);
            mRightDef = a.getBoolean(R.styleable.InputViewCheckV2_input_check_right_def, false);
            a.recycle();
            if (!TextUtils.isEmpty(leftStr)) {
                mLeftStr = leftStr;
            } else {
                mLeftStr = "选项1";
            }
            if (!TextUtils.isEmpty(rightStr)) {
                mRightStr = rightStr;
            } else {
                mRightStr = "选项2";
            }
        }
    }

    @Override
    protected void bindView() {
        initContent();
    }

    @Override
    protected void initData() {
        mTvLeft.setText(mLeftStr);
        mTvRight.setText(mRightStr);
        if (mLeftDef) {
            mImgLeft.setSelected(true);
            mCheck = LEFT;
        } else if (mRightDef) {
            mImgRight.setSelected(true);
            mCheck = RIGHT;
        }
    }

    private void initContent() {
        view_rb_1 = findViewById(R.id.view_rb_1);
        view_rb_2 = findViewById(R.id.view_rb_2);
        mImgLeft = findViewById(R.id.img_rb_1);
        mImgRight = findViewById(R.id.img_rb_2);

        mTvLeft = findViewById(R.id.rb_1);
        mTvRight = findViewById(R.id.rb_2);

        findViewById(R.id.view_rb_1).setOnClickListener(v -> {
            if (!reClickFlag && mCheck == LEFT) {
                return;
            }
            if (!canClick) {
                return;
            }
            if (mListener != null) {
                boolean b = mListener.onClickCheck(getId(), InputViewCheckV6.this, LEFT, mLeftStr);
                if (b) {
                    setCheck(LEFT);
                }
            } else {
                setCheck(LEFT);
            }
        });
        findViewById(R.id.view_rb_2).setOnClickListener(v -> {
            if (!reClickFlag && mCheck == RIGHT) {
                return;
            }
            if (!canClick) {
                return;
            }
            if (mListener != null) {
                boolean b = mListener.onClickCheck(getId(), InputViewCheckV6.this, RIGHT, mRightStr);
                if (b) {
                    setCheck(RIGHT);
                }
            } else {
                setCheck(RIGHT);
            }
        });
    }

    /**
     * public static final int LEFT = 1; public static final int RIGHT = 2; public static final int
     * NONE = 3;
     */
    public void setCheck(int check) {
        mCheck = check;
        mImgLeft.setSelected(check == LEFT);
        mImgRight.setSelected(check == RIGHT);
    }

    /**
     * 是否可以切换
     *
     * @param click
     */
    public void setCanClick(boolean click) {
        canClick = click;
    }

    public int getCheck() {
        return mCheck;
    }

    public void setReClickFlag(boolean reClickFlag) {
        this.reClickFlag = reClickFlag;
    }

    public boolean isReClickFlag() {
        return reClickFlag;
    }

    public static abstract class Listener extends BaseListener<InputViewCheckV6> {

        /**
         * @return true     ->  点击后 选中 false    ->  点击后 不选中，需要自己设置选中状态
         */
        public abstract boolean onClickCheck(int viewId, @NonNull InputViewCheckV6 view, int check,
                                             @NonNull String radioStr);
    }

    public ImageView getImgLeft() {
        return mImgLeft;
    }

    public ImageView getImgRight() {
        return mImgRight;
    }

    public TextView getTvLeft() {
        return mTvLeft;
    }

    public TextView getTvRight() {
        return mTvRight;
    }

    public void setLeftVisible(boolean visible) {
        ViewUtil.setVisible(view_rb_1, visible);
    }

    public void setRightVisible(boolean visible) {
        ViewUtil.setVisible(view_rb_2, visible);
    }
}
