package com.zczy.cargo_owner.invoice.req

import com.zczy.comm.http.entity.BaseNewRequest
import com.zczy.comm.http.entity.BaseRsp

/**
 *  desc: 查询常用开票格式
 *  user: 宋双朋
 *  time: 2024/9/14 14:36
 */
class ReqQueryDefaultInvoiceRuleInfo(
    var feeType: String? = null, //费用类型
    var subsidiaryId: String? = null,
    val businessType: String = "1",// 业务类型，汽运传1
) : BaseNewRequest<BaseRsp<RspQueryInvoiceRuleInfo>>("ams-app/ams/app/invoice/manage/queryDefaultInvoiceRuleInfo")

class RspCommData(
    var data1: MutableList<RspQueryInvoiceSubsidiaryQuotaList>? = null,// 发票类型
    var data2: MutableList<RspQueryInvoiceFeeInfo>? = null,//
    var data3: MutableList<RspQueryInvoiceRuleInfo>? = null,//
    var data4: RspQueryInvoiceRuleInfo? = null,//
)
