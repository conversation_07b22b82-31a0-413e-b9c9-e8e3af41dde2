package com.zczy.cargo_owner.invoice.req

import com.zczy.comm.http.entity.BaseNewRequest
import com.zczy.comm.http.entity.BaseRsp
import com.zczy.comm.http.entity.PageList

/**
 *  desc: 撤销申请
 *  user: 宋双朋
 *  time: 2024/9/14 14:36
 */
class ReqQueryOrderDetailInfo(
    var nowPage: Int = 1,
    var pageSize: Int = 10,
    var id: String? = null,
    var historyYear: String? = null, // 运单发布年份（feeType ==1 时必传，其余费用类型不需要展示和传参）
    var applyId: String? = null,
    var applyState: String? = null,
    var feeType: String? = null,
    var oldFlow: String? = null,
    val businessType: String = "1",// 业务类型，汽运传1
) : BaseNewRequest<BaseRsp<PageList<RspQueryOrderDetailInfo>>>("ams-app/ams/app/invoice/manage/queryOrderDetailInfo")

class RspQueryOrderDetailInfo(
    val orderId: String? = null, // 	运单号
    val cargoName: String? = null, // 	货物名称
    val cargoInfo: String? = null, // 	货物名称
    val plateNumber: String? = null, // 	车牌号
    val unitDesc: String? = null, // 	单位 （吨之类）
    val weight: String? = null, // 	数量
    val invoiceMoney: String? = null, // 	开票金额
    val invoiceMoneyMico: String? = null, // 金额千分位
    val invoiceNos: String? = null, // 	发票号
    val invoiceCode: String? = null, // 	发票代码
    val invoiceDate: String? = null, // 	开票时间
    val billNo: String? = null, // 	对账单号
    val accountPeriod: String? = null, // 	账期
    val billCheckTime: String? = null, // 	对账时间
)