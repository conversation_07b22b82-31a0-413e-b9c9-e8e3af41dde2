package com.zczy.cargo_owner.invoice.fragment

import android.annotation.SuppressLint
import android.graphics.Color
import android.os.Bundle
import android.text.TextUtils
import android.view.View
import androidx.fragment.app.Fragment
import androidx.viewpager.widget.ViewPager
import androidx.viewpager.widget.ViewPager.OnPageChangeListener
import com.sfh.lib.event.RxBusEvent
import com.sfh.lib.mvvm.service.BaseViewModel
import com.sfh.lib.ui.dialog.DialogBuilder
import com.zczy.cargo_owner.R
import com.zczy.cargo_owner.invoice.activity.InvoiceFilterActivity
import com.zczy.cargo_owner.invoice.adapter.InvoicePageAdapter
import com.zczy.cargo_owner.invoice.dialog.InvoiceCollectInfoDialog
import com.zczy.cargo_owner.invoice.model.InvoiceModel
import com.zczy.cargo_owner.invoice.req.ReqQueryCanApplyFlag
import com.zczy.cargo_owner.invoice.req.ReqQueryCanApplyOrderList
import com.zczy.cargo_owner.invoice.req.RspQueryCanApplyInvoice
import com.zczy.cargo_owner.invoice.req.RspQueryCanApplyOrderList
import com.zczy.cargo_owner.invoice.req.RspQuerySubsidiaryWithOrderToApply
import com.zczy.cargo_owner.invoice.req.initReqQueryCollectInfo
import com.zczy.cargo_owner.invoice.req.setData
import com.zczy.comm.ui.BaseFragment
import com.zczy.comm.utils.ex.isNull
import com.zczy.comm.utils.ex.isTrue
import com.zczy.comm.utils.ex.setVisible
import com.zczy.comm.utils.ex.toCommaString
import com.zczy.comm.utils.toJson
import com.zczy.comm.widget.dialog.ChooseDialogV1
import kotlinx.android.synthetic.main.invoice_apply_fragment_v3.expandableLayout
import kotlinx.android.synthetic.main.invoice_apply_fragment_v3.ivCheck1
import kotlinx.android.synthetic.main.invoice_apply_fragment_v3.ivCheck2
import kotlinx.android.synthetic.main.invoice_apply_fragment_v3.ivExpand
import kotlinx.android.synthetic.main.invoice_apply_fragment_v3.llBottom
import kotlinx.android.synthetic.main.invoice_apply_fragment_v3.llCheckView
import kotlinx.android.synthetic.main.invoice_apply_fragment_v3.tabView1
import kotlinx.android.synthetic.main.invoice_apply_fragment_v3.tabView2
import kotlinx.android.synthetic.main.invoice_apply_fragment_v3.tabView3
import kotlinx.android.synthetic.main.invoice_apply_fragment_v3.tabView4
import kotlinx.android.synthetic.main.invoice_apply_fragment_v3.tvCheckCount
import kotlinx.android.synthetic.main.invoice_apply_fragment_v3.tvExpand
import kotlinx.android.synthetic.main.invoice_apply_fragment_v3.tvLeft
import kotlinx.android.synthetic.main.invoice_apply_fragment_v3.tvName1_1
import kotlinx.android.synthetic.main.invoice_apply_fragment_v3.tvName2_1
import kotlinx.android.synthetic.main.invoice_apply_fragment_v3.tvName3_1
import kotlinx.android.synthetic.main.invoice_apply_fragment_v3.tvName3_2
import kotlinx.android.synthetic.main.invoice_apply_fragment_v3.tvName3_3
import kotlinx.android.synthetic.main.invoice_apply_fragment_v3.tvName3_4
import kotlinx.android.synthetic.main.invoice_apply_fragment_v3.tvRight
import kotlinx.android.synthetic.main.invoice_apply_fragment_v3.viewLine1_2
import kotlinx.android.synthetic.main.invoice_apply_fragment_v3.viewLine2_2
import kotlinx.android.synthetic.main.invoice_apply_fragment_v3.viewPager3

/**
 *  desc: 开票申请
 *  user: 宋双朋
 *  time: 2024/9/14 9:48
 */
@SuppressLint("SetTextI18n")
class InvoiceApplyFragmentV3 : BaseFragment<BaseViewModel>() {
    private var mViewPager3: ViewPager? = null
    private val fragment1 = InvoiceApplyFragmentV31.newInstance(tabType = InvoiceApplyFragmentV31.TAB_TYPE_1)
    private val fragment2 = InvoiceApplyFragmentV31.newInstance(tabType = InvoiceApplyFragmentV31.TAB_TYPE_2)
    private var onNextStep: (data1: RspQuerySubsidiaryWithOrderToApply, data2: RspQueryCanApplyInvoice, keyIds: String) -> Unit = { _, _, _ ->

    }
    private var onLastStep: () -> Unit = {

    }
    private var checkType = "" // 1 本页全选 2 全部全选
    private val checkList = mutableListOf<RspQueryCanApplyOrderList>() // 选择的数据
    private var mRspQuerySubsidiaryWithOrderToApply: RspQuerySubsidiaryWithOrderToApply = RspQuerySubsidiaryWithOrderToApply()
    private var mRspQueryCanApplyInvoice: RspQueryCanApplyInvoice = RspQueryCanApplyInvoice()
    private var sortType = ""
    private var filterReq: ReqQueryCanApplyOrderList? = null
    private var totalSize: Int = 0

    companion object {
        private const val EXTRA_DATA = "extraData"

        fun newInstance(extraData: String?): InvoiceApplyFragmentV3 {
            val fragment = InvoiceApplyFragmentV3()
            val bundle = Bundle()
            bundle.putString(EXTRA_DATA, extraData)
            fragment.arguments = bundle
            return fragment
        }
    }

    fun setOnNextStep(onBlock: (data1: RspQuerySubsidiaryWithOrderToApply, data2: RspQueryCanApplyInvoice, keyIds: String) -> Unit) {
        this.onNextStep = onBlock
    }

    fun setOnLastStep(onBlock: () -> Unit) {
        this.onLastStep = onBlock
    }

    fun setData(data1: RspQuerySubsidiaryWithOrderToApply, data2: RspQueryCanApplyInvoice) {
        mRspQuerySubsidiaryWithOrderToApply = data1
        mRspQueryCanApplyInvoice = data2
        //已选
        tvName3_1.text = "已选：${data2.feeName}"
        //开票方
        tvName3_2.text = data1.subsidiaryName
        //受票方
        tvName3_3.text = data1.actualPerson
        refreshListData()
    }

    private fun refreshList(queryType: String): ReqQueryCanApplyOrderList {
        return ReqQueryCanApplyOrderList(
            queryType = queryType,
            subsidiaryId = mRspQuerySubsidiaryWithOrderToApply.subsidiaryId,
            actualPerson = mRspQuerySubsidiaryWithOrderToApply.actualPerson,
            feeType = mRspQueryCanApplyInvoice.feeType,
            sort = initSortType(),
            order = initOrderType(),
            settleType = initChooseSettleType(),
            nowPage = 1
        )
    }

    override fun getLayout(): Int {
        return R.layout.invoice_apply_fragment_v3
    }

    override fun initData() {

    }

    override fun bindView(view: View, bundle: Bundle?) {
        val fragmentChildList = mutableListOf<Fragment>()
        mViewPager3 = view.findViewById(R.id.viewPager3)
        fragment1.apply {
            setOnBlock { item ->
                val find = checkList.find { ob -> TextUtils.equals(ob.id, item.id) }
                if (find.isNull) {
                    checkList.add(item)
                } else {
                    val list = checkList.filter { ob -> !TextUtils.equals(ob.id, item.id) }
                    checkList.clear()
                    checkList.addAll(list)
                }
                val size = fragment1.getAdapterData().size
                checkType = if (size == checkList.size) {
                    "1"
                } else {
                    ""
                }
                refreshCheckAllView()
            }
            setOnCancelAll {
                checkList.clear()
                checkType = ""
                refreshCheckAllView()
            }
            onListSuccessBlock = { size ->
                totalSize = size
                refreshCheckAllView()
            }
        }
        fragmentChildList.add(fragment1)
        fragment2.setShowCheckView(false)
        fragmentChildList.add(fragment2)
        val viewPageAdapter = InvoicePageAdapter(fragmentManager = childFragmentManager, fragmentList = fragmentChildList)
        mViewPager3?.adapter = viewPageAdapter
        mViewPager3?.addOnPageChangeListener(object : OnPageChangeListener {
            override fun onPageScrolled(position: Int, positionOffset: Float, positionOffsetPixels: Int) {

            }

            override fun onPageSelected(position: Int) {
                when (position) {
                    0 -> {
                        llCheckView.setVisible(true)
                    }

                    1 -> {
                        llCheckView.setVisible(false)
                    }
                }
            }

            override fun onPageScrollStateChanged(state: Int) {

            }
        })
        tvName3_4.text = "全部"
        bindClickEvent(tvLeft)
        bindClickEvent(tvRight)
        bindClickEvent(ivCheck1)
        bindClickEvent(ivCheck2)
        bindClickEvent(tvName3_4)
        bindClickEvent(tabView1)
        bindClickEvent(tabView2)
        bindClickEvent(tabView3)
        bindClickEvent(tabView4)
        bindClickEvent(tvExpand)
        bindClickEvent(ivExpand)
    }

    private fun refreshCheckAllView() {
        ivCheck1.isSelected = checkType.isTrue
        ivCheck2.isSelected = TextUtils.equals("2", checkType)
        val checkList = fragment1.getAdapterData().filter { it.isSelect }
        when (checkType) {
            "1" -> {
                tvCheckCount.text = "已选：${checkList.size}/$totalSize"
            }

            "2" -> {
                tvCheckCount.text = "已选：$totalSize/$totalSize"
            }

            else -> {
                tvCheckCount.text = "已选：${checkList.size}/$totalSize"
            }
        }
    }

    override fun onSingleClick(v: View) {
        super.onSingleClick(v)
        when (v.id) {
            R.id.tvLeft -> {
                onLastStep()
            }

            R.id.tvRight -> {
                checkApply()
            }

            R.id.ivCheck1 -> {
                //本页全选
                when (checkType) {
                    "1" -> {
                        checkType = ""
                        fragment1.setCheckType("")
                    }

                    else -> {
                        checkType = "1"
                        fragment1.setCheckType("1")
                    }
                }
                refreshCheckAllView()
            }

            R.id.ivCheck2 -> {
                //全部全选
                when (checkType) {
                    "2" -> {
                        checkType = ""
                        fragment1.setCheckType("")
                    }

                    else -> {
                        checkType = "2"
                        fragment1.setCheckType("2")
                    }
                }
                refreshCheckAllView()
            }

            R.id.tvName3_4 -> {
                //结算方式
                ChooseDialogV1.instance(mutableListOf("全部", "现结", "账期", "周期", "短周期"))
                    .setClick { s, _ ->
                        tvName3_4.text = s
                        refreshListData()
                    }
                    .setChoose(tvName3_4.text.toString())
                    .show(this@InvoiceApplyFragmentV3)
            }

            R.id.tabView1 -> {
                viewPager3.currentItem = 0
                tvName1_1.setTextColor(Color.parseColor("#246EFF"))
                tvName2_1.setTextColor(Color.parseColor("#666666"))
                viewLine1_2.setVisible(true)
                viewLine2_2.setVisible(false)
                llCheckView.setVisible(true)
                llBottom.setVisible(true)
            }

            R.id.tabView2 -> {
                viewPager3.currentItem = 1
                tvName1_1.setTextColor(Color.parseColor("#666666"))
                tvName2_1.setTextColor(Color.parseColor("#246EFF"))
                viewLine1_2.setVisible(false)
                viewLine2_2.setVisible(true)
                llCheckView.setVisible(false)
                llBottom.setVisible(false)
            }

            R.id.tabView3 -> {
                //排序
                ChooseDialogV1.instance(mutableListOf("最新结算时间", "最早结算时间", "最新确认收货时间", "最早确认收货时间", "最新确认发货时间", "最早确认发货时间", "最新发单时间", "最早发单时间"))
                    .setClick { s, _ ->
                        sortType = s
                        refreshListData()
                    }
                    .setChoose(tvName3_4.text.toString())
                    .show(this@InvoiceApplyFragmentV3)
            }

            R.id.tabView4 -> {
                InvoiceFilterActivity.jumpPage(context = <EMAIL>, filter = filterReq.toJson())
            }

            R.id.ivExpand, R.id.tvExpand -> {
                expandableLayout.isExpanded = !expandableLayout.isExpanded
                if (expandableLayout.isExpanded) {
                    tvExpand.text = "收起"
                    ivExpand.setBackgroundResource(R.drawable.invoice_arrow_up_black)
                } else {
                    tvExpand.text = "展开"
                    ivExpand.setBackgroundResource(R.drawable.invoice_arrow_down_black)
                }
            }
        }
    }

    private fun checkApply() {
        getViewModel(InvoiceModel::class.java).execute(
            ReqQueryCanApplyFlag(
                subsidiaryId = mRspQuerySubsidiaryWithOrderToApply.subsidiaryId
            )
        ) { rsp1 ->
            activity?.runOnUiThread {
                if (rsp1.success()) {
                    val applyFlag = rsp1.data?.applyFlag ?: false
                    if (applyFlag) {
                        queryCollectInfo()
                    } else {
                        val dialogBuilder = DialogBuilder()
                        dialogBuilder.title = "提示"
                        dialogBuilder.message = rsp1.data?.resultMsg
                        dialogBuilder.isHideCancel = true
                        dialogBuilder.setOKText("知道了")
                        showDialog(dialogBuilder)
                    }
                } else {
                    val dialogBuilder = DialogBuilder()
                    dialogBuilder.title = "提示"
                    dialogBuilder.message = rsp1.msg
                    dialogBuilder.isHideCancel = true
                    dialogBuilder.setOKText("知道了")
                    showDialog(dialogBuilder)
                }
            }
        }
    }

    private fun queryCollectInfo() {
        val req = initReqQueryCollectInfo(fragment1.getReqList())
        val list = fragment1.getAdapterData().filter { it.isSelect }
        req.keyIds = list.toCommaString { it.id ?: "" }
        req.allCheckFlag = if (ivCheck2.isSelected) {
            "1"
        } else {
            "0"
        }
        getViewModel(InvoiceModel::class.java).execute(req) { rsp1 ->
            activity?.runOnUiThread {
                if (rsp1.success()) {
                    InvoiceCollectInfoDialog(rsp1.data) {
                        onNextStep(mRspQuerySubsidiaryWithOrderToApply, mRspQueryCanApplyInvoice, rsp1.data?.keyIds ?: "")
                    }.show(this@InvoiceApplyFragmentV3)
                } else {
                    val dialogBuilder = DialogBuilder()
                    dialogBuilder.title = "提示"
                    dialogBuilder.message = rsp1.msg
                    dialogBuilder.isHideCancel = true
                    dialogBuilder.setOKText("知道了")
                    showDialog(dialogBuilder)
                }
            }
        }
    }

    private fun refreshListData() {
        val req1 = refreshList(queryType = InvoiceApplyFragmentV31.TAB_TYPE_1)
        req1.setData(filterReq)
        val req2 = refreshList(queryType = InvoiceApplyFragmentV31.TAB_TYPE_2)
        req2.setData(filterReq)
        fragment1.refreshList(reqQueryCanApplyOrderList = req1)
        fragment2.refreshList(reqQueryCanApplyOrderList = req2)
    }

    @RxBusEvent(from = "筛选")
    open fun onFilterSuccess(req: ReqQueryCanApplyOrderList) {
        filterReq = req
        refreshListData()
    }

    private fun initChooseSettleType(): String? {
        val str = tvName3_4.text.toString()
        return when (str) {
            "现结" -> {
                "1"
            }

            "账期" -> {
                "2"
            }

            "周期" -> {
                "3"
            }

            "短周期" -> {
                "4"
            }

            else -> {
                null
            }
        }
    }

    private fun initSortType(): String {
        return when (sortType) {
            "最新结算时间", "最早结算时间" -> {
                "settleTime"
            }

            "最新确认收货时间", "最早确认收货时间" -> {
                "endTime"
            }

            "最新确认发货时间", "最早确认发货时间" -> {
                "startTime"
            }

            "最新发单时间", "最早发单时间" -> {
                "orderPublishTime"
            }

            else -> {
                "settleTime"
            }
        }
    }

    private fun initOrderType(): String {
        return when (sortType) {
            "最新结算时间", "最新确认收货时间", "最新确认发货时间", "最新发单时间" -> {
                "desc"
            }

            "最早结算时间", "最早确认收货时间", "最早确认发货时间", "最早发单时间" -> {
                "asc"
            }

            else -> {
                "desc"
            }
        }
    }
}