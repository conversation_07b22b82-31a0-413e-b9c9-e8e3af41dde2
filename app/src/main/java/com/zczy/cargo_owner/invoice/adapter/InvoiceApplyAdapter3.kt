package com.zczy.cargo_owner.invoice.adapter

import android.widget.ImageView
import com.chad.library.adapter.base.BaseQuickAdapter
import com.chad.library.adapter.base.BaseViewHolder
import com.zczy.cargo_owner.R
import com.zczy.cargo_owner.invoice.req.RspQueryCanApplyOrderList
import com.zczy.cargo_owner.order.confirm.bean.isNotNullOrNotEmpty

/**
 *  desc: 开票申请-第3步
 *  user: 宋双朋
 *  time: 2024/9/14 9:58
 */
class InvoiceApplyAdapter3(var showCheckView: Boolean = true) : BaseQuickAdapter<RspQueryCanApplyOrderList, BaseViewHolder>(R.layout.invoice_apply_item_3) {

    override fun convert(helper: BaseViewHolder, item: RspQueryCanApplyOrderList) {
        helper.setText(R.id.tvTime, item.settleTime)
        helper.setText(R.id.tvCargoName, "${item.cargoName ?: ""}     ${item.weightStr ?: ""}${item.unitDesc ?: ""}")
        helper.setText(R.id.tvCompany1, item.despatchCompany)
        helper.setText(R.id.tvCompany2, item.deliverCompany)
        helper.setText(R.id.tvPlateNumber, item.plateNumber)
        helper.setText(R.id.tvMoney, "${item.invoiceMoneyMico}元")
        helper.setText(R.id.tvRate, "${item.taxStr}")
        helper.addOnClickListener(R.id.tvDetail)
        helper.addOnClickListener(R.id.ivCheck)
        val imageView = helper.getView<ImageView>(R.id.ivCheck)
        imageView.isSelected = item.isSelect
        imageView.isEnabled = showCheckView
        helper.setGone(R.id.viewException, item.applyExceptionReason.isNotNullOrNotEmpty())
        helper.setText(R.id.tvExceptionReason, item.applyExceptionReason ?: "")
    }

    fun setCheckItem(position: Int) {
        val item = mData[position]
        item.isSelect = !item.isSelect
        notifyItemChanged(position)
    }

}