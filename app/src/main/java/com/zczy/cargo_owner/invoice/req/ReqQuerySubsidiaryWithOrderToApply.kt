package com.zczy.cargo_owner.invoice.req

import com.zczy.comm.http.entity.BaseNewRequest
import com.zczy.comm.http.entity.BaseRsp
import com.zczy.comm.http.entity.ResultData

/**
 *  desc: 查询可申请费用类型
 *  user: 宋双朋
 *  time: 2024/9/14 14:36
 */
class ReqQuerySubsidiaryWithOrderToApply(
    val businessType: String = "1",// 业务类型，汽运传1
    var feeType: String? = null,// 费用类型
) : BaseNewRequest<BaseRsp<RspQuerySubsidiaryWithOrderToApplyV1>>("ams-app/ams/app/invoice/manage/querySubsidiaryWithOrderToApply")

class RspQuerySubsidiaryWithOrderToApplyV1(
    val subArr: MutableList<RspQuerySubsidiaryWithOrderToApply>? = null, //
    val actualPerson: String? = null, //受票方，用于下一步的查询传参
) : ResultData()

class RspQuerySubsidiaryWithOrderToApply(
    val subsidiaryId: String? = null,// 结算平台id
    val subsidiaryName: String? = null,// 结算平台
    var actualPerson: String? = null,// 受票方，用于下一步的查询传参 自己维护
)