package com.zczy.cargo_owner.invoice.adapter

import android.text.TextUtils
import android.widget.ImageView
import com.chad.library.adapter.base.BaseQuickAdapter
import com.chad.library.adapter.base.BaseViewHolder
import com.zczy.cargo_owner.R
import com.zczy.cargo_owner.invoice.fragment.InvoiceRecordFragment.Companion.TAB_TYPE_1
import com.zczy.cargo_owner.invoice.fragment.InvoiceRecordFragment.Companion.TAB_TYPE_3
import com.zczy.cargo_owner.invoice.req.RspQueryApplyList
import com.zczy.cargo_owner.invoice.req.RspQueryPackOrderList
import com.zczy.cargo_owner.order.confirm.bean.isNotNullOrNotEmpty

/**
 *  desc: 发票管家
 *  user: 宋双朋
 *  time: 2024/9/14 9:58
 */
class InvoiceManagerAdapter : BaseQuickAdapter<RspQueryPackOrderList, BaseViewHolder>(R.layout.invoice_manager_item) {

    override fun convert(helper: BaseViewHolder, item: RspQueryPackOrderList) {
        helper.setText(R.id.tv1, "发票号码：${item.invoiceNo}")
        helper.setText(R.id.tv2, item.feeName)
        helper.setText(R.id.tv3, item.invoiceTypeStr)
        helper.setText(R.id.tv4, item.subsidiaryName)
        helper.setText(R.id.tv5, item.title)
        helper.setText(R.id.tv6, item.invoiceTime)
        helper.setText(R.id.tv7, item.taxRateStr)
        helper.setText(R.id.tv8, item.invoiceMoneyMico)
        helper.addOnClickListener(R.id.ivCheck)
        val imageView = helper.getView<ImageView>(R.id.ivCheck)
        imageView.isSelected = item.isSelected
        imageView.isEnabled = TextUtils.equals(item.invoiceType, "6") || TextUtils.equals(item.invoiceType, "7")
    }

    fun setCheck(position: Int) {
        val item = mData[position]
        item.isSelected = !item.isSelected
        notifyItemChanged(position, item)
    }
}