package com.zczy.cargo_owner.invoice.activity

import android.annotation.SuppressLint
import android.content.Context
import android.content.Intent
import android.os.Bundle
import androidx.fragment.app.Fragment
import com.sfh.lib.event.RxBusEvent
import com.sfh.lib.mvvm.service.BaseViewModel
import com.zczy.cargo_owner.R
import com.zczy.cargo_owner.invoice.adapter.InvoicePageAdapter
import com.zczy.cargo_owner.invoice.fragment.InvoiceApplyFragmentV6
import com.zczy.cargo_owner.invoice.fragment.InvoiceApplyFragmentV7
import com.zczy.cargo_owner.invoice.req.RxInvoiceEvent
import com.zczy.comm.ui.BaseActivity
import com.zczy.comm.widget.viewpager.CustomViewPager

/**
 *  desc: 开票申请-重新申请
 *  user: 宋双朋
 *  time: 2024/9/18 19:12
 */
class InvoiceReApplyActivity : BaseActivity<BaseViewModel>() {
    private val fragmentList = mutableListOf<Fragment>()
    private var mViewPager: CustomViewPager? = null
    private val invoiceRecordItem by lazy { intent.getStringExtra(INVOICE_RECORD_ITEM) }

    companion object {
        private const val INVOICE_RECORD_ITEM = "invoiceRecordItem"

        @JvmStatic
        fun jumpPage(context: Context?, invoiceRecordItem: String?) {
            val intent = Intent(context, InvoiceReApplyActivity::class.java)
            intent.putExtra(INVOICE_RECORD_ITEM, invoiceRecordItem)
            context?.startActivity(intent)
        }
    }

    override fun getLayout(): Int {
        return R.layout.invoice_apply_activty
    }

    @SuppressLint("CommitTransaction")
    override fun bindView(bundle: Bundle?) {
        mViewPager = findViewById(R.id.viewPager)
        mViewPager?.setScanScroll(false)
        val fragment6 = InvoiceApplyFragmentV6.newInstance(invoiceRecordItem = invoiceRecordItem)
        val fragment7 = InvoiceApplyFragmentV7.newInstance()
        fragment6.apply {
            setOnNextStep { item1, item2 ->
                mViewPager?.currentItem = 1
                fragment7.setData(data1 = item1, data2 = item2)
            }
        }
        fragment7.apply {
            setOnLastStep {
                mViewPager?.currentItem = 0
            }
        }
        fragmentList.add(fragment6)
        fragmentList.add(fragment7)
        val viewPageAdapter = InvoicePageAdapter(fragmentManager = supportFragmentManager, fragmentList = fragmentList)
        mViewPager?.adapter = viewPageAdapter
    }

    override fun initData() {

    }

    @RxBusEvent(from = "开票申请")
    open fun onInvoiceSuccess(data: RxInvoiceEvent) {
        if (data.success) {
            finish()
        }
    }
}