package com.zczy.cargo_owner.invoice.req

import com.zczy.comm.http.entity.BaseNewRequest
import com.zczy.comm.http.entity.BaseRsp
import com.zczy.comm.http.entity.ResultData

/**
 *  desc: 开票申请
 *  user: 宋双朋
 *  time: 2024/9/14 14:36
 */
class ReqQueryCanApplyFlag(
    var subsidiaryId: String? = null,
    val businessType: String = "1",// 业务类型，汽运传1
) : BaseNewRequest<BaseRsp<RspReqQueryCanApplyFlag>>("ams-app/ams/app/invoice/manage/queryCanApplyFlag")

class RspReqQueryCanApplyFlag(
    val applyFlag: Boolean = true //false-不可申请 true-可申请
) : ResultData()
