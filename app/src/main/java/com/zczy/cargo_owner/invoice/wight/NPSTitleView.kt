package com.zczy.cargo_owner.invoice.wight

import android.annotation.SuppressLint
import android.content.Context
import android.graphics.Color
import android.util.AttributeSet
import android.view.Gravity
import android.view.View
import android.view.ViewGroup
import android.widget.LinearLayout
import android.widget.TextView
import androidx.constraintlayout.widget.ConstraintLayout
import com.zczy.cargo_owner.R
import com.zczy.cargo_owner.invoice.req.RspNPSQuestionnaire
import kotlinx.android.synthetic.main.bill_nps_research_title.view.*

/**
 * desc: 问卷调查标题
 * user: 宋双朋
 * time: 2024/11/26 15:29
 */
class NPSTitleView : ConstraintLayout {
    var questionScore: Int = 0
    var callback: ViewOnClickListenerCallback? = null
    var data: RspNPSQuestionnaire? = null

    constructor(context: Context, data: RspNPSQuestionnaire?) : super(context) {
        init(context)
    }

    constructor(context: Context, attrs: AttributeSet?) : super(context, attrs) {
        init(context)
    }

    fun init(context: Context?) {
        inflate(context, R.layout.bill_nps_research_title, this)
    }

    interface ViewOnClickListenerCallback {
        fun onClick(v: View?, index: Int)
    }

    @SuppressLint("SetTextI18n")
    fun showData(data: RspNPSQuestionnaire?, callback: ViewOnClickListenerCallback?) {
        this.data = data
        this.callback = callback
        tv_title.text = data?.stem
        tv_left_txt.text = data?.startPosition
        tv_right_txt.text = data?.endPosition

        ly.removeAllViews()
        val size = data?.score?.toIntOrNull() ?: 0
        val params = LinearLayout.LayoutParams(ViewGroup.LayoutParams.MATCH_PARENT, ViewGroup.LayoutParams.WRAP_CONTENT)
        params.weight = 1f
        params.rightMargin = 7
        for (i in 0 until size) {
            val tv = TextView(context)
            tv.setBackgroundResource(R.drawable.file_nps_fraction_bg2)
            tv.text = (i + 1).toString()
            tv.gravity = Gravity.CENTER
            tv.setOnClickListener(ViewOnClickListener(i))
            ly.addView(tv, params)
        }

    }

    private val txtColor = Color.parseColor("#475585")

    internal inner class ViewOnClickListener(var index: Int) : OnClickListener {
        override fun onClick(v: View) {
            val childSize = ly.childCount
            for (i in 0 until childSize) {
                //现全部置灰
                val tv = ly.getChildAt(i) as TextView
                if (i <= index) {
                    tv.setBackgroundResource(R.drawable.file_nps_fraction_bg)
                    tv.setTextColor(Color.WHITE)
                } else {
                    tv.setBackgroundResource(R.drawable.file_nps_fraction_bg2)
                    tv.setTextColor(txtColor)
                }
            }
            questionScore = index
            callback?.onClick(v, index)
        }
    }
}
