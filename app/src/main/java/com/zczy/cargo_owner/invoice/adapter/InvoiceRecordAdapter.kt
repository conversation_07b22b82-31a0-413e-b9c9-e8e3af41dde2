package com.zczy.cargo_owner.invoice.adapter

import android.text.TextUtils
import com.chad.library.adapter.base.BaseQuickAdapter
import com.chad.library.adapter.base.BaseViewHolder
import com.zczy.cargo_owner.R
import com.zczy.cargo_owner.invoice.fragment.InvoiceRecordFragment.Companion.TAB_TYPE_1
import com.zczy.cargo_owner.invoice.fragment.InvoiceRecordFragment.Companion.TAB_TYPE_2
import com.zczy.cargo_owner.invoice.fragment.InvoiceRecordFragment.Companion.TAB_TYPE_3
import com.zczy.cargo_owner.invoice.req.RspQueryApplyList
import com.zczy.cargo_owner.order.confirm.bean.isNotNullOrNotEmpty

/**
 *  desc: 申请记录
 *  user: 宋双朋
 *  time: 2024/9/14 9:58
 */
class InvoiceRecordAdapter(var tabType: Int = 1) : BaseQuickAdapter<RspQueryApplyList, BaseViewHolder>(R.layout.invoice_record_item) {
    override fun convert(helper: BaseViewHolder, item: RspQueryApplyList) {
        helper.setText(R.id.tv1, item.serialNumber)
        helper.setText(R.id.tv2, item.feeName)
        helper.setText(R.id.tv3, item.invoiceTypeStr)
        helper.setText(R.id.tv4, item.subsidiaryName)
        helper.setText(R.id.tv5, item.title)
        helper.setText(R.id.tv6, item.createTime)
        helper.setText(R.id.tv7, item.orderNum)
        helper.setText(R.id.tv8, item.invoiceMoneyMico)
        helper.setText(R.id.tvRejectReason, "驳回原因：${item.applyCheckRemark}")
        helper.addOnClickListener(R.id.ivRepeal)
        helper.addOnClickListener(R.id.ivReApply)
        helper.addOnClickListener(R.id.detailView)
        helper.setGone(R.id.tvRejectReason, false)
        when (tabType) {
            TAB_TYPE_1 -> {
                //待审核
                helper.setBackgroundRes(R.id.ivRepeal, R.drawable.invoice_record_icon_1)
                helper.setGone(R.id.ivRepeal, true)
                helper.setGone(R.id.ivReApply, false)
            }

            TAB_TYPE_3 -> {
                //驳回/撤回
                helper.setBackgroundRes(R.id.ivRepeal, R.drawable.invoice_record_icon_7)
                helper.setGone(R.id.ivRepeal, true)
                helper.setGone(R.id.ivReApply, true)
                helper.setGone(R.id.tvRejectReason, item.applyCheckRemark.isNotNullOrNotEmpty() && !TextUtils.equals("5", item.applyState))
            }

            else -> {
                helper.setGone(R.id.ivRepeal, false)
                helper.setGone(R.id.ivReApply, false)
            }
        }
    }
}