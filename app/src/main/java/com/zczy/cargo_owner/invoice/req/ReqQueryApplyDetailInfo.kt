package com.zczy.cargo_owner.invoice.req

import com.zczy.comm.http.entity.BaseNewRequest
import com.zczy.comm.http.entity.BaseRsp
import com.zczy.comm.http.entity.ResultData

/**
 *  desc: 申请记录详情
 *  user: 宋双朋
 *  time: 2024/9/14 14:36
 */
class ReqQueryApplyDetailInfo(
    var id: String? = null,
    var applyId: String? = null,
    val businessType: String = "1",// 业务类型，汽运传1
) : BaseNewRequest<BaseRsp<RspQueryApplyDetailInfo>>("ams-app/ams/app/invoice/manage/queryApplyDetailInfo")

class RspQueryApplyDetailInfo(
    val subsidiaryName: String? = null, //开票方
    val invoiceTypeStr: String? = null, //发票类型
    val title: String? = null, //受票方
    val taxName: String? = null, //发票项目
    val taxpayerNumber: String? = null, //纳税人识别码
    val tax: String? = null, //	税率
    val invoiceMoney: String? = null, //	开票金额
    val invoiceMoneyMico: String? = null, //金额千分位
    val companyBank: String? = null, //	开户行
    val bankNumber: String? = null, //	账号
    val noTaxMoney: String? = null, //	发票不含税金额
    val ruleName: String? = null, //	开票规则
    val specifyMode: String? = null, //	规格型号
    val salelistFlag: String? = null, //	发票内容: 汇总 /明细
    val invoiceSplitText: String? = null, //	分票要求
    val branchSplitText: String? = null, //	分行要求
    val invoiceRemark: String? = null, //	发票备注
    val itemListFlag: String? = null, //	清单附件要求
    val otherRemark: String? = null, //	其他要求
    val createTime: String? = null, //申请时间
    val serialNumber: String? = null, //	申请流水号
    val invoiceNoList: MutableList<String>? = null, //发票号码
    val cargoInfo: String? = null, //	货品信息
    val orderNum: String? = null, //明细数
) : ResultData()