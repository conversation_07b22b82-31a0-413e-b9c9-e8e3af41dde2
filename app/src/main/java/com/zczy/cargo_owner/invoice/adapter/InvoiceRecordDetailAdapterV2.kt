package com.zczy.cargo_owner.invoice.adapter

import com.chad.library.adapter.base.BaseQuickAdapter
import com.chad.library.adapter.base.BaseViewHolder
import com.zczy.cargo_owner.R
import com.zczy.cargo_owner.invoice.req.RspQueryOrderDetailInfo

/**
 *  desc: 申请记录详情-运单明细
 *  user: 宋双朋
 *  time: 2024/9/14 9:58
 */
class InvoiceRecordDetailAdapterV2 : BaseQuickAdapter<RspQueryOrderDetailInfo, BaseViewHolder>(R.layout.invoice_record_detail_item_2) {
    override fun convert(helper: BaseViewHolder, item: RspQueryOrderDetailInfo) {
        helper.setText(R.id.tv2_0, "${helper.layoutPosition}、")
        helper.setText(R.id.tv2_1, item.orderId ?: "")
        helper.setText(R.id.tv2_2, "车牌号：${item.plateNumber ?: ""}")
        helper.setText(R.id.tv2_3, "货品信息：${item.cargoInfo ?: ""}")
        helper.setText(R.id.tv2_4, "${item.invoiceMoneyMico ?: ""}元")
    }
}