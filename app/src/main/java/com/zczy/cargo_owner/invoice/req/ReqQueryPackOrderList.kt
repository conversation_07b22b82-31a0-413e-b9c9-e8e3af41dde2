package com.zczy.cargo_owner.invoice.req

import com.zczy.comm.http.entity.BaseNewRequest
import com.zczy.comm.http.entity.BaseRsp
import com.zczy.comm.http.entity.PageList
import com.zczy.comm.http.entity.ResultData

/**
 *  desc: 撤销申请
 *  user: 宋双朋
 *  time: 2024/9/14 14:36
 */
class ReqQueryPackOrderList(
    val businessType: String = "1",// 业务类型，汽运传1
    var nowPage: Int = 1,//
    var pageSize: Int = 10,//
    var invoiceTimeStart: String? = null, // 开始时间
    var invoiceTimeEnd: String? = null, // 结束时间
    var subsidiaryId: String? = null,//结算平台id
) : BaseNewRequest<BaseRsp<PageList<RspQueryPackOrderList>>>("ams-app/ams/app/invoice/manage/queryPackOrderList")

class RspQueryPackOrderList(
    val id: String? = null,//
    val feeName: String? = null,//	费用类型
    val invocieCode: String? = null,//	发票代码
    val invoiceNo: String? = null,//	发票号码
    val invoiceTime: String? = null,//	开票时间
    val invoiceMoney: String? = null,//	开票金额
    val taxRateStr: String? = null,//	税率
    val subsidiaryName: String? = null,//	开票方
    val title: String? = null,//	受票方
    val invoiceTypeStr: String? = null,//	发票类型
    val mailAddress: String? = null,//	邮箱地址
    val invoiceMoneyMico: String? = null,//	开票金额（千分位格式）
    val invoiceType: String? = null,//	发票类型 （数电 6 或 7）
) {
    var isSelected: Boolean = false//选择操作
}

/**
 *  desc: 查询开票信息中邮箱地址
 *  user: 宋双朋
 *  time: 2024/9/18 9:58
 */
class ReqQueryDefaultMail(
    val businessType: String = "1",// 业务类型，汽运传1
) : BaseNewRequest<BaseRsp<RspQueryDefaultMail>>("ams-app/ams/app/invoice/manage/queryDefaultMail")

class RspQueryDefaultMail(
    val mailAddress: String? = null, // 邮箱地址
) : ResultData()

/**
 *  desc: 发送邮箱
 *  user: 宋双朋
 *  time: 2024/9/18 9:58
 */
class ReqSendEmail(
    val businessType: String = "1",// 业务类型，汽运传1
    var packIds: MutableList<String> = mutableListOf(),// 勾选数据id集合
    var mailAddress: String? = null,// 邮箱地址
) : BaseNewRequest<BaseRsp<RspQueryDefaultMail>>("ams-app/ams/app/invoice/manage/sendEmail")