package com.zczy.cargo_owner.invoice.req

import com.zczy.comm.http.entity.BaseNewRequest
import com.zczy.comm.http.entity.BaseRsp
import com.zczy.comm.http.entity.PageList

/**
 *  desc: 重新申请
 *  user: 宋双朋
 *  time: 2024/9/14 14:36
 */
class ReqDoInvoiceReapplyPreview(
    var applyId: String? = null, // 原申请applyId
    var businessType: String = "1", // 业务类型，汽运传1，水运传2
    var itemListFlag: String = "0",
    var feeType: String? = null, // 费用类型 来源页面选择
    var subsidiaryId: String? = null, // 开票方Id 来源页面选择
    var keyIds: String? = null, // 待申请Id拼接（逗号拼接） 来源queryCollectInfo接口返回
    var addIds: String? = null, // 新增申请id拼接（英文逗号拼接）来源queryCollectInfo接口返回
    var delIds: String? = null, // 删除申请id拼接（英文逗号拼接）来源queryCollectInfo接口返回
    var feeInfoId: String? = null, // 发票项目ID 来源页面选择
    var invoiceType: String? = null, // 发票类型 来源页面选择的发票类型
    var otherRemark: String? = null, // 其他备注
    var ruleId: String? = null, // 选择的规则ID（即开票要求那块） 来源页面选择 (非自定义规则时必填)
    var taxName: String? = null, // 税控项目 来源页面选择
    var ruleSource: String? = null, // 规则来源 1无特殊要求 2 用户常用 3 自定义规则 来源页面选择，水运传1
    var buyerName: String? = null, // 受票方名称 选择的受票方名称
    var salelistFlag: String? = null, // 发票内容 0 汇总 1 明细
    var invoiceSplitFlag: String? = null, // 分票要求 0 否 1 是
    var invoiceSplitCollect: String? = null, // 分票规则 根据勾选项编码以逗号拼接， 编码见下方枚举 InvoiceSplit (分票要求为是时必填)
    var branchSplitFlag: String? = null, // 分行要求 0 否 1 是
    var branchSplitCollect: String? = null, // 分行规则 根据勾选项编码以逗号拼接， 编码见下方枚举 InvoiceSplit (分行要求为是时必填)
    var specifyMode: String? = null, // 规格型号
    var invoiceRemark: String? = null, // 发票备注 根据勾选项编码以逗号拼接， 编码见下方枚举 InvoiceRemark
    var demurrageSplit: String? = null, // 滞期费是否分行开票 0-否 1-是 (有滞期费必传)
    var feeInfoDemurrageId: String? = null // 发票项目id（滞期费） (滞期费是否分行开票为是时，必传)
) : BaseNewRequest<BaseRsp<RspApplyPreview>>("ams-app/ams/app/invoice/manage/doInvoiceReapplyPreview")


