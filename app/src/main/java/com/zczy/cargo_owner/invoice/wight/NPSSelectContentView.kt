package com.zczy.cargo_owner.invoice.wight

import android.annotation.SuppressLint
import android.content.Context
import android.graphics.Color
import android.text.TextUtils
import android.util.AttributeSet
import android.view.View
import android.widget.TextView
import androidx.constraintlayout.widget.ConstraintLayout
import com.example.library.AutoFlowLayout
import com.example.library.FlowAdapter
import com.sfh.lib.rx.ui.UtilRxView
import com.zczy.cargo_owner.R
import com.zczy.cargo_owner.invoice.req.ENPSItem
import com.zczy.cargo_owner.invoice.req.NpsTreeList
import com.zczy.comm.utils.ex.setVisible
import kotlinx.android.synthetic.main.bill_nps_research_select_content.view.et_content
import kotlinx.android.synthetic.main.bill_nps_research_select_content.view.fy_edit
import kotlinx.android.synthetic.main.bill_nps_research_select_content.view.history_flexboxLayout
import kotlinx.android.synthetic.main.bill_nps_research_select_content.view.tv_content
import kotlinx.android.synthetic.main.bill_nps_research_select_content.view.tv_content_size

/**
 * desc: 评价内容
 * user: 宋双朋
 * time: 2024/11/26 15:27
 */
class NPSSelectContentView : ConstraintLayout {

    var itemList: List<ENPSItem> = ArrayList()
        private set

    constructor(context: Context) : super(context) {
        init(context)
    }

    constructor(context: Context, attrs: AttributeSet?) : super(context, attrs) {
        init(context)
    }

    @SuppressLint("SetTextI18n")
    fun init(context: Context?) {
        inflate(context, R.layout.bill_nps_research_select_content, this)

        UtilRxView.afterTextChangeEvents(et_content, 100) { charSequence ->
            if (charSequence.isEmpty()) {
                tv_content_size.text = "0/100"
            } else {
                tv_content_size.text = String.format("%s/%s", (100 - charSequence.length), 100)
            }
        }

        history_flexboxLayout.setOnItemClickListener { i, view ->
            val item = itemList[i]
            item.select = !item.select
            val tv = view.findViewById<TextView>(R.id.tv)
            tv.setTextColor(if (item.select) Color.parseColor("#ff5086fc") else Color.parseColor("#ff666666"))
            tv.setBackgroundResource(
                if (item.select) {
                    R.drawable.order_main_list_ic_bg_d9ecff
                } else {
                    R.drawable.base_ui_shape_eff0f3_solid_4radius
                }
            )
        }
    }

    var data: NpsTreeList? = null

    fun showData(data: NpsTreeList) {
        val fv = history_flexboxLayout as AutoFlowLayout<ENPSItem>
        this.data = data
        tv_content.text = data.stem
        this.itemList = data.children ?: mutableListOf()
        fv.clearViews()
        fv.setAdapter(object : FlowAdapter<ENPSItem>(itemList) {
            override fun getView(i: Int): View {
                val view = inflate(context, R.layout.order_nps_search_item, null)
                val tv = view.findViewById<TextView>(R.id.tv)
                tv.text = itemList[i].newsName
                return view
            }
        })
        fy_edit.setVisible(TextUtils.equals("1", data.hasAnswer))
    }

    fun getEditContent(): String {
        return et_content.text.toString()
    }
}
