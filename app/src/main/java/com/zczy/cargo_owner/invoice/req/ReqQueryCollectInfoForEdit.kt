package com.zczy.cargo_owner.invoice.req

import com.zczy.comm.http.entity.BaseNewRequest
import com.zczy.comm.http.entity.BaseRsp

/**
 *  desc: 重新申请-点击下一步汇总数据
 *  user: 宋双朋
 *  time: 2024/9/14 14:36
 */
class ReqQueryCollectInfoForEdit(
    var applyId: String? = null, // 原申请applyId
    var queryType: String? = null, //	1-可申请 2-其他运单
    var autoOperFlag: String? = null, // 是否自动勾选标记 水运是 0-否 1-是
    var businessType: String? = "1", // 业务类型，汽运传1，水运传2
    var feeType: String? = null, // 费用类型
    var subsidiaryId: String? = null, // 开票方Id
    var keyIds: String? = null, // 待申请Id拼接（逗号拼接）
    var buyerName: String? = null, // 受票方名称 选择的受票方名称
    var allCheckFlag: String? = null, // 是否全部全选 0-否 1-是
    var settleType: String? = null, // 结算方式1现结、2账期、3周期 、4短周期
    var orderNumber: String? = null, // 关联单号
    var waybillNumbers: String? = null, // 运单号
    var plateNumber: String? = null, // 车牌号、船号
    var orderTitle: String? = null, // 货运信息标题
    var despatchCompany: String? = null, // 发货单位
    var deliverCompany: String? = null, // 收货单位
    var startPlace: String? = null, //		目的地
    var endPlace: String? = null, // 目的地、收货码头
    var settleTimeS: String? = null, // 结算时间 开始
    var settleTimeE: String? = null, // 结算时间 结束
    var orderPublicTimeS: String? = null, // 发单时间 开始
    var orderPublicTimeE: String? = null, // 发单时间 结束
    var cargoName: String? = null, // 货物名称
    var remark3: String? = null, // 批量货单号
    var delistTimeS: String? = null, // 成交时间开始
    var delistTimeE: String? = null, // 成交时间结束
    var startTimeS: String? = null, // 发货时间开始
    var startTimeE: String? = null, // 发货时间结束
    var endTimeS: String? = null, // 收货时间开始
    var endTimeE: String? = null, // 收货时间结束
    var billingFlag: String? = null, //		是否达到开票条件 0：否；1：是
    var billNo: String? = null, //		对账单号
    var accountPeriod: String? = null, //		账期
    var sort: String = "settleTime", //排序字段名 发单时间：orderPublishTime 确认发货时间：startTime  确认收货时间：endTime 结算时间：settleTime(默认)
    var order: String = "desc", //	排序方式 升序：asc降序：desc（默认）
    var orderPublishTime: String? = null, //发单时间
    var billCheckTimeS: String? = null, // 对账时间
    var billCheckTimeE: String? = null, // 对账时间
    var startTime: String? = null, //确认发货时间
    var endTime: String? = null, //确认收货时间
    var settleTime: String? = null, //	结算时间
) : BaseNewRequest<BaseRsp<RspQueryCollectInfo>>("ams-app/ams/app/invoice/manage/queryCollectInfoForEdit")


