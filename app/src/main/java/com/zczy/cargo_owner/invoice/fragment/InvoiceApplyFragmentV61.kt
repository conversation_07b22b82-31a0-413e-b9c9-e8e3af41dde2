package com.zczy.cargo_owner.invoice.fragment

import android.annotation.SuppressLint
import android.os.Bundle
import android.text.TextUtils
import android.view.View
import androidx.recyclerview.widget.LinearLayoutManager
import com.sfh.lib.mvvm.annotation.LiveDataMatch
import com.sfh.lib.mvvm.service.BaseViewModel
import com.zczy.cargo_owner.R
import com.zczy.cargo_owner.invoice.activity.InvoiceWebActivity
import com.zczy.cargo_owner.invoice.adapter.InvoiceApplyAdapter6
import com.zczy.cargo_owner.invoice.model.InvoiceModel
import com.zczy.cargo_owner.invoice.req.ReqQueryAllListByApplyId
import com.zczy.cargo_owner.invoice.req.ReqQueryEditApplyOrderList
import com.zczy.cargo_owner.invoice.req.RspQueryCanApplyOrderList
import com.zczy.cargo_owner.libcomm.config.HttpConfig
import com.zczy.comm.http.entity.PageList
import com.zczy.comm.ui.BaseFragment
import kotlinx.android.synthetic.main.invoice_apply_fragment_v61.swipeRefreshMoreLayout
import java.util.Calendar

/**
 *  desc: 申请开票-重新开票
 *  user: 宋双朋
 *  time: 2024/9/14 9:48
 */
@SuppressLint("NotifyDataSetChanged")
class InvoiceApplyFragmentV61 : BaseFragment<BaseViewModel>() {
    private val mAdapter = InvoiceApplyAdapter6()
    private var onCheckStep: (data: RspQueryCanApplyOrderList) -> Unit = {

    }
    private var onCancelAll: () -> Unit = {

    }
    var onListSuccessBlock: (totalSize: Int) -> Unit = {

    }
    private var reqQueryList: ReqQueryEditApplyOrderList? = null
    private var checkType: String = ""
    private var applyId: String? = null
    private val tabType by lazy { arguments?.getString(TAB_TYPE) }
    private var oldCheckList: MutableList<RspQueryCanApplyOrderList>? = null

    companion object {
        private const val TAB_TYPE = "tabType"
        const val TAB_TYPE_1 = "1"
        const val TAB_TYPE_2 = "2"

        fun newInstance(tabType: String?): InvoiceApplyFragmentV61 {
            val fragment = InvoiceApplyFragmentV61()
            val bundle = Bundle()
            bundle.putString(TAB_TYPE, tabType)
            fragment.arguments = bundle
            return fragment
        }
    }

    fun getReqList(): ReqQueryEditApplyOrderList? {
        return reqQueryList
    }

    fun setOnBlock(block: (data: RspQueryCanApplyOrderList) -> Unit) {
        this.onCheckStep = block
    }

    fun setOnCancelAll(block: () -> Unit) {
        this.onCancelAll = block
    }

    fun setApplyId(applyId: String?) {
        this.applyId = applyId
    }

    fun setCheckType(checkType: String) {
        this.checkType = checkType
        when (checkType) {
            "1", "2" -> {
                //本页全选
                mAdapter.data.forEach {
                    it.isSelect = true
                }
                mAdapter.notifyDataSetChanged()
            }

            else -> {
                mAdapter.data.forEach {
                    it.isSelect = false
                }
                mAdapter.notifyDataSetChanged()
            }
        }
    }

    fun setShowCheckView(show: Boolean) {
        mAdapter.showCheckView = show
    }

    fun getAdapterData(): MutableList<RspQueryCanApplyOrderList> {
        return mAdapter.data
    }

    override fun getLayout(): Int {
        return R.layout.invoice_apply_fragment_v61
    }

    override fun initData() {
        swipeRefreshMoreLayout.onAutoRefresh()
    }

    fun refreshList(reqQueryCanApplyOrderList: ReqQueryEditApplyOrderList) {
        reqQueryList = reqQueryCanApplyOrderList
    }

    override fun bindView(view: View, bundle: Bundle?) {
        swipeRefreshMoreLayout.apply {
            setLayoutManager(LinearLayoutManager(<EMAIL>))
            setAdapter(mAdapter, true)
            setEmptyView(R.layout.invoice_apply_empty_item_2)
            setOnLoadListener2 {
                reqQueryList?.let { it1 ->
                    it1.nowPage = it
                    if (it == 1 && TextUtils.equals(TAB_TYPE_1, tabType)) {
                        getViewModel(InvoiceModel::class.java).initReApplyOrderList(
                            req1 = ReqQueryAllListByApplyId(
                                applyId = applyId,
                                subsidiaryId = it1.subsidiaryId,
                                feeType = it1.feeType,
                            ), req2 = it1
                        )
                    } else {
                        getViewModel(InvoiceModel::class.java).queryEditApplyOrderList(req = it1)
                    }
                }
            }
            addOnItemChildClickListener { _, view, position ->
                when (view.id) {
                    R.id.ivCheck -> {
                        when (checkType) {
                            "2" -> {
                                //取消所有选择
                                checkType = ""
                                mAdapter.data.forEach {
                                    it.isSelect = false
                                }
                                mAdapter.notifyDataSetChanged()
                                onCancelAll()
                            }

                            else -> {
                                mAdapter.setCheckItem(position)
                                postDelayed({
                                    onCheckStep(mAdapter.data[position])
                                }, 300)
                            }
                        }
                    }

                    R.id.tvDetail -> {
                        val item = mAdapter.data[position]
                        val url = HttpConfig.getWebUrl() + "/form_h5/h5_inner/index.html?_t=${Calendar.getInstance().timeInMillis}#/hzInvoicingDetails?id=${item.id}&type=${reqQueryList?.feeType}"
                        InvoiceWebActivity.jumpPage(
                            context = <EMAIL>,
                            url = url,
                            applyId = "",
                        )
                    }
                }
            }
        }
    }

    @LiveDataMatch
    open fun onInitReApplyOrderListSuccess(list1: MutableList<RspQueryCanApplyOrderList>, list2: PageList<RspQueryCanApplyOrderList>) {
        oldCheckList = list1
        list2.rootArray?.forEach { item1 ->
            val find = list1.find { item2 -> TextUtils.equals(item2.id, item1.id) }
            if (find != null) {
                item1.isSelect = true
            }
        }
        swipeRefreshMoreLayout.onRefreshCompale(list2)
    }

    @LiveDataMatch
    open fun queryEditApplyOrderListSuccess(data: PageList<RspQueryCanApplyOrderList>) {
        when (tabType) {
            TAB_TYPE_1 -> {
                onListSuccessBlock(data.totalSize)
                data.rootArray?.forEach { item1 ->
                    val find = oldCheckList?.find { item2 -> TextUtils.equals(item2.id, item1.id) }
                    if (find != null) {
                        item1.isSelect = true
                    }
                }
            }
        }
        when (this.checkType) {
            "2" -> {
                //全部全选
                data.rootArray?.forEach {
                    it.isSelect = true
                }
            }
        }
        swipeRefreshMoreLayout.onRefreshCompale(data)
    }

    @LiveDataMatch
    open fun queryEditApplyOrderListError() {
        swipeRefreshMoreLayout.onLoadMoreFail()
    }
}