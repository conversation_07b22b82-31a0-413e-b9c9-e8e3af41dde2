package com.zczy.cargo_owner.invoice.wight

import android.annotation.SuppressLint
import android.content.Context
import android.util.AttributeSet
import android.view.LayoutInflater
import android.view.ViewGroup
import android.view.WindowManager
import android.widget.LinearLayout
import androidx.recyclerview.widget.LinearLayoutManager
import com.zczy.cargo_owner.R
import com.zczy.cargo_owner.invoice.adapter.InvoiceRecordDetailAdapterV1
import com.zczy.cargo_owner.invoice.req.RspQueryApplyDetailInfo
import kotlinx.android.synthetic.main.invoice_head_view_layout.view.invoiceInfoRecycleView
import kotlinx.android.synthetic.main.invoice_head_view_layout.view.tv1
import kotlinx.android.synthetic.main.invoice_head_view_layout.view.tv10
import kotlinx.android.synthetic.main.invoice_head_view_layout.view.tv11
import kotlinx.android.synthetic.main.invoice_head_view_layout.view.tv12
import kotlinx.android.synthetic.main.invoice_head_view_layout.view.tv13
import kotlinx.android.synthetic.main.invoice_head_view_layout.view.tv13_1
import kotlinx.android.synthetic.main.invoice_head_view_layout.view.tv4
import kotlinx.android.synthetic.main.invoice_head_view_layout.view.tv5
import kotlinx.android.synthetic.main.invoice_head_view_layout.view.tv6
import kotlinx.android.synthetic.main.invoice_head_view_layout.view.tv7
import kotlinx.android.synthetic.main.invoice_head_view_layout.view.tv8
import kotlinx.android.synthetic.main.invoice_head_view_layout.view.tv9
import kotlinx.android.synthetic.main.invoice_head_view_layout.view.tvSwitchYear
import kotlinx.android.synthetic.main.invoice_head_view_layout.view.tvYear
import java.util.Calendar

/**
 *  desc: 申请记录详情顶部数据
 *  user: 宋双朋
 *  time: 2024/10/9 14:57
 */
@SuppressLint("SetTextI18n")
class InvoiceRecordHeadView : LinearLayout {
    private val mAdapterV1 = InvoiceRecordDetailAdapterV1()
    var currentYear: Int = 0
    var switchYearBlock: () -> Unit = {

    }

    constructor(context: Context?) : super(context) {
        init()
    }

    constructor(context: Context?, attrs: AttributeSet?) : super(context, attrs) {
        init()
    }

    constructor(context: Context?, attrs: AttributeSet?, defStyleAttr: Int) : super(context, attrs, defStyleAttr) {
        init()
    }

    private fun init() {
        val view = LayoutInflater.from(context).inflate(R.layout.invoice_head_view_layout, null)
        val layoutParams = LayoutParams(ViewGroup.LayoutParams.MATCH_PARENT, WindowManager.LayoutParams.WRAP_CONTENT)
        addView(view, layoutParams)
        val calendar = Calendar.getInstance()
        currentYear = calendar.get(Calendar.YEAR)
        tvYear.text = "当前：${currentYear}"
        val emptyView = LayoutInflater.from(context).inflate(R.layout.invoice_record_detail_empty_view, null)
        mAdapterV1.setEmptyView(emptyView)
        invoiceInfoRecycleView.apply {
            layoutManager = LinearLayoutManager(context)
            adapter = mAdapterV1
        }
        tvSwitchYear.setOnClickListener {
            //切换年份
            switchYearBlock()
        }
    }

    fun setData(data: RspQueryApplyDetailInfo) {
        tv1.text = data.serialNumber
        tv4.text = data.subsidiaryName
        tv5.text = data.title
        tv6.text = data.createTime
        tv7.text = data.orderNum
        tv8.text = data.invoiceMoneyMico
        tv9.text = data.ruleName
        tv10.text = data.salelistFlag
        tv11.text = data.invoiceSplitText
        tv12.text = data.specifyMode
        tv13.text = data.invoiceRemark
        tv13_1.text = data.invoiceTypeStr
        mAdapterV1.setNewData(data.invoiceNoList)
    }

    fun setYear() {
        tvYear.text = "当前：${currentYear}"
    }
}