package com.zczy.cargo_owner.invoice.adapter

import androidx.fragment.app.Fragment
import androidx.fragment.app.FragmentActivity
import androidx.fragment.app.FragmentManager
import androidx.fragment.app.FragmentPagerAdapter
import androidx.fragment.app.FragmentStatePagerAdapter
import androidx.viewpager2.adapter.FragmentStateAdapter

/**
 *  desc: 开票申请
 *  user: 宋双朋
 *  time: 2024/9/19 11:19
 */
class InvoicePageAdapter(fragmentManager: FragmentManager, private val fragmentList: MutableList<Fragment>) : FragmentStatePagerAdapter(fragmentManager) {

    override fun getCount(): Int {
        return fragmentList.size
    }

    override fun getItem(position: Int): Fragment {
        return fragmentList[position]
    }
}