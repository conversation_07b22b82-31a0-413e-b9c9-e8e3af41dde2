package com.zczy.cargo_owner.invoice.activity

import android.annotation.SuppressLint
import android.app.Activity
import android.content.Intent
import android.os.Bundle
import android.text.TextUtils
import android.view.View
import com.sfh.lib.mvvm.service.BaseViewModel
import com.zczy.cargo_owner.R
import com.zczy.cargo_owner.invoice.activity.InvoiceRecordFilterActivity.Companion.TIME_TYPE_1
import com.zczy.cargo_owner.invoice.activity.InvoiceRecordFilterActivity.Companion.TIME_TYPE_2
import com.zczy.cargo_owner.invoice.req.ReqQuerySubsidiaryList
import com.zczy.cargo_owner.invoice.req.RspQuerySubsidiaryList
import com.zczy.comm.TimePickerUtilV1
import com.zczy.comm.TimePickerUtilV1.YYYY
import com.zczy.comm.TimePickerUtilV1.YYYY_MM_DD_HH_MM_SS
import com.zczy.comm.ui.BaseActivity
import com.zczy.comm.utils.ex.YYYY_MM_DD
import com.zczy.comm.utils.ex.getFormatTime
import com.zczy.comm.utils.ex.isNull
import com.zczy.comm.utils.setVisible
import com.zczy.comm.utils.toJson
import com.zczy.comm.utils.toJsonObject
import com.zczy.comm.widget.dialog.ChooseDialogV1
import com.zczy.comm.widget.inputv2.InputViewClick
import kotlinx.android.synthetic.main.invoice_manage_filter_activty.inputView1
import kotlinx.android.synthetic.main.invoice_manage_filter_activty.inputView2
import kotlinx.android.synthetic.main.invoice_manage_filter_activty.inputView3
import kotlinx.android.synthetic.main.invoice_manage_filter_activty.tvLeft
import kotlinx.android.synthetic.main.invoice_manage_filter_activty.tvRight
import java.util.Calendar

/**
 *  desc: 发票管家-筛选
 *  user: 宋双朋
 *  time: 2024/9/23 15:14
 */

@SuppressLint("SetTextI18n")
class InvoiceManageFilterActivity : BaseActivity<BaseViewModel>() {
    private var startTime: String? = null // 开始时间
    private var endTime: String? = null // 结束时间
    private var mRspQueryCanApplyInvoice: RspQuerySubsidiaryList? = null
    private var timeType: String = "1" // 1.最近三个月 2.自定义时间

    companion object {
        private const val EXTRA_DATA_1 = "extraData1"
        private const val EXTRA_DATA_2 = "extraData2"
        private const val EXTRA_DATA_3 = "extraData3"
        private const val EXTRA_DATA_4 = "extraData4"
        const val REQUEST_CODE = 0x11

        @JvmStatic
        fun jumpPage(activity: Activity, startTime: String?, endTime: String?, data: String?, timeType: String?) {
            val intent = Intent(activity, InvoiceManageFilterActivity::class.java)
            intent.putExtra(EXTRA_DATA_1, startTime)
            intent.putExtra(EXTRA_DATA_2, endTime)
            intent.putExtra(EXTRA_DATA_3, data)
            intent.putExtra(EXTRA_DATA_4, timeType)
            activity.startActivityForResult(intent, REQUEST_CODE)
        }

        @JvmStatic
        fun obtainTime1(intent: Intent?): String? {
            return intent?.getStringExtra(EXTRA_DATA_1)
        }

        @JvmStatic
        fun obtainTime2(intent: Intent?): String? {
            return intent?.getStringExtra(EXTRA_DATA_2)
        }

        @JvmStatic
        fun obtainTime3(intent: Intent?): String {
            return intent?.getStringExtra(EXTRA_DATA_4) ?: "1"
        }

        @JvmStatic
        fun obtainData(intent: Intent?): RspQuerySubsidiaryList? {
            return intent?.getStringExtra(EXTRA_DATA_3).toJsonObject(RspQuerySubsidiaryList::class.java)
        }
    }

    override fun getLayout(): Int {
        return R.layout.invoice_manage_filter_activty
    }

    override fun bindView(bundle: Bundle?) {
        timeType = intent?.getStringExtra(EXTRA_DATA_4) ?: "1"
        when (timeType) {
            "1" -> {
                inputView1.content = TIME_TYPE_1
                inputView2.setVisible(false)
            }

            "2" -> {
                startTime = obtainTime1(intent)
                endTime = obtainTime2(intent)
                inputView1.content = TIME_TYPE_2
                inputView2.setVisible(true)
                inputView2.content = "$startTime 至 $endTime"
            }
        }
        inputView1.setListener(object : InputViewClick.Listener() {
            override fun onClick(viewId: Int, view: InputViewClick, content: String) {
                ChooseDialogV1.instance(mutableListOf(TIME_TYPE_1, TIME_TYPE_2))
                    .setClick { s, _ ->
                        view.content = s
                        inputView2.setVisible(TextUtils.equals(s, TIME_TYPE_2))
                    }
                    .show(this@InvoiceManageFilterActivity)
            }
        })
        inputView2.setListener(object : InputViewClick.Listener() {
            override fun onClick(viewId: Int, view: InputViewClick, content: String) {
                TimePickerUtilV1.showStartEnd(context = this@InvoiceManageFilterActivity, selectedDate = null) { s, e ->
                    val t1 = s.getFormatTime(YYYY)
                    val t2 = e.getFormatTime(YYYY)
                    if (TextUtils.equals(t1, t2)) {
                        val check = e?.before(s) ?: false
                        if (check) {
                            showDialogToast("结束时间不能早于开始时间")
                            return@showStartEnd
                        }
                        startTime = s.getFormatTime(YYYY_MM_DD_HH_MM_SS)
                        endTime = e.getFormatTime(YYYY_MM_DD_HH_MM_SS)
                        inputView2.content = "$startTime 至 $endTime"
                    } else {
                        showDialogToast("开始日期，结束日期，不允许选择非同一年份的日期")
                    }
                }
            }
        })
        inputView3.setListener(object : InputViewClick.Listener() {
            override fun onClick(viewId: Int, view: InputViewClick, content: String) {
                getViewModel(BaseViewModel::class.java).execute(ReqQuerySubsidiaryList()) { rsp1 ->
                    if (rsp1.success()) {
                        ChooseDialogV1.instance(rsp1.data?.subsidiaryList ?: emptyList())
                            .setTitle("请选择开票方")
                            .setFlatMap { upSettlePartName ?: "" }
                            .setClick { s, _ ->
                                mRspQueryCanApplyInvoice = s
                                inputView3.content = s.upSettlePartName ?: ""
                            }
                            .show(this@InvoiceManageFilterActivity)
                    }
                }
            }
        })
        tvLeft.setVisible(false)
        bindClickEvent(tvLeft)
        bindClickEvent(tvRight)
    }

    override fun initData() {
        mRspQueryCanApplyInvoice = obtainData(intent)
        if (mRspQueryCanApplyInvoice.isNull) {
            getViewModel(BaseViewModel::class.java).execute(ReqQuerySubsidiaryList()) { rsp1 ->
                if (rsp1.success()) {
                    rsp1.data?.subsidiaryList?.let { list ->
                        if (list.isNotEmpty()) {
                            mRspQueryCanApplyInvoice = list[0]
                        }
                        inputView3.content = mRspQueryCanApplyInvoice?.upSettlePartName ?: ""
                    }
                }
            }
        } else {
            inputView3.content = mRspQueryCanApplyInvoice?.upSettlePartName ?: ""
        }
    }

    override fun onSingleClick(v: View) {
        super.onSingleClick(v)
        when (v.id) {
            R.id.tvLeft -> {
                finish()
            }

            R.id.tvRight -> {
                if (mRspQueryCanApplyInvoice.isNull) {
                    showDialogToast("请选择开票方")
                    return
                }
                val content = inputView1.content
                if (content.isEmpty()) {
                    showDialogToast("请选择开票时间")
                    return
                }
                if (TextUtils.equals(content, TIME_TYPE_2) && (startTime.isNullOrEmpty() || endTime.isNullOrEmpty())) {
                    showDialogToast("请选择自定义时间")
                    return
                }

                if (TextUtils.equals(content, TIME_TYPE_1)) {
                    val time1 = Calendar.getInstance().also {
                        it.add(Calendar.DAY_OF_MONTH, -90)
                    }.time.getFormatTime(YYYY_MM_DD)
                    startTime = "$time1 00:00:00"
                    val time2 = Calendar.getInstance().time.getFormatTime(YYYY_MM_DD_HH_MM_SS)
                    endTime = "$time2 23:59:59"
                    intent.putExtra(EXTRA_DATA_4, "1")
                } else {
                    intent.putExtra(EXTRA_DATA_4, "2")
                }
                intent.putExtra(EXTRA_DATA_1, startTime)
                intent.putExtra(EXTRA_DATA_2, endTime)
                intent.putExtra(EXTRA_DATA_3, mRspQueryCanApplyInvoice.toJson())
                setResult(Activity.RESULT_OK, intent)
                finish()
            }
        }
    }
}