package com.zczy.cargo_owner.invoice.fragment

import android.annotation.SuppressLint
import android.os.Bundle
import android.view.View
import androidx.recyclerview.widget.LinearLayoutManager
import com.sfh.lib.mvvm.annotation.LiveDataMatch
import com.sfh.lib.mvvm.service.BaseViewModel
import com.sfh.lib.ui.dialog.DialogBuilder
import com.zczy.cargo_owner.R
import com.zczy.cargo_owner.invoice.activity.InvoiceReApplyActivity
import com.zczy.cargo_owner.invoice.activity.InvoiceRecordDetailActivity
import com.zczy.cargo_owner.invoice.adapter.InvoiceRecordAdapter
import com.zczy.cargo_owner.invoice.model.InvoiceModel
import com.zczy.cargo_owner.invoice.req.ReqDeleteInvoiceApplyOrder
import com.zczy.cargo_owner.invoice.req.ReqQueryApplyList
import com.zczy.cargo_owner.invoice.req.ReqRecallApplyOrder
import com.zczy.cargo_owner.invoice.req.RspQueryApplyList
import com.zczy.comm.TimePickerUtilV1.YYYY_MM_DD_HH_MM_SS
import com.zczy.comm.http.entity.PageList
import com.zczy.comm.ui.BaseFragment
import com.zczy.comm.utils.ex.YYYY_MM_DD
import com.zczy.comm.utils.ex.getFormatTime
import com.zczy.comm.utils.ex.setVisible
import com.zczy.comm.utils.toJson
import kotlinx.android.synthetic.main.invoice_record_fragment.*
import java.util.Calendar

/**
 *  desc: 申请记录
 *  user: 宋双朋
 *  time: 2024/9/14 9:48
 */
@SuppressLint("SetTextI18n")
class InvoiceRecordFragment : BaseFragment<BaseViewModel>() {

    private val tabType by lazy { arguments?.getInt(TAB_TYPE, TAB_TYPE_1) ?: TAB_TYPE_1 }
    private val mAdapter = InvoiceRecordAdapter()
    private var startTime: String? = null // 开始时间
    private var endTime: String? = null // 结束时间
    private var cargoNameExact: String? = null // 货物名称

    companion object {

        const val TAB_TYPE_1 = 1 // 待审核
        const val TAB_TYPE_2 = 2 // 待开票
        const val TAB_TYPE_3 = 3 // 已驳回/已撤回
        const val TAB_TYPE_4 = 4 // 已开票
        private const val TAB_TYPE = "tabType"

        fun newInstance(tabType: Int): InvoiceRecordFragment {
            val fragment = InvoiceRecordFragment()
            val bundle = Bundle()
            bundle.putInt(TAB_TYPE, tabType)
            fragment.arguments = bundle
            return fragment
        }
    }

    override fun getLayout(): Int {
        return R.layout.invoice_record_fragment
    }

    override fun initData() {
        inputView1.setVisible(false)
        val time1 = Calendar.getInstance().also {
            it.add(Calendar.DAY_OF_MONTH, -90)
        }.time.getFormatTime(YYYY_MM_DD)
        startTime = "$time1 00:00:00"
        val time2 = Calendar.getInstance().time.getFormatTime(YYYY_MM_DD_HH_MM_SS)
        endTime = "$time2 23:59:59"
        setFilterView()
        swipeRefreshMoreLayout.onAutoRefresh()
    }

    private fun invoicesHaveBeenIssued(): Boolean {
        return TAB_TYPE_4 == tabType
    }

    override fun bindView(view: View, bundle: Bundle?) {
        mAdapter.apply {
            tabType = <EMAIL>
        }
        swipeRefreshMoreLayout.apply {
            setLayoutManager(LinearLayoutManager(<EMAIL>))
            setAdapter(mAdapter, true)
            setEmptyView(R.layout.invoice_apply_empty_item_2)
            setOnLoadListener2 { nowPage ->
                if (!invoicesHaveBeenIssued()) {
                    startTime = null
                    endTime = null
                }
                getViewModel(InvoiceModel::class.java).queryApplyList(
                    req = ReqQueryApplyList(
                        nowPage = nowPage,
                        appApplyState = tabType,
                        createTimeS = startTime,
                        createTimeE = endTime,
                        cargoNameExact = cargoNameExact,
                    )
                )
            }
            addOnItemChildClickListener { _, view, position ->
                val item = mAdapter.data[position]
                when (view.id) {
                    R.id.ivRepeal -> {
                        when (tabType) {
                            TAB_TYPE_1 -> {
                                //待审核 撤销
                                val dialogBuilder = DialogBuilder()
                                dialogBuilder.title = "提示"
                                dialogBuilder.message = "撤回功能无需后台介入审核，请确认是否一键撤回已申请发票批次？撤回后请操作【重新申请】，或【删除申请】将数据释放"
                                dialogBuilder.setOkListener { dialog, _ ->
                                    dialog.dismiss()
                                    getViewModel(InvoiceModel::class.java).execute(
                                        ReqRecallApplyOrder(
                                            id = item.id,
                                            applyId = item.applyId,
                                        )
                                    ) { rsp ->
                                        activity?.runOnUiThread {
                                            if (rsp.success()) {
                                                swipeRefreshMoreLayout.onAutoRefresh()
                                            } else {
                                                showToast(rsp.msg)
                                            }
                                        }
                                    }
                                }
                                showDialog(dialogBuilder)
                            }

                            TAB_TYPE_3 -> {
                                //已驳回/已撤回 删除
                                val dialogBuilder = DialogBuilder()
                                dialogBuilder.title = "提示"
                                dialogBuilder.message = "是否确认删除该申请批次，删除后可至【开票申请】重新操作"
                                dialogBuilder.setOkListener { dialog, _ ->
                                    dialog.dismiss()
                                    getViewModel(InvoiceModel::class.java).execute(
                                        ReqDeleteInvoiceApplyOrder(
                                            id = item.id,
                                            applyId = item.applyId,
                                        )
                                    ) { rsp ->
                                        activity?.runOnUiThread {
                                            if (rsp.success()) {
                                                swipeRefreshMoreLayout.onAutoRefresh()
                                            } else {
                                                showToast(rsp.msg)
                                            }
                                        }
                                    }
                                }
                                showDialog(dialogBuilder)
                            }
                        }
                    }

                    R.id.ivReApply -> {
                        // 重新申请
                        InvoiceReApplyActivity.jumpPage(
                            context = <EMAIL>,
                            invoiceRecordItem = item.toJson()
                        )
                    }

                    R.id.detailView -> {
                        //详情
                        InvoiceRecordDetailActivity.jumpPage(context = <EMAIL>, extraData = item.toJson())
                    }
                }
            }
        }
    }

    fun refreshList(startTime: String?, endTime: String?, cargoNameExact: String?) {
        this.startTime = startTime
        this.endTime = endTime
        this.cargoNameExact = cargoNameExact
        setFilterView()
        swipeRefreshMoreLayout.onAutoRefresh()
    }

    private fun setFilterView() {
        tv2.text = "$startTime 至 $endTime"
    }

    @LiveDataMatch
    open fun queryApplyListSuccess(data: PageList<RspQueryApplyList>) {
        swipeRefreshMoreLayout.onRefreshCompale(data)
    }

    @LiveDataMatch
    open fun queryApplyListError() {
        swipeRefreshMoreLayout.onLoadMoreFail()
    }
}