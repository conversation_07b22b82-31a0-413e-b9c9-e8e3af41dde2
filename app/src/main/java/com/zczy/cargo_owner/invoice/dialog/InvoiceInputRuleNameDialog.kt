package com.zczy.cargo_owner.invoice.dialog

import android.os.Bundle
import android.view.View
import com.zczy.cargo_owner.R
import com.zczy.comm.ui.BaseDialog
import kotlinx.android.synthetic.main.invoice_input_rule_name_dialog.editWithCountViewV1
import kotlinx.android.synthetic.main.invoice_input_rule_name_dialog.tvLeft
import kotlinx.android.synthetic.main.invoice_input_rule_name_dialog.tvRight

/**
 *  desc: 设置规则名称
 *  user: 宋双朋
 *  time: 2025/2/6 10:45
 */
class InvoiceInputRuleNameDialog(var onBlock: (ruleName: String) -> Unit = {}) : BaseDialog() {

    override fun bindView(view: View, bundle: Bundle?) {
        tvLeft.setOnClickListener {
            dismiss()
        }
        tvRight.setOnClickListener {
            val ruleName = editWithCountViewV1.getContent()
            dismiss()
            onBlock(ruleName)
        }
    }

    override fun getDialogTag(): String {
        return "InvoiceInputRuleNameDialog"
    }

    override fun getDialogLayout(): Int {
        return R.layout.invoice_input_rule_name_dialog
    }
}