package com.zczy.cargo_owner.invoice.req

import com.zczy.comm.http.entity.BaseNewRequest
import com.zczy.comm.http.entity.BaseRsp
import com.zczy.comm.http.entity.ResultData

/**
 *  desc: 开票方
 *  user: 宋双朋
 *  time: 2024/9/14 14:36
 */
class ReqQuerySubsidiaryList(
    val businessType: String = "1",// 业务类型，汽运传1
) : BaseNewRequest<BaseRsp<RspQuerySubsidiaryListV1>>("ams-app/ams/app/invoice/manage/querySubsidiaryList")

class RspQuerySubsidiaryListV1(
    val subsidiaryList: MutableList<RspQuerySubsidiaryList>? = null,
) : ResultData()

class RspQuerySubsidiaryList(
    val upSettlePartId: String? = null, // 开票方 id
    val upSettlePartName: String? = null, // 开票方名称
)