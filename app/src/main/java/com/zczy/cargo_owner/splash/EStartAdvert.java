package com.zczy.cargo_owner.splash;

import android.text.TextUtils;

import com.zczy.comm.http.entity.ResultData;

/**
 * 功能描述:查询APP启动页，广告
 *
 * <AUTHOR> 孙飞虎
 * @company 中储智运科技股份有限公司
 * @copyright （版权）中储南京智慧物流科技有限公司所有
 * @date 2019/3/19
 */
public class EStartAdvert extends ResultData {
    String picUrl;
    String picJumpLink;
    String picFlag;
    String lastUpdTime;

    public String getLastUpdTime() {
         return TextUtils.isEmpty(lastUpdTime) ? "" : lastUpdTime;
    }
    public String getPicUrl() {
        return TextUtils.isEmpty(picUrl) ? "" : picUrl;
    }

    public String getPicJumpLink() {
        return TextUtils.isEmpty(picJumpLink) ? "" : picJumpLink;
    }

    public String getPicFlag() {
        return TextUtils.isEmpty(picFlag) ? "" : picFlag;
    }

    public boolean isNotEmptyData() {
        return !TextUtils.isEmpty(picUrl);
    }
}
