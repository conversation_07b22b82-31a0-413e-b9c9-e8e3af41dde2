package com.zczy.cargo_owner.splash.dialog;

import android.os.Bundle;
import androidx.annotation.NonNull;
import androidx.annotation.Nullable;
import android.view.View;
import android.widget.TextView;

import com.sfh.lib.AppCacheManager;
import com.zczy.cargo_owner.R;
import com.zczy.comm.ui.BaseDialog;

/**
 * 功能描述:
 *
 * <AUTHOR> 贾发展
 * @company 中储智运科技股份有限公司
 * @copyright （版权）中储南京智慧物流科技有限公司所有
 * @date 2019/12/31
 */
public class TipDialog extends BaseDialog {

    @Override
    protected void bindView(@NonNull View view, @Nullable Bundle bundle) {
        TextView tvOk = view.findViewById(R.id.tvOk);
        tvOk.setOnClickListener(v -> {
            AppCacheManager.putCache("first_dialog_reject", true);
            dismiss();
        });
    }

    @Override
    protected boolean isCancelableOnTouchOutside() {
        return false;
    }

    @Override
    public boolean isCancelable() {
        return false;
    }

    @Override
    protected String getDialogTag() {
        return "TipDialog";
    }

    @Override
    protected int getDialogLayout() {
        return R.layout.dialog_tip;
    }

}
