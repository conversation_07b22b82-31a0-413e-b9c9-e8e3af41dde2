package com.zczy.cargo_owner.splash;

import android.graphics.drawable.Drawable;
import android.os.Bundle;
import androidx.annotation.NonNull;
import androidx.annotation.Nullable;
import androidx.fragment.app.Fragment;
import androidx.core.content.ContextCompat;
import android.view.LayoutInflater;
import android.view.View;
import android.view.ViewGroup;
import android.widget.ImageView;

import com.zczy.cargo_owner.R;
import com.zczy.cargo_owner.home.HomeActivity;
import com.zczy.cargo_owner.user.UserManager;
import com.zczy.cargo_owner.user.login.activity.LoginAccountActivity;

/**
 * 功能描述:
 *
 * <AUTHOR> 孙飞虎
 * @company 中储智运科技股份有限公司
 * @copyright （版权）中储南京智慧物流科技有限公司所有
 * @date 2019/7/24
 */
public class ImageFragment extends Fragment {
    public static ImageFragment newInstance(int position) {

        Bundle args = new Bundle();
        args.putInt("position", position);
        ImageFragment fragment = new ImageFragment();
        fragment.setArguments(args);
        return fragment;
    }

    Drawable drawable;
    ImageView iv;
    View tvOnlicke;


    @Nullable
    @Override
    public View onCreateView(@NonNull LayoutInflater inflater, @Nullable ViewGroup container, @Nullable Bundle savedInstanceState) {

        View view = inflater.inflate(R.layout.home_lead_fragment, container, false);
        iv = view.findViewById(R.id.iv);
        tvOnlicke = view.findViewById(R.id.tv_onlicke);
        return view;
    }

    @Override
    public void onViewCreated(@NonNull View view, @Nullable Bundle savedInstanceState) {
        super.onViewCreated(view, savedInstanceState);
        int position = getArguments().getInt("position");

        drawable = ContextCompat.getDrawable(getContext(), position == 0 ? R.drawable.lead_1 :
                position == 1 ? R.drawable.lead_2 : R.drawable.lead_3
        );
        iv.setImageDrawable(drawable);
        if (position == 2){
            tvOnlicke.setVisibility(View.VISIBLE);
            tvOnlicke.setOnClickListener(new View.OnClickListener() {
                @Override
                public void onClick(View v) {
                    if (UserManager.isLogin()) {
                        HomeActivity.start(getContext());
                    } else {
                        LoginAccountActivity.start(getContext());
                    }
                    getActivity().finish();
                }
            });
        }else {
            tvOnlicke.setVisibility(View.GONE);
        }
    }

    @Override
    public void onDestroy() {
        iv.setImageDrawable(null);
        drawable.setCallback(null);
        drawable = null;
        super.onDestroy();
    }
}
