package com.zczy.cargo_owner.splash;

import android.content.Context;
import android.content.Intent;
import android.net.Uri;
import android.os.Bundle;
import android.view.View;
import android.webkit.WebChromeClient;
import android.webkit.WebSettings;
import android.webkit.WebView;
import android.webkit.WebViewClient;
import android.widget.ProgressBar;

import com.sfh.lib.ui.AbstractLifecycleActivity;
import com.zczy.cargo_owner.R;
import com.zczy.comm.widget.AppToolber;

public class WebActivityJuhe extends AbstractLifecycleActivity {

    private WebView mWebview;
    private ProgressBar myProgressBar;
    AppToolber toolbar;
    public static void jumpToWebActivity(Context context, String jumpUrl, String title) {
        Intent intent = new Intent();
        intent.putExtra("jumpUrl", jumpUrl);
        intent.putExtra("title", title);
        intent.setClass(context, WebActivityJuhe.class);
        context.startActivity(intent);
    }

    @Override
    protected void onCreate(Bundle savedInstanceState) {
        super.onCreate(savedInstanceState);
        setContentView(R.layout.activity_web_juhe);
        initView();
    }

    private void initView() {
        String jumpUrl = getIntent().getStringExtra("jumpUrl");
        String title = getIntent().getStringExtra("title");

        mWebview = (WebView) findViewById(R.id.wb_webview);
        myProgressBar = (ProgressBar) findViewById(R.id.myProgressBar);
        this.toolbar = (AppToolber) this.findViewById(R.id.appToolber);
        this.toolbar.getTvLeft().setText("返回");
        this.toolbar.getTvLeft().setTextSize(0, 46.0F);

        toolbar.setOnClickListener(new View.OnClickListener() {
            @Override
            public void onClick(View v) {
                finish();
            }
        });
        toolbar.setTitle(title);
        WebSettings webSettings = mWebview.getSettings();
        // 支持中文，否则页面中中文显示乱码
        // 设置字符集编码
        webSettings.setDefaultTextEncodingName("UTF-8");
        // 设置响应JS
        webSettings.setJavaScriptEnabled(true);
        webSettings.setJavaScriptCanOpenWindowsAutomatically(true);
        webSettings.setDomStorageEnabled(true);
        webSettings.setCacheMode(WebSettings.LOAD_NO_CACHE);

        webSettings.setBuiltInZoomControls(true);// 设置缩放工具
        webSettings.setDisplayZoomControls(false);// 不显示webview缩放按钮
        webSettings.setSupportZoom(true);// 设置支持缩放
        webSettings.setDefaultFontSize(18);//设置字体大小

        // 网页自适应大小
        webSettings.setLayoutAlgorithm(WebSettings.LayoutAlgorithm.NARROW_COLUMNS);
        webSettings.setUseWideViewPort(true);

        // 禁用 file 协议；
        webSettings.setAllowFileAccess(false);
        webSettings.setAllowFileAccessFromFileURLs(false);
        webSettings.setAllowUniversalAccessFromFileURLs(false);
        //Http和Https混合问题
       /* if (Build.VERSION.SDK_INT >= Build.VERSION_CODES.LOLLIPOP) {
            webSettings.setMixedContentMode(WebSettings.MIXED_CONTENT_ALWAYS_ALLOW);
        }*/
        mWebview.setHorizontalScrollBarEnabled(false);//禁止水平滚动
        mWebview.setVerticalScrollBarEnabled(true);//允许垂直滚动
        mWebview.loadUrl(jumpUrl);

        //设置WebChromeClient类
        mWebview.setWebChromeClient(new WebChromeClient() {
            @Override
            public void onProgressChanged(WebView view, int newProgress) {
                if (newProgress < 100) {
                    myProgressBar.setVisibility(View.VISIBLE);
                    myProgressBar.setProgress(newProgress);
                } else if (newProgress == 100) {
                    myProgressBar.setVisibility(View.GONE);
                }
            }


        });

        mWebview.setWebViewClient(new WebViewClient(){

            @Override
            public boolean shouldOverrideUrlLoading(WebView view, String url) {

                if (url.startsWith("http")) {
                    return super.shouldOverrideUrlLoading(view, url);
                } else {
                    try {
                        Intent intent = new Intent();
                        intent.setAction(Intent.ACTION_VIEW);
                        intent.setData(Uri.parse(url));
                        startActivity(intent);
                        finish();
                        return true;
                    } catch (Exception e) {//防止crash (如果手机上没有安装处理某个scheme开头的url的APP, 会导致crash)
                        return true;//没有安装该app时，返回true，表示拦截自定义链接，但不跳转，避免弹出上面的错误页面
                    }

                }
            }
        });

    }
}