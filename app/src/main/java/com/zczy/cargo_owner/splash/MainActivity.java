package com.zczy.cargo_owner.splash;

import android.app.Activity;
import android.app.Dialog;
import android.os.Bundle;
import android.text.TextUtils;
import android.view.View;
import android.widget.ImageView;


import com.sfh.lib.AppCacheManager;
import com.sfh.lib.mvvm.annotation.LiveDataMatch;
import com.sfh.lib.ui.AbstractLifecycleActivity;
import com.sfh.lib.ui.dialog.DialogBuilder;
import com.zczy.cargo_owner.R;
import com.zczy.cargo_owner.home.HomeActivity;
import com.zczy.cargo_owner.libcomm.config.HttpConfig;
import com.zczy.cargo_owner.splash.dialog.SplashPermissionDialog;
import com.zczy.cargo_owner.user.UserManager;
import com.zczy.cargo_owner.user.login.activity.LoginAccountActivity;
import com.zczy.comm.utils.imgloader.ImgUtil;
import com.zczy.comm.utils.imgloader.Options;
import com.zczy.comm.x5.X5WebActivity;
import com.zczy.lib_zshare.ZShare;
import com.zczy.lib_zshare.share.ShareInfo;
import com.zczy.version.sdk.ZVersionManager;

/***
 * 启动页
 */
public class MainActivity extends AbstractLifecycleActivity<SplashModel> {

    boolean firstStart = AppCacheManager.getCache("first_start", Boolean.class, true);

    @Override
    protected void onCreate(Bundle savedInstanceState) {

        super.onCreate(savedInstanceState);
        setContentView(R.layout.splash_activity);
        if (firstStart) {
            this.ShowSplashPermissionDialog();
        } else {
            getViewModel(SplashModel.class).init(this);
        }
    }

    private void ShowSplashPermissionDialog() {

        new SplashPermissionDialog(this, new SplashPermissionDialog.LicenseOnClickListener() {
            @Override
            public void agree(Dialog dialog) {
                AppCacheManager.putCache("first_start", false);
                getViewModel(SplashModel.class).init(MainActivity.this);
            }

            @Override
            public void reject(Dialog dialog) {

                DialogBuilder builder = new DialogBuilder();
                builder.setTitle("同意隐私政策才能继续使用中储智运");
                builder.setMessage("为了保证您正常，安全的使用中储智运APP ,您需要阅读并同意我们的隐私政策,如果不同意本隐私政策，很遗憾中储智运将无法为您提供服务。");
                builder.setCancelTextColor("仍不同意", R.color.color_b2b2b2);
                builder.setCancelListener((dialog2, which) -> {
                    dialog2.dismiss();
                    dialog.dismiss();
                    System.exit(0);
                });
                builder.setOKTextColor("查看协议", R.color.color_5086fc);
                builder.setOkListener((dialog1, which) -> dialog1.dismiss());
                builder.setHideCancel(false);
                showDialog(builder);
            }
        }).show();
    }

    @LiveDataMatch(tag = "显示广告")
    public void onAdvertSuccess(EStartAdvert advert) {

        if (advert == null) {
            onGotoHomeUI();
            return;
        }
        ImageView iv_advert = findViewById(R.id.iv_advert);
        iv_advert.setOnClickListener((View v) -> {
            if (advert == null || TextUtils.isEmpty(advert.getPicJumpLink())) {
                return;
            }
            onGotoHomeUI();
            X5WebActivity.listener = v1 -> {
                // 通过View获取Activity上下文，并检查Activity是否仍然有效
                if (v1 != null && v1.getContext() instanceof Activity) {
                    Activity activity = (Activity) v1.getContext();
                    if (!activity.isFinishing() && !activity.isDestroyed()) {
                        ShareInfo info = new ShareInfo();
                        info.title = X5WebActivity.text;
                        info.content = X5WebActivity.text;
                        info.webUrl = X5WebActivity.Shareurl;
                        ZShare.share(activity, info);
                    }
                }
            };
            X5WebActivity.startContentUI(MainActivity.this, advert.getPicJumpLink());

        });
        iv_advert.setVisibility(View.VISIBLE);
        AdvertRxTimeButton tv_time = findViewById(R.id.tv_time);
        tv_time.setVisibility(View.VISIBLE);
        tv_time.startInterval(5);
        tv_time.setIntervalListener(() -> {
            onGotoHomeUI();
        });
        ImgUtil.loadUrl(iv_advert, HttpConfig.getUrlImage(advert.getPicUrl()), Options.creator().setPlaceholder(-1).setSkipMemoryCache(true));

    }

    @LiveDataMatch(tag = "进入主页")
    public void onGotoHomeUI() {

        if (UserManager.isLogin()) {
            HomeActivity.start(this);
        } else {
            LoginAccountActivity.start(this);
        }
        ZVersionManager.getInstance().start();
        finish();
    }

}
