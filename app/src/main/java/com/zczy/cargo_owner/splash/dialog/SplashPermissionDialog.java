package com.zczy.cargo_owner.splash.dialog;

import android.app.AlertDialog;
import android.app.Dialog;
import android.content.Context;
import android.graphics.Color;
import android.graphics.Typeface;
import android.os.Bundle;
import androidx.annotation.NonNull;
import android.text.Spannable;
import android.text.SpannableString;
import android.text.SpannableStringBuilder;
import android.text.TextPaint;
import android.text.method.LinkMovementMethod;
import android.text.style.ClickableSpan;
import android.view.View;
import android.widget.TextView;

import com.zczy.cargo_owner.R;
import com.zczy.cargo_owner.libcomm.config.HttpConfig;
import com.zczy.cargo_owner.splash.WebActivityJuhe;


/**
 * <AUTHOR>
 */
public class SplashPermissionDialog extends AlertDialog {

    public interface LicenseOnClickListener {
        void agree(Dialog dialog);

        void reject(Dialog dialog);
    }

    LicenseOnClickListener listener;

    public SplashPermissionDialog(Context context, LicenseOnClickListener listener) {
        super(context, R.style.dialog_Fullscreen);
        this.listener = listener;
    }

    @Override
    protected void onCreate(Bundle savedInstanceState) {
        super.onCreate(savedInstanceState);
        setContentView(R.layout.splash_permission_dialog2);
        setCanceledOnTouchOutside(false);
        setCancelable(false);

        findViewById(R.id.btn_cancel).setOnClickListener(v -> {
            if (listener != null) {
                listener.reject(SplashPermissionDialog.this);
            }
        });
        findViewById(R.id.btn_accept).setOnClickListener(v -> {
            this.dismiss();
            if (listener != null) {
                listener.agree(SplashPermissionDialog.this);
            }
        });

        TextView tv_content = findViewById(R.id.tv_content);
        tv_content.setMovementMethod(LinkMovementMethod.getInstance());

        SpannableStringBuilder builder = new SpannableStringBuilder();
        builder.append("感谢您信任并使用中储智运！我们依据最新法律要求，更新了隐私政策，请您仔细阅读并充分了解我们对您个人信息的处理规则，其中的重点条款已为您标注，方便您了解自己的权利。")
                .append("为了提供优质的服务,应用启动需要联网，当您开始使用本软件时，为保证相关产品功能正常体验，在使用过程中可能会收据您以下信息:位置信息(提供附近车辆信息和周边服务、推送专属优惠)")
                .append("、存储(保存图片、回单上传图片/视频)")
                .append("、设备信息(保障账号风控与交易安全)等信息，您有权拒绝与取消授权。")
                .append("\n\n在您使用App之前，请您阅读并充分理解");

        SpannableString txt = new SpannableString("《隐私政策》");
        txt.setSpan(new ClickableSpan() {
            @Override
            public void onClick(View view) {
                String url = HttpConfig.getWebUrl("/form_h5/documents/privacy_owner.html");
                WebActivityJuhe.jumpToWebActivity(getContext(), url, "隐私政策");

            }

            @Override
            public void updateDrawState(@NonNull TextPaint ds) {
                ds.setUnderlineText(false);
                ds.setTypeface(Typeface.DEFAULT_BOLD);
                ds.setColor(Color.parseColor("#5086FC"));
            }
        }, 0, txt.length(), Spannable.SPAN_EXCLUSIVE_EXCLUSIVE);
        builder.append(txt);

        SpannableString txt2 = new SpannableString("《用户授权协议》");
        txt2.setSpan(new ClickableSpan() {
            @Override
            public void onClick(@NonNull View widget) {
                String url = HttpConfig.getWebUrl("form_h5/documents/authorize.html");
                WebActivityJuhe.jumpToWebActivity(getContext(), url, "用户授权协议");
            }

            @Override
            public void updateDrawState(@NonNull TextPaint ds) {
                ds.setUnderlineText(false);
                ds.setTypeface(Typeface.DEFAULT_BOLD);
                ds.setColor(Color.parseColor("#5086FC"));
            }
        }, 0, txt2.length(), Spannable.SPAN_EXCLUSIVE_EXCLUSIVE);
        builder.append(txt2);

        builder.append("。如您同意以上内容，请点击“同意”并开始使用我们的产品与服务。\n\n我们将尽全力保护您的个人信息及合法权益，再次感谢您的信任!");
        tv_content.setText(builder);

    }
}
