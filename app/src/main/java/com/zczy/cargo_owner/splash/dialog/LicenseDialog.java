package com.zczy.cargo_owner.splash.dialog;

import android.graphics.Color;
import android.graphics.Typeface;
import android.os.Bundle;
import androidx.annotation.NonNull;
import androidx.annotation.Nullable;
import android.text.Spannable;
import android.text.SpannableStringBuilder;
import android.text.Spanned;
import android.text.TextPaint;
import android.text.method.LinkMovementMethod;
import android.text.style.ClickableSpan;
import android.text.style.ForegroundColorSpan;
import android.text.style.StyleSpan;
import android.view.View;
import android.widget.TextView;

import com.zczy.cargo_owner.R;
import com.zczy.cargo_owner.libcomm.config.HttpConfig;

import com.zczy.cargo_owner.splash.WebActivityJuhe;
import com.zczy.comm.ui.BaseDialog;

/**
 * 功能描述:
 *
 * <AUTHOR> 贾发展
 * @company 中储智运科技股份有限公司
 * @copyright （版权）中储南京智慧物流科技有限公司所有
 * @date 2019/12/31
 */
public class LicenseDialog extends BaseDialog {
    private LicenseOnClickListener onClickListener;

    public LicenseDialog setOnClickListener(LicenseOnClickListener onClickListener) {
        this.onClickListener = onClickListener;
        return this;
    }

    public interface LicenseOnClickListener {
        void agree();

        void reject();
    }

    @Override
    protected void bindView(@NonNull View view, @Nullable Bundle bundle) {
        TextView tvDesc = view.findViewById(R.id.tvDesc);
        TextView btnReject = view.findViewById(R.id.btn_reject);
        btnReject.setOnClickListener(v -> {
            if (onClickListener != null) {
                onClickListener.reject();
            }
        });
        TextView btnAgree = view.findViewById(R.id.btn_agree);
        btnAgree.setOnClickListener(v -> {
            if (onClickListener != null) {
                onClickListener.agree();
                dismiss();
            }
        });
        SpannableStringBuilder spannableString = new SpannableStringBuilder();
        spannableString.append("请注意 您点击同意即表示您已阅读并同意我们的《隐私政策》《用户授权协议》。我们将尽全力保护您的个人信息及合法权益，再次感谢您的信任!");
        ClickableSpan clickableSpan = new ClickableSpan() {
            @Override
            public void onClick(View view) {
                String url = HttpConfig.getWebUrl("form_h5/documents/privacy_owner.html");
                WebActivityJuhe.jumpToWebActivity(getContext(), url, "隐私政策");
            }

            @Override
            public void updateDrawState(@NonNull TextPaint ds) {
                ds.setUnderlineText(false);
            }
        };
        spannableString.setSpan(new StyleSpan(Typeface.BOLD), 0, 3, Spanned.SPAN_INCLUSIVE_INCLUSIVE);
        spannableString.setSpan(clickableSpan, 22, 28, Spannable.SPAN_INCLUSIVE_INCLUSIVE);
        spannableString.setSpan(new ClickableSpan() {
            @Override
            public void onClick(@NonNull View widget) {
                String url = HttpConfig.getWebUrl("form_h5/documents/authorize.html");
                WebActivityJuhe.jumpToWebActivity(getContext(), url, "用户授权协议");
            }

            @Override
            public void updateDrawState(@NonNull TextPaint ds) {
                ds.setUnderlineText(false);
            }
        }, 28, 36, Spannable.SPAN_INCLUSIVE_INCLUSIVE);
        spannableString.setSpan(new ForegroundColorSpan(Color.parseColor("#5086FC")), 22, 28, Spanned.SPAN_INCLUSIVE_INCLUSIVE);
        spannableString.setSpan(new ForegroundColorSpan(Color.parseColor("#5086FC")), 28, 36, Spanned.SPAN_INCLUSIVE_INCLUSIVE);
        tvDesc.setText(spannableString);
        tvDesc.setMovementMethod(LinkMovementMethod.getInstance());

    }

    @Override
    protected boolean isCancelableOnTouchOutside() {
        return false;
    }

    @Override
    public boolean isCancelable() {
        return false;
    }

    @Override
    protected String getDialogTag() {
        return "LicenseDialog";
    }

    @Override
    protected int getDialogLayout() {
        return R.layout.dialog_license;
    }
}
