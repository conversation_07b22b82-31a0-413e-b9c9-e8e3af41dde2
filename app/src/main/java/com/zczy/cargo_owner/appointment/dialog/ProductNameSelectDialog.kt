package com.zczy.cargo_owner.appointment.dialog

import android.os.Bundle
import android.view.View
import androidx.recyclerview.widget.LinearLayoutManager
import com.chad.library.adapter.base.BaseQuickAdapter
import com.chad.library.adapter.base.BaseViewHolder
import com.zczy.cargo_owner.R
import com.zczy.cargo_owner.deliver.bean.DeliverCargoNameData
import com.zczy.comm.ui.BaseDialog
import com.zczy.comm.ui.BaseDialog.DialogType
import kotlinx.android.synthetic.main.dialog_product_name_select.*

/**
 * PS: 品名选择弹窗
 * Created by AI Assistant on 2025/01/20.
 */
class ProductNameSelectDialog(
    private val cargoNameList: List<DeliverCargoNameData>,
    private val selectedCargoName: String? = null,
    private val onItemSelected: (DeliverCargoNameData) -> Unit
) : BaseDialog() {

    private lateinit var mAdapter: ProductNameAdapter
    private var selectedItem: DeliverCargoNameData? = null

    override fun getDialogLayout(): Int = R.layout.dialog_product_name_select

    override fun getDialogTag(): String = "ProductNameSelectDialog"

    override fun getDialogType(): DialogType = DialogType.bottom

    override fun bindView(view: View, bundle: Bundle?) {
        // 不设置 reAdjustView，使用布局自身的高度控制

        // 设置标题
        tv_title.text = "品名"

        // 设置关闭按钮
        iv_close.setOnClickListener {
            dismiss()
        }

        // 设置确定按钮
        btn_confirm.setOnClickListener {
            selectedItem?.let { onItemSelected(it) }
            dismiss()
        }

        // 初始化RecyclerView
        setupRecyclerView()
    }

    private fun setupRecyclerView() {
        mAdapter = ProductNameAdapter()
        recycler_view.layoutManager = LinearLayoutManager(context)
        recycler_view.adapter = mAdapter

        // 过滤只显示审核通过的品名 (state = "1")
        val filteredList = cargoNameList.filter { it.state == "1" }

        // 设置当前选中的项
        selectedItem = filteredList.find { it.baseName == selectedCargoName }

        mAdapter.setNewData(filteredList)

        mAdapter.setOnItemClickListener { adapter, view, position ->
            val item = adapter.getItem(position) as DeliverCargoNameData
            // 更新选中状态
            selectedItem = item
            mAdapter.notifyDataSetChanged()
        }
    }

    /**
     * 品名适配器
     */
    private inner class ProductNameAdapter : BaseQuickAdapter<DeliverCargoNameData, BaseViewHolder>(R.layout.item_product_name_select) {
        override fun convert(helper: BaseViewHolder, item: DeliverCargoNameData) {
            helper.setText(R.id.tv_product_name, item.baseName)

            // 设置选中状态
            val isSelected = selectedItem?.baseName == item.baseName
            helper.setVisible(R.id.iv_selected, isSelected)
        }
    }

    companion object {
        /**
         * 显示品名选择弹窗
         */
        fun show(
            cargoNameList: List<DeliverCargoNameData>,
            selectedCargoName: String? = null,
            onItemSelected: (DeliverCargoNameData) -> Unit
        ): ProductNameSelectDialog {
            return ProductNameSelectDialog(cargoNameList, selectedCargoName, onItemSelected)
        }
    }
}
