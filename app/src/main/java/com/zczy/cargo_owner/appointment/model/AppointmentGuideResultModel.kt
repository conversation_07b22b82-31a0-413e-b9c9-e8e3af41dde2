package com.zczy.cargo_owner.appointment.model

import androidx.lifecycle.MutableLiveData
import com.sfh.lib.exception.HandleException
import com.sfh.lib.mvvm.service.BaseViewModel
import com.sfh.lib.rx.IResult
import com.zczy.cargo_owner.appointment.req.ReqAppointmentGuideResult
import com.zczy.cargo_owner.appointment.req.RspAppointmentGuideResultData
import com.zczy.comm.http.entity.BaseRsp
import com.zczy.comm.http.entity.PageList

/**
 * PS: 预约指导外呼结果ViewModel
 * Created by AI Assistant on 2025/01/20.
 */
class AppointmentGuideResultModel : BaseViewModel() {

    /**
     * 获取指导结果列表
     */
    fun getResultList(
        nowPage: Int,
        callOutGuideId: String
    ) {
        val req = ReqAppointmentGuideResult(
            pageNum = nowPage,
            pageSize = 10,
            callOutGuideId = callOutGuideId
        )
        
        this.execute(req, object : IResult<BaseRsp<PageList<RspAppointmentGuideResultData>>> {
            override fun onSuccess(t: BaseRsp<PageList<RspAppointmentGuideResultData>>) {
                if (t.success()) {
                    setValue("onGetResultListSuccess", t.data)
                } else {
                    showDialogToast(t.msg)
                    setValue("onGetResultListSuccess", null)
                }
            }

            override fun onFail(e: HandleException) {
                showDialogToast(e.msg)
                setValue("onGetResultListSuccess", null)
            }
        })
    }
}
