package com.zczy.cargo_owner.appointment.req

import com.zczy.comm.http.entity.BaseNewRequest
import com.zczy.comm.http.entity.BaseRsp
import com.zczy.comm.http.entity.ResultData
import java.io.Serializable

/**
 * PS: 查询货主是否维护
 * Created by AI Assistant on 2025/01/20.
 */
class ReqQueryConsignorMaintenance(
) : BaseNewRequest<BaseRsp<RspQueryConsignorMaintenance>>("/wo-app/callOut/appointmentGuide/queryConsignorMaintenance")

/**
 * 查询货主维护状态响应
 */
class RspQueryConsignorMaintenance(
    var maintenanceFlag: Int = 0 // 是否维护 0：否 1：是
) : ResultData()
