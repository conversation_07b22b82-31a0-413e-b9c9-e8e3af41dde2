package com.zczy.cargo_owner.appointment.req

import com.zczy.comm.http.entity.BaseNewRequest
import com.zczy.comm.http.entity.BaseRsp
import com.zczy.comm.http.entity.ResultData

/**
 * 新增-车牌号校验 1001该车牌号已存在未完结的外呼母单
 * http://wiki.zczy56.com/pages/viewpage.action?pageId=91586604
 */
data class ReqHaveUnfinishedOutCall(
    var carrierVehicleNum: String = "", // 车牌号
) : BaseNewRequest<BaseRsp<ResultData>>("wo-app/callOut/appointmentGuide/haveUnfinishedOutCall")



