package com.zczy.cargo_owner.appointment.req

import com.zczy.comm.http.entity.BaseNewRequest
import com.zczy.comm.http.entity.BaseRsp
import com.zczy.comm.http.entity.PageList
import com.zczy.comm.http.entity.ResultData

/**
 * 收发货人地址列表接口
 * http://wiki.zczy56.com/pages/viewpage.action?pageId=91586604
 */
data class ReqQueryConsignorAddressList(
    var pageNum: Int = 1,
    var pageSize : Int = 10,
    var consType: String = "" //收/发货人类型:1 发货人，2 收货人
) : BaseNewRequest<BaseRsp<PageList<ConsignorAddressItem>>>("wo-app/callOut/appointmentGuide/queryConsignorAddressList")

data class ConsignorAddressItem(
    val id: String,
    val userId: String,
    val customerId: String,
    val childUserId: String?,
    val childUserIds: String?,
    val consignorCompany: String,
    val consignorUserName: String,
    val consignorChildName: String?,
    val consType: Int,
    val consName: String,
    val consMobile: String,
    val consProvince: String,
    val consCity: String,
    val consArea: String,
    val consDetailAddr: String,
    val defaultType: Int,
    val childDefaultType: String?,
    val consCompanyName: String,
    val consCompanyCode: String?,
    val createdBy: String,
    val createdByName: String,
    val createdTime: String,
    val lastUptBy: String,
    val lastUptByName: String,
    val lastUptTime: String,
    val deleteFlag: Int,
    val spareMobile: String?,
    val consignorUserId: String?,
    val consCoordinateX: String,
    val consCoordinateY: String,
    val consProCode: String,
    val consCityCode: String,
    val consDisCode: String,
    val source: Int,
    val version: String?,
    val consLocationAddr: String,
    val fenceRange: Int,
    val userType: String?,
    val childOperateAllFlag: String?,
    val userName: String?,
    val deliverConfirmFlag: Int,
    val loadingLimitationType: String?,
    val loadingLimitationRange: String?
)
