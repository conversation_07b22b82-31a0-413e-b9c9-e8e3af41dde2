package com.zczy.cargo_owner.appointment.adapter

import android.graphics.Color
import com.chad.library.adapter.base.BaseQuickAdapter
import com.chad.library.adapter.base.BaseViewHolder
import com.zczy.cargo_owner.R
import com.zczy.cargo_owner.appointment.req.RspAppointmentGuideResultData
import com.zczy.cargo_owner.appointment.req.formatMessageType
import com.zczy.cargo_owner.appointment.req.getMessageTypeColor

/**
 * PS: 预约指导外呼结果列表适配器
 * Created by AI Assistant on 2025/01/20.
 */
class AppointmentGuideResultAdapter
    : BaseQuickAdapter<RspAppointmentGuideResultData, BaseViewHolder>(R.layout.appointment_guide_result_item) {

    override fun convert(helper: BaseViewHolder, item: RspAppointmentGuideResultData) {
        // 设置时间
        helper.setText(R.id.tv_time, item.createdTime)

        // 设置消息类型
        helper.setText(R.id.tv_message_type, item.formatMessageType())
        helper.setTextColor(R.id.tv_message_type, Color.parseColor(item.getMessageTypeColor()))

        // 设置消息内容
        helper.setText(R.id.tv_message_content, item.messageContent)

        // 根据消息类型设置不同的样式
        when (item.messageType) {
            1 -> {
                // 客服消息 - 蓝色圆点
                helper.setBackgroundRes(R.id.iv_message_dot, R.drawable.shape_blue_circle)
            }
            2 -> {
                // 货主消息 - 灰色圆点
                helper.setBackgroundRes(R.id.iv_message_dot, R.drawable.shape_gray_circle)
            }
            else -> {
                // 默认灰色圆点
                helper.setBackgroundRes(R.id.iv_message_dot, R.drawable.shape_gray_circle)
            }
        }

        // 如果是最后一个item，隐藏连接线
        val isLastItem = helper.adapterPosition == data.size - 1
        helper.setVisible(R.id.view_timeline_line, !isLastItem)
    }
}
