package com.zczy.cargo_owner.appointment.dialog

import android.os.Bundle
import android.view.View
import androidx.recyclerview.widget.LinearLayoutManager
import com.chad.library.adapter.base.BaseQuickAdapter
import com.chad.library.adapter.base.BaseViewHolder
import com.zczy.cargo_owner.R
import com.zczy.cargo_owner.appointment.req.VehicleInfo
import com.zczy.comm.ui.BaseDialog
import com.zczy.comm.ui.BaseDialog.DialogType
import kotlinx.android.synthetic.main.dialog_plate_number_select.*

/**
 * PS: 车牌号选择弹窗
 * Created by AI Assistant on 2025/01/20.
 */
class PlateNumberSelectDialog(
    private val vehicleList: List<VehicleInfo>,
    private val selectedPlateNumber: String? = null,
    private val onItemSelected: (VehicleInfo) -> Unit
) : BaseDialog() {

    private lateinit var mAdapter: PlateNumberAdapter
    private var selectedItem: VehicleInfo? = null

    override fun getDialogLayout(): Int = R.layout.dialog_plate_number_select

    override fun getDialogTag(): String = "PlateNumberSelectDialog"

    override fun getDialogType(): DialogType = DialogType.bottom

    override fun bindView(view: View, bundle: Bundle?) {
        // 不设置 reAdjustView，使用布局自身的高度控制

        // 设置标题
        tv_title.text = "车牌号"

        // 设置关闭按钮
        iv_close.setOnClickListener {
            dismiss()
        }

        // 设置确定按钮
        btn_confirm.setOnClickListener {
            selectedItem?.let { onItemSelected(it) }
            dismiss()
        }

        // 初始化RecyclerView
        setupRecyclerView()
    }

    private fun setupRecyclerView() {
        mAdapter = PlateNumberAdapter()
        recycler_view.layoutManager = LinearLayoutManager(context)
        recycler_view.adapter = mAdapter

        // 过滤只显示有车牌号的车辆
        val filteredList = vehicleList.filter { it.plateNumber.isNotEmpty() }

        // 设置当前选中的项
        selectedItem = filteredList.find { it.plateNumber == selectedPlateNumber }

        mAdapter.setNewData(filteredList)

        mAdapter.setOnItemClickListener { adapter, view, position ->
            val item = adapter.getItem(position) as VehicleInfo
            // 更新选中状态
            selectedItem = item
            mAdapter.notifyDataSetChanged()
        }
    }

    /**
     * 车牌号适配器
     */
    private inner class PlateNumberAdapter : BaseQuickAdapter<VehicleInfo, BaseViewHolder>(R.layout.item_plate_number_select) {
        override fun convert(helper: BaseViewHolder, item: VehicleInfo) {
            helper.setText(R.id.tv_plate_number, item.plateNumber)
            
            // 显示车辆详细信息
            val vehicleInfo = buildString {
                if (item.vehicleType.isNotEmpty()) {
                    append("车型：${item.vehicleType}")
                }
                if (item.vehicleLength > 0) {
                    if (isNotEmpty()) append(" | ")
                    append("车长：${item.vehicleLength}m")
                }
                if (item.vehicleLoad > 0) {
                    if (isNotEmpty()) append(" | ")
                    append("载重：${item.vehicleLoad}t")
                }
            }
            helper.setText(R.id.tv_vehicle_info, vehicleInfo)
            helper.setVisible(R.id.tv_vehicle_info, vehicleInfo.isNotEmpty())

            // 设置选中状态
            val isSelected = selectedItem?.plateNumber == item.plateNumber
            helper.setVisible(R.id.iv_selected, isSelected)
        }
    }

    companion object {
        /**
         * 显示车牌号选择弹窗
         */
        fun show(
            vehicleList: List<VehicleInfo>,
            selectedPlateNumber: String? = null,
            onItemSelected: (VehicleInfo) -> Unit
        ): PlateNumberSelectDialog {
            return PlateNumberSelectDialog(vehicleList, selectedPlateNumber, onItemSelected)
        }
    }
}
