package com.zczy.cargo_owner.appointment.req

import com.zczy.comm.http.entity.BaseNewRequest
import com.zczy.comm.http.entity.BaseRsp
import com.zczy.comm.http.entity.PageList
import com.zczy.comm.http.entity.ResultData
import java.io.Serializable

/**
 * PS: 查询预约指导外呼结果
 * Created by AI Assistant on 2025/01/20.
 */
data class ReqAppointmentGuideResult(
    var pageNum: Int = 1,
    var pageSize: Int = 10,
    var callOutGuideId: String = ""
) : BaseNewRequest<BaseRsp<PageList<RspAppointmentGuideResultData>>>("wo-app/callOut/appointmentGuide/queryAppointmentGuideMessage")

/**
 * 指导外呼结果数据
 */
data class RspAppointmentGuideResultData(
    var callOutGuideLogId: String = "", // 指导外呼日志ID
    var createdTime: String = "", // 创建时间
    var messageContent: String = "", // 留言内容
    var messageType: Int = 0 // 处理方类型:1.客服,2.货主
) : ResultData(), Serializable

/**
 * 格式化消息类型
 */
fun RspAppointmentGuideResultData.formatMessageType(): String {
    return when (messageType) {
        1 -> "客服"
        2 -> "货主"
        else -> ""
    }
}

/**
 * 获取消息类型颜色
 */
fun RspAppointmentGuideResultData.getMessageTypeColor(): String {
    return when (messageType) {
        1 -> "#FF5086FC" // 蓝色 - 客服
        2 -> "#FF666666" // 灰色 - 货主
        else -> "#FF666666"
    }
}
