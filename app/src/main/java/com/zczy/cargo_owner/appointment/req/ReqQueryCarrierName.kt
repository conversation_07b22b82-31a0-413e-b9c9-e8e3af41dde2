package com.zczy.cargo_owner.appointment.req

import com.zczy.comm.http.entity.BaseNewRequest
import com.zczy.comm.http.entity.BaseRsp
import com.zczy.comm.http.entity.ResultData
import java.io.Serializable

/**
 * PS: 根据承运方联系号码查询承运方姓名和车牌号
 * Created by AI Assistant on 2025/01/20.
 */
data class ReqQueryCarrierName(
    var carrierMobile: String = ""
) : BaseNewRequest<BaseRsp<RspQueryCarrierName>>("/wo-app/callOut/appointmentGuide/queryCarrierName")

/**
 * 查询承运方信息响应
 */
data class RspQueryCarrierName(
    var mobile: Long = 0,
    var userId: Long = 0,
    var customerName: String = "",
    var vehicleList: List<VehicleInfo> = emptyList()
) : ResultData(), Serializable

/**
 * 车辆信息
 */
data class VehicleInfo(
    var vehicleLength: Double = 0.0,
    var vehicleWidth: Double = 0.0,
    var vehicleLoad: Double = 0.0,
    var vehicleHeight: Double = 0.0,
    var vehicleId: Long = 0,
    var plateNumber: String = "",
    var vehicleType: String = ""
) : Serializable
