package com.zczy.cargo_owner.appointment.req

import com.zczy.comm.http.entity.BaseNewRequest
import com.zczy.comm.http.entity.BaseRsp
import com.zczy.comm.http.entity.ResultData

/**
 * PS: 预约指导外呼留言请求
 * Created by AI Assistant on 2025/01/20.
 */
data class ReqAppointmentGuideMessage(
    var callOutGuideId: String = "", // 预约指导外呼ID (使用callOutGuideCode)
    var messageContent: String = "" // 留言内容
) : BaseNewRequest<BaseRsp<ResultData>>("wo-app/callOut/appointmentGuide/appointmentGuideMessage")
