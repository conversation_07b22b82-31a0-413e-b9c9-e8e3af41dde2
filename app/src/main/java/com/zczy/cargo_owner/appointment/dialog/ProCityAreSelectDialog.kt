package com.zczy.cargo_owner.appointment.dialog

import android.os.Bundle
import android.text.TextUtils
import android.view.View
import androidx.fragment.app.FragmentActivity
import com.chad.library.adapter.base.BaseQuickAdapter
import com.chad.library.adapter.base.BaseViewHolder
import com.sfh.lib.exception.HandleException
import com.sfh.lib.mvvm.service.BaseViewModel
import com.sfh.lib.rx.IResult
import com.zczy.cargo_owner.R
import com.zczy.cargo_owner.appointment.req.ConsignorAddressItem
import com.zczy.cargo_owner.appointment.req.ReqQueryConsignorAddressList
import com.zczy.cargo_owner.deliver.addorder.DeliverAddOrderMainActivity
import com.zczy.comm.http.entity.BaseRsp
import com.zczy.comm.http.entity.PageList
import com.zczy.comm.ui.BaseDialog
import kotlinx.android.synthetic.main.dialog_pro_city_ares_select.*

/**
 * PS: 收发货地址选择
 * Created by AI Assistant on 2025/01/20.
 */
class ProCityAreSelectDialog(
    private val baseViewModel: BaseViewModel,
    private val consType: Int? ,
    private val onItemSelected: (ConsignorAddressItem) -> Unit
) : BaseDialog() {


    override fun getDialogLayout(): Int = R.layout.dialog_pro_city_ares_select

    override fun getDialogTag(): String = "ProCityAreSelectDialog"

    override fun getDialogType(): DialogType = DialogType.full

    override fun bindView(view: View, bundle: Bundle?) {
        // 不设置 reAdjustView，使用布局自身的高度控制

        // 设置标题
        tv_title.text = if (consType == 1) "启运地" else "目的地"

        // 设置关闭按钮
        iv_close.setOnClickListener {
            dismiss()
        }
        var mAdapter = ProNameAdapter()
        recycler_view.setAdapter(mAdapter,true)
        recycler_view.addItemDecorationSize(1)
        recycler_view.setOnLoadListener2 {
            baseViewModel.execute(ReqQueryConsignorAddressList(pageNum = it, pageSize = 10, consType = "${consType}"),object :IResult<BaseRsp<PageList<ConsignorAddressItem>>>{
                override fun onSuccess(t: BaseRsp<PageList<ConsignorAddressItem>>) {
                    if (t.success()){
                        recycler_view.onRefreshCompale(t.data)
                    }else{
                        baseViewModel.showToast(t.msg)
                        recycler_view.onLoadMoreFail()
                    }
                }

                override fun onFail(e: HandleException) {
                    baseViewModel.showToast(e.msg)
                    recycler_view.onLoadMoreFail()
                }
            })
        }
        recycler_view.addOnItemListener { adapter, view, position ->
            if (adapter.getItem(position) is ConsignorAddressItem) {
                val item = adapter.getItem(position) as ConsignorAddressItem
                // 触发点击事件
                mAdapter.selectedItem = item
                mAdapter.notifyDataSetChanged()
            }
        }
        recycler_view.onAutoRefresh()
        tv_add_page.setOnClickListener {
            dismiss()
            // 跳转货主发单页面
            context?.let {
                DeliverAddOrderMainActivity.start(it)
            }
        }
        tv_ok.setOnClickListener {
            if (mAdapter.selectedItem == null){
                baseViewModel.showToast("请选择地址")
                return@setOnClickListener
            }
            dismiss()
            onItemSelected(mAdapter.selectedItem!!)
        }
    }



    private inner class ProNameAdapter : BaseQuickAdapter<ConsignorAddressItem, BaseViewHolder>(R.layout.item_pro_city_select) {
        var selectedItem: ConsignorAddressItem? = null
        override fun convert(helper: BaseViewHolder, item: ConsignorAddressItem) {
            helper.setText(R.id.tv_pro, "${item.consProvince} ${item.consCity} ${item.consArea}".trim())
            helper.setText(R.id.tv_address, item.consDetailAddr)
            helper.setGone(R.id.iv_select,selectedItem != null && TextUtils.equals(selectedItem?.id,item.id))
        }
    }

    companion object {
        /**
         * 显示选择弹窗
         */
        fun show(
            activity: FragmentActivity,
            baseViewModel: BaseViewModel,
            consType: Int?,
            onItemSelected: (ConsignorAddressItem) -> Unit
        ) {
            return ProCityAreSelectDialog(baseViewModel, consType, onItemSelected).show(activity)
        }
    }
}
