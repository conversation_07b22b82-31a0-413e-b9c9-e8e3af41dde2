package com.zczy.cargo_owner.cement

import android.annotation.SuppressLint
import android.content.Context
import android.content.Intent
import android.os.Bundle
import android.text.Editable
import android.text.InputFilter
import android.text.TextUtils
import android.text.TextWatcher
import com.sfh.lib.event.RxBusEventManager
import com.sfh.lib.mvvm.annotation.LiveDataMatch
import com.zczy.cargo_owner.R
import com.zczy.cargo_owner.cement.model.CementNoticeAddEditModel
import com.zczy.cargo_owner.cement.req.ReqCementNoticeAddEdit
import com.zczy.cargo_owner.cement.req.RspCementNoticeDetail
import com.zczy.cargo_owner.cement.req.RxCementNoticeAddEditSuccess
import com.zczy.comm.ui.BaseActivity
import com.zczy.comm.widget.inputv2.InputViewCheckV2
import kotlinx.android.synthetic.main.cement_notice_add_edit_activity.*

/**
 *  user: ssp
 *  time: 2021/5/21 14:13
 *  desc: 卸车通知
 */
class CementNoticeAddEditActivity : BaseActivity<CementNoticeAddEditModel>() {

    private val edit by lazy { intent.getBooleanExtra(EDIT, false) }
    private val noticeId by lazy { intent.getStringExtra(NOTICE_ID) }

    companion object {

        const val EDIT = "edit"
        const val NOTICE_ID = "noticeId"

        @JvmStatic
        fun jumpUi(context: Context, edit: Boolean, noticeId: String) {
            val intent = Intent(context, CementNoticeAddEditActivity::class.java)
            intent.putExtra(EDIT, edit)
            intent.putExtra(NOTICE_ID, noticeId)
            context.startActivity(intent)
        }
    }

    override fun getLayout(): Int {
        return R.layout.cement_notice_add_edit_activity
    }

    override fun initData() {
        if (edit) {
            viewModel?.queryDetail(noticeId?:"")
        }
    }

    override fun bindView(bundle: Bundle?) {

        if (edit) {
            tvAdd.text = "编辑"
            appToolbar.setTitle("编辑卸车通知")
        } else {
            tvAdd.text = "添加"
            appToolbar.setTitle("添加卸车通知")
        }

        //通知字数控制
        edit_code.filters = arrayOf<InputFilter>(InputFilter.LengthFilter(25))
        edit_code.addTextChangedListener(object : TextWatcher {
            @SuppressLint("SetTextI18n")
            override fun afterTextChanged(s: Editable?) {
                if (TextUtils.isEmpty(s)) {
                    tv_code_size.text = "0/25"
                }
                s?.let {
                    if (s.length > 25) {
                        return
                    }
                    tv_code_size.text = "${s.length}/25"
                }
            }

            override fun beforeTextChanged(s: CharSequence?, start: Int, count: Int, after: Int) {

            }

            override fun onTextChanged(s: CharSequence?, start: Int, before: Int, count: Int) {

            }
        })
        tvAdd.setOnClickListener {
            val reqCoalNoticeAddEdit = ReqCementNoticeAddEdit()
            reqCoalNoticeAddEdit.targetType = "4"
            val text = edit_code.text
            if (TextUtils.isEmpty(text)) {
                showDialogToast("公告内容不能为空!")
                return@setOnClickListener
            }
            reqCoalNoticeAddEdit.content = text.toString().trim()
            when (input_need_expedited.check) {
                InputViewCheckV2.LEFT -> {
                    reqCoalNoticeAddEdit.warningFlag = "1"
                }
                InputViewCheckV2.RIGHT -> {
                    reqCoalNoticeAddEdit.warningFlag = "2"
                }
                else -> {
                    showDialogToast("请选择是否预警!")
                    return@setOnClickListener
                }
            }
            when (edit) {
                true -> {
                    reqCoalNoticeAddEdit.operateType = "2"
                    reqCoalNoticeAddEdit.noticeId = noticeId
                }
                else -> {
                    reqCoalNoticeAddEdit.operateType = "1"
                }
            }
            viewModel?.add(reqCoalNoticeAddEdit)
        }
        tv_cancel.setOnClickListener {
            finish()
        }
    }

    @LiveDataMatch
    open fun onQueryDetailSuccess(data: RspCementNoticeDetail?) {
        data?.apply {
            edit_code.setText(content)
            when (warningFlag) {
                "1" -> {
                    input_need_expedited.check = InputViewCheckV2.LEFT
                }
                "2" -> {
                    input_need_expedited.check = InputViewCheckV2.RIGHT
                }
                else -> {
                    input_need_expedited.check = InputViewCheckV2.NONE
                }
            }
            input_need_expedited
        }
    }

    @LiveDataMatch
    open fun onAddSuccess(msg: String) {
        showToast(msg)
        RxBusEventManager.postEvent(RxCementNoticeAddEditSuccess())
        finish()
    }

    @LiveDataMatch
    open fun onEditSuccess(msg: String) {
        showToast(msg)
        RxBusEventManager.postEvent(RxCementNoticeAddEditSuccess())
        finish()
    }
}