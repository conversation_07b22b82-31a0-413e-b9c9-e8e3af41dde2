package com.zczy.cargo_owner.cement.adapter

import com.chad.library.adapter.base.BaseQuickAdapter
import com.chad.library.adapter.base.BaseViewHolder
import com.zczy.cargo_owner.R
import com.zczy.cargo_owner.cement.req.RspCementNoticeList

/**
 *  user: ssp
 *  time: 2021/5/21 11:13
 *  desc: 卸车通知
 */

class CementNoticeAdapter : BaseQuickAdapter<RspCementNoticeList, BaseViewHolder>(R.layout.cement_notice_item) {

    override fun convert(helper: BaseViewHolder?, item: RspCementNoticeList?) {
        helper?.apply {
            item?.apply {
                setText(R.id.tvStart, content)
                setText(R.id.tvTime, createdTimeStr)
                addOnClickListener(R.id.tvEdit)
                addOnClickListener(R.id.tvDelete)
            }
        }
    }

}