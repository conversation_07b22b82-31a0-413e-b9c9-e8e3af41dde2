package com.zczy.cargo_owner.cement.model

import com.sfh.lib.mvvm.service.BaseViewModel
import com.zczy.cargo_owner.cement.req.ReqCementNoticeDelete
import com.zczy.cargo_owner.cement.req.ReqCementNoticeList
import com.zczy.cargo_owner.coal.req.ReqCoalNoticeDelete

/**
 *  user: ssp
 *  time: 2021/5/21 14:04
 *  desc: 卸车通知
 */

class CementNoticeModel : BaseViewModel() {

    fun queryList(pageNum: Int, targetType: String) {
        execute(ReqCementNoticeList(pageNum = pageNum, targetType = targetType)) {
            if (it.success()) {
                setValue("onQueryListSuccess", it.data)
            } else {
                setValue("onQueryListError", it.msg)
            }
        }
    }

    fun deleteItem(noticeId: String, targetType: String) {
        execute(ReqCementNoticeDelete(noticeId = noticeId, targetType = targetType)) {
            if (it.success()) {
                setValue("onDeleteSuccess", it.msg)
            } else {
                showDialogToast(it.msg)
            }
        }
    }
}