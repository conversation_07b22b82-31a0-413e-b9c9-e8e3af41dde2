package com.zczy.cargo_owner.cement.req

import com.zczy.comm.http.entity.BaseNewRequest
import com.zczy.comm.http.entity.BaseRsp
import com.zczy.comm.http.entity.ResultData

/**
 *  user: ssp
 *  time: 2021/5/21 13:58
 *  desc: 卸车通知
 */

data class ReqCementNoticeAddEdit(
        var noticeId: String? = null,//公告id修改必传。新增非必传
        var content: String = "",// 公告内容必传
        var urgencyFlag: String? = null,//紧急标识  1-是 2-否煤厂公告必传
        var warningFlag: String? = null,// 预警标识  1-是 2-否 卸车通知必传
        var operateType: String = "",//操作类型 1、新增 2、修改
        var targetType: String = ""//公告类型 1：煤厂公告,2:生产能力;3:发运计划:4:卸车通知管理'
) : BaseNewRequest<BaseRsp<ResultData>>("mms-app/notice/addOrUpdateNotice")

data class RxCementNoticeAddEditSuccess(
        var success: Boolean = true
)