package com.zczy.cargo_owner.cement.req

import com.zczy.comm.http.entity.BaseNewRequest
import com.zczy.comm.http.entity.BaseRsp
import com.zczy.comm.http.entity.ResultData

/**
 *  user: ssp
 *  time: 2021/5/21 13:58
 *  desc: 卸车通知
 */

data class ReqCementNoticeDelete(
        var noticeId: String = "",//公告ID
        var targetType: String = ""// 公告类型 1：煤厂公告,2:生产能力;3:发运计划:4:卸车通知管理
) : BaseNewRequest<BaseRsp<ResultData>>("mms-app/notice/deleteNotice")