package com.zczy.cargo_owner.cement.model

import com.sfh.lib.mvvm.service.BaseViewModel
import com.zczy.cargo_owner.cement.req.ReqCementNoticeAddEdit
import com.zczy.cargo_owner.cement.req.ReqCementNoticeDetail
import com.zczy.cargo_owner.cement.req.ReqCementNoticeEdit
import com.zczy.cargo_owner.cement.req.success

/**
 *  user: ssp
 *  time: 2021/5/21 14:04
 *  desc: 卸车通知
 */

class CementNoticeAddEditModel : BaseViewModel() {

    fun add(req: ReqCementNoticeAddEdit) {
        execute(req) {
            if (it.success()) {
                setValue("onAddSuccess", it.msg)
            } else {
                showDialogToast(it.msg)
            }
        }
    }

    fun edit() {
        execute(ReqCementNoticeEdit()) {
            if (it.success()) {
                setValue("onEditSuccess", it.msg)
            } else {
                showDialogToast(it.msg)
            }
        }
    }

    fun queryDetail(noticeId: String) {
        execute(ReqCementNoticeDetail(noticeId = noticeId)) {
            if (it.success()) {
                val data = it.data
                data?.let { bean ->
                    if (bean.isNullOrEmpty()) {
                        return@let
                    }
                    setValue("onQueryDetailSuccess", bean[0])
                }
            } else {
                showDialogToast(it.msg)
            }
        }
    }
}