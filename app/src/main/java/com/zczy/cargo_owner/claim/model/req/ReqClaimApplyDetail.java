package com.zczy.cargo_owner.claim.model.req;

import com.zczy.cargo_owner.claim.model.resp.RspClaimApplyDetail;
import com.zczy.comm.http.entity.BaseNewRequest;
import com.zczy.comm.http.entity.BaseRsp;

/*=============================================================================================
 * 功能描述:
 *---------------------------------------------------------------------------------------------
 *  涉及业务 & 更新说明:
 *
 *
 *--------------------------------------------------------------------------------------------
 *  <AUTHOR>  on  2022/10/13
 *  @Company    中储智运科技股份有限公司
 *  @Copyright （版权）中储南京智慧物流科技有限公司所有                                                                                        *
 *=============================================================================================*/
public class ReqClaimApplyDetail extends BaseNewRequest<BaseRsp<RspClaimApplyDetail>> {
    public String claimApplyId;

    public ReqClaimApplyDetail(String claimApplyId) {
        super("wo-app/claimApply/queryClaimApplyDetail");
        this.claimApplyId = claimApplyId;
    }
}