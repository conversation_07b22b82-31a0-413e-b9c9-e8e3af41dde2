package com.zczy.cargo_owner.claim.model;

import com.sfh.lib.exception.HandleException;
import com.sfh.lib.mvvm.service.BaseViewModel;
import com.sfh.lib.rx.IResult;
import com.zczy.cargo_owner.claim.model.req.ReqAddClaimApply;
import com.zczy.cargo_owner.claim.model.req.ReqClaimApplyDetail;
import com.zczy.cargo_owner.claim.model.req.ReqClaimApplyList;
import com.zczy.cargo_owner.claim.model.req.ReqClaimApplyOrderDetail;
import com.zczy.cargo_owner.claim.model.req.ReqGetOrderList;
import com.zczy.cargo_owner.claim.model.req.RespGetOrderList;
import com.zczy.cargo_owner.claim.model.resp.RespClaimApplyList;
import com.zczy.cargo_owner.claim.model.resp.RspAddClaimApply;
import com.zczy.cargo_owner.claim.model.resp.RspClaimApplyDetail;
import com.zczy.cargo_owner.claim.model.resp.RspClaimApplyOrderDetail;
import com.zczy.comm.CommServer;
import com.zczy.comm.file.IFileServer;
import com.zczy.comm.http.entity.BaseRsp;
import com.zczy.comm.http.entity.PageList;

import java.io.File;
import java.util.List;

import io.reactivex.disposables.Disposable;

/*=============================================================================================
 * 功能描述:
 *---------------------------------------------------------------------------------------------
 *  涉及业务 & 更新说明:
 *
 *
 *--------------------------------------------------------------------------------------------
 *  <AUTHOR>  on  2022/10/9
 *  @Company    中储智运科技股份有限公司
 *  @Copyright （版权）中储南京智慧物流科技有限公司所有                                                                                        *
 *=============================================================================================*/
public class ClaimModel extends BaseViewModel {

    /***
     * 上传文件
     * 响应方法：onaddViolateSuccess，onFileFailure
     * @param file
     */
    public void upFile(List<String> file) {

        for (String p : file) {
            this.upFile(p);
        }
    }

    public void upFile(String file) {

        Disposable disposable = CommServer.getFileServer().update(new File(file), new IFileServer.OnFileUploaderListener() {
            @Override
            public void onSuccess(File tag, String url) {
                setValue("onFileSuccess", tag, url);
            }

            @Override
            public void onFailure(File tag, String error) {
                showToast(error);
                setValue("onFileFailure", tag, error);
            }
        });
        this.putDisposable(disposable);
    }

    /**
     * 理赔列表
     *
     * @param orderId
     * @param nowPage
     */
    public void getClaimApplyList(String orderId, int nowPage) {
        this.execute(new ReqClaimApplyList(orderId, nowPage), new IResult<BaseRsp<PageList<RespClaimApplyList>>>() {
            @Override
            public void onFail(HandleException e) {
                showToast(e.getMsg());
            }

            @Override
            public void onSuccess(BaseRsp<PageList<RespClaimApplyList>> pageListBaseRsp) throws Exception {
                if (pageListBaseRsp.success()) {
                    setValue("onClaimApplyListSuccess", pageListBaseRsp.getData());
                } else {
                    showToast(pageListBaseRsp.getMsg());
                }
            }
        });
    }

    /**
     * 理赔详情
     *
     * @param claimApplyId
     */
    public void getClaimApplyDetail(String claimApplyId) {
        showLoading(false);
        this.execute(new ReqClaimApplyDetail(claimApplyId), new IResult<BaseRsp<RspClaimApplyDetail>>() {
            @Override
            public void onSuccess(BaseRsp<RspClaimApplyDetail> baseRsp) throws Exception {
                hideLoading();
                if (baseRsp.success()) {
                    setValue("onGetClaimApplyDetailSuccess", baseRsp.getData());
                } else {
                    showDialogToast(baseRsp.getMsg());
                }
            }

            @Override
            public void onFail(HandleException e) {
                hideLoading();
                showDialogToast(e.getMsg());
            }
        });
    }

    /**
     * 查询运单详情
     *
     * @param orderId
     */
    public void getClaimApplyOrderDetail(String orderId) {
        showLoading(false);
        this.execute(new ReqClaimApplyOrderDetail(orderId), new IResult<BaseRsp<RspClaimApplyOrderDetail>>() {
            @Override
            public void onSuccess(BaseRsp<RspClaimApplyOrderDetail> baseRsp) throws Exception {
                hideLoading();
                if (baseRsp.success()) {
                    setValue("onGetClaimApplyOrderDetailSuccess", baseRsp.getData());
                } else {
                    showDialogToast(baseRsp.getMsg());
                }
            }

            @Override
            public void onFail(HandleException e) {
                hideLoading();
                showDialogToast(e.getMsg());
            }
        });
    }

    /**
     * 理赔申请
     *
     * @param req
     */
    public void addClaimApply(ReqAddClaimApply req) {
        showLoading(false);
        this.execute(req, new IResult<BaseRsp<RspAddClaimApply>>() {
            @Override
            public void onSuccess(BaseRsp<RspAddClaimApply> baseRsp) throws Exception {
                hideLoading();
                if (baseRsp.success()) {
                    setValue("onAddClaimApplySuccess", baseRsp.getData());
                } else {
                    showDialogToast(baseRsp.getMsg());
                }
            }

            @Override
            public void onFail(HandleException e) {
                hideLoading();
                showDialogToast(e.getMsg());
            }
        });
    }


    /**
     * 理赔运单选择
     *
     * @param orderId
     * @param nowPage
     */
    public void getOrderList(int nowPage, String userId, String childUserFlag, String orderId) {
        this.execute(new ReqGetOrderList(nowPage,userId,childUserFlag,orderId,10), new IResult<BaseRsp<PageList<RespGetOrderList>>>() {
            @Override
            public void onFail(HandleException e) {
                showToast(e.getMsg());
            }

            @Override
            public void onSuccess(BaseRsp<PageList<RespGetOrderList>> pageListBaseRsp) throws Exception {
                if (pageListBaseRsp.success()) {
                    setValue("onGetOrderListSuccess", pageListBaseRsp.getData());
                } else {
                    showToast(pageListBaseRsp.getMsg());
                }
            }
        });
    }

}
