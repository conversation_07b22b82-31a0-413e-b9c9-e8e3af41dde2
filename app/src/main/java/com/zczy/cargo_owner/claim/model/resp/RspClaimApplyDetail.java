package com.zczy.cargo_owner.claim.model.resp;

import com.zczy.comm.http.entity.ResultData;

import java.util.List;

/*=============================================================================================
 * 功能描述:
 *---------------------------------------------------------------------------------------------
 *  涉及业务 & 更新说明:
 *
 *
 *--------------------------------------------------------------------------------------------
 *  <AUTHOR>  on  2022/10/13
 *  @Company    中储智运科技股份有限公司
 *  @Copyright （版权）中储南京智慧物流科技有限公司所有                                                                                        *
 *=============================================================================================*/
public class RspClaimApplyDetail extends ResultData {

    private List<UploadFile> uploadFilesModels;     //图片列表

    private ClaimApplyDto claimApplyDto;

    public List<UploadFile> getUploadFilesModels() {
        return uploadFilesModels;
    }

    public ClaimApplyDto getClaimApplyDto() {
        return claimApplyDto;
    }

    public void setClaimApplyDto(ClaimApplyDto claimApplyDto) {
        this.claimApplyDto = claimApplyDto;
    }

    public class ClaimApplyDto {
        private String claimApplyId;        //理赔申请Id
        private String claimApplyCode;        //理赔申请单号
        private String orderId;        //运单号
        private String consignorName;        //货主名称
        private String mobile;        //手机号
        private String statusName;        //状态
        private String createdTime;        //提交时间
        private String remark;        //理赔事由
        private String context;        //日志内容
        private String claimMoney; //申请理赔金额

        public String getClaimApplyId() {
            return claimApplyId;
        }

        public String getClaimApplyCode() {
            return claimApplyCode;
        }

        public String getOrderId() {
            return orderId;
        }

        public String getConsignorName() {
            return consignorName;
        }

        public String getMobile() {
            return mobile;
        }

        public String getStatusName() {
            return statusName;
        }

        public String getCreatedTime() {
            return createdTime;
        }

        public String getRemark() {
            return remark;
        }

        public String getContext() {
            return context;
        }

        public String getClaimMoney() {
            return claimMoney;
        }
    }
}
