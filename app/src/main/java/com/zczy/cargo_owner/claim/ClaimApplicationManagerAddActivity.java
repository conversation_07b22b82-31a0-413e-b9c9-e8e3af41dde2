package com.zczy.cargo_owner.claim;

import android.app.Activity;
import android.content.Context;
import android.content.Intent;
import android.os.Bundle;
import androidx.annotation.Nullable;
import android.text.Editable;
import android.text.TextUtils;
import android.text.TextWatcher;
import android.view.Gravity;
import android.view.View;
import android.widget.EditText;
import android.widget.TextView;

import com.sfh.lib.event.RxBusEventManager;
import com.sfh.lib.mvvm.annotation.LiveDataMatch;
import com.sfh.lib.ui.AbstractLifecycleActivity;
import com.sfh.lib.ui.dialog.DialogBuilder;
import com.zczy.cargo_owner.R;
import com.zczy.cargo_owner.claim.model.req.ReqAddClaimApply;
import com.zczy.cargo_owner.claim.model.req.RespGetOrderList;
import com.zczy.cargo_owner.claim.model.resp.RspAddClaimApply;
import com.zczy.cargo_owner.libcomm.config.HttpConfig;
import com.zczy.cargo_owner.libcomm.widget.CheckSelfPermissionDialog;
import com.zczy.comm.CommServer;
import com.zczy.comm.data.entity.EImage;
import com.zczy.comm.data.entity.ELogin;
import com.zczy.comm.data.entity.EProcessFile;
import com.zczy.comm.permission.PermissionCallBack;
import com.zczy.comm.permission.PermissionUtil;
import com.zczy.comm.utils.imageselector.ImagePreviewActivity;
import com.zczy.comm.utils.imageselector.ImageSelectProgressView;
import com.zczy.comm.utils.imageselector.ImageSelector;
import com.zczy.cargo_owner.claim.model.ClaimModel;

import java.io.File;
import java.util.ArrayList;
import java.util.List;

/*=============================================================================================
 * 功能描述:理赔申请
 *---------------------------------------------------------------------------------------------
 *  涉及业务 & 更新说明:
 *
 *
 *--------------------------------------------------------------------------------------------
 *  <AUTHOR>  on  2022/10/10
 *  @Company    中储智运科技股份有限公司
 *  @Copyright （版权）中储南京智慧物流科技有限公司所有                                                                                        *
 *=============================================================================================*/
public class ClaimApplicationManagerAddActivity extends AbstractLifecycleActivity<ClaimModel> implements View.OnClickListener {
    private static final int REQUEST_PHOTO = 0x32;
    private static final int REQUEST_ORDER = 0x33;
    private TextView tv_order_id_title;
    private TextView tv_order_id_value;
    private TextView tv_name_value;
    private TextView tv_code_size;
    private TextView tv_commit;
    private TextView tv_address_start;
    private TextView tv_address_end;
    private TextView tv_goods_detail;
    private EditText et_mobile_value;
    private EditText edit_code;
    private EditText et_money_value;
    private ImageSelectProgressView imageSelectView;
    private RespGetOrderList mOrder = null;
    private ELogin login;

    public static void start(Context context) {
        Intent intent = new Intent(context, ClaimApplicationManagerAddActivity.class);
        context.startActivity(intent);
    }

    @Override
    protected void onCreate(@Nullable Bundle savedInstanceState) {
        super.onCreate(savedInstanceState);
        setContentView(R.layout.claim_application_add_activity);
        initView();
    }

    private void initView() {
        tv_order_id_title = findViewById(R.id.tv_order_id_title);
        tv_order_id_value = findViewById(R.id.tv_order_id_value);
        tv_name_value = findViewById(R.id.tv_name_value);
        tv_code_size = findViewById(R.id.tv_code_size);
        tv_commit = findViewById(R.id.tv_commit);
        tv_address_start = findViewById(R.id.tv_address_start);
        tv_address_end = findViewById(R.id.tv_address_end);
        tv_goods_detail = findViewById(R.id.tv_goods_detail);
        et_mobile_value = findViewById(R.id.et_mobile_value);
        edit_code = findViewById(R.id.edit_code);
        et_money_value = findViewById(R.id.et_money_value);
        imageSelectView = findViewById(R.id.image_select_view);
        login = CommServer.getUserServer().getLogin();
        if (login != null && login.getMobile() != null) {
            et_mobile_value.setText(login.getMobile());
        }
        tv_order_id_title.setOnClickListener(this);
        tv_order_id_value.setOnClickListener(this);
        tv_commit.setOnClickListener(this);
        edit_code.addTextChangedListener(new TextWatcher() {
            @Override
            public void beforeTextChanged(CharSequence s, int start, int count, int after) {

            }

            @Override
            public void onTextChanged(CharSequence s, int start, int before, int count) {

            }

            @Override
            public void afterTextChanged(Editable s) {
                if (s.length() > 600) {
                    return;
                }
                tv_code_size.setText("(" + s.length() + "/600)");
            }
        });
        imageSelectView.setOnItemSelectListener(new ImageSelectProgressView.OnItemSelectListener() {
            @Override
            public void onSelectImageClick(int surplus) {
                CheckSelfPermissionDialog.cameraPermissionDialog(ClaimApplicationManagerAddActivity.this, new PermissionCallBack() {
                    @Override
                    public void onHasPermission() {
                        PermissionUtil.openAlbum(ClaimApplicationManagerAddActivity.this,
                                new PermissionCallBack() {
                                    @Override
                                    public void onHasPermission() {
                                        ImageSelector.open(ClaimApplicationManagerAddActivity.this, surplus, true, REQUEST_PHOTO);
                                    }
                                });
                    }
                });

            }

            @Override
            public void onUpImageClick(String file) {

            }

            @Override
            public void onLookImageClick(List<EProcessFile> file, int position) {
                //查看大图
                List<EImage> list = new ArrayList<>(file.size());
                for (EProcessFile processFile : file) {
                    EImage image = new EImage();
                    image.setNetUrl(HttpConfig.getUrlImage(processFile.getImagUrl()));
                    list.add(image);
                }
                ImagePreviewActivity.start(ClaimApplicationManagerAddActivity.this, list, position);
            }

            @Override
            public void onDelateClick(int position) {
                DialogBuilder dialogBuilder = new DialogBuilder();
                dialogBuilder.setMessage("确定删除当前图片吗？");
                dialogBuilder.setOkListener((DialogBuilder.DialogInterface dialogInterface, int i) -> {

                    dialogInterface.dismiss();
                    imageSelectView.deleteImage(position);
                });
                showDialog(dialogBuilder);
            }
        });
    }

    @Override
    public void onClick(View v) {
        int id = v.getId();
        if (id == R.id.tv_order_id_value) {
            String childUserFlag = "";
            if (login == null) {
                return;
            }
            if (TextUtils.isEmpty(login.getChildId())) {
                childUserFlag = "0";
            } else {
                childUserFlag = "1";
            }
            ClaimChooseOrderActivity.start(ClaimApplicationManagerAddActivity.this, login.getUserId(), childUserFlag, REQUEST_ORDER);
        } else if (id == R.id.tv_order_id_title) {
            showTipsDialog("仅支持发货后且货主回单确认前的运单");

        } else if (id == R.id.tv_commit) {
            List<EProcessFile> selectFileList = imageSelectView.getDataList();
            List<String> uploadFilesPath = new ArrayList<>();
            for (int i = 0; i < selectFileList.size(); i++) {
                uploadFilesPath.add(selectFileList.get(i).getImagUrl());
            }
            if (TextUtils.isEmpty(tv_order_id_value.getText().toString())) {
                showToast("请选择运单");
                return;
            }
            if (TextUtils.isEmpty(et_mobile_value.getText().toString())) {
                showToast("请输入联系电话");
                return;
            }
            if (TextUtils.isEmpty(edit_code.getText().toString())) {
                showToast("请描述申请理赔原因");
                return;
            }
            if (selectFileList.size() < 1) {
                showToast("请上传定损材料");
                return;
            }
            if (TextUtils.isEmpty(et_money_value.getText().toString())) {
                showToast("请输入申请理赔金额");
                return;
            }

            ReqAddClaimApply req = new ReqAddClaimApply();
            req.orderId = mOrder.getOrderId();
            req.consignorId = mOrder.getConsignorUserId();
            req.consignorName = tv_name_value.getText().toString();
            req.mobile = et_mobile_value.getText().toString();
            req.claimMoney = et_money_value.getText().toString();
            req.remark = edit_code.getText().toString();
            req.filePaths = uploadFilesPath;
            getViewModel().addClaimApply(req);
        }
    }

    private void showTipsDialog(String msg) {
        DialogBuilder dialogBuilder = new DialogBuilder();
        dialogBuilder.setCancelable(false);
        dialogBuilder.setHideCancel(true);
        dialogBuilder.setTitle("提示");
        dialogBuilder.setOKTextColor("确定", com.zczy.cargo_owner.order.R.color.text_blue);
        dialogBuilder.setMessageGravity(msg, Gravity.CENTER);
        dialogBuilder.setOkListener((dialog, which) -> {
            dialog.dismiss();
        });
        showDialog(dialogBuilder);
    }

    @LiveDataMatch(tag = "上传文件成功")
    public void onFileSuccess(File tag, String url) {
        imageSelectView.onUpLoadFileSuccess(tag.getAbsolutePath(), url);
    }

    @LiveDataMatch(tag = "上传文件失败")
    public void onFileFailure(File tag, String error) {
        imageSelectView.onUpLoadFileError(tag.getAbsolutePath());
    }

    @LiveDataMatch
    public void onAddClaimApplySuccess(RspAddClaimApply rsp) {
        DialogBuilder dialogBuilder = new DialogBuilder();
        dialogBuilder.setTitle("提示");
        dialogBuilder.setMessage(rsp.getResultMsg());
        dialogBuilder.setHideCancel(true);
        dialogBuilder.setOkListener((dialogInterface, i) -> {
            dialogInterface.dismiss();
            RxBusUploadSuccess rxBusUploadSuccess = new RxBusUploadSuccess();
            rxBusUploadSuccess.setSuccess(true);
            RxBusEventManager.postEvent(rxBusUploadSuccess);
            finish();
        });
        showDialog(dialogBuilder);
    }

    @Override
    public void onActivityResult(int requestCode, int resultCode, Intent data) {
        super.onActivityResult(requestCode, resultCode, data);
        if (resultCode == Activity.RESULT_OK) {
            switch (requestCode) {
                case REQUEST_PHOTO:
                    List<String> file = ImageSelector.obtainPathResult(data);
                    if (file != null) {
                        imageSelectView.onUpLoadStart(file);
                        getViewModel().upFile(file);
                    }
                    break;
                case REQUEST_ORDER:
                    mOrder = ClaimChooseOrderActivity.obtainData(data);
                    if (mOrder != null) {
                        tv_order_id_value.setText(mOrder.getOrderId());
                        tv_address_start.setVisibility(View.VISIBLE);
                        tv_address_end.setVisibility(View.VISIBLE);
                        tv_goods_detail.setVisibility(View.VISIBLE);
                        tv_name_value.setText(mOrder.getConsignorCompany());
                        tv_address_start.setText(mOrder.getDespatchAddress());
                        tv_address_end.setText(mOrder.getDeliverAddress());
                        tv_goods_detail.setText(mOrder.getGoodsDescription());
                    } else {
                        tv_address_start.setVisibility(View.GONE);
                        tv_address_end.setVisibility(View.GONE);
                        tv_goods_detail.setVisibility(View.GONE);
                    }
                    break;
                default:
                    break;
            }
        }
    }
}
