package com.zczy.cargo_owner.claim;

import android.content.Context;
import android.content.Intent;
import android.os.Bundle;
import androidx.annotation.Nullable;
import android.text.Html;
import android.text.TextUtils;
import android.widget.TextView;

import com.sfh.lib.mvvm.annotation.LiveDataMatch;
import com.sfh.lib.ui.AbstractLifecycleActivity;
import com.zczy.cargo_owner.R;
import com.zczy.cargo_owner.claim.model.ClaimModel;
import com.zczy.cargo_owner.claim.model.resp.RspClaimApplyDetail;
import com.zczy.cargo_owner.claim.model.resp.RspClaimApplyOrderDetail;
import com.zczy.cargo_owner.claim.model.resp.UploadFile;
import com.zczy.cargo_owner.libcomm.widget.ImageSelectViewV1;
import com.zczy.comm.data.entity.EImage;

import java.util.ArrayList;
import java.util.List;

/*=============================================================================================
 * 功能描述:理赔详情
 *---------------------------------------------------------------------------------------------
 *  涉及业务 & 更新说明:
 *
 *
 *--------------------------------------------------------------------------------------------
 *  <AUTHOR>  on  2022/10/10
 *  @Company    中储智运科技股份有限公司
 *  @Copyright （版权）中储南京智慧物流科技有限公司所有                                                                                        *
 *=============================================================================================*/
public class ClaimApplicationManagerDetailActivity extends AbstractLifecycleActivity<ClaimModel> {
    private TextView tv_apply_id_value;
    private TextView tv_order_id_value;
    private TextView tv_address_start;
    private TextView tv_address_end;
    private TextView tv_goods_detail;
    private TextView tv_cargo_name_value;
    private TextView tv_mobile_value;
    private TextView tv_reason_value;
    private TextView tv_money_value;
    private TextView tv_time_value;
    private TextView tv_state_value;
    private TextView tv_remarks;
    private ImageSelectViewV1 imageSelectView;
    private String claimApplyId;
    private String orderId;

    public static void start(Context context, String claimApplyId, String orderId) {
        Intent intent = new Intent(context, ClaimApplicationManagerDetailActivity.class);
        intent.putExtra("claimApplyId", claimApplyId);
        intent.putExtra("orderId", orderId);
        context.startActivity(intent);
    }

    @Override
    protected void onCreate(@Nullable Bundle savedInstanceState) {
        super.onCreate(savedInstanceState);
        setContentView(R.layout.claim_application_detail_activity);
        initView();
        initData();
    }


    private void initView() {
        tv_apply_id_value = findViewById(R.id.tv_apply_id_value);
        tv_order_id_value = findViewById(R.id.tv_order_id_value);
        tv_address_start = findViewById(R.id.tv_address_start);
        tv_address_end = findViewById(R.id.tv_address_end);
        tv_goods_detail = findViewById(R.id.tv_goods_detail);
        tv_cargo_name_value = findViewById(R.id.tv_cargo_name_value);
        tv_mobile_value = findViewById(R.id.tv_mobile_value);
        tv_reason_value = findViewById(R.id.tv_reason_value);
        tv_money_value = findViewById(R.id.tv_money_value);
        tv_time_value = findViewById(R.id.tv_time_value);
        tv_state_value = findViewById(R.id.tv_state_value);
        tv_remarks = findViewById(R.id.tv_remarks);
        imageSelectView = findViewById(R.id.imageSelectView);
        imageSelectView.setCanSelect(false);
        imageSelectView.setCanDelete(false);
        claimApplyId = getIntent().getStringExtra("claimApplyId");
        orderId = getIntent().getStringExtra("orderId");
    }

    private void initData() {
        getViewModel().getClaimApplyDetail(claimApplyId);
        getViewModel().getClaimApplyOrderDetail(orderId);
    }

    @LiveDataMatch(tag = "理赔详情")
    public void onGetClaimApplyDetailSuccess(RspClaimApplyDetail data) {
        setData(data);
    }


    @LiveDataMatch(tag = "理赔运单详情")
    public void onGetClaimApplyOrderDetailSuccess(RspClaimApplyOrderDetail data) {
        tv_address_start.setText(data.getDespatchAddress());
        tv_address_end.setText(data.getDeliverAddress());
        tv_goods_detail.setText(data.getAllCargoName());
    }

    private void setData(RspClaimApplyDetail data) {
        tv_apply_id_value.setText(data.getClaimApplyDto().getClaimApplyCode());
        tv_order_id_value.setText(data.getClaimApplyDto().getOrderId());
        tv_cargo_name_value.setText(data.getClaimApplyDto().getConsignorName());
        tv_mobile_value.setText(data.getClaimApplyDto().getMobile());
        tv_reason_value.setText(data.getClaimApplyDto().getRemark());
        tv_money_value.setText(data.getClaimApplyDto().getClaimMoney() + "元");
        tv_time_value.setText(data.getClaimApplyDto().getCreatedTime());
        tv_state_value.setText(data.getClaimApplyDto().getStatusName());
        if (TextUtils.equals(data.getClaimApplyDto().getStatusName(), "审核中")) {
            tv_remarks.setText("平台工作人员已介入开始为您处理");
        } else if (TextUtils.equals(data.getClaimApplyDto().getStatusName(), "审核通过")) {
            tv_remarks.setText("理赔事件已通过平台审核");
        } else if (TextUtils.equals(data.getClaimApplyDto().getStatusName(), "财务处理")) {
            tv_remarks.setText("赔付工作准备中");
        } else if (TextUtils.equals(data.getClaimApplyDto().getStatusName(), "赔付完成")) {
            tv_remarks.setText(Html.fromHtml("已完成赔付，赔付金额" + "<font color=\"#FF0000\">" + data.getClaimApplyDto().getClaimMoney() + "</font>" + "付款时间" + data.getClaimApplyDto().getCreatedTime()));
        } else if (TextUtils.equals(data.getClaimApplyDto().getStatusName(), "审核驳回")) {
            tv_remarks.setText("理赔事件不符合理赔要");
        }else if(TextUtils.equals(data.getClaimApplyDto().getStatusName(), "提交成功")){
            tv_remarks.setText("已提交成功，待平台处理");
        }
        ArrayList<EImage> list = new ArrayList<>();
        List<UploadFile> uploadFilesModels = data.getUploadFilesModels();
        for (int i = 0; i < uploadFilesModels.size(); i++) {
            EImage eImage = new EImage();
            eImage.setImageId(uploadFilesModels.get(i).getFile_url());
            list.add(eImage);
        }
        imageSelectView.setImgList(list);
    }
}
