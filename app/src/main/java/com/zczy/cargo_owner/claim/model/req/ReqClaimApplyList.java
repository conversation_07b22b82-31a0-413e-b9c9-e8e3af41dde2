package com.zczy.cargo_owner.claim.model.req;

import com.zczy.cargo_owner.claim.model.resp.RespClaimApplyList;
import com.zczy.cargo_owner.user.assure.Assure;
import com.zczy.comm.http.entity.BaseNewRequest;
import com.zczy.comm.http.entity.BaseRsp;
import com.zczy.comm.http.entity.PageList;

/*=============================================================================================
 * 功能描述:
 *---------------------------------------------------------------------------------------------
 *  涉及业务 & 更新说明:
 *
 *
 *--------------------------------------------------------------------------------------------
 *  <AUTHOR>  on  2022/10/13
 *  @Company    中储智运科技股份有限公司
 *  @Copyright （版权）中储南京智慧物流科技有限公司所有                                                                                        *
 *=============================================================================================*/
public class ReqClaimApplyList extends BaseNewRequest<BaseRsp<PageList<RespClaimApplyList>>> {
    int nowPage;
    int pageSize = 10;
    String orderId;

    public ReqClaimApplyList(String orderId, int nowPage) {
        super("wo-app/claimApply/queryClaimApplyList");
        this.nowPage = nowPage;
        this.orderId = orderId;
    }
}
