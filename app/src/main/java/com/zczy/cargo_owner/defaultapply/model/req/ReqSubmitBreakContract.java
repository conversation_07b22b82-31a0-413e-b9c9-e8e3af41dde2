package com.zczy.cargo_owner.defaultapply.model.req;

import com.zczy.comm.http.entity.BaseRsp;
import com.zczy.comm.http.entity.ResultData;
import com.zczy.plugin.wisdom.BaseWisdomRequest;

/*=============================================================================================
 * 功能描述:
 *---------------------------------------------------------------------------------------------
 *  涉及业务 & 更新说明:
 *
 *
 *--------------------------------------------------------------------------------------------
 *  <AUTHOR>  on  2022/10/27
 *  @Company    中储智运科技股份有限公司
 *  @Copyright （版权）中储南京智慧物流科技有限公司所有                                                                                        *
 *=============================================================================================*/
public class ReqSubmitBreakContract extends BaseWisdomRequest<BaseRsp<ResultData>> {
    public String insuranceUrl;
    public ReqSubmitBreakContract(String insuranceUrl) {
        super("mms-app/userBreakContract/submitBreakContract");
        this.insuranceUrl = insuranceUrl;
    }
}
