package com.zczy.cargo_owner.defaultapply;

import android.app.Activity;
import android.content.Context;
import android.content.Intent;
import android.os.Bundle;
import androidx.annotation.Nullable;
import android.text.TextUtils;
import android.view.View;
import android.widget.ImageView;
import android.widget.TextView;

import com.chad.library.adapter.base.BaseQuickAdapter;
import com.sfh.lib.mvvm.annotation.LiveDataMatch;
import com.sfh.lib.ui.AbstractLifecycleActivity;
import com.zczy.cargo_owner.R;
import com.zczy.cargo_owner.defaultapply.adapter.DefaultApplyExemptionConfirmListAdapter;
import com.zczy.cargo_owner.defaultapply.model.DefaultApplyExemptionConfirmModel;
import com.zczy.cargo_owner.defaultapply.model.rsp.RspApplyList;
import com.zczy.cargo_owner.defaultapply.model.rsp.RspBreakContractValue;
import com.zczy.cargo_owner.libcomm.config.HttpConfig;
import com.zczy.comm.data.entity.EImage;
import com.zczy.comm.http.entity.PageList;
import com.zczy.comm.utils.imageselector.ImagePreviewActivity;
import com.zczy.comm.widget.pulltorefresh.CommEmptyView;
import com.zczy.comm.widget.pulltorefresh.SwipeRefreshMoreLayout;

import java.util.ArrayList;
import java.util.List;

/*=============================================================================================
 * 功能描述:违约申请免确认管理
 *---------------------------------------------------------------------------------------------
 *  涉及业务 & 更新说明:
 *
 *
 *--------------------------------------------------------------------------------------------
 *  <AUTHOR>  on  2022/10/26
 *  @Company    中储智运科技股份有限公司
 *  @Copyright （版权）中储南京智慧物流科技有限公司所有                                                                                        *
 *=============================================================================================*/
public class DefaultApplyExemptionConfirmActivity extends AbstractLifecycleActivity<DefaultApplyExemptionConfirmModel> implements BaseQuickAdapter.OnItemChildClickListener {

    private TextView tv_apply;
    private TextView tv_finish;
    private ImageView iv_status;
    private SwipeRefreshMoreLayout swipeRefreshMoreLayout;
    private DefaultApplyExemptionConfirmListAdapter defaultApplyExemptionConfirmListAdapter;

    public static void start(Context context) {
        Intent intent = new Intent(context, DefaultApplyExemptionConfirmActivity.class);
        context.startActivity(intent);
    }

    @Override
    protected void onCreate(@Nullable Bundle savedInstanceState) {
        super.onCreate(savedInstanceState);
        setContentView(R.layout.default_exception_activity);
        initView();
    }

    private void initView() {
        iv_status = findViewById(R.id.iv_status);
        tv_apply = findViewById(R.id.tv_apply);
        defaultApplyExemptionConfirmListAdapter = new DefaultApplyExemptionConfirmListAdapter(this);
        swipeRefreshMoreLayout = findViewById(R.id.swipeRefreshMoreLayout);
        swipeRefreshMoreLayout.setAdapter(defaultApplyExemptionConfirmListAdapter, true);
        swipeRefreshMoreLayout.setEmptyView(CommEmptyView.creatorDef(this));
        swipeRefreshMoreLayout.addItemDecorationSize(15);
        swipeRefreshMoreLayout.setOnLoadListener2(nowPage -> getViewModel().getApplyList());
        swipeRefreshMoreLayout.addOnItemChildClickListener(this);
        tv_apply.setOnClickListener(new View.OnClickListener() {
            @Override
            public void onClick(View v) {
                DefaultApplyExemptionConfirmApplyActivity.start(DefaultApplyExemptionConfirmActivity.this, 1);
            }
        });
        swipeRefreshMoreLayout.onAutoRefresh();
        tv_finish = findViewById(R.id.tv_finish);
        tv_finish.setOnClickListener(new View.OnClickListener() {
            @Override
            public void onClick(View v) {
                DefaultApplyExemptionConfirmActivity.this.finish();
            }
        });
        getViewModel().getBreakContractValue();
    }

    @Override
    public void onActivityResult(int requestCode, int resultCode, Intent data) {
        super.onActivityResult(requestCode, resultCode, data);
        if (resultCode == Activity.RESULT_OK) {
            swipeRefreshMoreLayout.onAutoRefresh();
        }
    }

    @LiveDataMatch
    public void onGetApplyListSuccess(PageList<RspApplyList> data) {
        swipeRefreshMoreLayout.onRefreshCompale(data);
    }

    @LiveDataMatch
    public void onGetApplyListError(String error) {
        showDialogToast(error);
        swipeRefreshMoreLayout.onLoadMoreFail();

    }

    public void onGetBreakContractValueSuccess(RspBreakContractValue rsp) {
        if (TextUtils.equals("1", rsp.getValue())) {
            tv_apply.setVisibility(View.VISIBLE);
            iv_status.setImageResource(com.zczy.plugin.wisdom.R.drawable.earnest_close);
        } else if(TextUtils.equals("0", rsp.getValue())){
            tv_apply.setVisibility(View.GONE);
            iv_status.setImageResource(com.zczy.plugin.wisdom.R.drawable.earnest_open);
        }else if(TextUtils.equals("2", rsp.getValue())){
            tv_apply.setVisibility(View.GONE);
            iv_status.setImageResource(com.zczy.plugin.wisdom.R.drawable.earnest_close);
        }

    }

    @Override
    public void onItemChildClick(BaseQuickAdapter adapter, View view, int position) {
        final RspApplyList data = (RspApplyList) adapter.getItem(position);
        int id = view.getId();
        if (id == R.id.iv_pic) {
            ImagePreviewActivity.start(DefaultApplyExemptionConfirmActivity.this, getImageList(data.getInsuranceUrl()), 0, false);
        } else if (id == R.id.tv_reapply) {
            DefaultApplyExemptionConfirmApplyActivity.start(DefaultApplyExemptionConfirmActivity.this, 1);
        }
    }

    public static List<EImage> getImageList(String netUrl) {
        List<EImage> list = new ArrayList<>();
        EImage image = new EImage();
        image.setNetUrl(HttpConfig.getUrlImage(netUrl));
        list.add(image);
        return list;
    }
}
