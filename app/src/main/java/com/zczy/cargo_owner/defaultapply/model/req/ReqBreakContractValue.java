package com.zczy.cargo_owner.defaultapply.model.req;

import com.zczy.cargo_owner.defaultapply.model.rsp.RspApplyList;
import com.zczy.cargo_owner.defaultapply.model.rsp.RspBreakContractValue;
import com.zczy.comm.http.entity.BaseRsp;
import com.zczy.plugin.wisdom.BaseWisdomRequest;

/*=============================================================================================
 * 功能描述:
 *---------------------------------------------------------------------------------------------
 *  涉及业务 & 更新说明:
 *
 *
 *--------------------------------------------------------------------------------------------
 *  <AUTHOR>  on  2022/10/27
 *  @Company    中储智运科技股份有限公司
 *  @Copyright （版权）中储南京智慧物流科技有限公司所有                                                                                        *
 *=============================================================================================*/
public class ReqBreakContractValue extends BaseWisdomRequest<BaseRsp<RspBreakContractValue>> {
    public ReqBreakContractValue() {
        super("mms-app/userBreakContract/getBreakContractValue");
    }
}




