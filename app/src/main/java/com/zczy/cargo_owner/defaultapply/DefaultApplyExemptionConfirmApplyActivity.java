package com.zczy.cargo_owner.defaultapply;

import android.app.Activity;
import android.content.Intent;
import android.os.Bundle;
import androidx.annotation.Nullable;
import android.text.Html;
import android.text.TextUtils;
import android.view.View;
import android.widget.TextView;

import com.sfh.lib.mvvm.annotation.LiveDataMatch;
import com.sfh.lib.ui.AbstractLifecycleActivity;
import com.sfh.lib.ui.dialog.DialogBuilder;
import com.zczy.cargo_owner.R;
import com.zczy.cargo_owner.defaultapply.model.DefaultApplyExemptionConfirmModel;
import com.zczy.cargo_owner.libcomm.config.HttpConfig;
import com.zczy.comm.Const;
import com.zczy.comm.data.entity.EImage;
import com.zczy.comm.data.entity.EProcessFile;
import com.zczy.comm.http.entity.ResultData;
import com.zczy.comm.utils.PhoneUtil;
import com.zczy.comm.utils.imageselector.ImagePreviewActivity;
import com.zczy.comm.utils.imageselector.ImageSelectProgressView;
import com.zczy.comm.utils.imageselector.ImageSelector;

import java.io.File;
import java.util.ArrayList;
import java.util.List;

/*=============================================================================================
 * 功能描述:违约申请免确认申请
 *---------------------------------------------------------------------------------------------
 *  涉及业务 & 更新说明:
 *
 *
 *--------------------------------------------------------------------------------------------
 *  <AUTHOR>  on  2022/10/26
 *  @Company    中储智运科技股份有限公司
 *  @Copyright （版权）中储南京智慧物流科技有限公司所有                                                                                        *
 *=============================================================================================*/
public class DefaultApplyExemptionConfirmApplyActivity extends AbstractLifecycleActivity<DefaultApplyExemptionConfirmModel> implements ImageSelectProgressView.OnItemSelectListener {
    private ImageSelectProgressView mBillImageSelectView;
    private static final int REQUESTCODE = 0x18;
    private TextView tv_phone;
    private TextView tv_submit;
    private String picUrl;


    public static void start(Activity activity,int requestCode) {
        Intent intent = new Intent(activity, DefaultApplyExemptionConfirmApplyActivity.class);
        activity.startActivityForResult(intent,requestCode);
    }

    @Override
    protected void onCreate(@Nullable Bundle savedInstanceState) {
        super.onCreate(savedInstanceState);
        setContentView(R.layout.default_exception_apply_activity);
        initView();
    }

    private void initView() {
        tv_phone = findViewById(R.id.tv_phone);
        String strMsg = "<font color=\"#FF602E\">温馨提示：开通承运方违约申请免确认功能需上传申请照片，如需申请模板请咨询业务经理/平台客服：</font><font color=\"#5086FC\">4000885566，</font>" +
                "<font color=\"#FF602E\">谢谢您的配合！</font>";
        tv_phone.setText(Html.fromHtml(strMsg));
        tv_phone.setOnClickListener((View v) -> {
            PhoneUtil.callPhone(DefaultApplyExemptionConfirmApplyActivity.this, Const.PHONE_SERVER_400);
        });
        mBillImageSelectView = findViewById(R.id.bill_image_select_view);
        mBillImageSelectView.setOnItemSelectListener(this);
        mBillImageSelectView.setDelete(true);
        mBillImageSelectView.setShowSize(1, 1);
        tv_submit = findViewById(R.id.tv_submit);
        tv_submit.setOnClickListener(new View.OnClickListener() {
            @Override
            public void onClick(View v) {
                if (TextUtils.isEmpty(picUrl)) {
                    DefaultApplyExemptionConfirmApplyActivity.this.showToast("请选择申请照片");
                    return;
                }
                getViewModel().submitBreakContract(picUrl);
            }
        });
    }

    long lastTime = -1;

    @Override
    public void onSelectImageClick(int surplus) {
        long time = System.currentTimeMillis();
        if (time - this.lastTime >= 1000) {
            ImageSelector.open(DefaultApplyExemptionConfirmApplyActivity.this, surplus, true, REQUESTCODE);
            this.lastTime = time;
        }
    }

    @Override
    public void onUpImageClick(String file) {

    }

    @Override
    public void onLookImageClick(List<EProcessFile> file, int position) {
        //查看大图
        List<EImage> list = new ArrayList<>(file.size());
        for (EProcessFile processFile : file) {
            EImage image = new EImage();
            image.setNetUrl(HttpConfig.getUrlImage(processFile.getImagUrl()));
            list.add(image);
        }
        ImagePreviewActivity.start(this, list, position);
    }

    @Override
    public void onDelateClick(int position) {
        DialogBuilder dialogBuilder = new DialogBuilder();
        dialogBuilder.setMessage("确定删除当前图片吗？");
        dialogBuilder.setOkListener((DialogBuilder.DialogInterface dialogInterface, int i) -> {
            dialogInterface.dismiss();
            mBillImageSelectView.deleteImage(position);
        });
        showDialog(dialogBuilder);
    }

    @Override
    public void onActivityResult(int requestCode, int resultCode, Intent data) {

        super.onActivityResult(requestCode, resultCode, data);
        if (resultCode == Activity.RESULT_OK) {

            List<EProcessFile> eProcessFiles = new ArrayList<>();

            if (REQUESTCODE == requestCode) {

                List<String> files = ImageSelector.obtainPathResult(data);
                for (String path : files) {
                    if (TextUtils.isEmpty(path)) {
                        showToast("图片损坏或图片不存在,请重新上传");
                        return;
                    }
                    File select = new File(path);
                    if (!select.exists() || !select.canRead() || select.length() <= 1 * 1024) {
                        showToast("图片损坏或图片不存在,请重新上传");
                        return;
                    }
                    EProcessFile processFile = new EProcessFile();
                    processFile.setTag(path);
                    eProcessFiles.add(processFile);
                }

            } else {
                String image = data.getStringExtra("imagePath");
                String imageWater = data.getStringExtra("imagePath");
                if (TextUtils.isEmpty(image) || TextUtils.isEmpty(imageWater)) {
                    showToast("图片损坏或图片不存在,请重新上传");
                    return;
                }
                EProcessFile processFile = new EProcessFile();
                processFile.setTag(imageWater);//水印图
                processFile.setImage2Tag(image);//原图
                eProcessFiles.add(processFile);
            }

            this.mBillImageSelectView.onUpLoadStartList(eProcessFiles);
            this.getViewModel().upFile(eProcessFiles);
        }
    }

    @LiveDataMatch(tag = "上传文件成功")
    public synchronized void onFileSuccess(File tag, String url) {
        this.picUrl = url;
        this.mBillImageSelectView.onUpLoadFileSuccess(tag.getAbsolutePath(), url);
    }

    @LiveDataMatch(tag = "上传文件2成功")
    public synchronized void onFileSuccess2(File tag, String url) {

        this.mBillImageSelectView.onUpdateImage2Status(tag.getAbsolutePath(), url);
    }

    @LiveDataMatch(tag = "上传文件失败")
    public void onFileFailure(File tag, String error) {

        this.showToast(error);
        this.mBillImageSelectView.onUpLoadFileError(tag.getAbsolutePath());
    }

    @LiveDataMatch
    public synchronized void onSubmitBreakContractSuccess(ResultData data) {
        showToast(data.getResultMsg());
        setResult(Activity.RESULT_OK);
        DefaultApplyExemptionConfirmApplyActivity.this.finish();
    }

}
