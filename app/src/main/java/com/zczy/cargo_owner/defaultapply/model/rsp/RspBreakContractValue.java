package com.zczy.cargo_owner.defaultapply.model.rsp;

import com.zczy.comm.http.entity.ResultData;

/*=============================================================================================
 * 功能描述:
 *---------------------------------------------------------------------------------------------
 *  涉及业务 & 更新说明:
 *
 *
 *--------------------------------------------------------------------------------------------
 *  <AUTHOR>  on  2022/10/27
 *  @Company    中储智运科技股份有限公司
 *  @Copyright （版权）中储南京智慧物流科技有限公司所有                                                                                        *
 *=============================================================================================*/
public class RspBreakContractValue extends ResultData {
    public String getValue() {
        return value;
    }

    private String value; // 1：启用按钮，文案：已关闭；0：禁用按钮，文案已开通
}
