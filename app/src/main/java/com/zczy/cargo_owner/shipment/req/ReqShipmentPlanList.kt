package com.zczy.cargo_owner.shipment.req

import com.zczy.comm.CommServer
import com.zczy.comm.http.entity.BaseNewRequest
import com.zczy.comm.http.entity.BaseRsp
import com.zczy.comm.http.entity.ResultData

/**
 *  user: ssp
 *  time: 2021/5/21 11:42
 *  desc: 发运计划
 */

class ReqShipmentPlanList(
        var userId: String = "",
        var userName: String = "",
        var childUserId: String = "",
        var childUserName: String = "",
        var nowPage: Int = 1,
        var pageSize: Int = 10
) : BaseNewRequest<BaseRsp<HzPageList<RspShipmentPlanList>>>("oms-app/sendPlan/querySendPlanListPage")

fun ReqShipmentPlanList.setUserInfo() {
    val login = CommServer.getUserServer().login
    login?.let {
        if (it.isChild) {
            childUserId = it.childId
            childUserName = it.userName
        } else {
            userId = it.userId
            userName = it.userName
        }
    }
}

data class RspShipmentPlanList(
    var dayPlanVehicleNum: String = "",
    var deliverPlace: String = "",
    var despatchPlace: String = "",
    var id: String = "",
    var inventory: String = "",
    var deterrentLine: String = "",
    var loadedVehicleNum: String = "",
    var reportVehicleNum: String = "",
    var sendTime: String = "",
    var unloadedVehicleNum: String = "",
    var consumption: String = ""
) : ResultData()

internal interface HzIPageList<E> {
    fun getNowPage(): Int
    fun getTotalSize(): Int
    fun getTotalPage(): Int
    fun getRootArray(): List<E>?
}

class HzPageList<E> : ResultData(), HzIPageList<E> {
    private var pageSize = 0
    private var nowPage = 0
    private var totalSize = 0
    private var totalPage = 0
    private var rootArray: List<E>? = emptyList()
    var pages: Int = 0
    var count: Int = 0
    var list: MutableList<E>? = mutableListOf()
    override fun getNowPage(): Int {
        return nowPage
    }

    override fun getTotalSize(): Int {
        return totalSize
    }

    override fun getTotalPage(): Int {
        return totalPage
    }

    override fun getRootArray(): List<E>? {
        return rootArray
    }

    fun setRootArray(rootArray: List<E>?) {
        this.rootArray = rootArray
    }

    fun setPageSize(pageSize: Int) {
        this.pageSize = pageSize
    }

    fun setNowPage(nowPage: Int) {
        this.nowPage = nowPage
    }

    fun setTotalSize(totalSize: Int) {
        this.totalSize = totalSize
    }

    fun setTotalPage(totalPage: Int) {
        this.totalPage = totalPage
    }

}

data class RxShipmentAddEditSuccess(
        var success: Boolean = true
)