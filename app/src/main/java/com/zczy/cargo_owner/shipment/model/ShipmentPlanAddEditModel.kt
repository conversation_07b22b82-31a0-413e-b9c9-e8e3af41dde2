package com.zczy.cargo_owner.shipment.model

import com.sfh.lib.mvvm.service.BaseViewModel
import com.zczy.cargo_owner.shipment.req.ReqShipmentPlanAdd
import com.zczy.cargo_owner.shipment.req.ReqShipmentPlanDetail
import com.zczy.cargo_owner.shipment.req.ReqShipmentPlanEdit
import com.zczy.cargo_owner.shipment.req.setUserInfo

/**
 *  user: ssp
 *  time: 2021/5/21 14:04
 *  desc: 发运计划
 */

class ShipmentPlanAddEditModel : BaseViewModel() {

    fun add(req: ReqShipmentPlanAdd) {
        req.setUserInfo()
        execute(req) {
            if (it.success()) {
                setValue("onAddSuccess", it.msg)
            } else {
                showDialogToast(it.msg)
            }
        }
    }

    fun edit(req: ReqShipmentPlanEdit) {
        req.setUserInfo()
        execute(req) {
            if (it.success()) {
                setValue("onEditSuccess", it.msg)
            } else {
                showDialogToast(it.msg)
            }
        }
    }

    fun queryDetail(id: String) {
        val reqShipmentPlanDetail = ReqShipmentPlanDetail(id = id)
        reqShipmentPlanDetail.setUserInfo()
        execute(reqShipmentPlanDetail) {
            if (it.success()) {
                setValue("onQueryDetailSuccess", it.data)
            } else {
                showDialogToast(it.msg)
            }
        }
    }
}