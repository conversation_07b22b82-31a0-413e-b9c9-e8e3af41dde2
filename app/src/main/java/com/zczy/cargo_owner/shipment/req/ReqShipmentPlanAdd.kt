package com.zczy.cargo_owner.shipment.req

import com.zczy.comm.CommServer
import com.zczy.comm.http.entity.BaseNewRequest
import com.zczy.comm.http.entity.BaseRsp
import com.zczy.comm.http.entity.ResultData

/**
 *  user: ssp
 *  time: 2021/5/21 13:58
 *  desc: 发运计划
 */

class ReqShipmentPlanAdd(
        var userId: String = "",
        var userName: String = "",
        var childUserId: String = "",
        var childUserName: String = "",
        var despatchPlace: String = "",
        var deliverPlace: String = "",
        var sendTime: String = "",
        var inventory: String = "",
        var dayPlanVehicleNum: String = "",
        var loadedVehicleNum: String = "",
        var unloadedVehicleNum: String = "",
        var reportVehicleNum: String = "",
        var consumption: String = "",
        var deterrentLine: String = ""
) : BaseNewRequest<BaseRsp<ResultData>>("oms-app/sendPlan/insertOneSendPlan")

fun ReqShipmentPlanAdd.setUserInfo() {
    val login = CommServer.getUserServer().login
    login?.let {
        if (it.isChild) {
            childUserId = it.childId
            childUserName = it.userName
        } else {
            userId = it.userId
            userName = it.userName
        }
    }
}