package com.zczy.cargo_owner.production.req

import android.os.Parcelable
import com.zczy.comm.CommServer
import com.zczy.comm.http.entity.BaseNewRequest
import com.zczy.comm.http.entity.BaseRsp
import com.zczy.comm.http.entity.PageList
import com.zczy.comm.http.entity.ResultData
import kotlinx.android.parcel.Parcelize

/**
 *  user: ssp
 *  time: 2021/5/21 13:58
 *  desc: 生产能力新增
 */

class ReqShippingDeliverAddress(
        var userId: String = "",
        var userName: String = "",
        var childUserId: String = "",
        var childUserName: String = "",
        var type: String = ""//类型,1 发货单位名称， 2收货单位名称，必填
) : BaseNewRequest<BaseRsp<PageList<RspShippingDeliverAddress>>>("oms-app/consignor/address/queryConsCompanyNames")

fun ReqShippingDeliverAddress.setUserInfo() {
    val login = CommServer.getUserServer().login
    login?.let {
        if (it.isChild) {
            childUserId = it.childId
            childUserName = it.userName
        } else {
            userId = it.userId
            userName = it.userName
        }
    }
}

@Parcelize
data class RspShippingDeliverAddress(
        var consCompanyName: String = "",//发货单位
        var address: String = ""//详细地址
) : ResultData(), Parcelable