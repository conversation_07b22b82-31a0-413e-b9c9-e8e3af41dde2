package com.zczy.cargo_owner.production.model

import com.sfh.lib.mvvm.service.BaseViewModel
import com.zczy.cargo_owner.production.req.ReqShippingDeliverAddress
import com.zczy.cargo_owner.production.req.setUserInfo

/**
 *  user: ssp
 *  time: 2021/5/25 14:47
 *  desc: 收发货地址
 */

class ShippingDeliverAddressModel : BaseViewModel() {
    fun queryList(type: String) {
        val reqShippingDeliverAddress = ReqShippingDeliverAddress(type = type)
        reqShippingDeliverAddress.setUserInfo()
        execute(reqShippingDeliverAddress) {
            if (it.success()) {
                setValue("onQuerySuccess", it.data)
            } else {
                setValue("onQueryError", it.msg)
            }
        }

    }
}