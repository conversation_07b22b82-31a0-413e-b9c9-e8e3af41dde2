package com.zczy.cargo_owner.production

import android.content.Context
import android.content.Intent
import android.os.Bundle
import com.sfh.lib.event.RxBusEvent
import com.sfh.lib.mvvm.annotation.LiveDataMatch
import com.zczy.cargo_owner.R
import com.zczy.cargo_owner.production.adapter.ProductionCapacityAdapter
import com.zczy.cargo_owner.production.model.ProductionCapacityModel
import com.zczy.cargo_owner.production.req.RspProductionList
import com.zczy.cargo_owner.production.req.RxProductionAddEditSuccess
import com.zczy.comm.http.entity.PageList
import com.zczy.comm.ui.BaseActivity
import com.zczy.comm.utils.dp2px
import com.zczy.comm.widget.pulltorefresh.CommEmptyView
import kotlinx.android.synthetic.main.production_capacity_activity.*

/**
 *  user: ssp
 *  time: 2021/5/21 10:59
 *  desc: 生产能力
 */

class ProductionCapacityActivity : BaseActivity<ProductionCapacityModel>() {

    private val mAdapter: ProductionCapacityAdapter = ProductionCapacityAdapter()

    companion object {

        @JvmStatic
        fun jumpUi(context: Context) {
            val intent = Intent(context, ProductionCapacityActivity::class.java)
            context.startActivity(intent)
        }
    }

    override fun getLayout(): Int {
        return R.layout.production_capacity_activity
    }

    override fun initData() {
    }

    override fun bindView(bundle: Bundle?) {
        swipe_refresh.apply {
            addItemDecorationSize(dp2px(7f))
            setAdapter(mAdapter, true)
            setEmptyView(CommEmptyView.creatorDef(context))
            addOnItemChildClickListener { adapter, view, position ->
                when (view?.id) {
                    R.id.tvEdit -> {
                        // 编辑
                        val item = adapter?.getItem(position) as RspProductionList
                        ProductionCapacityAddEditActivity.jumpUi(this@ProductionCapacityActivity, true, item.id)
                    }
                    R.id.tvDelete -> {
                        // 删除
                        val item = adapter?.getItem(position) as RspProductionList
                        viewModel?.deleteItem(item.id)
                    }
                }
            }
            setOnLoadListener2 { nowPage -> viewModel?.queryList(nowPage) }
            onAutoRefresh()
        }
        tvAdd.setOnClickListener {
            ProductionCapacityAddEditActivity.jumpUi(this@ProductionCapacityActivity, false, "")
        }
    }

    @LiveDataMatch
    open fun onQueryListSuccess(data: PageList<RspProductionList>?) {
        swipe_refresh.onRefreshCompale(data)
    }

    @LiveDataMatch
    open fun onQueryListError(data: String?) {
        swipe_refresh.onLoadMoreFail()
        showToast(data ?: "")
    }

    @LiveDataMatch
    open fun onDeleteSuccess(data: String?) {
        swipe_refresh.onAutoRefresh()
        showToast(data ?: "")
    }

    @RxBusEvent(from = " 新增 修改")
    open fun onRxShipmentAddEditSuccess(data: RxProductionAddEditSuccess) {
        if (data.success) {
            swipe_refresh.onAutoRefresh()
        }
    }
}