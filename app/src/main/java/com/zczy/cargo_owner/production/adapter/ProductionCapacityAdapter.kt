package com.zczy.cargo_owner.production.adapter

import com.chad.library.adapter.base.BaseQuickAdapter
import com.chad.library.adapter.base.BaseViewHolder
import com.zczy.cargo_owner.R
import com.zczy.cargo_owner.production.req.RspProductionList

/**
 *  user: ssp
 *  time: 2021/5/21 11:13
 *  desc: 生产能力
 */

class ProductionCapacityAdapter : BaseQuickAdapter<RspProductionList, BaseViewHolder>(R.layout.production_capacity_item) {

    override fun convert(helper: BaseViewHolder?, item: RspProductionList?) {
        helper?.apply {
            item?.apply {
                setText(R.id.tvTitle, despatchPlace)
                setText(R.id.tvContent, "生产能力：$productionCapacity")
                setText(R.id.tvTime, productionTime)
                addOnClickListener(R.id.tvEdit)
                addOnClickListener(R.id.tvDelete)
            }
        }
    }

}