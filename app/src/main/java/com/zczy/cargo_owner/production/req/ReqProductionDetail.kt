package com.zczy.cargo_owner.production.req

import com.zczy.cargo_owner.shipment.req.ReqShipmentPlanAdd
import com.zczy.comm.CommServer
import com.zczy.comm.http.entity.BaseNewRequest
import com.zczy.comm.http.entity.BaseRsp
import com.zczy.comm.http.entity.ResultData

/**
 *  user: ssp
 *  time: 2021/5/21 13:58
 *  desc: 生产能力详情
 */

class ReqProductionDetail(
        var userId: String = "",
        var userName: String = "",
        var childUserId: String = "",
        var childUserName: String = "",
        var id: String = ""
) : BaseNewRequest<BaseRsp<RspProductionDetail>>("oms-app/productionCapacity/queryOneProductionCapacity")


fun ReqProductionDetail.setUserInfo() {
    val login = CommServer.getUserServer().login
    login?.let {
        if (it.isChild) {
            childUserId = it.childId
            childUserName = it.userName
        } else {
            userId = it.userId
            userName = it.userName
        }
    }
}

data class RspProductionDetail(
        var despatchPlace: String = "",
        var id: String = "",
        var productionCapacity: String = "",
        var productionTime: String = ""
) : ResultData()