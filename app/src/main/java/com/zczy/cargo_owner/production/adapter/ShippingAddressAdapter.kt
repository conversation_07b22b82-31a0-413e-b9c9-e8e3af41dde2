package com.zczy.cargo_owner.production.adapter

import com.chad.library.adapter.base.BaseQuickAdapter
import com.chad.library.adapter.base.BaseViewHolder
import com.zczy.cargo_owner.R
import com.zczy.cargo_owner.production.req.RspShippingDeliverAddress

/**
 *  user: ssp
 *  time: 2021/5/21 11:13
 *  desc: 选择收货地址
 */

class ShippingAddressAdapter : BaseQuickAdapter<RspShippingDeliverAddress, BaseViewHolder>(R.layout.shipping_address_item) {

    override fun convert(helper: BaseViewHolder?, item: RspShippingDeliverAddress?) {
        helper?.apply {
            item?.let {
                setText(R.id.tvTitle, it.consCompanyName)
                setText(R.id.tvContent, it.address)
            }
        }

    }
}