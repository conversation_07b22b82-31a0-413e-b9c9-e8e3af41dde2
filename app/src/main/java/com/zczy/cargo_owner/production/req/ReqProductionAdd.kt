package com.zczy.cargo_owner.production.req

import com.zczy.comm.CommServer
import com.zczy.comm.http.entity.BaseNewRequest
import com.zczy.comm.http.entity.BaseRsp
import com.zczy.comm.http.entity.ResultData

/**
 *  user: ssp
 *  time: 2021/5/21 13:58
 *  desc: 生产能力新增
 */

class ReqProductionAdd(
        var userId: String = "",
        var userName: String = "",
        var childUserId: String = "",
        var childUserName: String = "",
        var despatchPlace: String = "",
        var productionCapacity: String = "",
        var productionTime: String = ""
) : BaseNewRequest<BaseRsp<ResultData>>("oms-app/productionCapacity/insertOneProductionCapacity")


fun ReqProductionAdd.setUserInfo() {
    val login = CommServer.getUserServer().login
    login?.let {
        if (it.isChild) {
            childUserId = it.childId
            childUserName = it.userName
        } else {
            userId = it.userId
            userName = it.userName
        }
    }
}