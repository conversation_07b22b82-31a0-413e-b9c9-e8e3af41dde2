package com.zczy.cargo_owner.home.mode

import com.sfh.lib.mvvm.service.BaseViewModel
import com.sfh.lib.utils.UtilLog
import com.zczy.cargo_owner.home.mode.request.Req37CheckHaveBeyondSignTimeAndOverdueTime
import com.zczy.cargo_owner.home.mode.request.ReqQueryConsignorAccountType
import com.zczy.cargo_owner.home.mode.request.ReqQueryConsignorContractNotice
import com.zczy.cargo_owner.home.mode.request.ReqQueryCount
import com.zczy.cargo_owner.home.mode.request.ReqQueryDeliveryDepositWarnState
import com.zczy.cargo_owner.home.mode.request.ReqQueryIndexCornerMarker
import com.zczy.cargo_owner.home.mode.request.ReqQueryOrderOverDueCount
import com.zczy.cargo_owner.home.mode.request.ReqQuerySettleRateTips
import com.zczy.cargo_owner.home.mode.request.ReqQueryUserOrderInfoForMobile
import com.zczy.cargo_owner.home.mode.request.ReqTrainingState
import com.zczy.cargo_owner.home.mode.request.RequestBanner
import com.zczy.cargo_owner.home.mode.request.RspQueryConsignorAccountType
import com.zczy.cargo_owner.home.mode.request.RspQueryConsignorContractNotice
import com.zczy.cargo_owner.home.mode.request.RspQueryCount
import com.zczy.cargo_owner.home.mode.request.RspQueryOrderOverDueCount
import com.zczy.cargo_owner.home.mode.request.RspQuerySettleRateTips
import com.zczy.cargo_owner.home.mode.request.RspQueryUserOrderInfoForMobile
import com.zczy.cargo_owner.home.mode.rsp.RspBannerData
import com.zczy.cargo_owner.home.mode.rsp.RspCornerMarker
import com.zczy.cargo_owner.invoice.req.ReqQueryCanApplyNum
import com.zczy.cargo_owner.invoice.req.RspQueryCanApplyNum
import com.zczy.cargo_owner.libcomm.config.SubUserAuthUtils
import com.zczy.cargo_owner.libcomm.req.ReqCheckUserIsFreeze
import com.zczy.cargo_owner.libcomm.req.ReqQuerySubUserAuthList4App
import com.zczy.cargo_owner.libcomm.req.RspQuerySubUserAuthList4AppV1
import com.zczy.cargo_owner.order.reminder.req.ReqTransportRemindTop
import com.zczy.cargo_owner.order.reminder.req.RspTransportRemindTop
import com.zczy.comm.CommServer
import com.zczy.comm.data.entity.ELogin
import com.zczy.comm.http.entity.BaseNewRequest
import com.zczy.comm.http.entity.BaseRsp
import com.zczy.comm.http.entity.ResultData
import com.zczy.comm.utils.json.toJson
import io.reactivex.Observable
import io.reactivex.Observer
import io.reactivex.android.schedulers.AndroidSchedulers
import io.reactivex.disposables.Disposable
import io.reactivex.schedulers.Schedulers

/**
 * PS:
 * Created by sdx on 2018/10/18.
 */
class MainFragmentModel : BaseViewModel() {
    override fun eventOnOff(): Boolean = true

    fun doRefreshNetRequest() {
        // task 处理 Observable  BaseNewRequest.task 没有执行onComplete 不能使用
        val task =
            fun(b: BaseNewRequest<out BaseRsp<out ResultData>>): Observable<BaseRsp<out ResultData>> {
                return Observable
                    .create<BaseRsp<out ResultData>> { emitter ->
                        emitter.onNext(b.sendRequest())
                        emitter.onComplete()
                    }
                    .subscribeOn(Schedulers.io()) // 不要删，不然mergeDelayError 不起效果
                    .observeOn(AndroidSchedulers.mainThread())
                    .onErrorResumeNext(Observable.empty())
            }

        val list = mutableListOf<Observable<BaseRsp<out ResultData>>>()
        // 查询子账号权限
        val userServer = CommServer.getUserServer()
        if (SubUserAuthUtils.isChild()) {
            list.add(task(ReqQuerySubUserAuthList4App(subUserId = userServer?.login?.childId)))
        }
        // 获取广告信息
        list.add(task(RequestBanner()))
        // 获取 首页角标
        list.add(task(ReqQueryIndexCornerMarker()))
        list.add(task(ReqTransportRemindTop()))
        //
        list.add(task(ReqQueryCount()))
        list.add(task(ReqQueryConsignorAccountType()))
        list.add(task(ReqQueryOrderOverDueCount()))
        // 手机端首页运单状态统计查询（已结算单数、运输中单数）
        if (SubUserAuthUtils.isChild()) {
            list.add(task(ReqQueryUserOrderInfoForMobile(childUserId = userServer?.login?.childId)))
        } else {
            list.add(task(ReqQueryUserOrderInfoForMobile()))
        }
        list.add(task(ReqQueryDeliveryDepositWarnState()))
        list.add(task(ReqQueryCanApplyNum()))
        list.add(task(ReqQuerySettleRateTips()))
        list.add(task(ReqQueryConsignorContractNotice()))
        Observable
            .mergeArrayDelayError(*list.toTypedArray())
            .subscribeOn(Schedulers.io())
            .observeOn(AndroidSchedulers.mainThread())
            .subscribe(object : Observer<BaseRsp<out ResultData>> {
                override fun onSubscribe(d: Disposable) {
                    UtilLog.e("MainFragmentModel", "onSubscribe")
                    putDisposable(d)
                }

                override fun onNext(t: BaseRsp<out ResultData>) {
                    UtilLog.e("MainFragmentModel", "onNext = " + t.toJson())
                    if (t.success()) {
                        val data = t.data
                        when (data) {
                            is RspQueryCount -> {
                                setValue("onRspQueryCount", data)
                            }

                            is RspQueryConsignorAccountType -> {
                                setValue("onRspQueryConsignorAccountType", data)
                            }

                            is RspQueryOrderOverDueCount -> {
                                setValue("onQueryOrderOverDueCount", data)
                            }

                            is RspQuerySubUserAuthList4AppV1 -> {
                                SubUserAuthUtils.save(data)
                            }

                            is RspBannerData -> {
                                setValue("onSuccessBanner", data)
                            }

                            is RspCornerMarker -> {
                                setValue("onQueryIndexCornerMarkerSuccess", data)
                            }

                            is RspQueryUserOrderInfoForMobile -> {
                                setValue("onQueryUserOrderInfoForMobile", data)
                            }

                            is RspTransportRemindTop -> {
                                setValue("onTransportTopSuccess", data)
                            }

                            is RspQueryCanApplyNum -> {
                                setValue("queryCanApplyNumSuccess", data)
                            }

                            is RspQuerySettleRateTips -> {
                                setValue("querySettleRateTipsSuccess", data)
                            }

                            is RspQueryConsignorContractNotice -> {
                                setValue("queryConsignorContractNoticeSuccess", data)
                            }
                        }
                    } else {
                        showToast(t.msg)
                    }
                }

                override fun onError(e: Throwable) {
                    UtilLog.e("MainFragmentModel", "onError = " + e.message)
                    hideLoading()
                    setValue("refreshEnd")
                }

                override fun onComplete() {
                    UtilLog.e("MainFragmentModel", "onComplete")
                    hideLoading()
                    setValue("refreshEnd")
                }
            })
    }

    // 查询用户是否被冻结
    fun checkUserIsFreeze(runnable: Runnable) {
        val login: ELogin? = CommServer.getUserServer().login
        val userId = login?.userId ?: ""
        execute(
            true,
            ReqCheckUserIsFreeze(userId = userId)
        ) { p0 ->
            // 0否 1是
            if (p0.success()) {
                if (p0.data?.freezeFlag ?: "" != "1") {
                    runnable.run()
                } else {
                    showDialogToast(p0.data?.resultMsg)
                }
            } else {
                showDialogToast(p0.msg)
            }
        }
    }


    fun queryTrainingState() {
        this.execute(
            ReqTrainingState()
        ) { t ->
            if (t.success()) {
                setValue("onTrainingState", t)
            }
        }
    }

    fun checkHaveBeyondSignTimeAndOverdueTime() {
        this.execute(
            Req37CheckHaveBeyondSignTimeAndOverdueTime()
        ) { t ->
            if (t.success()) {
                setValue("onCheckHaveBeyondSignTimeAndOverdueTimeSuccess", t.data)
            }
        }
    }
}