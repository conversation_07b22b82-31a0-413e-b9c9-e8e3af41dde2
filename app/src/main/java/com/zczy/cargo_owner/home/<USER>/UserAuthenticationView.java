package com.zczy.cargo_owner.home.view;

import android.content.Context;
import android.graphics.Color;
import androidx.annotation.Nullable;
import android.util.AttributeSet;
import android.view.Gravity;
import android.widget.ImageView;
import android.widget.LinearLayout;
import android.widget.TextView;

import com.zczy.cargo_owner.R;
import com.zczy.comm.data.role.ViewStatus;

/**
 * 功能描述: 认证状态UI
 *
 * <AUTHOR> 孙飞虎
 * @company 中储智运科技股份有限公司
 * @copyright （版权）中储南京智慧物流科技有限公司所有
 * @date 2018/10/30
 */
public class UserAuthenticationView extends LinearLayout {
    ImageView ivIcon;
    TextView tvContent;
    ImageView ivToast;

    public UserAuthenticationView(Context context) {
        super(context);
        this.initView();
    }

    public UserAuthenticationView(Context context, @Nullable AttributeSet attrs) {
        super(context, attrs);
        this.initView();
    }

    public UserAuthenticationView(Context context, @Nullable AttributeSet attrs, int defStyleAttr) {
        super(context, attrs, defStyleAttr);
        this.initView();
    }


    private void initView() {
        inflate(this.getContext(), R.layout.user_authentication_view, this);
        this.setGravity(Gravity.CENTER);
        this.ivIcon = findViewById(R.id.ivIcon);
        this.tvContent = findViewById(R.id.tvContent);
        this.ivToast = findViewById(R.id.ivToast);
    }

    public void onAuthentShow(ViewStatus status) {

        switch (status) {
            case UNAUTO:
                //未认证
            case AUTO: {
                //认证
                this.showUncertified();
                break;
            }
            case PERSON3:
                //审核不通过
            case PERSON6:
                //终审不通过
            case NOPASS: {
                //未通过
                showFailure();
                break;
            }
            case PERSON1:
                //待审核
            case PERSON4:
                //待终审
            case CHECK: {
                //审核中
                showAudit();
                break;
            }
            case PERSON2:
                //审核通过
            case PERSON5:
                //已认证
            case AUTOED: {
                //已认证
                showAuthentication();
                break;
            }

            default:
                break;
        }

    }


    /***
     * 未认证, 去认证
     */
    public void showUncertified() {
        this.setBackgroundResource(R.drawable.file_user_author_un);
        this.ivIcon.setImageResource(R.drawable.user_authentication_rlsb);
        this.tvContent.setTextColor(Color.parseColor("#ffffffff"));
        this.tvContent.setText("未认证, 去认证");
        this.ivToast.setVisibility(VISIBLE);
    }

    /***
     * 认证OK
     */
    public void showAuthentication() {
        this.setBackgroundResource(R.drawable.file_user_author_ok);
        this.ivIcon.setImageResource(R.drawable.user_authentication_rzok);
        this.tvContent.setTextColor(Color.parseColor("#fffdf1db"));
        this.tvContent.setText("已认证");
        this.ivToast.setVisibility(GONE);
    }

    /***
     * 认证失败, 去认证
     */
    private void showFailure() {
        this.setBackgroundResource(R.drawable.file_user_author_un);
        this.ivIcon.setImageResource(R.drawable.user_authentication_rlsb);
        this.tvContent.setTextColor(Color.parseColor("#ffffffff"));
        this.tvContent.setText("认证失败, 去认证");
        this.ivToast.setVisibility(VISIBLE);
    }


    /***
     *  审核中
     */
    private void showAudit() {
        this.setBackgroundResource(R.drawable.file_user_author_un);
        this.ivIcon.setImageResource(R.drawable.user_authentication_shz);
        this.tvContent.setTextColor(Color.parseColor("#ffffffff"));
        this.tvContent.setText("审核中");
        this.ivToast.setVisibility(GONE);
    }


}
