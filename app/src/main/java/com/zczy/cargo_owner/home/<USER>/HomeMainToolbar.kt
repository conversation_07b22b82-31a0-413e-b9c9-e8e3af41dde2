package com.zczy.cargo_owner.home.view

import android.animation.Animator
import android.animation.ObjectAnimator
import android.content.Context
import android.graphics.Color
import androidx.constraintlayout.widget.ConstraintLayout
import android.util.AttributeSet
import android.view.LayoutInflater
import android.view.View
import android.view.View.OnClickListener
import android.widget.ImageView
import android.widget.LinearLayout
import android.widget.TextView
import com.sfh.lib.utils.UtilLog
import com.zczy.cargo_owner.R
import com.zczy.libstyle.ZczySkinManager
import com.zczy.libstyle.ZczySkinType
import com.zczy.comm.utils.dp2px
import com.zczy.comm.utils.getResDrawable
import q.rorbin.badgeview.Badge
import q.rorbin.badgeview.QBadgeView


/**
 * PS: 主页的 toolbar
 * Created by sdx on 2018/11/1.
 */
class HomeMainToolbar(context: Context, attrs: AttributeSet?, defStyle: Int) :
    LinearLayout(context, attrs, defStyle) {

    constructor(context: Context) : this(context, null, 0)

    constructor(context: Context, attrs: AttributeSet?) : this(context, attrs, 0)

    private val TAG = "HomeMainToolbar"

    private val btnMessage: TextView
    private val viewSearch: View
    private val btnCustomService: ImageView
    private val tvUserSao: ImageView
    private val badge: Badge

    private val dp60 = dp2px(60f)
    private val dp15 = dp2px(15f)

    private var startMargin: Int = dp60
    private var endMargin: Int = dp15

    private var badgeNumber: Int = 0
    private var isDeformationState = false
    private val durationTime: Long = 200L
    var toolBarClickListener: ToolBarClickListener? = null

    private val onClickListener = OnClickListener { v ->
        when (v.id) {
            R.id.img_message -> {
                toolBarClickListener?.onClickMessage()
            }
            R.id.ed_search_bg -> {
                toolBarClickListener?.onClickSearch()
            }
            R.id.img_custom_service -> {
                toolBarClickListener?.onClickCustomService()
            }
            R.id.tv_user_sao -> {
                toolBarClickListener?.onClickScan()
            }
        }
    }

    init {
        LayoutInflater.from(context).inflate(R.layout.home_main_toolbar_include, this)

        btnMessage = findViewById(R.id.img_message)
        viewSearch = findViewById(R.id.ed_search_bg)
        btnCustomService = findViewById(R.id.img_custom_service)
        tvUserSao = findViewById(R.id.tv_user_sao)

        badge = QBadgeView(getContext())
            .bindTarget(btnMessage)
            .apply {
                badgeBackground = getResDrawable(R.drawable.home_shape_badge_bg)
                setBadgeTextSize(10f, true)
                isShowShadow = true
                setGravityOffset(0f, 6f, true)
            }

        btnMessage.setOnClickListener(onClickListener)
        viewSearch.setOnClickListener(onClickListener)
        btnCustomService.setOnClickListener(onClickListener)
        tvUserSao.setOnClickListener(onClickListener)

        //换肤监听
        ZczySkinManager.get().addLinster {

            it.setImageResource(tvUserSao, R.drawable.home_scan_white)
            it.setImageResource(btnCustomService, R.drawable.home_customer_gray)
            it.setBackgroundResource(viewSearch, R.drawable.home_main_tool_search_bg)

            when (it) {
                ZczySkinType.SkinDW,  ZczySkinType.SkinGQ ,ZczySkinType.SkinZQ,ZczySkinType.SkinNY -> {
                    btnMessage.setTextColor(Color.WHITE)
                }
                else -> {
                    btnMessage.setTextColor(Color.parseColor("#80333333"))
                }
            }
        }
    }

    fun setMessageNum(num: Int) {
        this.badgeNumber = num
        badge.badgeNumber = num
    }

    private val messageAnimator by lazy {
        ObjectAnimator.ofFloat(btnMessage, "alpha", 1f, 0f).apply {
            duration = durationTime
            addListener(object : Animator.AnimatorListener {
                override fun onAnimationRepeat(animation: Animator?) {
                    UtilLog.e(TAG, "messageAnimator onAnimationRepeat")
                }

                override fun onAnimationCancel(animation: Animator?) {
                    UtilLog.e(TAG, "messageAnimator onAnimationCancel")
                    btnMessage.visibility = View.VISIBLE
                }

                override fun onAnimationStart(animation: Animator?) {
                    UtilLog.e(TAG, "messageAnimator onAnimationStart")
                }

                override fun onAnimationEnd(animation: Animator?) {
                    UtilLog.e(TAG, "messageAnimator onAnimationEnd")
                    btnMessage.visibility = View.GONE
                }

            })
        }
    }

    private val customServiceAnimator by lazy {
        ObjectAnimator.ofFloat(btnCustomService, "alpha", 1f, 0f).apply {
            duration = durationTime
            addListener(object : Animator.AnimatorListener {
                override fun onAnimationRepeat(animation: Animator?) {
                    UtilLog.e(TAG, "customServiceAnimator onAnimationRepeat")
                }

                override fun onAnimationCancel(animation: Animator?) {
                    UtilLog.e(TAG, "customServiceAnimator onAnimationCancel")
                    btnCustomService.visibility = View.VISIBLE
                }

                override fun onAnimationStart(animation: Animator?) {
                    UtilLog.e(TAG, "customServiceAnimator onAnimationStart")
                }

                override fun onAnimationEnd(animation: Animator?) {
                    UtilLog.e(TAG, "customServiceAnimator onAnimationEnd")
                    btnCustomService.visibility = View.GONE
                }
            })
        }
    }

    private val messageAnimatorStop by lazy {
        ObjectAnimator.ofFloat(btnMessage, "alpha", 0f, 1f).apply {
            duration = durationTime
            addListener(object : Animator.AnimatorListener {
                override fun onAnimationRepeat(animation: Animator?) {
                }

                override fun onAnimationCancel(animation: Animator?) {
                    btnMessage.visibility = View.VISIBLE
                }

                override fun onAnimationStart(animation: Animator?) {
                    btnMessage.visibility = View.VISIBLE
                }

                override fun onAnimationEnd(animation: Animator?) {
                }
            })
        }
    }

    private val customServiceAnimatorStop by lazy {
        ObjectAnimator.ofFloat(btnCustomService, "alpha", 0f, 1f).apply {
            duration = durationTime
            addListener(object : Animator.AnimatorListener {
                override fun onAnimationRepeat(animation: Animator?) {
                }

                override fun onAnimationCancel(animation: Animator?) {
                }

                override fun onAnimationStart(animation: Animator?) {
                    btnCustomService.visibility = View.VISIBLE
                }

                override fun onAnimationEnd(animation: Animator?) {
                }
            })
        }
    }


    private val viewWrapper = ViewWrapper(viewSearch)

    private val searchAnimator by lazy {

        ObjectAnimator.ofInt(viewWrapper, "marginWrapper", startMargin, endMargin).apply {
            duration = durationTime
            addListener(object : Animator.AnimatorListener {
                override fun onAnimationRepeat(animation: Animator?) {
                }

                override fun onAnimationCancel(animation: Animator?) {
                    viewWrapper.setMarginWrapper(endMargin)
                }

                override fun onAnimationStart(animation: Animator?) {
                }

                override fun onAnimationEnd(animation: Animator?) {
                    viewWrapper.setMarginWrapper(endMargin)
                }
            })
        }
    }

    private val searchAnimatorStop by lazy {
        ObjectAnimator.ofInt(viewWrapper, "marginWrapper", endMargin, startMargin).apply {
            duration = durationTime
            addListener(object : Animator.AnimatorListener {
                override fun onAnimationRepeat(animation: Animator?) {
                }

                override fun onAnimationCancel(animation: Animator?) {
                    viewWrapper.setMarginWrapper(startMargin)
                }

                override fun onAnimationStart(animation: Animator?) {
                }

                override fun onAnimationEnd(animation: Animator?) {
                    viewWrapper.setMarginWrapper(startMargin)
                }
            })
        }
    }


    fun startDeformation() {
        if (isDeformationState) {
            return
        }
        UtilLog.e(TAG, "------ start")
        badge.badgeNumber = 0

        searchAnimatorStop.cancel()
        customServiceAnimatorStop.cancel()
        messageAnimatorStop.cancel()

        messageAnimator.start()
        customServiceAnimator.start()
        searchAnimator.start()

        isDeformationState = true
    }

    fun stopDeformation() {
        if (!isDeformationState) {
            return
        }
        UtilLog.e(TAG, "------ stop")
        badge.badgeNumber = badgeNumber

        searchAnimator.cancel()
        customServiceAnimator.cancel()
        messageAnimator.cancel()

        searchAnimatorStop.start()
        customServiceAnimatorStop.start()
        messageAnimatorStop.start()
        isDeformationState = false
    }

    class ViewWrapper(private var target: View) {
        private val layoutParams: ConstraintLayout.LayoutParams =
            target.layoutParams as ConstraintLayout.LayoutParams

        // 属性动画使用，不要删
        fun setMarginWrapper(margin: Int) {
            layoutParams.setMargins(margin, 0, margin, 0)
            target.layoutParams = layoutParams
            target.requestLayout()
        }
    }

    interface ToolBarClickListener {
        fun onClickMessage()
        fun onClickSearch()
        fun onClickCustomService()
        fun onClickScan()
    }
}