package com.zczy.cargo_owner.home.mode.request;

import com.sfh.lib.AppCacheManager;
import com.sfh.lib.rx.EmptyResult;
import com.sfh.lib.rx.RetrofitManager;
import com.zczy.comm.http.entity.BaseNewRequest;
import com.zczy.comm.http.entity.BaseRsp;
import com.zczy.comm.http.entity.ResultData;

import io.reactivex.disposables.Disposable;

public class ReqQueryAgentId extends BaseNewRequest<BaseRsp<ReqQueryAgentId.Agentid>> {

    public static class Agentid extends ResultData {
        String agentid;
    }

    public ReqQueryAgentId() {
        super("mms-app/customerService/queryAgentId");
    }

    public Disposable taskRun() {
        return RetrofitManager.executeSigin(this.getTask(), new EmptyResult<>());
    }

    @Override
    public void cacheResponse(BaseRsp<Agentid> data) {
        if (data.success()) {
            AppCacheManager.putCache("App_Agentid", data.getData().agentid);
        }
        super.cacheResponse(data);
    }
}
