package com.zczy.cargo_owner.home.mode.request

import com.zczy.comm.http.entity.BaseNewRequest
import com.zczy.comm.http.entity.BaseRsp
import com.zczy.comm.http.entity.ResultData

/**
 * 功能描述: 逾期首页提示
 * <AUTHOR>
 * wiki: http://wiki.zczy56.com/pages/viewpage.action?pageId=65183342
 * @date 2024/06/05 13:35
 */

class ReqQueryIfPopUpBox :
    BaseNewRequest<BaseRsp<RsqQueryIfPopUpBox>>("mms-app/plateFromPolicyChange/queryIfPopUpBox")

class RsqQueryIfPopUpBox(
    var ifPopUpBox: String? = null, //		为“1”时弹窗提示
    var text: String? = null, //		弹窗提示
) : ResultData()