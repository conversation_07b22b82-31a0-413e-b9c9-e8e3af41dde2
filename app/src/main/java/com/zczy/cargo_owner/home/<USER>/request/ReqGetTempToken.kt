package com.zczy.cargo_owner.home.mode.request

import com.zczy.comm.http.entity.BaseNewRequest
import com.zczy.comm.http.entity.BaseRsp
import com.zczy.comm.http.entity.ResultData

/**
 * 获取临时token(app端)
 * http://wiki.zczy56.com/pages/viewpage.action?pageId=65178644
 */

class ReqGetTempToken(
    var tenantCode:String = "ZCZY",//#需要指定登录的租户code，没有默认中储租户
    var systemFlag:Int = 34,//##需要指定跳转的系统来源端，    12.金融客户端  34.合同客户端 2.TMS-货主端
) : BaseNewRequest<BaseRsp<ETempToken>>("mms-app/mms/login/getTempToken")

data class ETempToken(
    var token: String = ""
):ResultData()
