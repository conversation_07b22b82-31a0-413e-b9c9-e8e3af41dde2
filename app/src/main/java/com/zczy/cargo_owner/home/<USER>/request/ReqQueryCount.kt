package com.zczy.cargo_owner.home.mode.request

import com.zczy.comm.http.entity.BaseNewRequest
import com.zczy.comm.http.entity.BaseRsp
import com.zczy.comm.http.entity.ResultData

/**
 * PS:获取订单角标信息
 * http://wiki.zczy56.com/pages/viewpage.action?pageId=16090097
 * Created by sdx on 2019-05-22.
 */
class ReqQueryCount : BaseNewRequest<BaseRsp<RspQueryCount>>("/oms-app/consignor/queryCount")

data class RspQueryCount(
    /** 结算申请数 */
    var settleCount: String = "",
    /** 发货单确认数 */
    var deliverCount: String = "",
    /** 快递管理数 */
    var expressCount: String = "",
    /** 我的违约 */
    var myBreakCount: String = "",
    /** 运单异常数据 */
    var exceptionOrderCount: String = "",
    /** 回单确认数 */
    var backTotalSize: String = "",
    /** 智运宝可用金额 */
    var accountMoney: String = "",
    /** 可用金额状态：0 正常，1 余额过少，2 余额不足 */
    var accountMoneyState: String = "",
    /** 每单保证金（默认500元） */
    var singleBondMoney: String = "",
    var usedMoney: String = "",
    var oweMoney: String = "",
    var diffMoneyReal: String = ""
) : ResultData()

class ReqQueryOrderOverDueCount : BaseNewRequest<BaseRsp<RspQueryOrderOverDueCount>>("/oms-app/order/overDueApp/queryOrderOverDueCount")

data class RspQueryOrderOverDueCount(
    /** 逾期总单数 */
    var orderOverDueCount: String = "",
) : ResultData()

class ReqQueryConsignorAccountType : BaseNewRequest<BaseRsp<RspQueryConsignorAccountType>>("/oms-app/consignor/queryConsignorAccountType")

data class RspQueryConsignorAccountType(
    //	1 现结帐户 2 现结帐户且临时账期 3.账期和短周期
    var accountType: String = "",
    //应付总运费 （空 不展示）
    var totalPayableAmount: String = "",
    //	最低转入金额 （空 不展示）
    var minimumTransferAmount: String = "",
    var todaySettleMoney: String = "",
    var totalFreight: String = "",
    var hideMoneyFlag: String = "",// 1显示 否则隐藏
) : ResultData()
