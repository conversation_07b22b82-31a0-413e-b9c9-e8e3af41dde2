package com.zczy.cargo_owner.home.mode.request

import com.zczy.comm.http.entity.BaseNewRequest
import com.zczy.comm.http.entity.BaseRsp
import com.zczy.comm.http.entity.ResultData

/**
 * 注释：WLHY-10313 货主首页提醒逾期消息
 * 时间：2024/12/25 0025 15:27
 * 作者：郭翰林
 */
class ReqCompulsoryHandleOverdueTip :
    BaseNewRequest<BaseRsp<RspCompulsoryHandleOverdueTip>>("oms-app/order/overDueApp/compulsoryHandleOverdueTip")


class RspCompulsoryHandleOverdueTip(
    var tipFlag: String? = null,
    var compulsoryHandleListOrderStr: String? = null
) : ResultData()