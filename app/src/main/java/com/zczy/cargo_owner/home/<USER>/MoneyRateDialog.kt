package com.zczy.cargo_owner.home.dialog

import android.os.Bundle
import android.view.View
import com.zczy.cargo_owner.R
import com.zczy.comm.ui.BaseDialog
import kotlinx.android.synthetic.main.money_rate_dialog.*

/**
 *  desc: 调整结算率配置触发运单下架 WLHY-8089
 *  user: 宋双朋
 *  time: 2024/11/14 8:49
 */
class MoneyRateDialog(var content: String?, var time: String?, var block: () -> Unit = {}) : BaseDialog() {

    override fun bindView(view: View, bundle: Bundle?) {
        tvContent.text = content ?: "--"
        tvTime.text = time ?: "--"
        tvOk.setOnClickListener {
            dismiss()
            block()
        }
    }

    override fun getDialogTag(): String {
        return "MoneyRateDialog"
    }

    override fun getDialogLayout(): Int {
        return R.layout.money_rate_dialog
    }

}