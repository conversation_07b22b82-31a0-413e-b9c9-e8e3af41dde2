package com.zczy.cargo_owner.home.mode.request

import com.zczy.comm.http.entity.BaseNewRequest
import com.zczy.comm.http.entity.BaseRsp
import com.zczy.comm.http.entity.ResultData

/**
 *  desc: 应收风险消息提醒
 *  user: ssp
 *  time: 2024/7/3 15:07
 */
class ReqQueryDeliveryDepositWarnState : BaseNewRequest<BaseRsp<RspQueryDeliveryDepositWarnState>>("/mms-app/member/queryDeliveryDepositWarnState")

class RspQueryDeliveryDepositWarnState(
    var deliveryDepositWarnState: String? = null, // 发货订金预警提醒状态 0-不提醒，1-提醒
) : ResultData()