package com.zczy.cargo_owner.home.view

import android.content.Context
import android.graphics.Color
import androidx.constraintlayout.widget.ConstraintLayout
import android.text.SpannableString
import android.text.SpannableStringBuilder
import android.text.Spanned
import android.text.style.ForegroundColorSpan
import android.util.AttributeSet
import android.view.LayoutInflater
import android.widget.ImageView
import android.widget.TextView
import com.zczy.cargo_owner.R
import com.zczy.libstyle.ZczySkinManager
import com.zczy.libstyle.ZczySkinType
import com.zczy.comm.utils.dp2px
import kotlinx.android.synthetic.main.home_main_balance_view.view.*

/**
 *  user: ssp
 *  time: 2021/7/13 14:47
 *  desc: 运输提醒
 */

class HomeMainTransportReminderView(context: Context, attrs: AttributeSet?, defStyle: Int) :
    ConstraintLayout(context, attrs, defStyle) {

    constructor(context: Context) : this(context, null, 0)

    constructor(context: Context, attrs: AttributeSet?) : this(context, attrs, 0)

    private var ivLook: ImageView
    private var tvOrderCount: TextView
    var onClickLookBtn: () -> Unit = {}

    init {
        LayoutInflater.from(context).inflate(R.layout.home_main_transport_reminder_view, this)

        setBackgroundResource(R.drawable.home_main_transport_reminder_bg)
        minHeight = dp2px(80f)

        tvOrderCount = findViewById(R.id.tvOrderCount)
        ivLook = findViewById(R.id.ivLook)
        ivLook.setOnClickListener {
            onClickLookBtn()
        }

        //换肤监听
       ZczySkinManager.get().addLinster {

            it.setBackgroundResource(this@HomeMainTransportReminderView,R.drawable.home_main_transport_reminder_bg)

        }
    }

    fun setNewData(remindCount: String) {

        val spannableStringBuilder = SpannableStringBuilder()
        val spannableString1 = SpannableString("您有 ")
        val spannableString2 = SpannableString(remindCount)
        spannableString2.setSpan(
            ForegroundColorSpan(Color.parseColor("#E23442")),
            0,
            spannableString2.length,
            Spanned.SPAN_EXCLUSIVE_EXCLUSIVE
        )
        val spannableString3 = SpannableString(" 条运输提醒待关注 ")
        spannableStringBuilder.append(spannableString1)
            .append(spannableString2)
            .append(spannableString3)
        tvOrderCount.text = spannableStringBuilder
    }
}