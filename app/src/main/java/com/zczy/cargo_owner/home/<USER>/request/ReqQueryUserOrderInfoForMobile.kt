package com.zczy.cargo_owner.home.mode.request

import com.zczy.comm.http.entity.BaseNewRequest
import com.zczy.comm.http.entity.BaseRsp
import com.zczy.comm.http.entity.ResultData

/**
 * 10 手机端首页运单状态统计查询（已结算单数、运输中单数）
 * http://wiki.zczy56.com/pages/viewpage.action?pageId=12355552
 */
data class ReqQueryUserOrderInfoForMobile(
        var childUserId: String? = ""
) : BaseNewRequest<BaseRsp<RspQueryUserOrderInfoForMobile>>
("/oms-app/order/common/queryUserOrderInfoForMobile")

data class RspQueryUserOrderInfoForMobile(
        var settleCount: String = "", // 用户已结算单数
        var transportCount: String = "" // 用户运输中单数
) : ResultData()
