package com.zczy.cargo_owner.home.mode.request;

import com.zczy.cargo_owner.home.mode.Config;
import com.zczy.comm.http.entity.BaseNewRequest;
import com.zczy.comm.http.entity.BaseRsp;

public class ReqQuerySingleDictConfig extends BaseNewRequest<BaseRsp<Config>> {

    String dictCode = "INSURANCE_MALL_SHOW";


    public ReqQuerySingleDictConfig() {
        super("mms-app/dictConfig/querySingleDictConfig");
    }

    public String getDictCode() {
        return dictCode;
    }

    public void setDictCode(String dictCode) {
        this.dictCode = dictCode;
    }
}
