package com.zczy.cargo_owner.home.dialog;

import android.content.Context;
import android.content.Intent;
import android.graphics.Color;
import android.net.Uri;
import android.os.Build;
import android.os.Bundle;
import android.text.TextUtils;
import android.view.KeyEvent;
import android.view.MotionEvent;
import android.view.View;
import android.view.ViewGroup;
import android.view.WindowManager;
import android.webkit.JavascriptInterface;
import android.widget.FrameLayout;

import androidx.annotation.Nullable;

import com.jaeger.library.StatusBarUtil;
import com.sfh.lib.ui.AbstractLifecycleActivity;
import com.tencent.smtt.export.external.interfaces.IX5WebChromeClient;
import com.tencent.smtt.sdk.ValueCallback;
import com.tencent.smtt.sdk.WebChromeClient;
import com.tencent.smtt.sdk.WebSettings;
import com.tencent.smtt.sdk.WebView;
import com.zczy.cargo_owner.home.fragment.MainFragment;
import com.zczy.cargo_owner.order.change.OrderChangeMainActivity;
import com.zczy.cargo_owner.order.consignor.OrderConsignorConfirmMainActivity;
import com.zczy.cargo_owner.order.detail.WaybillBean;
import com.zczy.cargo_owner.order.mileage.CargoGoodsMapActivity;
import com.zczy.cargo_owner.order.pledge.PledgeMainActivity;
import com.zczy.cargo_owner.order.reminder.TransportReminderHomeActivity;
import com.zczy.cargo_owner.order.violate.OrderViolateMainActivity;
import com.zczy.cargo_owner.report.ReportHomeActivity;
import com.zczy.cargo_owner.tickling.UserTicklingActivity;
import com.zczy.cargo_owner.user.coupon.CouponListActivity;
import com.zczy.cargo_owner.user.evaluate.EvaluateManagerActivity;
import com.zczy.cargo_owner.user.exception.WaybillExceptionActivity;
import com.zczy.cargo_owner.user.integral.UsersIntegralActivity;
import com.zczy.cargo_owner.user.questionnaire.QuestionnaireSurveyActivity;
import com.zczy.cargo_owner.user.satisfaction.SatisfactionEvaluationActivity;
import com.zczy.comm.X5BaseJavascriptInterface;
import com.zczy.comm.pluginserver.AMainServer;
import com.zczy.comm.utils.imageselector.ImageSelector;
import com.zczy.comm.utils.json.JsonUtil;
import com.zczy.comm.widget.AppToolber;
import com.zczy.comm.x5.X5WebChromeClient2;
import com.zczy.comm.x5.X5WebTitle;
import com.zczy.comm.x5.X5WebView;
import com.zczy.ui.R;

import java.io.ByteArrayOutputStream;
import java.io.File;
import java.io.FileInputStream;
import java.io.FileNotFoundException;
import java.io.IOException;
import java.util.List;

public class X5VideoWebNoToolBarActivity extends AbstractLifecycleActivity implements X5WebTitle, X5WebChromeClient2 {

    private ValueCallback<Uri> mUploadMessage;

    private ValueCallback<Uri[]> uploadMessage;

    public static void startContentUI(Context context, String url) {
        Intent intent = new Intent();
        intent.putExtra("url", url);
        intent.setClass(context, X5VideoWebNoToolBarActivity.class);
        context.startActivity(intent);
    }


    protected AppToolber toolbar;

    protected X5WebView webView;
    protected static final FrameLayout.LayoutParams COVER_SCREEN_PARAMS = new FrameLayout.LayoutParams(ViewGroup.LayoutParams.MATCH_PARENT, ViewGroup.LayoutParams.MATCH_PARENT);
    private View customView;
    private FrameLayout fullscreenContainer;
    private IX5WebChromeClient.CustomViewCallback customViewCallback;

    @Override
    protected void onCreate(@Nullable Bundle savedInstanceState) {

        super.onCreate(savedInstanceState);
        setContentView(R.layout.base_comm_web_activity);


        StatusBarUtil.setColor(this, Color.BLACK, 0);

        this.toolbar = findViewById(R.id.appToolber);
        this.toolbar.setVisibility(View.GONE);

        this.webView = findViewById(R.id.webLayout);
        final String url = getIntent().getStringExtra("url");

        if (TextUtils.isEmpty(url)) {
            return;
        }
        WebSettings webViewSettings = webView.getSettings();
        String baseAgent = webViewSettings.getUserAgentString();
        webViewSettings.setUserAgent(baseAgent + ";app/ANDROID");
        webView.addJavascriptInterface(jsUserInfoInterface, "android");
        this.webView.setChromeClient2(this);

        webView.setWebChromeClient(new WebChromeClient() {

            /*** 视频播放相关的方法 **/

            @Override
            public View getVideoLoadingProgressView() {
                FrameLayout frameLayout = new FrameLayout(X5VideoWebNoToolBarActivity.this);
                frameLayout.setLayoutParams(new ViewGroup.LayoutParams(ViewGroup.LayoutParams.MATCH_PARENT, ViewGroup.LayoutParams.MATCH_PARENT));
                return frameLayout;
            }

            @Override
            public void onShowCustomView(View view, IX5WebChromeClient.CustomViewCallback callback) {
                showCustomView(view, callback);
            }

            @Override
            public void onHideCustomView() {
                hideCustomView();
            }
        });

        this.webView.loadUrl(url);
    }

    /**
     * 视频播放全屏
     **/
    private void showCustomView(View view, IX5WebChromeClient.CustomViewCallback callback) {
        // if a view already exists then immediately terminate the new one
        if (customView != null) {
            callback.onCustomViewHidden();
            return;
        }

        X5VideoWebNoToolBarActivity.this.getWindow().getDecorView();

        FrameLayout decor = (FrameLayout) getWindow().getDecorView();
        fullscreenContainer = new FullscreenHolder(X5VideoWebNoToolBarActivity.this);
        fullscreenContainer.addView(view, COVER_SCREEN_PARAMS);
        decor.addView(fullscreenContainer, COVER_SCREEN_PARAMS);
        customView = view;
        setStatusBarVisibility(false);
        customViewCallback = callback;
    }

    /**
     * 隐藏视频全屏
     */
    private void hideCustomView() {
        if (customView == null) {
            return;
        }

        setStatusBarVisibility(true);
        FrameLayout decor = (FrameLayout) getWindow().getDecorView();
        decor.removeView(fullscreenContainer);
        fullscreenContainer = null;
        customView = null;
        customViewCallback.onCustomViewHidden();
        webView.setVisibility(View.VISIBLE);
    }

    /**
     * 全屏容器界面
     */
    static class FullscreenHolder extends FrameLayout {

        public FullscreenHolder(Context ctx) {
            super(ctx);
            setBackgroundColor(ctx.getResources().getColor(android.R.color.black));
        }

        @Override
        public boolean onTouchEvent(MotionEvent evt) {
            return true;
        }
    }

    private void setStatusBarVisibility(boolean visible) {
        int flag = visible ? 0 : WindowManager.LayoutParams.FLAG_FULLSCREEN;
        getWindow().setFlags(flag, WindowManager.LayoutParams.FLAG_FULLSCREEN);
    }

    @Override
    public boolean onKeyUp(int keyCode, KeyEvent event) {
        switch (keyCode) {
            case KeyEvent.KEYCODE_BACK:
                /** 回退键 事件处理 优先级:视频播放全屏-网页回退-关闭页面 */
                if (customView != null) {
                    hideCustomView();
                } else if (webView.canGoBack()) {
                    webView.goBack();
                } else {
                    finish();
                }
                return true;
            default:
                return super.onKeyUp(keyCode, event);
        }
    }

    @Override
    public void onReceivedTitle(WebView webView, String s) {

        this.toolbar.setTitle(s);
    }

    @Override
    public void openFileChooser(ValueCallback<Uri> uploadMsg) {

        if (this.mUploadMessage != null) {
            this.mUploadMessage.onReceiveValue(null);
            this.mUploadMessage = null;
        }
        this.mUploadMessage = uploadMsg;
        this.openSeletcImage();
    }

    @Override
    public boolean onShowFileChooser(WebView mWebView, ValueCallback<Uri[]> filePathCallback, WebChromeClient.FileChooserParams fileChooserParams) {

        if (this.uploadMessage != null) {
            this.uploadMessage.onReceiveValue(null);
            this.uploadMessage = null;
        }
        this.uploadMessage = filePathCallback;
        this.openSeletcImage();
        return true;
    }

    @Override
    protected void onDestroy() {
        ViewGroup view = (ViewGroup) getWindow().getDecorView();
        view.removeAllViews();
        if (this.webView != null) {
            this.webView.destroy();
        }
        super.onDestroy();
    }


    /***
     * 取消选择图库
     */
    public void cancleSeletcFile() {

        if (uploadMessage != null) {
            uploadMessage.onReceiveValue(null);
        }

        if (mUploadMessage != null) {
            mUploadMessage.onReceiveValue(null);
        }
    }

    /***
     * 上传文件
     * @param path
     */
    public void uploadFile(String path) {

        if (Build.VERSION.SDK_INT >= Build.VERSION_CODES.LOLLIPOP) {

            if (uploadMessage == null) {
                return;
            }
            Uri[] result = new Uri[]{Uri.fromFile(new File(path))};
            uploadMessage.onReceiveValue(result);
            uploadMessage = null;

        } else {
            if (null == mUploadMessage) {
                return;
            }
            Uri result = Uri.fromFile(new File(path));
            mUploadMessage.onReceiveValue(result);
            mUploadMessage = null;
        }
    }


    /**
     * 监听Back键按下事件,方法2:
     * 注意:
     * 返回值表示:是否能完全处理该事件
     * 在此处返回false,所以会继续传播该事件.
     * 在具体项目中此处的返回值视情况而定.
     */
    @Override
    public boolean onKeyDown(int keyCode, KeyEvent event) {
        if ((keyCode == KeyEvent.KEYCODE_BACK)) {
            if (null != webView) {
                if (webView.canGoBack()) {
                    webView.goBack();
                } else {
                    finish();
                }
            }
            return false;
        } else {
            return super.onKeyDown(keyCode, event);
        }
    }

    /***
     * 打开图库
     */
    public void openSeletcImage() {
        Intent i = new Intent(Intent.ACTION_GET_CONTENT);
        i.addCategory(Intent.CATEGORY_OPENABLE);
        i.setType("image/*");
        startActivityForResult(Intent.createChooser(i, "test"), 0);
    }


    @Override
    protected void onActivityResult(int requestCode, int resultCode, Intent data) {
        super.onActivityResult(requestCode, resultCode, data);
        if (resultCode == RESULT_OK) {
            switch (requestCode) {
                case 0:
                    if (null != mUploadMessage) {
                        Uri result = data == null || resultCode != RESULT_OK ? null
                                : data.getData();
                        mUploadMessage.onReceiveValue(result);
                        mUploadMessage = null;
                    }
                    if (null != uploadMessage) {
                        Uri result = data == null || resultCode != RESULT_OK ? null
                                : data.getData();
                        uploadMessage.onReceiveValue(new Uri[]{result});
                        uploadMessage = null;
                    }
                case 10086:
                    List<String> imageList = ImageSelector.obtainPathResult(data);
                    if (imageList != null && imageList.size() > 0) {
                        String picUrl = imageList.get(0);
                        if (TextUtils.isEmpty(picUrl)) {
                            showDialogToast("文件损坏，请重新选择文件");
                            return;
                        }
                        sendToJsFun(picUrl);
                    }
                    break;
                default:
                    break;
            }
        } else if (resultCode == RESULT_CANCELED) {
            if (null != mUploadMessage) {
                mUploadMessage.onReceiveValue(null);
                mUploadMessage = null;
            }
        }
    }

    private void sendToJsFun(String picUrl) {
//        File file = new File(picUrl);
//        String photo = Base64.encodeToString(file2Byte(file), Base64.DEFAULT).replaceAll("\r|\n", "");
//        ImageUpload upload = new ImageUpload();
//        upload.filePath = photo;
//        Gson gson = new Gson();
//        String uploadJson = gson.toJson(upload);
//        //webView.loadUrl("javascript:getImageFunc(" + uploadJson + ")");
//        String path = String.format("{filePath:'%s'}", photo);
//        String s = "javascript:getImageFunc(" + path + ")";
//        webView.evaluateJavascript(s, s1 -> {
//
//        });

    }

    public static byte[] file2Byte(File tradeFile) {
        byte[] buffer = null;
        try {
            FileInputStream fis = new FileInputStream(tradeFile);
            ByteArrayOutputStream bos = new ByteArrayOutputStream();
            byte[] b = new byte[1024];
            int n;
            while ((n = fis.read(b)) != -1) {
                bos.write(b, 0, n);
            }
            fis.close();
            bos.close();
            buffer = bos.toByteArray();
        } catch (FileNotFoundException e) {
            e.printStackTrace();
        } catch (IOException e) {
            e.printStackTrace();
        }
        return buffer;
    }

    private X5BaseJavascriptInterface jsUserInfoInterface = new X5BaseJavascriptInterface(this) {
        @JavascriptInterface
        public void viewRoute(String orderDetail) {
            WaybillBean waybillBean = JsonUtil.toJsonObject(orderDetail, WaybillBean.class);
            CargoGoodsMapActivity.startUI(X5VideoWebNoToolBarActivity.this, waybillBean.getOrderId());
        }

        @JavascriptInterface
        public void problemFeedback() {
            // 问题反馈
            UserTicklingActivity.start(X5VideoWebNoToolBarActivity.this);
        }

        @JavascriptInterface
        public void dispatchService() {
            // 发货单确认
            OrderConsignorConfirmMainActivity.start(X5VideoWebNoToolBarActivity.this);
        }

        @JavascriptInterface
        public void waybillException() {
            // 运单异常管理
            WaybillExceptionActivity.startUI(X5VideoWebNoToolBarActivity.this);
        }

        @JavascriptInterface
        public void waybillChange() {
            // 变更承运
            OrderChangeMainActivity.start(X5VideoWebNoToolBarActivity.this);
        }

        @JavascriptInterface
        public void waybillCancel() {
            // 违约管理
            OrderViolateMainActivity.start(X5VideoWebNoToolBarActivity.this);
        }

        @JavascriptInterface
        public void returnDepositService() {
            // 押金管理
            PledgeMainActivity.start(X5VideoWebNoToolBarActivity.this);
        }

        @JavascriptInterface
        public void reportService() {
            // 统计报表
            ReportHomeActivity.jumpPage(X5VideoWebNoToolBarActivity.this);
        }

        @JavascriptInterface
        public void transportReminder() {
            // 运输提醒
            TransportReminderHomeActivity.jumpPage(X5VideoWebNoToolBarActivity.this);
        }

        @JavascriptInterface
        public void pointService() {
            // 积分管理
            UsersIntegralActivity.start(X5VideoWebNoToolBarActivity.this);
        }

        @JavascriptInterface
        public void couponService() {
            // 优惠券管理
            CouponListActivity.jumpPage(X5VideoWebNoToolBarActivity.this);
        }

        @JavascriptInterface
        public void wayBillEvaluation() {
            // 评价管理
            EvaluateManagerActivity.start(X5VideoWebNoToolBarActivity.this);
        }

        @JavascriptInterface
        public void onlineService() {
            // 打开在线客服
            AMainServer.getPluginServer().openLineServer(X5VideoWebNoToolBarActivity.this);
        }

        @JavascriptInterface
        public void serviceEvaluation() {
            // 客服满意度评价
            SatisfactionEvaluationActivity.start(X5VideoWebNoToolBarActivity.this);
        }

        @JavascriptInterface
        public void customerResearch() {
            // 问卷调查
            QuestionnaireSurveyActivity.start(X5VideoWebNoToolBarActivity.this);
        }
    };
}
