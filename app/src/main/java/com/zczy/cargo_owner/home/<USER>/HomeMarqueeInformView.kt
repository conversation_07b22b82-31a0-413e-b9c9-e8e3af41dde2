package com.zczy.cargo_owner.home.view

import android.content.Context
import androidx.constraintlayout.widget.ConstraintLayout
import android.util.AttributeSet
import android.view.LayoutInflater
import android.view.View
import com.zczy.cargo_owner.R
import com.zczy.cargo_owner.home.mode.request.RspQueryNoticeList
import com.zczy.comm.utils.ex.isTrue
import com.zczy.comm.utils.ex.setVisible
import com.zczy.comm.x5.X5WebActivity
import kotlinx.android.synthetic.main.home_main_marquee_inform_view.view.*

/**
 * 首页通知 跑马灯
 */
class HomeMarqueeInformView : ConstraintLayout {

    private var mData: RspQueryNoticeList? = null

    constructor(context: Context) : super(context) {
        init()
    }

    constructor(context: Context, attrs: AttributeSet?) : super(context, attrs) {
        init()
    }

    constructor(context: Context, attrs: AttributeSet?, defStyleAttr: Int) : super(context, attrs, defStyleAttr) {
        init()
    }

    private fun init() {
        setBackgroundResource(R.drawable.home_main_marquee_inform_vbg)
        LayoutInflater.from(context).inflate(R.layout.home_main_marquee_inform_view, this)
        initView()
        refreshData()
    }

    private fun initView() {
        btn_close.setOnClickListener {
            visibility = View.GONE
        }

        tv_msg.setOnClickListener {
            if (mData?.link?.isNotEmpty() == true) {
                val title = if (mData?.noticeType == "1") "公告" else "提示"
                X5WebActivity.startContentUI(context, title, mData?.link ?: "")
            }
        }

        tv_msg.isSelected = true
        tv_msg.marqueeRepeatLimit = -1
    }

    private fun refreshData() {
        if (mData?.title?.isEmpty() != false) {
            setVisible(false)
            return
        }

        if(mData?.noticeType == "1") {
            img_inform.setImageResource(R.drawable.home_main_marquee_inform)
        } else if(mData?.noticeType == "2") {
            img_inform.setImageResource(R.drawable.home_main_marquee_inform_2)
        }

        tv_msg.text = mData?.title
        btn_close.setVisible(mData?.haveClose.isTrue)
        setVisible(true)
    }

    fun setData(data: RspQueryNoticeList) {
        mData = data
        refreshData()
    }
}
