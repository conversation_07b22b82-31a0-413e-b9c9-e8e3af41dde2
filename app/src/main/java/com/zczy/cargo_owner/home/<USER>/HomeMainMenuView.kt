package com.zczy.cargo_owner.home.view

import android.content.Context
import android.graphics.Color
import android.text.TextUtils
import android.util.AttributeSet
import android.view.View
import android.view.ViewGroup
import androidx.annotation.DrawableRes
import androidx.recyclerview.widget.GridLayoutManager
import androidx.recyclerview.widget.RecyclerView
import com.chad.library.adapter.base.BaseQuickAdapter
import com.chad.library.adapter.base.BaseViewHolder
import com.sfh.lib.mvvm.service.BaseViewModel
import com.sfh.lib.ui.dialog.DialogBuilder
import com.zczy.cargo_owner.R
import com.zczy.cargo_owner.deliver.addorder.DeliverAddOrderMainActivity
import com.zczy.cargo_owner.deliver.batch.DeliverBatchManageActivity
import com.zczy.cargo_owner.home.mode.request.RspQueryCount
import com.zczy.cargo_owner.libcomm.config.SubUserAuthUtils
import com.zczy.cargo_owner.libcomm.req.ReqCheckUserIsFreeze
import com.zczy.cargo_owner.libcomm.utils.getChildPermission
import com.zczy.cargo_owner.order.confirm.ReturnedOrderConfirmListActivity
import com.zczy.cargo_owner.order.express.OrderExpressMainActivity
import com.zczy.cargo_owner.order.overdue.OrderOverdueMainActivity
import com.zczy.cargo_owner.order.settlement.SettlementApplicationListActivity
import com.zczy.cargo_owner.order.violate.OrderViolateMainActivity
import com.zczy.cargo_owner.user.certification.CertificationUtils
import com.zczy.cargo_owner.user.exception.WaybillExceptionActivity
import com.zczy.comm.CommServer
import com.zczy.comm.utils.dp2px
import com.zczy.comm.utils.getResDrawable
import com.zczy.comm.widget.itemdecoration.CommItemGirdDecoration
import com.zczy.libstyle.ZczySkinManager
import com.zczy.libstyle.ZczySkinType
import q.rorbin.badgeview.Badge
import q.rorbin.badgeview.QBadgeView

/**
 * PS: 菜单栏
 * Created by sdx on 2018/11/2.
 */
class HomeMainMenuView(context: Context, attrs: AttributeSet?, defStyle: Int) : RecyclerView(context, attrs, defStyle) {

    constructor(context: Context) : this(context, null, 0)

    constructor(context: Context, attrs: AttributeSet?) : this(context, attrs, 0)

    private val menuAdapter = HomeMenuAdapter()

    /**
     * 菜单 点击
     */
    private val menuTouchListener: BaseQuickAdapter.OnItemClickListener =
        object : BaseQuickAdapter.OnItemClickListener {
            override fun onItemClick(adapter: BaseQuickAdapter<*, *>, view: View, position: Int) {
                val item = adapter.getItem(position)
                if (item is HomeMainMenuData) {
                    when (item.name) {
                        "发新货" -> {
                            val nowTime = System.currentTimeMillis()
                            if (nowTime - lastTime < 500) {
                                lastTime = nowTime
                                return
                            }
                            lastTime = nowTime
                            val login = CommServer.getUserServer().login
                            // 审核类型
                            if (login != null && "1" != login.examineType) {
                                val dialogBuilder = DialogBuilder()
                                dialogBuilder.setMessage("请先认证为会员")
                                dialogBuilder
                                    .setOKText("去认证")
                                    .setOkListener { dialogInterface: DialogBuilder.DialogInterface, i: Int ->
                                        dialogInterface.dismiss()
                                        CertificationUtils.hzCertification(context)
                                    }
                                viewMode?.showDialog(dialogBuilder)
                                return
                            }
                            // 子账号权限
                            if (SubUserAuthUtils.isChild()) {
                                val moCommonOrder = SubUserAuthUtils.get().moCommonOrder.getChildPermission()
                                if (moCommonOrder.isNotEmpty()) {
                                    viewMode?.showDialogToast(moCommonOrder)
                                    return
                                }
                                val moBatchOrder =
                                    SubUserAuthUtils.get().moBatchOrder.getChildPermission()
                                if (moBatchOrder.isNotEmpty()) {
                                    viewMode?.showDialogToast(moBatchOrder)
                                    return
                                }
                            }
                            // 查询用户是否被冻结
                            viewMode?.execute(
                                true,
                                ReqCheckUserIsFreeze(
                                    userId = CommServer.getUserServer().login?.userId ?: ""
                                )
                            ) { p0 ->
                                // 0否 1是
                                if (p0.success()) {
                                    if ((p0.data?.freezeFlag ?: "") != "1") {
                                        DeliverAddOrderMainActivity.start(context)
                                    } else {
                                        viewMode?.showDialogToast(p0.data?.resultMsg)
                                    }
                                } else {
                                    viewMode?.showDialogToast(p0.msg)
                                }
                            }

                        }

                        "回单确认" -> {
                            ReturnedOrderConfirmListActivity.start(context, 0, "")
                        }

                        "结算申请" -> {
                            // 子账号权限
                            if (SubUserAuthUtils.isChild()) {
                                val saSettleApply =
                                    SubUserAuthUtils.get().saSettleApply.getChildPermission()
                                if (saSettleApply.isNotEmpty()) {
                                    viewMode?.showDialogToast(saSettleApply)
                                    return
                                }
                            }
                            SettlementApplicationListActivity.start(context, 0, "")
                        }

                        "回单签收" -> {
                            OrderExpressMainActivity.start(context, 0, "")
                        }

                        "违约管理" -> {
                            OrderViolateMainActivity.start(context)
                        }

                        "批量货管理" -> {
                            DeliverBatchManageActivity.start(context)
                        }

                        "运单异常管理" -> {
                            WaybillExceptionActivity.startUI(context)
                        }

                        "逾期异常处理" -> {
                            OrderOverdueMainActivity.start(context)
                        }

                        else -> {}
                    }
                }
            }

        }

    init {
        this.apply {
            setBackgroundResource(R.drawable.home_main_menu_bg)
            val p = dp2px(20.0f)
            val tb = dp2px(15.0f)
            setPadding(p, tb, p, tb);
            layoutManager = GridLayoutManager(getContext(), 4)
            setHasFixedSize(true)
            addItemDecoration(CommItemGirdDecoration(dp2px(5f), dp2px(5f)))
            isNestedScrollingEnabled = false
            isFocusable = false
            adapter = menuAdapter
            menuAdapter.setNewData(getMenuList())
            menuAdapter.onItemClickListener = menuTouchListener

            //换肤监听
            ZczySkinManager.get().addLinster {
                it.setBackgroundResource(this@HomeMainMenuView, R.drawable.home_main_menu_bg)
                menuAdapter.notifyDataSetChanged()
            }
        }

    }

    private var viewMode: BaseViewModel? = null
    private var lastTime = 0L


    private fun getMenuList(): List<HomeMainMenuData> {

        val menus = ArrayList<HomeMainMenuData>()
        menus.add(
            HomeMainMenuData(
                "发新货", R.drawable.home_selector_menu_1
            )
        )
        menus.add(
            HomeMainMenuData(
                "批量货管理", R.drawable.home_selector_menu_6
            )
        )
        menus.add(
            HomeMainMenuData(
                "回单确认", R.drawable.home_selector_menu_2
            )
        )
        menus.add(
            HomeMainMenuData(
                "结算申请", R.drawable.home_selector_menu_3
            )
        )
        menus.add(
            HomeMainMenuData(
                "回单签收", R.drawable.home_selector_menu_5
            )
        )
        menus.add(
            HomeMainMenuData(
                "违约管理", R.drawable.home_selector_menu_7
            )
        )
        menus.add(
            HomeMainMenuData(
                "运单异常管理", R.drawable.home_selector_menu_8
            )
        )
        menus.add(
            HomeMainMenuData(
                "逾期异常处理", R.drawable.home_selector_menu_9
            )
        )
        return menus
    }

    fun setViewMode(viewMode: BaseViewModel?) {
        this.viewMode = viewMode
    }

    fun setAllCount(count: RspQueryCount?) {
        val list = menuAdapter.data
        for (item in list) {
            when (item.name) {
                "回单确认" -> {
                    item?.count = count?.backTotalSize?.toIntOrNull() ?: 0
                }

                "结算申请" -> {
                    item?.count = count?.settleCount?.toIntOrNull() ?: 0
                }

                "回单签收" -> {
                    item?.count = count?.expressCount?.toIntOrNull() ?: 0
                }

                "违约管理" -> {
                    item?.count = count?.myBreakCount?.toIntOrNull() ?: 0
                }

                "运单异常管理" -> {
                    item?.count = count?.exceptionOrderCount?.toIntOrNull() ?: 0
                }
            }
        }
        menuAdapter.notifyDataSetChanged()
    }

    fun setCount(name: String, count1: String?) {
        val list = menuAdapter.data
        for (item in list) {
            if (TextUtils.equals(name, item.name)) {
                item.count = count1?.toIntOrNull() ?: 0
                menuAdapter.notifyDataSetChanged()
                break
            }
        }

    }

    inner class HomeMenuAdapter : BaseQuickAdapter<HomeMainMenuData, BaseViewHolder>(R.layout.home_main_menu_recycle_item) {
        override fun onCreateViewHolder(parent: ViewGroup, viewType: Int): BaseViewHolder {
            return super.onCreateViewHolder(parent, viewType).apply {
                val view = getView<View?>(R.id.ic_menu)
                if (view != null) {
                    val badge = QBadgeView(context).bindTarget(view).apply {
                        badgeBackground = getResDrawable(R.drawable.home_shape_badge_bg)
                        setBadgeTextSize(10f, true)
                        isShowShadow = true
                    }
                    view.tag = badge
                }
            }
        }

        override fun convert(helper: BaseViewHolder, item: HomeMainMenuData) {

            val color = getColor()
            val icon = getIconByName(item.name)
            if (icon != 0) {
                helper.setImageResource(R.id.ic_menu, icon)
                    .setText(R.id.name_menu, item.name)
                    .setTextColor(R.id.name_menu, color)
            }

            val tag = helper.getView<View>(R.id.ic_menu).tag
            if (tag is Badge) {
                when (item.name) {
                    "逾期异常处理" -> {
                        if (item.count > 99) {
                            tag.badgeText = "99+"
                        } else if (item.count <= 0) {
                            tag.hide(false)
                        } else {
                            tag.badgeText = item.count.toString()
                        }
                    }

                    else -> {
                        if (item.count > 99999) {
                            tag.badgeText = "99999+"
                        } else if (item.count <= 0) {
                            tag.hide(false)
                        } else {
                            tag.badgeText = item.count.toString()
                        }
                    }
                }
            }
        }
    }

    private fun getIconByName(name: String): Int {
        val type = ZczySkinManager.get().zczySkinType
        when (name) {
            "发新货" -> {
                return type.getDrawable(context, R.drawable.home_selector_menu_1)
            }

            "回单确认" -> {
                return type.getDrawable(context, R.drawable.home_selector_menu_2)
            }

            "结算申请" -> {
                return type.getDrawable(context, R.drawable.home_selector_menu_3)
            }

            "回单签收" -> {
                return type.getDrawable(context, R.drawable.home_selector_menu_5)
            }

            "违约管理" -> {
                return type.getDrawable(context, R.drawable.home_selector_menu_7)
            }

            "批量货管理" -> {
                return type.getDrawable(context, R.drawable.home_selector_menu_6)
            }

            "运单异常管理" -> {
                return type.getDrawable(context, R.drawable.home_selector_menu_8)
            }

            "逾期异常处理" -> {
                return type.getDrawable(context, R.drawable.home_selector_menu_9)
            }

            "发票中心" -> {
                return type.getDrawable(context, R.drawable.home_selector_menu_10)
            }

            "运费通" -> {
                return type.getDrawable(context, R.drawable.home_selector_menu_11)
            }

            else -> {
                return 0
            }
        }
    }

    private fun getColor(): Int {
        val type = ZczySkinManager.get().zczySkinType
        return when (type) {
            ZczySkinType.SkinDW -> {
                Color.parseColor("#17584A")
            }

            ZczySkinType.SkinZQ -> {
                Color.WHITE
            }

            else -> {
                Color.parseColor("#333333")
            }
        }
    }
}

/**
 * 首页菜单面板实体类
 */
data class HomeMainMenuData(
    var name: String = "", @DrawableRes var iconResId: Int = 0, var count: Int = 0
)