package com.zczy.cargo_owner.home.view;

import android.graphics.Color;
import android.os.Bundle;
import androidx.annotation.LayoutRes;
import androidx.annotation.NonNull;
import androidx.annotation.Nullable;
import android.view.View;
import android.view.WindowManager;

import com.jakewharton.rxbinding2.view.RxView;
import com.sfh.lib.mvvm.service.BaseViewModel;
import com.sfh.lib.ui.AbstractLifecycleActivity;
import com.zczy.comm.ui.UtilStatus;

import java.util.concurrent.TimeUnit;

import io.reactivex.Observable;
import io.reactivex.disposables.Disposable;

/**
 * author : Ssp
 * e-mail : <EMAIL>
 * date   : 2020/3/317:03
 * desc   :
 * version: 1.0
 */
public abstract class MaintenanceBaseActivity<VM extends BaseViewModel> extends AbstractLifecycleActivity<VM> {


    protected final String TAG = MaintenanceBaseActivity.this.getClass().getSimpleName();

    protected long defaultWindowDuration = 500; //默认点击间隔

    @Override
    final protected void onCreate(@Nullable Bundle savedInstanceState) {
        super.onCreate(savedInstanceState);
        init();
        setContentView(getLayout());
        initStatus();
        bindView(savedInstanceState);
        initData();
    }

    private void init() {
        getWindow().setSoftInputMode(WindowManager.LayoutParams.SOFT_INPUT_STATE_HIDDEN | WindowManager.LayoutParams.SOFT_INPUT_ADJUST_PAN);
    }

    private Observable<Object> bindClickEventOb(View v) {
        return RxView.clicks(v).throttleFirst(defaultWindowDuration, TimeUnit.MILLISECONDS);
    }

    final protected void bindClickEvent(View v) {
        Disposable subscribe = bindClickEventOb(v).subscribe(aVoid -> onSingleClick(v));
        putDisposable(subscribe);
    }

    protected void initStatus() {
        UtilStatus.initStatus(this, Color.WHITE);
    }

    protected void onSingleClick(@NonNull View v) {
    }

    @LayoutRes
    protected abstract int getLayout();

    protected abstract void bindView(@Nullable Bundle bundle);

    protected abstract void initData();
}
