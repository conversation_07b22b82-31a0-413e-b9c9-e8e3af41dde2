package com.zczy.cargo_owner.home;

import android.graphics.Color;
import android.os.Bundle;
import androidx.annotation.LayoutRes;
import androidx.annotation.NonNull;
import androidx.annotation.Nullable;
import android.view.View;
import android.view.WindowManager;

import com.jakewharton.rxbinding2.view.RxView;
import com.sfh.lib.mvvm.service.BaseViewModel;
import com.sfh.lib.ui.AbstractLifecycleActivity;
import com.zczy.comm.ui.UtilStatus;

import java.util.concurrent.TimeUnit;

import io.reactivex.Observable;
import io.reactivex.disposables.Disposable;

/**
 * 统一 状态栏 、 关闭横屏 、 显示关闭键盘
 * 增加 点击事件防抖动
 * sdx 2019/2/12
 */
public abstract class BaseTransparentActivity<VM extends BaseViewModel> extends AbstractLifecycleActivity<VM> {

    protected final String TAG = BaseTransparentActivity.this.getClass().getSimpleName();

    protected long defaultWindowDuration = 500; //默认点击间隔

    @Override
    final protected void onCreate(@Nullable Bundle savedInstanceState) {
        super.onCreate(savedInstanceState);
        init();
        setContentView(getLayout());
        initStatus();
        bindView(savedInstanceState);
        initData();
    }

    private void init() {
        getWindow().setSoftInputMode(WindowManager.LayoutParams.SOFT_INPUT_STATE_HIDDEN | WindowManager.LayoutParams.SOFT_INPUT_ADJUST_PAN);
    }

    private Observable<Object> bindClickEventOb(View v) {
        return RxView.clicks(v).throttleFirst(defaultWindowDuration, TimeUnit.MILLISECONDS);
    }

    final protected void bindClickEvent(View v) {
        Disposable subscribe = bindClickEventOb(v).subscribe(aVoid -> onSingleClick(v));
        putDisposable(subscribe);
    }

    protected void initStatus() {
        UtilStatus.initStatus (this,Color.WHITE);
    }

    protected void onSingleClick(@NonNull View v) {
    }

    @LayoutRes
    protected abstract int getLayout();

    protected abstract void bindView(@Nullable Bundle bundle);

    protected abstract void initData();
}
