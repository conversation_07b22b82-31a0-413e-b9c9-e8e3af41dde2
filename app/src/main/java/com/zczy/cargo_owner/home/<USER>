package com.zczy.cargo_owner.home

import android.annotation.SuppressLint
import android.content.Context
import android.content.Intent
import android.graphics.Color
import android.os.Bundle
import android.text.SpannableString
import android.text.SpannableStringBuilder
import android.text.Spanned
import android.text.style.ForegroundColorSpan
import android.view.KeyEvent
import android.view.View
import com.jaeger.library.StatusBarUtil
import com.sfh.lib.mvvm.service.BaseViewModel
import com.zczy.cargo_owner.R
import io.reactivex.Observable
import io.reactivex.Observer
import io.reactivex.android.schedulers.AndroidSchedulers
import io.reactivex.disposables.Disposable
import kotlinx.android.synthetic.main.money_plan_dialog_activity.*
import java.util.concurrent.TimeUnit

/**
 *    author : Ssp
 *    date   : 2019/10/12 16:49
 *    desc   : 资金划拨页面
 */

class MoneyPlanDialogActivity : BaseTransparentActivity<BaseViewModel>() {

    private var disposable: Disposable? = null

    companion object {
        const val MONEY_PLAN_CONTENT: String = "MONEY_PLAN_CONTENT"
        private const val COUNT_TIME = 3

        @JvmStatic
        fun startContentUI(context: Context, title: String) {
            val intent = Intent(context, MoneyPlanDialogActivity::class.java)
            intent.addFlags(Intent.FLAG_ACTIVITY_CLEAR_TOP)
            intent.addFlags(Intent.FLAG_ACTIVITY_NEW_TASK)
            intent.putExtra(MONEY_PLAN_CONTENT, title)
            context.startActivity(intent)
        }

    }

    override fun getLayout(): Int {
        return R.layout.money_plan_dialog_activity
    }

    @SuppressLint("SetTextI18n")
    override fun initData() {
        val titleStr = intent?.getStringExtra(MONEY_PLAN_CONTENT) ?: ""

        tv_content.text = dealContent(titleStr)

        Observable.interval(0, 1, TimeUnit.SECONDS)
                .subscribeOn(AndroidSchedulers.mainThread())
                .observeOn(AndroidSchedulers.mainThread())
                .map { increaseTime: Long -> COUNT_TIME - increaseTime.toInt() }
                .take(COUNT_TIME + 1.toLong())
                .subscribe(object : Observer<Int> {
                    override fun onError(e: Throwable) {}
                    override fun onComplete() {
                        tv_close.isEnabled = true
                        tv_close.text = "关闭"
                    }


                    override fun onSubscribe(d: Disposable) {
                        disposable = d
                        tv_close.text = "关闭( " + COUNT_TIME.toString() + "S )"
                        tv_close.isEnabled = false

                    }

                    override fun onNext(integer: Int) {
                        tv_close.text = "关闭( " + integer.toString() + "S )"
                    }
                })
    }


    private fun dealContent(text: String?): SpannableStringBuilder {

        val ssb = SpannableStringBuilder()
        val str = "预计补充运费金额"
        val str1 = "请及时登录网页端操作资金转入"
        val str2 = "尊敬的货主："
        val moneyUnit = "元"
//        val tempStr = "截至今天上午10点整，您的智运宝可用余额为1000.00元，请及时登录网页端操作资金转入，以免司机无法卸货。明天上午10点前预计卸货金额6700.00元，预计补充运费金额4500.00元，后天上午10点前预计卸货金额8200.00元，预计补充运费金额6000.00元。"
        text?.apply {
            var textStr = ""
            //1.截取最后一个字符之前的内容
            val substringBeforeLast1 = substringBeforeLast(str)
            //2.截取最后一个字符之后的内容
            val substringAfter1 = substringAfterLast(str)
            //3.截取元之前的内容 第二个补充运费金额
            val money2 = substringAfter1.substringBeforeLast(moneyUnit)
            //4.截取第一个补充充运费之前的内容
            val substringBeforeLast2 = substringBeforeLast1.substringBeforeLast(str)
            //5.截取第一个补充运费之后的内容
            val substringAfterLast2 = substringBeforeLast1.substringAfterLast(str)
            //6.截取第一个补充运费的金额
            val substringAfterLast3 = substringAfterLast2.substringBeforeLast(moneyUnit)
            //7.截取补充运费金额
            val money1 = substringAfterLast3.substringBeforeLast(moneyUnit)
            //8.截取补充运费金额之后内容
            val substringAfterLast4 = substringAfterLast3.substringAfterLast(moneyUnit)
            //9.截取 请及时登录网页端操作资金转入 之前内容
            val substringBeforeLast = substringBeforeLast2.substringBeforeLast(str1)
            //10.截取 请及时登录网页端操作资金转入 之后内容
            val substringAfterLast = substringBeforeLast2.substringAfterLast(str1)
            //11.截取两个补充运费之间的内容
            val substringAfterLast1 = substringAfterLast2.substringAfterLast(moneyUnit)
            textStr = substringBeforeLast + str1 + substringAfterLast + str + money1 + moneyUnit + substringAfterLast1 + str + money2 + moneyUnit

            val ss1 = SpannableString(substringBeforeLast.substringAfterLast(str2))
            val ss2 = SpannableString(str1)
            ss2.setSpan(ForegroundColorSpan(Color.parseColor("#FF2C2C")), 0, ss2.length, Spanned.SPAN_INCLUSIVE_EXCLUSIVE)
            val ss3 = SpannableString(substringAfterLast)
            val ss4 = SpannableString(str)
            val ss5 = SpannableString(money1)
            ss5.setSpan(ForegroundColorSpan(Color.parseColor("#FF2C2C")), 0, ss5.length, Spanned.SPAN_INCLUSIVE_EXCLUSIVE)
            val ss6 = SpannableString(moneyUnit)
            val ss7 = SpannableString(substringAfterLast4)
            val ss8 = SpannableString(moneyUnit)
            val ss9 = SpannableString(substringAfterLast1)
            val ss10 = SpannableString(str)
            val ss11 = SpannableString(money2)
            ss11.setSpan(ForegroundColorSpan(Color.parseColor("#FF2C2C")), 0, ss11.length, Spanned.SPAN_INCLUSIVE_EXCLUSIVE)
            val ss12 = SpannableString("$moneyUnit。")
            ssb.append(ss1).append(ss2).append(ss3).append(ss4).append(ss5).append(ss6).append(ss7).append(ss8).append(ss9).append(ss10).append(ss11).append(ss12)
            println("截取后内容:   $textStr")
        }

        return ssb
    }

    override fun bindView(bundle: Bundle?) {
        StatusBarUtil.setTranslucent(this)
        bindClickEvent(tv_close)
    }

    override fun onSingleClick(v: View) {
        super.onSingleClick(v)
        when (v.id) {
            R.id.tv_close -> {
                //关闭当前页面
                finish()
            }

        }
    }

    override fun onKeyDown(keyCode: Int, event: KeyEvent?): Boolean {
        return when (keyCode) {
            KeyEvent.KEYCODE_BACK -> {
                if (!tv_close.isEnabled) {
                    true
                } else {
                    super.onKeyDown(keyCode, event)
                }
            }
            else -> {
                super.onKeyDown(keyCode, event)
            }
        }

    }

    override fun onDestroy() {
        super.onDestroy()
        disposable?.dispose()
    }

}
