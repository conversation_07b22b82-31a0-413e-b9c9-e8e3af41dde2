package com.zczy.cargo_owner.home.mode.request

import com.zczy.comm.http.entity.BaseNewRequest
import com.zczy.comm.http.entity.BaseRsp
import com.zczy.comm.http.entity.ResultData

/**
 * 类描述：货主配置项
 * 作者：ssp
 * 创建时间：2024/3/29
 */
class ReqQueryMemberConfig(
    var userId: String? = null,
    var configType: String? = null, // 配置项类型ID(查询多个时，用逗号拼接) 144：是否开启线下专区 186:理赔申请管理 178:结算申请审核 223:议价规则设置
) : BaseNewRequest<BaseRsp<RspMemberConfigData>>("mms-app/memberConfig/queryMemberConfig")

class RspMemberConfigData(
    var offLineSpecialAreaSwitch: String? = null, // 线下专区是否展示
    var onlineCargoClaimSwitch: String? = null, // 理赔申请管理是否展示
    var tenderBiddingSwitch: String? = null, // 议价规则设置是否展示
    var settlementApplyAuditSwitch: String? = null, // 结算申请审核是否展示
) : ResultData()