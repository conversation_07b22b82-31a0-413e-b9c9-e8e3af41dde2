package com.zczy.cargo_owner.home.mode.request;

import com.zczy.cargo_owner.home.mode.VenueConfig;
import com.zczy.comm.http.entity.BaseNewRequest;
import com.zczy.comm.http.entity.BaseRsp;

/***
 * 查询场内物流配置
 */
public class ReqVenueQuerySingleDictConfig extends BaseNewRequest<BaseRsp<VenueConfig>> {

    String dictCode="ON_SITE_LOGISTICS_USER_ID";

    public ReqVenueQuerySingleDictConfig() {
        super("mms-app/dictConfig/querySingleDictConfig");
    }

}
