//package com.zczy.cargo_owner.home.dialog;
//
//import android.Manifest;
//import android.content.Context;
//import android.os.Bundle;
//import android.view.Gravity;
//
//import androidx.annotation.NonNull;
//import androidx.fragment.app.FragmentActivity;
//import androidx.appcompat.app.AlertDialog;
//
//import com.hollycrm.pjsip.utils.HollyPhoneManager;
//import com.sfh.lib.AppCacheManager;
//import com.sfh.lib.ui.AbstractLifecycleActivity;
//import com.sfh.lib.ui.dialog.DialogBuilder;
//import com.sfh.lib.utils.UtilTool;
//import com.zczy.cargo_owner.R;
//import com.zczy.cargo_owner.home.onlinecall.OnLineCallActivity;
//import com.zczy.cargo_owner.tickling.UserTicklingActivity;
//import com.zczy.comm.CommServer;
//import com.zczy.comm.Const;
//import com.zczy.comm.permission.PermissionCallBack;
//import com.zczy.comm.permission.PermissionUtil;
//import com.zczy.comm.utils.PhoneUtil;
//import com.zczy.lib_zstatistics.sdk.ZStatistics;
//
//import java.util.HashMap;
//import java.util.Map;
//
///**
// * 功能描述:在线客服+客服电话+问题反馈
// *
// * <AUTHOR> 孙飞虎
// * @company 中储智运科技股份有限公司
// * @copyright （版权）中储南京智慧物流科技有限公司所有
// * @date 2019/3/14
// */
//public class ServerPhoneDialog extends AlertDialog {
//
//    public static void showDialogUI(FragmentActivity activity) {
//        ServerPhoneDialog dialog = new ServerPhoneDialog(activity);
//        dialog.show();
//    }
//    FragmentActivity activity;
//    protected ServerPhoneDialog(@NonNull FragmentActivity activity) {
//        super(activity, R.style.dialogToast);
//        this.activity = activity;
//    }
//
//    @Override
//    protected void onCreate(Bundle savedInstanceState) {
//        super.onCreate(savedInstanceState);
//        setContentView(R.layout.home_server_phone_dialog);
//        this.findViewById(R.id.cl_phone).setOnClickListener(v -> {
//            //客服
//            ServerPhoneDialog.this.dismiss();
////            PhoneUtil.callPhone(getContext(), Const.PHONE_SERVER_400);
//
//            // 网络电话
//            PermissionUtil.checkPermissions(getContext(), "中储智运需申请您的录制音频权限与文件存储权限，以便您能正常使用语音通话功能。拒绝或取消授权不影响使用其他服务", new String[]{Manifest.permission.RECORD_AUDIO,
//                    Manifest.permission.WRITE_EXTERNAL_STORAGE}, new PermissionCallBack() {
//                @Override
//                public void onHasPermission() {
//                    ServerPhoneDialog.this.dismiss();
//                    ZStatistics.onViewClick(getContext(), "ServerPhoneDialog&cl_photo_2");
//
//                    if (UtilTool.isConnect(getContext())) {
//
//                            Map<String, String> map1 = new HashMap<>();
//                            map1.put("account", "zczy");
//                            map1.put("serviceNumber", "**********");
//                            map1.put("phoneNum", CommServer.getUserServer().getLogin().getMobile());
//                            HollyPhoneManager.getInstance(AppCacheManager.getApplication()).callServiceNumber(map1, R.drawable.intenet_call, "com.zczy.onlinecall.MyBroadcastReceiver");
//                    } else {
//                        DialogBuilder dialogBuilder = new DialogBuilder();
//                        dialogBuilder.setTitle("网络异常");
//                        dialogBuilder.setGravity(Gravity.CENTER);
//                        dialogBuilder.setMessage("很抱歉，当前网络异常，为了帮您更快捷的解决问题，您可以联系在线客服或400人工客服！");
//                        dialogBuilder.setOKTextColor("在线客服", R.color.color_5086fc);
//                        dialogBuilder.setCancelTextColor("人工电话客服", R.color.color_5086fc);
//                        dialogBuilder.setOkListener((dialogInterface, i) -> {
//                            dialogInterface.dismiss();
//                            OnLineCallActivity.start(getContext(), "");
//                        });
//                        dialogBuilder.setCancelListener(new DialogBuilder.DialogInterface.OnClickListener() {
//                            @Override
//                            public void onClick(DialogBuilder.DialogInterface dialog, int which) {
//                                dialog.dismiss();
//                                PhoneUtil.callPhone(getContext(), Const.PHONE_SERVER_400);
//                            }
//                        });
//                        ((AbstractLifecycleActivity<?>) activity).showDialog(dialogBuilder);
//                    }
//                }
//            });
//        });
//
//        this.findViewById(R.id.cl_line).setOnClickListener(v -> {
//            // 在线客服
//            ServerPhoneDialog.this.dismiss();
//            OnLineCallActivity.start(getContext(),"");
//        });
//
//        this.findViewById(R.id.cl_bug).setOnClickListener(v -> {
//            //问题反馈
//            ServerPhoneDialog.this.dismiss();
//            UserTicklingActivity.start(getContext());
//        });
//
//        this.findViewById(R.id.tv_close).setOnClickListener(v -> dismiss());
//    }
//
//}
