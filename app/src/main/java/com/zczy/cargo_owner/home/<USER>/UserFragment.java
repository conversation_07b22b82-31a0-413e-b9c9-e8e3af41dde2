package com.zczy.cargo_owner.home.fragment;

import android.os.Bundle;
import android.text.TextUtils;
import android.util.Log;
import android.view.View;

import androidx.annotation.NonNull;
import androidx.annotation.Nullable;
import androidx.recyclerview.widget.LinearLayoutManager;
import androidx.recyclerview.widget.RecyclerView;

import com.chad.library.adapter.base.BaseQuickAdapter;
import com.google.gson.Gson;
import com.jaeger.library.StatusBarUtil;
import com.sfh.lib.event.RxBusEvent;
import com.sfh.lib.mvvm.annotation.LiveDataMatch;
import com.sfh.lib.mvvm.service.BaseViewModel;
import com.sfh.lib.rx.IResultSuccess;
import com.sfh.lib.ui.dialog.DialogBuilder;
import com.zczy.cargo_owner.R;
import com.zczy.cargo_owner.capacity.CapacityManageActivity;
import com.zczy.cargo_owner.cement.CementNoticeActivity;
import com.zczy.cargo_owner.claim.ClaimApplicationManagerActivity;
import com.zczy.cargo_owner.coal.CoalNoticeActivity;
import com.zczy.cargo_owner.defaultapply.DefaultApplyExemptionConfirmActivity;
import com.zczy.cargo_owner.deliver.addorder.container.DeliverContainerListActivity;
import com.zczy.cargo_owner.deliver.address.consignor.DeliverAddressMainActivity;
import com.zczy.cargo_owner.deliver.address.receipt.DeliverReceiptMainActivity;
import com.zczy.cargo_owner.deliver.cargo_name_management.CargoNameManagementActivity;
import com.zczy.cargo_owner.deliver.drafts.ui.DeliverDraftsMainActivity;
import com.zczy.cargo_owner.deliver.norms.GoodsNormsManageActivity;
import com.zczy.cargo_owner.freight.activity.FreightPassHomeActivity;
import com.zczy.cargo_owner.home.mode.EMenuSize;
import com.zczy.cargo_owner.home.mode.QuestionnaireCount;
import com.zczy.cargo_owner.home.mode.UserCenterModel;
import com.zczy.cargo_owner.home.mode.request.ETempToken;
import com.zczy.cargo_owner.home.mode.request.ReqCheckUserPermissions;
import com.zczy.cargo_owner.home.mode.request.ReqGetTempToken;
import com.zczy.comm.data.ReqQueryHaveOpen;
import com.zczy.cargo_owner.home.mode.request.RespMedalCountData;
import com.zczy.cargo_owner.home.mode.request.RspMemberConfigData;
import com.zczy.cargo_owner.home.mode.request.RspQueryAvatarBorder;
import com.zczy.cargo_owner.home.mode.request.RspQueryOrderOverDueCount;
import com.zczy.cargo_owner.home.mode.rsp.MenuPermissionList;
import com.zczy.cargo_owner.home.mode.rsp.RsqQueryConsignorQrcode;
import com.zczy.cargo_owner.home.view.HomeUserInfoView;
import com.zczy.cargo_owner.home.view.UserAutheToastView;
import com.zczy.cargo_owner.home.view.UserExclusiveErCodeDialog;
import com.zczy.cargo_owner.invoice.activity.InvoiceCenterActivity;
import com.zczy.cargo_owner.libcomm.DeliverProvider;
import com.zczy.cargo_owner.libcomm.IDeliverProvider;
import com.zczy.cargo_owner.libcomm.config.HttpConfig;
import com.zczy.cargo_owner.libcomm.config.SubUserAuthUtils;
import com.zczy.cargo_owner.libcomm.utils.StringUtils;
import com.zczy.cargo_owner.libcomm.widget.CheckSelfPermissionDialog;
import com.zczy.cargo_owner.message.entity.RxBusUserInfo;
import com.zczy.cargo_owner.offline.OfflineActivity;
import com.zczy.cargo_owner.order.billing.OrderBillingReviewHomeActivity;
import com.zczy.cargo_owner.order.change.OrderChangeMainActivity;
import com.zczy.cargo_owner.order.consignor.OrderConsignorConfirmMainActivity;
import com.zczy.cargo_owner.order.dispatch.DispatchListManagerActivity;
import com.zczy.cargo_owner.order.overdue.OrderOverdueMainActivity;
import com.zczy.cargo_owner.order.pledge.PledgeMainActivity;
import com.zczy.cargo_owner.order.reminder.TransportReminderHomeActivity;
import com.zczy.cargo_owner.order.venue.VenueWaybillActivity;
import com.zczy.cargo_owner.production.ProductionCapacityActivity;
import com.zczy.cargo_owner.report.ReportHomeActivity;
import com.zczy.cargo_owner.shipment.ShipmentPlanActivity;
import com.zczy.cargo_owner.tickling.UserTicklingActivity;
import com.zczy.cargo_owner.transport.TransportPlanActivity;
import com.zczy.cargo_owner.user.InsuranceWebActivity;
import com.zczy.cargo_owner.user.ItemMenuAdapter;
import com.zczy.cargo_owner.user.UserScanLoginActivity;
import com.zczy.cargo_owner.user.X5ServiceCenterWebActivity;
import com.zczy.cargo_owner.user.assure.AssureListActivity;
import com.zczy.cargo_owner.user.certification.CertificationUtils;
import com.zczy.cargo_owner.user.contact.ContactListActivity;
import com.zczy.cargo_owner.user.coupon.CouponListActivity;
import com.zczy.cargo_owner.user.driver.DriverBlackListActivity;
import com.zczy.cargo_owner.user.evaluate.EvaluateManagerActivity;
import com.zczy.cargo_owner.user.exception.WaybillExceptionActivity;
import com.zczy.cargo_owner.user.h5face.X5WebH5FaceActivity;
import com.zczy.cargo_owner.user.info.UserInfoActivity;
import com.zczy.cargo_owner.user.info.model.EVipCustomer;
import com.zczy.cargo_owner.user.integral.UsersIntegralActivity;
import com.zczy.cargo_owner.user.invitation.InvitationRegistrationActivity;
import com.zczy.cargo_owner.user.overdue.CysExpiredCertificateManagementActivity;
import com.zczy.cargo_owner.user.questionnaire.QuestionnaireSurveyActivity;
import com.zczy.cargo_owner.user.satisfaction.SatisfactionEvaluationActivity;
import com.zczy.cargo_owner.user.setting.UserSettingActivity;
import com.zczy.cargo_owner.user.usermanage.SubUserManageActivity;
import com.zczy.comm.CommServer;
import com.zczy.comm.data.entity.ELogin;
import com.zczy.comm.data.entity.EUser;
import com.zczy.comm.data.request.ReqUserBriefInfo;
import com.zczy.comm.data.role.IRelation;
import com.zczy.comm.data.role.ViewStatus;
import com.zczy.comm.http.entity.BaseRsp;
import com.zczy.comm.permission.PermissionCallBack;
import com.zczy.comm.pluginserver.AMainServer;
import com.zczy.comm.ui.BaseFragment;
import com.zczy.comm.utils.ex.ViewUtil;
import com.zczy.comm.x5.X5WebActivity;
import com.zczy.lib_zstatistics.sdk.ZStatistics;
import com.zczy.lib_zstatistics.sdk.base.EventKeyType;
import com.zczy.lib_zstatistics.sdk.base.EventType;
import com.zczy.plugin.wisdom.earnest.FreeEarnestMoneyMainActivity;
import com.zczy.rn.ReactNativeMainActivity;

import org.json.JSONException;
import org.json.JSONObject;

import java.io.IOException;
import java.util.ArrayList;
import java.util.List;
import java.util.concurrent.ExecutorService;
import java.util.concurrent.Executors;
import java.util.concurrent.TimeUnit;

import okhttp3.Call;
import okhttp3.OkHttpClient;
import okhttp3.Request;
import okhttp3.Response;
import okhttp3.internal.sse.RealEventSource;
import okhttp3.sse.EventSource;
import okhttp3.sse.EventSourceListener;

public class UserFragment extends BaseFragment<UserCenterModel> implements BaseQuickAdapter.OnItemClickListener {


    private HomeUserInfoView userInfo;

    private UserAutheToastView userAuthentToastView;

    private RecyclerView userMenuView;

    private ItemMenuAdapter adapter;

    private View iv_bxsc;
    OkHttpClient client;
    RsqQueryConsignorQrcode erCodeInfo;
    private final ExecutorService executor = Executors.newSingleThreadExecutor();
    private Call sseCall;
    private String sseUrl = "http://172.20.21.124:8188/stream-sse?userId=";

    @Override
    public int getLayout() {
        return R.layout.home_user_fragment;
    }

    @Override
    protected void bindView(@NonNull View view, @Nullable Bundle bundle) {

        userInfo = view.findViewById(R.id.userInfo);
        userInfo.getUserAuthenticationView().setOnClickListener(v -> {
            // 认证
            CertificationUtils.hzCertification(getContext());
        });
        userInfo.getTvSetting().setOnClickListener(v -> {
            // 设置
            UserSettingActivity.start(getContext());
        });
        //隐藏积分签到
        ViewUtil.setVisible(userInfo.getTvIntegral(), false);
        userInfo.getTvIntegral().setOnClickListener(v -> {
            // 签到
            UsersIntegralActivity.start(requireContext());
        });
        userInfo.getTvUserSao().setOnClickListener(v -> {
            // 扫描登录web
            CheckSelfPermissionDialog.cameraPermissionDialog(getContext(), new PermissionCallBack() {
                @Override
                public void onHasPermission() {
                    UserScanLoginActivity.start(getContext());
                }
            });
        });
        userInfo.getIvYQTG().setOnClickListener(v -> {
            // 邀请推广
            InvitationRegistrationActivity.start(getContext());
        });

        userInfo.getIvUserHead().setOnClickListener(v -> {
            // 个人详情
            UserInfoActivity.start(getContext());
        });
//        userInfo.getVip().setOnClickListener(new View.OnClickListener() {
//            @Override
//            public void onClick(View v) {
//                //VIP
//                new VipDialog(getActivity(),userInfo.getVipCustomer()).show();
//            }
//        });
        userInfo.getTvUserErCode().setOnClickListener(v -> {
            UserExclusiveErCodeDialog dialog = new UserExclusiveErCodeDialog(getContext());
            dialog.showDialog(erCodeInfo);
        });
        iv_bxsc = view.findViewById(R.id.iv_bxsc);
        iv_bxsc.setOnClickListener(new View.OnClickListener() {
            @Override
            public void onClick(View v) {
                // 保险商城
                InsuranceWebActivity.statUI(getActivity());
//                UmsAgent.onEvent(getContext(), "home_Insurance", userId);
            }
        });
        userAuthentToastView = view.findViewById(R.id.userAutheToastView);
        view.findViewById(R.id.ivServiceCenter).setOnClickListener(view1 -> {
            // 服务中心
            X5ServiceCenterWebActivity.jumpPage(getContext(), HttpConfig.getWebUrl("/form_h5/h5_inner/index.html?_t=" + System.currentTimeMillis() + "#/helpIndex"));
        });
        userMenuView = view.findViewById(R.id.userMenuView);
        client = new OkHttpClient.Builder()
                .connectTimeout(10, TimeUnit.MINUTES)
                .readTimeout(10, TimeUnit.MINUTES)
                .build();
        userMenuView.setLayoutManager(new LinearLayoutManager(getContext()));
        adapter = new ItemMenuAdapter();
        adapter.setOnItemClickListener(this);
        userMenuView.setAdapter(adapter);
        this.getViewModel().queryConsignorQrcode();
        StatusBarUtil.setTransparentForImageViewInFragment(getActivity(), userInfo);
        // 初始化 SSEManager
        ELogin eLogin = CommServer.getUserServer().getLogin();
        if (eLogin != null) {
            String userId = eLogin.getUserId();
            uploadUserInfo(userId);
        }
    }

    /**
     * 建立SSE连接并监听消息
     */
    private void connectSSE(String userId) {

        Request request = new Request.Builder()
                .url(HttpConfig.getSseUrl() + "?userId=" + userId)
                .build();

        executor.submit(() -> {
            RealEventSource realEventSource = new RealEventSource(request, new EventSourceListener() {
                @Override
                public void onClosed(@NonNull EventSource eventSource) {
                    super.onClosed(eventSource);
                }

                @Override
                public void onEvent(@NonNull EventSource eventSource, @Nullable String id, @Nullable String type, @NonNull String data) {
                    super.onEvent(eventSource, id, type, data);
                    if (type.equals("message")) {
                        // 解析 JSON 数据
                        Gson gson = new Gson();
                        TaskEvent taskEvent = gson.fromJson(data, TaskEvent.class);
                        // 检查 taskType 是否为 1
                        if ("1".equals(taskEvent.getTaskType())) {
                            // 刷新 UI
                            refreshPage();
                        }
                    } else if (type.equals("error")) {
                        retryConnectSSE();
                    }
                }

                @Override
                public void onFailure(@NonNull EventSource eventSource, @Nullable Throwable t, @Nullable Response response) {
                    super.onFailure(eventSource, t, response);
                    retryConnectSSE();
                }

                @Override
                public void onOpen(@NonNull EventSource eventSource, @NonNull Response response) {
                    super.onOpen(eventSource, response);
                }
            });
            realEventSource.connect(client);
        });
    }

    /**
     * 刷新页面的方法
     */
    private void refreshPage() {
        getActivity().runOnUiThread(new Runnable() {
            @Override
            public void run() {
                // 这里可以刷新页面，或者更新UI
                onResume();
            }
        });
    }

    /**
     * 重试建立SSE连接
     */
    private void retryConnectSSE() {
        // 重连机制：延迟后重试
        Log.d("SSEFragment", "Retrying SSE connection...");
        ELogin eLogin = CommServer.getUserServer().getLogin();
        String userId = "";
        if (eLogin != null) {
            userId = eLogin.getUserId();
            uploadUserInfo(userId);
        }
    }

    @Override
    public void onDestroyView() {
        super.onDestroyView();
        // 确保 Fragment 销毁时停止 SSE 连接
    }

    /**
     * 上传用户信息
     */
    private void uploadUserInfo(String userId) {
        new Thread(new Runnable() {
            @Override
            public void run() {
                try {
                    /*TODO 线上地址http://172.30.30.218:8088/stream-sse*/
                    String url = HttpConfig.getSseUrl();
                    JSONObject jsonBody = new JSONObject();
                    jsonBody.put("userId", userId);

                    okhttp3.RequestBody body = okhttp3.RequestBody.create(
                            okhttp3.MediaType.parse("application/json"), jsonBody.toString());

                    okhttp3.Request request = new okhttp3.Request.Builder()
                            .url(url)
                            .post(body)
                            .build();
                    // 使用 try-with-resources 确保响应流被正确关闭
                    try (okhttp3.Response response = client.newCall(request).execute()) {
                        if (response.isSuccessful()) {
                            String responseBody = response.body().string();
                            connectSSE(userId);
                            Log.d("SSEFragment", "User info uploaded successfully: " + responseBody);
                        } else {
                            Log.e("SSEFragment", "Failed to upload user info");
                        }
                    } catch (IOException e) {
                        throw new RuntimeException(e);
                    }
                } catch (JSONException e) {
                    throw new RuntimeException(e);
                } catch (Exception e) {
                    // 捕获其他未知异常
                    Log.e("SSEFragment", "未知错误: " + e.getMessage(), e);
                }
            }
        }).start();
    }


    @Override
    protected void initData() {

    }

    long zStatisticsTime = 0L;

    @Override
    public void onResume() {
        super.onResume();
        ELogin eLogin = CommServer.getUserServer().getLogin();
        String userId = "";
        if (eLogin != null) {
            userId = eLogin.getUserId();
        }
        UserCenterModel viewModel = this.getViewModel(UserCenterModel.class);
        viewModel.queryUser();
        viewModel.querySize();
        viewModel.queryAvatarBorder();
        viewModel.queryQuestionnaireCount();
        viewModel.querySingleDictConfig();
        viewModel.querySingleDictConfigV1();
        viewModel.querySingleDictConfigV2();
        viewModel.venueQuerySingleDictConfig();
        viewModel.querryMedalCount();
        viewModel.queryVipCustomerByCustomerId();
        viewModel.queryOrderOverDueCount();
        viewModel.queryMemberConfig(userId, "144,186,178,223");
        if (eLogin != null && TextUtils.isEmpty(eLogin.getChildId())) {
            ReqCheckUserPermissions req = new ReqCheckUserPermissions();
            getViewModel(BaseViewModel.class).execute(req, userPermissionBaseRsp -> {
                if (userPermissionBaseRsp.success()) {
                    List<MenuPermissionList> list = userPermissionBaseRsp.getData().getMenuPermissionList();
                    boolean show = false;
                    if (list != null && !list.isEmpty()) {
                        for (MenuPermissionList menuPermissionList : list) {
                            if ("用户管理".equals(menuPermissionList.getName())) {
                                show = true;
                                break;
                            }
                        }
                    }
                    adapter.setUserManageMenu(show);
                }
            });
        }
        ReqQueryHaveOpen req = new ReqQueryHaveOpen();
        getViewModel(BaseViewModel.class).execute(req, userPermissionBaseRsp -> {
            adapter.setFreightPassMenu(userPermissionBaseRsp.success());
        });
        zStatisticsTime = System.currentTimeMillis();
    }

    @Override
    public void onPause() {
        super.onPause();
        ZStatistics.onEvent(this.getClass().getName(), this.getClass().getSimpleName(), map -> {
            map.put(EventKeyType.EVENT.value(), EventType.ON_VIEW);
            map.put(EventKeyType.EVENTDURATION.value(), System.currentTimeMillis() - zStatisticsTime);
        });
    }

    @Override
    public void onItemClick(BaseQuickAdapter adapter, View view, int position) {
        ELogin eLogin = CommServer.getUserServer().getLogin();
        String userId = "";
        if (eLogin != null) {
            userId = eLogin.getUserId();
        }
        List data = adapter.getData();
        if (data.isEmpty()) {
            return;
        }
        Object o = data.get(position);
        ItemMenuAdapter.ItemData item = null;
        if (o instanceof ItemMenuAdapter.ItemData) {
            item = (ItemMenuAdapter.ItemData) o;
        }
        if (item == null) {
            return;
        }
        switch (item.name) {

            case "用户管理": {
                SubUserManageActivity.start(getContext());
                break;
            }
            case "发货单管理": {
                OrderConsignorConfirmMainActivity.start(getContext());
                break;
            }
            case "我的草稿箱": {
                DeliverDraftsMainActivity.start(getContext());
                break;
            }
            case "发货人管理": {
                DeliverAddressMainActivity.start(UserFragment.this, DeliverAddressMainActivity.TYPE_SENDER);
                break;
            }
            case "收货人管理": {
                DeliverAddressMainActivity.start(UserFragment.this, DeliverAddressMainActivity.TYPE_RECEIVER);
                break;
            }
            case "收件人管理": {
                DeliverReceiptMainActivity.start(UserFragment.this, null);
                break;
            }
            case "联系人管理": {
                ContactListActivity.start(getContext(), Long.parseLong(userId));
                break;
            }
            case "变更管理": {
                OrderChangeMainActivity.start(this);
                break;
            }
            case "运单异常管理": {
                WaybillExceptionActivity.startUI(getContext());
                break;
            }
            case "逾期运单处理": {
                OrderOverdueMainActivity.start(getContext());
                break;
            }
            case "货物名称管理": {
                CargoNameManagementActivity.start(getContext());
                break;
            }
            case "保障服务": {
                AssureListActivity.start(getActivity());
                break;
            }
            case "评价管理": {
                EvaluateManagerActivity.start(getActivity());
                break;
            }
            case "问题反馈": {
                UserTicklingActivity.start(UserFragment.this);
                break;
            }
            case "问卷调查": {
                QuestionnaireSurveyActivity.start(getContext());
                break;
            }
            // 客服满意度评价 3.0.2
            case "客服满意度评价": {
                SatisfactionEvaluationActivity.start(getContext());
                break;
            }
            case "积分管理": {
                UsersIntegralActivity.start(getContext());
                break;
            }
            case "优惠券管理": {
                CouponListActivity.jumpPage(getContext());
                break;
            }
            case "押金管理": {
                PledgeMainActivity.start(requireContext());
                break;
            }
            case "我的培训": {
                X5WebActivity.startContentUI(getContext(), HttpConfig.getWebUrl() + "/form_h5/h5_mobile/index.html?_t=" + System.currentTimeMillis() + "#/consignor");
                break;
            }
            case "场内物流": {
                ELogin login = CommServer.getUserServer().getLogin();
                if (login == null) {
                    return;
                }
                IRelation relation = login.getRelation();
                String cnwlOperateall = StringUtils.getChildPermission(SubUserAuthUtils.get().getCnwlOperateall());
                if (relation.isChildAccount()) {
                    if (!TextUtils.isEmpty(cnwlOperateall)) {
                        showDialogToast(cnwlOperateall);
                        return;
                    }
                }
                VenueWaybillActivity.start(getContext());
                break;
            }
            case "货物规格管理": {
                GoodsNormsManageActivity.start(getContext());
                break;
            }
            case "运输提醒": {
                TransportReminderHomeActivity.jumpPage(getContext());
                break;
            }
//            case "帮助中心": {
////                X5WebActivity.startContentUI(getContext(),"帮助中心", HttpConfig.getWebUrl() + "form_h5/h5_inner/index.html?_t=" + System.currentTimeMillis() + "#/operateVideo?isNative=1");
//                X5VideoWebNoToolBarActivity.startContentUI(getContext(), HttpConfig.getUrl("form_h5/h5_inner/index.html?_t=" + System.currentTimeMillis() + "#/operateVideo?isNative=1"));
//                break;
//            }
            case "生产能力": {
                // 生产能力
                ProductionCapacityActivity.jumpUi(requireContext());
                break;
            }
            case "发运计划": {
                // 发运计划
                ShipmentPlanActivity.jumpUi(requireContext());
                break;
            }
            case "煤矿公告管理": {
                // 煤矿公告管理
                ELogin login = CommServer.getUserServer().getLogin();
                if (login == null) {
                    return;
                }
                IRelation relation = login.getRelation();
                if (relation.isChildAccount()) {
                    showDialogToast("您的账号没有相关操作权限!");
                    return;
                }
                CoalNoticeActivity.jumpUi(requireContext());
                break;
            }
            case "水泥厂卸车情况通知管理": {
                // 水泥厂卸车情况通知管理
                CementNoticeActivity.jumpUi(requireContext());
                break;
            }
            case "运输计划": {
                // 运输计划
                TransportPlanActivity.jumpUi(requireContext());
                break;
            }
            case "线下专区": {
                OfflineActivity.jumpUi(requireContext());
                break;
            }
            case "集装箱管理": {
                DeliverContainerListActivity.jumpUi(requireContext());
                break;
            }
            case "免诚意金管理": {
                FreeEarnestMoneyMainActivity.start(getContext());
                break;
            }
            case "派车单管理": {
                DispatchListManagerActivity.jumpUi(requireContext());
                break;
            }
            case "黑名单管理": {
                DriverBlackListActivity.jumpPage(getContext());
                break;
            }
            case "结算申请审核": {
                OrderBillingReviewHomeActivity.jumpPage(getContext());
                break;
            }
            case "理赔申请管理": {
                ClaimApplicationManagerActivity.start(getContext());
                break;
            }
            case "统计报表": {
                ReportHomeActivity.jumpPage(getContext());
                break;
            }
            case "证件过期管理": {
                CysExpiredCertificateManagementActivity.jumpPage(getContext());
                break;
            }
            case "违约申请免确认管理": {
                DefaultApplyExemptionConfirmActivity.start(getContext());
                break;
            }
            case "议价设置": {
                IDeliverProvider deliver = DeliverProvider.deliver;
                if (deliver != null) {
                    deliver.openDeliverBidSetActivity(UserFragment.this);
                }
                break;
            }
            case "原有运力管理": {
                CapacityManageActivity.jumpPage(getContext());
                break;
            }
            case "预约回访": {
                X5WebActivity.startNoTitleContentUI(getActivity(), HttpConfig.getWebUrl("/form_h5/h5_inner/index.html?_t=" + System.currentTimeMillis() + "#/appointmentFollowup"));
                break;
            }
            case "监管账户资料补充/更新": {
                ReactNativeMainActivity.start(getActivity(), "SupervisionPage");
                break;
            }
            case "合同管理": {
                getViewModel(UserCenterModel.class).execute(true, new ReqGetTempToken(), new IResultSuccess<BaseRsp<ETempToken>>() {
                    @Override
                    public void onSuccess(BaseRsp<ETempToken> eTempTokenBaseRsp) throws Exception {
                        if (eTempTokenBaseRsp.success()) {
                            X5WebH5FaceActivity.startContentUI(getActivity(), String.format("%s/mobile/#/home?token=%s&originFrom=app_android", HttpConfig.getSignContractUrl(), eTempTokenBaseRsp.getData().getToken()));
                        } else {
                            showDialogToast(eTempTokenBaseRsp.getMsg());
                        }
                    }
                });
                break;
            }
            case "运费通": {
                FreightPassHomeActivity.Companion.jumpPage(getContext());
                break;
            }
            case "发票中心": {
                getViewModel(UserCenterModel.class).execute(true, new ReqUserBriefInfo(), resp -> {
                    if (resp.success()) {
                        IRelation relation = resp.getData().getRelation();
                        if (relation.isPrimaryShipper()) {
                            //初级货主
                            DialogBuilder dialogBuilder = new DialogBuilder();
                            dialogBuilder.setTitle("提示");
                            dialogBuilder.setHideCancel(true);
                            dialogBuilder.setMessage("非已认证会员角色不能操作");
                            dialogBuilder.setOKText("我知道了");
                            showDialog(dialogBuilder);
                        } else {
                            InvoiceCenterActivity.jumpPage(getActivity());
                        }
                    } else {
                        showDialogToast(resp.getMsg());
                    }
                });
                break;
            }
            default:
                break;
        }
    }


    @LiveDataMatch
    public void queryMemberConfigSuccess(RspMemberConfigData data) {
        adapter.showQueryMemberConfigSuccess(data);
    }

    @LiveDataMatch(tag = "勋章数量")
    public void querryMedalCountSuccess(RespMedalCountData data) {
        if (null != data) {
            this.userInfo.setMedalCount(String.valueOf(data.medalNum));
        }
    }

    @LiveDataMatch
    public void onQueryOrderOverDueCount(RspQueryOrderOverDueCount data) {
        adapter.showEMenuSize(data);
    }


    @LiveDataMatch(tag = "菜单数量显示")
    public void onQuerySizeSuccess(EMenuSize size) {
        adapter.showEMenuSize(size);
    }

    @LiveDataMatch(tag = "问卷调查数量")
    public void onQueryQuestionnaireCountSuccess(QuestionnaireCount count) {
        adapter.showQuestionnaireCount(count);
    }

    @LiveDataMatch(tag = "用户信息")
    public void onUserInfoSuccess(EUser user) {

        this.userInfo.onUserInfoSuccess(user);
    }

    @LiveDataMatch
    public void onQueryAvatarBorder(@Nullable RspQueryAvatarBorder data) {
        this.userInfo.setAvatarBorder(data);
    }

    @LiveDataMatch(tag = "认证状态")
    public void onAuthentShow(ViewStatus status) {
        //认证状态
        this.userInfo.onAuthentShow(status);
        this.userAuthentToastView.onAuthentShow(status);
    }

    @RxBusEvent(from = "推送 用户信息")
    public void onRxUserInfoSuccess(RxBusUserInfo user) {
        this.getViewModel().queryUser();
    }

    @LiveDataMatch(tag = "保险商城")
    public void showInsuranceMall(boolean show) {
        iv_bxsc.setVisibility(show ? View.VISIBLE : View.GONE);
    }

    @LiveDataMatch(tag = "保险商城")
    public void showJiDongMenu(boolean show) {
        List<ItemMenuAdapter.ItemData> list = adapter.getData();
        List<ItemMenuAdapter.ItemData> list1 = new ArrayList<>();
        for (ItemMenuAdapter.ItemData itemData : list) {
            if (TextUtils.equals("发运计划", itemData.name) ||
                    TextUtils.equals("生产能力", itemData.name) ||
                    TextUtils.equals("煤矿公告管理", itemData.name) ||
                    TextUtils.equals("水泥厂卸车情况通知管理", itemData.name) ||
                    TextUtils.equals("派车单管理", itemData.name)) {
                list1.add(itemData);
                break;
            }
        }

        if (show && list1.size() == 0) {

            list.add(list.size(), new ItemMenuAdapter.ItemData("发运计划", R.drawable.user_menu_20));
            list.add(list.size(), new ItemMenuAdapter.ItemData("生产能力", R.drawable.user_menu_21));
            list.add(list.size(), new ItemMenuAdapter.ItemData("煤矿公告管理", R.drawable.user_menu_22));
            list.add(list.size(), new ItemMenuAdapter.ItemData("水泥厂卸车情况通知管理", R.drawable.user_menu_23));
            list.add(list.size(), new ItemMenuAdapter.ItemData("派车单管理", R.drawable.user_menu_27));
            adapter.notifyDataSetChanged();
            return;
        }
        if (!show && list1.size() > 0) {
            list.removeAll(list1);
            adapter.notifyDataSetChanged();
        }
    }

    @LiveDataMatch(tag = "场内物流")
    public void onVenueShow(boolean show) {
        List<ItemMenuAdapter.ItemData> list = adapter.getData();
        ItemMenuAdapter.ItemData data = null;
        for (ItemMenuAdapter.ItemData itemData : list) {
            if (TextUtils.equals("场内物流", itemData.name)) {
                data = itemData;
                break;
            }
        }

        if (show && data == null) {
            list.add(12, new ItemMenuAdapter.ItemData("场内物流", R.drawable.user_menu_12));
            adapter.notifyDataSetChanged();
            return;
        }
        if (!show && data != null) {
            list.remove(data);
            adapter.notifyDataSetChanged();
        }
    }


    @LiveDataMatch(tag = "专属二维码")
    public void queryConsignorQrcodeSuccess(RsqQueryConsignorQrcode result) {
        this.erCodeInfo = result;
    }

    @LiveDataMatch(tag = "场内物流")
    public void showTransPortMenu(boolean show) {
        List<ItemMenuAdapter.ItemData> list = adapter.getData();
        List<ItemMenuAdapter.ItemData> list1 = new ArrayList<>();
        for (ItemMenuAdapter.ItemData itemData : list) {
            if (TextUtils.equals("运输计划", itemData.name)) {
                list1.add(itemData);
                break;
            }
        }

        if (show && list1.size() == 0) {

            list.add(list.size(), new ItemMenuAdapter.ItemData("运输计划", R.drawable.user_menu_20));
            adapter.notifyDataSetChanged();
            return;
        }
        if (!show && list1.size() > 0) {
            list.removeAll(list1);
            adapter.notifyDataSetChanged();
        }
    }

    @LiveDataMatch
    public void onVip(EVipCustomer customer) {
        //VIP
        userInfo.showVip(customer);

    }
}
