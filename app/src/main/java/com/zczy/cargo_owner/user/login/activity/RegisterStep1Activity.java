package com.zczy.cargo_owner.user.login.activity;

import android.content.Context;
import android.content.Intent;
import android.os.Bundle;
import androidx.annotation.Nullable;
import android.text.Editable;
import android.text.TextUtils;
import android.text.TextWatcher;
import android.util.SparseArray;
import android.view.KeyEvent;
import android.view.View;
import android.widget.Button;
import android.widget.EditText;
import android.widget.ImageView;
import android.widget.LinearLayout;
import android.widget.RelativeLayout;
import android.widget.TextView;
import android.view.LayoutInflater;
import android.view.ViewGroup;
import android.view.Gravity;
import android.graphics.Color;
import android.graphics.drawable.ColorDrawable;
import androidx.recyclerview.widget.RecyclerView;
import androidx.recyclerview.widget.LinearLayoutManager;
import android.app.Dialog;
import android.view.Window;
import android.view.WindowManager;

import com.jakewharton.rxbinding2.view.RxView;
import com.sfh.lib.AppCacheManager;
import com.sfh.lib.mvvm.annotation.LiveDataMatch;
import com.sfh.lib.ui.dialog.DialogBuilder;
import com.sfh.lib.utils.UtilTool;
import com.zczy.cargo_owner.R;
import com.zczy.cargo_owner.home.onlinecall.OnLineCallActivity;
import com.zczy.cargo_owner.libcomm.config.HttpConfig;
import com.zczy.cargo_owner.user.login.mode.RegisterMode;
import com.zczy.cargo_owner.user.login.request.ReqRegisterCheckCode;
import com.zczy.cargo_owner.user.setting.model.ReqGetVideoPath;
import com.zczy.cargo_owner.user.setting.model.RespGetVideoPath;
import com.zczy.cargo_owner.wight.AgreementView;
import com.zczy.cargo_owner.wight.UserArgeementViewDialog;
import com.zczy.comm.SpannableHepler;
import com.zczy.comm.data.entity.ECity;
import com.zczy.comm.data.request.ReqCheckCode;
import com.zczy.comm.data.request.ReqSendCode;
import com.zczy.comm.ui.BaseActivity;
import com.zczy.comm.utils.VerificationCodeUtil;
import com.zczy.comm.videoplayer.VideoPlayActivity;
import com.zczy.comm.widget.AppToolber;
import com.zczy.comm.widget.ImageCodeDialog;
import com.zczy.comm.widget.RxTimeCountView;

import java.util.ArrayList;
import java.util.List;
import java.util.concurrent.TimeUnit;

import io.reactivex.disposables.Disposable;

/**
 * 注册第一步
 */
public class RegisterStep1Activity extends BaseActivity<RegisterMode> implements TextWatcher {

    public static void start(Context context, String userType, String phone) {
        Intent intent = new Intent(context, RegisterStep1Activity.class);
        intent.putExtra("userType", userType);
        intent.putExtra("phone", phone);
        context.startActivity(intent);
    }

    public static void start(Context context, String phone) {
        Intent intent = new Intent(context, RegisterStep1Activity.class);
        intent.putExtra("userType", "6");
        intent.putExtra("phone", phone);
        context.startActivity(intent);
    }

    private EditText etPhone;
    private Button btNext;
    //验证码
    private EditText etCode;
    private ImageView imgCustomer;
    private RxTimeCountView tvTime;
    private LinearLayout lyVoice;
    private RxTimeCountView tvVoiceTime;
    private VerificationCodeUtil util;
    private ImageView img_video_guide;
    private RelativeLayout rl_tips;
    private View view_line_guide;
    private EditText etCompanyName;
    private LinearLayout ly1;
    private TextView tv_province;
    private String provinceCode;

    /***
     * 角色类型
     */
    private String userType;
    /***
     * 验证码类型
     */
    private String codeType = ReqSendCode.TYPE_SMS;

    private AgreementView agreementView;
    private List<ECity> originalData = new ArrayList<>();


    @Override
    protected int getLayout() {
        return R.layout.user_register_step1_activity;
    }

    @Override
    protected void bindView(@Nullable Bundle bundle) {
        agreementView = findViewById(R.id.agreementView);

        etPhone = findViewById(R.id.etPhone);
        btNext = findViewById(R.id.btNext);
        etCode = findViewById(R.id.etCode);
        tvTime = findViewById(R.id.tvTime);
        lyVoice = findViewById(R.id.lyVoice);
        imgCustomer = findViewById(R.id.img_customer);
        tvVoiceTime = findViewById(R.id.tvVoiceTime);
        img_video_guide = findViewById(R.id.img_video_guide);
        view_line_guide = findViewById(R.id.view_line_guide);
        rl_tips = findViewById(R.id.rl_tips);
        etCompanyName = findViewById(R.id.etCompanyName);
        ly1 = findViewById(R.id.ly1);
        tv_province = findViewById(R.id.tv_province);

        etPhone.addTextChangedListener(this);
        etCode.addTextChangedListener(this);
        this.setListener();
        userType = getIntent().getStringExtra("userType");
        String phone = getIntent().getStringExtra("phone");
        if (!TextUtils.isEmpty(phone) && UtilTool.isMobile(phone)) {
            etPhone.setText(phone);
        }
        imgCustomer.setOnClickListener(v ->
                OnLineCallActivity.start(RegisterStep1Activity.this,"")
        );
        AppToolber appToolber = findViewById(R.id.appToolber);
        appToolber.setLeftOnClickListener(v -> showExit());

        findViewById(R.id.img_customer).setOnClickListener(v -> OnLineCallActivity.start(RegisterStep1Activity.this,""));

        SparseArray<AgreementView.AgreementTxt> agreementTxtSparseArray = new SparseArray<>();
        agreementTxtSparseArray.put("中储智运平台服务协议".hashCode(), new AgreementView.AgreementTxt("中储智运平台服务协议", HttpConfig.getWebUrl("/form_h5/documents/app_documents/app_consignor/service.html?sceneCode=133&plateType=1&time="+System.currentTimeMillis())));
        agreementTxtSparseArray.put("中储智运网络货运平台交易规则".hashCode(), new AgreementView.AgreementTxt("中储智运网络货运平台交易规则", HttpConfig.getWebUrl("/form_h5/documents/app_documents/app_consignor/tradeRule.html?sceneCode=133&plateType=2&time="+System.currentTimeMillis())));
        agreementTxtSparseArray.put("隐私政策".hashCode(), new AgreementView.AgreementTxt("隐私政策", HttpConfig.getWebUrl("/form_h5/documents/app_documents/app_consignor/privacy.html?sceneCode=133&plateType=4&time="+System.currentTimeMillis())));
        agreementTxtSparseArray.put("用户授权协议".hashCode(), new AgreementView.AgreementTxt("用户授权协议", HttpConfig.getWebUrl("/form_h5/documents/app_documents/app_consignor/authorization.html?sceneCode=133&plateType=3&time="+System.currentTimeMillis())));

        agreementView.newData(agreementTxtSparseArray);

        ly1.setOnClickListener(new View.OnClickListener() {
            @Override
            public void onClick(View v) {
                showProvinceSelectDialog();
            }
        });

    }

    @Override
    protected void initData() {
        ReqGetVideoPath reqGetVideoPath = new ReqGetVideoPath();
        reqGetVideoPath.showPosition = 0;
        reqGetVideoPath.targetScope = 1;
        getViewModel(RegisterMode.class).getVideoPath(reqGetVideoPath);

        getViewModel(RegisterMode.class).getCityInfo();
    }

    private void setListener() {
        util = new VerificationCodeUtil(ReqSendCode.MODULE_TYPE_A, new VerificationCodeUtil.IOnCallback() {
            @Override
            public String getPhone() {
                return etPhone.getText().toString();
            }

            @Override
            public void onClickCode(ReqSendCode req) {
                codeType = req.getType();
                // 注册的手机号是非平台会员 plateFormType=0：非平台会员
                req.setPlateFormType("0");
                getViewModel(RegisterMode.class).showImageVerifyCode(req);
            }

            @Override
            public void showToast(CharSequence toast) {
                DialogBuilder dialogBuilder = new DialogBuilder();
                dialogBuilder.setMessage("提示");
                dialogBuilder.setMessage(toast);
                dialogBuilder.setHideCancel(true);
                showDialog(dialogBuilder);
            }
        }).build(tvTime, tvVoiceTime, lyVoice);


        Disposable cilck = RxView.clicks(btNext).throttleFirst(1, TimeUnit.SECONDS).subscribe(o -> {
            //下一步
            String phone = etPhone.getText().toString();
            String code = etCode.getText().toString();
            String companyName = etCompanyName.getText().toString();
            String provinceName = tv_province.getText().toString();
            if (TextUtils.isEmpty(phone)) {
                showToast("请输入手机号码");
                return;
            }
            if (TextUtils.isEmpty(code)) {
                showToast("请输入验证码");
                return;
            }

            if(TextUtils.isEmpty(companyName)){
                showToast("请输入公司名称");
                return;
            }

            if(TextUtils.isEmpty(provinceName)){
                showToast("请选择省份");
                return;
            }

            if (!agreementView.getCheckBox().isChecked()) {
                UserArgeementViewDialog.showDialogUI(RegisterStep1Activity.this, agreementView.getData(), v -> agreementView.getCheckBox().setChecked(true));
                return;
            }

            ReqRegisterCheckCode reqCheckCode = new ReqRegisterCheckCode();
            reqCheckCode.setMobile(phone);
            reqCheckCode.setModuleType(ReqCheckCode.MODULE_TYPE_A);
            reqCheckCode.setVerifyCode(code);
            reqCheckCode.setVerifyCodeType(codeType);
            reqCheckCode.setUserType("6");
            getViewModel(RegisterMode.class).checkVerifyCode(reqCheckCode);

        });
        this.putDisposable(cilck);
    }

    @Override
    public boolean onKeyDown(int keyCode, KeyEvent event) {
        if (keyCode == KeyEvent.KEYCODE_BACK) {
            showExit();
            return true;
        }
        return super.onKeyDown(keyCode, event);
    }

    private void showExit() {
        DialogBuilder builder = new DialogBuilder();
        builder.setMessage("返回后注册将中断,是否确认返回？");
        builder.setOkListener((dialog, which) -> {
            dialog.dismiss();
            RegisterStep1Activity.this.finish();
        });
        showDialog(builder);
    }

    @LiveDataMatch
    public void ongetVideoPathSuccess(RespGetVideoPath respGetVideoPath) {
        if (null != respGetVideoPath && null != respGetVideoPath.data && !TextUtils.isEmpty(respGetVideoPath.data.content)) {
            rl_tips.setVisibility(View.VISIBLE);
            img_video_guide.setVisibility(View.VISIBLE);
            view_line_guide.setVisibility(View.VISIBLE);

            String changepwd_show_popu = AppCacheManager.getCache("register_show_popu", String.class, "1");
            if (changepwd_show_popu.equals("1")) {
                rl_tips.setVisibility(View.VISIBLE);
            } else {
                rl_tips.setVisibility(View.GONE);
            }

            img_video_guide.setOnClickListener(new View.OnClickListener() {
                @Override
                public void onClick(View v) {
                    // 播放视频
                    Intent intent = new Intent(RegisterStep1Activity.this, VideoPlayActivity.class);
                    intent.putExtra("videoUri", respGetVideoPath.data.content);
                    startActivity(intent);
                }
            });

            rl_tips.setOnClickListener(new View.OnClickListener() {
                @Override
                public void onClick(View v) {
                    rl_tips.setVisibility(View.GONE);
                    AppCacheManager.putCache("register_show_popu", "0");
                }
            });
        } else {
            rl_tips.setVisibility(View.GONE);
            img_video_guide.setVisibility(View.GONE);
            view_line_guide.setVisibility(View.GONE);
        }
    }


    @LiveDataMatch(tag = "发送验证码结果")
    public void onSendCodeRelust(boolean success, String type) {

        this.util.onSendCodeResult(success, type);

    }

    @LiveDataMatch(tag = "验证码验证结果")
    public void onCheckVerifyCodeSuccess() {
        String phone = etPhone.getText().toString();
        String code = etCode.getText().toString();
        String companyName = etCompanyName.getText().toString();
        String provinceName = tv_province.getText().toString();
        RegisterStep2Activity.start(RegisterStep1Activity.this, phone, userType, "", codeType, code, companyName,provinceName,provinceCode);
        finish();
    }

    @LiveDataMatch(tag = "显示图片验证码")
    public void onShowImageVerifyCode(final ReqSendCode req) {
        final String phone = etPhone.getText().toString();
        new ImageCodeDialog(this, phone, (dialog, code) -> {

            req.setImageCode(code);
            getViewModel(RegisterMode.class).sendVerifyCode(req);
        }).show();
    }

    @LiveDataMatch(tag = "获取省市区")
    public void onCityInfoSuccess(List<ECity> cities) {
        this.originalData = cities;
    }

    @Override
    public void beforeTextChanged(CharSequence s, int start, int count, int after) {

    }

    @Override
    public void onTextChanged(CharSequence s, int start, int before, int count) {

    }

    @Override
    public void afterTextChanged(Editable s) {
        if (s == this.etPhone.getText()) {
            String phone = s.toString();
            if (!TextUtils.isEmpty(phone) && phone.length() >= 11) {
                String code = etCode.getText().toString();
                this.etCode.requestFocus();
                this.etCode.setSelection(TextUtils.isEmpty(code) ? 0 : code.length() - 1);
                this.util.setAbled(true);
            } else {
                this.util.setAbled(false);
            }
        }

        String phone = this.etPhone.getText().toString();
        String code = this.etCode.getText().toString();
        if (TextUtils.isEmpty(phone) || TextUtils.isEmpty(code)) {
            this.btNext.setEnabled(false);
        } else {
            this.btNext.setEnabled(true);
        }
    }

    @LiveDataMatch(tag = "提示")
    public void onPhoneExist(String phone) {
        //ZCZY-10873 供应链会员中台-用户部分
        UserRegisterPhoneExitActivity.start(this,phone);
        finish();
    }

    /**
     * 显示省份选择底部弹窗
     */
    private void showProvinceSelectDialog() {
        if (originalData == null || originalData.isEmpty()) {
            showToast("暂无省份数据");
            return;
        }

        final Dialog dialog = new Dialog(this, R.style.BottomDialogStyle);
        View contentView = LayoutInflater.from(this).inflate(R.layout.dialog_province_select, null);
        dialog.setContentView(contentView);
        
        Window window = dialog.getWindow();
        if (window != null) {
            window.setGravity(Gravity.BOTTOM);
            window.setBackgroundDrawable(new ColorDrawable(Color.TRANSPARENT));
            WindowManager.LayoutParams lp = window.getAttributes();
            lp.width = WindowManager.LayoutParams.MATCH_PARENT;
            lp.height = WindowManager.LayoutParams.WRAP_CONTENT;
            window.setAttributes(lp);
        }
        
        TextView tvTitle = contentView.findViewById(R.id.tv_title);
        RecyclerView recyclerView = contentView.findViewById(R.id.recycler_view);
        TextView tvCancel = contentView.findViewById(R.id.tv_cancel);
        
        tvTitle.setText("选择省份");
        tvCancel.setOnClickListener(v -> dialog.dismiss());
        
        // 设置RecyclerView
        recyclerView.setLayoutManager(new LinearLayoutManager(this));
        ProvinceAdapter adapter = new ProvinceAdapter(originalData);
        adapter.setOnItemClickListener(new ProvinceAdapter.OnItemClickListener() {
            @Override
            public void onItemClick(ECity city) {
                tv_province.setText(city.getAreaName());
                provinceCode = city.getAreaCode();
                dialog.dismiss();
            }
        });
        recyclerView.setAdapter(adapter);
        
        dialog.show();
    }
    
    /**
     * 省份选择适配器
     */
    private static class ProvinceAdapter extends RecyclerView.Adapter<ProvinceAdapter.ViewHolder> {
        private List<ECity> data;
        private OnItemClickListener listener;
        
        interface OnItemClickListener {
            void onItemClick(ECity city);
        }
        
        public ProvinceAdapter(List<ECity> data) {
            this.data = data;
        }
        
        public void setOnItemClickListener(OnItemClickListener listener) {
            this.listener = listener;
        }
        
        @Override
        public ViewHolder onCreateViewHolder(ViewGroup parent, int viewType) {
            View view = LayoutInflater.from(parent.getContext()).inflate(R.layout.item_province, parent, false);
            return new ViewHolder(view);
        }
        
        @Override
        public void onBindViewHolder(ViewHolder holder, int position) {
            ECity city = data.get(position);
            holder.tvProvinceName.setText(city.getAreaName());
            
            holder.itemView.setOnClickListener(v -> {
                if (listener != null) {
                    listener.onItemClick(city);
                }
            });
        }
        
        @Override
        public int getItemCount() {
            return data == null ? 0 : data.size();
        }
        
        static class ViewHolder extends RecyclerView.ViewHolder {
            TextView tvProvinceName;
            
            public ViewHolder(View itemView) {
                super(itemView);
                tvProvinceName = itemView.findViewById(R.id.tv_province_name);
            }
        }
    }

}
