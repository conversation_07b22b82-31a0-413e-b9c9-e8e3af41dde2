package com.zczy.cargo_owner.user.setting

import android.content.Context
import android.content.Intent
import android.os.Bundle
import android.os.Handler
import android.os.Looper
import android.text.TextUtils
import android.util.SparseArray
import android.view.View
import android.view.animation.AnimationUtils
import android.widget.CheckBox
import com.jakewharton.rxbinding2.view.RxView
import com.jakewharton.rxbinding2.widget.RxTextView
import com.sfh.lib.AppCacheManager
import com.sfh.lib.event.RxBusEventManager
import com.sfh.lib.mvvm.annotation.LiveDataMatch
import com.sfh.lib.mvvm.service.BaseViewModel
import com.sfh.lib.rx.ui.UtilRxView
import com.sfh.lib.ui.dialog.DialogBuilder
import com.zczy.cargo_owner.R
import com.zczy.cargo_owner.home.HomeActivity
import com.zczy.cargo_owner.home.onlinecall.OnLineCallActivity
import com.zczy.cargo_owner.libcomm.config.HttpConfig
import com.zczy.cargo_owner.user.certification.CertificationUtils
import com.zczy.cargo_owner.user.login.activity.RegisterStep1Activity
import com.zczy.cargo_owner.user.login.activity.SelectPersonDialog
import com.zczy.cargo_owner.user.login.activity.UserLoginOtherErrorDialogActivity
import com.zczy.cargo_owner.user.login.authenticationdialog.AuthenticationDialog
import com.zczy.cargo_owner.user.login.entity.RxReStarHome
import com.zczy.cargo_owner.user.login.mode.LoginCodeModel
import com.zczy.cargo_owner.user.login.request.ReqLogin.CodeBuilder
import com.zczy.cargo_owner.user.login.request.ReqSendCodeV1
import com.zczy.cargo_owner.wight.AgreementView.AgreementTxt
import com.zczy.comm.Const
import com.zczy.comm.ZczyApplication
import com.zczy.comm.data.entity.ELogin
import com.zczy.comm.data.request.ReqSendCode
import com.zczy.comm.ui.BaseActivity
import com.zczy.comm.utils.VerificationCodeUtil
import com.zczy.comm.widget.ImageCodeDialog
import com.zczy.comm.x5.X5WebActivity
import io.reactivex.Observable
import kotlinx.android.synthetic.main.user_switch_phone_longin_activity.agreementView
import kotlinx.android.synthetic.main.user_switch_phone_longin_activity.btLogin
import kotlinx.android.synthetic.main.user_switch_phone_longin_activity.etAccount
import kotlinx.android.synthetic.main.user_switch_phone_longin_activity.etPwd
import kotlinx.android.synthetic.main.user_switch_phone_longin_activity.lyVoice
import kotlinx.android.synthetic.main.user_switch_phone_longin_activity.tvChangeLogin
import kotlinx.android.synthetic.main.user_switch_phone_longin_activity.tvTime
import kotlinx.android.synthetic.main.user_switch_phone_longin_activity.tvVoiceTime
import kotlinx.android.synthetic.main.user_switch_phone_longin_activity.tv_call
import java.util.concurrent.TimeUnit

/**
 *  desc: 切换登录验证码登录
 *  user: 宋双朋
 *  time: 2024/8/16 14:35
 */
class UserSwitchPhoneLoginActivity : BaseActivity<BaseViewModel>() {
    private var mCodeType = ReqSendCode.TYPE_SMS
    private var codeUtil: VerificationCodeUtil? = null
    private var authenticationDialog: AuthenticationDialog? = null
    private val phone by lazy { intent.getStringExtra(PHONE) }

    companion object {
        const val PHONE = "phone"

        @JvmStatic
        fun startSwitch(context: Context, phone: String?) {
            val intent = Intent(context, UserSwitchPhoneLoginActivity::class.java)
            intent.putExtra(PHONE, phone)
            context.startActivity(intent)
        }
    }

    override fun getLayout(): Int {
        return R.layout.user_switch_phone_longin_activity
    }

    override fun bindView(bundle: Bundle?) {
        tv_call.setOnClickListener {
            OnLineCallActivity.start(
                this@UserSwitchPhoneLoginActivity,
                ""
            )
        }
        val changes = Observable.combineLatest<CharSequence, CharSequence, Boolean>(
            RxTextView.textChanges(etAccount), RxTextView.textChanges(etPwd)
        ) { charSequence: CharSequence?, charSequence2: CharSequence? ->
            (TextUtils.isEmpty(
                charSequence
            ) || TextUtils.isEmpty(charSequence2))
        }.subscribe { aBoolean: Boolean ->
            btLogin.isEnabled = !aBoolean
        }
        UtilRxView.afterTextChangeEvents(etAccount, 500) {
            checkPhone(isPhone = true)
        }
        UtilRxView.afterTextChangeEvents(etPwd, 500) {
            checkPhone(isPhone = false)
        }
        this.putDisposable(changes)
        // 登录
        val click = RxView.clicks(this.btLogin).throttleFirst(1, TimeUnit.SECONDS).subscribe {
            if (!agreementView.checkBox.isChecked) {
                showToast("请先勾选同意后再登录")
                val shake = AnimationUtils.loadAnimation(applicationContext, R.anim.translate)
                agreementView.startAnimation(shake)
                return@subscribe
            }
            val account = etAccount.text.toString()
            val pwd = etPwd.text.toString()
            val builder = CodeBuilder()
            builder.setLoginName(account)
            builder.setLoginCode(pwd)
            //验证码类型
            builder.setCodeType(TextUtils.equals(ReqSendCode.TYPE_SMS, mCodeType))
            getViewModel(LoginCodeModel::class.java).login(builder.bulder())
        }
        tvChangeLogin.setOnClickListener {
            UserSwitchAccountLoginActivity.startSwitch(this@UserSwitchPhoneLoginActivity, "")
            finish()
        }
        this.putDisposable(click)
        //验证辅助
        this.codeUtil = VerificationCodeUtil(
            ReqSendCode.MODULE_TYPE_I,
            object : VerificationCodeUtil.IOnCallback {
                override fun getPhone(): String {
                    return etAccount.text.toString()
                }

                override fun onClickCode(req: ReqSendCode) {
                    mCodeType = req.type
                    getViewModel(LoginCodeModel::class.java).showImageVerifyCode(req)
                }

                override fun showToast(toast: CharSequence) {
                    val dialogBuilder = DialogBuilder()
                    dialogBuilder.setMessage("提示")
                    dialogBuilder.setMessage(toast)
                    dialogBuilder.setHideCancel(true)
                    showDialog(dialogBuilder)
                }
            }).build(this.tvTime, this.tvVoiceTime, this.lyVoice)
        this.etAccount.setText(phone)
        val agreementTxtSparseArray = SparseArray<AgreementTxt>()
        agreementTxtSparseArray.put(
            "中储智运平台服务协议".hashCode(),
            AgreementTxt(
                "中储智运平台服务协议",
                HttpConfig.getWebUrl("/form_h5/documents/app_documents/app_consignor/service.html?sceneCode=133&plateType=1&time=" + System.currentTimeMillis())
            )
        )
        agreementTxtSparseArray.put(
            "中储智运网络货运平台交易规则".hashCode(),
            AgreementTxt(
                "中储智运网络货运平台交易规则",
                HttpConfig.getWebUrl("/form_h5/documents/app_documents/app_consignor/tradeRule.html?sceneCode=133&plateType=2&time=" + System.currentTimeMillis())
            )
        )
        agreementTxtSparseArray.put(
            "隐私政策".hashCode(),
            AgreementTxt(
                "隐私政策",
                HttpConfig.getWebUrl("/form_h5/documents/app_documents/app_consignor/privacy.html?sceneCode=133&plateType=4&time=" + System.currentTimeMillis())
            )
        )
        agreementTxtSparseArray.put(
            "用户授权协议".hashCode(),
            AgreementTxt(
                "用户授权协议",
                HttpConfig.getWebUrl("/form_h5/documents/app_documents/app_consignor/authorization.html?sceneCode=133&plateType=3&time=" + System.currentTimeMillis())
            )
        )
        agreementView.newData(agreementTxtSparseArray)
    }

    override fun initData() {

    }

    private fun checkPhone(isPhone: Boolean) {
        if (isPhone) {
            val phone = etAccount.text.toString()
            if (!TextUtils.isEmpty(phone) && phone.length >= 11) {
                val code = etPwd.text.toString()
                this.etPwd.requestFocus()
                this.etPwd.setSelection(if (TextUtils.isEmpty(code)) 0 else code.length - 1)
                codeUtil?.setAbled(true)
            } else {
                codeUtil?.setAbled(false)
            }
        }
        this.btLogin.isEnabled =
            !(TextUtils.isEmpty(etAccount.text.toString()) || TextUtils.isEmpty(etPwd.text.toString()))
    }

    @LiveDataMatch(tag = "图片验证码结果")
    open fun onSendCodeRelust(success: Boolean, type: String?) {
        codeUtil?.onSendCodeResult(success, type)
    }

    @LiveDataMatch(tag = "显示图片验证码")
    open fun onShowImageVerifyCode(req: ReqSendCode) {
        val phone = etAccount.text.toString()
        ImageCodeDialog(this, phone) { _: ImageCodeDialog?, code: String? ->
            val reqNew = ReqSendCodeV1()
            reqNew.type = req.type
            reqNew.moduleType = req.moduleType
            reqNew.mobile = req.mobile
            reqNew.setImageCode(code)
            getViewModel(LoginCodeModel::class.java).sendVerifyCodeV1(reqNew)
        }.show()
    }

    @LiveDataMatch(tag = "新设备认证短信发送成功")
    open fun onSendAuthenticationCodeSuccess(login: ELogin?) {
        //新设备认证提示
        if (this.authenticationDialog == null) {
            this.authenticationDialog =
                AuthenticationDialog(this).setListener(object : AuthenticationDialog.Listener {
                    override fun onReSendCode(data: ELogin) {
                        // 重新发送验证码
                        getViewModel(LoginCodeModel::class.java).sendAuthenticationSMS(data)
                    }

                    override fun onCommit(data: ELogin, code: String) {
                        // 认证
                        getViewModel(LoginCodeModel::class.java).checkAuthenticationCode(data, code)
                    }
                })
        }
        authenticationDialog?.setLogin(login)
        if (authenticationDialog?.isShowing != true) {
            authenticationDialog?.show()
        }
    }

    @LiveDataMatch(tag = "登录成功 or 新设备认证提示")
    open fun onLoginSuccess(login: ELogin) {
        AppCacheManager.putCache("login_phone", etAccount.text.toString())
        if (TextUtils.equals("1", login.readProtocol)) {
            val view = View.inflate(this, R.layout.user_login_dialog, null)
            val checkBox = view.findViewById<CheckBox>(R.id.cbSelect)

            view.findViewById<View>(R.id.tvAgreement).setOnClickListener { v: View? ->
                X5WebActivity.startContentUI(
                    this,
                    "中储智运平台服务协议",
                    HttpConfig.getWebUrl("/form_h5/documents/app_documents/app_consignor/service.html?sceneCode=133&plateType=1&time=" + System.currentTimeMillis())
                )
            }
            view.findViewById<View>(R.id.tvRule).setOnClickListener { v: View? ->
                X5WebActivity.startContentUI(
                    this,
                    "中储智运网络货运平台交易规则",
                    HttpConfig.getWebUrl("/form_h5/documents/app_documents/app_consignor/tradeRule.html?sceneCode=133&plateType=2&time=" + System.currentTimeMillis())
                )
            }

            val builder = DialogBuilder()
            builder.setView(view)
            builder.setTitle("提示")
            builder.setCancelListener { dialogInterface: DialogBuilder.DialogInterface, i: Int ->
                dialogInterface.dismiss()
                AppCacheManager.removeCache(Const.LOGIN_KEY)
            }
            builder.setOkListener { dialogInterface: DialogBuilder.DialogInterface, i: Int ->
                if (!checkBox.isChecked) {
                    showToast("请选择阅读并同意")
                    return@setOkListener
                }
                dialogInterface.dismiss()
                getViewModel(LoginCodeModel::class.java).updateUserSpecialBackUp()
                gotoHome(login)
            }
            showDialog(builder)
        } else {
            gotoHome(login)
        }
    }

    @LiveDataMatch(tag = "登录ZCZY-10873 供应链会员中台-用户部分")
    open fun onLoginSuccessToast(login: ELogin) {
        SelectPersonDialog(this, login.notUserTypeMsg) { dialog, which ->
            dialog.dismiss()
            if (which == 1) {
                //确认认证
                getViewModel(LoginCodeModel::class.java).switchUserType()
            } else {
                //退出登录
                finish()
                val application = AppCacheManager.getApplication<ZczyApplication>()
                application.onLoseToken("", "")
            }
        }.show()
    }

    @LiveDataMatch
    open fun onSwitchUserTypeSuccess() {
        //确认认证
        HomeActivity.start(this)
        CertificationUtils.hzCertification(this@UserSwitchPhoneLoginActivity)
        finish()
    }

    @LiveDataMatch
    open fun loginError(msg: String?) {
        UserLoginOtherErrorDialogActivity.startContentUI(this, msg)
    }

    private fun gotoHome(login: ELogin) {
        if (TextUtils.equals("1", login.wheterChangePassword)) {
            //需要修改密码
            UserEditPasswordctivity.startUI(this)
        } else {
            toHome()
        }
    }

    private fun toHome() {
        RxBusEventManager.postEvent(RxReStarHome(reStar = true))
        Handler(Looper.getMainLooper()).postDelayed({
            HomeActivity.start(this@UserSwitchPhoneLoginActivity)
            this.finish()
        }, 500)
    }

    @LiveDataMatch(tag = "去注册")
    open fun onRegiester() {
        RegisterStep1Activity.start(this, this.etAccount.text.toString())
    }
}