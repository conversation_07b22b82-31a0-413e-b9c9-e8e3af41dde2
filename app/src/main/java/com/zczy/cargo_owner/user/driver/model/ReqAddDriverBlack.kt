package com.zczy.cargo_owner.user.driver.model

import com.zczy.comm.CommServer
import com.zczy.comm.http.entity.BaseNewRequest
import com.zczy.comm.http.entity.BaseRsp
import com.zczy.comm.http.entity.ResultData

/**
 *@Desc 添加承运方黑名单
 *@User ssp
 *@Date 2023/8/31-19:38
 */
class ReqAddDriverBlack(
    var consignorUserId: String? = null, //	货主userId
    var consignorUserName: String? = null,//	货主用户名
    var consignorMemberName: String? = null, //	货主企业名称
    var consignorMobile: String? = null, //	货主手机号
    var memberName: String? = null,//  司机姓名
    var mobile: String? = null,//司机手机号
    var remark: String? = null,//备注
) : BaseNewRequest<BaseRsp<ResultData>>("/mms-app/consignorCarrierRelation/addRelationBlack") {

    override fun buildParam(): Any {
        CommServer.getUserServer().login?.let {
            consignorUserId = it.userId
            consignorUserName = it.userName
            consignorMemberName = it.memberName
            consignorMobile = it.mobile
        }
        return super.buildParam()
    }
}