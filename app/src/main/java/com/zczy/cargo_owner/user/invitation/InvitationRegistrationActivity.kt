package com.zczy.cargo_owner.user.invitation

import android.content.Context
import android.content.Intent
import android.os.Bundle
import androidx.core.content.ContextCompat
import android.util.Base64
import android.view.View
import android.widget.ImageView
import android.widget.TextView
import android.widget.Toast
//import cn.sharesdk.framework.Platform
//import cn.sharesdk.framework.PlatformActionListener
//import cn.sharesdk.framework.ShareSDK
//import cn.sharesdk.system.text.ShortMessage
//import cn.sharesdk.tencent.qq.QQ
//import cn.sharesdk.wechat.friends.Wechat
//import cn.sharesdk.wechat.moments.WechatMoments
import com.sfh.lib.mvvm.annotation.LiveDataMatch
import com.sfh.lib.ui.AbstractLifecycleActivity
import com.zczy.cargo_owner.R
import com.zczy.cargo_owner.libcomm.config.HttpConfig
import com.zczy.cargo_owner.user.invitation.bean.InvitationRegister
import com.zczy.comm.CommServer
import com.zczy.comm.ui.UtilStatus
import com.zczy.comm.utils.imgloader.ImgUtil
import com.zczy.comm.widget.AppToolber
import com.zczy.lib_zshare.ZShare
import com.zczy.lib_zshare.share.ShareConstants
import com.zczy.lib_zshare.share.ShareInfo
import kotlinx.android.synthetic.main.user_invitation_registration_activity.*
import java.lang.Exception
import java.util.ArrayList
import java.util.HashMap

/** 功能描述:
 *
 * <AUTHOR> 贾发展
 * @company 中储智运科技股份有限公司
 * @copyright （版权）中储南京智慧物流科技有限公司所有
 * @date 2019/6/18
 */
open class InvitationRegistrationActivity : AbstractLifecycleActivity<InvitationModel>() {

    private val tvInvitationCode by lazy { findViewById<TextView>(R.id.tv_invitation_code) }
    private val tvInvitationShare by lazy { findViewById<TextView>(R.id.tv_invitation_share) }
    private val ivInvitationQcode by lazy { findViewById<ImageView>(R.id.iv_invitation_qcode) }
    private val appToolber by lazy { findViewById<AppToolber>(R.id.appToolber) }

    companion object {
        private const val pictureUrl = "https://zczy.oss-cn-shanghai.aliyuncs.com/logo.png"
        private const val titleText = "你的好友向你推荐中储智运！"
        private const val description = "注册成为智运承运人，首单抽4999现金券大奖！"

        @JvmStatic
        fun start(context: Context) {
            val intent = Intent(context, InvitationRegistrationActivity::class.java)
            context.startActivity(intent)
        }
    }

    private val decodeUserId: String
        get() {
            val login = CommServer.getUserServer().login
            return if (login != null && login.userId.isNotEmpty()) {
                try {
                    Base64.encodeToString(login.userId.toByteArray(), Base64.NO_WRAP)
                } catch (e: Exception) {
                    ""
                }
            } else {
                ""
            }
        }
    var content = HttpConfig.getWebUrl() + "mms-app/share/index.html#/share?userId=" + decodeUserId + "&clientType=ANDROID"

    override fun onCreate(savedInstanceState: Bundle?) {
        super.onCreate(savedInstanceState)
        setContentView(R.layout.user_invitation_registration_activity)
        UtilStatus.initStatus(this, ContextCompat.getColor(this, R.color.text_blue))
        initData()
        appToolber.setOnClickListener { }
        appToolber.setRightOnClickListener { MyInvitationActivity.start(this) }
        tv_invitation_share.setOnClickListener {
            showShare(content)
        }
    }

    private fun getContentString(): String {
        return HttpConfig.getWebUrl() + "mms-app/share/index.html#/share?userId=" + decodeUserId + "&clientType=ANDROID"
    }


    private fun showShare(content: String) {
//        val shareDialog = ShareDialog(this)
//        shareDialog.setCancelButtonOnClickListener { shareDialog.dismiss() }
//
//        shareDialog.setWxShareButtonOnClickListener {
//            wxshareLoan(content)
//            shareDialog.dismiss()
//        }
//
//        shareDialog.setWxMomentsShareButtonOnClickListener {
//            wxpyqshareLoan(content)
//            shareDialog.dismiss()
//        }
//
//        shareDialog.setQQShareButtonOnClickListener {
//            qqshareLoan(content)
//            shareDialog.dismiss()
//        }
//
//        shareDialog.setQZoneShareButtonOnClickListener {
//            qqkjshareLoan(content)
//            shareDialog.dismiss()
//        }

        val info = ShareInfo()
        info.title = titleText
        info.content = description
        info.webUrl = content
        info.thumbnailUrl = pictureUrl
        ZShare.share(this@InvitationRegistrationActivity, info)

    }

    //微信分享
//    fun wxshareLoan(content: String) {
//        val sp = Platform.ShareParams()
//
//        sp.setTitle(title as String?)
//        sp.titleUrl = content
//        sp.imageUrl = pictureUrl
//        sp.url = content
//        sp.text = description
//        sp.shareType = Platform.SHARE_WEBPAGE
//        val wx = ShareSDK.getPlatform(Wechat.NAME)
//        val isInstall = wx.isClientValid
//        if (!isInstall) {
//            showToast("请安装微信客户端")
//            return
//        }
//        wx.platformActionListener = object : PlatformActionListener {
//            override fun onComplete(platform: Platform, i: Int, hashMap: HashMap<String, Any>) {
//
//            }
//
//            override fun onError(platform: Platform, i: Int, throwable: Throwable) {
//
//            }
//
//            override fun onCancel(platform: Platform, i: Int) {
//
//            }
//        }
//        // 执行图文分享
//        wx.share(sp)
//    }

    //微信朋友圈分享
    //微信分享
//    fun wxpyqshareLoan(content: String) {
//        val sp = Platform.ShareParams()
//        sp.setTitle(title.toString())
//        sp.titleUrl = content
//        sp.imageUrl = pictureUrl
//        sp.url = content
//        sp.shareType = Platform.SHARE_WEBPAGE
//        val wxpyq = ShareSDK.getPlatform(WechatMoments.NAME)
//        val isInstall = wxpyq.isClientValid
//        if (!isInstall) {
//            showToast("请安装微信客户端")
//            return
//        }
//        wxpyq.platformActionListener = object : PlatformActionListener {
//            override fun onComplete(platform: Platform, i: Int, hashMap: HashMap<String, Any>) {
//
//            }
//
//            override fun onError(platform: Platform, i: Int, throwable: Throwable) {
//
//            }
//
//            override fun onCancel(platform: Platform, i: Int) {
//
//            }
//        }
//        wxpyq.share(sp)
//    }

    //QQ分享
//    fun qqshareLoan(content: String) {
//        val sp = Platform.ShareParams()
//        sp.setTitle(title as String?)
//        sp.titleUrl = content // 标题的超链接
//        sp.imageUrl = pictureUrl
//        val qq = ShareSDK.getPlatform(QQ.NAME)
//        val isInstall = qq.isClientValid
//        if (!isInstall) {
//            showToast("请安装QQ客户端")
//            return
//        }
//        qq.platformActionListener = object : PlatformActionListener {
//            override fun onComplete(platform: Platform, i: Int, hashMap: HashMap<String, Any>) {
//
//            }
//
//            override fun onError(platform: Platform, i: Int, throwable: Throwable) {
//
//            }
//
//            override fun onCancel(platform: Platform, i: Int) {
//
//            }
//        }
//        qq.share(sp)
//    }

    //qq空间分享
//    fun qqkjshareLoan(content: String) {
//        val sp = Platform.ShareParams()
//        sp.text = content
//        val qqkj = ShareSDK.getPlatform(ShortMessage.NAME)
//
//        qqkj.platformActionListener = object : PlatformActionListener {
//            override fun onComplete(platform: Platform, i: Int, hashMap: HashMap<String, Any>) {
//
//            }
//
//            override fun onError(platform: Platform, i: Int, throwable: Throwable) {
//
//            }
//
//            override fun onCancel(platform: Platform, i: Int) {
//
//            }
//        }
//        qqkj.share(sp)
//    }

    private fun initData() {
        getViewModel()?.queryInviteCodeAndQRcode()
    }

    @LiveDataMatch
    open fun queryInviteCodeAndQRcodeSuccess(data: InvitationRegister?) {
        tvInvitationCode.text = data?.inviteCode
        ImgUtil.loadUrl(ivInvitationQcode, HttpConfig.getUrlImage(data?.qrCode))
    }
}