package com.zczy.cargo_owner.user.overdue.model

import android.text.TextUtils
import com.sfh.lib.exception.HandleException
import com.sfh.lib.mvvm.service.BaseViewModel
import com.sfh.lib.rx.IResult
import com.sfh.lib.ui.dialog.DialogBuilder
import com.zczy.cargo_owner.user.certification.req.PicUrlBean
import com.zczy.cargo_owner.user.certification.req.ReqUploadExpireDateLicense
import com.zczy.cargo_owner.user.overdue.req.ReqQueryUserExpireLicense
import com.zczy.cargo_owner.user.overdue.req.ReqQueryUserExpireLicenseDetail
import com.zczy.cargo_owner.user.overdue.req.RspQueryUserExpireLicenseB
import com.zczy.cargo_owner.user.overdue.req.RspQueryUserExpireLicenseDetail
import com.zczy.comm.CommServer
import com.zczy.comm.file.IFileServer
import com.zczy.comm.http.entity.BaseRsp
import com.zczy.comm.http.entity.ResultData
import java.io.File

/**
 * 功能描述: 证件过期管理
 * <AUTHOR>
 * @date 2022/10/14-11:28
 */

class CysExpiredCertificateManagementModel : BaseViewModel() {

    fun queryUserExpireLicense() {

        execute(ReqQueryUserExpireLicense()) {
            if (it.success()) {
                val list = mutableListOf<RspQueryUserExpireLicenseB>()
                val data = it.data
                val userList = mutableListOf<RspQueryUserExpireLicenseB>()
                data?.userData?.forEachIndexed { index, rspQueryUserExpireLicenseB ->
                    if (index == 0) {
                        rspQueryUserExpireLicenseB.title = "企业信息"
                        userList.add(rspQueryUserExpireLicenseB)
                    } else {
                        userList.add(rspQueryUserExpireLicenseB)
                    }
                }
                val vehicleList = mutableListOf<RspQueryUserExpireLicenseB>()
                data?.vehicleData?.forEach { b ->
                    b.vehicleLicense?.forEachIndexed { index, rspQueryUserExpireLicenseB ->
                        rspQueryUserExpireLicenseB.vehicleId = b.vehicleId
                        rspQueryUserExpireLicenseB.plateNumber = b.plateNumber
                        if (index == 0) {
                            rspQueryUserExpireLicenseB.title = b.plateNumber
                            vehicleList.add(rspQueryUserExpireLicenseB)
                        } else {
                            vehicleList.add(rspQueryUserExpireLicenseB)
                        }
                    }
                }
                list.addAll(userList)
                list.addAll(vehicleList)
                setValue("onListSuccess", list)
            } else {
                showToast(it.msg)
            }
        }
    }

    fun queryUserExpireLicenseDetail(req: ReqQueryUserExpireLicenseDetail) {
        execute(req, object : IResult<BaseRsp<RspQueryUserExpireLicenseDetail>> {
            override fun onSuccess(t: BaseRsp<RspQueryUserExpireLicenseDetail>) {
                if (t.success()) {
                    setValue("onQueryUserExpireLicenseDetailSuccess", t.data)
                } else {
                    showToast(t.msg)
                }
            }

            override fun onFail(e: HandleException) {
                setValue(
                    "onQueryUserExpireLicenseDetailSuccess", RspQueryUserExpireLicenseDetail(
                        licenseType = "400001",
                        licenseState = "3",
                        licenseReason = "测试不通过数据"
                    )
                )
            }
        })
    }

    /**
     * 图片上传
     *
     * @param file
     */
    fun upLoadPicNoZip(file: String, viewType: String) {
        if (TextUtils.isEmpty(file)) {
            return
        }
        showLoading(false)
        val fileServer = CommServer.getFileServer()
        val old = File(file)
        if (old.exists()) {
            val disposable = fileServer.update(old, object : IFileServer.OnFileUploaderListener {
                override fun onSuccess(old: File, url: String) {
                    hideLoading()
                    setValue("upLoadPicSuccess", viewType, url)
                }

                override fun onFailure(tag: File, error: String) {
                    hideLoading()
                    showToast(error)
                    showUploadPicDialog(file, "2", viewType)
                }
            }, false)
            putDisposable(disposable)
        }
    }

    /**
     * 图片上传
     *
     * @param file
     */
    fun upLoadPic(file: String, viewType: String) {
        if (TextUtils.isEmpty(file)) {
            return
        }
        showLoading(false)
        val fileServer = CommServer.getFileServer()
        val old = File(file)
        if (old.exists()) {
            val disposable = fileServer.update(old, object : IFileServer.OnFileUploaderListener {
                override fun onSuccess(tag: File, url: String) {
                    hideLoading()
                    setValue("upLoadPicSuccess", viewType, url)
                }

                override fun onFailure(tag: File, error: String) {
                    hideLoading()
                    showToast(error)
                    showUploadPicDialog(file, "1", viewType)
                }
            }, true)
            putDisposable(disposable)
        }
    }

    private fun showUploadPicDialog(file: String, flag: String, viewType: String) {
        val dialogBuilder = DialogBuilder()
        dialogBuilder.title = "提示"
        dialogBuilder.message = "图片上传失败，请重新上传！"
        dialogBuilder.okListener =
            DialogBuilder.DialogInterface.OnClickListener { dialog: DialogBuilder.DialogInterface, _: Int ->
                if (TextUtils.equals(flag, "1")) {
                    upLoadPic(file, viewType)
                } else if (TextUtils.equals(flag, "2")) {
                    upLoadPicNoZip(file, viewType)
                }
                dialog.dismiss()
            }
        showDialog(dialogBuilder)
    }

    /**
     * 上传过期图片
     *
     * @param picUrlList
     */
    fun uploadExpireDateLicense(picUrlList: MutableList<PicUrlBean>) {
        this.execute(true, ReqUploadExpireDateLicense(picUrlList = picUrlList), object : IResult<BaseRsp<ResultData>> {
            override fun onFail(e: HandleException) {
                showDialogToast(e.message)
            }

            @Throws(Exception::class)
            override fun onSuccess(resultDataBaseRsp: BaseRsp<ResultData>) {
                if (resultDataBaseRsp.success()) {
                    setValue("onUploadExpireDateSuccess")
                } else {
                    showDialogToast(resultDataBaseRsp.data?.resultMsg)
                }
            }
        })
    }
}