package com.zczy.cargo_owner.user.info

import android.annotation.SuppressLint
import android.content.Context
import android.content.Intent
import android.graphics.Color
import android.os.Bundle
import android.text.Editable
import android.text.TextUtils
import android.text.TextWatcher
import android.view.View
import com.sfh.lib.mvvm.annotation.LiveDataMatch
import com.sfh.lib.ui.AbstractLifecycleActivity
import com.sfh.lib.ui.dialog.DialogBuilder
import com.zczy.cargo_owner.R
import com.zczy.cargo_owner.user.info.model.ReqUnbindMobile
import com.zczy.cargo_owner.user.info.model.UserEditPhoneModel
import com.zczy.cargo_owner.user.login.mode.ForgetModel
import com.zczy.comm.CommServer
import com.zczy.comm.Const
import com.zczy.comm.data.entity.EUser
import com.zczy.comm.data.request.ReqCheckCode
import com.zczy.comm.data.request.ReqSendCode
import com.zczy.comm.http.entity.ResultData
import com.zczy.comm.ui.UtilStatus
import com.zczy.comm.utils.PhoneUtil.callPhone
import com.zczy.comm.utils.ex.setVisible
import com.zczy.comm.widget.ImageCodeDialog
import kotlinx.android.synthetic.main.user_edit_phone_activity.*

/**
 * 功能描述: 修改手机号码1
 *
 * <AUTHOR> 孙飞虎
 * @company 中储智运科技股份有限公司
 * @copyright （版权）中储南京智慧物流科技有限公司所有
 * @date 2018/11/20
 */
class UserEditPhoneOneActivity : AbstractLifecycleActivity<UserEditPhoneModel?>(), View.OnClickListener, TextWatcher {
    private var code: String = ""
    private var phone: String = ""
    override fun onCreate(savedInstanceState: Bundle?) {
        super.onCreate(savedInstanceState)
        setContentView(R.layout.user_edit_phone_activity)
        UtilStatus.initStatus(this, Color.WHITE)

        //若该手机无法接受验证码，您可通过人脸识别进行修改！
        val login = CommServer.getUserServer().login
        if (login != null) {
            phone = login.mobile
            tvPhone.text = phone.replace("(\\d{3})\\d{4}(\\d{4})".toRegex(), "$1****$2")
        }
        etCode.addTextChangedListener(this)
        tvSendCode.setTextContent("获取验证码", "重新发送(%ss)")
        tvSendCode.setOnClickListener(this)
        btMext.setOnClickListener(this)
        unBindPhone.setOnClickListener(this)
        iv_server_phone.setOnClickListener { callPhone(this@UserEditPhoneOneActivity, Const.PHONE_SERVER_400) }
        viewModel?.queryInfo()
    }

    /***
     * 简明信息成功
     * @param user
     */
    @SuppressLint("SetTextI18n")
    @LiveDataMatch
    open fun onUserInfoSuccess(user: EUser) {
        if (TextUtils.equals("0", user?.nooType?:"")) {
            //次用户
            unBindPhone.setVisible(true)
        } else {
            unBindPhone.setVisible(false)
        }
    }

    @LiveDataMatch
    open fun onSendCode(success: Boolean, type: String?) {
        if (success) {
            showDialogToast("验证码发送成功")
            tvSendCode.isEnabled = false
            tvSendCode.startInterval(60L)
        } else {
            tvSendCode.isEnabled = true
        }
    }

    @LiveDataMatch
    open fun onShowImageVerifyCode(req: ReqSendCode) {
        ImageCodeDialog(this, phone) { _: ImageCodeDialog?, code: String? ->
            req.setImageCode(code)
            viewModel?.sendVerifyCode(req)
        }.show()
    }

    @LiveDataMatch(tag = "验证码验证成功")
    open fun onCheckSuccess() {
        UserEditPhoneTwoActivity.start(this, phone, code, ReqSendCode.TYPE_SMS, ReqCheckCode.MODULE_TYPE_D)
        finish()
    }

    @LiveDataMatch
    open fun unBindSuccess(msg: String) {
        val builder = DialogBuilder()
        builder.message = msg
        builder.isHideCancel = true
        builder.isCancelable = false
        builder.setOKText("我知道了")
        builder.okListener = DialogBuilder.DialogInterface.OnClickListener { dialogInterface: DialogBuilder.DialogInterface, _: Int ->
            dialogInterface.dismiss()
            finish()
        }
        showDialog(builder)
    }

    @LiveDataMatch
    open fun checkMobileExistError(data: ResultData) {
        when (data.resultCode) {
            "0001" -> {
                //在非中储租户下有
                val builder = DialogBuilder()
                builder.message = data.resultMsg
                builder.isHideCancel = true
                builder.isCancelable = false
                builder.setOKText("我知道了")
                showDialog(builder)
            }

            "0002" -> {
                //仅在中储租户下有
                val builder = DialogBuilder()
                builder.message = "解绑后将无法使用手机号登录，请确认是否继续操作?"
                builder.isHideCancel = false
                builder.isCancelable = false
                builder.okListener = DialogBuilder.DialogInterface.OnClickListener { dialogInterface: DialogBuilder.DialogInterface, _: Int ->
                    dialogInterface.dismiss()
                    viewModel?.unbindMobile(
                        req = ReqUnbindMobile(
                            verifyCode = code,
                            mobile = phone
                        )
                    )
                }
                showDialog(builder)
            }
        }
    }

    override fun onClick(v: View) {
        when (v) {
            tvSendCode -> {
                val req = ReqSendCode()
                req.mobile = phone
                req.type = ReqSendCode.TYPE_SMS
                req.moduleType = ReqSendCode.MODULE_TYPE_D
                this.viewModel?.showImageVerifyCode(req)
            }

            btMext -> {
                code = etCode.text.toString()
                if (TextUtils.isEmpty(code)) {
                    showDialogToast("请输入验证码")
                    return
                }
                if (code.length < 6) {
                    showDialogToast("请输入正确的验证码")
                    return
                }
                val req = ReqCheckCode()
                req.mobile = phone
                req.verifyCode = code
                req.verifyCodeType = ReqSendCode.TYPE_SMS
                req.moduleType = ReqCheckCode.MODULE_TYPE_D
                this.viewModel?.checkVerifyCode(req)
            }

            unBindPhone -> {
                getViewModel(ForgetModel::class.java).checkMobileExistV1(
                    req = ReqCheckMobileExist(
                        checkType = ReqCheckMobileExist.checkType2,
                        mobile = phone
                    )
                )
            }
        }
    }

    override fun beforeTextChanged(s: CharSequence, start: Int, count: Int, after: Int) {}
    override fun onTextChanged(s: CharSequence, start: Int, before: Int, count: Int) {}
    override fun afterTextChanged(s: Editable) {
        btMext.isEnabled = !TextUtils.isEmpty(s.toString())
    }

    companion object {
        @JvmStatic
        fun start(context: Context) {
            val intent = Intent(context, UserEditPhoneOneActivity::class.java)
            context.startActivity(intent)
        }
    }
}