package com.zczy.cargo_owner.user.invitation.req

import com.zczy.cargo_owner.user.invitation.bean.InvitationRegister
import com.zczy.comm.http.entity.BaseNewRequest
import com.zczy.comm.http.entity.BaseRsp

/** 功能描述:
 *
 * <AUTHOR> 贾发展
 * @company 中储智运科技股份有限公司
 * @copyright （版权）中储南京智慧物流科技有限公司所有
 * @date 2019/6/19
 */
class ReqInviteCodeAndQRcode : BaseNewRequest<BaseRsp<InvitationRegister>>("mms-app/member/queryInviteCodeAndQRcode")