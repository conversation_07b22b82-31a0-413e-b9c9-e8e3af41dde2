package com.zczy.cargo_owner.user.questionnaire;

import android.content.Context;
import android.content.Intent;
import android.graphics.Color;
import android.os.Bundle;
import androidx.annotation.Nullable;
import android.view.View;

import com.chad.library.adapter.base.BaseQuickAdapter;
import com.chad.library.adapter.base.listener.OnItemClickListener;
import com.sfh.lib.mvvm.annotation.LiveDataMatch;
import com.sfh.lib.ui.AbstractLifecycleActivity;
import com.zczy.cargo_owner.R;
import com.zczy.comm.http.entity.PageList;
import com.zczy.comm.ui.UtilStatus;
import com.zczy.comm.utils.ResUtil;
import com.zczy.comm.widget.AppToolber;
import com.zczy.comm.widget.pulltorefresh.CommEmptyView;
import com.zczy.comm.widget.pulltorefresh.OnLoadingListener;
import com.zczy.comm.widget.pulltorefresh.SwipeRefreshMoreLayout;

/**
 * 功能描述: 问卷调查
 *
 * <AUTHOR>
 * @company 中储智运科技股份有限公司
 * @copyright （版权）中储南京智慧物流科技有限公司所有
 * @date 2018/12/12
 */
public class QuestionnaireSurveyActivity extends AbstractLifecycleActivity<QuestionNaireModel> implements OnLoadingListener {
    private SwipeRefreshMoreLayout mSwipeRefreshMoreLayout;

    public static void start(Context context) {
        Intent intent = new Intent(context, QuestionnaireSurveyActivity.class);
        context.startActivity(intent);
    }

    @Override
    protected void onCreate(@Nullable Bundle savedInstanceState) {
        super.onCreate(savedInstanceState);
        setContentView(R.layout.questionnaire_survey_activity);
        UtilStatus.initStatus(this, Color.WHITE);
        initView();
        initListener();
    }

    private void initListener() {
        mSwipeRefreshMoreLayout.addOnItemListener(new OnItemClickListener() {
            @Override
            public void onSimpleItemClick(BaseQuickAdapter adapter, View view, int position) {
                QusetionNaireBean qusetionNaireBean = (QusetionNaireBean) adapter.getItem(position);
                String url = qusetionNaireBean.getUrl();
                QuestionnaireX5WebActivity.start(QuestionnaireSurveyActivity.this,
                        url);

            }
        });
    }

    private void initView() {
        AppToolber mAppToolber = (AppToolber) findViewById(R.id.appToolber);
        mSwipeRefreshMoreLayout = (SwipeRefreshMoreLayout) findViewById(R.id.swipe_refresh_more_layout);
        QuestionNaireSurveyAdapter adapter = new QuestionNaireSurveyAdapter();
        mSwipeRefreshMoreLayout.setAdapter(adapter, true);
        mSwipeRefreshMoreLayout.addItemDecorationSize(ResUtil.dp2px(7));
        mSwipeRefreshMoreLayout.setOnLoadListener(this);
        View emptyView = CommEmptyView.creator(QuestionnaireSurveyActivity.this,
                R.drawable.no_questionnaire, "暂无问卷调查活动");
        mSwipeRefreshMoreLayout.setEmptyView(emptyView);
    }

    @Override
    protected void onResume() {
        super.onResume();
        mSwipeRefreshMoreLayout.onAutoRefresh();
    }

    @LiveDataMatch
    public void onQueryQuestionnaireListSuccess(PageList<QusetionNaireBean> data) {
        this.mSwipeRefreshMoreLayout.onRefreshCompale(data);
    }

    @Override
    public void onRefreshUI(int nowPage) {
        ReqQuestionNaire req = new ReqQuestionNaire();
        req.setNowPage(String.valueOf(nowPage));
        req.setPageSize("10");
        getViewModel().queryQuestionnaireList(req);
    }

    @Override
    public void onLoadMoreUI(int nowPage) {
        ReqQuestionNaire req = new ReqQuestionNaire();
        req.setNowPage(String.valueOf(nowPage));
        req.setPageSize("10");
        getViewModel().queryQuestionnaireList(req);
    }

}

