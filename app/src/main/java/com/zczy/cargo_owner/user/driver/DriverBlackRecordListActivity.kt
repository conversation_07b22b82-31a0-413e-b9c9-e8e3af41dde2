package com.zczy.cargo_owner.user.driver

import android.content.Context
import android.content.Intent
import android.graphics.Color
import android.os.Bundle
import com.sfh.lib.mvvm.annotation.LiveDataMatch
import com.sfh.lib.mvvm.service.BaseViewModel
import com.zczy.cargo_owner.R
import com.zczy.cargo_owner.user.driver.adapter.DriverBlackRecordListAdapter
import com.zczy.cargo_owner.user.driver.model.DriverBlacklistModel
import com.zczy.cargo_owner.user.driver.model.ReqQueryRelationBlackRecord
import com.zczy.cargo_owner.user.driver.model.RspQueryRelationBlackRecord
import com.zczy.comm.http.entity.PageList
import com.zczy.comm.ui.BaseActivity
import com.zczy.comm.ui.UtilStatus
import com.zczy.comm.utils.ex.isNull
import com.zczy.comm.widget.pulltorefresh.CommEmptyView
import kotlinx.android.synthetic.main.driver_black_record_list_activity.swipeRefresh

/**
 * 类描述：移除记录
 * 作者：ssp
 * 创建时间：2024/5/15
 */
class DriverBlackRecordListActivity : BaseActivity<BaseViewModel?>() {
    private val mAdapter = DriverBlackRecordListAdapter()
    private val targetId by lazy { intent.getStringExtra(TARGET_ID) }
    private val targetType by lazy { intent.getStringExtra(TARGET_TYPE) }

    companion object {
        const val TARGET_ID = "targetId"
        const val TARGET_TYPE = "targetType"
        const val TARGET_TYPE_1 = "1"//承运方
        const val TARGET_TYPE_2 = "2"//车辆

        @JvmStatic
        fun jumpPage(context: Context?, targetId: String?, targetType: String?) {
            val intent = Intent(context, DriverBlackRecordListActivity::class.java)
            intent.putExtra(TARGET_ID, targetId)
            intent.putExtra(TARGET_TYPE, targetType)
            context?.startActivity(intent)
        }
    }

    override fun getLayout(): Int {
        return R.layout.driver_black_record_list_activity
    }

    override fun bindView(bundle: Bundle?) {
        UtilStatus.initStatus(this, Color.WHITE)
        swipeRefresh.apply {
            setAdapter(mAdapter, true)
            setEmptyView(CommEmptyView.creatorDef(this@DriverBlackRecordListActivity))
            addItemDecorationSize(7)
            setOnLoadListener2 { nowPage ->
                getViewModel(DriverBlacklistModel::class.java).queryRelationBlackAppList(
                    req = ReqQueryRelationBlackRecord(
                        nowPage = nowPage,
                        targetId = targetId,
                        targetType = targetType,
                    )
                )
            }
            onAutoRefresh()
        }
    }

    override fun initData() {

    }

    @LiveDataMatch
    open fun queryRelationBlackAppListSuccess(data: PageList<RspQueryRelationBlackRecord>?) {
        if (data.isNull) {
            swipeRefresh.onLoadMoreFail()
        } else {
            swipeRefresh.onRefreshCompale(data)
        }
    }
}
