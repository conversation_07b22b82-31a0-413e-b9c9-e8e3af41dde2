package com.zczy.cargo_owner.user.assure;

import android.graphics.Color;
import android.text.TextUtils;

import com.chad.library.adapter.base.BaseQuickAdapter;
import com.chad.library.adapter.base.BaseViewHolder;
import com.zczy.cargo_owner.R;

public class AssureAdapter extends BaseQuickAdapter<Assure, BaseViewHolder> {
    private String nowCurrent;
    public AssureAdapter() {
        super(R.layout.user_assure_adapter);
    }

    public void setData(String nowCurrent){
        this.nowCurrent = nowCurrent;
    }

    @Override
    protected void convert(BaseViewHolder helper, Assure item) {

        helper.setText(R.id.tv_order_id,"运单号："+item.getOrderId());
        if (TextUtils.equals("2",nowCurrent)){

            helper.setGone(R.id.tv_money,false);
        }else {
            helper.setGone(R.id.tv_money,true);
            helper.setText(R.id.tv_money,"金额："+item.getGuaranteeFee()+"元");
        }
        if (TextUtils.isEmpty(item.getStartTime())){
            helper.setGone(R.id.tv_time,false);
        }else {
            helper.setGone(R.id.tv_time,true);
            helper.setText(R.id.tv_time,"生效时间："+item.getStartTime());
        }
        // 1未保障  2保障中 3保障完成
        if ( TextUtils.equals("2", item.getGuaranteeState())){
            helper.setText(R.id.tv_status,"保障中");
            helper.setVisible(R.id.iv_icon,true).setImageResource(R.id.iv_icon,R.drawable.assure_img);
            helper.setTextColor(R.id.tv_status, Color.parseColor("#ffff602e"));
        }else if (TextUtils.equals("1", item.getGuaranteeState()) ){
            helper.setText(R.id.tv_status,"未保障");
            helper.setVisible(R.id.iv_icon,false);
            helper.setTextColor(R.id.tv_status, Color.parseColor("#ff666666"));
            helper.setText(R.id.tv_time,"生效时间：  --");
        }else if (TextUtils.equals("3", item.getGuaranteeState()) ){
            helper.setText(R.id.tv_status,"保障完成");
            helper.setVisible(R.id.iv_icon,true).setImageResource(R.id.iv_icon,R.drawable.assure_compat);
            helper.setTextColor(R.id.tv_status, Color.parseColor("#ff666666"));
        }else{
            helper.setVisible(R.id.iv_icon,false);
            helper.setText(R.id.tv_status,"");
        }
    }
}
