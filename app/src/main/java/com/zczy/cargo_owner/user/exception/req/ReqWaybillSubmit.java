package com.zczy.cargo_owner.user.exception.req;

import com.zczy.comm.http.entity.BaseNewRequest;
import com.zczy.comm.http.entity.BaseRsp;
import com.zczy.comm.http.entity.ResultData;

/**
 * 功能描述:
 *
 * <AUTHOR>
 * @company 中储智运科技股份有限公司
 * @copyright （版权）中储南京智慧物流科技有限公司所有
 * @date 2019/3/5
 */
public class ReqWaybillSubmit extends BaseNewRequest<BaseRsp<ResultData>> {
    public ReqWaybillSubmit() {
        super("oms-app/order/consignor/exception/exceptionOrderSubmit");
    }

    private String id;
    private String childId;
    private String fileUrl; // 证明材料
    private String submitterType;
    private String remarks; // 备注
    private String uploadType; //上传途径 1.后台 2.前台 3.app

    public String getId() {
        return id;
    }

    public void setId(String id) {
        this.id = id;
    }

    public String getChildId() {
        return childId;
    }

    public void setChildId(String childId) {
        this.childId = childId;
    }

    public String getFileUrl() {
        return fileUrl;
    }

    public void setFileUrl(String fileUrl) {
        this.fileUrl = fileUrl;
    }

    public String getSubmitterType() {
        return submitterType;
    }

    public void setSubmitterType(String submitterType) {
        this.submitterType = submitterType;
    }

    public String getRemarks() {
        return remarks;
    }

    public void setRemarks(String remarks) {
        this.remarks = remarks;
    }

    public String getUploadType() {
        return uploadType;
    }

    public void setUploadType(String uploadType) {
        this.uploadType = uploadType;
    }
}

