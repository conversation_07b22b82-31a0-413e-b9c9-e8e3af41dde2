package com.zczy.cargo_owner.user.h5face;


import static com.zczy.cargo_owner.user.h5face.WBH5FaceVerifySDK.VIDEO_REQUEST;

import android.Manifest;
import android.app.Activity;
import android.app.AlertDialog;
import android.app.DownloadManager;
import android.content.BroadcastReceiver;
import android.content.Context;
import android.content.DialogInterface;
import android.content.Intent;
import android.content.IntentFilter;
import android.content.pm.PackageManager;
import android.content.pm.ResolveInfo;
import android.database.Cursor;
import android.graphics.Color;
import android.net.Uri;
import android.os.Build;
import android.os.Bundle;
import android.os.Environment;
import android.provider.Settings;
import android.text.TextUtils;
import android.view.KeyEvent;
import android.view.View;
import android.view.ViewGroup;
import android.webkit.JavascriptInterface;
import android.webkit.MimeTypeMap;
import android.webkit.PermissionRequest;
import android.webkit.URLUtil;
import android.widget.Toast;

import androidx.annotation.Nullable;
import androidx.core.app.ActivityCompat;
import androidx.core.content.ContextCompat;

import com.sfh.lib.event.RxBusEventManager;
import com.sfh.lib.ui.AbstractLifecycleActivity;
import com.tencent.smtt.export.external.interfaces.WebResourceRequest;
import com.tencent.smtt.sdk.ValueCallback;
import com.tencent.smtt.sdk.WebChromeClient;
import com.tencent.smtt.sdk.WebSettings;
import com.tencent.smtt.sdk.WebView;
import com.zczy.cargo_owner.user.setting.UrlObject;
import com.zczy.cargo_owner.user.setting.X5BaseJavascriptInterfaceImp;
import com.zczy.comm.permission.PermissionCallBack;
import com.zczy.comm.permission.PermissionUtil;
import com.zczy.comm.pluginserver.AMainServer;
import com.zczy.comm.ui.UtilStatus;
import com.zczy.comm.utils.JsonUtil;
import com.zczy.comm.utils.imageselector.ImageSelector;
import com.zczy.comm.widget.AppToolber;
import com.zczy.comm.x5.X5WebChromeClient2;
import com.zczy.comm.x5.X5WebTitle;
import com.zczy.comm.x5.X5WebView;
import com.zczy.comm.x5.X5WebViewClient;
import com.zczy.lib_zstatistics.sdk.utils.LogUtil;

import com.zczy.ui.R;
import com.zczy.zlog.ZLog;

import java.io.ByteArrayOutputStream;
import java.io.File;
import java.io.FileInputStream;
import java.io.FileNotFoundException;
import java.io.IOException;
import java.io.UnsupportedEncodingException;
import java.net.URLDecoder;
import java.util.List;

public class X5WebH5FaceActivity extends AbstractLifecycleActivity implements X5WebTitle, X5WebChromeClient2 {
    private static final int REQUEST_CODE_PERMISSION = 123;

    private ValueCallback<Uri> mUploadMessage;

    private ValueCallback<Uri[]> uploadMessage;

    private String downLoadUrl;

    private static final int PERMISSION_QUEST_TRTC_CAMERA_VERIFY = 12;//trtc模式的权限申请
    private static final int PERMISSION_QUEST_OLD_CAMERA_VERIFY = 11;//录制模式的权限申请

    private PermissionRequest request;
    private boolean belowApi21;// android 5.0以下系统
    private H5FaceWebChromeClient webViewClient;
    //实名scheme
    private final String SCHEMA_REAL = "zczy://hz/contractFace";

    private String curUrl = "";

    public static void startContentUI(Context context, String url) {
        Intent intent = new Intent();
        intent.putExtra("url", url);
        intent.setClass(context, X5WebH5FaceActivity.class);
        context.startActivity(intent);
    }


    protected AppToolber toolbar;

    protected X5WebView webView;

    protected String type;


    @Override
    protected void onCreate(@Nullable Bundle savedInstanceState) {

        super.onCreate(savedInstanceState);
        setContentView(R.layout.base_comm_web_activity);

        UtilStatus.initStatus(this, Color.WHITE);

        this.toolbar = findViewById(R.id.appToolber);
        this.toolbar.setVisibility(View.GONE);

        this.webView = findViewById(R.id.webLayout);
        final String url = getIntent().getStringExtra("url");
        type = getIntent().getStringExtra("type");

        if (TextUtils.isEmpty(url)) {
            return;
        }
        WebSettings webViewSettings = webView.getSettings();
        String baseAgent = webViewSettings.getUserAgentString();
        webViewSettings.setUserAgent(baseAgent + ";app/ANDROID" + ";kyc/h5face;kyc/2.0");
        webViewSettings.setBuiltInZoomControls(true);
        webViewSettings.setDisplayZoomControls(false);
        webView.addJavascriptInterface(jsUserInfoInterface, "android");
        this.webView.setChromeClient2(this);
        this.webView.loadUrl(url);
        webViewClient = new H5FaceWebChromeClient(X5WebH5FaceActivity.this);
        webView.setWebChromeClient(webViewClient);
        webView.setWebViewClient(new MyWebViewClient());
        WBH5FaceVerifySDK.getInstance().setWebViewSettings(webView, getApplicationContext());
        PermissionUtil.checkPermissions(X5WebH5FaceActivity.this, "中储智运需申请您的文件存储和相机权限。拒绝或取消授权不影响使用其他服务", new String[]{
                Manifest.permission.WRITE_EXTERNAL_STORAGE, Manifest.permission.CAMERA}, new PermissionCallBack() {
            @Override
            public void onHasPermission() {

            }
        });
    }

    @Override
    public void onReceivedTitle(WebView webView, String s) {

        this.toolbar.setTitle(s);
    }

    @Override
    public void openFileChooser(ValueCallback<Uri> uploadMsg) {

        if (this.mUploadMessage != null) {
            this.mUploadMessage.onReceiveValue(null);
            this.mUploadMessage = null;
        }
        this.mUploadMessage = uploadMsg;
        this.openSeletcImage();
    }

    @Override
    public boolean onShowFileChooser(WebView mWebView, ValueCallback<Uri[]> filePathCallback, WebChromeClient.FileChooserParams fileChooserParams) {

        if (this.uploadMessage != null) {
            this.uploadMessage.onReceiveValue(null);
            this.uploadMessage = null;
        }
        this.uploadMessage = filePathCallback;
        this.openSeletcImage();
        return true;
    }

    @Override
    protected void onDestroy() {
        ViewGroup view = (ViewGroup) getWindow().getDecorView();
        view.removeAllViews();
        if (this.webView != null) {
            this.webView.destroy();
        }
        super.onDestroy();
    }

    @Override
    protected void onNewIntent(Intent intent) {
        super.onNewIntent(intent);
        setIntent(intent);
        processExtraData();
    }

    /***
     * 取消选择图库
     */
    public void cancleSeletcFile() {

        if (uploadMessage != null) {
            uploadMessage.onReceiveValue(null);
        }

        if (mUploadMessage != null) {
            mUploadMessage.onReceiveValue(null);
        }
    }

    /***
     * 上传文件
     * @param path
     */
    public void uploadFile(String path) {

        if (Build.VERSION.SDK_INT >= Build.VERSION_CODES.LOLLIPOP) {

            if (uploadMessage == null) {
                return;
            }
            Uri[] result = new Uri[]{Uri.fromFile(new File(path))};
            uploadMessage.onReceiveValue(result);
            uploadMessage = null;

        } else {
            if (null == mUploadMessage) {
                return;
            }
            Uri result = Uri.fromFile(new File(path));
            mUploadMessage.onReceiveValue(result);
            mUploadMessage = null;
        }
    }


    /**
     * 监听Back键按下事件,方法2:
     * 注意:
     * 返回值表示:是否能完全处理该事件
     * 在此处返回false,所以会继续传播该事件.
     * 在具体项目中此处的返回值视情况而定.
     */
    @Override
    public boolean onKeyDown(int keyCode, KeyEvent event) {
        if ((keyCode == KeyEvent.KEYCODE_BACK)) {
            if (null != webView) {
                if (webView.canGoBack()) {
                    webView.goBack();
                } else {
                    finish();
                }
            }
            return false;
        } else {
            return super.onKeyDown(keyCode, event);
        }
    }

    /***
     * 打开图库
     */
    public void openSeletcImage() {
        Intent i = new Intent(Intent.ACTION_GET_CONTENT);
        i.addCategory(Intent.CATEGORY_OPENABLE);
        i.setType("image/*");
        startActivityForResult(Intent.createChooser(i, "test"), 0);
    }


    @Override
    protected void onActivityResult(int requestCode, int resultCode, Intent data) {
        super.onActivityResult(requestCode, resultCode, data);
        if (resultCode == RESULT_OK) {
            switch (requestCode) {
                case 0:
                    if (null != mUploadMessage) {
                        Uri result = data == null || resultCode != RESULT_OK ? null
                                : data.getData();
                        mUploadMessage.onReceiveValue(result);
                        mUploadMessage = null;
                    }
                    if (null != uploadMessage) {
                        Uri result = data == null || resultCode != RESULT_OK ? null
                                : data.getData();
                        uploadMessage.onReceiveValue(new Uri[]{result});
                        uploadMessage = null;
                    }
                case 10086:
                    List<String> imageList = ImageSelector.obtainPathResult(data);
                    if (imageList != null && imageList.size() > 0) {
                        String picUrl = imageList.get(0);
                        if (TextUtils.isEmpty(picUrl)) {
                            showDialogToast("文件损坏，请重新选择文件");
                            return;
                        }
                        sendToJsFun(picUrl);
                    }
                    break;
                default:
                    break;
            }
        } else if (resultCode == RESULT_CANCELED) {
            if (null != mUploadMessage) {
                mUploadMessage.onReceiveValue(null);
                mUploadMessage = null;
            }
        } else if (requestCode == VIDEO_REQUEST) {//收到录制模式调用系统相机录制完成视频的结果
            if (WBH5FaceVerifySDK.getInstance().receiveH5FaceVerifyResult(requestCode, resultCode, data)) {
                return;
            }
        } else if (requestCode == PERMISSION_QUEST_TRTC_CAMERA_VERIFY) {
            requestCameraPermission(true, belowApi21);
        } else if (requestCode == PERMISSION_QUEST_OLD_CAMERA_VERIFY) {
            requestCameraPermission(false, belowApi21);
        }
    }

    private void sendToJsFun(String picUrl) {
//        File file = new File(picUrl);
//        String photo = Base64.encodeToString(file2Byte(file), Base64.DEFAULT).replaceAll("\r|\n", "");
//        ImageUpload upload = new ImageUpload();
//        upload.filePath = photo;
//        Gson gson = new Gson();
//        String uploadJson = gson.toJson(upload);
//        //webView.loadUrl("javascript:getImageFunc(" + uploadJson + ")");
//        String path = String.format("{filePath:'%s'}", photo);
//        String s = "javascript:getImageFunc(" + path + ")";
//        webView.evaluateJavascript(s, s1 -> {
//
//        });

    }

    public static byte[] file2Byte(File tradeFile) {
        byte[] buffer = null;
        try {
            FileInputStream fis = new FileInputStream(tradeFile);
            ByteArrayOutputStream bos = new ByteArrayOutputStream();
            byte[] b = new byte[1024];
            int n;
            while ((n = fis.read(b)) != -1) {
                bos.write(b, 0, n);
            }
            fis.close();
            bos.close();
            buffer = bos.toByteArray();
        } catch (FileNotFoundException e) {
            e.printStackTrace();
        } catch (IOException e) {
            e.printStackTrace();
        }
        return buffer;
    }

    private void startDownload(String json) {
        UrlObject urlObject = JsonUtil.toJsonObject(json, UrlObject.class);
        String url = urlObject.getFileUrl();
        DownloadManager.Request request = new DownloadManager.Request(Uri.parse(url));
        request.setAllowedNetworkTypes(DownloadManager.Request.NETWORK_WIFI | DownloadManager.Request.NETWORK_MOBILE);
        request.setTitle("文件下载");
        request.setDescription("下载文件中");
        // 设置下载的保存路径和文件名
        String fileName = URLUtil.guessFileName(url, null, MimeTypeMap.getFileExtensionFromUrl(url));
        request.setDestinationInExternalPublicDir(Environment.DIRECTORY_DOWNLOADS, fileName);

        DownloadManager downloadManager = (DownloadManager) getSystemService(Context.DOWNLOAD_SERVICE);
        long downloadId = downloadManager.enqueue(request);

        // 显示正在下载文件的提示信息
        Toast.makeText(this, "正在下载文件", Toast.LENGTH_SHORT).show();

        // 创建一个广播接收器来监听下载完成的通知
        BroadcastReceiver downloadCompleteReceiver = new BroadcastReceiver() {
            @Override
            public void onReceive(Context context, Intent intent) {
                // 获取下载完成的通知ID
                long receivedDownloadId = intent.getLongExtra(DownloadManager.EXTRA_DOWNLOAD_ID, -1);

                if (downloadId == receivedDownloadId) {
                    // 下载完成，处理下载结果
                    DownloadManager.Query query = new DownloadManager.Query();
                    query.setFilterById(downloadId);
                    Cursor cursor = downloadManager.query(query);

                    if (cursor.moveToFirst()) {
                        int statusIndex = cursor.getColumnIndex(DownloadManager.COLUMN_STATUS);
                        int status = cursor.getInt(statusIndex);

                        if (status == DownloadManager.STATUS_SUCCESSFUL) {
                            // 下载成功，显示文件下载成功的提示信息
                            Toast.makeText(context, "文件下载成功", Toast.LENGTH_SHORT).show();
                        } else {
                            // 下载失败，显示文件下载失败的提示信息
                            Toast.makeText(context, "文件下载失败", Toast.LENGTH_SHORT).show();
                        }
                    }

                    cursor.close();

                    // 取消注册广播接收器
                    context.unregisterReceiver(this);
                }
            }
        };

        // 注册广播接收器来监听下载完成的通知
        registerReceiver(downloadCompleteReceiver, new IntentFilter(DownloadManager.ACTION_DOWNLOAD_COMPLETE));
    }

    private void checkAndDownloadFile(String url) {
        downLoadUrl = url;
        requestPermission();
    }

    private void requestPermission() {

        runOnUiThread(new Runnable() {
            @Override
            public void run() {
                PermissionUtil.checkPermissions(X5WebH5FaceActivity.this, "中储智运需申请您的文件存储权限。拒绝或取消授权不影响使用其他服务", new String[]{
                        Manifest.permission.WRITE_EXTERNAL_STORAGE}, new PermissionCallBack() {
                    @Override
                    public void onHasPermission() {
                        // 权限已授予，可以执行文件下载
                        startDownload(downLoadUrl);
                    }

                });
            }
        });
    }

    /**
     * 针对trtc录制模式，申请相机权限
     */
    public void requestCameraPermission(boolean trtc, boolean belowApi21) {
        this.belowApi21 = belowApi21;
        if (checkSdkPermission(Manifest.permission.CAMERA) != PackageManager.PERMISSION_GRANTED) {

            if (Build.VERSION.SDK_INT >= Build.VERSION_CODES.M) {  //23+的情况
                if (ActivityCompat.shouldShowRequestPermissionRationale(X5WebH5FaceActivity.this, Manifest.permission.CAMERA)) {
                    //用户之前拒绝过，这里返回true

                    if (trtc) {
                        ActivityCompat.requestPermissions(this,
                                new String[]{Manifest.permission.CAMERA},
                                PERMISSION_QUEST_TRTC_CAMERA_VERIFY);
                    } else {
                        ActivityCompat.requestPermissions(this,
                                new String[]{Manifest.permission.CAMERA},
                                PERMISSION_QUEST_OLD_CAMERA_VERIFY);
                    }
                } else {

                    if (trtc) {
                        ActivityCompat.requestPermissions(this,
                                new String[]{Manifest.permission.CAMERA},
                                PERMISSION_QUEST_TRTC_CAMERA_VERIFY);
                    } else {
                        ActivityCompat.requestPermissions(this,
                                new String[]{Manifest.permission.CAMERA},
                                PERMISSION_QUEST_OLD_CAMERA_VERIFY);
                    }

                }
            } else {
                if (trtc) {
                    //23以下没法系统弹窗动态申请权限，只能用户跳转设置页面，自己打开
                    openAppDetail(PERMISSION_QUEST_TRTC_CAMERA_VERIFY);
                } else {
                    //23以下没法系统弹窗动态申请权限，只能用户跳转设置页面，自己打开
                    openAppDetail(PERMISSION_QUEST_OLD_CAMERA_VERIFY);
                }
            }

        } else {

            if (trtc) {
                webViewClient.enterTrtcFaceVerify();
            } else {
                webViewClient.enterOldModeFaceVerify(belowApi21);
            }

        }
    }


    @Override
    public void onRequestPermissionsResult(int requestCode, String[] permissions,
                                           int[] grantResults) {
        super.onRequestPermissionsResult(requestCode, permissions, grantResults);
        switch (requestCode) {
            case PERMISSION_QUEST_TRTC_CAMERA_VERIFY: // trtc 模式，新模式。
                if (grantResults.length > 0) {
                    if (grantResults[0] == PackageManager.PERMISSION_GRANTED) {
                        webViewClient.enterTrtcFaceVerify();
                    } else if (grantResults[0] == PackageManager.PERMISSION_DENIED
                            && ActivityCompat.shouldShowRequestPermissionRationale(this, Manifest.permission.CAMERA) == false) {
                        openAppDetail(PERMISSION_QUEST_TRTC_CAMERA_VERIFY);
                    } else {
                        //权限被拒绝
                        askPermissionError();
                    }
                }
                break;
            case PERMISSION_QUEST_OLD_CAMERA_VERIFY://录制模式，老模式。
                if (grantResults.length > 0) {
                    if (grantResults[0] == PackageManager.PERMISSION_GRANTED) {
                        webViewClient.enterOldModeFaceVerify(belowApi21);
                    } else {
                        if (ActivityCompat.shouldShowRequestPermissionRationale(this, Manifest.permission.CAMERA) == false) {
                            //权限被拒绝
                            openAppDetail(PERMISSION_QUEST_OLD_CAMERA_VERIFY);
                        } else {
                            askPermissionError();
                        }
                    }
                }
                break;
        }
    }

    private void openAppDetail(int requestCode) {
        showWarningDialog(requestCode);
    }


    private void enterSettingActivity(int requestCode) {
        //部分插件化框架中用Activity.getPackageName拿到的不一定是宿主的包名，所以改用applicationContext获取
        String packageName = getApplicationContext().getPackageName();
        Uri uri = Uri.fromParts("package", packageName, null);
        Intent intent = new Intent(Settings.ACTION_APPLICATION_DETAILS_SETTINGS, uri);
        ResolveInfo resolveInfo = getPackageManager().resolveActivity(intent, 0);
        if (resolveInfo != null) {
            startActivityForResult(intent, requestCode);
        }
    }


    private void askPermissionError() {
        Toast.makeText(X5WebH5FaceActivity.this, "用户拒绝了权限,5秒后按钮可再点击", Toast.LENGTH_SHORT).show();
        WBH5FaceVerifySDK.getInstance().resetReceive();
    }


    AlertDialog dialog;

    private void showWarningDialog(final int requestCode) {
        dialog = new AlertDialog.Builder(this)
                .setTitle("权限申请提示")
                .setMessage("请前往设置->应用->权限中打开相关权限，否则功能无法正常运行！")
                .setPositiveButton("确定", new DialogInterface.OnClickListener() {
                    @Override
                    public void onClick(DialogInterface dialogInterface, int which) {
                        // 一般情况下如果用户不授权的话，功能是无法运行的，做退出处理,合作方自己根据自身产品决定是退出还是停留
                        if (dialog != null && dialog.isShowing()) {
                            dialog.dismiss();
                        }
                        dialog = null;
                        enterSettingActivity(requestCode);

                    }
                }).setNegativeButton("取消", new DialogInterface.OnClickListener() {
                    @Override
                    public void onClick(DialogInterface dialogInterface, int which) {
                        if (dialog != null && dialog.isShowing()) {
                            dialog.dismiss();
                        }
                        dialog = null;
                        WBH5FaceVerifySDK.getInstance().resetReceive();

                    }
                }).setCancelable(false).show();
    }

    private int checkSdkPermission(String permission) {
        if (Build.VERSION.SDK_INT >= Build.VERSION_CODES.M) {
            int permissionResult = ContextCompat.checkSelfPermission(this, permission);

            return permissionResult;
        } else {
            int permissionResult = getPackageManager().checkPermission(permission, getPackageName());

            return permissionResult;
        }
    }

    //处理首次进入和支付宝返回场景逻辑
    private void processExtraData() {
        Intent intent = this.getIntent();
        Uri uri = intent.getData();
        //支付宝返回
        if (uri != null) {
            String callbackUrl = uri.getQueryParameter("realnameUrl");
            LogUtil.e("$callbackUrl");
            if (!TextUtils.isEmpty(callbackUrl)) {
                if (callbackUrl.startsWith("http") == true || callbackUrl.startsWith("https") == true) {
                    try {
                        webView.loadUrl(URLDecoder.decode(callbackUrl, "utf-8"));
                    } catch (UnsupportedEncodingException e) {
                        e.printStackTrace();
                    }
                } else {
                    //api自己查询结果
                    Toast.makeText(this, "认证完成请调用接口查询结果", Toast.LENGTH_LONG).show();
                    finish();
                }
            }
        } else {
            //正常加载url
            String url = intent.getStringExtra("url");
            LogUtil.e(url);
            //alipays开头的 唤起支付宝
            if (url.startsWith("alipays")) {
                try {
                    Intent intent2 = new Intent(Intent.ACTION_VIEW, Uri.parse(url));
                    startActivity(intent2);
                    return;
                } catch (Exception e) {
                }
            }
            //正常加载流程
            curUrl = url;
            if (url != null) {
                webView.loadUrl(url);
            }
        }
    }

    //拦截处理地址逻辑
    private boolean processUrlLoading(WebView view, String url) {
        if (url == null) {
            return false;
        }

        Uri uri = Uri.parse(url);
        LogUtil.e("要加载的地址:" + uri.getScheme() + " " + url + " ");

        if (TextUtils.equals(uri.getScheme(), "http") || TextUtils.equals(uri.getScheme(), "https")) {
            view.loadUrl(url);
            return true;
        } else if (url.startsWith(SCHEMA_REAL)) {
            //esign://demo/realBack?status=true&authFlowId=OF-2ecd9c5e3408000d&lang=zh-CN

            //实名结果
            boolean status = uri.getBooleanQueryParameter("status", false);
            if (status) {
                //认证成功返回
                Toast.makeText(this, "认证成功", Toast.LENGTH_LONG).show();
                finish();
            }

            return true;
        } else if (TextUtils.equals(uri.getScheme(), "alipays")) {
            // 跳转到支付宝刷脸
            // alipays://platformapi/startapp?appId=20000067&pd=NO&url=https%3A%2F%2Fzmcustprod.zmxy.com.cn%2Fcertify%2Fbegin.htm%3Ftoken%3DZM201811133000000050500431389414
            try {
                Intent intent = new Intent(Intent.ACTION_VIEW, Uri.parse(url));
                startActivity(intent);
                return true;
            } catch (Exception e) {
                return false;
            }
        } else {
            return false;
        }
    }

    private X5BaseJavascriptInterfaceImp jsUserInfoInterface = new X5BaseJavascriptInterfaceImp(this) {
        @JavascriptInterface
        public void downLoadFile(String url) {
            if (!TextUtils.isEmpty(url)) {
                // 处理下载请求
                checkAndDownloadFile(url);
            }
        }

        @JavascriptInterface
        public void agreeChange() {
            signContract();
        }

        @JavascriptInterface
        public void againChange() {
            signContract();
        }

        long lastTime = -1;

        @JavascriptInterface
        public void signContract() {
            ZLog.i("signContract", "signContract web 回调-----");
            long nowTime = System.currentTimeMillis();
            if (nowTime - lastTime > 500) {
                lastTime = nowTime;
                finish();
            }
        }

        @JavascriptInterface
        public void refuseChange() {
            finish();
        }
    };

    public class MyWebViewClient extends X5WebViewClient {

        @Override
        public boolean shouldOverrideUrlLoading(WebView view, String url) {
            return processUrlLoading(view, url);
        }

        @Override
        public boolean shouldOverrideUrlLoading(WebView view, WebResourceRequest request) {
            String url = request.getUrl().toString();
            return processUrlLoading(view, url);
        }
    }
}
