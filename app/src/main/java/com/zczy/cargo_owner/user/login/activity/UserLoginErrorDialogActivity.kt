package com.zczy.cargo_owner.user.login.activity

import android.content.Context
import android.content.Intent
import android.os.Bundle
import android.view.View
import android.widget.TextView
import com.sfh.lib.mvvm.service.BaseViewModel

import com.sfh.lib.ui.AbstractLifecycleActivity
import com.zczy.cargo_owner.R

/**
 * author : Ssp
 * date   : 2019/9/4 9:53
 * desc   : 登录用户不存在提示框
 */
class UserLoginErrorDialogActivity : AbstractLifecycleActivity<BaseViewModel>(), View.OnClickListener {

    private val tvCancle by lazy { findViewById<TextView>(R.id.tv_cancel) }
    private val tvOK by lazy { findViewById<TextView>(R.id.tv_ok) }
    private val tvContent by lazy { findViewById<TextView>(R.id.tv_content) }

    companion object {
        @JvmStatic
        fun startContentUI(context: Context, msg: String?, phone: String) {
            val intent = Intent(context, UserLoginErrorDialogActivity::class.java)
            intent.putExtra("msg", msg)
            intent.putExtra("phone", phone)
            context.startActivity(intent)
        }
    }

    override fun onCreate(savedInstanceState: Bundle?) {
        super.onCreate(savedInstanceState)
        setContentView(R.layout.user_login_error_dialog_activity)
        val msg = intent.getStringExtra("msg")
        tvContent.text = msg ?: ""
        initView()
    }

    private fun initView() {
        tvCancle.setOnClickListener(this)
        tvOK.setOnClickListener(this)
    }

    override fun onClick(v: View) {
        when (v.id) {
            R.id.tv_cancel -> {
                finish()
            }

            R.id.tv_ok -> {
                //注册
                RegisterStep1Activity.start(this, intent.getStringExtra("phone"))
                finish()
            }
        }
    }

}
