package com.zczy.cargo_owner.user.login.activity;

import android.content.Context;
import android.content.Intent;
import android.os.Bundle;
import androidx.annotation.Nullable;
import android.view.View;

import com.sfh.lib.mvvm.annotation.LiveDataMatch;
//import com.wbtech.ums.UmsAgent;
import com.zczy.cargo_owner.R;
import com.zczy.cargo_owner.home.HomeActivity;
import com.zczy.cargo_owner.user.login.mode.LoginAccountModel;
import com.zczy.cargo_owner.user.login.request.ReqLogin;
import com.zczy.comm.data.entity.ELogin;
import com.zczy.comm.ui.BaseActivity;

/**
 * 注册成功
 */
public class RegisterSuccActivity extends BaseActivity<LoginAccountModel> {

    public static void start(Context context, String phone, String password) {
        Intent intent = new Intent(context, RegisterSuccActivity.class);
        intent.putExtra("phone", phone);
        intent.putExtra("password", password);
        context.startActivity(intent);
    }

    @Override
    protected int getLayout() {
        return R.layout.user_register_success_activity;
    }

    @Override
    protected void bindView(@Nullable Bundle bundle) {

        String phone = getIntent().getStringExtra("phone");
        String password = getIntent().getStringExtra("password");

        findViewById(R.id.btNext).setOnClickListener(new View.OnClickListener() {
            @Override
            public void onClick(View v) {
                //登录
                ReqLogin.AccountBuilder req = new ReqLogin.AccountBuilder();
                req.setLoginName(phone);
                req.setLoginPassword(password);
                getViewModel(LoginAccountModel.class).login(req.bulder());
            }
        });
    }

    @Override
    protected void initData() {

    }

    @LiveDataMatch(tag = "登录成功")
    public void onLoginSuccess(ELogin login) {

        if (login != null) {
//            UmsAgent.onEvent(this, "RegisterSuccessCarrier", login.getUserId());
        }
        HomeActivity.start(this);
        this.finish();
    }

//    public void onClickLogin(View v) {
//
//        UmsAgent.onEvent(this, "registerHz","");
//        LoginAccountActivity.start(this, getIntent().getStringExtra("phone"));
//        finish();
//    }
}
