package com.zczy.cargo_owner.user.login.request;

import com.zczy.comm.http.entity.BaseNewRequest;
import com.zczy.comm.http.entity.BaseRsp;
import com.zczy.comm.http.entity.ResultData;

import java.util.Map;

/** 
 *  desc: 验证吗
 *  user: ssp
 *  time: 2024/7/30 17:14
 */
public class ReqSendCodeV1 extends BaseNewRequest<BaseRsp<ResultData>> {

    public ReqSendCodeV1(){
        super ("mms-app/mms/verifyCode/sendVerifyCode");
    }

    @Override
    public void buildHeader(Map<String, String> header) {
        super.buildHeader(header);
        header.remove("ssoTokenId");
    }

    /*** 图片验证码*/
    String imageIdentifyCode;
    /*** 手机号*/
    String mobile;

    /*** 验证码类型(1：短信验证码 2：语音验证码)*/
    String type;

    /**
     * 模块类型
     * moduleType=1：账户注册
     * moduleType=2：修改服务密码
     * moduleType=3：忘记密码修改密码
     * moduleType=4：修改手机号
     * moduleType=5：重置支付密码
     * moduleType=6：登录密码修改
     * moduleType=7：HUE绑定设备
     * moduleType=8：HUE手机动态效验
     * moduleType=9：验证码登录
     * moduleType=10：设置支付密码
     * moduleType=11：找回密码
     * moduleType=12：设备验证
     */
    String moduleType;

    /**
     * 平台类型
     * plateFormType=0：非平台会员
     * plateFormType=1：汽运会员
     * plateFormType=2：加盟运力会员
     * plateFormType=3：所有平台会员
     * plateFormType=-1：所有手机号码（包含平台和非平台会员）
     */
    String plateFormType = "1";

    /*** 客户端类型 userClientType=3：android*/
    String userClientType = "3";

    public void setImageCode(String imageCode) {
        this.imageIdentifyCode = imageCode;
    }

    public void setMobile(String mobile) {
        this.mobile = mobile;
    }

    public void setType(String type) {
        this.type = type;
    }

    public void setModuleType(String moduleType) {
        this.moduleType = moduleType;
    }

    public String getType() {
        return type;
    }

    public String getMobile() {
        return mobile;
    }
    public void setPlateFormType(String plateFormType) {

        this.plateFormType = plateFormType;
    }

    public String getImageIdentifyCode() {
        return imageIdentifyCode;
    }

    public String getModuleType() {
        return moduleType;
    }

    public String getPlateFormType() {
        return plateFormType;
    }

    public String getUserClientType() {
        return userClientType;
    }
}
