package com.zczy.cargo_owner.user.assure;

import android.app.Activity;
import android.content.Intent;
import android.graphics.Color;
import android.os.Bundle;
import androidx.annotation.Nullable;
import androidx.fragment.app.Fragment;

import com.flyco.tablayout.CommonTabLayout;
import com.flyco.tablayout.listener.CustomTabEntity;
import com.sfh.lib.ui.AbstractLifecycleActivity;
import com.zczy.cargo_owner.R;
import com.zczy.comm.ui.UtilStatus;
import com.zczy.comm.widget.tablayout.CommonTabEntity;

import java.util.ArrayList;

/***
 * 货物保障服务列表
 */
public class AssureListActivity extends AbstractLifecycleActivity<AssureModel> {

    private AssureAdapter adapter = new AssureAdapter();
    private CommonTabLayout commonTabLayout;
    private ArrayList<Fragment> fragments = new ArrayList<>();
    public static void start(Activity context) {
        Intent intent = new Intent(context, AssureListActivity.class);
        context.startActivity(intent);
    }

    @Override
    protected void onCreate(@Nullable Bundle savedInstanceState) {
        super.onCreate(savedInstanceState);
        setContentView(R.layout.user_assure_list_activity);
        UtilStatus.initStatus(this, Color.WHITE);
        initCommonTabLayout();
    }

    private void initCommonTabLayout() {

        commonTabLayout = findViewById(com.zczy.cargo_owner.order.R.id.common_tab_layout);
        ArrayList<CustomTabEntity> tabEntities = new ArrayList<>();
        CommonTabEntity tabEntity1 = new CommonTabEntity();
        tabEntity1.title = "货主自主购买";
        CommonTabEntity tabEntity2 = new CommonTabEntity();
        tabEntity2.title = "要求承运方购买";
        tabEntities.add(tabEntity1);
        tabEntities.add(tabEntity2);
        AssureListHuozhuFragment fragment1 = (AssureListHuozhuFragment) Fragment.instantiate(this, AssureListHuozhuFragment.class.getName());
        fragments.add(fragment1);
        AssureListChenYunFanFragment fragment2 = (AssureListChenYunFanFragment) Fragment.instantiate(this, AssureListChenYunFanFragment.class.getName());
        fragments.add(fragment2);
        commonTabLayout.setTabData(tabEntities, this, com.zczy.cargo_owner.order.R.id.frame_layout, fragments);
        commonTabLayout.setCurrentTab(0);
    }

}
