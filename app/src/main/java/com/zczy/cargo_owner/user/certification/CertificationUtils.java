package com.zczy.cargo_owner.user.certification;

import android.content.Context;

import com.zczy.comm.CommServer;
import com.zczy.comm.data.IUserServer;
import com.zczy.comm.data.entity.ELogin;

public class CertificationUtils {

    public static void hzCertification(Context context) {
        IUserServer userServer = CommServer.getUserServer();
        ELogin login = userServer.getLogin();
        if (login != null) {
            switch (login.getExamineType()) {
                case "3":
                    //未提交认证
                    HzCertificationStartActivity.start(context);
                    break;
                case "0":
                    //审核中
                    HzCertificationAuditActivity.start(context);
                    break;
                case "2":
                    //审核未通过
                    HzCertificationRejectActivity.start(context);
                    break;
                case "1":
                    //审核通过
                    HzCertificationDeatilsActivity.start(context);
                    break;
            }
        }
    }

}
