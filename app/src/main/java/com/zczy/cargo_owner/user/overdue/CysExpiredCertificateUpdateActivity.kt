package com.zczy.cargo_owner.user.overdue

import android.app.Activity
import android.content.Context
import android.content.Intent
import android.os.Bundle
import android.text.TextUtils
import android.view.View
import com.sfh.lib.event.RxBusEventManager
import com.sfh.lib.mvvm.annotation.LiveDataMatch
import com.zczy.cargo_owner.R
import com.zczy.cargo_owner.user.certification.req.PicUrlBean
import com.zczy.cargo_owner.user.overdue.model.CysExpiredCertificateManagementModel
import com.zczy.cargo_owner.user.overdue.req.ReqQueryUserExpireLicenseDetail
import com.zczy.cargo_owner.user.overdue.req.RspQueryUserExpireLicenseB
import com.zczy.cargo_owner.user.overdue.req.RspQueryUserExpireLicenseDetail
import com.zczy.cargo_owner.user.overdue.req.RxBusUpdatePicSuccess
import com.zczy.comm.data.entity.EImage
import com.zczy.comm.ui.BaseActivity
import com.zczy.comm.utils.ex.setVisible
import kotlinx.android.synthetic.main.cys_expired_certufucate_update_activity.*

/**
 * 功能描述: 证件过期上传页
 * <AUTHOR>
 * @date 2022/10/14-15:42
 */

class CysExpiredCertificateUpdateActivity : BaseActivity<CysExpiredCertificateManagementModel>() {

    private val rspQueryUserExpireLicenseB by lazy { intent.getParcelableExtra<RspQueryUserExpireLicenseB>(RSP_QUERY_USER_EXPIRE_LICENSE_B) ?: null }
    private var licenseType: String? = null

    companion object {

        private const val REQUEST_CODE_V1 = 0x99
        private const val REQUEST_CODE_V3 = 0x96
        private const val REQUEST_CODE_V2 = 0x98
        private const val REQUEST_CODE_DELETE_V1 = 0x89
        private const val REQUEST_CODE_DELETE_V2 = 0x97
        private const val REQUEST_CODE_DELETE_V3 = 0x86
        private const val RSP_QUERY_USER_EXPIRE_LICENSE_B = "rspQueryUserExpireLicenseB"

        @JvmStatic
        fun jumpPage(
            context: Context?,
            rspQueryUserExpireLicenseB: RspQueryUserExpireLicenseB? = null,
        ) {
            val intent = Intent(context, CysExpiredCertificateUpdateActivity::class.java)
            intent.putExtra(RSP_QUERY_USER_EXPIRE_LICENSE_B, rspQueryUserExpireLicenseB)
            context?.startActivity(intent)
        }
    }

    override fun getLayout(): Int {
        return R.layout.cys_expired_certufucate_update_activity
    }

    override fun bindView(bundle: Bundle?) {
        //1.初始化view基本信息
        comCertificationUploadImgView1
            .setCanDelete(false)
            .setViewType(viewType = comCertificationUploadImgView1.id.toString())
            .setImgRequestCode(requestCode = REQUEST_CODE_V1)
            .setImgRequestCode2(requestCode = REQUEST_CODE_V2)
            .setImgDeleteRequestCode(requestCode = REQUEST_CODE_DELETE_V1)
            .setImgDeleteRequestCode2(requestCode = REQUEST_CODE_DELETE_V2)
            .setStencilImg(R.drawable.enterprise_business_license_modelpic_new)
            .setTvTitle1(rspQueryUserExpireLicenseB?.licenseName ?: "")
            .setTvTitle2("")

        // 设置法人身份证照片初始化
        comCertificationUploadImgView2
            .setCanDelete(true)
            .setViewType(viewType = comCertificationUploadImgView2.id.toString())
            .setImgRequestCode(requestCode = REQUEST_CODE_V3)
            .setImgDeleteRequestCode(requestCode = REQUEST_CODE_DELETE_V3)
            .setStencilImg(R.drawable.certification_idcard_model)
            .setTvTitle1("法人身份证")
            .setDescV1("若您的法人信息存在变动请更新最新的法人身份证")
        //2.设置view点击事件
        bindClickEvent(tvSubmit)
        bindClickEvent(ivClose)
    }

    override fun initData() {
        rspQueryUserExpireLicenseB?.plateNumber?.let {
            tvName.text = it
        }
        tvName.setVisible(!rspQueryUserExpireLicenseB?.plateNumber.isNullOrEmpty())
        viewModel?.queryUserExpireLicenseDetail(
            req = ReqQueryUserExpireLicenseDetail(
                targetId = rspQueryUserExpireLicenseB?.vehicleId,
                licenseType = rspQueryUserExpireLicenseB?.licenseType,
            )
        )
    }

    override fun onActivityResult(requestCode: Int, resultCode: Int, data: Intent?) {
        super.onActivityResult(requestCode, resultCode, data)
        if (resultCode == Activity.RESULT_OK) {
            when (requestCode) {
                REQUEST_CODE_V1 -> {
                    comCertificationUploadImgView1
                        .onActivityResult(requestCode, resultCode, data)
                        .setUrlType("1")
                    viewModel?.upLoadPic(
                        file = comCertificationUploadImgView1.getEImg().path,
                        viewType = comCertificationUploadImgView1.getViewType()
                    )
                }

                REQUEST_CODE_V3 -> {
                    comCertificationUploadImgView2
                        .onActivityResult(requestCode, resultCode, data)
                        .setUrlType("1")
                    viewModel?.upLoadPic(
                        file = comCertificationUploadImgView2.getEImg().path,
                        viewType = comCertificationUploadImgView2.getViewType()
                    )
                }

                REQUEST_CODE_V2 -> {
                    comCertificationUploadImgView1
                        .onActivityResult2(requestCode, resultCode, data)
                        .setUrlType("2")
                    viewModel?.upLoadPic(
                        file = comCertificationUploadImgView1.getEImg2().path,
                        viewType = comCertificationUploadImgView1.getViewType()
                    )
                }

                REQUEST_CODE_DELETE_V1 -> {
                    comCertificationUploadImgView1
                        .setStencilImg(useImgId = true)
                        .setViewBg2()
                }

                REQUEST_CODE_DELETE_V2 -> {
                    comCertificationUploadImgView1
                        .setStencilImg2(useImgId = true)
                        .setViewBg2()
                }

                REQUEST_CODE_DELETE_V3 -> {
                    comCertificationUploadImgView2
                        .setStencilImg(useImgId = true)
                        .setViewBg2()
                }
            }
        }
    }

    override fun onSingleClick(v: View) {
        super.onSingleClick(v)
        when (v.id) {
            R.id.tvSubmit -> {
                when (licenseType) {
                    "100001" -> {
                        //身份证
                        val url = comCertificationUploadImgView1.getUrl()
                        val url2 = comCertificationUploadImgView1.getUrl2()
                        val sb = StringBuilder()
                        sb.append(url).append(",").append(url2)
                        viewModel?.uploadExpireDateLicense(
                            picUrlList = mutableListOf(
                                PicUrlBean(
                                    type = licenseType,
                                    picUrl = sb.toString()
                                )
                            )
                        )
                    }

                    else -> {
                        val url = comCertificationUploadImgView1.getUrl()
                        val url3 = comCertificationUploadImgView2.getUrl()
                        val sb = StringBuilder()
                        if (!TextUtils.isEmpty(url3)){
                            sb.append(url).append(",").append(url3)
                        }else{
                            sb.append(url)
                        }
                        viewModel?.uploadExpireDateLicense(
                            picUrlList = mutableListOf(
                                PicUrlBean(
                                    type = licenseType,
                                    picUrl = sb.toString()
                                )
                            )
                        )
                    }
                }
            }

            R.id.ivClose -> {
                clRemind.setVisible(false)
            }
        }
    }

    // 图片上传成功回调
    @LiveDataMatch
    open fun upLoadPicSuccess(viewType: String, picUrl: String) {
        when (viewType) {
            comCertificationUploadImgView1.getViewType() -> {
                //左边图片
                when (comCertificationUploadImgView1.getUrlType()) {
                    "1" -> {
                        comCertificationUploadImgView1.setUrl(picUrl)
                    }

                    else -> {
                        comCertificationUploadImgView1.setUrl2(picUrl)
                    }
                }
            }

            comCertificationUploadImgView2.getViewType() -> {
                //法人身份证图片
                comCertificationUploadImgView2.setUrl(picUrl)
            }
        }
    }

    @LiveDataMatch()
    open fun onQueryUserExpireLicenseDetailSuccess(data: RspQueryUserExpireLicenseDetail?) {
        data?.let {
            licenseType = it.licenseType
            //0：待上传；1：审核中;  3：审核失败
            when (it.licenseState) {
                "0" -> {
                    // 待完善
                    comCertificationUploadImgView1
                        .setCanDelete(true)
                    comCertificationUploadImgView2
                        .setCanDelete(true)
                        .setImgRes(EImage(imageId = it.licenseUrl1 ?: ""))
                        .setImgResList()
                        .setUrl(it.licenseUrl1 ?: "")
                        .setVisible(TextUtils.equals("0", it.isEntrustRegister))
                }

                "1" -> {
                    //待审核
                    comCertificationUploadImgView1
                        .setCanDelete(false)
                        .setImgRes(EImage(imageId = it.licenseUrl ?: ""))
                        .setImgResList()
                        .setUrl((it.licenseUrl ?: ""))
                    comCertificationUploadImgView2
                        .setCanDelete(false)
                        .setImgRes(EImage(imageId = it.licenseUrl1 ?: ""))
                        .setImgResList()
                        .setUrl(it.licenseUrl1 ?: "")
                        .setVisible(TextUtils.equals("0", it.isEntrustRegister))
                }

                "3" -> {
                    //审核不通过
                    comCertificationUploadImgView1
                        .setCanDelete(true)
                        .setErrorMsg(mutableListOf(it.licenseReason ?: ""))
                        .setViewBg()
                    comCertificationUploadImgView2
                        .setCanDelete(true)
                        .setErrorMsg(mutableListOf(""))
                        .setViewBg()
                        .setVisible(TextUtils.equals("0", it.isEntrustRegister))
                }

                else -> {
                    //审核通过
                    comCertificationUploadImgView1
                        .setCanDelete(false)
                    comCertificationUploadImgView2
                        .setCanDelete(false)
                        .setUrl(it.licenseUrl1 ?: "")
                        .setImgResList()
                        .setVisible(TextUtils.equals("0", it.isEntrustRegister))
                }
            }
        }
    }

    @LiveDataMatch()
    open fun onUploadExpireDateSuccess() {
        RxBusEventManager.postEvent(RxBusUpdatePicSuccess(success = true))
        finish()
    }

}