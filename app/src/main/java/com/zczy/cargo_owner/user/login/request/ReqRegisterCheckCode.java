package com.zczy.cargo_owner.user.login.request;

import com.zczy.cargo_owner.user.login.entity.ERegisterCheckCode;
import com.zczy.comm.http.entity.BaseNewRequest;
import com.zczy.comm.http.entity.BaseRsp;

/**
 * 功能描述: 验证验证码接口
 * moduleType=1：账户注册
 * moduleType=2：修改服务密码
 * moduleType=3：忘记密码修改密码
 * moduleType=4：修改手机号
 * moduleType=5：重置支付密码
 * moduleType=6：登录密码修改
 * moduleType=7：HUE绑定设备
 * moduleType=8：HUE手机动态效验
 * moduleType=9：验证码登录
 * moduleType=10：设置支付密码
 * moduleType=11：找回密码
 *
 * <AUTHOR> 孙飞虎
 * @company 中储智运科技股份有限公司
 * @copyright （版权）中储南京智慧物流科技有限公司所有
 * @date 2018/10/26
 */
public class ReqRegisterCheckCode extends BaseNewRequest<BaseRsp<ERegisterCheckCode>> {


    public ReqRegisterCheckCode() {

        super ("mms-app/mms/verifyCode/checkVerifyCode");
    }

    /*** 客户端类型(ANDROID或IOS)*/
    String clientType = "ANDROID";

    /*** 手机号*/
    String mobile;

    /*** 验证码*/
    String verifyCode;

    String moduleType;

    /***短信验证码类型 1.短信 2.语音*/
    String verifyCodeType;

    /***
     * （只注册的时候需要）增加userType字段  //7:待认证个体司机(汽运);6:待认证货主;11:待认证物流企业(汽运);12:待认证车队老板(汽运);8:待认证船舶会员(船运);16:待认证单位船舶(船运)
     */
    String userType;

    public void setUserType(String userType) {
        this.userType = userType;
    }

    public void setMobile(String mobile) {

        this.mobile = mobile;
    }

    public void setVerifyCode(String verifyCode) {

        this.verifyCode = verifyCode;
    }

    public String getMobile() {

        return mobile;
    }

    public void setVerifyCodeType(String verifyCodeType) {

        this.verifyCodeType = verifyCodeType;
    }

    public void setModuleType(String moduleType) {

        this.moduleType = moduleType;
    }

    public String getClientType() {
        return clientType;
    }

    public String getVerifyCode() {
        return verifyCode;
    }

    public String getModuleType() {
        return moduleType;
    }

    public String getVerifyCodeType() {
        return verifyCodeType;
    }
}
