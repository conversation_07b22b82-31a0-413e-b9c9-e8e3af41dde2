package com.zczy.cargo_owner.user.overdue.widget

import android.content.Context
import android.graphics.Color
import android.graphics.drawable.GradientDrawable
import androidx.annotation.DrawableRes
import androidx.constraintlayout.widget.ConstraintLayout
import android.util.AttributeSet
import android.view.LayoutInflater
import com.sfh.lib.rx.ui.UtilRxView
import com.zczy.cargo_owner.R
import com.zczy.cargo_owner.user.overdue.ShadowDrawable
import com.zczy.cargo_owner.user.overdue.setGradientShape
import com.zczy.comm.utils.dp2px
import com.zczy.comm.utils.ex.setVisible
import com.zczy.comm.utils.getResColor
import kotlinx.android.synthetic.main.com_certification_for_driver_layout.view.*

/**
 * 功能描述: 司机认证
 * <AUTHOR>
 * @date 2022/6/24-14:58
 */

class ComCertificationForDriver(context: Context, attrs: AttributeSet?, defStyle: Int) :
    ConstraintLayout(context, attrs, defStyle) {

    var listener: Listener? = null
    var licenseStatus: String = "3"

    constructor(context: Context) : this(context, null, 0)

    constructor(context: Context, attrs: AttributeSet?) : this(context, attrs, 0)


    init {
        LayoutInflater.from(context).inflate(R.layout.com_certification_for_driver_layout, this)
        initAttrs(attrs)
        UtilRxView.clicks(tvDoubt, 1000) {
            listener?.onClickChildView(R.id.tvDoubt, licenseStatus)
        }
        UtilRxView.clicks(tvGoFinish, 1000) {
            listener?.onClickChildView(R.id.tvGoFinish, licenseStatus)
        }
        UtilRxView.clicks(llLogo3, 1000) {
            listener?.onClickChildView(R.id.llLogo3, licenseStatus)
        }

        ShadowDrawable.setShadowDrawable(
            view = cl2,
            bgColor = Color.parseColor("#ffffff"),
            shapeRadius = dp2px(8f),
            shadowColor = Color.parseColor("#405086fc"),
            shadowRadius = dp2px(5f),
            offsetX = 0,
            offsetY = 0
        )
    }


    private fun initAttrs(attrs: AttributeSet?) {
        if (attrs != null) {
            val a =
                context.obtainStyledAttributes(attrs, R.styleable.ComCertificationForDriverAttrs)
            //1.是否必填
            val boolean1 =
                a.getBoolean(
                    R.styleable.ComCertificationForDriverAttrs_certification_required_fields,
                    false
                )
            if (boolean1) {
                tvIsItRequired.text = "(必填)"
            } else {
                tvIsItRequired.text = "(非必填)"
            }
            //2.标题1
            val title1 =
                a.getString(R.styleable.ComCertificationForDriverAttrs_certification_required_title1)
            tvTitle.text = title1
            //3.标题2
            val title2 =
                a.getString(R.styleable.ComCertificationForDriverAttrs_certification_required_title2)
            tvTitle1.text = title2
            //4.标题3
            val title3 =
                a.getString(R.styleable.ComCertificationForDriverAttrs_certification_required_title3)
            tvDescribe.text = title3
            //5.是否展示去完成
            val boolean2 =
                a.getBoolean(
                    R.styleable.ComCertificationForDriverAttrs_certification_show_finish,
                    false
                )
            tvGoFinish.setVisible(boolean2)
            //6.是否展示错误信息
            val boolean3 =
                a.getBoolean(
                    R.styleable.ComCertificationForDriverAttrs_certification_show_error_msg,
                    false
                )
            tvErrorMsg.setVisible(boolean3)
            //7.是否展示向右箭头
            val boolean4 =
                a.getBoolean(
                    R.styleable.ComCertificationForDriverAttrs_certification_show_logo3,
                    true
                )
            ivLogo3.setVisible(boolean4)
            //8.是否展示遇到问题
            val boolean5 =
                a.getBoolean(
                    R.styleable.ComCertificationForDriverAttrs_certification_show_doubt,
                    false
                )
            tvDoubt.setVisible(boolean5)
            //9.标题2左侧图片
            val drawable1 =
                a.getDrawable(R.styleable.ComCertificationForDriverAttrs_certification_show_logo1)
            ivLogo1.background = drawable1
            //10.标题2右侧图片
            val drawable2 =
                a.getDrawable(R.styleable.ComCertificationForDriverAttrs_certification_show_logo2)
            ivLogo2.background = drawable2
            //11.标题2右侧图片
            val boolean6 =
                a.getBoolean(
                    R.styleable.ComCertificationForDriverAttrs_certification_show_cl_top,
                    false
                )
            clTop1.setVisible(boolean6)
            emptyView.setVisible(boolean6)
            a.recycle()
        }
    }

    fun setCl2Bg(): ComCertificationForDriver {
        ShadowDrawable.setShadowDrawable(
            view = cl2,
            bgColor = Color.parseColor("#ffffff"),
            shapeRadius = dp2px(6f),
            shadowColor = Color.parseColor("#405086fc"),
            shadowRadius = dp2px(6f),
            offsetX = 0,
            offsetY = 0
        )
        return this@ComCertificationForDriver
    }

    fun setViewBg(): ComCertificationForDriver {
        cl2.setGradientShape(
            startColor = getResColor(R.color.color_FFF3EF),
            endColor = getResColor(R.color.color_FFFFFF),
            strokeColor = getResColor(R.color.color_FFFFFF),
            orientation = GradientDrawable.Orientation.TOP_BOTTOM
        )
        return this@ComCertificationForDriver
    }

    fun setViewBg2(): ComCertificationForDriver {
        cl2.setBackgroundResource(R.drawable.com_gradient_bg)
        return this@ComCertificationForDriver
    }


    fun setLicenseStatus(licenseStatus: String = "3"): ComCertificationForDriver {
        <EMAIL> = licenseStatus
        return this@ComCertificationForDriver
    }

    fun setImageV1(@DrawableRes imgId: Int): ComCertificationForDriver {
        ivLogo1.setBackgroundResource(imgId)
        return this@ComCertificationForDriver
    }

    fun setImageV2(@DrawableRes imgId: Int): ComCertificationForDriver {
        ivLogo2.setBackgroundResource(imgId)
        return this@ComCertificationForDriver
    }

    fun setImageV3(@DrawableRes imgId: Int): ComCertificationForDriver {
        ivStatus.setBackgroundResource(imgId)
        return this@ComCertificationForDriver
    }

    fun setIvStatusVisible(visible: Boolean): ComCertificationForDriver {
        ivStatus.setVisible(visible)
        return this@ComCertificationForDriver
    }

    fun setEmptyViewVisible(visible: Boolean): ComCertificationForDriver {
        emptyView.setVisible(visible)
        return this@ComCertificationForDriver
    }

    fun setImgVisible1(visible: Boolean): ComCertificationForDriver {
        ivLogo1.setVisible(visible)
        return this@ComCertificationForDriver
    }

    fun setImgVisible2(visible: Boolean): ComCertificationForDriver {
        ivLogo2.setVisible(visible)
        return this@ComCertificationForDriver
    }

    fun setImgVisible3(visible: Boolean): ComCertificationForDriver {
        ivLogo3.setVisible(visible)
        return this@ComCertificationForDriver
    }

    fun setClTop1Visible(visible: Boolean): ComCertificationForDriver {
        clTop1.setVisible(visible)
        return this@ComCertificationForDriver
    }

    fun setTvRightTextVisible(visible: Boolean): ComCertificationForDriver {
        if (visible) {
            tvRightText.setVisible(visible)
        } else {
            tvRightText.visibility = INVISIBLE
        }
        return this@ComCertificationForDriver
    }

    fun setTvRightText(text: String): ComCertificationForDriver {
        tvRightText.text = text
        return this@ComCertificationForDriver
    }

    fun setErrorMsgVisible(visible: Boolean): ComCertificationForDriver {
        tvErrorMsg.setVisible(visible)
        return this@ComCertificationForDriver
    }

    fun setGoFinishVisible(visible: Boolean): ComCertificationForDriver {
        tvGoFinish.setVisible(visible)
        return this@ComCertificationForDriver
    }

    fun setTvDoubt(visible: Boolean): ComCertificationForDriver {
        tvDoubt.setVisible(visible)
        return this@ComCertificationForDriver
    }

    fun setTvIsItRequiredText(text: String?): ComCertificationForDriver {
        tvIsItRequired.text = text
        return this@ComCertificationForDriver
    }

    fun setTvTitle(text: String?): ComCertificationForDriver {
        tvTitle.text = text
        return this@ComCertificationForDriver
    }

    fun setTvTitle1(text: String?): ComCertificationForDriver {
        tvTitle1.text = text ?: ""
        return this@ComCertificationForDriver
    }

    fun setTvDescribe(text: String): ComCertificationForDriver {
        tvDescribe.text = text
        return this@ComCertificationForDriver
    }

    fun setTvErrorMsg(text: String): ComCertificationForDriver {
        tvErrorMsg.text = text
        return this@ComCertificationForDriver
    }

    abstract class Listener {
        abstract fun onClickChildView(viewId: Int, licenseStatus: String)
    }
}