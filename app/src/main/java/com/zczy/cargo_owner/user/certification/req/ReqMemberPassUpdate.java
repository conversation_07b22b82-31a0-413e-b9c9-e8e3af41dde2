package com.zczy.cargo_owner.user.certification.req;

import com.zczy.comm.CommServer;
import com.zczy.comm.data.entity.ELogin;
import com.zczy.comm.http.entity.BaseNewRequest;
import com.zczy.comm.http.entity.BaseRsp;
import com.zczy.comm.http.entity.ResultData;

/***
 * 修改紧急联系人手机号码
 */
public class ReqMemberPassUpdate  extends BaseNewRequest<BaseRsp<ResultData>> {


    String contacterPhone;
    String userId;

    public ReqMemberPassUpdate(String phone) {
        super("mms-app/mms/userUpgradeApp/memberPassUpdate");
        contacterPhone= phone;
    }

    @Override
    public Object buildParam() {
        ELogin login = CommServer.getUserServer().getLogin();
        userId = login.getUserId();
        return super.buildParam();
    }
}
