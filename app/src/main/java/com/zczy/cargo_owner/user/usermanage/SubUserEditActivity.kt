package com.zczy.cargo_owner.user.usermanage

import android.content.Context
import android.content.Intent
import android.os.Bundle
import android.text.Editable
import android.text.InputFilter
import android.text.InputFilter.LengthFilter
import android.text.InputType
import android.text.TextWatcher
import android.view.Gravity
import android.view.View
import android.widget.Toast
import com.sfh.lib.mvvm.service.BaseViewModel
import com.sfh.lib.ui.dialog.DialogBuilder
import com.zczy.cargo_owner.R
import com.zczy.cargo_owner.user.login.request.ReqRegisterCheckCode
import com.zczy.cargo_owner.user.usermanage.model.SubUserInfo
import com.zczy.cargo_owner.user.usermanage.request.ReqCheckSubMobileHasRegister
import com.zczy.cargo_owner.user.usermanage.request.ReqCheckUnBindPhone
import com.zczy.cargo_owner.user.usermanage.request.ReqQueryUserInfoDetail
import com.zczy.cargo_owner.user.usermanage.request.ReqSaveUserInfo
import com.zczy.comm.data.request.ReqSendCode
import com.zczy.comm.data.request.ReqShowVerifyCode
import com.zczy.comm.ui.BaseActivity
import com.zczy.comm.utils.CommUtils
import com.zczy.comm.utils.VerificationCodeUtil
import com.zczy.comm.widget.ImageCodeDialog
import com.zczy.comm.widget.RxTimeCountView
import kotlinx.android.synthetic.main.activity_user_edit.btn_cancel
import kotlinx.android.synthetic.main.activity_user_edit.btn_ok
import kotlinx.android.synthetic.main.activity_user_edit.input_name
import kotlinx.android.synthetic.main.activity_user_edit.input_password
import kotlinx.android.synthetic.main.activity_user_edit.input_password_confirm
import kotlinx.android.synthetic.main.activity_user_edit.input_phone
import kotlinx.android.synthetic.main.activity_user_edit.input_yzm
import kotlinx.android.synthetic.main.activity_user_edit.input_zhm
import kotlinx.android.synthetic.main.activity_user_edit.tvTime

/**
 * 注释：子用户编辑页
 * 时间：2024/6/19 0019 11:58
 * 作者：郭翰林
 */
class SubUserEditActivity : BaseActivity<BaseViewModel>() {

    private var verificationCodeUtil: VerificationCodeUtil? = null
    private var subUserInfo: SubUserInfo? = null

    companion object {
        @JvmStatic
        fun start(context: Context?, childId: String) {
            if (context == null) return
            val intent = Intent(context, SubUserEditActivity::class.java)
            intent.putExtra("childId", childId)
            context.startActivity(intent)
        }
    }

    override fun getLayout(): Int = R.layout.activity_user_edit

    override fun bindView(bundle: Bundle?) {
        //账户名
        input_zhm.editText.gravity = Gravity.START
        input_zhm.isEnabled = false
        //手机号
        input_phone.editText.gravity = Gravity.START
        val maxLength = 11 // 最大字符数
        val filters = arrayOf(
            LengthFilter(maxLength),
            InputFilter { source, start, end, dest, dstart, dend -> // 验证是否为中国手机号码
                val content = dest.toString() + source.toString()
                if (Regex("^1\\d*").matches(content)) {
                    return@InputFilter null
                } else {
                    return@InputFilter ""
                }
            }
        )
        input_phone.editText.filters = filters
        input_phone.editText.inputType = InputType.TYPE_CLASS_PHONE
        input_phone.editText.addTextChangedListener(object : TextWatcher {
            override fun beforeTextChanged(p0: CharSequence?, p1: Int, p2: Int, p3: Int) {
            }

            override fun onTextChanged(p0: CharSequence?, p1: Int, p2: Int, p3: Int) {
            }

            override fun afterTextChanged(s: Editable?) {
                if (Regex("^1[3-9]\\d{9}").matches(s.toString())) {
                    verificationCodeUtil?.setAbled(true)
                } else {
                    verificationCodeUtil?.setAbled(false)
                }
            }
        })
        //验证码
        input_yzm.editText.gravity = Gravity.START
        verificationCodeUtil = VerificationCodeUtil(ReqSendCode.MODULE_TYPE_D,
            object : VerificationCodeUtil.IOnCallback {
                override fun getPhone(): String {
                    return input_phone.editText.text.toString()
                }

                override fun onClickCode(req: ReqSendCode?) {
                    req?.let {
                        sendCode(it)
                    }
                }

                override fun showToast(toast: CharSequence?) {
                    val dialogBuilder = DialogBuilder()
                    dialogBuilder.setMessage("提示")
                    dialogBuilder.setMessage(toast)
                    dialogBuilder.setHideCancel(true)
                    showDialog(dialogBuilder)
                }
            }).build(tvTime, RxTimeCountView(this), View(this))
        verificationCodeUtil?.setAbled(false)

        //密码
        input_password.editText.gravity = Gravity.START
        input_password_confirm.editText.gravity = Gravity.START
        CommUtils.lookEditPassWord(input_password.editText)
        CommUtils.lookEditPassWord(input_password_confirm.editText)
        //姓名
        input_name.editText.gravity = Gravity.START
        input_name.editText.filters = arrayOf(LengthFilter(30))

        input_password.editText.hint = "请设置8-20位数字和字母的组合"
        input_password_confirm.editText.hint = "请设置8-20位数字和字母的组合"
        input_zhm.editText.hint = "请输入"
        input_phone.editText.hint = "请输入"
        input_name.editText.hint = "请输入"
        input_yzm.editText.hint = "请输入"

        btn_cancel.setOnClickListener { finish() }
        btn_ok.setOnClickListener { onCheck() }
    }

    override fun initData() {
        val childId = intent.getStringExtra("childId") ?: ""
        queryUserInfo(childId)
    }

    /**
     * 注释：查询用户信息
     * 时间：2024/6/19 0019 10:10
     * 作者：郭翰林
     */
    private fun queryUserInfo(childId: String) {
        val req = ReqQueryUserInfoDetail()
        req.childId = childId
        getViewModel(BaseViewModel::class.java).execute(req) { res ->
            if (res.success()) {
                subUserInfo = res.data
                input_zhm.editText.setText(res.data?.userName ?: "")
                input_name.editText.setText(res.data?.realName ?: "")
                input_phone.editText.setText(res.data?.mobile ?: "")
            }
        }
    }

    /**
     * 注释：发送验证码逻辑
     * 时间：2024/6/19 0019 14:34
     * 作者：郭翰林
     */
    private fun sendCode(req: ReqSendCode) {
        val step1 = ReqShowVerifyCode(req.mobile)
        //是否需要显示图形验证码
        getViewModel(BaseViewModel::class.java).execute(step1) { res ->
            if (res.success() && res.data?.noShow() == true) {
                //不需要直接调用
                getViewModel(BaseViewModel::class.java).execute(req) { response ->
                    if (response.success()) {
                        verificationCodeUtil?.onSendCodeResult(true, req.type)
                    } else {
                        verificationCodeUtil?.onSendCodeResult(false, req.type)
                        showDialogToast(response.msg)
                    }
                }
            } else {
                //显示图形验证码
                ImageCodeDialog(this, req.mobile) { dialog, code ->
                    req.setImageCode(code)
                    getViewModel(BaseViewModel::class.java).execute(req) { response ->
                        if (response.success()) {
                            verificationCodeUtil?.onSendCodeResult(true, req.type)
                        } else {
                            verificationCodeUtil?.onSendCodeResult(false, req.type)
                            showDialogToast(response.msg)
                        }
                    }
                }.show()
            }
        }
    }

    /**
     * 注释：校验
     * 时间：2024/6/19 0019 11:50
     * 作者：郭翰林
     */
    private fun onCheck() {
        val userName = input_zhm.editText.text.toString()
        val realName = input_name.editText.text.toString()
        val mobile = input_phone.editText.text.toString()
        val password = input_password.editText.text.toString()
        val passwordConfirm = input_password_confirm.editText.text.toString()
        val yzm = input_yzm.editText.text.toString()
        if (userName.isEmpty()) {
            Toast.makeText(this, "账户名不能为空", Toast.LENGTH_SHORT).show()
            return
        }
        if (realName.isEmpty()) {
            Toast.makeText(this, "姓名不能为空", Toast.LENGTH_SHORT).show()
            return
        }
        if (password.isEmpty()) {
            Toast.makeText(this, "密码不能为空", Toast.LENGTH_SHORT).show()
            return
        }
        if (passwordConfirm.isEmpty()) {
            Toast.makeText(this, "确认密码不能为空", Toast.LENGTH_SHORT).show()
            return
        }
        //校验手机号、验证码
        checkPhone(mobile) {
            if (mobile.isEmpty() && subUserInfo?.mobile?.isEmpty() == true) {
                //页面手机号无值原手机号也无值不做手机号和验证码检查
                onSave(password, passwordConfirm, userName, realName, mobile, yzm)
                return@checkPhone
            }
            checkYzm(mobile, yzm) {
                onSave(password, passwordConfirm, userName, realName, mobile, yzm)
            }
        }
    }

    /**
     * 注释：校验验证码
     * 时间：2024/6/19 0019 16:20
     * 作者：郭翰林
     */
    private fun checkYzm(mobile: String, yzm: String, runnable: Runnable) {
        if (yzm.isEmpty()) {
            Toast.makeText(this, "验证码不能为空", Toast.LENGTH_SHORT).show()
            return
        }
        val req = ReqRegisterCheckCode()
        req.moduleType = "4"
        req.verifyCode = yzm
        req.mobile = mobile
        req.verifyCodeType = "1"
        getViewModel(BaseViewModel::class.java).execute(req) { res ->
            if (res.success()) {
                runnable.run()
            } else {
                showDialogToast(res.msg)
            }
        }
    }

    /**
     * 注释：校验手机号逻辑
     * 时间：2024/6/19 0019 16:18
     * 作者：郭翰林
     */
    private fun checkPhone(mobile: String, runnable: Runnable) {
        if (mobile.isEmpty() && subUserInfo?.mobile?.isEmpty() == true) {
            //页面手机号无值原手机号也无值不做手机号和验证码检查
            runnable.run()
            return
        }
        if (mobile != subUserInfo?.mobile) {
            if (mobile.isEmpty() && subUserInfo?.mobile?.isNotEmpty() == true) {
                //页面手机号无值，但该用户原手机号有值，走解绑逻辑
                val dialogBuilder = DialogBuilder()
                dialogBuilder.title = "提示"
                dialogBuilder.message = "确认解除当前用户的手机号？"
                dialogBuilder.setOKText("确定")
                dialogBuilder.setCancelText("取消")
                dialogBuilder.setOkListener { dialog, _ ->
                    dialog.dismiss()
                    val req = ReqCheckUnBindPhone()
                    req.userId = subUserInfo?.childId ?: ""
                    getViewModel(BaseViewModel::class.java).execute(req) { res ->
                        if (res.success()) {
                            runnable.run()
                        } else {
                            showDialogToast(res.msg)
                        }
                    }
                }
                showDialog(dialogBuilder)
                return
            }
            //有变化则页面手机号需要进行唯一性校验
            val req = ReqCheckSubMobileHasRegister()
            req.mobile = mobile
            req.userId = subUserInfo?.childId ?: ""
            getViewModel(BaseViewModel::class.java).execute(req) { res ->
                if (res.success()) {
                    runnable.run()
                } else {
                    showDialogToast(res.msg)
                }
            }
        } else {
            runnable.run()
        }
    }

    /**
     * 注释：最终保存逻辑
     * 时间：2024/6/19 0019 16:19
     * 作者：郭翰林
     */
    private fun onSave(
        password: String,
        passwordConfirm: String,
        userName: String,
        realName: String,
        mobile: String,
        yzm: String
    ) {
        //校验密码
        val pattern = Regex("^(?=.*[A-Za-z])(?=.*\\d)[A-Za-z\\d]{8,20}$")
        if (!(pattern.matches(password) && pattern.matches(passwordConfirm))) {
            Toast.makeText(this, "密码长度应为8~20位的数字和字母组合！", Toast.LENGTH_SHORT).show()
            return
        }
        if (password != passwordConfirm) {
            Toast.makeText(this, "两次密码不一致", Toast.LENGTH_SHORT).show()
            return
        }
        //提交保存
        val req = ReqSaveUserInfo()
        req.childId = intent.getStringExtra("childId") ?: ""
        req.userName = userName
        req.realName = realName
        req.mobile = mobile
        req.userPassword = password
        req.confirmPassword = passwordConfirm
        req.verificationCode = yzm
        getViewModel(BaseViewModel::class.java).execute(req) { res ->
            if (res.success()) {
                Toast.makeText(this, "保存成功", Toast.LENGTH_SHORT).show()
                finish()
            }
        }
    }
}
