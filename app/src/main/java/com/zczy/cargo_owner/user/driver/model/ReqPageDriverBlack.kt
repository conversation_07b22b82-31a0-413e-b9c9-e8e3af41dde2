package com.zczy.cargo_owner.user.driver.model

import android.os.Parcelable
import com.zczy.comm.CommServer
import com.zczy.comm.http.entity.BaseNewRequest
import com.zczy.comm.http.entity.BaseRsp
import com.zczy.comm.http.entity.PageList
import kotlinx.android.parcel.Parcelize

/**
 *@Desc 承运方黑名单列表
 *@User ssp
 *@Date 2023/8/31-19:36
 */

class ReqPageDriverBlack(
    var nowPage: Int = 1,
    var pageSize: Int = 10,
    var queryParam: String? = null,//承运人姓名或手机号
    var blackStatus: String? = null, //黑名单状态  0-正常 1-移除
    private var consignorUserId: String? = null,
) : BaseNewRequest<BaseRsp<PageList<EDriverBlack>>>("/mms-app/consignorCarrierRelation/queryRelationBlackAppList") {

    override fun buildParam(): Any {
        consignorUserId = CommServer.getUserServer().login?.userId
        return super.buildParam()
    }
}

@Parcelize
data class EDriverBlack(
    var id: String? = null, //	主键id
    var userId: String? = null, // 司机userId
    var memberName: String? = null, //司机姓名
    var mobile: String? = null, //司机手机号
    var blackStatus: String? = null, //黑名单状态  0-正常 1-移除
    var userType: String? = null, //司机手机号个体司机-2 物流企业-3 车老板-10
): Parcelable