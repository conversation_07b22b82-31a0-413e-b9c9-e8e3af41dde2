package com.zczy.cargo_owner.user.usermanage

import android.content.Context
import android.content.Intent
import android.os.Bundle
import com.sfh.lib.mvvm.service.BaseViewModel
import com.zczy.cargo_owner.R
import com.zczy.cargo_owner.user.usermanage.request.ReqQueryUserInfoDetail
import com.zczy.comm.ui.BaseActivity
import kotlinx.android.synthetic.main.activity_user_detail.img_status
import kotlinx.android.synthetic.main.activity_user_detail.text_account
import kotlinx.android.synthetic.main.activity_user_detail.text_name
import kotlinx.android.synthetic.main.activity_user_detail.text_phone
import kotlinx.android.synthetic.main.activity_user_detail.text_time

/**
 * 注释：子用户详情页
 * 时间：2024/6/18 0018 16:00
 * 作者：郭翰林
 */
class SubUserDetailActivity : BaseActivity<BaseViewModel>() {
    companion object {
        @JvmStatic
        fun start(context: Context?, childId: String) {
            if (context == null) return
            val intent = Intent(context, SubUserDetailActivity::class.java)
            intent.putExtra("childId", childId)
            context.startActivity(intent)
        }
    }

    override fun getLayout(): Int = R.layout.activity_user_detail

    override fun bindView(bundle: Bundle?) {
    }

    override fun initData() {
        val childId = intent.getStringExtra("childId") ?: ""
        queryUserInfo(childId)
    }


    /**
     * 注释：查询用户信息
     * 时间：2024/6/19 0019 10:10
     * 作者：郭翰林
     */
    private fun queryUserInfo(childId: String) {
        val req = ReqQueryUserInfoDetail()
        req.childId = childId
        getViewModel(BaseViewModel::class.java).execute(req) { res ->
            if (res.success()) {
                text_name.text = res.data?.realName ?: ""
                text_account.text = res.data?.userName ?: ""
                text_phone.text = res.data?.mobile ?: ""
                text_time.text = res.data?.createdTime ?: ""
                img_status.setImageResource(if (res.data?.status == "1") R.drawable.ic_start_use else R.drawable.ic_stop_use)
            }
        }
    }
}