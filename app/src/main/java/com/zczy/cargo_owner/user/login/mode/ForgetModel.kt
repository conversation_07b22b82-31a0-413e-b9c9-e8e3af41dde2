package com.zczy.cargo_owner.user.login.mode

import com.sfh.lib.exception.HandleException
import com.sfh.lib.rx.IResult
import com.zczy.cargo_owner.user.info.ReqCheckMobileExist
import com.zczy.cargo_owner.user.info.model.ReqBindMobile
import com.zczy.cargo_owner.user.login.request.ReqResetPassword
import com.zczy.comm.data.SMSCodeModel
import com.zczy.comm.data.request.ReqSendCode
import com.zczy.comm.http.entity.BaseRsp
import com.zczy.comm.http.entity.ResultData

class ForgetModel : SMSCodeModel() {
    override fun onShowImageVerifyCode(req: ReqSendCode) {
        this.setValue("onShowImageVerifyCode", req)
    }

    override fun onSendCode(success: Boolean, type: String) {
        this.setValue("onSendCode", success, type)
    }

    override fun onCheckSuccess() {
        this.setValue("onCheckSuccess")
    }

    fun save(req: ReqResetPassword) {
        this.execute<BaseRsp<ResultData>>(true, req, object : IResult<BaseRsp<ResultData>> {
            @Throws(Exception::class)
            override fun onSuccess(baseRsp: BaseRsp<ResultData>) {
                if (baseRsp.success()) {
                    setValue("onSaveSuccess", baseRsp.msg)
                } else {
                    showDialogToast(baseRsp.msg)
                }
            }

            override fun onFail(e: HandleException) {
                showDialogToast(e.msg)
            }
        })
    }

    fun checkMobileExist(req: ReqCheckMobileExist, req1 : ReqSendCode?) {
        this.execute(true, req, object : IResult<BaseRsp<ResultData>> {
            @Throws(Exception::class)
            override fun onSuccess(baseRsp: BaseRsp<ResultData>) {
                if (baseRsp.success()) {
                    setValue("checkMobileExistSuccess", req1)
                } else {
                    when (baseRsp.data?.resultCode) {
                        "0001", "0002" -> {
                            showDialogToast("手机号已存在！")
                        }

                        else -> {
                            showDialogToast(baseRsp.msg)
                        }
                    }
                }
            }

            override fun onFail(e: HandleException) {
                showDialogToast(e.msg)
            }
        })
    }

    fun checkMobileExistV1(req: ReqCheckMobileExist) {
        this.execute(true, req, object : IResult<BaseRsp<ResultData>> {
            @Throws(Exception::class)
            override fun onSuccess(baseRsp: BaseRsp<ResultData>) {
                if (baseRsp.success()) {
                    setValue("checkMobileExistSuccess")
                } else {
                    when (baseRsp.data?.resultCode) {
                        "0001", "0002" -> {
                            setValue("checkMobileExistError", baseRsp.data)
                        }

                        else -> {
                            showDialogToast(baseRsp.msg)
                        }
                    }
                }
            }

            override fun onFail(e: HandleException) {
                showDialogToast(e.msg)
            }
        })
    }

    fun bindMobile(req: ReqBindMobile) {
        this.execute(true, req, object : IResult<BaseRsp<ResultData>> {
            @Throws(Exception::class)
            override fun onSuccess(baseRsp: BaseRsp<ResultData>) {
                if (baseRsp.success()) {
                    showToast(baseRsp.msg)
                    setValue("bindMobileSuccess")
                } else {
                    showDialogToast(baseRsp.msg)
                }
            }

            override fun onFail(e: HandleException) {
                showDialogToast(e.msg)
            }
        })
    }
}