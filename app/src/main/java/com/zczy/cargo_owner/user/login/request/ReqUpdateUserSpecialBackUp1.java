package com.zczy.cargo_owner.user.login.request;

import com.zczy.comm.CommServer;
import com.zczy.comm.data.entity.ELogin;
import com.zczy.comm.http.entity.BaseNewRequest;
import com.zczy.comm.http.entity.BaseRsp;
import com.zczy.comm.http.entity.ResultData;

/**
 * 功能描述: 协议阅读更新
 *
 * <AUTHOR> 孙飞虎
 * @company 中储智运科技股份有限公司
 * @copyright （版权）中储南京智慧物流科技有限公司所有
 * @date 2018/10/23
 */
public class ReqUpdateUserSpecialBackUp1 extends BaseNewRequest<BaseRsp<ResultData>> {

    String userId;
    public ReqUpdateUserSpecialBackUp1() {
        super("mms-app/mms/login/updateUserSpecialBackUp1");
    }
    @Override
    public Object buildParam() {
        ELogin login = CommServer.getUserServer().getLogin();
        if (login != null) {
            userId = login.getUserId();
        }
        return super.buildParam();
    }

}
