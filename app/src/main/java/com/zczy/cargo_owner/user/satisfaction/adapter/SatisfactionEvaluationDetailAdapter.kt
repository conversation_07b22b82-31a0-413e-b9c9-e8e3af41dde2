package com.zczy.cargo_owner.user.satisfaction.adapter

import androidx.core.content.ContextCompat
import android.util.SparseArray
import android.widget.TextView
import com.chad.library.adapter.base.BaseQuickAdapter
import com.chad.library.adapter.base.BaseViewHolder
import com.zczy.cargo_owner.R
import com.zczy.cargo_owner.user.satisfaction.rsp.RspSatisfactionReason

/**
 *    author : Ssp
 *    date   : 2019/7/4 15:01
 *    desc   : 客服满意度评价详情
 */
open class SatisfactionEvaluationDetailAdapter
    : BaseQuickAdapter<RspSatisfactionReason, BaseViewHolder>(R.layout.satisfaction_evaluation_detail_item) {
    val data = SparseArray<RspSatisfactionReason>()

    override fun convert(helper: BaseViewHolder, item: RspSatisfactionReason) {
        helper.setText(R.id.tv_satisfaction, item.evaluateReason)
        val tvSatisfaction = helper.getView<TextView>(R.id.tv_satisfaction)
        when {
            data.size() <= 0 -> {
                tvSatisfaction.setBackgroundResource(R.drawable.file_gray_circle_bg_stroke)
                tvSatisfaction.setTextColor(ContextCompat.getColor(mContext, R.color.text_66))
            }
            data.get(item.evaluateReason.hashCode()) == null -> {
                //表示该项未选择
                tvSatisfaction.setBackgroundResource(R.drawable.file_gray_circle_bg_stroke)
                tvSatisfaction.setTextColor(ContextCompat.getColor(mContext, R.color.text_66))
            }
            else -> {
                //表示已选择过该项
                tvSatisfaction.setBackgroundResource(R.drawable.file_blue_circle_bg_stroke)
                tvSatisfaction.setTextColor(ContextCompat.getColor(mContext, R.color.text_blue))
            }
        }
    }

    /**
     *设置选中状态
     */
    open fun setSelected(item: RspSatisfactionReason) {
        when {
            data.size() <= 0 ->
                //表示选择该项
                data.append(item.evaluateReason.hashCode(), item)
            data.get(item.evaluateReason.hashCode()) == null ->
                //表示选择该项
                data.append(item.evaluateReason.hashCode(), item)
            else ->
                //表示已选择过该项
                data.remove(item.evaluateReason.hashCode())
        }
        notifyDataSetChanged()
    }

    /**
     * 返回当前选择的数据
     */
    open fun getSelected(): SparseArray<RspSatisfactionReason> {
        return data
    }

    /**
     * 切换评价星数 清空当前选择
     */
    open fun clearSelectData() {
        data.clear()
    }
}


