package com.zczy.cargo_owner.user.certification;

import android.Manifest;
import android.app.Activity;
import android.content.Context;
import android.content.Intent;
import android.graphics.Color;
import android.os.Bundle;
import android.text.Editable;
import android.text.InputFilter;
import android.text.TextUtils;
import android.text.TextWatcher;
import android.view.Gravity;
import android.view.LayoutInflater;
import android.view.View;
import android.widget.EditText;
import android.widget.ImageView;
import android.widget.LinearLayout;
import android.widget.RadioButton;
import android.widget.TextView;

import androidx.annotation.NonNull;
import androidx.annotation.Nullable;

import com.jakewharton.rxbinding2.widget.RxTextView;
import com.sfh.lib.mvvm.annotation.LiveDataMatch;
import com.sfh.lib.mvvm.service.BaseViewModel;
import com.sfh.lib.ui.dialog.DialogBuilder;
import com.zczy.cargo_owner.R;
import com.zczy.cargo_owner.libcomm.config.HttpConfig;
import com.zczy.cargo_owner.libcomm.widget.CheckSelfPermissionDialog;
import com.zczy.cargo_owner.user.CarOwnerFileWebviewActivity;
import com.zczy.cargo_owner.user.certification.bean.CompanyArray;
import com.zczy.cargo_owner.user.certification.bean.MemberDetails;
import com.zczy.cargo_owner.user.certification.dialog.SelectCompanyInfoDialog;
import com.zczy.cargo_owner.user.certification.model.CertificationModel;
import com.zczy.cargo_owner.user.certification.req.ReqHzCertification;
import com.zczy.cargo_owner.user.certification.req.RsqQueryCustomerRoleAuthState;
import com.zczy.comm.data.entity.EImage;
import com.zczy.comm.http.entity.BaseRsp;
import com.zczy.comm.http.entity.ResultData;
import com.zczy.comm.permission.PermissionCallBack;
import com.zczy.comm.permission.PermissionUtil;
import com.zczy.comm.pluginserver.AMainServer;
import com.zczy.comm.ui.BaseActivity;
import com.zczy.comm.utils.imageselector.ImagePreviewActivity;
import com.zczy.comm.utils.imageselector.ImageSelector;
import com.zczy.comm.widget.AppToolber;
import com.zczy.comm.widget.BaseCheckProtocolView;
import com.zczy.comm.widget.dialog.MenuDialogV1;
import com.zczy.comm.widget.dialog.NormalInfoDialog;
import com.zczy.comm.widget.inputv2.InputViewClick;
import com.zczy.comm.widget.inputv2.InputViewEdit;
import com.zczy.comm.widget.inputv2.InputViewImage;
import com.zczy.comm.x5.X5WebActivity;

import org.jetbrains.annotations.NotNull;

import java.util.ArrayList;
import java.util.Date;
import java.util.List;

import io.reactivex.Observable;
import io.reactivex.disposables.Disposable;
import kotlin.collections.CollectionsKt;

/**
 * 货主认证
 */
public class HzCertificationStartActivity extends BaseActivity<CertificationModel> implements View.OnClickListener {

    private AppToolber mAppToolber;
    /**
     * 是
     */
    private RadioButton mRadioTrue;
    /**
     * 否
     */
    private RadioButton mRadioFalse;
    private InputViewEdit mTvContact;
    private InputViewEdit mTvContactPhoneNumber;
    private InputViewClick mTvEnterpriseName;//企业名称
    private InputViewEdit inputView1;//统一社会信用代码
    private InputViewImage mBusinessLicense;
    private InputViewImage mAttorney;
    private InputViewImage mLegalPersonIdcard;
    private InputViewClick view_type_zhuti;
    /**
     * 请输入备注信息
     */
    private EditText mNoteTv;
    /**
     * (0/150)
     */
    private TextView mSizeTv;
    /**
     * 提交资料
     */
    private TextView mTvSubmit;
    private String isEntrustRegister = "0";
    // 营业执照照片
    private final int BUSINESS_LICENSE_PIC = 1;

    // 授权委托书
    private final int LETTER_ATTORNEY_PIC = 2;

    // 法人身份证照片
    private final int LEGALPERSON_IDCARD_PIC = 3;
    // 注册人身份证照片
    private final int REGISTRANTIDCARD_PIC = 5;
    private String businessLicenseUrl = "";
    private String letterAttorneyUrl = "";
    private String legalPersonIdcardUrl = "";
    private String registrantidcardUrl = "";
    private int flagPic;
    private final int selectPic = 0x01;
    private final int deletePic = 0x02;
    private boolean tvNextEnable;
    private ReqHzCertification reqHzCertification = new ReqHzCertification();
    private InputViewImage mRegistrantIdcard;
    private BaseCheckProtocolView bcv;
    private LinearLayout download_ll;
    private TextView download_txt;
    private LinearLayout cert_status;

    public static void start(Context context) {
        Intent intent = new Intent(context, HzCertificationStartActivity.class);
        context.startActivity(intent);
    }

    public static List<EImage> getImageList(String netUrl) {
        List<EImage> list = new ArrayList<>();
        EImage image = new EImage();
        image.setNetUrl(HttpConfig.getUrlImage(netUrl));
        list.add(image);
        return list;
    }

    @Override
    protected int getLayout() {
        return R.layout.hz_certification_start_activity;
    }

    @Override
    protected void bindView(@Nullable Bundle bundle) {
        initView();
        initListener();
    }

    @Override
    protected void initData() {
        Observable<CharSequence> Observable1 = RxTextView.textChanges(mTvContact.getEditText());
        Observable<CharSequence> Observable2 = RxTextView.textChanges(mTvContactPhoneNumber.getEditText());
        Disposable subscribe = Observable
                .combineLatest(
                        Observable1,
                        Observable2,
                        (charSequence, charSequence1) ->
                                TextUtils.isEmpty(charSequence) || TextUtils.isEmpty(charSequence1)
                ).subscribe(aBoolean -> {
                    boolean a = aBoolean || TextUtils.isEmpty(mTvEnterpriseName.getContent()) || TextUtils.isEmpty(inputView1.getContent());
                    tvNextEnable = !a;
                    changeBtnStatus();
                });
        putDisposable(subscribe);
        getViewModel().getMemberDetail();
    }

    private void initView() {
        mAppToolber = (AppToolber) findViewById(R.id.appToolber);
        mRadioTrue = (RadioButton) findViewById(R.id.radioTrue);
        mRadioFalse = (RadioButton) findViewById(R.id.radioFalse);
        mTvContact = (InputViewEdit) findViewById(R.id.tv_contact);
        cert_status = (LinearLayout) findViewById(R.id.cert_status);
        mTvContactPhoneNumber = (InputViewEdit) findViewById(R.id.tv_contact_phoneNumber);
        mTvEnterpriseName = (InputViewClick) findViewById(R.id.tv_enterprise_name);
        inputView1 = (InputViewEdit) findViewById(R.id.inputView1);
        inputView1.getEditText().setTextColor(Color.parseColor("#B2B2B2"));
        inputView1.setEnabled(false);
        inputView1.setListener(new InputViewEdit.Listener() {
            @Override
            public void onTextChanged(int viewId, @NonNull InputViewEdit view, @NonNull String s) {
                boolean a = TextUtils.isEmpty(mTvContact.getEditText().toString().trim()) || TextUtils.isEmpty(mTvContactPhoneNumber.getEditText().toString().trim()) || TextUtils.isEmpty(mTvEnterpriseName.getContent()) || TextUtils.isEmpty(inputView1.getContent());
                tvNextEnable = !a;
                changeBtnStatus();
            }
        });
        mBusinessLicense = (InputViewImage) findViewById(R.id.business_license);
        mRegistrantIdcard = (InputViewImage) findViewById(R.id.registrant_idcard);
        mAttorney = (InputViewImage) findViewById(R.id.attorney);
        mLegalPersonIdcard = (InputViewImage) findViewById(R.id.legal_person_idcard);
        mNoteTv = (EditText) findViewById(R.id.noteTv);
        mNoteTv.setFilters(new InputFilter[]{new InputFilter.LengthFilter(120)});
        mSizeTv = (TextView) findViewById(R.id.sizeTv);
        mTvSubmit = (TextView) findViewById(R.id.tv_submit);
        bcv = findViewById(R.id.bcv);
        mTvSubmit.setOnClickListener(this);
        view_type_zhuti = findViewById(R.id.view_type_zhuti);
        download_ll = findViewById(R.id.download_ll);
        download_txt = findViewById(R.id.download_txt);
        mTvEnterpriseName.setListener(new InputViewClick.Listener() {
            @Override
            public void onClick(int viewId, @NonNull InputViewClick view, @NonNull String content) {
                //选择货主名称
                SelectCompanyInfoDialog selectCompanyInfoDialog = new SelectCompanyInfoDialog(getViewModel(), (dialog, rspCompanyInfo) -> {
                    dialog.dismiss();
                    mTvEnterpriseName.setContent(rspCompanyInfo.getBusinessLicenseName());
                    if (TextUtils.isEmpty(rspCompanyInfo.getTaxId())) {
                        inputView1.setEnabled(true);
                        inputView1.setContent("");
                        cert_status.setVisibility(View.GONE);
                        mBusinessLicense.setVisibility(View.VISIBLE);
                    } else {
                        inputView1.setEnabled(false);
                        inputView1.setContent(rspCompanyInfo.getTaxId());
                        getViewModel().queryCustomerRoleAuthState(rspCompanyInfo.getTaxId());
                    }
                    changeBtnStatus();
                    return null;
                });
                selectCompanyInfoDialog.show(HzCertificationStartActivity.this);
            }
        });
        view_type_zhuti.setListener(new InputViewClick.Listener() {
            @Override
            public void onClick(int viewId, @NonNull InputViewClick view, @NonNull String content) {
                getViewModel().querySubsidiaryList4APP();
            }
        });
        download_txt.setOnClickListener(v -> {
            String url = "202107081028458112615.docx";
            String url2 = "http://img.zczy56.com/" + url;
            String[] list = new String[]{Manifest.permission.WRITE_EXTERNAL_STORAGE, Manifest.permission.READ_EXTERNAL_STORAGE};
            CheckSelfPermissionDialog.storagePermissionDialog(HzCertificationStartActivity.this, new PermissionCallBack() {
                @Override
                public void onHasPermission() {
                    PermissionUtil.checkPermissions(HzCertificationStartActivity.this, "请允许访问您的存储空间", list, new PermissionCallBack() {
                        @Override
                        public void onHasPermission() {
                            getViewModel().loadFile(url2, url,HzCertificationStartActivity.this);
                            getViewModel().loadFile2(url2, url);
                        }
                    });
                }
            });

        });
    }

    @LiveDataMatch
    public void onQueryCustomerRoleAuthState(RsqQueryCustomerRoleAuthState customerRoleAuthState) {
        if (customerRoleAuthState.getExamineType() == 1) {
            cert_status.setVisibility(View.VISIBLE);
            mBusinessLicense.setVisibility(View.GONE);
        } else {
            cert_status.setVisibility(View.GONE);
            mBusinessLicense.setVisibility(View.VISIBLE);
        }
    }

    @LiveDataMatch
    public void querySubsidiaryList4APPSuccess(List<CompanyArray> companyArrayBean) {
        MenuDialogV1.instance(companyArrayBean).setClick((CompanyArray companyArrayBeans, Integer integer) -> {
            view_type_zhuti.setContent(companyArrayBeans.getSubsidiaryName());
            return null;
        }).show(HzCertificationStartActivity.this);
    }

    @LiveDataMatch
    public void onMemberDetailSuccess(MemberDetails data) {

        //自动带出企业名称并不可修改
        String companyNameEditFlag = data.getCompanyNameEditFlag();
        if (TextUtils.equals("0", companyNameEditFlag)) {
            mTvEnterpriseName.setEnabled(false);
            inputView1.setEnabled(false);
        }
        if (!TextUtils.isEmpty(data.getCompanyName())) {
            //有企业名称
            mTvEnterpriseName.setContent(data.getCompanyName());
            //统一社会信用代码
            if (TextUtils.isEmpty(data.getTaxId())) {
                //无统一社会信用代码，可输入
                inputView1.setEnabled(true);
            } else {
                inputView1.setEnabled(false);
                inputView1.setContent(data.getTaxId());
            }
        }

        legalPersonIdcardUrl = data.getLegalIdCardUrl();
        if (!TextUtils.isEmpty(legalPersonIdcardUrl)) {
            mLegalPersonIdcard.setImgUrl(HttpConfig.getUrlImage(legalPersonIdcardUrl));
        }
        // 营业执照
        businessLicenseUrl = data.getBusiLicUrl();
        if (!TextUtils.isEmpty(businessLicenseUrl)) {
            mBusinessLicense.setImgUrl(HttpConfig.getUrlImage(businessLicenseUrl));
        }
    }

    private void initListener() {
        mBusinessLicense.setListener(imageViewListener);
        mAttorney.setListener(imageViewListener);
        mLegalPersonIdcard.setListener(imageViewListener);
        mRegistrantIdcard.setListener(imageViewListener);
        mNoteTv.addTextChangedListener(new TextWatcher() {
            @Override
            public void beforeTextChanged(CharSequence s, int start, int count, int after) {
            }

            @Override
            public void onTextChanged(CharSequence s, int start, int before, int count) {
            }

            @Override
            public void afterTextChanged(Editable s) {
                if (s.length() > 120) {
                    return;
                }
                mSizeTv.setText("(" + s.length() + "/120)");
            }
        });

        mRadioTrue.setOnCheckedChangeListener((compoundButton, b) -> {
            if (b) {
                isEntrustRegister = "0";
                download_ll.setVisibility(View.GONE);
                mAttorney.setVisibility(View.GONE);
                mLegalPersonIdcard.setVisibility(View.VISIBLE);
                mRegistrantIdcard.setVisibility(View.GONE);
                mRadioFalse.setChecked(false);
                changeBtnStatus();
            }
        });
        mRadioFalse.setOnCheckedChangeListener((compoundButton, b) -> {
            if (b) {
                isEntrustRegister = "1";
                download_ll.setVisibility(View.VISIBLE);
                mAttorney.setVisibility(View.VISIBLE);
                mLegalPersonIdcard.setVisibility(View.GONE);
                mRegistrantIdcard.setVisibility(View.VISIBLE);
                mRadioTrue.setChecked(false);
                changeBtnStatus();
            }
        });
        mAppToolber.setLeftOnClickListener(view -> {
            DialogBuilder dialogBuilder = new DialogBuilder();
            dialogBuilder.setTitle("提示")
                    .setMessage("确定要放弃认证么？")
                    .setGravity(Gravity.CENTER)
                    .setOKTextColor("确认", R.color.text_blue)
                    .setCancelTextColor("取消", R.color.text_blue)
                    .setOkListener((dialog, which) -> {
                        dialog.dismiss();
                        finish();
                    });
            showDialog(dialogBuilder);
        });
        mAppToolber.setRightOnClickListener(v -> {
            AMainServer mainServer = AMainServer.getPluginServer();
            if (mainServer != null) {
                mainServer.showLineServerPhone(this);
            }
        });
        bcv.setOnClickContentListener(() -> {
                    Date date = new Date();
                    long time = date.getTime();
                    X5WebActivity.startContentUI(HzCertificationStartActivity.this,
                            "信息采集授权协议", HttpConfig.getWebUrl() + "/form_h5/order/index.html?_t=" + time + "#/infoCollection");
                }
        );
    }

    @Override
    public void onClick(View view) {
        if (view.getId() == R.id.tv_submit) {
            ReqHzCertification req = new ReqHzCertification();
            req.setIsEntrustRegister(isEntrustRegister);
            req.setContacter(mTvContact.getContent());
            req.setContacterPhone(mTvContactPhoneNumber.getContent());
            req.setCompanyName(mTvEnterpriseName.getContent());
            req.setTaxId(inputView1.getContent());
            req.setBusiLicUrl(businessLicenseUrl);
            req.setNeedBusinessLicense(mBusinessLicense.getVisibility() == View.VISIBLE);
            if (TextUtils.equals(isEntrustRegister, "0")) {
                // 法人身份证照片
                req.setLegalIdCardUrl(legalPersonIdcardUrl);
            } else if (TextUtils.equals(isEntrustRegister, "1")) {
                // 非法人身份证照片
                req.setNonLegalIdCardUrl(registrantidcardUrl);
                // 非法人授权委托书
                req.setNonLegalAuthUrl(letterAttorneyUrl);
            }
            req.setReceptRemark(mNoteTv.getText().toString());
            //用户类型：货主【promoteType= 1】 和  加盟商【promoteType= 13】
            req.setPromoteType("1");
            getViewModel().hzUserPromote(req);
        }
    }

    @LiveDataMatch
    public void hzUserPromoteSuccess(BaseRsp<ResultData> baseRsp) {
        if (baseRsp.success()) {
            finish();
        } else {
            showDialogToast(baseRsp.getData().getResultMsg());
        }
    }

    InputViewImage.Listener imageViewListener = new InputViewImage.Listener() {

        @Override
        public void onClickImg(int id, @NonNull InputViewImage view) {
            if (id == R.id.business_license) {
                flagPic = BUSINESS_LICENSE_PIC;
                if (TextUtils.isEmpty(businessLicenseUrl)) {
                    showDialog();
                } else {
                    ImagePreviewActivity.start(HzCertificationStartActivity.this, getImageList(businessLicenseUrl), 0, true, deletePic);
                }
            } else if (id == R.id.attorney) {
                flagPic = LETTER_ATTORNEY_PIC;
                if (TextUtils.isEmpty(letterAttorneyUrl)) {
                    showDialog();
                } else {
                    ImagePreviewActivity.start(HzCertificationStartActivity.this, getImageList(letterAttorneyUrl), 0, true, deletePic);
                }
            } else if (id == R.id.legal_person_idcard) {
                flagPic = LEGALPERSON_IDCARD_PIC;
                if (TextUtils.isEmpty(legalPersonIdcardUrl)) {
                    showDialog();
                } else {
                    ImagePreviewActivity.start(HzCertificationStartActivity.this, getImageList(legalPersonIdcardUrl), 0, true, deletePic);
                }
            } else if (id == R.id.registrant_idcard) {
                flagPic = REGISTRANTIDCARD_PIC;
                if (TextUtils.isEmpty(registrantidcardUrl)) {
                    showDialog();
                } else {
                    ImagePreviewActivity.start(HzCertificationStartActivity.this, getImageList(registrantidcardUrl), 0, true, deletePic);
                }
            }
        }
    };


    @Override
    protected void onActivityResult(int requestCode, int resultCode, Intent data) {
        super.onActivityResult(requestCode, resultCode, data);
        if (resultCode != Activity.RESULT_OK) {
            return;
        }
        switch (requestCode) {
            case selectPic:
                List<String> stringList = ImageSelector.obtainPathResult(data);
                setImageViewData(stringList);
                break;
            case deletePic:
                stringList = CollectionsKt.map(ImagePreviewActivity.onActivityResult(data), EImage::getNetUrl);
                setImageViewData(stringList);
                break;
        }
    }

    private void setImageViewData(List<String> stringList) {
        String picUrl = "";
        if (stringList != null && stringList.size() > 0) {
            picUrl = stringList.get(0);
        }
        switch (flagPic) {
            case BUSINESS_LICENSE_PIC:
                setImageViewUrl(mBusinessLicense, picUrl);
                if (TextUtils.isEmpty(picUrl)) {
                    businessLicenseUrl = "";
                }
                break;
            case LETTER_ATTORNEY_PIC:
                setImageViewUrl(mAttorney, picUrl);
                if (TextUtils.isEmpty(picUrl)) {
                    letterAttorneyUrl = "";
                }
                break;
            case LEGALPERSON_IDCARD_PIC:
                setImageViewUrl(mLegalPersonIdcard, picUrl);
                if (TextUtils.isEmpty(picUrl)) {
                    legalPersonIdcardUrl = "";
                }
                break;
            case REGISTRANTIDCARD_PIC:
                setImageViewUrl(mRegistrantIdcard, picUrl);
                if (TextUtils.isEmpty(picUrl)) {
                    registrantidcardUrl = "";
                }
                break;
        }
        if (!TextUtils.isEmpty(picUrl)) {
            getViewModel().upLoadPic(picUrl);
        }
    }

    // 图片上传失败回调
    @LiveDataMatch
    public void upLoadPicError() {
        switch (flagPic) {
            case BUSINESS_LICENSE_PIC:
                businessLicenseUrl = "";
                reqHzCertification.setBusiLicUrl(businessLicenseUrl);
                setImageViewUrl(mBusinessLicense, "");
                break;
            case LETTER_ATTORNEY_PIC:
                letterAttorneyUrl = "";
                reqHzCertification.setNonLegalAuthUrl(letterAttorneyUrl);
                setImageViewUrl(mAttorney, "");
                break;
            case LEGALPERSON_IDCARD_PIC:
                legalPersonIdcardUrl = "";
                reqHzCertification.setNonLegalIdCardUrl(legalPersonIdcardUrl);
                setImageViewUrl(mLegalPersonIdcard, "");
                break;
            case REGISTRANTIDCARD_PIC:
                registrantidcardUrl = "";
                reqHzCertification.setLegalIdCardUrl(registrantidcardUrl);
                setImageViewUrl(mRegistrantIdcard, "");
                break;
        }
        changeBtnStatus();
    }

    // 图片上传成功回调
    @LiveDataMatch
    public void upLoadPicSuccess(String picUrl) {
        switch (flagPic) {
            case BUSINESS_LICENSE_PIC:
                businessLicenseUrl = picUrl;
                reqHzCertification.setBusiLicUrl(businessLicenseUrl);
                break;
            case LETTER_ATTORNEY_PIC:
                letterAttorneyUrl = picUrl;
                reqHzCertification.setNonLegalAuthUrl(letterAttorneyUrl);
                break;
            case LEGALPERSON_IDCARD_PIC:
                legalPersonIdcardUrl = picUrl;
                reqHzCertification.setNonLegalIdCardUrl(legalPersonIdcardUrl);
                break;
            case REGISTRANTIDCARD_PIC:
                registrantidcardUrl = picUrl;
                reqHzCertification.setLegalIdCardUrl(registrantidcardUrl);
                break;
        }
        changeBtnStatus();
    }

    private void changeBtnStatus() {
        if (TextUtils.equals(isEntrustRegister, "0")) {
            if (!tvNextEnable || TextUtils.isEmpty(legalPersonIdcardUrl)) {
                mTvSubmit.setEnabled(false);
            } else {
                mTvSubmit.setEnabled(true);
            }
        } else if (TextUtils.equals(isEntrustRegister, "1")) {
            if (!tvNextEnable || TextUtils.isEmpty(registrantidcardUrl) || TextUtils.isEmpty(letterAttorneyUrl)) {
                mTvSubmit.setEnabled(false);
            } else {
                mTvSubmit.setEnabled(true);
            }
        }
        if (cert_status.getVisibility() == View.GONE && TextUtils.isEmpty(businessLicenseUrl)) {
            mTvSubmit.setEnabled(false);
        }
    }

    private void setImageViewUrl(InputViewImage view, String picUrl) {
        if (TextUtils.isEmpty(picUrl)) {
            view.clearImg();
//            ImgUtil.loadRes(view.getImgRight(), R.drawable.base_selector_view_photo);
        } else {
            view.setImgFile(picUrl);
//            ImgUtil.loadFile(view.getImgRight(), picUrl);
        }
    }

    private void showDialog() {
        View view = LayoutInflater.from(this).inflate(R.layout.certification_model_frame, null);
        ImageView viewModel = view.findViewById(R.id.iv_model);
        TextView download_txt_dilog = view.findViewById(R.id.download_txt_dilog);
        final NormalInfoDialog normalInfoDialog = new NormalInfoDialog()
                .setTitle("使用照片须知")
                .setContentView(view)
                .setDefRightBtn("拍照")
                .setLeftBtn("从相册选择")
                .setTitleMessage("证件详细信息清晰可读");
        if (flagPic == BUSINESS_LICENSE_PIC) {
            normalInfoDialog.setTitleMessage("要求：证件详细信息清晰可读");
            viewModel.setBackgroundDrawable(getResources().getDrawable(R.drawable.base_business_license_modelpic));
        } else if (flagPic == LETTER_ATTORNEY_PIC) {
            normalInfoDialog.setTitleMessage("");
            viewModel.setBackgroundDrawable(getResources().getDrawable(R.drawable.base_letter_attorney_modelpic));
            download_txt_dilog.setVisibility(View.VISIBLE);
            download_txt_dilog.setOnClickListener(new View.OnClickListener() {
                @Override
                public void onClick(View v) {
                    String url = "202107081028458112615.docx";
                    String url2 = "http://img.zczy56.com/" + url;
                    String[] list = new String[]{Manifest.permission.WRITE_EXTERNAL_STORAGE, Manifest.permission.READ_EXTERNAL_STORAGE};
                    CheckSelfPermissionDialog.storagePermissionDialog(HzCertificationStartActivity.this, new PermissionCallBack() {
                        @Override
                        public void onHasPermission() {
                            PermissionUtil.checkPermissions(HzCertificationStartActivity.this, "请允许访问您的存储空间", list, new PermissionCallBack() {
                                @Override
                                public void onHasPermission() {
                                    getViewModel().loadFile(url2, url,HzCertificationStartActivity.this);
                                    getViewModel().loadFile2(url2, url);
                                }
                            });
                        }
                    });


                }
            });
        } else if (flagPic == LEGALPERSON_IDCARD_PIC || flagPic == REGISTRANTIDCARD_PIC) {
            normalInfoDialog.setTitleMessage("身份证正面，必须字迹清晰");
            viewModel.setBackgroundDrawable(getResources().getDrawable(R.drawable.certification_idcard_model));
        }
        normalInfoDialog.show(HzCertificationStartActivity.this);
        normalInfoDialog.setNormalInfoDialogListener(new NormalInfoDialog.NormalInfoDialogListener() {
            @Override
            public void onClickDefRightBtn(@NotNull NormalInfoDialog dialog) {
                CheckSelfPermissionDialog.cameraPermissionDialog(HzCertificationStartActivity.this, new PermissionCallBack() {
                    @Override
                    public void onHasPermission() {
                        PermissionUtil.openAlbum(HzCertificationStartActivity.this,
                                new PermissionCallBack() {
                                    @Override
                                    public void onHasPermission() {
                                        ImageSelector.open(HzCertificationStartActivity.this, 1, true, selectPic);
                                        normalInfoDialog.dismiss();
                                    }
                                });
                    }
                });


            }

            @Override
            public void onClickLeftBtn(@NotNull NormalInfoDialog dialog) {
                super.onClickLeftBtn(dialog);
                CheckSelfPermissionDialog.cameraPermissionDialog(HzCertificationStartActivity.this, new PermissionCallBack() {
                    @Override
                    public void onHasPermission() {
                        PermissionUtil.openAlbum(HzCertificationStartActivity.this,
                                new PermissionCallBack() {
                                    @Override
                                    public void onHasPermission() {
                                        normalInfoDialog.dismiss();
                                        ImageSelector.open(HzCertificationStartActivity.this, 1, false, selectPic);

                                    }
                                });
                    }
                });

            }
        });
    }


    @LiveDataMatch(tag = "下载文件成功")
    public void downLoadSuccess(String path) {
        String paths = path;
        String pathO = paths.substring(paths.indexOf("/z"));
        showDialogToast("文件保存在" + pathO);
        CarOwnerFileWebviewActivity.startContentUI(HzCertificationStartActivity.this, path, "查看文件");
    }
}
