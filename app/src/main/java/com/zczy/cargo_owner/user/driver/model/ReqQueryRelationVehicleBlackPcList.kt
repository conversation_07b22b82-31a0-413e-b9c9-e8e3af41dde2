package com.zczy.cargo_owner.user.driver.model

import android.os.Parcelable
import com.zczy.comm.http.entity.BaseNewRequest
import com.zczy.comm.http.entity.BaseRsp
import com.zczy.comm.http.entity.PageList
import kotlinx.android.parcel.Parcelize

/**
 *@Desc 黑名单管理按照车牌号和添加时间查询
 *@User ssp
 *@Date 2023/7/19-9:37
 */
class ReqQueryRelationVehicleBlackPcList(
    var nowPage: Int = 1,
    var pageSize: Int = 10,
    var plateNumber: String? = null,
    var createdTimeStart: String? = null,
    var createdTimeEnd: String? = null,
    var consignorUserId: String? = null,
    var blackStatus: String? = null, //黑名单状态  0-正常 1-移除
) : BaseNewRequest<BaseRsp<PageList<RspQueryRelationVehicleBlackPcList>>>("mms-app/consignorCarrierRelation/queryRelationVehicleBlackAppList")

@Parcelize
data class RspQueryRelationVehicleBlackPcList(
    val id: String = "", //记录id
    val createdByName: String = "", //操作人
    val createdTime: String = "", //创建时间
    val plateNumber: String = "", //车牌号
    val plateNumberId: String = "", //ID
    var blackStatus: String? = null, //黑名单状态  0-正常 1-移除
): Parcelable