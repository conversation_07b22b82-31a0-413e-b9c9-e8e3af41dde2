package com.zczy.cargo_owner.user.evaluate;

/**
 * 功能描述:
 *
 * <AUTHOR>
 * @company 中储智运科技股份有限公司
 * @copyright （版权）中储南京智慧物流科技有限公司所有
 * @date 2019/3/5
 */
enum EnumRating {
    A(1, "非常差"), B(2, "差"), C(3, "一般"), D(4, "好"), E(5, "非常好"), N(0, "");
    public int key;
    public String value;

    EnumRating(int key, String value) {
        this.key = key;
        this.value = value;
    }

    public static EnumRating getEnumByKey(int key) {
        switch (key) {
            case 1:
                return A;
            case 2:
                return B;
            case 3:
                return C;
            case 4:
                return D;
            case 5:
                return E;
            default:
                return N;
        }
    }
}
