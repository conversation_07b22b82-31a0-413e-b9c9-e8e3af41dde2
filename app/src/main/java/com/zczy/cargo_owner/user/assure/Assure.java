package com.zczy.cargo_owner.user.assure;

import android.text.TextUtils;

public class Assure {
    /***运单ID*/
    String orderId;
    /***保障状态 1未保障  2保障中 3保障完成*/
    String guaranteeState;
    /***保障金额*/
    String guaranteeFee;
    /***支付时间*/
    String startTime;

    /*生效时间*/
    String createdTime;

    /*货物品种*/
    String policyClassName;

    public String getCreatedTime() {
        return createdTime;
    }

    public String getPolicyClassName() {
        return policyClassName;
    }

    public String getOrderId() {
        return orderId;
    }

    public String getGuaranteeState() {
        return guaranteeState;
    }

    public String getGuaranteeFee() {
        return guaranteeFee;
    }

    public String getStartTime() {
        return TextUtils.isEmpty(startTime)?"":startTime;
    }
}
