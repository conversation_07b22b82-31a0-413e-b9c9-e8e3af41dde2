package com.zczy.cargo_owner.user.info;

import android.annotation.SuppressLint;
import android.app.Activity;
import android.content.Context;
import android.content.Intent;
import android.graphics.Color;
import android.os.Bundle;
import androidx.annotation.NonNull;
import androidx.annotation.Nullable;
import android.text.TextUtils;
import android.view.View;
import android.widget.ImageView;
import android.widget.TextView;

import com.google.gson.Gson;
import com.sfh.lib.mvvm.annotation.LiveDataMatch;
import com.sfh.lib.ui.AbstractLifecycleActivity;
import com.zczy.cargo_owner.R;
import com.zczy.cargo_owner.libcomm.config.HttpConfig;
import com.zczy.cargo_owner.libcomm.widget.CheckSelfPermissionDialog;
import com.zczy.cargo_owner.user.certification.CertificationUtils;
import com.zczy.cargo_owner.user.city.ELocationCity;
import com.zczy.cargo_owner.user.info.model.ReqUpdateUseInfo;
import com.zczy.cargo_owner.user.info.model.UserInfoModel;
import com.zczy.comm.data.entity.EImage;
import com.zczy.comm.data.entity.EUser;
import com.zczy.comm.data.role.AutoHelper;
import com.zczy.comm.data.role.IRelation;
import com.zczy.comm.data.role.ViewStatus;
import com.zczy.comm.permission.PermissionCallBack;
import com.zczy.comm.permission.PermissionUtil;
import com.zczy.comm.ui.UtilStatus;
import com.zczy.comm.utils.imageselector.ImagePreviewActivity;
import com.zczy.comm.utils.imageselector.ImageSelector;
import com.zczy.comm.utils.imgloader.ImgUtil;
import com.zczy.comm.utils.imgloader.Options;
import com.zczy.comm.widget.inputv2.InputViewClick;

import java.util.ArrayList;
import java.util.List;

/**
 * 功能描述: 个人资料
 *
 * <AUTHOR> 孙飞虎
 * @company 中储智运科技股份有限公司
 * @copyright （版权）中储南京智慧物流科技有限公司所有
 * @date 2018/9/27
 */
public class UserInfoActivity extends AbstractLifecycleActivity<UserInfoModel> {
    public static void start(Context context) {
        Intent intent = new Intent(context, UserInfoActivity.class);
        context.startActivity(intent);
    }

    private ImageView ivHead;
    private TextView tvName;
    private TextView tvPhone;
    private TextView tvCity;
    private TextView tvIdentityAuto;

    private View lyCity;
    private InputViewClick inputViewRole;
    private View lyIdentityAuto;

    private IRelation mRelation;
    private EUser mUser;

    @Override
    protected void onCreate(@Nullable Bundle savedInstanceState) {
        super.onCreate(savedInstanceState);
        setContentView(R.layout.user_info_activity);
        UtilStatus.initStatus(this, Color.WHITE);
        ivHead = findViewById(R.id.ivHead);
        tvName = findViewById(R.id.tvName);
        tvPhone = findViewById(R.id.tvPhone);
        tvCity = findViewById(R.id.tvCity);
        tvIdentityAuto = findViewById(R.id.tvIdentityAuto);
        inputViewRole = findViewById(R.id.input_view_role);

        lyCity = findViewById(R.id.lyCity);
        lyIdentityAuto = findViewById(R.id.lyIdentityAuto);

        inputViewRole.setListener(new InputViewClick.Listener() {
            @Override
            public void onClick(int viewId, @NonNull InputViewClick view, @NonNull String content) {
                // 角色认证
                CertificationUtils.hzCertification(UserInfoActivity.this);
            }
        });
    }

    @Override
    protected void onResume() {
        super.onResume();
        this.getViewModel().queryInfo();
    }

    @Override
    protected void onActivityResult(int requestCode, int resultCode, Intent data) {
        super.onActivityResult(requestCode, resultCode, data);
        if (requestCode == 1000 && resultCode == Activity.RESULT_OK) {
            List<String> files = ImageSelector.obtainPathResult(data);
            this.getViewModel().saveHead(files.get(0));
        } else if (requestCode == 1002 && resultCode == Activity.RESULT_OK) {

            ELocationCity city = new Gson().fromJson(data.getStringExtra("City"), ELocationCity.class);
            ReqUpdateUseInfo req = new ReqUpdateUseInfo();
            req.setCityName(city.getProNm(), city.getCityNm(), city.getAreaNm());
            req.setCityCode(city.getProCode(), city.getCityCode(), city.getAreaCode());
            this.getViewModel().saveCity(req.buildCity());
        } else if (requestCode == 10009 && resultCode == Activity.RESULT_OK) {
            //删除头像
            this.getViewModel().saveHead("");
        }
    }

    public void onClickHeader(View v) {
        // 选择头像
        if (this.mUser == null) {
            return;
        }
        if (TextUtils.isEmpty(this.mUser.getUserHeadPic())) {
            //选择图片
            CheckSelfPermissionDialog.cameraPermissionDialog(UserInfoActivity.this, new PermissionCallBack() {
                @Override
                public void onHasPermission() {
                    PermissionUtil.openAlbum(UserInfoActivity.this,
                            new PermissionCallBack() {
                                @Override
                                public void onHasPermission() {
                                    ImageSelector.open(UserInfoActivity.this, 1, true, 1000);
                                }
                            });
                }
            });

        } else {
            // 查看大图
            EImage image = new EImage();
            image.setImageId(this.mUser.getUserHeadPic());
            List<EImage> list = new ArrayList<>(1);
            list.add(image);
            ImagePreviewActivity.start(this, list, 0, true, 10009);
        }
    }

    public void onClickPhone(View v) {
        if (TextUtils.equals("完善手机号",tvPhone.getText().toString())){
            UserRefinePhoneActivity.jumpPage(this);
        }else {
            // 选择手机号码
            UserEditPhoneOneActivity.start(this);
        }
    }

    /***
     * 简明信息成功
     * @param user
     */
    @SuppressLint("SetTextI18n")
    @LiveDataMatch
    public void onUserInfoSuccess(EUser user) {
        this.mUser = user;
        if (TextUtils.isEmpty(user.getUserHeadPic())) {
            ivHead.setImageResource(R.drawable.base_user_def);
        } else {
            ImgUtil.loadUrl(ivHead, HttpConfig.getUrlImage(user.getUserHeadPic()), Options.creator().setCircle(true).setError(R.drawable.base_user_def).setPlaceholder(R.drawable.base_user_def));
        }
        tvName.setText(user.getUserName());
        if (TextUtils.isEmpty(user.getMobile())) {
            tvPhone.setText("完善手机号");
        } else {
            tvPhone.setText(user.getMobile().substring(0, 3) + "****" + user.getMobile().substring(7, 11));
        }

        tvCity.setText(user.getCity());
        mRelation = user.getRelation();
        //角色认证
        this.getAutohName(user);
        this.checkViewRank(user);
        this.hideUI(user);
    }

    /***
     * 角色认证- 认证类型名称
     */
    private void getAutohName(EUser user) {

        //角色认证
        ViewStatus viewStatus = AutoHelper.checkViewStatus(user);
        inputViewRole.setContent(viewStatus.toString());
        if (viewStatus == ViewStatus.CHECK || viewStatus == ViewStatus.AUTOED || viewStatus == ViewStatus.PERSON1 || viewStatus == ViewStatus.PERSON4) {
            inputViewRole.setEnabled(false);
            inputViewRole.setArrowVisible(false);
        } else {
            inputViewRole.setEnabled(true);
            inputViewRole.setArrowVisible(true);
        }

        //1-高级货主 2-高级承运人 3-承运商 4-个体船舶 5-单位船舶 6-初级货主 7-初级承运人 8-初级船舶 9-经纪人 10 车老板
        if (mRelation.isShipper()) {
            inputViewRole.setTitle("货主认证");
        }

    }

    /***
     * 显示隐藏UI
     */
    private void hideUI(EUser user) {
        if (mRelation.isChildAccount()) {
            //不可编辑手机号码
//            tvPhone.setEnabled(false);
        } else {
            //角色认证
            inputViewRole.setVisibility(View.VISIBLE);
            //区域
            lyCity.setVisibility(View.VISIBLE);
            //身份认证
            lyIdentityAuto.setVisibility(View.VISIBLE);
        }
    }

    /***
     * 检查【身份认证】用户视图状态 [认证][审核中][已认证][未通过]
     * @param user
     */
    private void checkViewRank(EUser user) {

        //0 未认证  1  已认证 2  未通过 3  初级会员不可以让认证
        IRelation relation = user.getRelation();
        if (!relation.isSeniorVip()) {
            // 初级会员
            tvIdentityAuto.setText("初级会员不可以认证");
            tvIdentityAuto.setEnabled(false);
            return;
        }
        //系统实名认证状态 0-未认证 1-认证通过 2-认证未通过

        if (TextUtils.isEmpty(user.getVerifyStatus())) {
            tvIdentityAuto.setText("未认证");
            tvIdentityAuto.setEnabled(true);
            return;
        }
        if (TextUtils.equals("0", user.getVerifyStatus())) {
            tvIdentityAuto.setText("未认证");
            tvIdentityAuto.setEnabled(true);
            return;
        }
        if (TextUtils.equals("1", user.getVerifyStatus())) {
            tvIdentityAuto.setText("已认证");
            tvIdentityAuto.setCompoundDrawablesWithIntrinsicBounds(0, 0, 0, 0);
            tvIdentityAuto.setEnabled(false);
            return;
        }
        if (TextUtils.equals("2", user.getVerifyStatus())) {
            tvIdentityAuto.setText("未通过");
            tvIdentityAuto.setCompoundDrawablesWithIntrinsicBounds(R.drawable.base_warning, 0, R.drawable.base_right_arrow_black, 0);
            tvIdentityAuto.setEnabled(true);
        }
    }
}


