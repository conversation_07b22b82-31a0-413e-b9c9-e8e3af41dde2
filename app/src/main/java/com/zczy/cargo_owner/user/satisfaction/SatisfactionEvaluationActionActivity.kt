package com.zczy.cargo_owner.user.satisfaction

import android.annotation.SuppressLint
import android.content.Context
import android.content.Intent
import android.os.Bundle
import androidx.constraintlayout.widget.ConstraintLayout
import androidx.recyclerview.widget.GridLayoutManager
import androidx.recyclerview.widget.RecyclerView
import android.view.LayoutInflater
import android.view.View
import android.widget.EditText
import android.widget.TextView
import com.chad.library.adapter.base.BaseQuickAdapter
import com.chad.library.adapter.base.listener.OnItemClickListener
import com.sfh.lib.event.RxBusEventManager
import com.sfh.lib.mvvm.annotation.LiveDataMatch
import com.zczy.cargo_owner.R
import com.zczy.cargo_owner.user.evaluate.wight.RatingBarView
import com.zczy.comm.ui.BaseActivity
import com.zczy.comm.utils.dp2px
import com.zczy.comm.widget.itemdecoration.CommItemGirdDecoration
import com.zczy.cargo_owner.user.satisfaction.adapter.SatisfactionEvaluationDetailAdapter
import com.zczy.cargo_owner.user.satisfaction.modle.SatisfactionEvaluationActionModel
import com.zczy.cargo_owner.user.satisfaction.req.ReqSatisfactionEvaluationAction
import com.zczy.cargo_owner.user.satisfaction.req.RxSatisfactionSuccess
import com.zczy.cargo_owner.user.satisfaction.rsp.RspSatisfactionReason

/**
 *    author : Ssp
 *    e-mail : <EMAIL>
 *    date   : 2019/7/413:34
 *    desc   :客服满意度评价详情
 *    version: 1.0
 */

open class SatisfactionEvaluationActionActivity : BaseActivity<SatisfactionEvaluationActionModel>(),
    View.OnClickListener {

    companion object {
        @JvmStatic
        fun start(context: Context?, consultationId: String) {
            context ?: return
            val intent = Intent(context, SatisfactionEvaluationActionActivity::class.java)
            intent.putExtra("consultationId", consultationId)
            context.startActivity(intent)
        }
    }

    private var consultationId: String = ""
    private val swipeRefreshMoreLayout by lazy {
        findViewById<androidx.recyclerview.widget.RecyclerView>(
            R.id.swipe_refresh_more_layout
        )
    }
    private val tvAccept by lazy { findViewById<TextView>(R.id.tv_accept) }
    private val tvOrder by lazy { findViewById<TextView>(R.id.tv_order) }
    private var tvEvaluationBottom: TextView? = null
    private var cl2: ConstraintLayout? = null
    private var sizeTv: TextView? = null
    private var noteTv: EditText? = null
    private var rbReceipt: RatingBarView? = null
    private var vBottomEmpty: View? = null
    private var mAdapter = SatisfactionEvaluationDetailAdapter()

    /**
     * 请求参数
     */
    private var reqSatisfactionEvaluationAction: ReqSatisfactionEvaluationAction =
        ReqSatisfactionEvaluationAction()

    override fun getLayout(): Int {
        return R.layout.satisfacton_evaluation_action_activity
    }

    override fun initData() {
        swipeRefreshMoreLayout.apply {
            layoutManager = androidx.recyclerview.widget.GridLayoutManager(
                this@SatisfactionEvaluationActionActivity,
                2
            )
            addItemDecoration(CommItemGirdDecoration(dp2px(5F)))
            addOnItemTouchListener(menuTouchListener)
            initHeaderView()
            initFooterView()
            adapter = mAdapter
        }
    }

    /**
     * 菜单 点击
     */
    private val menuTouchListener = object : OnItemClickListener() {
        override fun onSimpleItemClick(adapter: BaseQuickAdapter<*, *>, view: View, position: Int) {
            val bean: RspSatisfactionReason = adapter.data[position] as RspSatisfactionReason
            mAdapter.setSelected(bean)
            reqSatisfactionEvaluationAction.evaluateReason = bean.evaluateCode
        }
    }

    @SuppressLint("SetTextI18n")
    override fun bindView(bundle: Bundle?) {
        consultationId = intent.getStringExtra("consultationId") ?: ""
        tvOrder.text = "业务号：$consultationId"
        tvAccept.setOnClickListener(this)
    }

    private fun initFooterView() {
        val footerView = LayoutInflater.from(this)
            .inflate(R.layout.satisfaction_evaluation_action_footer, swipeRefreshMoreLayout, false)
        sizeTv = footerView.findViewById(R.id.sizeTv)
        noteTv = footerView.findViewById(R.id.noteTv)
        vBottomEmpty = footerView.findViewById(R.id.v_bottom_empty)
        mAdapter.removeAllFooterView()
        mAdapter.addFooterView(footerView)
    }

    private fun initHeaderView() {
        val headerView = LayoutInflater.from(this)
            .inflate(R.layout.satisfaction_evaluation_action_header, swipeRefreshMoreLayout, false)
        cl2 = headerView.findViewById(R.id.cl_2)
        tvEvaluationBottom = headerView.findViewById(R.id.tv_evaluation_bottom)
        rbReceipt = headerView.findViewById(R.id.rb_receipt)
//        rbReceipt?.apply {
//            setOnRatingListener(object : RatingBarView.OnRatingListener {
//                override fun onRating(bindObject: Any?, RatingScore: Int) {
//                    tvEvaluationBottom?.visibility = View.VISIBLE
//                    tvEvaluationBottom?.text = EnumEvaluation.getEnumByKey(RatingScore).value
//                    reqSatisfactionEvaluationAction.evaluateLevel = RatingScore
//                    when {
//                        RatingScore <= 2 -> {
//                            //1星和2星
//                            mAdapter.clearSelectData()
//                            initCl2(true)
//                            initAdapterData(initStartContent1())
//                            initEmpty(false)
//                        }
//
//                        RatingScore == 3 -> {
//                            //3星
//                            mAdapter.clearSelectData()
//                            initCl2(false)
//                            initEmpty(true)
//                        }
//
//                        else -> {
//                            //4星和5星
//                            mAdapter.clearSelectData()
//                            initCl2(true)
//                            initAdapterData(initStartContent2())
//                            initEmpty(false)
//                        }
//                    }
//                }
//            }
//        }
        mAdapter.removeAllHeaderView()
        mAdapter.addHeaderView(headerView)
    }

    override fun onClick(view: View) {
        when (view.id) {
            R.id.tv_accept -> {
                //提交评价信息
                reqSatisfactionEvaluationAction.consultationId = this.consultationId
                reqSatisfactionEvaluationAction.message = noteTv?.text.toString().trim()
                viewModel?.doEvaluation(reqSatisfactionEvaluationAction)
            }
        }
    }

    /**
     * 控制基于哪些原因基于评价view
     * @param flag true展示布局
     */
    private fun initCl2(flag: Boolean) {
        when {
            flag -> {
                cl2?.visibility = View.VISIBLE
                swipeRefreshMoreLayout.visibility = View.VISIBLE
            }

            else -> {
                cl2?.visibility = View.GONE
                swipeRefreshMoreLayout.visibility = View.VISIBLE
            }
        }

    }

    /**
     * 控制底部空白View
     */
    private fun initEmpty(flag: Boolean) {
        when {
            flag -> {
                vBottomEmpty?.visibility = View.VISIBLE
            }

            else -> {
                vBottomEmpty?.visibility = View.GONE
            }
        }
    }

    /**
     * 1 星 2 星评价内容
     */
    private fun initStartContent1(): ArrayList<RspSatisfactionReason> {
        val data = arrayListOf<RspSatisfactionReason>()
        data.add(RspSatisfactionReason("处理时间过长", 1))
        data.add(RspSatisfactionReason("服务态度", 2))
        data.add(RspSatisfactionReason("系统因素", 3))
        data.add(RspSatisfactionReason("承诺未兑现", 4))
        data.add(RspSatisfactionReason("问题未解决", 5))
        return data
    }

    /**
     * 4 星 5 星评价内容
     */
    private fun initStartContent2(): ArrayList<RspSatisfactionReason> {
        val data = arrayListOf<RspSatisfactionReason>()
        data.add(RspSatisfactionReason("服务态度好", 6))
        data.add(RspSatisfactionReason("解决速度快", 7))
        data.add(RspSatisfactionReason("服务专业", 8))
        return data
    }

    /**
     * 初始化adapter数据
     */
    private fun initAdapterData(data: ArrayList<RspSatisfactionReason>) {
        mAdapter.setNewData(data)
    }

    @LiveDataMatch
    open fun onSuccess(msg: String) {
        showToast(msg)
        val data = RxSatisfactionSuccess(true)
        RxBusEventManager.postEvent(data)
        finish()
    }
}