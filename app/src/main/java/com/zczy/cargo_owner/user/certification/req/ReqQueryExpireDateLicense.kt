package com.zczy.cargo_owner.user.certification.req

import com.zczy.comm.http.entity.BaseNewRequest
import com.zczy.comm.http.entity.BaseRsp
import com.zczy.comm.http.entity.ResultData

/** 功能描述:
 *
 * <AUTHOR> 贾发展
 * @company 中储智运科技股份有限公司
 * @copyright （版权）中储南京智慧物流科技有限公司所有
 * @date 2020/2/18
 */
class ReqQueryExpireDateLicense(
) : BaseNewRequest<BaseRsp<DataList<ExpireDate>>>("mms-app/member/queryExpireDateLicense")

class ExpireDate {
    var type: String = ""
    var vehicleId: String = ""
}


open class DataList<E> : ResultData() {

    var data: List<E>? = null


}
