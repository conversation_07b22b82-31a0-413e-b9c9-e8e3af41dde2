package com.zczy.cargo_owner.user.exception.req

import com.zczy.comm.http.entity.BaseNewRequest
import com.zczy.comm.http.entity.BaseRsp
import com.zczy.comm.http.entity.PageList
import java.io.Serializable

/**
 * 功能描述: 运单异常管理
 * <AUTHOR>
 * @date 2023/4/3-13:54
 */

class ReqWayBillList(
    var dealType: String? = null, //1：待处理2：已处理
    var nowPage: Int = 1,
    var pageSize: Int = 10,
) : BaseNewRequest<BaseRsp<PageList<WaybillException>>>("oms-app/order/consignor/exception/queryExceptionOrderAuditList")

class WaybillException(
    val id: String = "",
    val orderId: String = "",
    val haveFranchiser: String = "",
    val franchiserCompany: String = "",
    val franchiserMobile: String = "",
    val submitterType: String = "",
    val needFlag: String = "",
    val exceptionType: String = "",
    val message: String = "",
    val handleOpinion: String = "",
    val examineState: String = "",
    val examineOpinion: String = "",
    val remarks: String = "",
    val createdBy: String = "",
    val createdTime: String = "",
    val dealTime: String = "",
    val examineBy: String = "",
    val examineTime: String = "",
    val dealUser: String = "",
    val lastUptBy: String = "",
    val lastUptTime: String = "",
    val deleteFlag: String = "",
    val userId: String = "",
    val childId: String = "",
    val nowPage: String = "",
    val pageSize: String = "",
    val totalPage: String = "",
    val totalSize: String = "",
    val dealType: String = "",
    val driverUserName: String = "",
    val driverMobile: String = "",
    val plateNumber: String = "",
    val examineStateName: String = "",
    val despatch: String = "",
    val despatchPro: String = "",
    val despatchCity: String = "",
    val despatchDis: String = "",
    val despatchPlace: String = "",
    val deliver: String = "",
    val deliverPro: String = "",
    val deliverCity: String = "",
    val deliverDis: String = "",
    val deliverPlace: String = "",
    val remarkLength: String = "",
    val fileUrl: String = "",
    val fileName: String = "",
    val uploadType: String = "",
    val consignorCompany: String = "",
    val despatchName: String = "",
    val despatchMobile: String = "",
    val monitorId: String = "",
    val carrierId: String = "",
    val type: String = "",
    var LTLOrderFlag: String = "", //零担标识 1 是
    var businessSource: String = "",//业务来源：0 网络货运 1 多式联运 2商贸质押单
    val showUrgeFlag: String = "", // 1 的时候展示催促按钮
) : Serializable