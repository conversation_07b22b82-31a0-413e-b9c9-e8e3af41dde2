package com.zczy.cargo_owner.user.questionnaire;

import com.chad.library.adapter.base.BaseQuickAdapter;
import com.chad.library.adapter.base.BaseViewHolder;
import com.zczy.cargo_owner.R;

import java.text.DateFormat;
import java.text.ParseException;
import java.text.SimpleDateFormat;
import java.util.Date;

/**
 * 功能描述:
 *
 * <AUTHOR>
 * @company 中储智运科技股份有限公司
 * @copyright （版权）中储南京智慧物流科技有限公司所有
 * @date 2018/12/12
 */
public class QuestionNaireSurveyAdapter extends BaseQuickAdapter<QusetionNaireBean, BaseViewHolder> {
    public QuestionNaireSurveyAdapter() {
        super(R.layout.questionnaire_survey_item);
    }

    @Override
    protected void convert(BaseViewHolder helper, QusetionNaireBean item) {
        helper.setText(R.id.tv_train_content, item.getActivityName()).
                setText(R.id.tv_time, "调研时间：" + getStringByFormat(item.getStartTime(),
                        "yyyy-MM-dd", "yyyy.MM.dd") + "-" +
                        getStringByFormat(item.getEndTime(), "yyyy-MM-dd", "yyyy.MM.dd"));
    }


    /**
     * 获取任意格式指定日期的指定格式，必须指定输入的日期格式
     *
     * @param strDate     指定日期
     * @param inputFormat 输入日期的格式
     * @param outFormat   输出日期的格式
     * @return
     */
    public static String getStringByFormat(String strDate, String inputFormat,
                                           String outFormat) {
        String mDateTime = null;
        try {
            DateFormat format1 = new SimpleDateFormat(inputFormat);
            Date d1 = format1.parse(strDate);
            format1 = new SimpleDateFormat(outFormat);
            mDateTime = format1.format(d1);
        } catch (ParseException e) {
            e.printStackTrace();
        }
        return mDateTime;
    }
}

