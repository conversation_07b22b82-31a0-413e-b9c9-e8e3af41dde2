package com.zczy.cargo_owner.user;

import android.content.Context;
import android.content.Intent;
import android.graphics.Bitmap;
import android.graphics.BitmapFactory;
import android.graphics.Color;
import android.net.Uri;
import android.os.Bundle;
import android.os.Environment;
import android.text.TextUtils;
import android.widget.FrameLayout;

import com.sfh.lib.ui.AbstractLifecycleActivity;
import com.tencent.smtt.sdk.TbsReaderView;
import com.zczy.cargo_owner.R;
import com.zczy.comm.ui.UtilStatus;
import com.zczy.comm.widget.AppToolber;
import com.zczy.lib_zshare.ZShare;
import com.zczy.lib_zshare.share.ShareConstants;
import com.zczy.lib_zshare.share.ShareInfo;

import java.io.File;
import java.util.ArrayList;
import java.util.List;

public class CarOwnerFileWebviewActivity extends AbstractLifecycleActivity {

    private FrameLayout open_officefile;
    private TbsReaderView mTbsReaderView;
    private String path;

    public static void startContentUI(Context context, String path, String title) {
        Intent intent = new Intent(context, CarOwnerFileWebviewActivity.class);
        intent.putExtra("title", title);
        intent.putExtra("path", path);
        context.startActivity(intent);
    }

    @Override
    protected void onCreate(Bundle savedInstanceState) {
        super.onCreate(savedInstanceState);
        setContentView(R.layout.carowner_file_webview_activity);
        UtilStatus.initStatus(this, Color.WHITE);
        open_officefile = (FrameLayout) findViewById(R.id.open_officefile);
        AppToolber appToolber = findViewById(R.id.appToolber);
        path = getIntent().getStringExtra("path");
        final String title = getIntent().getStringExtra("title");
        if (title != null && !title.isEmpty()) {
            appToolber.setTitle(title);
        }
        appToolber.setRightOnClickListener(v -> {
//            Intent intent = getWordFileIntent(path);
//            try {
//                startActivity(intent);
//            } catch (Exception e) {
//                e.printStackTrace();
//            }
            Bitmap bitmap = BitmapFactory.decodeResource(getResources(), com.zczy.lib_zshare.R.mipmap.icon_wechat_friend);

            List<String> items = new ArrayList<>();
            items.add(ShareConstants.SHARE_SENCE_FRIEND);

            ShareInfo info = new ShareInfo();
            info.title = "授权委托书模板";
            info.content = "";
            info.thumbnail = bitmap;
            info.shareSenceItems = items;
            info.shareType = ShareConstants.SHARE_WXMSG_TYPE_FILE;
//                info.filePath =  getExternalFilesDir(null) + "/img/IMG_20210608_084108.jpg";
//                info.filePath =  getExternalFilesDir(null) + "/img/aaa.txt";
            info.filePath = Environment.getExternalStorageDirectory() + "/zczyOwenr/202107081028458112615.docx";
            info.authority = getResources().getString(R.string.file_authority);

            ZShare.share(this, info);
        });
        initTbs();
    }

    //android获取一个用于打开Word文件的intent
    public static Intent getWordFileIntent(String param) {
        Intent intent = new Intent("android.intent.action.VIEW");
        intent.addCategory("android.intent.category.DEFAULT");
        intent.addFlags(Intent.FLAG_ACTIVITY_NEW_TASK);
        Uri uri = Uri.fromFile(new File(param));
        intent.setDataAndType(uri, "application/msword");
        return intent;
    }

    private void initTbs() {
        mTbsReaderView = new TbsReaderView(this, new TbsReaderView.ReaderCallback() {
            @Override
            public void onCallBackAction(Integer integer, Object o, Object o1) {

            }
        });
        if (path != null && !TextUtils.isEmpty(path)) {
            //增加下面一句解决没有TbsReaderTemp文件夹存在导致加载文件失败
            String bsReaderTemp = "/storage/emulated/0/TbsReaderTemp";
            File bsReaderTempFile = new File(bsReaderTemp);
            if (!bsReaderTempFile.exists()) {
                bsReaderTempFile.mkdir();
            }
            //通过bundle把文件传给x5,打开的事情交由x5处理
            Bundle bundle = new Bundle();
            //传递文件路径
            bundle.putString("filePath", path);
            //加载插件保存的路径
            bundle.putString("tempPath", Environment.getExternalStorageDirectory() + "/" + "TbsReaderTemp");

            //加载文件前的初始化工作,加载支持不同格式的插件
            boolean b = mTbsReaderView.preOpen(getFileType(path), false);
            if (b) {
                this.mTbsReaderView.openFile(bundle);
            }
            open_officefile.addView(mTbsReaderView);
        }
    }

    /***
     * 获取文件类型
     *
     * @param path 文件路径
     * @return 文件的格式
     */
    private String getFileType(String path) {
        String str = "";

        if (TextUtils.isEmpty(path)) {
            return str;
        }
        int i = path.lastIndexOf('.');
        if (i <= -1) {
            return str;
        }
        str = path.substring(i + 1);
        return str;
    }


    @Override
    protected void onDestroy() {
        super.onDestroy();
        if (mTbsReaderView != null) {
            mTbsReaderView.onStop();
        }
    }

}
