package com.zczy.cargo_owner.user.integral.dialog

import android.os.Bundle
import android.view.View
import android.widget.ImageView
import android.widget.TextView
import com.zczy.cargo_owner.R
import com.zczy.cargo_owner.libcomm.utils.DateUtils
import com.zczy.cargo_owner.user.integral.req.UserSign
import com.zczy.comm.ui.BaseDialog

/** 功能描述:
 *
 * <AUTHOR> 贾发展
 * @company 中储智运科技股份有限公司
 * @copyright （版权）中储南京智慧物流科技有限公司所有
 * @date 2020/3/23
 */
class UserIntegralSignInDialog : BaseDialog() {
    var mData: UserSign? = null
    override fun bindView(view: View, bundle: Bundle?) {
        val integralClose = view.findViewById<ImageView>(R.id.iv_integral_close)
        val tvOK = view.findViewById<TextView>(R.id.tv_ok)
        val integralTime = view.findViewById<TextView>(R.id.tv_integral_time)
        val integral = view.findViewById<TextView>(R.id.tv_integral)
        integralTime.text = DateUtils.getCurrentDate(DateUtils.dateFormatYMD4)
        integral.text = "积分 +" + mData?.signIntegral
        integralClose.setOnClickListener { dismiss() }
        tvOK.setOnClickListener {
            dismiss()
            if (mListener != null) {
                mListener?.onSignOk()
            }
        }
    }

    override fun getDialogTag(): String {
        return "UserIntegralSignInDialog"
    }

    override fun getDialogLayout(): Int {
        return R.layout.user_integral_signin_dialog
    }


    fun setUserSignData(data: UserSign): UserIntegralSignInDialog {
        mData = data
        return this
    }

    var mListener: OnUserSignListener? = null

    interface OnUserSignListener {
        fun onSignOk()
    }

    fun setUserSignListener(listener: OnUserSignListener): UserIntegralSignInDialog {
        this.mListener = listener
        return this
    }
}