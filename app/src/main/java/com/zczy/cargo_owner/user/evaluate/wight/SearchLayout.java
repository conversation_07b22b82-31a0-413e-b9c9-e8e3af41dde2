package com.zczy.cargo_owner.user.evaluate.wight;

import android.content.Context;
import androidx.annotation.IdRes;
import androidx.annotation.Nullable;
import android.text.Editable;
import android.text.TextUtils;
import android.text.TextWatcher;
import android.util.AttributeSet;
import android.view.View;
import android.view.inputmethod.EditorInfo;
import android.view.inputmethod.InputMethodManager;
import android.widget.EditText;
import android.widget.ImageView;
import android.widget.LinearLayout;
import android.widget.TextView;

import com.zczy.cargo_owner.R;
import com.zczy.comm.utils.ResUtil;


/**
 * <AUTHOR> 宋双朋
 * @description (重卡租赁详情 头部布局)
 * @company 中储智运科技股份有限公司
 * @copyright （版权）中储南京智慧物流科技有限公司所有
 * @time 2018/7/16 16:06
 */

public class SearchLayout extends LinearLayout implements View.OnClickListener, TextWatcher {

    private ImageView searchIv;
    private ImageView imgClear;
    private EditText searchEt;
    private TextView searchTv;
    SeachContentListener seachContentListener;

    public SearchLayout(Context context) {
        super(context);
        this.init();
    }

    public SearchLayout(Context context, @Nullable AttributeSet attrs) {

        super(context, attrs);
        this.init();
    }

    public SearchLayout(Context context, @Nullable AttributeSet attrs, int defStyleAttr) {

        super(context, attrs, defStyleAttr);
        this.init();
    }

    /**
     * 设置搜索回调
     *
     * @param searchListener
     */
    public void setOnSearchListener(SeachContentListener searchListener) {
        this.seachContentListener = searchListener;
    }

    @Override
    public void onClick(View view) {
        if (view.getId() == R.id.img_clear) {
            searchEt.setText("");
        } else if (view.getId() == R.id.search_tv) {
            if (seachContentListener != null) {
                seachContentListener.doCancelSearch();
            }
        }
    }

    @Override
    public void beforeTextChanged(CharSequence charSequence, int i, int i1, int i2) {

    }

    @Override
    public void onTextChanged(CharSequence charSequence, int i, int i1, int i2) {
    }


    int dp33 = ResUtil.dp2px(33F);
    int dp40 = ResUtil.dp2px(40F);
    int dp10 = ResUtil.dp2px(10F);

    @Override
    public void afterTextChanged(Editable editable) {
        if (TextUtils.isEmpty(editable)) {
            this.searchIv.setVisibility(VISIBLE);
            imgClear.setVisibility(GONE);
            searchEt.setPadding(dp33, 0, dp40, 0);
        } else {
            this.searchIv.setVisibility(GONE);
            imgClear.setVisibility(VISIBLE);
            searchEt.setPadding(dp10, 0, dp40, 0);
        }
    }

    public interface SeachContentListener {
        void doSearchListener(String searchContent);

        void doCancelSearch();
    }

    private void init() {
        inflate(getContext(), R.layout.search_layout, this);
        this.setOrientation(VERTICAL);
        this.searchIv = $(R.id.search_iv);
        this.searchEt = $(R.id.search_et);
        this.searchTv = $(R.id.search_tv);
        this.imgClear = $(R.id.img_clear);
        this.searchEt.addTextChangedListener(this);
        this.searchTv.setOnClickListener(this);
        this.imgClear.setOnClickListener(this);
        searchEt.setOnEditorActionListener((v, actionId, event) -> {
            if (actionId == EditorInfo.IME_ACTION_SEARCH) {
                String searchContent = searchEt.getText().toString().trim();
                hideKeyboard(searchEt);
                if (seachContentListener != null) {
                    seachContentListener.doSearchListener(searchContent);
                }
                return true;
            }
            return false;
        });
    }

    private <T extends View> T $(@IdRes int resId) {

        return (T) super.findViewById(resId);
    }

    /**
     * 设置搜索提示信息
     *
     * @param text
     */
    public void setSearchEtHintText(String text) {
        if (searchEt == null) {
            return;
        }
        searchEt.setHint(text);
    }

    public void setSearchEtContent(String text) {
        if (searchEt == null) {
            return;
        }
        searchEt.setText(text);
    }

    /**
     * 隐藏软键盘 可以和{@link #(EditText, boolean)}搭配使用，进行键盘的显示隐藏控制。
     *
     * @param view 当前页面上任意一个可用的view
     */
    public boolean hideKeyboard(final View view) {
        if (null == view)
            return false;

        InputMethodManager inputManager = (InputMethodManager) view.getContext().getApplicationContext()
                .getSystemService(Context.INPUT_METHOD_SERVICE);
        // 即使当前焦点不在editText，也是可以隐藏的。
        return inputManager.hideSoftInputFromWindow(view.getWindowToken(),
                InputMethodManager.HIDE_NOT_ALWAYS);
    }
}
