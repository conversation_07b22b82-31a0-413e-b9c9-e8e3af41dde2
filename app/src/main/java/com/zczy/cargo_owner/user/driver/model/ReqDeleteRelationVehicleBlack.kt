package com.zczy.cargo_owner.user.driver.model

import com.zczy.comm.http.entity.BaseNewRequest
import com.zczy.comm.http.entity.BaseRsp
import com.zczy.comm.http.entity.PageList
import com.zczy.comm.http.entity.ResultData

/**
 *@Desc 黑名单删除车辆接口
 *@User ssp
 *@Date 2023/7/19-9:37
 */
class ReqDeleteRelationVehicleBlack(
    var id: String? = null, // 车牌号
    var consignorUserId: String? = null, // 货主id
    var remark: String? = null, //备注
) : BaseNewRequest<BaseRsp<ResultData>>("mms-app/consignorCarrierRelation/deleteRelationVehicleBlack")
