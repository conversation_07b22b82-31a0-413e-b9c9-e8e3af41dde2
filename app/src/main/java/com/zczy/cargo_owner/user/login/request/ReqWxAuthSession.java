package com.zczy.cargo_owner.user.login.request;

import com.sfh.lib.AppCacheManager;
import com.sfh.lib.utils.UtilTool;
import com.zczy.comm.http.entity.BaseNewRequest;
import com.zczy.comm.http.entity.BaseRsp;
import com.zczy.cargo_owner.user.login.entity.WxAuthSession;

/**
 * 功能描述:
 *
 * <AUTHOR> 孙飞虎
 * @company 中储智运科技股份有限公司
 * @copyright （版权）中储南京智慧物流科技有限公司所有
 * @date 2019/1/24
 */
public class ReqWxAuthSession extends BaseNewRequest<BaseRsp<WxAuthSession>> {

    private String code;//微信code
    private String mobile;//手机号码
    String mac = UtilTool.getMacDeviceId(AppCacheManager.getApplication());

    public String getMobile() {
        return mobile;
    }

    public void setMobile(String mobile) {
        this.mobile = mobile;
    }

    public ReqWxAuthSession(String code) {
        super("mms-app/platform/weiXiOwner/getWxOauthSessionOwner");
        this.code = code;
    }
}


