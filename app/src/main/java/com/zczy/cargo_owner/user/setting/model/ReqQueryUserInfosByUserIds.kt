package com.zczy.cargo_owner.user.setting.model

import com.zczy.comm.http.entity.BaseNewRequest
import com.zczy.comm.http.entity.BaseRsp
import com.zczy.comm.http.entity.PageList

/**
 *  desc: 根据用户id集合查询用户简明信息
 *  user: ssp
 *  time: 2024/7/25 9:30
 */
class ReqQueryUserInfosByUserIds(
    var userIdList: MutableList<String> = mutableListOf()
) : BaseNewRequest<BaseRsp<PageList<RspQueryUserInfosByUserIds>>>("mms-app/mms/upgrade/queryUserInfosByUserIds")

class RspQueryUserInfosByUserIds(
    val mainUserNm: String? = null, //主用户名称
    val nooType: String? = null, //是否主用户 0-否 1-是
    val userHeadPic: String? = null, //头像地址
    val userId: String? = null, // 用户id
    val userNm: String? = null, //用户名
    val mobile: String? = null, //手机号
)