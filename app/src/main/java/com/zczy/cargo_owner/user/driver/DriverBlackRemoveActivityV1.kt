package com.zczy.cargo_owner.user.driver

import android.annotation.SuppressLint
import android.app.Activity
import android.content.Intent
import android.os.Bundle
import android.text.*
import android.view.View
import com.sfh.lib.event.RxBusEventManager
import com.sfh.lib.mvvm.annotation.LiveDataMatch
import com.sfh.lib.ui.dialog.DialogBuilder
import com.zczy.cargo_owner.R
import com.zczy.cargo_owner.user.driver.model.*
import com.zczy.comm.ui.BaseActivity
import kotlinx.android.synthetic.main.driver_black_list_remove_activity_v1.*
/**
 * @Desc 黑名单移除司机
 * @User zzf
 * @Date 2024/8/2
 */
class DriverBlackRemoveActivityV1 : BaseActivity<DriverBlacklistModel?>() {

    private val black by lazy { intent.getParcelableExtra<EDriverBlack>("black") ?: null }

    companion object {
        @JvmStatic
        fun jumpPage(activity: Activity?, black: EDriverBlack?) {
            val starter = Intent(activity, DriverBlackRemoveActivityV1::class.java)
            starter.putExtra("black", black)
            activity?.startActivityForResult(starter, 1000)
        }
    }

    override fun getLayout(): Int {
        return R.layout.driver_black_list_remove_activity_v1
    }

    override fun bindView(bundle: Bundle?) {
        bindClickEvent(tv_add)
        noteTv.filters = arrayOf<InputFilter>(InputFilter.LengthFilter(200))
        noteTv.addTextChangedListener(object : TextWatcher {
            @SuppressLint("SetTextI18n")
            override fun afterTextChanged(s: Editable) {
                if (s.length > 200) {
                    return
                }
                sizeTv.text = "${s.length}/200"
            }

            override fun beforeTextChanged(s: CharSequence?, start: Int, count: Int, after: Int) {
            }

            override fun onTextChanged(s: CharSequence?, start: Int, before: Int, count: Int) {
            }
        })
    }

    override fun onSingleClick(v: View) {
        super.onSingleClick(v)
        when (v.id) {
            R.id.tv_add -> {
                val builder = DialogBuilder()
                builder.title = "黑名单移除提示"
                builder.message = "确认将该记录从黑名单移除么?"
                builder.setOkListener { dialogInterface, _ ->
                    dialogInterface.dismiss()
                    getViewModel(DriverBlacklistModel::class.java).del(ReqDelDriverBlack(id = black?.id, remark = noteTv.text.toString()))
                }
                showDialog(builder)
            }
        }
    }

    override fun initData() {
        black?.apply {
            et_name.content = memberName.toString()
            et_phone.content = mobile.toString()
        }
    }

    @LiveDataMatch
    open fun onRefresh() {
        RxBusEventManager.postEvent(RxAddRelationVehicleBlack(success = true))
        finish()
    }
}
