package com.zczy.cargo_owner.user.certification.req;

import com.zczy.comm.http.entity.BaseNewRequest;
import com.zczy.comm.http.entity.BaseRsp;
import com.zczy.comm.http.entity.ResultData;

public class ReqHzCertification extends BaseNewRequest<BaseRsp<ResultData>> {


    public ReqHzCertification() {
        super("mms-app/platform/authentication/userPromote");
    }

    private String contacter; //联系人
    private String contacterPhone; //联系人手机号码
    private String companyName;// 公司名称
    private String taxId;//统一社会信用代码
    private String busiLicUrl; //营业执照图片
    private String isEntrustRegister; //委托注册关开值
    private String legalIdCardUrl; //法人身份证照片正面照
    private String nonLegalIdCardUrl; //非法人注册身份证图片正面
    private String nonLegalAuthUrl; //非法人注册授权委托书
    private String receiptTemplateUrl; // 回单样板照片
    private String receptRemark; //  回单备注
    private String promoteType;
    private boolean isNeedBusinessLicense;


    public boolean isNeedBusinessLicense() {
        return isNeedBusinessLicense;
    }

    public void setNeedBusinessLicense(boolean needBusinessLicense) {
        isNeedBusinessLicense = needBusinessLicense;
    }

    public String getTaxId() {
        return taxId;
    }

    public void setTaxId(String taxId) {
        this.taxId = taxId;
    }

    public String getPromoteType() {
        return promoteType;
    }

    public void setPromoteType(String promoteType) {
        this.promoteType = promoteType;
    }

    public String getReceiptTemplateUrl() {
        return receiptTemplateUrl;
    }

    public void setReceiptTemplateUrl(String receiptTemplateUrl) {
        this.receiptTemplateUrl = receiptTemplateUrl;
    }

    public String getReceptRemark() {
        return receptRemark;
    }

    public void setReceptRemark(String receptRemark) {
        this.receptRemark = receptRemark;
    }

    public String getContacter() {
        return contacter;
    }

    public void setContacter(String contacter) {
        this.contacter = contacter;
    }

    public String getContacterPhone() {
        return contacterPhone;
    }

    public void setContacterPhone(String contacterPhone) {
        this.contacterPhone = contacterPhone;
    }

    public String getCompanyName() {
        return companyName;
    }

    public void setCompanyName(String companyName) {
        this.companyName = companyName;
    }

    public String getBusiLicUrl() {
        return busiLicUrl;
    }

    public void setBusiLicUrl(String busiLicUrl) {
        this.busiLicUrl = busiLicUrl;
    }

    public String getIsEntrustRegister() {
        return isEntrustRegister;
    }

    public void setIsEntrustRegister(String isEntrustRegister) {
        this.isEntrustRegister = isEntrustRegister;
    }

    public String getLegalIdCardUrl() {
        return legalIdCardUrl;
    }

    public void setLegalIdCardUrl(String legalIdCardUrl) {
        this.legalIdCardUrl = legalIdCardUrl;
    }

    public String getNonLegalIdCardUrl() {
        return nonLegalIdCardUrl;
    }

    public void setNonLegalIdCardUrl(String nonLegalIdCardUrl) {
        this.nonLegalIdCardUrl = nonLegalIdCardUrl;
    }

    public String getNonLegalAuthUrl() {
        return nonLegalAuthUrl;
    }

    public void setNonLegalAuthUrl(String nonLegalAuthUrl) {
        this.nonLegalAuthUrl = nonLegalAuthUrl;
    }
}
