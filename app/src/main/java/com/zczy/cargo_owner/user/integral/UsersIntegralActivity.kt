package com.zczy.cargo_owner.user.integral

import android.content.Context
import android.content.Intent
import android.graphics.Color
import android.os.Bundle
import androidx.recyclerview.widget.GridLayoutManager
import androidx.recyclerview.widget.LinearLayoutManager
import androidx.recyclerview.widget.RecyclerView
import android.text.SpannableString
import android.text.SpannableStringBuilder
import android.text.Spanned
import android.text.TextUtils
import android.text.style.AbsoluteSizeSpan
import android.text.style.ForegroundColorSpan
import android.view.View
import android.widget.ImageView
import android.widget.LinearLayout
import android.widget.TextView
import com.aspsine.swipetoloadlayout.OnRefreshListener
import com.aspsine.swipetoloadlayout.SwipeToLoadLayout
import com.jaeger.library.StatusBarUtil
import com.sfh.lib.mvvm.annotation.LiveDataMatch
import com.sfh.lib.ui.AbstractLifecycleActivity
import com.zczy.cargo_owner.R
import com.zczy.cargo_owner.libcomm.config.HttpConfig
import com.zczy.cargo_owner.user.integral.dialog.UserIntegralSignInDialog
import com.zczy.cargo_owner.user.integral.modle.IntegralModel
import com.zczy.cargo_owner.user.integral.req.SignCycle
import com.zczy.cargo_owner.user.integral.req.SignGuideList
import com.zczy.cargo_owner.user.integral.req.UserSign
import com.zczy.comm.CommServer
import com.zczy.comm.data.entity.EUser
import com.zczy.comm.http.entity.BaseRsp
import com.zczy.comm.http.entity.PageList
import com.zczy.comm.utils.dp2px
import com.zczy.comm.utils.imgloader.ImgUtil
import com.zczy.comm.utils.imgloader.Options
import com.zczy.comm.widget.AppToolber
import com.zczy.comm.widget.itemdecoration.CommItemGirdDecoration
import com.zczy.comm.x5.X5WebActivity
import java.util.*

/** 功能描述:
 *
 * <AUTHOR> 贾发展
 * @company 中储智运科技股份有限公司
 * @copyright （版权）中储南京智慧物流科技有限公司所有
 * @date 2020/3/19
 */
open class UsersIntegralActivity : AbstractLifecycleActivity<IntegralModel>(), OnRefreshListener, View.OnClickListener {
    private val userSignInAdapter by lazy { UserSignInAdapter() }
    private val integralAdapter by lazy { IntegralAdapter() }
    private val mAppToolber by lazy { findViewById<AppToolber>(R.id.appToolber) }
    private val ivHead by lazy { findViewById<ImageView>(R.id.iv_head) }
    private val signInRecyclerView by lazy { findViewById<androidx.recyclerview.widget.RecyclerView>(R.id.sign_in_recyclerView) }
    private val taskRecyclerView by lazy { findViewById<androidx.recyclerview.widget.RecyclerView>(R.id.task_recyclerView) }
    private val swipeToLoadLayout by lazy { findViewById<SwipeToLoadLayout>(R.id.swipe_to_load_layout) }
    private val tvSignInValue by lazy { findViewById<TextView>(R.id.tv_sign_in_value) }
    private val lyMore by lazy { findViewById<LinearLayout>(R.id.ly_more) }
    private val tvMore by lazy { findViewById<TextView>(R.id.tv_more) }
    private val tvSignInnDes by lazy { findViewById<TextView>(R.id.tv_sing_in_des) }
    private val llSign by lazy { findViewById<LinearLayout>(R.id.llSign) }
    private val tvExpire by lazy { findViewById<TextView>(R.id.tv_expire) }
    /**
     * 大车司机
     */
    private var tvName: TextView? = null
    /**
     * 302 积分
     */
    private var tvIntegral: TextView? = null

    override fun onCreate(savedInstanceState: Bundle?) {
        super.onCreate(savedInstanceState)
        setContentView(R.layout.user_integral_activity)
        initView()
        onRefresh()
        initListener()
        val login = CommServer.getUserServer().login
        if (login != null && !login.relation.isPrimaryShipper) {
            viewModel?.userSignIntegral()
        }
    }

    private fun initView() {
        StatusBarUtil.setTranslucentForImageViewInFragment(this, 0, mAppToolber)
        swipeToLoadLayout.setOnRefreshListener(this)
        mAppToolber.setRightOnClickListener {
            //规则
            X5WebActivity.startContentUI(this@UsersIntegralActivity, HttpConfig.getWebUrl("mms-app/h5/memberPointRule"))
        }
        signInRecyclerView.apply {
            layoutManager = androidx.recyclerview.widget.GridLayoutManager(
                this@UsersIntegralActivity,
                4
            )
            addItemDecoration(CommItemGirdDecoration(dp2px(5F)))
            setHasFixedSize(false)
            isNestedScrollingEnabled = false
            isFocusable = false
            adapter = userSignInAdapter
        }
        taskRecyclerView.apply {
            layoutManager =
                androidx.recyclerview.widget.LinearLayoutManager(this@UsersIntegralActivity)
            addItemDecoration(CommItemGirdDecoration(dp2px(5F)))
            setHasFixedSize(false)
            isNestedScrollingEnabled = false
            isFocusable = false
            adapter = integralAdapter
        }
        userSignInAdapter.setSpanSizeLookup { _: androidx.recyclerview.widget.GridLayoutManager?, position: Int ->
            if (position == 6) {
                return@setSpanSizeLookup 2
            }
            1
        }
        tvName = findViewById(R.id.tv_name)
        tvIntegral = findViewById(R.id.tv_integral)
        tvMore.setOnClickListener(this)
        tvIntegral?.setOnClickListener(this)
        findViewById<View>(R.id.iv_jh).setOnClickListener(this)
    }

    private fun initListener() {
        tvSignInnDes.setOnClickListener(this)
    }

    @LiveDataMatch
    open fun onUserInfo(user: EUser) {
        if (TextUtils.isEmpty(user.userHeadPic)) {
            ivHead.setImageResource(R.drawable.base_user_def)
        } else {
            ImgUtil.loadUrl(ivHead, HttpConfig.getUrlImage(user.userHeadPic), Options.creator().setCircle(true).setError(R.drawable.base_user_def).setPlaceholder(R.drawable.base_user_def))
        }
        tvName?.text = if (!TextUtils.isEmpty(user.memberName)) {
            user.memberName
        } else {
            if (!TextUtils.isEmpty(user.mobile)) user.mobile.substring(0, 3) + "****" + user.mobile.substring(7, 11) else user.userName
        }
    }

    @LiveDataMatch
    open fun onPageData(page: PageList<EIntegral>?) {
        swipeToLoadLayout.isRefreshing = false
        integralAdapter.setNewData(page?.rootArray)
    }

    @LiveDataMatch
    open fun onPageScoreSuccess(data: IntegralScore?) {
        swipeToLoadLayout.isRefreshing = false
        if (data == null) {
            return
        }
        lyMore.visibility = View.VISIBLE
        val builder = SpannableStringBuilder()
        val span = SpannableString(data.totalIntegral)
        span.setSpan(ForegroundColorSpan(Color.parseColor("#FF602E")), 0, span.length, Spanned.SPAN_INCLUSIVE_EXCLUSIVE)
        span.setSpan(AbsoluteSizeSpan(18, true), 0, span.length, Spanned.SPAN_EXCLUSIVE_EXCLUSIVE)
        builder.append(span)
        builder.append("  积分")
        tvIntegral?.text = builder
        if(data.willExpireIntegral.isNotEmpty()&&!TextUtils.equals(data.willExpireIntegral,"0")){
            tvExpire.text = "您有" + data.willExpireIntegral + "积分即将过期"
            tvExpire.visibility = View.VISIBLE
        } else {
            tvExpire.visibility = View.GONE
        }
    }

    override fun onRefresh() {
        this.viewModel?.queryPage(1)
        this.viewModel?.queryAllPage()
        this.viewModel?.querySignCycleList()
    }

    @LiveDataMatch(tag = "签到弹窗")
    open fun onUserSignIntegral(data: UserSign) {
        UserIntegralSignInDialog().setUserSignData(data).setUserSignListener(object : UserIntegralSignInDialog.OnUserSignListener {

            override fun onSignOk() {
            }

        }).show(this@UsersIntegralActivity)
    }

    @LiveDataMatch(tag = "抽奖列表")
    open fun onQuerySignCycleListSuccess(list: BaseRsp<SignGuideList<SignCycle>>) {
        list.data?.let {
            if (TextUtils.equals(it.resultCode, "UNCERTIFIED")) {
                llSign.visibility = View.GONE
            } else {
                llSign.visibility = View.VISIBLE
            }
            userSignInAdapter.setNewData(it.signGuideList)
            tvSignInValue.text = "(" + it.actualSignDay + "/" + it.cycleDays + ")"
            val builder = SpannableStringBuilder()
            val span = SpannableString(tvSignInValue.text.toString().trim())
            span.setSpan(ForegroundColorSpan(Color.parseColor("#FF6A69")), 1, 2, Spanned.SPAN_INCLUSIVE_EXCLUSIVE)
            builder.append(span)
            tvSignInValue.text = builder
        }
    }

    override fun onClick(v: View) {
        when (v.id) {
            R.id.iv_jh -> {
                // 积分兑换
                val date = Date()
                val time = date.time
                UserIntegralWebActivity.start(this, HttpConfig.getWebUrl() + "form_h5/jfstore/index.html?_t=" + time + "#/storeIndex")
            }
            R.id.tv_more -> {
                UserIntegralListActivity.start(this)
            }
            R.id.tv_integral -> {
                UserIntegralListActivity.start(this)
            }

            R.id.tv_sing_in_des -> {
                // 积分规则
                X5WebActivity.start(this, HttpConfig.getWebUrl() + "form_h5/documents/signInRules.html", "签到规则")
            }
            else -> {
            }
        }
    }

    companion object {
        @JvmStatic
        fun start(context: Context) {
            val intent = Intent(context, UsersIntegralActivity::class.java)
            context.startActivity(intent)
        }
    }
}