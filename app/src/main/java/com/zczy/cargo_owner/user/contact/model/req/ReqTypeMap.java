package com.zczy.cargo_owner.user.contact.model.req;

import com.zczy.cargo_owner.user.contact.model.CPageList;
import com.zczy.cargo_owner.user.contact.model.resp.RespAddContactData;
import com.zczy.cargo_owner.user.contact.model.resp.RespContactListData;
import com.zczy.cargo_owner.user.contact.model.resp.RespTypeMap;
import com.zczy.comm.http.entity.BaseNewRequest;
import com.zczy.comm.http.entity.BaseRsp;
import com.zczy.comm.http.entity.PageList;

public class ReqTypeMap extends BaseNewRequest<BaseRsp<CPageList<RespTypeMap>>> {
    public ReqTypeMap() {
        super("mms-app/dictConfig/queryDictConfig");
    }

    public String dictCode = "CONTACTS_TYPE";
}
