package com.zczy.cargo_owner.user.usermanage.adapter

import android.graphics.Color
import android.util.TypedValue
import android.widget.TextView
import com.chad.library.adapter.base.BaseQuickAdapter
import com.chad.library.adapter.base.BaseViewHolder
import com.zczy.cargo_owner.R
import com.zczy.cargo_owner.user.usermanage.model.SubUserInfo
import com.zczy.comm.widget.inputv2.InputViewSwitch

/**
 * 注释：用户管理Adapter
 * 时间：2024/6/19 0019 8:42
 * 作者：郭翰林
 */
class UserManageAdapter :
    BaseQuickAdapter<SubUserInfo, BaseViewHolder>(R.layout.layout_user_manage_item) {
    private var onSwitchListener: OnSwitchListener? = null

    /**
     * 注释：设置切换监听
     * 时间：2024/6/19 0019 9:27
     * 作者：郭翰林
     */
    fun setOnSwitch(onSwitchListener: OnSwitchListener) {
        this.onSwitchListener = onSwitchListener
    }

    override fun convert(helper: BaseViewHolder?, item: SubUserInfo) {
        helper?.apply {
            setText(R.id.text_name, item.realName)
            setText(R.id.text_account, item.userName)
            setImageResource(
                R.id.img_status,
                if (item.status == "1") R.drawable.ic_start_use else R.drawable.ic_stop_use
            )
            val switch = getView<InputViewSwitch>(R.id.switch_status)
            switch.isChecked = item.status == "1"
            val title = switch.findViewById<TextView>(R.id.tv_title)
            title.setTextColor(Color.parseColor("#5086FC"))
            title.setTextSize(TypedValue.COMPLEX_UNIT_SP, 17f)
            addOnClickListener(R.id.tv_title)
            switch.setListener(object : InputViewSwitch.Listener() {
                override fun onSwitch(viewId: Int, view: InputViewSwitch, isChecked: Boolean) {
                    setImageResource(
                        R.id.img_status,
                        if (isChecked) R.drawable.ic_start_use else R.drawable.ic_stop_use
                    )
                    onSwitchListener?.onSwitch(isChecked, item)
                }
            })
        }
    }
}

interface OnSwitchListener {
    fun onSwitch(isChecked: Boolean, item: SubUserInfo)
}