package com.zczy.cargo_owner.user.overdue

import android.content.Context
import android.content.Intent
import android.graphics.Color
import android.os.Bundle
import androidx.recyclerview.widget.LinearLayoutManager
import android.text.SpannableString
import android.text.SpannableStringBuilder
import android.text.Spanned
import android.text.style.ForegroundColorSpan
import com.sfh.lib.event.RxBusEvent
import com.sfh.lib.mvvm.annotation.LiveDataMatch
import com.zczy.cargo_owner.R
import com.zczy.cargo_owner.user.overdue.adapter.CysExpiredCertificateManagementAdapter
import com.zczy.cargo_owner.user.overdue.model.CysExpiredCertificateManagementModel
import com.zczy.cargo_owner.user.overdue.req.RspQueryUserExpireLicenseB
import com.zczy.cargo_owner.user.overdue.req.RxBusUpdatePicSuccess
import com.zczy.comm.ui.BaseActivity
import com.zczy.comm.utils.ex.setVisible
import kotlinx.android.synthetic.main.cys_expired_certufucate_management_activity.*

/**
 * 功能描述: 证件过期管理
 * <AUTHOR>
 * @date 2022/10/13-9:38
 */

class CysExpiredCertificateManagementActivity : BaseActivity<CysExpiredCertificateManagementModel>() {

    private val mAdapter = CysExpiredCertificateManagementAdapter()

    companion object {

        @JvmStatic
        fun jumpPage(context: Context?) {
            val intent = Intent(context, CysExpiredCertificateManagementActivity::class.java)
            context?.startActivity(intent)
        }
    }

    override fun getLayout(): Int {
        return R.layout.cys_expired_certufucate_management_activity
    }

    override fun bindView(p0: Bundle?) {
        initComView()
        mAdapter.apply {
            bindToRecyclerView(recyclerView)
            setOnItemClickListener { adapter, _, position ->
                val item = adapter.data[position] as RspQueryUserExpireLicenseB
                CysExpiredCertificateUpdateActivity.jumpPage(
                    context = this@CysExpiredCertificateManagementActivity,
                    rspQueryUserExpireLicenseB = item
                )
            }
        }
        recyclerView.apply {
            adapter = mAdapter
            layoutManager =
                androidx.recyclerview.widget.LinearLayoutManager(this@CysExpiredCertificateManagementActivity)
        }
    }

    override fun initData() {
        viewModel?.queryUserExpireLicense()
    }

    private fun initComView() {
        val sb = SpannableStringBuilder()
        val sp1 = SpannableString("您的证件即将过期/已过期，为保障您的业务不受影响，请")
        val sp2 = SpannableString("及时更新")
        sp2.setSpan(ForegroundColorSpan(Color.parseColor("#F4615A")), 0, sp2.length, Spanned.SPAN_EXCLUSIVE_EXCLUSIVE)
        sb.append(sp1).append(sp2)
        tvRemind.text = sb
    }

    @LiveDataMatch()
    open fun onListSuccess(data: MutableList<RspQueryUserExpireLicenseB>?) {
        clRemind.setVisible(!data.isNullOrEmpty())
        tvEmptyText.setVisible(data.isNullOrEmpty())
        data?.let {
            mAdapter.setNewData(it)
        }
    }

    @RxBusEvent(from = "证件更新成功")
    open fun onSuccess(data: RxBusUpdatePicSuccess) {
        if (data.success) {
            viewModel?.queryUserExpireLicense()
        }
    }
}