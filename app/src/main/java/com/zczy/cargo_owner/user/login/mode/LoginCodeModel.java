package com.zczy.cargo_owner.user.login.mode;

import android.text.TextUtils;

import com.sfh.lib.exception.HandleException;
import com.sfh.lib.rx.IResult;
import com.sfh.lib.rx.IResultSuccess;
import com.zczy.cargo_owner.user.login.request.ReqSendCodeV1;
import com.zczy.cargo_owner.user.login.request.ReqUpdateUserSpecialBackUp1;
import com.zczy.comm.data.entity.EShowVerifyCode;
import com.zczy.comm.data.request.ReqSendCode;
import com.zczy.comm.data.request.ReqShowVerifyCode;
import com.zczy.comm.http.entity.BaseRsp;
import com.zczy.comm.http.entity.ResultData;

import java.util.Map;

import io.reactivex.Observable;
import io.reactivex.ObservableEmitter;
import io.reactivex.ObservableOnSubscribe;


public class LoginCodeModel extends LoginModel {

    /***
     * 显示图片验证码
     */
    @Override
    public void onShowImageVerifyCode(ReqSendCode req) {
        setValue("onShowImageVerifyCode", req);
    }

    /***
     * 发送验证码
     * @param type
     */
    @Override
    public void onSendCode(boolean success, String type) {
        setValue("onSendCodeRelust", success, type);
    }

    public void updateUserSpecialBackUp() {
        this.execute(new ReqUpdateUserSpecialBackUp1());
    }

    /***
     * 发送验证码
     * @param req
     */
    @Override
    public void sendVerifyCode(final ReqSendCode req) {

        this.execute(false, req, new IResult<BaseRsp<ResultData>>() {

            @Override
            public void onSuccess(BaseRsp<ResultData> baseRsp) throws Exception {

                if (baseRsp.success()) {
                    onSendCode(true, req.getType());
                } else if (TextUtils.equals("MC300355", baseRsp.getCode())) {
                    loginCYR(baseRsp.getMsg());
                } else {
                    showDialogToast(baseRsp.getMsg());
                    onSendCode(false, req.getType());
                }
            }

            @Override
            public void onFail(HandleException e) {

                showDialogToast(e.getMsg());
                onSendCode(false, req.getType());
            }
        });
    }


    /***
     * 发送验证码
     * @param req
     */
    public void sendVerifyCodeV1(final ReqSendCodeV1 req) {

        this.execute(false, req, new IResult<BaseRsp<ResultData>>() {

            @Override
            public void onSuccess(BaseRsp<ResultData> baseRsp) throws Exception {

                if (baseRsp.success()) {
                    onSendCode(true, req.getType());
                } else if (TextUtils.equals("MC300355", baseRsp.getCode())) {
                    loginCYR(baseRsp.getMsg());
                } else {
                    showDialogToast(baseRsp.getMsg());
                    onSendCode(false, req.getType());
                }
            }

            @Override
            public void onFail(HandleException e) {

                showDialogToast(e.getMsg());
                onSendCode(false, req.getType());
            }
        });
    }

    /***
     * 发送验证码是否显示图片验证码
     * @param req
     */
    @Override
    public void showImageVerifyCode(final ReqSendCode req) {
        //1.发送验证码是否显示图片验证码
        this.execute(new ReqShowVerifyCode(req.getMobile()), eShowVerifyCodeBaseRsp -> {
            if (eShowVerifyCodeBaseRsp.success() && eShowVerifyCodeBaseRsp.getData().show()) {
                //显示图片验证码
                onShowImageVerifyCode(req);
            } else if (eShowVerifyCodeBaseRsp.success() && eShowVerifyCodeBaseRsp.getData().noShow()) {
                sendCode(req);
            }else if (TextUtils.equals("MC300355", eShowVerifyCodeBaseRsp.getCode())) {
                loginCYR(eShowVerifyCodeBaseRsp.getMsg());
            } else {
                showDialogToast(eShowVerifyCodeBaseRsp.getMsg());
            }
        });
    }

    private void sendCode(ReqSendCode req) {
        ReqSendCodeV1 reqNew = new ReqSendCodeV1();
        reqNew.setType(req.getType());
        reqNew.setModuleType(req.getModuleType());
        reqNew.setMobile(req.getMobile());
        this.execute(reqNew, resultDataBaseRsp -> {
            if (resultDataBaseRsp.success()) {
                //发送验证码 成功
                onSendCode(true, req.getType());
            } else if (TextUtils.equals("MC300355", resultDataBaseRsp.getCode())) {
                loginCYR(resultDataBaseRsp.getMsg());
            } else {
                showDialogToast(resultDataBaseRsp.getMsg());
            }
        });
    }

}
