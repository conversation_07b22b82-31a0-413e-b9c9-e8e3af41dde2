package com.zczy.cargo_owner.user.overdue

import android.app.Activity
import android.content.ContentValues
import android.content.Context
import android.content.Intent
import android.graphics.Bitmap
import android.graphics.Color
import android.graphics.drawable.BitmapDrawable
import android.net.Uri
import android.os.Build
import android.os.Bundle
import android.os.Environment
import android.provider.MediaStore
import androidx.fragment.app.Fragment
import androidx.viewpager.widget.PagerAdapter
import androidx.viewpager.widget.ViewPager
import android.text.TextUtils
import android.view.LayoutInflater
import android.view.View
import android.view.ViewGroup
import android.widget.ImageView
import android.widget.ProgressBar
import android.widget.TextView
import android.widget.Toast
import com.bumptech.glide.load.DataSource
import com.bumptech.glide.load.engine.GlideException
import com.bumptech.glide.request.target.Target
import com.github.chrisbanes.photoview.PhotoView
import com.github.chrisbanes.photoview.PhotoViewAttacher
import com.hjq.permissions.Permission
import com.jaeger.library.StatusBarUtil
import com.sfh.lib.mvvm.service.BaseViewModel
import com.sfh.lib.ui.AbstractLifecycleActivity
import com.zczy.cargo_owner.R
import com.zczy.cargo_owner.libcomm.widget.CheckSelfPermissionDialog
import com.zczy.comm.data.entity.EImage
import com.zczy.comm.http.HttpBaseConfig
import com.zczy.comm.permission.PermissionCallBack
import com.zczy.comm.permission.PermissionUtil
import com.zczy.comm.utils.ex.isVisible
import com.zczy.comm.utils.ex.loadFile
import com.zczy.comm.utils.ex.loadUrl
import com.zczy.comm.utils.imageselector.DeletePhotoDialog
import com.zczy.comm.utils.imageselector.ImagePreviewViewPager
import com.zczy.comm.utils.imgloader.Options
import com.zczy.comm.utils.json.toJson
import com.zczy.comm.utils.json.toJsonArray
import com.zczy.comm.widget.dialog.MenuDialogV2
import io.reactivex.Observable
import io.reactivex.ObservableEmitter
import io.reactivex.ObservableOnSubscribe
import io.reactivex.Observer
import io.reactivex.android.schedulers.AndroidSchedulers
import io.reactivex.disposables.Disposable
import io.reactivex.schedulers.Schedulers
import java.io.File
import java.io.FileOutputStream
import java.util.*
import java.util.concurrent.TimeUnit

/**
 * PS: 图片 查看大图 界面
 * Created by sdx on 2018/11/16.
 */
class ImagePreviewActivityV2
    : AbstractLifecycleActivity<BaseViewModel>(), View.OnClickListener {
    companion object {

        private const val EXTRA_EIMAGE_LIST = "extra_eimage_list"
        private const val EXTRA_INT_SELECT_INDEX_INT = "extra_int_select_index_int"
        private const val EXTRA_BOOLEAN_CAN_DELETE = "extra_boolean_can_delete"

        private const val DEF_REQUEST_CODE = 0x44
        private const val FINISH_TASK_WITH_ACTIVITY = 0x999

        @JvmStatic
        @JvmOverloads
        fun start(
            context: Context,
            imageData: List<EImage>,
            selectIndex: Int = 0,
            canDelete: Boolean,
            requestCode: Int = DEF_REQUEST_CODE
        ) {
            if (imageData.isEmpty()) return
            val mIntent = makeIntent(context, imageData, selectIndex, canDelete)
            if (context is Activity) {
                context.startActivityForResult(mIntent, requestCode)
            } else {
                context.startActivity(mIntent)
            }
        }

        @JvmStatic
        @JvmOverloads
        fun start(
            fragment: androidx.fragment.app.Fragment, imageData: List<EImage>,
            selectIndex: Int = 0,
            canDelete: Boolean = false,
            requestCode: Int = DEF_REQUEST_CODE
        ) {
            if (imageData.isEmpty()) return
            val mIntent = makeIntent(fragment.context, imageData, selectIndex, canDelete)
            fragment.startActivityForResult(mIntent, requestCode)
        }

        /**
         * @param selectIndex 进来展示的哪一页
         * @param canDelete 是否可以删除操作
         */
        @JvmStatic
        @JvmOverloads
        fun start(
            activity: Activity, imageData: List<EImage>,
            selectIndex: Int = 0,
            canDelete: Boolean = false,
            requestCode: Int = DEF_REQUEST_CODE
        ) {
            if (imageData.isEmpty()) return
            val mIntent = makeIntent(activity, imageData, selectIndex, canDelete)
            activity.startActivityForResult(mIntent, requestCode)
        }

        /**
         * 解析选择图片返回
         */
        @JvmStatic
        fun onActivityResult(data: Intent?): List<EImage> {
            return data?.getStringExtra(EXTRA_EIMAGE_LIST)?.toJsonArray(EImage::class.java)
                ?: arrayListOf()
        }

        private fun makeIntent(
            context: Context?,
            imageData: List<EImage>,
            selectIndex: Int,
            canDelete: Boolean
        ): Intent {
            val mIntent = Intent(context, ImagePreviewActivityV2::class.java)
            mIntent.putExtra(EXTRA_EIMAGE_LIST, imageData.toJson())
            mIntent.putExtra(EXTRA_INT_SELECT_INDEX_INT, selectIndex)
            mIntent.putExtra(EXTRA_BOOLEAN_CAN_DELETE, canDelete)
            return mIntent
        }
    }

    private val viewTitle by lazy { findViewById<View>(R.id.view_title) }

    // 头部 1/9 文字 view_title
    private val tvTitle by lazy { findViewById<TextView>(R.id.tv_title) }
    private val btnBack by lazy { findViewById<ImageView>(R.id.btn_back) }
    private val pager by lazy { findViewById<ImagePreviewViewPager>(R.id.pager) }
    private val btnDelete by lazy { findViewById<ImageView>(R.id.btn_delete) }

    private val viewAdapter = AdapterViewpager()
    private var subscribe: Disposable? = null

    private val data: MutableList<EImage> by lazy {
        intent.getStringExtra(EXTRA_EIMAGE_LIST)?.toJsonArray(EImage::class.java) ?: mutableListOf()
    }

    private val selectIndex: Int by lazy {
        intent.getIntExtra(EXTRA_INT_SELECT_INDEX_INT, 0)
    }

    private val canDelete: Boolean by lazy {
        intent.getBooleanExtra(EXTRA_BOOLEAN_CAN_DELETE, false)
    }

    override fun onCreate(savedInstanceState: Bundle?) {
        super.onCreate(savedInstanceState)
        setContentView(R.layout.base_image_preview_activity)

        StatusBarUtil.setColor(this, Color.BLACK)

        btnBack.setOnClickListener(this)

        initViewPager()

        initDeleteBtn()

        viewTitle.visibility = View.VISIBLE

        if (data.size > 1) {
            subscribe = Observable.just(1)
                .delay(2, TimeUnit.SECONDS)
                .subscribeOn(Schedulers.io())
                .observeOn(AndroidSchedulers.mainThread())
                .subscribe {
                    viewTitle.visibility = View.GONE
                }
            putDisposable(subscribe)
        }
    }

    private fun initViewPager() {
        pager.adapter = viewAdapter
        pager.addOnPageChangeListener(object : androidx.viewpager.widget.ViewPager.OnPageChangeListener {
            override fun onPageScrollStateChanged(state: Int) {
            }

            override fun onPageScrolled(
                position: Int,
                positionOffset: Float,
                positionOffsetPixels: Int
            ) {
            }

            override fun onPageSelected(position: Int) {
                tvTitle.text = "${position + 1}/${data.size}"
                if (data.size > 1) {
                    subscribe?.dispose()
                    viewTitle.visibility = View.GONE
                }
            }
        })
        tvTitle.text = "1/${data.size}"
        pager.currentItem = selectIndex
    }

    private fun initDeleteBtn() {
        if (canDelete) {
            btnDelete.visibility = View.VISIBLE
            btnDelete.setOnClickListener(this)
        } else {
            btnDelete.visibility = View.GONE
        }
    }

    override fun onClick(v: View) {
        when (v.id) {
            R.id.btn_back -> {
                setResult(FINISH_TASK_WITH_ACTIVITY)
                finish()
            }
            R.id.btn_delete -> {
                DeletePhotoDialog.instance {
                    val currentItem = pager.currentItem
                    val size = data.size
                    when {
                        size == 1 -> {
                            arrayListOf<EImage>().map { }
                            intent.putExtra(EXTRA_EIMAGE_LIST, arrayListOf<EImage>().toJson())
                            setResult(Activity.RESULT_OK)
                            finish()
                        }
                        currentItem > 0 -> {
                            data.removeAt(currentItem)
                            viewAdapter.notifyDataSetChanged()
                            pager.setCurrentItem(currentItem - 1, false)
                            intent.putExtra(EXTRA_EIMAGE_LIST, data.toJson())
                            setResult(Activity.RESULT_OK)
                        }
                        else -> {
                            data.removeAt(currentItem)
                            viewAdapter.notifyDataSetChanged()
                            pager.setCurrentItem(0, false)
                            tvTitle.text = "1/${data.size}"

                            intent.putExtra(EXTRA_EIMAGE_LIST, data.toJson())
                            setResult(Activity.RESULT_OK)
                        }
                    }
                }.show(this)
            }
        }
    }

    inner class AdapterViewpager : androidx.viewpager.widget.PagerAdapter() {

        override fun getCount(): Int = data.size

        override fun isViewFromObject(view: View, `object`: Any): Boolean = view === `object`

        override fun getItemPosition(`object`: Any): Int = POSITION_NONE

        override fun instantiateItem(container: ViewGroup, position: Int): Any {
            val view = LayoutInflater.from(this@ImagePreviewActivityV2)
                .inflate(R.layout.base_image_preview_pager_item, container, false)

            val photoView = view.findViewById<PhotoView>(R.id.photo_view)
            val errorView = view.findViewById<ImageView>(R.id.img_error)
            val progressDialog = view.findViewById<ProgressBar>(R.id.progressView1)
            //  data
            val imageData = data[position]

            errorView.setOnClickListener {
                loadImage(imageData, progressDialog, errorView, photoView)
            }

            view.setOnClickListener {
                changeTitleShow()
            }

            loadImage(imageData, progressDialog, errorView, photoView)
            container.addView(view)
            return view
        }

        private fun loadImage(
            imageData: EImage,
            progressDialog: ProgressBar,
            errorView: ImageView,
            photoView: PhotoView
        ) {
            progressDialog.visibility = View.VISIBLE
            progressDialog.progress = 0
            errorView.visibility = View.GONE
            // url
            var url = ""
            when {
                imageData.path.isNotEmpty() -> {
                    photoView.loadFile(imageData.path)
                    progressDialog.visibility = View.GONE
                    return
                }
                imageData.imageId.isNotEmpty() -> {
                    url = HttpBaseConfig.getUrlImage(imageData.imageId)
                }
                imageData.netUrl.isNotEmpty() -> {
                    url = imageData.netUrl
                }
            }
            val listener = object : Options.ImageLoadListener {
                override fun onLoadFailed(
                    e: GlideException?,
                    model: Any?,
                    target: Target<Bitmap>?,
                    isFirstResource: Boolean
                ): Boolean {
                    errorView.visibility = View.VISIBLE
                    progressDialog.visibility = View.GONE
                    return false
                }

                override fun onResourceReady(
                    resource: Bitmap?,
                    model: Any?,
                    target: Target<Bitmap>?,
                    dataSource: DataSource?,
                    isFirstResource: Boolean
                ): Boolean {
                    progressDialog.visibility = View.GONE
                    val attacher = PhotoViewAttacher(photoView)
                    initPhotoView(photoView, attacher, imageData)
                    return false
                }
            }

            val o = Options.creator()
                .setPlaceholder(-1)
                .setError(-1)
                .setListener(listener)

            photoView.loadUrl(url, o)
        }

        private fun initPhotoView(
            imageView: PhotoView,
            attacher: PhotoViewAttacher,
            imageData: EImage
        ) {
            // 单击事件
            attacher.setOnPhotoTapListener { _, _, _ ->
                changeTitleShow()
            }

            // 长按事件
            attacher.setOnLongClickListener {
                val menus = listOf("保存")
                val dialogV2 = MenuDialogV2.instance(data = menus)
                dialogV2.setClick { name, _ ->
                    if (name == "保存") {
                        CheckSelfPermissionDialog.storagePermissionDialog(this@ImagePreviewActivityV2, object : PermissionCallBack() {
                            override fun onHasPermission() {
                                PermissionUtil.checkPermissions(
                                    this@ImagePreviewActivityV2,
                                    "需要本地存储权限",
                                    arrayOf(Permission.WRITE_EXTERNAL_STORAGE),
                                    object : PermissionCallBack() {
                                        override fun onHasPermission() {
                                            val bitmap = (imageView.drawable as BitmapDrawable).bitmap
                                            val saveName = "img_" + System.currentTimeMillis() + ".jpg"

                                            Observable.create(object : ObservableOnSubscribe<Boolean> {
                                                override fun subscribe(emitter: ObservableEmitter<Boolean>) {
                                                    val saveBitmapRes = saveBitmap(bitmap, saveName,this@ImagePreviewActivityV2)
                                                    emitter.onNext(saveBitmapRes)
                                                    emitter.onComplete()
                                                }
                                            }).subscribeOn(Schedulers.io())
                                                .observeOn(AndroidSchedulers.mainThread())
                                                .subscribe(object : Observer<Boolean> {
                                                    override fun onComplete() {

                                                    }

                                                    override fun onSubscribe(d: Disposable) {

                                                    }

                                                    override fun onNext(t: Boolean) {
                                                        if (t) {
                                                            Toast.makeText(
                                                                this@ImagePreviewActivityV2,
                                                                "保存成功",
                                                                Toast.LENGTH_SHORT
                                                            ).show()
                                                        } else {
                                                            Toast.makeText(
                                                                this@ImagePreviewActivityV2,
                                                                "保存失败",
                                                                Toast.LENGTH_SHORT
                                                            ).show()
                                                        }
                                                    }

                                                    override fun onError(e: Throwable) {
                                                        Toast.makeText(
                                                            this@ImagePreviewActivityV2,
                                                            "保存失败",
                                                            Toast.LENGTH_SHORT
                                                        ).show()
                                                    }
                                                })
                                        }
                                    })
                            }})

                    }
                }
                dialogV2.show(this@ImagePreviewActivityV2)
                false
            }
        }

        override fun destroyItem(container: ViewGroup, position: Int, `object`: Any) {
            container.removeView(`object` as View)
        }
    }

    private fun changeTitleShow() {
        if (data.size > 1) {
            if (viewTitle.isVisible) {
                subscribe?.dispose()
                viewTitle.visibility = View.GONE
            } else {
                viewTitle.visibility = View.VISIBLE
            }
        }
    }

    fun saveBitmap(bitmap: Bitmap, saveName: String,context: Context): Boolean {

        if (TextUtils.isEmpty(saveName)) {
            return false
        }

        try {

            if(Build.VERSION.SDK_INT >= Build.VERSION_CODES.Q){
                val values = ContentValues().apply {
                    put(MediaStore.Images.Media.DISPLAY_NAME, saveName + ".jpg")
                    put(MediaStore.Images.Media.MIME_TYPE, "image/jpg")
                    put(MediaStore.Images.Media.RELATIVE_PATH, Environment.DIRECTORY_PICTURES)
                }

                val resolver = context.contentResolver
                val imageUri: Uri? = resolver.insert(MediaStore.Images.Media.EXTERNAL_CONTENT_URI, values)

                Objects.requireNonNull(imageUri)?.let {
                    resolver.openOutputStream(it).use { outputStream ->
                        //将bitmap写入outputStream
                        if(outputStream != null){
                            bitmap.compress(Bitmap.CompressFormat.PNG, 100, outputStream)
                        }
                    }
                }
                // 发送广播通知系统相册有新图片
                context.sendBroadcast(Intent(Intent.ACTION_MEDIA_SCANNER_SCAN_FILE, imageUri))
            } else {
                val dirPath = Environment.getExternalStorageDirectory().absolutePath + "/zczy/"
                val dirFile = File(dirPath)
                if (!dirFile.exists()) {
                    dirFile.mkdirs()
                }

                val f = File(dirPath + saveName)

                //保存图片到sdcard
                val out = FileOutputStream(f)
                bitmap.compress(Bitmap.CompressFormat.PNG, 100, out)
                out.flush()
                out.close()
                //通知图库更新
                sendBroadcast(
                    Intent(
                        Intent.ACTION_MEDIA_SCANNER_SCAN_FILE,
                        Uri.parse("file://" + dirPath + saveName)
                    )
                )
            }
        } catch (e: Exception) {
            e.printStackTrace()
            return false
        }

        return true
    }
}