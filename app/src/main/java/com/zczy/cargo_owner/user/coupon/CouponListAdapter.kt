package com.zczy.cargo_owner.user.coupon

import android.graphics.Color
import androidx.constraintlayout.widget.ConstraintLayout
import android.text.TextUtils
import android.widget.Button
import android.widget.ImageView
import com.chad.library.adapter.base.BaseQuickAdapter
import com.chad.library.adapter.base.BaseViewHolder
import com.zczy.cargo_owner.R
import com.zczy.cargo_owner.user.coupon.req.RspUserCoupon
import com.zczy.comm.utils.ex.isTrue


/**
 * PS: 个人中心 优惠卷 适配器
 */
class CouponListAdapter :
    BaseQuickAdapter<RspUserCoupon, BaseViewHolder>(R.layout.user_coupon_main_un_used_item) {


    var color_5086FC = Color.parseColor("#5086FC")
    var color_666666 = Color.parseColor("#666666")

    var itemViewType = CouponFragment.TYPE_UN_USED;

    override fun convert(helper: BaseViewHolder, item: RspUserCoupon) {
        when (itemViewType) {
            // 未使用
            CouponFragment.TYPE_UN_USED -> {
                convertUnUsed(helper, item)
            }
            // 使用记录
            CouponFragment.TYPE_USED -> {
                convertUsed(helper, item)
            }
            // 已过期
            CouponFragment.TYPE_EXPIRED -> {
                convertExpired(helper, item)
            }
        }
    }

    // 未使用
    private fun convertUnUsed(helper: BaseViewHolder, item: RspUserCoupon) {

        this.convertComm(helper, item)

        helper.setTextColor(R.id.tv_coupon_money, color_5086FC)
        helper.setTextColor(R.id.tv_coupon_type_name, color_5086FC)
        helper.setGone(R.id.btn_use, true)
        helper.setImageResource(
            R.id.iv_coupon_bg,
            if (!TextUtils.isEmpty(item.instructions)) R.drawable.order_coupon_main_item_unused_big_bg else R.drawable.order_coupon_main_item_unused_bg
        )

        // 优惠券是否可以开始使用
        val button = helper.getView<Button>(R.id.btn_use)
        button.isEnabled = item.isUse.isTrue

    }

    private fun convertUsed(helper: BaseViewHolder, item: RspUserCoupon) {
        convertComm(helper, item)

        helper.setTextColor(R.id.tv_coupon_money, color_666666)
        helper.setTextColor(R.id.tv_coupon_type_name, color_666666)
        helper.setGone(R.id.iv_status_type, true).setImageResource(
            R.id.iv_status_type,
            R.drawable.order_coupon_main_item_used_ic
        ).setImageResource(
            R.id.iv_coupon_bg,
            if (!TextUtils.isEmpty(item.instructions)) R.drawable.order_coupon_main_item_used_big_bg else R.drawable.order_coupon_main_item_used_bg
        )


    }

    private fun convertExpired(helper: BaseViewHolder, item: RspUserCoupon) {

        this.convertComm(helper, item)
        helper.setTextColor(R.id.tv_coupon_money, color_666666)
        helper.setTextColor(R.id.tv_coupon_type_name, color_666666)
        helper.setGone(R.id.iv_status_type, true)
        helper.setImageResource(R.id.iv_status_type, R.drawable.order_coupon_main_item_expired_ic)
        helper.setImageResource(
            R.id.iv_coupon_bg,
            if (!TextUtils.isEmpty(item.instructions)) R.drawable.order_coupon_main_item_used_big_bg else R.drawable.order_coupon_main_item_used_bg
        )

    }

    private fun convertComm(helper: BaseViewHolder, item: RspUserCoupon) {

        helper.setGone(R.id.btn_use, false)  //立即使用按鈕
        helper.setGone(R.id.iv_status_type, false) //使用状态
        helper.setText(R.id.tv_coupon_type_name, item.formatCouponTypeName())   // 优惠券类型名称
        helper.setText(R.id.tv_validity_time, item.validityTime) // 优惠券有效期
        helper.setText(R.id.tv_coupon_description, item.instructions)  //使用说明栏

        //使用说明栏,设置不同使用背景图片
        val imageView = helper.getView<ImageView>(R.id.iv_coupon_bg)
        val layoutParam =
            ConstraintLayout.LayoutParams(imageView.layoutParams as ConstraintLayout.LayoutParams)

        if (!TextUtils.isEmpty(item.instructions)) {
            helper.setGone(R.id.cl_coupon_bottom, true)
            layoutParam.dimensionRatio = "H,345:120" //设置背景宽高比
        } else {
            helper.setGone(R.id.cl_coupon_bottom, false)
            layoutParam.dimensionRatio = "H,345:100"
        }
        imageView.layoutParams = layoutParam


        // 优惠券类型ID 1 增值卷 2 抵用卷 3 任务类型-满赠卷 4 任务类型-满减卷
        when (item.couponTypeId) {
            // 增值卷 任务类型-满赠卷 任务类型-满减卷
            "1", "3", "4" -> {
                helper
                    // 优惠券面额
                    .setText(R.id.tv_coupon_money, item.formatCouponMoney())
                    // 满多少可用
                    .setText(R.id.tv_min_money, item.formatMinMoney())
            }
            // 抵用卷
            "2" -> {
                helper
                    // 优惠券面额
                    .setText(R.id.tv_coupon_money, item.formatDiscountRatio())
                    // 满多少可用
                    .setText(R.id.tv_min_money, item.discountMoneyTopName)
            }
            else -> {
                helper
                    // 优惠券面额
                    .setText(R.id.tv_coupon_money, item.formatCouponMoney())
                    // 满多少可用
                    .setText(R.id.tv_min_money, item.formatMinMoney())
            }
        }

    }
}