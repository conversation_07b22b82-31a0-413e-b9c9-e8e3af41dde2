package com.zczy.cargo_owner.user.certification.model;

import android.content.Context;
import android.net.Uri;
import android.text.TextUtils;

import com.sfh.lib.AppCacheManager;
import com.sfh.lib.exception.HandleException;
import com.sfh.lib.http.down.HttpDownHelper;
import com.sfh.lib.mvvm.service.BaseViewModel;
import com.sfh.lib.rx.IResult;
import com.sfh.lib.rx.IResultSuccess;
import com.zczy.cargo_owner.deliver.batch.util.DownloadUtil;
import com.zczy.cargo_owner.user.certification.bean.CPageList;
import com.zczy.cargo_owner.user.certification.bean.CompanyArray;
import com.zczy.cargo_owner.user.certification.bean.MemberDetails;
import com.zczy.cargo_owner.user.certification.req.DataList;
import com.zczy.cargo_owner.user.certification.req.ExpireDate;
import com.zczy.cargo_owner.user.certification.req.PicUrlBean;
import com.zczy.cargo_owner.user.certification.req.ReqHzCertification;
import com.zczy.cargo_owner.user.certification.req.ReqMemberDetail;
import com.zczy.cargo_owner.user.certification.req.ReqMemberPassUpdate;
import com.zczy.cargo_owner.user.certification.req.ReqQueryCustomerRoleAuthState;
import com.zczy.cargo_owner.user.certification.req.ReqQueryExpireDateLicense;
import com.zczy.cargo_owner.user.certification.req.ReqSubsidiary;
import com.zczy.cargo_owner.user.certification.req.ReqUploadExpireDateLicense;
import com.zczy.cargo_owner.user.certification.req.RsqQueryCustomerRoleAuthState;
import com.zczy.comm.CommServer;
import com.zczy.comm.file.IFileServer;
import com.zczy.comm.http.entity.BaseRsp;
import com.zczy.comm.http.entity.ResultData;

import java.io.File;
import java.util.List;

import io.reactivex.Observable;
import io.reactivex.annotations.NonNull;
import io.reactivex.disposables.Disposable;
import io.reactivex.functions.Function;

public class CertificationModel extends BaseViewModel {


    public void uploadExpireDateLicense(List<PicUrlBean> picUrlList) {
        this.execute(true, new ReqUploadExpireDateLicense(picUrlList), new IResult<BaseRsp<ResultData>>() {
            @Override
            public void onFail(HandleException e) {
                showDialogToast(e.getMessage());
            }

            @Override
            public void onSuccess(BaseRsp<ResultData> resultDataBaseRsp) throws Exception {
                if (resultDataBaseRsp.success()) {
                    setValue("onUploadExpireDateSuccess");
                } else {
                    showDialogToast(resultDataBaseRsp.getData().getResultMsg());
                }
            }
        });
    }


    public void queryExpireDateLicense() {
        this.execute(true, new ReqQueryExpireDateLicense(), new IResult<BaseRsp<DataList<ExpireDate>>>() {
            @Override
            public void onFail(HandleException e) {
                showDialogToast(e.getMessage());
            }

            @Override
            public void onSuccess(BaseRsp<DataList<ExpireDate>> pageListBaseRsp) throws Exception {
                if (pageListBaseRsp.success()) {
                    setValue("onQueryExpireDateLicenseSuccess", pageListBaseRsp.getData());
                } else {
                    showDialogToast(pageListBaseRsp.getData().getResultMsg());
                }
            }
        });
    }


    /**
     * 注释: 查询用户认证状态
     * 时间: 2024/7/8 0008 16:49
     *
     * <AUTHOR>
     */
    public void queryCustomerRoleAuthState(String taxId) {
        ReqQueryCustomerRoleAuthState reqQueryCustomerRoleAuthState = new ReqQueryCustomerRoleAuthState(taxId);
        this.execute(false, reqQueryCustomerRoleAuthState, new IResult<BaseRsp<RsqQueryCustomerRoleAuthState>>() {
            @Override
            public void onFail(HandleException e) {
                showDialogToast(e.getMsg());
            }

            @Override
            public void onSuccess(BaseRsp<RsqQueryCustomerRoleAuthState> rsqQueryCustomerRoleAuthStateBaseRsp) throws Exception {
                if (rsqQueryCustomerRoleAuthStateBaseRsp.success()) {
                    setValue("onQueryCustomerRoleAuthState", rsqQueryCustomerRoleAuthStateBaseRsp.getData());
                } else {
                    showDialogToast(rsqQueryCustomerRoleAuthStateBaseRsp.getMsg());
                }
            }
        });
    }

    /**
     * 获取会员详情
     * mms/upgrade/getMemberDetail
     */
    public void getMemberDetail() {
        ReqMemberDetail memberDetail = new ReqMemberDetail();
        this.execute(false, memberDetail,
                new IResult<BaseRsp<MemberDetails>>() {

                    @Override
                    public void onSuccess(BaseRsp<MemberDetails> resultDataBaseRsp) throws Exception {
                        if (resultDataBaseRsp.success()) {
                            setValue("onMemberDetailSuccess", resultDataBaseRsp.getData());
                        } else {
                            showDialogToast(resultDataBaseRsp.getMsg());
                        }
                    }

                    @Override
                    public void onFail(HandleException e) {
                        showDialogToast(e.getMsg());
                    }
                });
    }

    /**
     * 图片上传
     *
     * @param file
     */
    public void upLoadPic(String file) {
        showLoading(true);
        IFileServer fileServer = CommServer.getFileServer();
        File old = new File(file);
        if (old.exists()) {
            Disposable disposable = fileServer.update(old, new IFileServer.OnFileUploaderListener() {
                @Override
                public void onSuccess(File tag, String url) {
                    hideLoading();
                    setValue("upLoadPicSuccess", url);
                }

                @Override
                public void onFailure(File tag, String error) {
                    hideLoading();
                    setValue("upLoadPicError");
                    showToast(error);
                }
            }, true);
            this.putDisposable(disposable);
        }
    }

    /*
     * 文件下载
     *
     * */
    public void loadFile2(String url, String fileName) {

        //                file = HttpDownHelper.Builder(s).setTagFile(file).start()
        this.execute(true, Observable.just(url).map(s -> {
            File file = new File(AppCacheManager.getFileCache(), fileName);
            file = new HttpDownHelper.Builder(s).setTagFile(file).start();
            return file.getAbsolutePath();
        }), new IResultSuccess<String>() {

            @Override
            public void onSuccess(String s) throws Exception {

            }
        });
    }

    /*
     * 文件下载
     *
     * */
    public void loadFile(String url, String fileName,Context context) {

        DownloadUtil.downloadFile(context,url,fileName,"docx");

        //                file = HttpDownHelper.Builder(s).setTagFile(file).start()
        /*this.execute(true, Observable.just(url).map(new Function<String, String>() {
            @Override
            public String apply(@NonNull String s) throws Exception {
                File file = new File(AppCacheManager.getFileCache(), Uri.decode("授权委托书模板") + ".docx");
                file = new HttpDownHelper.Builder(s).setTagFile(file).start();
                return file.getAbsolutePath();
            }
        }), s -> setValue("downLoadSuccess", s));*/
    }
//    fun loadFile(url: String, fileName: String) {
//        this.execute(true, Observable.just(url).map<String> { s ->
//                var file = File(AppCacheManager.getFileCache(), fileName)
//                file = HttpDownHelper.Builder(s).setTagFile(file).start()
//                file.absolutePath
//        }, object : IResult<String> {
//            @Throws(Exception::class)
//            override fun onSuccess(path: String) {
//                setValue("downLoadSuccess", path)
//            }
//
//            override fun onFail(e: HandleException) {
//                showDialogToast(e.message)
//            }
//        })
//    }

    /***
     *
     * 查询主题类型
     */
    public void querySubsidiaryList4APP() {
        this.execute(true, new ReqSubsidiary(), new IResult<BaseRsp<CPageList<CompanyArray>>>() {
            @Override
            public void onSuccess(BaseRsp<CPageList<CompanyArray>> companyArrayPageList) throws Exception {
                if (companyArrayPageList.success()) {
                    setValue("querySubsidiaryList4APPSuccess", companyArrayPageList.getData().getRootArray());
                } else {
                    showDialogToast(companyArrayPageList.getData().getResultMsg());
                }
            }

            @Override
            public void onFail(HandleException e) {
                showDialogToast(e.getMsg());
            }
        });
    }

    /**
     * 图片上传
     *
     * @param file
     */
    public void upLoadPicNoZip(String file) {
        showLoading(true);
        IFileServer fileServer = CommServer.getFileServer();
        File old = new File(file);
        if (old.exists()) {
            Disposable disposable = fileServer.update(old, new IFileServer.OnFileUploaderListener() {
                @Override
                public void onSuccess(File old, String url) {
                    hideLoading();
                    setValue("upLoadPicSuccess", old, url);
                }

                @Override
                public void onFailure(File tag, String error) {
                    hideLoading();
                    showToast(error);
                }
            }, false);
            this.putDisposable(disposable);
        }
    }

    /**
     * 货主认证
     *
     * @param req
     */
    public void hzUserPromote(ReqHzCertification req) {
        if (TextUtils.isEmpty(req.getContacter())) {
            showToast("联系人姓名不能为空！");
            return;
        }
        if (TextUtils.isEmpty(req.getContacterPhone())) {
            showToast("紧急联系人号码不能为空！");
            return;
        }

        if (TextUtils.isEmpty(req.getCompanyName())) {
            showToast("企业名称不能为空！");
            return;
        }

        String isEntrustRegister = req.getIsEntrustRegister();
        String busiLicUrl = req.getBusiLicUrl();
        if (TextUtils.isEmpty(busiLicUrl) && req.isNeedBusinessLicense()) {
            showToast("营业执照不能为空！");
            return;
        }
        if (TextUtils.equals(isEntrustRegister, "0")) {
            // 法人身份证照片
            if (TextUtils.isEmpty(req.getLegalIdCardUrl())) {
                showToast("注册人身份证照(正面)不能为空");
                return;
            }
        } else if (TextUtils.equals(isEntrustRegister, "1")) {
            // 授权委托书
            String nonLegalAuthUrl = req.getNonLegalAuthUrl();
            if (TextUtils.isEmpty(nonLegalAuthUrl)) {
                showToast("授权委托书照片不能为空！");
                return;
            }
            // 注册人身份证照片（正面）
            String nonLegalIdCardUrl = req.getNonLegalIdCardUrl();
            if (TextUtils.isEmpty(nonLegalIdCardUrl)) {
                showToast("注册人身份证照(正面)不能为空！");
                return;
            }
        }
        this.execute(false, req, new IResult<BaseRsp<ResultData>>() {
            @Override
            public void onSuccess(BaseRsp<ResultData> baseRsp) throws Exception {
                setValue("hzUserPromoteSuccess", baseRsp);
            }

            @Override
            public void onFail(HandleException e) {
                showDialogToast(e.getMessage());
                setValue("hzUserPromoteFailed", e.getMessage());
            }
        });
    }


    public void editPhone(String phone) {

        this.execute(false, new ReqMemberPassUpdate(phone), resultDataBaseRsp -> {
            if (resultDataBaseRsp.success()) {
                showToast(resultDataBaseRsp.getMsg());
                getMemberDetail();
            } else {
                showDialogToast(resultDataBaseRsp.getMsg());
            }
        });

    }

}
