package com.zczy.cargo_owner.user.evaluate.bean

/** 功能描述:
 *
 * <AUTHOR> 贾发展
 * @company 中储智运科技股份有限公司
 * @copyright （版权）中储南京智慧物流科技有限公司所有
 * @date 2019/3/28
 */
data class EvaluationBean(
    /*车牌号*/
    val plateNumbers: String,

    /*货物名称和吨位*/
    val allCargoName: String,

    /*公司名称*/
    val consignorCompany: String,

    /*摘牌类型 1团摘 2直摘*/
    val delistType: String,

    /*价格类型和吨位*/
    val moneyType: String,

    val money: String, //是金额

    val type: String, //type 是价格类型

    /*运单号*/
    val orderId: String,

    /*抢单还是议价 0 抢单 1议价*/
    val orderModel: String,

    /*目的地*/
    val deliverCity: String,

    /*起运地*/
    val despatchCity: String,

    /*费用类型：0 包车价，1 单价*/
    private val freightType: String,

    //是否预付：0 否, 1 是
    val isAdvance: String,

    /*指定承运方*/
    val specifyCarriers: String,

    //重货或泡货 1：重货，2 泡货
    val cargoCategory: String,

    /**
     * 货主是否评价：0 否,1 是
     */
    val consignorHaveEvaluate: String,
    /**
     * 0正常货源 1集装箱货源 2零担货源
     */
    val goodsSource: String,
)