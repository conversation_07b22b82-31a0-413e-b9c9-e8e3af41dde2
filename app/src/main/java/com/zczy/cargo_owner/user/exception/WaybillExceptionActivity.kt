package com.zczy.cargo_owner.user.exception

import android.content.Context
import android.content.Intent
import android.graphics.Color
import android.os.Bundle
import androidx.fragment.app.Fragment
import com.sfh.lib.mvvm.service.BaseViewModel
import com.zczy.cargo_owner.R
import com.zczy.cargo_owner.user.exception.WaybillExceptionFragment.Companion.newInstance
import com.zczy.comm.ui.BaseActivity
import com.zczy.comm.ui.UtilStatus
import kotlinx.android.synthetic.main.waybill_exception_activity.tab_layout
import kotlinx.android.synthetic.main.waybill_exception_activity.viwepager

/**
 * desc: 运单异常处理
 * user: ssp
 * time: 2024/7/18 15:51
 */
class WaybillExceptionActivity : BaseActivity<BaseViewModel>() {

    private val mTitles = arrayOf(
        "待处理", "处理中", "已处理", "审核关闭", "全部"
    )

    companion object {
        @JvmStatic
        fun startUI(context: Context?) {
            val intent = Intent(context, WaybillExceptionActivity::class.java)
            context?.startActivity(intent)
        }
    }

    override fun getLayout(): Int {
        return R.layout.waybill_exception_activity
    }

    override fun bindView(bundle: Bundle?) {
        UtilStatus.initStatus(this, Color.WHITE)
        initFragment()
    }

    override fun initData() {

    }

    private fun initFragment() {
        val fragmentList: MutableList<Fragment> = ArrayList()
        val waybillExceptionFragment1 = newInstance(WaybillExceptionFragment.WAITING_WAY_BILL)
        val waybillExceptionFragment4 = newInstance(WaybillExceptionFragment.DEALING_WAY_BILL)
        val waybillExceptionFragment2 = newInstance(WaybillExceptionFragment.DEAL_WITH_WAY_BILL)
        val waybillExceptionFragment5 = newInstance(WaybillExceptionFragment.DEAL_WITH_WAY_CLOSE)
        val waybillExceptionFragment3 = newInstance(WaybillExceptionFragment.ALL_WAY_BILL)
        fragmentList.add(waybillExceptionFragment1)
        fragmentList.add(waybillExceptionFragment4)
        fragmentList.add(waybillExceptionFragment2)
        fragmentList.add(waybillExceptionFragment5)
        fragmentList.add(waybillExceptionFragment3)
        tab_layout.setViewPager(viwepager, mTitles, this@WaybillExceptionActivity, fragmentList as ArrayList<Fragment>)
        viwepager.currentItem = 0
    }
}
