package com.zczy.cargo_owner.user.login.activity;

import android.app.AlertDialog;
import android.content.Context;
import android.os.Bundle;
import android.view.View;
import android.widget.TextView;

import com.zczy.cargo_owner.R;


/**
 * 功能描述:
 *
 * <AUTHOR> 孙飞虎
 * @company 中储智运科技股份有限公司
 * @copyright （版权）中储南京智慧物流科技有限公司所有
 * @date 2019/5/28
 */
public class SelectPersonDialog extends AlertDialog {
    String des;
    OnClickListener onClickListener;

    public SelectPersonDialog(Context context, String des, OnClickListener onClickListener) {
        super(context, R.style.dialogToast);
        this.getWindow().setWindowAnimations(R.style.dialogAnim);
        this.setCanceledOnTouchOutside(false);
        this.setCancelable(false);
        this.des = des;
        this.onClickListener = onClickListener;
    }

    @Override
    protected void onCreate(Bundle savedInstanceState) {

        super.onCreate(savedInstanceState);
        this.setContentView(R.layout.user_register_select_dialog);

        TextView tv_1 = findViewById(R.id.tv_1);
        tv_1.setText(des);

        findViewById(R.id.bt_cancle).setOnClickListener(new View.OnClickListener() {
            @Override
            public void onClick(View view) {
                onClickListener.onClick(SelectPersonDialog.this, 1);
            }
        });
        findViewById(R.id.bt_ok).setOnClickListener(new View.OnClickListener() {
            @Override
            public void onClick(View view) {
                onClickListener.onClick(SelectPersonDialog.this, 2);
            }
        });
    }
}
