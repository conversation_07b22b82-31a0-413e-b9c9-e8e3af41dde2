package com.zczy.cargo_owner.user.login.activity;

import android.content.Context;
import android.content.Intent;
import android.os.Bundle;
import androidx.annotation.Nullable;
import android.text.TextUtils;
import android.view.View;
import android.widget.Button;
import android.widget.EditText;
import android.widget.ImageView;

import com.jakewharton.rxbinding2.view.RxView;
import com.jakewharton.rxbinding2.widget.RxTextView;
import com.sfh.lib.mvvm.annotation.LiveDataMatch;
import com.zczy.cargo_owner.R;
import com.zczy.cargo_owner.home.onlinecall.OnLineCallActivity;
import com.zczy.cargo_owner.user.login.PwdUtilKt;
import com.zczy.cargo_owner.user.login.mode.RegisterMode;
import com.zczy.cargo_owner.user.login.request.ReqRegister;
import com.zczy.comm.ui.BaseActivity;
import com.zczy.comm.utils.CommUtils;

import java.util.concurrent.TimeUnit;

import io.reactivex.Observable;
import io.reactivex.disposables.Disposable;

/**
 * 注册第二步
 */
public class RegisterStep2Activity extends BaseActivity<RegisterMode> implements View.OnClickListener {

    public static void start(Context context, String phone, String type, String invitationCode, String codeType, String code,String companyName,String provinceName, String provinceCode) {
        Intent intent = new Intent(context, RegisterStep2Activity.class);
        intent.putExtra("phone", phone);
        intent.putExtra("userType", type);
        intent.putExtra("invitationCode", invitationCode);
        intent.putExtra("codeType", codeType);
        intent.putExtra("code", code);
        intent.putExtra("companyName", companyName);
        intent.putExtra("provinceName", provinceName);
        intent.putExtra("provinceCode", provinceCode);
        context.startActivity(intent);
    }

    private EditText etPwd;
    private ImageView tvOneLook;
    private EditText etPwdTwo;
    private ImageView tvTwoLook;
    private Button btOK;
    private ImageView imgCustomer;

    ReqRegister registerMember = new ReqRegister();

    @Override
    protected int getLayout() {
        return R.layout.user_register_step2_activity;
    }

    @Override
    protected void bindView(@Nullable Bundle bundle) {
        etPwd = this.findViewById(R.id.etPwd);
        tvOneLook = this.findViewById(R.id.tvOneLook);
        etPwdTwo = this.findViewById(R.id.etPwdTwo);
        tvTwoLook = this.findViewById(R.id.tvTwoLook);
        btOK = this.findViewById(R.id.btOK);
        this.setListener();
        imgCustomer = findViewById(R.id.img_customer);
        registerMember.setSerialNumber(getIntent().getStringExtra("phone"));
        registerMember.setVerifyCode(getIntent().getStringExtra("code"));
        registerMember.setVerifyCodeType(getIntent().getStringExtra("codeType"));
        registerMember.setProvinceCode(getIntent().getStringExtra("provinceCode"));
        registerMember.companyName = getIntent().getStringExtra("companyName");
        registerMember.provinceName = getIntent().getStringExtra("provinceName");

        imgCustomer.setOnClickListener(v -> OnLineCallActivity.start(RegisterStep2Activity.this,""));
    }

    @Override
    protected void initData() {

    }

    @Override
    public void onClick(View v) {
        if (v == tvOneLook) {
            boolean look = CommUtils.lookEditPassWord(etPwd);
            tvOneLook.setImageResource(look ? R.drawable.user_register_look_ok : R.drawable.user_register_look_no);
        } else if (v == tvTwoLook) {
            boolean look = CommUtils.lookEditPassWord(etPwdTwo);
            tvTwoLook.setImageResource(look ? R.drawable.user_register_look_ok : R.drawable.user_register_look_no);
        }
    }

    private void setListener() {
        Disposable changes = Observable
                .combineLatest(RxTextView.textChanges(etPwd),
                        RxTextView.textChanges(etPwdTwo),
                        (charSequence, charSequence2) -> TextUtils.isEmpty(charSequence) || TextUtils.isEmpty(charSequence2))
                .subscribe(aBoolean -> btOK.setEnabled(!aBoolean));
        this.putDisposable(changes);

        Disposable cilck = RxView
                .clicks(btOK)
                .throttleFirst(1, TimeUnit.SECONDS)
                .subscribe(o -> {
                    String password = etPwd.getText().toString();
                    if (TextUtils.isEmpty(password)) {
                        showDialogToast("请输入密码");
                        return;
                    }
                    String password2 = etPwdTwo.getText().toString();
                    if (TextUtils.isEmpty(password2)) {
                        showDialogToast("请输入确认密码");
                        return;
                    }
                    if (!TextUtils.equals(password2, password)) {
                        showDialogToast("两次密码不一致,请重新输入");
                        return;
                    }
                    if (!PwdUtilKt.checkPwdRobustness(password)) {
                        PwdUtilKt.showCheckPwdDialog(RegisterStep2Activity.this);
                        return;
                    }
                    registerMember.setUserPassword(password);
                    getViewModel(RegisterMode.class).register(registerMember);
                });
        this.putDisposable(cilck);
        tvOneLook.setOnClickListener(this);
        tvTwoLook.setOnClickListener(this);

        findViewById(R.id.img_customer).setOnClickListener(v -> OnLineCallActivity.start(RegisterStep2Activity.this,""));

    }

    @LiveDataMatch
    public void onRegisterSuccess() {
        RegisterSuccActivity.start(this, registerMember.getSerialNumber(), etPwd.getText().toString());
        finish();
    }
}
