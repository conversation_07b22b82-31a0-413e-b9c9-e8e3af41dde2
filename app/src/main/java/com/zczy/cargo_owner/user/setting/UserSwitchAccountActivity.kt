package com.zczy.cargo_owner.user.setting

import android.annotation.SuppressLint
import android.content.Context
import android.content.Intent
import android.os.Bundle
import android.os.Handler
import android.os.Looper
import android.text.TextUtils
import android.view.View
import androidx.recyclerview.widget.LinearLayoutManager
import com.sfh.lib.AppCacheManager
import com.sfh.lib.event.RxBusEventManager
import com.sfh.lib.mvvm.annotation.LiveDataMatch
import com.sfh.lib.mvvm.service.BaseViewModel
import com.sfh.lib.ui.dialog.DialogBuilder
import com.zczy.cargo_owner.R
import com.zczy.cargo_owner.findTokenId
import com.zczy.cargo_owner.findUserId
import com.zczy.cargo_owner.home.HomeActivity
import com.zczy.cargo_owner.user.certification.CertificationUtils
import com.zczy.cargo_owner.user.login.activity.RegisterStep1Activity
import com.zczy.cargo_owner.user.login.activity.SelectPersonDialog
import com.zczy.cargo_owner.user.login.activity.UserLoginErrorDialogActivity
import com.zczy.cargo_owner.user.login.activity.UserLoginOtherErrorDialogActivity
import com.zczy.cargo_owner.user.login.authenticationdialog.AuthenticationDialog
import com.zczy.cargo_owner.user.login.entity.RxReStarHome
import com.zczy.cargo_owner.user.login.mode.LoginAccountModel
import com.zczy.cargo_owner.user.login.request.ReqLogin.AccountBuilder
import com.zczy.cargo_owner.user.login.showNeedEditPwdDialog
import com.zczy.cargo_owner.user.setting.adapter.UserSwitchAccountAdapter
import com.zczy.cargo_owner.user.setting.dialog.UserClearAccountDialog
import com.zczy.cargo_owner.user.setting.model.ReqQueryUserInfosByUserIds
import com.zczy.cargo_owner.user.setting.model.RspQueryUserInfosByUserIds
import com.zczy.cargo_owner.user.setting.model.UserSwitchModel
import com.zczy.comm.ZczyApplication
import com.zczy.comm.data.entity.ELogin
import com.zczy.comm.ui.BaseActivity
import com.zczy.comm.utils.toJson
import com.zczy.comm.utils.toJsonArray
import com.zczy.comm.widget.dialog.NormalInfoDialog
import kotlinx.android.synthetic.main.user_switch_account_activity.appToolbar
import kotlinx.android.synthetic.main.user_switch_account_activity.recycler_view

/**
 *  desc: 切换账号
 *  user: ssp
 *  time: 2024/7/23 19:45
 */

@SuppressLint("SetTextI18n")
class UserSwitchAccountActivity : BaseActivity<BaseViewModel>() {

    private val mAdapter = UserSwitchAccountAdapter()
    private var inflateView: View? = null
    private var authenticationDialog: AuthenticationDialog? = null
    private var itemSwitch: RspQueryUserInfosByUserIds? = null

    companion object {
        const val LOGIN_ACCOUNT_LIST = "LOGIN_ACCOUNT_LIST"

        @JvmStatic
        fun start(context: Context?) {
            val intent = Intent(context, UserSwitchAccountActivity::class.java)
            context?.startActivity(intent)
        }
    }

    override fun getLayout(): Int {
        return R.layout.user_switch_account_activity
    }

    override fun bindView(bundle: Bundle?) {
        appToolbar.setRightOnClickListener {
            //管理或取消
            val text = appToolbar.tvRight.text
            when (text) {
                "管理" -> {
                    appToolbar.tvRight.text = "取消"
                    mAdapter.refreshOperation(showClear = true)
                    mAdapter.removeFooterView(inflateView)
                }

                "取消" -> {
                    appToolbar.tvRight.text = "管理"
                    mAdapter.refreshOperation(showClear = false)
                    mAdapter.addFooterView(inflateView)
                }
            }
        }
        inflateView = layoutInflater.inflate(R.layout.user_switch_account_footer_view, null)
        inflateView?.setOnClickListener {
            //登录其他账号
            val list = AppCacheManager.getCacheEditServer().getString(LOGIN_ACCOUNT_LIST, null).toJsonArray(ELogin::class.java) ?: mutableListOf()
            if (list.size < 10) {
                // 登录其他账号
                UserSwitchAccountLoginActivity.startSwitch(this@UserSwitchAccountActivity, "")
            } else {
                val dialogBuilder = DialogBuilder()
                dialogBuilder.title = "提示"
                dialogBuilder.message = "当前设备管理账号已达到上限，建议清除无效设备后添加"
                dialogBuilder.setCancelTextListener("放弃") { dialog, _ ->
                    dialog.dismiss()
                }
                dialogBuilder.setOKTextListener("去清除") { dialog, _ ->
                    dialog.dismiss()
                    appToolbar.tvRight.text = "取消"
                    mAdapter.refreshOperation(showClear = true)
                    mAdapter.removeFooterView(inflateView)
                }
            }
        }
        mAdapter.addFooterView(inflateView)
        mAdapter.bindToRecyclerView(recycler_view)
        mAdapter.setOnItemClickListener { _, _, position ->
            val item = mAdapter.data[position]
            val list = AppCacheManager.getCacheEditServer().getString(LOGIN_ACCOUNT_LIST, null).toJsonArray(ELogin::class.java) ?: mutableListOf()
            val req = AccountBuilder()
            req.setLoginTokenId(list.findTokenId(item.userId ?: "", item.nooType ?: ""))
            req.setSwitchLoginFlag(true)
            req.setLoginName(item.userNm)
            getViewModel(LoginAccountModel::class.java).login(req.bulder())
        }
        mAdapter.setOnItemChildClickListener { _, view, position ->
            val item = mAdapter.data[position]
            when (view.id) {
                R.id.tv3 -> {
                    //清除账号
                    val dialog = UserClearAccountDialog(userNm = item.userNm) { clear ->
                        if (clear) {
                            mAdapter.remove(position)
                            val list = AppCacheManager.getCacheEditServer().getString(LOGIN_ACCOUNT_LIST, null).toJsonArray(ELogin::class.java) ?: mutableListOf()
                            val filter = list.filter { ob -> !TextUtils.equals(ob.userId, item.userId) }
                            AppCacheManager.getCacheEditServer().putCache(LOGIN_ACCOUNT_LIST, filter.toJson())
                        }
                    }
                    dialog.show(this@UserSwitchAccountActivity)
                }
            }
        }
        recycler_view.apply {
            layoutManager = LinearLayoutManager(this@UserSwitchAccountActivity)
            adapter = mAdapter
        }
    }

    override fun initData() {
        val list = AppCacheManager.getCacheEditServer().getString(LOGIN_ACCOUNT_LIST, null).toJsonArray(ELogin::class.java) ?: mutableListOf()
        if (list.isEmpty()) {
            return
        }
        getViewModel(UserSwitchModel::class.java).execute(ReqQueryUserInfosByUserIds(userIdList = list.findUserId())) {
            if (it.success()) {
                mAdapter.setNewData(it.data?.rootArray)
            } else {
                showToast(it.msg)
            }
        }
    }

    @LiveDataMatch
    open fun loginError(msg: String?) {
        UserLoginOtherErrorDialogActivity.startContentUI(this, msg)
    }

    @LiveDataMatch
    open fun toLoginView(loginName: String?) {
        //打开登录页面
        UserSwitchAccountLoginActivity.startSwitch(this@UserSwitchAccountActivity, loginName)
        finish()
    }

    // 去验证码登录
    @LiveDataMatch
    open fun gotoLoginSMS(msg: String?) {
        val builder = DialogBuilder()
        builder.setTitle("提示")
        builder.setMessage(msg)
        builder.setOkListener { dialogInterface: DialogBuilder.DialogInterface, _: Int ->
            dialogInterface.dismiss()
            UserSwitchPhoneLoginActivity.startSwitch(this@UserSwitchAccountActivity, "")
            finish()
        }
        showDialog(builder)
    }

    @LiveDataMatch(tag = "去注册")
    open fun onRegiester() {
        RegisterStep1Activity.start(this, "", itemSwitch?.userNm)
    }

    @LiveDataMatch
    open fun loginNoAccountError(msg: String?) {
        UserLoginErrorDialogActivity.startContentUI(this, msg, itemSwitch?.userNm ?: "")
    }

    @LiveDataMatch(tag = "新设备认证短信发送成功")
    open fun onSendAuthenticationCodeSuccess(login: ELogin?) {
        //新设备认证提示
        if (this.authenticationDialog == null) {
            this.authenticationDialog = AuthenticationDialog(this).setListener(object : AuthenticationDialog.Listener {
                override fun onReSendCode(data: ELogin) {
                    // 重新发送验证码
                    getViewModel(LoginAccountModel::class.java).sendAuthenticationSMS(data)
                }

                override fun onCommit(data: ELogin, code: String) {
                    // 认证
                    getViewModel(LoginAccountModel::class.java).checkAuthenticationCode(data, code)
                }
            })
        }
        authenticationDialog?.setLogin(login)
        val showing = authenticationDialog?.isShowing ?: false
        if (!showing) {
            authenticationDialog?.show()
        }
    }

    @LiveDataMatch(tag = "登录成功 or 新设备认证提示")
    open fun onLoginSuccess(login: ELogin) {
        AppCacheManager.putCache<String>("login_name", itemSwitch?.userNm)
        gotoHome(login)
    }

    @LiveDataMatch(tag = "登录ZCZY-10873 供应链会员中台-用户部分")
    open fun onLoginSuccessToast(login: ELogin) {
        SelectPersonDialog(this, login.notUserTypeMsg) { dialog, which ->
            dialog.dismiss()
            if (which == 1) {
                //确认认证
                getViewModel(LoginAccountModel::class.java).switchUserType()
            } else {
                //退出登录
                finish()
                val application = AppCacheManager.getApplication<ZczyApplication>()
                application.onLoseToken("", "")
            }
        }.show()
    }

    @LiveDataMatch
    open fun onSwitchUserTypeSuccess() {
        //确认认证
        toHome()
        CertificationUtils.hzCertification(this@UserSwitchAccountActivity)
        finish()
    }

    private fun gotoHome(login: ELogin) {
        if (TextUtils.equals("1", login.wheterChangePassword)) {
            this.showNeedEditPwdDialog(object : NormalInfoDialog.NormalInfoDialogListener() {
                override fun onClickDefRightBtn(dialog: NormalInfoDialog) {
                    dialog.dismiss()
                    UserEditPasswordctivity.startUI(this@UserSwitchAccountActivity)
                }

                override fun onClickLeftBtn(dialog: NormalInfoDialog) {
                    dialog.dismiss()
                    toHome()
                }
            })
        } else {
            toHome()
        }
    }

    private fun toHome() {
        RxBusEventManager.postEvent(RxReStarHome(reStar = true))
        Handler(Looper.getMainLooper()).postDelayed({
            HomeActivity.start(this@UserSwitchAccountActivity)
            this.finish()
        }, 500)
    }

}