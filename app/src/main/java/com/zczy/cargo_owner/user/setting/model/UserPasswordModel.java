package com.zczy.cargo_owner.user.setting.model;

import com.sfh.lib.rx.IResultSuccess;
import com.zczy.comm.data.SMSCodeModel;
import com.zczy.comm.data.request.ReqSendCode;
import com.zczy.comm.http.entity.BaseRsp;
import com.zczy.comm.http.entity.ResultData;


/**
 * 功能描述: 修改密码1
 *
 * <AUTHOR> 孙飞虎
 * @company 中储智运科技股份有限公司
 * @copyright （版权）中储南京智慧物流科技有限公司所有
 * @date 2018/11/13
 */
public class UserPasswordModel extends SMSCodeModel {

    @Override
    public void onSendCode(boolean success, String type) {
        this.setValue("onSendCodeSuccess", success, type);
    }

    @Override
    public void onShowImageVerifyCode(ReqSendCode req) {
        this.setValue("onShowImageVerifyCode", req);
    }

    @Override
    public void onCheckSuccess() {
        this.setValue("onCheckSuccess");
    }

    /***
     * 修改密码
     * @param req
     */
    public void savePassword(ReqPassword req) {


        this.execute(false, req, new IResultSuccess<BaseRsp<ResultData>> () {
            @Override
            public void onSuccess(BaseRsp<ResultData> resultDataBaseRsp) throws Exception {
                if (resultDataBaseRsp.success()) {
                    setValue("onSaveSuccess",resultDataBaseRsp.getMsg());
                } else {
                    showDialogToast(resultDataBaseRsp.getMsg());
                }
            }
        });
    }

}
