package com.zczy.cargo_owner.user.certification.bean;

import com.zczy.comm.http.entity.ResultData;

/**
 * 功能描述:
 *
 * <AUTHOR>
 * @company 中储智运科技股份有限公司
 * @copyright （版权）中储南京智慧物流科技有限公司所有
 * @date 2019/2/14
 */
public class MemberDetails extends ResultData {


    /**
     * frontIdCardUrl : 201811011542477487016.jpg
     * companyName : 测试公司
     * idCardNo : 32012319545142102
     * headUrl : 1281012010787537917201811071557076553184.jpg
     * roadTransportPermitUrl : null
     * busiLicUrl : null
     * vehicleHeight : null
     * vehicleId : null
     * trailerNewUrl : null
     * roadCertificateNum : null
     * vehicleType : null
     * negativeIdCardUrl : 201811011542559906426.jpg
     * transportUrl : 201810151641383403023.jpg
     * triverPermitUrl : null
     * personCarUrl : 201810151641407744274.jpg
     * vehicleLoad : null
     * mobile : ***********
     * vehicleFlag : null
     * examineType : 1
     * plateNumber : null
     * userId : 1281012010787537917
     * roadTransportDate : null
     * customerName : null
     * idCardEffectDate : 1539187200000
     * contacter : null
     * qualificateNo : 32012319545142102
     * vehicleLength : null
     * examineNewType : 3
     * vehicleWidth : null
     * contacterPhone : null
     * businessPermit : null
     * plantainUrl : null
     * userType : 2
     * driverLicUrl : 201810151641354580221.jpg
     */

    private String provinceName; //注册省份
    private String cityName; //注册城市
    private String areaName; //注册区域
    private String registerSystem;//注册系统：船运、汽运
    private String registerChannel; //注册渠道
    private String userNm; // 用户名
    private String isEntrustRegister; // 1非法人注册 0 法人注册
    private String nonLegalIdCardUrl;//非法人注册身份证图片正面
    private String legalIdCardUrl;//法人身份证照
    private String nonLegalAuthUrl;//非法人注册授权委托书
    private String frontIdCardUrl;
    private String companyName;
    private String idCardNo;
    private String headUrl;
    private String roadTransportPermitUrl;
    private String busiLicUrl;
    private String vehicleHeight;
    private String vehicleId;
    private String trailerNewUrl;
    private String roadCertificateNum;
    private String vehicleType;
    private String negativeIdCardUrl;
    private String transportUrl;
    private String triverPermitUrl; ////车头行驶证||行驶证照片
    private String personCarUrl;
    private String vehicleLoad;
    private String mobile;
    private String vehicleFlag;
    private int examineType;
    private String plateNumber;
    private String userId;
    private String roadTransportDate;
    private String customerName; // 用户名称
    private String idCardEffectDate;
    private String contacter; // 用户名称
    private String qualificateNo;
    private String vehicleLength;
    private int examineNewType;
    private String vehicleWidth;
    private String contacterPhone;
    private String businessPermit;
    private String plantainUrl;
    private String userType;
    private String driverLicUrl;
    private String receptRemark; // 回单备注
    private String receiptTemplateUrl;//回单样板照片
    private String companyNameEditFlag;//0-不能改，其他情况都能改
    private String taxId;//统一社会信用代码

    public String getTaxId() {
        return taxId;
    }

    public void setTaxId(String taxId) {
        this.taxId = taxId;
    }

    public String getCompanyNameEditFlag() {
        return companyNameEditFlag;
    }

    public void setCompanyNameEditFlag(String companyNameEditFlag) {
        this.companyNameEditFlag = companyNameEditFlag;
    }

    public String getReceiptTemplateUrl() {
        return receiptTemplateUrl;
    }

    public void setReceiptTemplateUrl(String receiptTemplateUrl) {
        this.receiptTemplateUrl = receiptTemplateUrl;
    }

    public String getLegalIdCardUrl() {
        return legalIdCardUrl;
    }

    public void setLegalIdCardUrl(String legalIdCardUrl) {
        this.legalIdCardUrl = legalIdCardUrl;
    }

    public String getReceptRemark() {
        return receptRemark;
    }

    public void setReceptRemark(String receptRemark) {
        this.receptRemark = receptRemark;
    }

    public String getProvinceName() {
        return provinceName;
    }

    public void setProvinceName(String provinceName) {
        this.provinceName = provinceName;
    }

    public String getCityName() {
        return cityName;
    }

    public void setCityName(String cityName) {
        this.cityName = cityName;
    }

    public String getAreaName() {
        return areaName;
    }

    public void setAreaName(String areaName) {
        this.areaName = areaName;
    }

    public String getRegisterSystem() {
        return registerSystem;
    }

    public void setRegisterSystem(String registerSystem) {
        this.registerSystem = registerSystem;
    }

    public String getRegisterChannel() {
        return registerChannel;
    }

    public void setRegisterChannel(String registerChannel) {
        this.registerChannel = registerChannel;
    }

    public String getUserNm() {
        return userNm;
    }

    public void setUserNm(String userNm) {
        this.userNm = userNm;
    }

    public String getIsEntrustRegister() {
        return isEntrustRegister;
    }

    public void setIsEntrustRegister(String isEntrustRegister) {
        this.isEntrustRegister = isEntrustRegister;
    }

    public String getNonLegalIdCardUrl() {
        return nonLegalIdCardUrl;
    }

    public void setNonLegalIdCardUrl(String nonLegalIdCardUrl) {
        this.nonLegalIdCardUrl = nonLegalIdCardUrl;
    }

    public String getNonLegalAuthUrl() {
        return nonLegalAuthUrl;
    }

    public void setNonLegalAuthUrl(String nonLegalAuthUrl) {
        this.nonLegalAuthUrl = nonLegalAuthUrl;
    }

    public String getFrontIdCardUrl() {
        return frontIdCardUrl;
    }

    public void setFrontIdCardUrl(String frontIdCardUrl) {
        this.frontIdCardUrl = frontIdCardUrl;
    }

    public String getCompanyName() {
        return companyName;
    }

    public void setCompanyName(String companyName) {
        this.companyName = companyName;
    }

    public String getIdCardNo() {
        return idCardNo;
    }

    public void setIdCardNo(String idCardNo) {
        this.idCardNo = idCardNo;
    }

    public String getHeadUrl() {
        return headUrl;
    }

    public void setHeadUrl(String headUrl) {
        this.headUrl = headUrl;
    }

    public String getRoadTransportPermitUrl() {
        return roadTransportPermitUrl;
    }

    public void setRoadTransportPermitUrl(String roadTransportPermitUrl) {
        this.roadTransportPermitUrl = roadTransportPermitUrl;
    }

    public String getBusiLicUrl() {
        return busiLicUrl;
    }

    public void setBusiLicUrl(String busiLicUrl) {
        this.busiLicUrl = busiLicUrl;
    }

    public String getVehicleHeight() {
        return vehicleHeight;
    }

    public void setVehicleHeight(String vehicleHeight) {
        this.vehicleHeight = vehicleHeight;
    }

    public String getVehicleId() {
        return vehicleId;
    }

    public void setVehicleId(String vehicleId) {
        this.vehicleId = vehicleId;
    }

    public String getTrailerNewUrl() {
        return trailerNewUrl;
    }

    public void setTrailerNewUrl(String trailerNewUrl) {
        this.trailerNewUrl = trailerNewUrl;
    }

    public String getRoadCertificateNum() {
        return roadCertificateNum;
    }

    public void setRoadCertificateNum(String roadCertificateNum) {
        this.roadCertificateNum = roadCertificateNum;
    }

    public String getVehicleType() {
        return vehicleType;
    }

    public void setVehicleType(String vehicleType) {
        this.vehicleType = vehicleType;
    }

    public String getNegativeIdCardUrl() {
        return negativeIdCardUrl;
    }

    public void setNegativeIdCardUrl(String negativeIdCardUrl) {
        this.negativeIdCardUrl = negativeIdCardUrl;
    }

    public String getTransportUrl() {
        return transportUrl;
    }

    public void setTransportUrl(String transportUrl) {
        this.transportUrl = transportUrl;
    }

    public String getTriverPermitUrl() {
        return triverPermitUrl;
    }

    public void setTriverPermitUrl(String triverPermitUrl) {
        this.triverPermitUrl = triverPermitUrl;
    }

    public String getPersonCarUrl() {
        return personCarUrl;
    }

    public void setPersonCarUrl(String personCarUrl) {
        this.personCarUrl = personCarUrl;
    }

    public String getVehicleLoad() {
        return vehicleLoad;
    }

    public void setVehicleLoad(String vehicleLoad) {
        this.vehicleLoad = vehicleLoad;
    }

    public String getMobile() {
        return mobile;
    }

    public void setMobile(String mobile) {
        this.mobile = mobile;
    }

    public String getVehicleFlag() {
        return vehicleFlag;
    }

    public void setVehicleFlag(String vehicleFlag) {
        this.vehicleFlag = vehicleFlag;
    }

    public int getExamineType() {
        return examineType;
    }

    public void setExamineType(int examineType) {
        this.examineType = examineType;
    }

    public String getPlateNumber() {
        return plateNumber;
    }

    public void setPlateNumber(String plateNumber) {
        this.plateNumber = plateNumber;
    }

    public String getUserId() {
        return userId;
    }

    public void setUserId(String userId) {
        this.userId = userId;
    }

    public String getRoadTransportDate() {
        return roadTransportDate;
    }

    public void setRoadTransportDate(String roadTransportDate) {
        this.roadTransportDate = roadTransportDate;
    }

    public String getCustomerName() {
        return customerName;
    }

    public void setCustomerName(String customerName) {
        this.customerName = customerName;
    }

    public String getIdCardEffectDate() {
        return idCardEffectDate;
    }

    public void setIdCardEffectDate(String idCardEffectDate) {
        this.idCardEffectDate = idCardEffectDate;
    }

    public String getContacter() {
        return contacter;
    }

    public void setContacter(String contacter) {
        this.contacter = contacter;
    }

    public String getQualificateNo() {
        return qualificateNo;
    }

    public void setQualificateNo(String qualificateNo) {
        this.qualificateNo = qualificateNo;
    }

    public String getVehicleLength() {
        return vehicleLength;
    }

    public void setVehicleLength(String vehicleLength) {
        this.vehicleLength = vehicleLength;
    }

    public int getExamineNewType() {
        return examineNewType;
    }

    public void setExamineNewType(int examineNewType) {
        this.examineNewType = examineNewType;
    }

    public String getVehicleWidth() {
        return vehicleWidth;
    }

    public void setVehicleWidth(String vehicleWidth) {
        this.vehicleWidth = vehicleWidth;
    }

    public String getContacterPhone() {
        return contacterPhone;
    }

    public void setContacterPhone(String contacterPhone) {
        this.contacterPhone = contacterPhone;
    }

    public String getBusinessPermit() {
        return businessPermit;
    }

    public void setBusinessPermit(String businessPermit) {
        this.businessPermit = businessPermit;
    }

    public String getPlantainUrl() {
        return plantainUrl;
    }

    public void setPlantainUrl(String plantainUrl) {
        this.plantainUrl = plantainUrl;
    }

    public String getUserType() {
        return userType;
    }

    public void setUserType(String userType) {
        this.userType = userType;
    }

    public String getDriverLicUrl() {
        return driverLicUrl;
    }

    public void setDriverLicUrl(String driverLicUrl) {
        this.driverLicUrl = driverLicUrl;
    }
}

