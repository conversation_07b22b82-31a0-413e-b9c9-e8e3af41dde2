package com.zczy.cargo_owner.user.setting.safe;

import android.content.Context;
import android.content.Intent;
import android.graphics.Color;
import android.os.Bundle;
import android.text.Editable;
import android.text.TextUtils;
import android.text.TextWatcher;
import android.view.KeyEvent;
import android.view.View;
import android.widget.Button;
import android.widget.ImageView;

import com.sfh.lib.mvvm.annotation.LiveDataMatch;
import com.sfh.lib.rx.IResultSuccess;
import com.sfh.lib.rx.ui.UtilRxView;
import com.sfh.lib.ui.AbstractLifecycleActivity;
import com.sfh.lib.ui.dialog.DialogBuilder;
import com.sfh.lib.utils.UtilSoftKeyboard;
import com.zczy.cargo_owner.R;
import com.zczy.cargo_owner.user.setting.model.ReqPassword;
import com.zczy.cargo_owner.user.setting.model.UserPasswordModel;
import com.zczy.comm.data.request.ReqSendCode;
import com.zczy.comm.ui.UtilStatus;
import com.zczy.comm.utils.CommUtils;
import com.zczy.comm.widget.EditTextCloseView;

import java.util.regex.Pattern;

import io.reactivex.disposables.Disposable;

/**
 * 功能描述: 修改电话服务密码2
 *
 * <AUTHOR> 孙飞虎
 * @company 中储智运科技股份有限公司
 * @copyright （版权）中储南京智慧物流科技有限公司所有
 * @date 2018/11/19
 */
public class UserEditSafePasswordTwoctivity extends AbstractLifecycleActivity<UserPasswordModel> implements TextWatcher, View.OnClickListener {

    public static void start(Context context,String oldMobile, String verifyCode, String verifyCodeType,String oldModuleType) {
        Intent intent = new Intent(context, UserEditSafePasswordTwoctivity.class);
        intent.putExtra("verifyCode", verifyCode);
        intent.putExtra("verifyCodeType", verifyCodeType);
        intent.putExtra("oldModuleType",oldModuleType);
        intent.putExtra("oldMobile",oldMobile);
        context.startActivity(intent);
    }


    private EditTextCloseView etPwd;
    private ImageView tvLook;
    private EditTextCloseView etPwd2;
    private ImageView tvLook2;
    private Button btOK;
    String verifyCode;
    String verifyCodeType;
    private String oldMobile;
    private String oldModuleType;

    @Override
    protected void onCreate(Bundle savedInstanceState) {
        super.onCreate(savedInstanceState);
        setContentView(R.layout.user_set_safe_edit_password_two_activity);
        UtilStatus.initStatus(this, Color.WHITE);
        verifyCode = getIntent().getStringExtra("verifyCode");
        verifyCodeType = getIntent().getStringExtra("verifyCodeType");
        oldMobile = getIntent().getStringExtra("oldMobile");
        oldModuleType = getIntent().getStringExtra("oldModuleType");
        etPwd = (EditTextCloseView) findViewById(R.id.etPwd);
        tvLook = (ImageView) findViewById(R.id.tvLook);
        etPwd2 = (EditTextCloseView) findViewById(R.id.etPwd2);
        tvLook2 = (ImageView) findViewById(R.id.tvLook2);
        btOK = (Button) findViewById(R.id.btOK);
        etPwd.addTextChangedListener(this);
        etPwd2.addTextChangedListener(this);
        tvLook.setOnClickListener(this);
        tvLook2.setOnClickListener(this);

        Disposable disposable = UtilRxView.clicks(btOK, 1000, new IResultSuccess<Object>() {
            @Override
            public void onSuccess(Object o) throws Exception {

                UtilSoftKeyboard.hide(btOK);

                String password = etPwd.getText().toString();
                if (TextUtils.isEmpty(password)) {
                    showDialogToast("请输入密码");
                    return;
                }
                String password2 = etPwd2.getText().toString();
                if (TextUtils.isEmpty(password2)) {
                    showDialogToast("请输入确认密码");
                    return;
                }
                if (!TextUtils.equals(password2, password)) {
                    showDialogToast("两次密码不一致,请重新输入");
                    return;
                }
                if (password.length() != 6) {
                    showDialogToast("密码为6位字符，只能为数字");
                    return;
                }
                //数字不是连续的
                String regres = "^(?:(\\d)(?!((?<=9)8|(?<=8)7|(?<=7)6|(?<=6)5|(?<=5)4|(?<=4)3|(?<=3)2|(?<=2)1|(?<=1)0){5})(?!\1{5})(?!((?<=0)1|(?<=1)2|(?<=2)3|(?<=3)4|(?<=4)5|(?<=5)6|(?<=6)7|(?<=7)8|(?<=8)9){5})){6}$";
                //数字不是重复的
                String reg = "^(?=.*\\d+)(?!.*?([\\d])\\1{5})[\\d]{6}$";

                if (!Pattern.matches(regres, password) || !Pattern.matches(reg, password)) {
                    showDialogToast("密码为6位数字，且不重复不连续！");
                    return;
                }

                ReqPassword req = new ReqPassword();
                req.setModuleType(ReqSendCode.MODULE_TYPE_B);
                req.setNewPwd(etPwd.getText().toString());
                req.setVerifyCod(verifyCode);
                req.setVerifyCodeType(verifyCodeType);
                req.setOldMobile(oldMobile);
                req.setOldModuleType(oldModuleType);
                getViewModel().savePassword(req.buildSafePassword());
            }
        });
        this.putDisposable(disposable);
    }

    @Override
    public boolean onKeyDown(int keyCode, KeyEvent event) {
        if (keyCode == KeyEvent.KEYCODE_BACK) {
            DialogBuilder dialogBuilder = new DialogBuilder();
            dialogBuilder.setTitle("提示");
            dialogBuilder.setMessage("确定放弃修改密码？");
            dialogBuilder.setOkListener(new DialogBuilder.DialogInterface.OnClickListener() {
                @Override
                public void onClick(DialogBuilder.DialogInterface dialogInterface, int i) {
                    dialogInterface.dismiss();
                    finish();
                }
            });
            showDialog(dialogBuilder);
            return true;
        }
        return super.onKeyDown(keyCode, event);
    }

    @LiveDataMatch(tag = "修改密码成功")
    public void onSaveSuccess(String toast) {

        showToast("修改成功");
        finish();
    }
    @Override
    public void beforeTextChanged(CharSequence s, int start, int count, int after) {

    }

    @Override
    public void onTextChanged(CharSequence s, int start, int before, int count) {

    }

    @Override
    public void afterTextChanged(Editable s) {
        if (TextUtils.isEmpty(etPwd.getText().toString()) || TextUtils.isEmpty(etPwd2.getText().toString())) {
            btOK.setEnabled(false);
        } else {
            btOK.setEnabled(true);
        }
    }

    @Override
    public void onClick(View v) {
        if (tvLook == v) {
            boolean look = CommUtils.lookEditPassWord(etPwd);
            tvLook.setImageResource(look ? R.drawable.user_register_look_ok : R.drawable.user_register_look_no);

        } else if (tvLook2 == v) {

            boolean look = CommUtils.lookEditPassWord(etPwd2);
            tvLook2.setImageResource(look ? R.drawable.user_register_look_ok : R.drawable.user_register_look_no);

        }
    }
}
