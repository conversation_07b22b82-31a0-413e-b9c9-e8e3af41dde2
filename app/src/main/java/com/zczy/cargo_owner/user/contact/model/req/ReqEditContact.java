package com.zczy.cargo_owner.user.contact.model.req;

import com.zczy.cargo_owner.user.contact.model.resp.RespAddContactData;
import com.zczy.cargo_owner.user.contact.model.resp.RespEditContactData;
import com.zczy.comm.http.entity.BaseNewRequest;
import com.zczy.comm.http.entity.BaseRsp;

public class ReqEditContact extends BaseNewRequest<BaseRsp<RespEditContactData>> {
    public ReqEditContact() {
        super("mms-app/consignorTelPhone/editConsignorTelphone");
    }

    public long id;//通讯录id
    public String fullName;//姓名
    public String mobile; //手机号
    public String contactsType; //类型 1:挂单  2:装卸货  3:回单  4:轨迹  5:结算  6:财务  7:其他
    public String contactStartTime; //开始时间
    public String contactEndTime;    //结束时间
    public String remark; //备注
}
