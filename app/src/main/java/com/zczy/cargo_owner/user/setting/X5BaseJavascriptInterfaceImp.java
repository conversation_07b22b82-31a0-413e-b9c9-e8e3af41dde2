package com.zczy.cargo_owner.user.setting;

import android.content.Context;
import android.content.Intent;
import android.content.pm.PackageManager;
import android.graphics.Bitmap;
import android.graphics.BitmapFactory;
import android.net.Uri;
import android.os.Environment;
import android.util.Base64;
import android.webkit.JavascriptInterface;
import android.widget.Toast;

import androidx.fragment.app.FragmentActivity;

import com.google.gson.Gson;
import com.sfh.lib.ui.AbstractLifecycleActivity;
import com.sfh.lib.utils.UtilLog;
import com.sfh.lib.utils.UtilTool;
import com.zczy.comm.CommServer;
import com.zczy.comm.data.entity.ELogin;
import com.zczy.comm.pluginserver.BaseServer;
import java.io.File;
import java.io.FileOutputStream;
import java.lang.ref.WeakReference;

import io.reactivex.annotations.Nullable;

public class X5BaseJavascriptInterfaceImp {

    @Nullable
    private WeakReference<FragmentActivity> activity;

    public X5BaseJavascriptInterfaceImp(FragmentActivity activity) {
        this.activity = new WeakReference<>(activity);
    }

    public void destroy() {
        activity = null;
    }

    @JavascriptInterface
    public void copyOrderId(String code) {
        if (activity != null && activity.get() != null) {
            UtilTool.setCopyText(activity.get(), "单号", code);
            Toast.makeText(activity.get(), "复制单号成功", Toast.LENGTH_SHORT).show();
        }
    }

    @JavascriptInterface
    public void certifcate() {
        if (activity != null) {
            //去认证会员
            BaseServer mainServer = BaseServer.getLoginPluginServer();
            if (mainServer != null && activity.get() != null) {
                mainServer.userAuthent(activity.get());
            }
        }
    }

    @JavascriptInterface
    public void callUp(String phone) {
//        拨打电话（跳转到拨号界面，用户手动点击拨打）
        Context context = activity.get();
        if (context != null) {
            Intent intent = new Intent(Intent.ACTION_DIAL);
            intent.setFlags(Intent.FLAG_ACTIVITY_NEW_TASK);
            intent.setData(Uri.parse("tel:" + phone));
            if (context.getPackageManager().resolveActivity(intent, PackageManager.MATCH_DEFAULT_ONLY) != null) {
                context.startActivity(intent);
            } else {
                Toast.makeText(context, "无法拨打电话，请检查应用权限设置", Toast.LENGTH_SHORT).show();
            }
        }
    }

    @JavascriptInterface
    public void finishActivity() {
        if (activity != null && activity.get() != null) {
            activity.get().finish();
        }
    }

    @JavascriptInterface
    public String getUserInfo() {
        ELogin login = CommServer.getUserServer().getLogin();
        if (login != null) {
            String s = new Gson().toJson(login);
            UtilLog.e("X5JavascriptInterface", "login= " + s);
            return s;
        }
        return "";
    }

    @JavascriptInterface
    public void needLogin() {
        if (activity != null && activity.get() != null) {
            BaseServer server = BaseServer.getLoginPluginServer();
            server.openLogin(activity.get());
        }
    }

    @JavascriptInterface
    public void showToastDialog(String msg) {
        if (activity != null && activity.get() != null) {
            if (activity.get() instanceof AbstractLifecycleActivity) {
                ((AbstractLifecycleActivity) activity.get()).showDialogToast(msg);
            }
        }
    }
    private void saveToLocal(Bitmap bitmap) {
        File file = new File(Environment.getExternalStorageDirectory() + "/zczy/" + "medal_" + System.currentTimeMillis() + ".jpg");
        if (file.exists()) {
            file.delete();
        }
        FileOutputStream out;
        try {
            out = new FileOutputStream(file);
            if (bitmap.compress(Bitmap.CompressFormat.PNG, 70, out)) {
                out.flush();
                out.close();
                //保存图片后发送广播通知更新数据库
                Intent intent = new Intent(Intent.ACTION_MEDIA_SCANNER_SCAN_FILE);
                Uri uri = Uri.fromFile(file);
                intent.setData(uri);
                activity.get().sendBroadcast(intent);
                Toast.makeText(activity.get(), "保存成功", Toast.LENGTH_SHORT).show();
            }
        } catch (Exception e) {
            e.printStackTrace();
        }
    }

    public Bitmap stringToBitmap(String string) {
        Bitmap bitmap = null;

        try {
            byte[] bitmapArray = Base64.decode(string.split(",")[1], Base64.DEFAULT);

            bitmap = BitmapFactory.decodeByteArray(bitmapArray, 0, bitmapArray.length);

        } catch (Exception e) {
            e.printStackTrace();

        }

        return bitmap;

    }
}
