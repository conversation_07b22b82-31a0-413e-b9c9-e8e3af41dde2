package com.zczy.cargo_owner.user.certification;

import android.content.Context;
import android.content.Intent;
import android.os.Bundle;
import androidx.annotation.Nullable;
import android.widget.TextView;

//import com.wbtech.ums.UmsAgent;
import com.zczy.cargo_owner.R;
import com.zczy.cargo_owner.home.HomeActivity;
import com.zczy.comm.CommServer;
import com.zczy.comm.data.entity.ELogin;
import com.zczy.comm.pluginserver.AMainServer;
import com.zczy.comm.ui.BaseActivity;
import com.zczy.comm.widget.AppToolber;

/**
 * 货主认证，实名认证已提交
 */
public class HzCertificationAuditActivity extends BaseActivity {

    private AppToolber mAppToolber;


    public static void start(Context context) {
        Intent intent = new Intent(context, HzCertificationAuditActivity.class);
        context.startActivity(intent);
    }

    @Override
    protected int getLayout() {
        return R.layout.hz_certification_audit_activity;
    }

    @Override
    protected void bindView(@Nullable Bundle bundle) {
        mAppToolber = (AppToolber) findViewById(R.id.appToolber);
        /**
         * 我知道了
         */
        TextView mTvNext = findViewById(R.id.tv_next);
        mTvNext.setOnClickListener(view -> {
            String userId = "";
            ELogin login = CommServer.getUserServer().getLogin();
            if (login != null) userId = login.getUserId();
//            UmsAgent.onEvent(this, "certificationSuccessHz",userId);
            HomeActivity.start(this);
        });
    }

    @Override
    protected void initData() {
        mAppToolber.setRightOnClickListener(v -> {
            AMainServer mainServer = AMainServer.getPluginServer();
            if (mainServer != null) {
                mainServer.showLineServerPhone(this);
            }
        });
    }

}
