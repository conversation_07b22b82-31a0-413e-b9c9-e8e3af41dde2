package com.zczy.cargo_owner.user.setting.safe;

import android.content.Context;
import android.content.Intent;
import android.graphics.Color;
import android.os.Bundle;
import android.text.Editable;
import android.text.TextUtils;
import android.text.TextWatcher;
import android.widget.Button;
import android.widget.LinearLayout;
import android.widget.TextView;

import com.sfh.lib.mvvm.annotation.LiveDataMatch;
import com.sfh.lib.rx.IResultSuccess;
import com.sfh.lib.rx.ui.UtilRxView;
import com.sfh.lib.ui.AbstractLifecycleActivity;
import com.sfh.lib.utils.UtilSoftKeyboard;
import com.zczy.cargo_owner.R;
import com.zczy.cargo_owner.user.setting.model.UserPasswordModel;
import com.zczy.comm.CommServer;
import com.zczy.comm.data.entity.ELogin;
import com.zczy.comm.data.request.ReqCheckCode;
import com.zczy.comm.data.request.ReqSendCode;
import com.zczy.comm.ui.UtilStatus;
import com.zczy.comm.utils.VerificationCodeUtil;
import com.zczy.comm.widget.EditTextCloseView;
import com.zczy.comm.widget.ImageCodeDialog;
import com.zczy.comm.widget.RxTimeCountView;

import io.reactivex.disposables.Disposable;


/**
 * 功能描述: 修改电话服务密码1
 *
 * <AUTHOR> 孙飞虎
 * @company 中储智运科技股份有限公司
 * @copyright （版权）中储南京智慧物流科技有限公司所有
 * @date 2018/11/19
 */
public class UserEditSafePasswordctivity extends AbstractLifecycleActivity<UserPasswordModel> implements TextWatcher {

    public static void startUI(Context context) {

        Intent intent = new Intent (context, UserEditSafePasswordctivity.class);
        context.startActivity (intent);
    }

    private TextView tvPhone;

    private EditTextCloseView etPwd;

    private RxTimeCountView tvTime;

    private LinearLayout lyVoice;

    private RxTimeCountView tvVoiceTime;

    private Button btLogin;

    String verifyCodeType = ReqSendCode.TYPE_SMS;

    VerificationCodeUtil util;

    String phone;

    @Override
    protected void onCreate(Bundle savedInstanceState) {

        super.onCreate (savedInstanceState);
        setContentView (R.layout.user_set_safe_edit_password_one_activity);
        UtilStatus.initStatus(this, Color.WHITE);
        tvPhone = findViewById (R.id.tvPhone);
        etPwd = findViewById (R.id.etPwd);
        tvTime = findViewById (R.id.tvTime);
        lyVoice = findViewById (R.id.lyVoice);
        tvVoiceTime = findViewById (R.id.tvVoiceTime);
        btLogin = findViewById (R.id.btLogin);

        etPwd.addTextChangedListener (this);

        ELogin login = CommServer.getUserServer ().getLogin ();
        if (login != null) {
            phone = login.getMobile ();
            tvPhone.setText (phone.replaceAll("(\\d{3})\\d{4}(\\d{4})","$1****$2"));
        }
        util = new VerificationCodeUtil(ReqSendCode.MODULE_TYPE_B, new VerificationCodeUtil.IOnCallback () {

            @Override
            public String getPhone() {

                return phone;
            }

            @Override
            public void onClickCode(ReqSendCode req) {

                verifyCodeType = req.getType ();
                getViewModel ().showImageVerifyCode (req);
            }

            @Override
            public void showToast(CharSequence toast) {

                UserEditSafePasswordctivity.this.showDialogToast (toast);
            }
        }).build (tvTime, tvVoiceTime, lyVoice);


        Disposable disposable =UtilRxView.clicks(btLogin, 1000, new IResultSuccess<Object>() {
            @Override
            public void onSuccess(Object o) throws Exception {
                UtilSoftKeyboard.hide(btLogin);
                    String code = etPwd.getText ().toString ();
                    if (code.length () < 6) {
                        showDialogToast ("请输入6位验证码");
                        return;
                    }

                    //验证验证码
                    ReqCheckCode reqCheckCode = new ReqCheckCode();
                    reqCheckCode.setVerifyCodeType (verifyCodeType);
                    reqCheckCode.setVerifyCode (code);
                    reqCheckCode.setMobile (phone);
                    reqCheckCode.setModuleType (ReqCheckCode.MODULE_TYPE_B);
                    getViewModel ().checkVerifyCode (reqCheckCode);
            }
        });

        this.putDisposable(disposable);
    }

    @LiveDataMatch(tag = "发送验证码成功")
    public void onSendCodeSuccess(boolean success, String type) {

        this.util.onSendCodeResult (success, type);

    }

    @LiveDataMatch(tag = "显示图片验证码")
    public void onShowImageVerifyCode(ReqSendCode req) {

        new ImageCodeDialog(this, phone, (ImageCodeDialog dialog, String code)-> {

                req.setImageCode (code);
                getViewModel ().sendVerifyCode (req);
        }).show ();

    }


    @Override
    public void beforeTextChanged(CharSequence s, int start, int count, int after) {

    }

    @Override
    public void onTextChanged(CharSequence s, int start, int before, int count) {

    }

    @Override
    public void afterTextChanged(Editable s) {

        btLogin.setEnabled (!TextUtils.isEmpty (s.toString ()));
    }

    @LiveDataMatch(tag = "验证码验证成功")
    public void onCheckSuccess() {

        String code = etPwd.getText ().toString ();
        UserEditSafePasswordTwoctivity.start (this,phone, code, verifyCodeType,ReqCheckCode.MODULE_TYPE_D);
        finish ();
    }

}
