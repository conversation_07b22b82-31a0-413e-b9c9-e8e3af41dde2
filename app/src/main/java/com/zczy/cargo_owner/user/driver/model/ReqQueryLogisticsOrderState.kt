package com.zczy.cargo_owner.user.driver.model

import com.zczy.comm.http.entity.BaseNewRequest
import com.zczy.comm.http.entity.BaseRsp
import com.zczy.comm.http.entity.PageList
import com.zczy.comm.http.entity.ResultData

/**
 *@Desc 2.查询物流企业摘单状态
 *@User ssp
 *@Date 2023/8/31-19:14
 */
class ReqQueryLogisticsOrderState : BaseNewRequest<BaseRsp<RspQueryLogisticsOrderState>>("mms-app/consignorCarrierRelation/queryLogisticsOrderState")

class RspQueryLogisticsOrderState(
    val state: String = "" //0-恢复 1-限制
) : ResultData()