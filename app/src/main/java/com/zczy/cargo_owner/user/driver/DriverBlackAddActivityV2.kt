package com.zczy.cargo_owner.user.driver

import android.annotation.SuppressLint
import android.app.Activity
import android.content.Intent
import android.os.Bundle
import android.text.Editable
import android.text.InputFilter
import android.text.TextUtils
import android.text.TextWatcher
import android.view.View
import com.sfh.lib.event.RxBusEventManager
import com.sfh.lib.mvvm.annotation.LiveDataMatch
import com.sfh.lib.ui.dialog.DialogBuilder
import com.zczy.cargo_owner.R
import com.zczy.cargo_owner.user.driver.model.DriverBlacklistModel
import com.zczy.cargo_owner.user.driver.model.ReqAddRelationVehicleBlack
import com.zczy.cargo_owner.user.driver.model.RxAddRelationVehicleBlack
import com.zczy.comm.CommServer
import com.zczy.comm.ui.BaseActivity
import kotlinx.android.synthetic.main.driver_black_list_add_activity_v2.*

/**
 * @Desc 黑名单添加车辆
 * @User ssp
 * @Date 2023/7/19-10:42
 */
class DriverBlackAddActivityV2 : BaseActivity<DriverBlacklistModel>() {

    companion object {
        @JvmStatic
        fun jumpPage(activity: Activity?) {
            val starter = Intent(activity, DriverBlackAddActivityV2::class.java)
            activity?.startActivityForResult(starter, 1000)
        }
    }

    override fun getLayout(): Int {
        return R.layout.driver_black_list_add_activity_v2
    }

    override fun bindView(bundle: Bundle?) {
        bindClickEvent(tv_add)
        noteTv.filters = arrayOf<InputFilter>(InputFilter.LengthFilter(200))
        noteTv.addTextChangedListener(object : TextWatcher {
            @SuppressLint("SetTextI18n")
            override fun afterTextChanged(s: Editable) {
                if (s.length > 200) {
                    return
                }
                sizeTv.text = "${s.length}/200"
            }

            override fun beforeTextChanged(s: CharSequence?, start: Int, count: Int, after: Int) {
            }

            override fun onTextChanged(s: CharSequence?, start: Int, before: Int, count: Int) {
            }
        })
    }

    override fun onSingleClick(v: View) {
        super.onSingleClick(v)
        when (v.id) {
            R.id.tv_add -> {
                val name = et_name.content
                if (TextUtils.isEmpty(name)) {
                    showToast("请输入车牌号！")
                    return
                }
                val builder = DialogBuilder()
                builder.title = "温馨提示"
                builder.message = "确定添加吗?"
                builder.setOkListener { dialogInterface, _ ->
                    dialogInterface.dismiss()
                    val req = ReqAddRelationVehicleBlack()
                    req.plateNumber = name
                    CommServer.getUserServer().login?.let {
                        req.consignorUserId = it.userId
                        req.consignorUserName = it.userName
                        req.consignorMemberName = it.memberName
                        req.consignorMobile = it.mobile
                    }
                    req.remark = noteTv.text.toString().trim()
                    getViewModel(DriverBlacklistModel::class.java).add(req = req)
                }
                showDialog(builder)
            }
        }
    }

    override fun initData() {

    }

    @LiveDataMatch
    open fun onRefresh() {
        RxBusEventManager.postEvent(RxAddRelationVehicleBlack(success = true))
        finish()
    }
}
