package com.zczy.cargo_owner.user

import android.text.TextUtils
import android.widget.TextView
import com.chad.library.adapter.base.BaseQuickAdapter
import com.chad.library.adapter.base.BaseViewHolder
import com.zczy.cargo_owner.R
import com.zczy.cargo_owner.home.mode.EMenuSize
import com.zczy.cargo_owner.home.mode.QuestionnaireCount
import com.zczy.cargo_owner.home.mode.request.RspMemberConfigData
import com.zczy.cargo_owner.home.mode.request.RspQueryOrderOverDueCount
import com.zczy.cargo_owner.user.ItemMenuAdapter.ItemData
import com.zczy.comm.utils.dp2px
import com.zczy.comm.utils.ex.isNotNull
import com.zczy.comm.utils.ex.isNull
import com.zczy.comm.utils.ex.isTrue

/**
 * 类描述：个人中心
 * 作者：ssp
 * 创建时间：2024/3/29
 */
class ItemMenuAdapter : BaseQuickAdapter<ItemData, BaseViewHolder>(R.layout.user_info_menu_adapter) {

    init {
        val itemData = mutableListOf<ItemData>()
        itemData.add(ItemData("发货单管理", R.drawable.fahuodan))
        itemData.add(ItemData("我的草稿箱", R.drawable.user_menu_2))
        itemData.add(ItemData("发货人管理", R.drawable.user_menu_4))
        itemData.add(ItemData("收货人管理", R.drawable.user_menu_5))
        itemData.add(ItemData("收件人管理", R.drawable.user_menu_6))
        itemData.add(ItemData("联系人管理", R.drawable.icon_concat))
        itemData.add(ItemData("黑名单管理", R.drawable.user_menu_37))
        itemData.add(ItemData("变更管理", R.drawable.user_menu_12))
        itemData.add(ItemData("运单异常管理", R.drawable.user_menu_7))
        itemData.add(ItemData("逾期运单处理", R.drawable.user_menu_14))
        itemData.add(ItemData("货物名称管理", R.drawable.user_menu_16))
        itemData.add(ItemData("货物规格管理", R.drawable.user_menu_19))
        itemData.add(ItemData("保障服务", R.drawable.user_menu_8))
        itemData.add(ItemData("押金管理", R.drawable.user_menu_17))
        itemData.add(ItemData("评价管理", R.drawable.user_menu_9))
        itemData.add(ItemData("证件过期管理", R.drawable.user_menu_item_expired))
        itemData.add(ItemData("监管账户资料补充/更新", R.drawable.icon_supervision_1))
        itemData.add(ItemData("问题反馈", R.drawable.user_menu_10))
        itemData.add(ItemData("场内物流", R.drawable.user_menu_12))
        itemData.add(ItemData("问卷调查", R.drawable.user_menu_13))
        itemData.add(ItemData("客服满意度评价", R.drawable.user_menu_11))
        itemData.add(ItemData("积分管理", R.drawable.user_menu_15))
        itemData.add(ItemData("优惠券管理", R.drawable.user_menu_38))
        itemData.add(ItemData("我的培训", R.drawable.user_menu_18))
        itemData.add(ItemData("运输提醒", R.drawable.user_menu_25))
        itemData.add(ItemData("集装箱管理", R.drawable.user_menu_26))
        itemData.add(ItemData("统计报表", R.drawable.user_report_icon_30))
        itemData.add(ItemData("免诚意金管理", R.drawable.user_earnest_icon))
        itemData.add(ItemData("违约申请免确认管理", R.drawable.user_earnest_icon))
        itemData.add(ItemData("原有运力管理", R.drawable.user_earnest_icon))
        itemData.add(ItemData("预约回访", R.drawable.icon_mine_callback))
        itemData.add(ItemData("合同管理", R.drawable.icon_mine_sign_concact))
        itemData.add(ItemData("发票中心", R.drawable.icon_mine_fapiao))
        setNewData(itemData)
    }


    class ItemData(@JvmField var name: String, var res: Int) {
        var size: String? = null
    }

    fun showEMenuSize(size: EMenuSize?) {
        if (size == null) {
            return
        }
        //运单异常管理
        for (item in data) {
            if ("运单异常管理" == item.name) {
                item.size = size.showCornerLabels()
            }
        }
        notifyDataSetChanged()
    }

    fun showEMenuSize(data: RspQueryOrderOverDueCount?) {
        if (data == null) {
            return
        }
        //逾期运单处理
        for (item in getData()) {
            if ("逾期运单处理" == item.name) {
                val orderOverDueCount = data.orderOverDueCount
                if (!TextUtils.isEmpty(orderOverDueCount)) {
                    if (orderOverDueCount.length > 2) {
                        item.size = "99+"
                    } else {
                        item.size = orderOverDueCount
                    }
                }
            }
        }
        notifyDataSetChanged()
    }

    fun showQuestionnaireCount(size: QuestionnaireCount?) {
        if (size == null) {
            return
        }
        // 问卷调查
        for (item in data) {
            if ("问卷调查" == item.name) {
                item.size = size.unDealCount
            }
        }
    }

    fun showQueryMemberConfigSuccess(rsp: RspMemberConfigData?) {
        try {
            val newList = data.map { it }.toMutableList()
            showHideItem(newList, rsp?.offLineSpecialAreaSwitch, "联系人管理", "线下专区", R.drawable.icon_offer_line)
            showHideItem(newList, rsp?.onlineCargoClaimSwitch, "问卷调查", "理赔申请管理", R.drawable.user_menu_28)
            showHideItem(newList, rsp?.tenderBiddingSwitch, "收货人管理", "议价规则设置", R.drawable.icon_bid_rule)
            showHideItem(newList, rsp?.settlementApplyAuditSwitch, "免诚意金管理", "结算申请审核", R.drawable.user_earnest_icon)
            setNewData(newList)
        } catch (e: Exception) {
            e.printStackTrace()
        }

    }

    private fun showHideItem(newList: MutableList<ItemData>, switch: String?, top: String, name: String, icon: Int) {

        val find = newList.find { TextUtils.equals(it.name, name) }
        if (TextUtils.equals("1", switch) && find == null) {
            var addIndex = -1
            newList.forEachIndexed { index, item ->
                if (TextUtils.equals(item.name, top)) {
                    addIndex = index + 1
                    return@forEachIndexed
                }
            }
            if (addIndex > -1) {
                newList.add(addIndex, ItemData(name, icon))
            } else {
                newList.add(ItemData(name, icon))
            }

        } else if (!TextUtils.equals("1", switch) && find != null) {
            newList.remove(find)
        }
    }


    /**
     * 注释：设置用户管理
     * 时间：2024/6/21 0021 10:01
     * 作者：郭翰林
     */
    fun setUserManageMenu(show: Boolean) {
        val find = data.find { f -> f.name == "用户管理" }
        if (show) {
            if (find.isNotNull) {
                return
            }
            data.add(0, ItemData("用户管理", R.drawable.ic_user_manger))
            notifyDataSetChanged()
        } else {
            if (find.isNotNull) {
                val list = data.filter { f -> f.name == "用户管理" }
                setNewData(list)
            }
        }
    }

    /**
     * 注释：运费通
     * 时间：2024/6/21 0021 10:01
     * 作者：宋双朋
     */
    fun setFreightPassMenu(show: Boolean) {
        val find = data.find { f -> f.name == "运费通" }
        if (show) {
            if (find.isNotNull) {
                return
            }
            data.add(0, ItemData("运费通", R.drawable.user_menu_39))
            notifyDataSetChanged()
        } else {
            if (find.isNotNull) {
                val list = data.filter { f -> f.name == "运费通" }
                setNewData(list)
            }
        }
    }


    override fun convert(helper: BaseViewHolder, item: ItemData) {
        helper.setText(R.id.tv_name, item.name)
        helper.setImageResource(R.id.iv_icon, item.res)
        val view = helper.getView<TextView>(R.id.tv_size)
        if (TextUtils.isEmpty(item.size)) {
            view.setPadding(dp2px(2f), dp2px(2f), dp2px(2f), dp2px(2f))
        } else if ((item.size?.length ?: 0) <= 1) {
            view.setPadding(dp2px(5f), dp2px(2f), dp2px(5f), dp2px(2f))
        } else {
            view.setPadding(dp2px(3f), dp2px(2f), dp2px(3f), dp2px(2f))
        }
        helper.setGone(R.id.tv_size, !(TextUtils.isEmpty(item.size) || TextUtils.equals("0", item.size)))
            .setText(R.id.tv_size, item.size)
        val position = helper.adapterPosition
        if (position == itemCount - 1) {
            helper.setGone(R.id.tv_line, false)
        } else {
            helper.setGone(R.id.tv_line, true)
        }
    }
}
