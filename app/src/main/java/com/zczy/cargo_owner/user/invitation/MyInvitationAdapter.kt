package com.zczy.cargo_owner.user.invitation

import android.widget.ImageView
import com.chad.library.adapter.base.BaseQuickAdapter
import com.chad.library.adapter.base.BaseViewHolder
import com.zczy.cargo_owner.R
import com.zczy.cargo_owner.user.invitation.bean.MyInvitationBean
import com.zczy.comm.utils.imgloader.ImgUtil

/** 功能描述:
 *
 * <AUTHOR> 贾发展
 * @company 中储智运科技股份有限公司
 * @copyright （版权）中储南京智慧物流科技有限公司所有
 * @date 2019/6/19
 */
class MyInvitationAdapter : BaseQuickAdapter<MyInvitationBean, BaseViewHolder>(R.layout.user_myinvitation_item) {
    override fun convert(helper: BaseViewHolder?, item: MyInvitationBean?) {

        helper?.setText(R.id.tv_invitation_nickname, item?.userNm)
        helper?.setText(R.id.tv_invitation_time, item?.createdTime)
        val ivInvitationUserIcon = helper?.getView<ImageView>(R.id.iv_invitation_user_icon)
        if (ivInvitationUserIcon != null) {
            ImgUtil.loadUrl(ivInvitationUserIcon, item?.headUrl)
        }

        /**
         * userType：

        1-“已认证货主”

        2-“已认证个体司机”

        3-“已认证物流企业”

        4-“已认证个体船舶会员”

        5-“已认证单位船舶会员”

        6-“待认证货主”

        7-“待认证个体司机”

        8-“待认证船舶会员”

        9-“已认证经纪人”

        10-“已认证车队老板”

        11-“待认证物流企业”

        12-“待认证核对老板”

        13-“已认证加盟商”

        14-“待认证加盟商”

        15-“个体编外”

        16-“企业编外”
         */
        when (item?.userType) {
            "1" -> {
                helper?.setText(R.id.tv_invitation_role, "已认证货主")
            }
            "2" -> {
                helper?.setText(R.id.tv_invitation_role, "已认证个体司机")
            }
            "3" -> {
                helper?.setText(R.id.tv_invitation_role, "已认证物流企业")
            }
            "4" -> {
                helper?.setText(R.id.tv_invitation_role, "已认证个体船舶会员")
            }
            "5" -> {
                helper?.setText(R.id.tv_invitation_role, "已认证单位船舶会员")
            }
            "6" -> {
                helper?.setText(R.id.tv_invitation_role, "待认证货主")
            }
            "7" -> {
                helper?.setText(R.id.tv_invitation_role, "待认证个体司机")
            }
            "8" -> {
                helper?.setText(R.id.tv_invitation_role, "待认证船舶会员")
            }
            "9" -> {
                helper?.setText(R.id.tv_invitation_role, "已认证经纪人")
            }
            "10" -> {
                helper?.setText(R.id.tv_invitation_role, "已认证车队老板")
            }
            "11" -> {
                helper?.setText(R.id.tv_invitation_role, "待认证物流企业")
            }
            "12" -> {
                helper?.setText(R.id.tv_invitation_role, "待认证核对老板")
            }
            "13" -> {
                helper?.setText(R.id.tv_invitation_role, "已认证加盟商")
            }
            "14" -> {
                helper?.setText(R.id.tv_invitation_role, "待认证加盟商")
            }
            "15" -> {
                helper?.setText(R.id.tv_invitation_role, "个体编外")
            }
            "16" -> {
                helper?.setText(R.id.tv_invitation_role, "企业编外")
            }


        }
    }

}