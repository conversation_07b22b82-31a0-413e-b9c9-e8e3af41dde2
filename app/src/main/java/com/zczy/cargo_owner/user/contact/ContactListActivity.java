package com.zczy.cargo_owner.user.contact;

import android.content.Context;
import android.content.Intent;
import android.graphics.Color;
import android.os.Bundle;
import android.view.View;
import android.widget.RelativeLayout;
import android.widget.Toast;

import com.chad.library.adapter.base.BaseQuickAdapter;
import com.sfh.lib.mvvm.annotation.LiveDataMatch;
import com.sfh.lib.ui.AbstractLifecycleActivity;
import com.zczy.cargo_owner.R;
import com.zczy.cargo_owner.user.contact.adapter.ContactAdapter;
import com.zczy.cargo_owner.user.contact.model.ContactModel;
import com.zczy.cargo_owner.user.contact.model.req.ReqQueryContact;
import com.zczy.cargo_owner.user.contact.model.resp.RespContactListData;
import com.zczy.comm.http.entity.PageList;
import com.zczy.comm.ui.UtilStatus;
import com.zczy.comm.utils.ResUtil;
import com.zczy.comm.widget.AppToolber;
import com.zczy.comm.widget.pulltorefresh.CommEmptyView;
import com.zczy.comm.widget.pulltorefresh.OnLoadingListener2;
import com.zczy.comm.widget.pulltorefresh.SwipeRefreshMoreLayout;

public class ContactListActivity extends AbstractLifecycleActivity<ContactModel> {

    private AppToolber mAppToolber;
    private SwipeRefreshMoreLayout list_contact;
    private long userId;
    private RelativeLayout rl_add_contact;

    public static void start(Context context, long userId) {
        Intent intent = new Intent(context, ContactListActivity.class);
        intent.putExtra("userId", userId);
        context.startActivity(intent);
    }

    @Override
    protected void onCreate(Bundle savedInstanceState) {
        super.onCreate(savedInstanceState);
        setContentView(R.layout.activity_contact_list);
        UtilStatus.initStatus(this, Color.WHITE);
        userId = getIntent().getLongExtra("userId", 0);
        initView();
    }

    @Override
    protected void onResume() {
        super.onResume();

        ReqQueryContact reqQueryContact = new ReqQueryContact();
        reqQueryContact.pageNum = 1;
        reqQueryContact.pageSize = 10;
        reqQueryContact.userId = userId;
        getViewModel(ContactModel.class).queryContactList(reqQueryContact);
    }

    private void initView() {
        mAppToolber = findViewById(R.id.appToolber);
        list_contact = findViewById(R.id.list_contact);
        rl_add_contact = findViewById(R.id.rl_add_contact);
        mAppToolber.setLeftOnClickListener(view -> finish());

        ContactAdapter contactAdapter = new ContactAdapter();

        contactAdapter.setOnItemChildClickListener(new BaseQuickAdapter.OnItemChildClickListener() {
            @Override
            public void onItemChildClick(BaseQuickAdapter adapter, View view, int position) {
                if (view.getId() == R.id.tv_edit) {
                    RespContactListData respContactListData = (RespContactListData) adapter.getItem(position);
                    if (null != respContactListData) {
                        EditContactActivity.start(ContactListActivity.this, respContactListData);
                    } else {
                        Toast.makeText(ContactListActivity.this, "数据加载失败", Toast.LENGTH_SHORT).show();
                    }
                }
            }
        });

        list_contact.setAdapter(contactAdapter, true);
        list_contact.addItemDecorationSize(ResUtil.dp2px(8));
        list_contact.setEmptyView(CommEmptyView.creator(this, R.drawable.evalutate_no_information, "没有找到相关信息"));
        list_contact.setOnLoadListener2(new OnLoadingListener2() {
            @Override
            public void onLoadUI(int nowPage) {
                ReqQueryContact reqQueryContact = new ReqQueryContact();
                reqQueryContact.pageNum = nowPage;
                reqQueryContact.pageSize = 10;
                reqQueryContact.userId = userId;
                getViewModel(ContactModel.class).queryContactList(reqQueryContact);
            }
        });

        rl_add_contact.setOnClickListener(new View.OnClickListener() {
            @Override
            public void onClick(View v) {
                AddContactActivity.start(ContactListActivity.this, userId);
            }
        });
    }

    @LiveDataMatch
    public void onQueryContactListSuccess(PageList<RespContactListData> data) {
        list_contact.onRefreshCompale(data);
    }
}