package com.zczy.cargo_owner.user.setting.dialog

import android.annotation.SuppressLint
import android.os.Bundle
import android.view.View
import com.zczy.cargo_owner.R
import com.zczy.comm.ui.BaseDialog
import kotlinx.android.synthetic.main.user_clear_account_dialog.tv1_1
import kotlinx.android.synthetic.main.user_clear_account_dialog.tv1_2
import kotlinx.android.synthetic.main.user_clear_account_dialog.tv1_3

/**
 *  desc: 账号管理清除账号提示
 *  user: ssp
 *  time: 2024/7/25 10:40
 */
class UserClearAccountDialog(var userNm: String?, var block: (clear: Boolean) -> Unit) : BaseDialog() {
    @SuppressLint("SetTextI18n")
    override fun bindView(view: View, bundle: Bundle?) {
        tv1_1.text = "清除账号${userNm}的登录记录"
        tv1_2.setOnClickListener {
            dismiss()
            block(true)
        }
        tv1_3.setOnClickListener {
            dismiss()
            block(false)
        }
    }

    override fun getDialogTag(): String {
        return "'UserClearAccountDialog"
    }

    override fun getDialogType(): DialogType {
        return DialogType.bottom
    }

    override fun getDialogLayout(): Int {
        return R.layout.user_clear_account_dialog
    }
}