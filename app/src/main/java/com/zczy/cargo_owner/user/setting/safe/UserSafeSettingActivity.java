package com.zczy.cargo_owner.user.setting.safe;

import android.content.Context;
import android.content.Intent;
import android.graphics.Color;
import android.os.Bundle;
import androidx.annotation.Nullable;
import android.text.TextUtils;
import android.view.Gravity;
import android.view.View;
import android.widget.ImageView;
import android.widget.RelativeLayout;
import android.widget.TextView;

import com.sfh.lib.AppCacheManager;
import com.sfh.lib.event.RxBusEvent;
import com.sfh.lib.mvvm.annotation.LiveDataMatch;
import com.sfh.lib.ui.AbstractLifecycleActivity;
import com.sfh.lib.ui.dialog.DialogBuilder;
import com.zczy.cargo_owner.R;
import com.zczy.cargo_owner.home.dialog.X5VideoWebNoToolBarActivity;
import com.zczy.cargo_owner.libcomm.config.HttpConfig;
import com.zczy.cargo_owner.user.login.activity.UserBindAccountActivity;
import com.zczy.cargo_owner.user.login.entity.WxAuthSession;
import com.zczy.cargo_owner.user.setting.UserEditPasswordctivity;
import com.zczy.cargo_owner.user.setting.model.ReqGetVideoPath;
import com.zczy.cargo_owner.user.setting.model.RespGetVideoPath;
import com.zczy.cargo_owner.user.setting.model.UserSetModel;
import com.zczy.cargo_owner.wxapi.RxWXLoginResultData;
import com.zczy.comm.ZczyApplication;
import com.zczy.comm.data.entity.EUser;
import com.zczy.comm.data.role.IRelation;
import com.zczy.comm.ui.UtilStatus;

/**
 * 功能描述: 账号安全
 *
 * <AUTHOR> 孙飞虎
 * @company 中储智运科技股份有限公司
 * @copyright （版权）中储南京智慧物流科技有限公司所有
 * @date 2018/11/6
 */
public class UserSafeSettingActivity extends AbstractLifecycleActivity<UserSetModel> implements View.OnClickListener {

    private TextView tvBindState;
    private TextView tvLogoutAccount;
    private String isBangWeiXi;
    private String mobile;
    private ImageView iv_video_guide;
    private ImageView iv_guide_delete;
    private RelativeLayout rl_guide;

    public static void start(Context context) {
        Intent intent = new Intent(context, UserSafeSettingActivity.class);
        context.startActivity(intent);
    }


    @Override
    protected void onCreate(@Nullable Bundle savedInstanceState) {
        super.onCreate(savedInstanceState);
        setContentView(R.layout.user_set_safe_activity);
        UtilStatus.initStatus(this, Color.WHITE);

        findViewById(R.id.tvPassWord).setOnClickListener(this);
        findViewById(R.id.tvPhonePassWord).setOnClickListener(this);
        tvBindState = findViewById(R.id.tv_bind_state);
        tvLogoutAccount = findViewById(R.id.tvLogoutAccount);
        tvLogoutAccount.setOnClickListener(this);
        tvBindState.setOnClickListener(this);
        iv_video_guide = findViewById(R.id.iv_video_guide);
        iv_guide_delete = findViewById(R.id.iv_guide_delete);
        rl_guide = findViewById(R.id.rl_guide);

        getViewModel().queryUserInfo();

        ReqGetVideoPath reqGetVideoPath = new ReqGetVideoPath();
        reqGetVideoPath.showPosition = 3;
        reqGetVideoPath.targetScope = 1;
        getViewModel().getVideoPath(reqGetVideoPath);
    }

    @Override
    public void onClick(View v) {

        if (v.getId() == R.id.tvPassWord) {
            //修改密码
            if (TextUtils.isEmpty(mobile)) {
                DialogBuilder builder = new DialogBuilder();
                builder.setTitle("温馨提示");
                builder.setHideCancel(true);
                builder.setOKText("我知道了");
                builder.setMessage("修改密码需绑定手机号验证，若无法绑定请联系主用户进行密码重置操作！");
                showDialog(builder);
                return;
            }
            UserEditPasswordctivity.startUI(this);
        } else if (v.getId() == R.id.tvPhonePassWord) {
            //修改电话服务密码
            UserEditSafePasswordctivity.startUI(this);
        } else if (v.getId() == R.id.tv_bind_state) {
            // 切換绑定状态
            if (TextUtils.equals(isBangWeiXi, "0")) {
                getViewModel().weChatLogin(UserSafeSettingActivity.this);
            } else {
                DialogBuilder builder = new DialogBuilder();
                builder.setTitle("解除关联");
                builder.setCancelText("取消");
                builder.setOKText("解除关联");
                builder.setMessage("确定要解除账号与微信的关联吗？\n" +
                        "解除关联后将无法使用微信登录此账号");
                builder.setOkListener((dialog, which) -> {
                    getViewModel().getWxLogoutStatus();
                    dialog.dismiss();
                });
                showDialog(builder);
            }
        } else if (v.getId() == R.id.tvLogoutAccount) {
            //注销账号
            DialogBuilder builder = new DialogBuilder();
            builder.setTitle("注销账号");
            builder.setCancelText("取消");
            builder.setOKTextColor("坚持注销账号", R.color.color_da3400);
            builder.setMessageGravity("是否确定注销账号，\n" +
                    "注销之后该手机将无法登陆！", Gravity.CENTER);
            builder.setOkListener((dialog, which) -> {
                getViewModel().onLogoutAccount();
                dialog.dismiss();
            });
            showDialog(builder);
        }
    }

    @LiveDataMatch
    public void ongetVideoPathSuccess(RespGetVideoPath respGetVideoPath) {
        if (null != respGetVideoPath && null != respGetVideoPath.data && !TextUtils.isEmpty(respGetVideoPath.data.content)) {
            iv_video_guide.setVisibility(View.VISIBLE);
            rl_guide.setVisibility(View.VISIBLE);

            String changepwd_show_popu = AppCacheManager.getCache("changepwd_show_popu", String.class, "1");
            if (changepwd_show_popu.equals("1")) {
                rl_guide.setVisibility(View.VISIBLE);
            } else {
                rl_guide.setVisibility(View.GONE);
            }

            iv_video_guide.setOnClickListener(new View.OnClickListener() {
                @Override
                public void onClick(View v) {
                    // 播放视频
//                    Intent intent = new Intent(UserSafeSettingActivity.this, VideoPlayActivity.class);
////                    intent.putExtra("videoUri", "https://img.zczy56.com/shou_huo.mp4");
//                    intent.putExtra("videoUri", respGetVideoPath.data.content);
//                    startActivity(intent);

                    X5VideoWebNoToolBarActivity.startContentUI(UserSafeSettingActivity.this, HttpConfig.getWebUrl("form_h5/h5_inner/index.html?_t=" + System.currentTimeMillis() + "#videoDetail?type=1&id=3&isNative=1"));
                }
            });

            iv_guide_delete.setOnClickListener(new View.OnClickListener() {
                @Override
                public void onClick(View v) {
                    rl_guide.setVisibility(View.GONE);
                    AppCacheManager.putCache("changepwd_show_popu", "0");
                }
            });

            rl_guide.setOnClickListener(new View.OnClickListener() {
                @Override
                public void onClick(View v) {
                    rl_guide.setVisibility(View.GONE);
                    AppCacheManager.putCache("changepwd_show_popu", "0");
                }
            });
        } else {
            iv_video_guide.setVisibility(View.GONE);
            rl_guide.setVisibility(View.GONE);
        }
    }

    @LiveDataMatch
    public void onWxLogoutStatus() {
        tvBindState.setText("未关联");
        getViewModel().queryUserInfo();
    }

    @LiveDataMatch
    public void onLogoutAccountSuccess() {
        //注销成功 退出登录
        finish();
        ZczyApplication application = AppCacheManager.getApplication();
        application.onLoseToken("", "");
    }

    @LiveDataMatch
    public void onQueryUserInfo(EUser user) {
        //0未关联，大于0关联
        isBangWeiXi = user.getIsBangWeiXi();
        mobile = user.getMobile();
        if (TextUtils.equals(isBangWeiXi, "0")) {
            tvBindState.setText("未关联");
        } else {
            tvBindState.setText("已关联");
        }
        IRelation relation = user.getRelation();

        if (relation.isPrimaryShipper()) {
            //初级货主
            tvLogoutAccount.setVisibility(View.VISIBLE);
        } else {
            tvLogoutAccount.setVisibility(View.GONE);
        }
    }

    @RxBusEvent(from = "微信登录成功")
    public void weiXinAuthorSuccess(RxWXLoginResultData resultData) {
        if (resultData.query) {
            getViewModel().getWxOauthSession(resultData.code, mobile);
        } else {
            showToast("授权失败");
        }
    }

    @LiveDataMatch
    public void onReqWxAuthSession(WxAuthSession session) {
        UserBindAccountActivity.start(this, session.getOpenId(), mobile);
    }
}

