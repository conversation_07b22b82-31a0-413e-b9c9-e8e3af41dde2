package com.zczy.cargo_owner.user.login.entity

import com.zczy.comm.data.entity.ELogin
import com.zczy.comm.http.entity.ResultData

/** 功能描述:
 *
 * <AUTHOR> 贾发展
 * @company 中储智运科技股份有限公司
 * @copyright （版权）中储南京智慧物流科技有限公司所有
 * @date 2020/6/10
 */
data class WxAuthSession(
        var openId: String? = "",//OPENID 用户唯一标识
        var unionId: String? = "",//用户在开放平台的唯一标识符
        var successLogin: String? = "",//“1”;//代表登录成功
        var loginInfo: ELogin? = null
) : ResultData()