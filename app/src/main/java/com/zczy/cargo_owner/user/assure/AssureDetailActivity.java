package com.zczy.cargo_owner.user.assure;

import android.app.Activity;
import android.content.Intent;
import android.graphics.Color;
import android.os.Bundle;
import androidx.annotation.Nullable;
import android.text.TextUtils;
import android.view.View;
import android.widget.LinearLayout;
import android.widget.TextView;

import com.sfh.lib.mvvm.annotation.LiveDataMatch;
import com.sfh.lib.ui.AbstractLifecycleActivity;
import com.zczy.cargo_owner.R;
import com.zczy.comm.ui.UtilStatus;

public class AssureDetailActivity extends AbstractLifecycleActivity<AssureModel> {
    public static void start(Activity context,String id,String current) {
        Intent intent = new Intent(context, AssureDetailActivity.class);
        intent.putExtra("id",id);
        intent.putExtra("current",current);
        context.startActivity(intent);
    }
    private TextView tvServerId;
    private TextView tvPalyTime;
    private TextView tvOrderId;
    private TextView tvCarNumber;
    private TextView tvMoneyFl;
    private TextView tvMoney;
    private TextView tvType;
    private TextView tvStatus;
    private TextView tvCarMoney;
    private LinearLayout ll_money_cyf;
    private LinearLayout ll_fwdh;

    @Override
    protected void onCreate(@Nullable Bundle savedInstanceState) {
        super.onCreate(savedInstanceState);
        setContentView(R.layout.user_assure_detail);
        UtilStatus.initStatus(this, Color.WHITE);

        tvServerId = findViewById(R.id.tv_serverId);
        tvPalyTime = findViewById(R.id.tv_palyTime);
        tvOrderId =  findViewById(R.id.tv_orderId);
        tvCarNumber = findViewById(R.id.tv_car_number);
        tvMoneyFl =  findViewById(R.id.tv_money_fl);
        tvMoney = findViewById(R.id.tv_money);
        tvType =  findViewById(R.id.tv_type);
        tvStatus =  findViewById(R.id.tv_status);
        tvCarMoney = findViewById(R.id.tv_car_money);
        ll_money_cyf = findViewById(R.id.ll_money_cyf);
        ll_fwdh = findViewById(R.id.ll_fwdh);
        String id = getIntent().getStringExtra("id");
        String current = getIntent().getStringExtra("current");
        if (TextUtils.equals("1",current)){
            ll_money_cyf.setVisibility(View.GONE);
        }else {
            ll_money_cyf.setVisibility(View.VISIBLE);
        }
        getViewModel().quesyDeatil(id);
    }

    @LiveDataMatch
    public void onDeatil(AssureDetail detail) {
        if (TextUtils.isEmpty(detail.getId())){
            ll_fwdh.setVisibility(View.GONE);
        }
        if (TextUtils.equals("1", detail.getGuaranteeState()) ){
            tvPalyTime.setText(" - -");
        }else {
            tvPalyTime.setText(detail.getPalyTime());
        }
        tvServerId.setText(detail.getId());
        tvOrderId.setText(detail.getOrderId());
        tvCarNumber.setText(detail.getCarNumber());
        tvMoneyFl.setText(detail.getShowRateStr());
        tvMoney.setText(detail.getMoney());
        tvType.setText(detail.getType());
        tvStatus.setText(detail.getStatus());
        tvCarMoney.setText(detail.getCarMoney());
     }
}
