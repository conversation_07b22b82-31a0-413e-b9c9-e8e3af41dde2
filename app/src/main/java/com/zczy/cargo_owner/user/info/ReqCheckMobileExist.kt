package com.zczy.cargo_owner.user.info

import com.zczy.comm.http.entity.BaseNewRequest
import com.zczy.comm.http.entity.BaseRsp
import com.zczy.comm.http.entity.ResultData

/**
 *@Desc 校验手机号是否存在
 *@User ssp
 *@Date 2023/6/5-13:38
 */
class ReqCheckMobileExist(
    var checkType: String = "", // 1 绑定手机号，2 解绑手机号
    var mobile: String = "", // 手机号
) : BaseNewRequest<BaseRsp<ResultData>>("mms-app/mms/upgrade/checkMobileExist") {
    companion object {
        const val checkType1 = "1"
        const val checkType2 = "2"
    }
}