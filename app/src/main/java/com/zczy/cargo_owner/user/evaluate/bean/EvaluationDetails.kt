package com.zczy.user.evaluate.bean

import com.zczy.comm.http.entity.ResultData

/** 功能描述:
 *
 * <AUTHOR> 贾发展
 * @company 中储智运科技股份有限公司
 * @copyright （版权）中储南京智慧物流科技有限公司所有
 * @date 2019/3/28
 */
data class EvaluationDetails(
        /**
         * 评价类型：1 承运方评价货主, 2 货主评价承运商, 3 货主评价承运人
         */
        private var type: String = "",

        /**
         * 承运方评价货主-发货人准时装货
         */
        var consignorOntimeDespatch: String = "",

        /**
         * 承运方评价货主-收货人准时卸货
         */
        var consignorOntimeDeliver: String = "",

        /**
         * 承运方评价货主-信息准确性
         */
        var consignorMessageAccuracy: String = "",

        /**
         * 货主评价承运方-承运方按时到达装货
         */
        var carrierOntimeDespatch: String = "",

        /**
         * 货主评价承运方-承运方按时到达卸货
         */
        var carrierOntimeDeliver: String = "",

        /**
         * 货主评价承运方-承运方货物安全送达(是否有货损货差)
         */
        var carrierCargoSafety: String = "",

        /**
         * 货主评价承运方-承运方服务态度
         */
        var carrierServiceAttitude: String = "",

        /**
         * 评价内容
         */
        var remark: String = "",

        /**
         * 创建时间
         */
        var createdTime: String = ""
) : ResultData()