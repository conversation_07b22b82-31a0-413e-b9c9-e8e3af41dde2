package com.zczy.cargo_owner.user.login.request;

import com.zczy.cargo_owner.user.login.mode.RegisterMode;
import com.zczy.comm.http.entity.BaseNewRequest;
import com.zczy.comm.http.entity.BaseRsp;

public class ReqRegister extends BaseNewRequest<BaseRsp<RegisterMode.RegisterResponse>> {

    public ReqRegister() {
        super("mms-app/platform/registers/registerMemberApp");
    }

    //用户名生成方式：1-前台自定义用户名 2-随机生成用户名
    String userNmType = "2";

    //客户端类型
    String clientType = "ANDROID";

    //注册系统：1、汽运  2、船运
    String registerSystem = "1";

    //手机号   当userNm为空时该参数必传
    String serialNumber;

    //登录密码
    String userPassword;

    //密码等级
    String passwordGrade;

    //用户类型
    String userType="6";

    //经度
    String longitude;

    //纬度
    String latitude;

    //注册渠道
    String registerChannel="3";

    //注册省份编码
    String provinceCode;

    //注册省份名称
    String registerProvince;

    //注册市区编码
    String cityCode;

    //注册市区名称
    String registerCity;

    //注册区县编码
    String areaCode;

    //注册区县名称
    String registerArea;

    //推荐人用户Id
    String salesmanUserId;
    //邀请码
    String inviteCode;
    //是否存在邀请人
    String ifHaveRole;

    //是否是加盟运力邀请
    String invitedByExtra;

    //验证码
    String verifyCode;

    //验证码类型 1:短信验证码 2:语音验证码
    String verifyCodeType;
    public String companyName;
    public String provinceName;

    public void setVerifyCode(String verifyCode) {
        this.verifyCode = verifyCode;
    }

    public void setVerifyCodeType(String verifyCodeType) {
        this.verifyCodeType = verifyCodeType;
    }

    public void setSerialNumber(String serialNumber) {
        this.serialNumber = serialNumber;
    }

    public void setInviteCode(String inviteCode) {
        this.inviteCode = inviteCode;
    }

    public void setIfHaveRole(String ifHaveRole) {
        this.ifHaveRole = ifHaveRole;
    }

    public void setUserPassword(String userPassword) {
        this.userPassword = userPassword;
    }

    public void setPasswordGrade(String passwordGrade) {
        this.passwordGrade = passwordGrade;
    }

    public void setUserType(String userType) {
        this.userType = userType;
    }

    public void setRegisterChannel(String registerChannel) {
        this.registerChannel = registerChannel;
    }

    public void setProvinceCode(String provinceCode) {
        this.provinceCode = provinceCode;
    }

    public void setRegisterProvince(String registerProvince) {
        this.registerProvince = registerProvince;
    }

    public void setCityCode(String cityCode) {
        this.cityCode = cityCode;
    }

    public void setRegisterCity(String registerCity) {
        this.registerCity = registerCity;
    }

    public void setAreaCode(String areaCode) {
        this.areaCode = areaCode;
    }

    public void setRegisterArea(String registerArea) {
        this.registerArea = registerArea;
    }


    public void setSalesmanUserId(String salesmanUserId) {
        this.salesmanUserId = salesmanUserId;
    }

    public void setLongitude(String longitude) {
        this.longitude = longitude;
    }

    public void setLatitude(String latitude) {
        this.latitude = latitude;
    }

    public void setInvitedByExtra(String invitedByExtra) {
        this.invitedByExtra = invitedByExtra;
    }

    public String getSerialNumber() {
        return serialNumber;
    }
}
