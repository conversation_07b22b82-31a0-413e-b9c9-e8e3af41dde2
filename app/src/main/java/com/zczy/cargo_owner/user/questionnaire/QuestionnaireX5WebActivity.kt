package com.zczy.cargo_owner.user.questionnaire

import android.content.Context
import android.content.Intent
import android.os.Bundle
import com.sfh.lib.mvvm.service.BaseViewModel
import com.zczy.cargo_owner.R
import com.zczy.comm.X5BaseJavascriptInterface
import com.zczy.comm.ui.BaseActivity
import com.zczy.comm.widget.AppToolber
import com.zczy.comm.x5.X5WebView

/**
 * PS:
 * Created by sdx on 2019/3/5.
 */
class QuestionnaireX5WebActivity : BaseActivity<BaseViewModel>() {

    private val mAgentWeb by lazy { findViewById<X5WebView>(R.id.webLayout) }
    private val appToolber by lazy { findViewById<AppToolber>(R.id.appToolber) }
    private val eUrl by lazy { intent.getStringExtra(EXTRA_URL) }

    companion object {
        private const val EXTRA_URL = "extra_url"

        @JvmStatic
        fun start(context: Context?, url: String) {
            val intent = Intent(context, QuestionnaireX5WebActivity::class.java)
            intent.putExtra(EXTRA_URL, url)
            context?.startActivity(intent)
        }
    }


    override fun getLayout(): Int = R.layout.questionnaire_web_activity

    override fun bindView(bundle: Bundle?) {
        initHardwareAccelerate()
        initWebView()
    }

    /**
     * 启用硬件加速
     */
    private fun initHardwareAccelerate() {
        try {
            if (android.os.Build.VERSION.SDK_INT >= 11) {
                window.setFlags(
                    android.view.WindowManager.LayoutParams.FLAG_HARDWARE_ACCELERATED,
                    android.view.WindowManager.LayoutParams.FLAG_HARDWARE_ACCELERATED
                )
            }
        } catch (e: Exception) {
            e.printStackTrace()
        }

    }

    private fun initWebView() {
        val webViewSettings = mAgentWeb.settings
        val baseAgent = webViewSettings.userAgentString
        webViewSettings.setUserAgent("$baseAgent;app/ANDROID")
        mAgentWeb.addJavascriptInterface(jsUserInfoInterface, "android")
    }

    override fun initData() {
        mAgentWeb.loadUrl(eUrl)
    }

    override fun onDestroy() {
        //释放资源
        jsUserInfoInterface.destroy()
        if (mAgentWeb != null) {
            mAgentWeb.destroy()
        }
        super.onDestroy()
    }

    private val jsUserInfoInterface = object : X5BaseJavascriptInterface(this) {

    }
}