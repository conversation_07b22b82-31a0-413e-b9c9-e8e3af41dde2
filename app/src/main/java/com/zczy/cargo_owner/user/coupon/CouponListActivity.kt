package com.zczy.cargo_owner.user.coupon

import android.content.Context
import android.content.Intent
import android.os.Bundle
import androidx.fragment.app.Fragment
import android.text.TextUtils
import com.flyco.tablayout.listener.CustomTabEntity
import com.sfh.lib.mvvm.annotation.LiveDataMatch
import com.sfh.lib.ui.AbstractLifecycleActivity
import com.zczy.cargo_owner.R
import com.zczy.cargo_owner.user.coupon.req.ReqRedemptionCodeCouponNew
import com.zczy.cargo_owner.user.coupon.req.RspUserCouponNum
import com.zczy.comm.widget.tablayout.CommonTabEntity
import kotlinx.android.synthetic.main.user_coupon_list_activity.*

/***
 * 优惠券列表
 */
class CouponListActivity : AbstractLifecycleActivity<CouponModel>() {

    companion object {
        @JvmStatic
        fun jumpPage(activity: Context?) {
            val starter = Intent(activity, CouponListActivity::class.java)
            activity?.startActivity(starter)
        }
    }

    private val fragments: ArrayList<androidx.fragment.app.Fragment> = ArrayList<androidx.fragment.app.Fragment>()

    private val tabEntitys = ArrayList<CustomTabEntity>()

    override fun onCreate(savedInstanceState: Bundle?) {
        super.onCreate(savedInstanceState)
        setContentView(R.layout.user_coupon_list_activity)


        tv_redemptionCode.setOnClickListener {
            //兑换
            var redemptionCode = et_code.text.toString().trim()
            if (TextUtils.isEmpty(redemptionCode)) {
                showToast("请输入优惠券兑换码")
                return@setOnClickListener
            }
            getViewModel(CouponModel::class.java).execute(
                false,
                ReqRedemptionCodeCouponNew(redemptionCode = redemptionCode)
            ) {
                showToast(it.msg)
                if (it.success()) {
                    et_code.setText("")
                }
            }
        }

        initCommonTab();

    }

    open fun initCommonTab() {

        val tabEntity1 = CommonTabEntity()
        tabEntity1.title = "未使用"
        val tabEntity2 = CommonTabEntity()
        tabEntity2.title = "已使用"
        val tabEntity3 = CommonTabEntity()
        tabEntity3.title = "已过期"
        tabEntitys.add(tabEntity1)
        tabEntitys.add(tabEntity2)
        tabEntitys.add(tabEntity3)

        var refresh = object : CouponFragment.OnRefresh {
            override fun refresh() {
                getViewModel(CouponModel::class.java).queryUserCouponNum()
            }
        }

        var oneCouponFragment = CouponFragment()
        oneCouponFragment.mOnRefresh = refresh
        oneCouponFragment.type = CouponFragment.TYPE_UN_USED;
        fragments.add(oneCouponFragment)

        var twoCouponFragment = CouponFragment()
        twoCouponFragment.mOnRefresh = refresh
        twoCouponFragment.type = CouponFragment.TYPE_USED;
        fragments.add(twoCouponFragment)

        var threeCouponFragment = CouponFragment()
        threeCouponFragment.mOnRefresh = refresh
        threeCouponFragment.type = CouponFragment.TYPE_EXPIRED;
        fragments.add(threeCouponFragment)

        common_tab_layout.setTabData(tabEntitys, this, R.id.frame_layout, fragments)
        common_tab_layout.setCurrentTab(0)

    }

    @LiveDataMatch
    open fun onQueryUserCouponNumSuccess(num: RspUserCouponNum?) {
        num?.let {
            (tabEntitys[0] as CommonTabEntity).title = it.getUnusedCouponTxt()
            (tabEntitys[1] as CommonTabEntity).title = it.getUsedCouponTxt()
            (tabEntitys[2] as CommonTabEntity).title = it.getExpiredCouponTxt()
            common_tab_layout.notifyDataSetChanged()
        }

    }
}