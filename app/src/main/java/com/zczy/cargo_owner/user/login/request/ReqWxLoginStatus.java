package com.zczy.cargo_owner.user.login.request;

import com.zczy.comm.http.entity.BaseNewRequest;
import com.zczy.comm.http.entity.BaseRsp;
import com.zczy.cargo_owner.user.login.entity.WxLoginStatus;

/**
 * 功能描述:当日是否已签到
 *
 * <AUTHOR> 孙飞虎
 * @company 中储智运科技股份有限公司
 * @copyright （版权）中储南京智慧物流科技有限公司所有
 * @date 2019/1/24
 */
public class ReqWxLoginStatus extends BaseNewRequest<BaseRsp<WxLoginStatus>> {

    private String mobile;//手机号码
    private String openId;

    public ReqWxLoginStatus(String openId,String mobile) {
        super("mms-app/platform/weiXiOwner/getWxLoginStatusOwner");
        this.mobile = mobile;
        this.openId = openId;
    }
}


