package com.zczy.cargo_owner.user.city;

import android.app.Activity;
import android.content.Intent;
import android.graphics.Color;
import android.os.Bundle;
import androidx.annotation.NonNull;
import android.text.TextUtils;
import android.view.View;
import android.widget.TextView;

import com.sfh.lib.mvvm.annotation.LiveDataMatch;
import com.sfh.lib.ui.AbstractLifecycleActivity;
import com.sfh.lib.ui.dialog.DialogBuilder;
import com.zczy.cargo_owner.R;
import com.zczy.comm.data.entity.ECity;
import com.zczy.comm.data.entity.ECityAddress;
import com.zczy.comm.ui.UtilStatus;
import com.zczy.comm.utils.json.JsonUtil;
import com.zczy.comm.widget.selectcityv2.SelectCityView;

import java.util.List;

/***
 * 所属区域选择
 */
public class UserCityActivity extends AbstractLifecycleActivity<UserCityModel> implements SelectCityView.Listener, View.OnClickListener {

    public static void start(Activity context, int requestCode) {
        Intent intent = new Intent(context, UserCityActivity.class);
        context.startActivityForResult(intent, requestCode);
    }

    ELocationCity mCity;

    private View viewSearch;
    private TextView tvLocation;
    private TextView tvOK;
    private SelectCityView orderSelectCityView;

    @Override
    protected void onCreate(Bundle savedInstanceState) {
        super.onCreate(savedInstanceState);
        setContentView(R.layout.user_city_activity);
        UtilStatus.initStatus(this, Color.WHITE);
        viewSearch = findViewById(R.id.view_search);
        tvLocation = findViewById(R.id.tvLocation);
        tvOK = findViewById(R.id.tvOK);
        tvOK.setOnClickListener(this);
        viewSearch.setOnClickListener(this);
        orderSelectCityView = findViewById(R.id.order_select_city_view);
        orderSelectCityView.setDestinationShow(false);
        orderSelectCityView.setHistoryShow(false);
        orderSelectCityView.setCommitBtn(false);
        orderSelectCityView.setSelectMode(SelectCityView.SINGLE_MODE);
        orderSelectCityView.setListener(this);
    }

    @LiveDataMatch
    public void onAreaCodeByAddressSuccess(ELocationCity city) {
        this.mCity = city;
        this.tvOK.setEnabled(true);
        this.tvLocation.setText(city.getName());
    }

    @Override
    public void onClick(View v) {
        if (v == tvOK) {
            if (this.mCity != null && !TextUtils.isEmpty(mCity.getName())) {
                Intent data = new Intent();
                data.putExtra("City", JsonUtil.toJson(mCity));
                setResult(RESULT_OK, data);
                finish();
            } else {
                DialogBuilder dialogBuilder = new DialogBuilder ();
                dialogBuilder.setMessage("未定位到当前位置");
                dialogBuilder.setHideCancel(false);
                showDialog(dialogBuilder);
            }
        } else if (v == viewSearch) {
            UserCitySearchActivity.start(this, 1003);
        }
    }

    @Override
    protected void onActivityResult(int requestCode, int resultCode, Intent data) {
        super.onActivityResult(requestCode, resultCode, data);
        if (requestCode == 1003 && resultCode == RESULT_OK) {
            String json = data.getStringExtra(UserCitySearchActivity.EXTRA_CHOOSE_LIST);

            this.setBackData(JsonUtil.toJsonObject(json, ECityAddress.class));
        }
    }

    @Override
    public void onChooseComplete(@NonNull List<ECityAddress> selectCities) {
        if (selectCities.isEmpty()) {
            return;
        }
        this.setBackData(selectCities.get(0));
    }

    private void setBackData(ECityAddress city) {

        if (mCity == null) {
            mCity = new ELocationCity();
        }

        ECity area = city.getArea();
        if (area != null){
            mCity.setAreaCode(area.getAreaCode());
            mCity.setAreaNm(area.getAreaName());
        }
        ECity city2 = city.getCity();
        if (city2 != null){
            mCity.setCityCode(city2.getAreaCode());
            mCity.setCityNm(city2.getAreaName());
        }
        ECity pro = city.getPro();
        if (pro != null){
            mCity.setProCode(pro.getAreaCode());
            mCity.setProNm(pro.getAreaName());
        }
      
        Intent data = new Intent();
        data.putExtra("City", JsonUtil.toJson(mCity));
        setResult(RESULT_OK, data);
        finish();
    }
}
