package com.zczy.cargo_owner.user.info.model

import android.text.TextUtils
import com.sfh.lib.exception.HandleException
import com.sfh.lib.rx.IResult
import com.sfh.lib.rx.IResultSuccess
import com.zczy.comm.CommServer
import com.zczy.comm.data.SMSCodeModel
import com.zczy.comm.data.entity.EShowVerifyCode
import com.zczy.comm.data.entity.EUser
import com.zczy.comm.data.request.ReqCheckCode
import com.zczy.comm.data.request.ReqSendCode
import com.zczy.comm.data.request.ReqShowVerifyCode
import com.zczy.comm.http.entity.BaseRsp
import com.zczy.comm.http.entity.ResultData
import io.reactivex.Observable
import io.reactivex.ObservableOnSubscribe
import io.reactivex.ObservableSource
import io.reactivex.functions.Function

/**
 * 功能描述: 修改手机号码
 *
 * <AUTHOR> 孙飞虎
 * @company 中储智运科技股份有限公司
 * @copyright （版权）中储南京智慧物流科技有限公司所有
 * @date 2018/11/13
 */
class UserEditPhoneModel : SMSCodeModel() {
    override fun onCheckSuccess() {
        this.setValue("onCheckSuccess")
    }

    override fun onSendCode(success: Boolean, type: String) {
        this.setValue("onSendCode", success, type)
    }

    override fun onShowImageVerifyCode(req: ReqSendCode) {
        this.setValue("onShowImageVerifyCode", req)
    }

    /***
     * 保存新手机号码
     * @param phone
     * @param req
     */
    fun saveNewPhone(oldMobile: String, oldVerifyCode: String, oldVerifyCodeType: String, oldModuleType: String, phone: String, req: ReqCheckCode) {
        this.execute(false, Observable.just(req).flatMap { s -> getCheckVerifyCode(s) }.flatMap(Function<BaseRsp<ResultData>, ObservableSource<BaseRsp<ResultData>>> { rsp ->
            if (rsp.success()) {
                val info = ReqUpdateUseInfo()
                return@Function info.buildPhone(oldMobile, oldVerifyCode, oldVerifyCodeType, oldModuleType, phone, req.verifyCode, req.verifyCodeType, req.moduleType).task
            }
            Observable.just(rsp)
        }), object : IResult<BaseRsp<ResultData>> {
            @Throws(Exception::class)
            override fun onSuccess(t: BaseRsp<ResultData>) {
                if (t.success()) {
                    setValue("onEditSuccess")
                } else {
                    showDialogToast(t.msg)
                }
            }

            override fun onFail(e: HandleException) {
                showDialogToast(e.msg)
            }
        })
    }

    fun queryInfo() {
        val userServer = CommServer.getUserServer()
        val disposable = userServer.queryUserInfo(object : IResult<EUser> {
            @Throws(java.lang.Exception::class)
            override fun onSuccess(user: EUser) {
                setValue("onUserInfoSuccess", user)
            }

            override fun onFail(e: HandleException) {
                showDialogToast(e.msg)
            }
        })
        putDisposable(disposable)
    }

    fun unbindMobile(req: ReqUnbindMobile) {
        execute(req) {
            if (it.success()) {
                setValue("unBindSuccess", it.msg)
            } else {
                showToast(it.msg)
            }
        }
    }

    /***
     * 发送验证码是否显示图片验证码
     * @param req
     */
    override fun showImageVerifyCode(req: ReqSendCode) {
        this.execute(false, Observable.just(req).map {
            //1.发送验证码是否显示图片验证码
            var result = ReqShowVerifyCode(req.mobile).sendRequest()
            if (result.success() &&(result.data?.noShow() == true) ) {
                //2.直接发送短信
                return@map  req.sendRequest()
            }
            return@map result
        },
            IResultSuccess { baseRsp ->
                if (baseRsp.success()) {
                    val resultData = baseRsp.data
                    if (resultData is EShowVerifyCode) {
                        if (resultData.show()) {
                            //显示图片验证码
                            onShowImageVerifyCode(req)
                        }
                        return@IResultSuccess
                    }

                    //发送验证码 成功
                    onSendCode(true, req.type)
                } else if (TextUtils.equals(baseRsp.code, "MI300067")) {
                    setValue("goLogin", baseRsp.msg)
                } else {
                    showDialogToast(baseRsp.msg)
                }
            })
    }
}