package com.zczy.cargo_owner.user.setting;

import android.content.Context;
import android.content.Intent;
import android.os.Bundle;
import androidx.annotation.Nullable;
import android.text.Editable;
import android.text.TextUtils;
import android.text.TextWatcher;
import android.text.method.HideReturnsTransformationMethod;
import android.text.method.PasswordTransformationMethod;
import android.text.method.TransformationMethod;
import android.view.KeyEvent;
import android.view.View;
import android.widget.Button;
import android.widget.ImageView;

import com.sfh.lib.AppCacheManager;
import com.sfh.lib.mvvm.annotation.LiveDataMatch;
import com.sfh.lib.ui.dialog.DialogBuilder;
import com.zczy.cargo_owner.AppMainContext;
import com.zczy.cargo_owner.R;
import com.zczy.cargo_owner.user.login.PwdUtilKt;
import com.zczy.cargo_owner.user.setting.model.ReqPassword;
import com.zczy.cargo_owner.user.setting.model.UserPasswordModel;
import com.zczy.comm.data.request.ReqSendCode;
import com.zczy.comm.ui.BaseActivity;
import com.zczy.comm.widget.AppToolber;
import com.zczy.comm.widget.EditTextCloseView;

/**
 * 功能描述: 修改密码2
 *
 * <AUTHOR> 孙飞虎
 * @company 中储智运科技股份有限公司
 * @copyright （版权）中储南京智慧物流科技有限公司所有
 * @date 2018/11/19
 */
public class UserEditPasswordTwoActivity extends BaseActivity<UserPasswordModel> implements TextWatcher, View.OnClickListener {

    private EditTextCloseView etPwd;
    private ImageView tvLook;
    private EditTextCloseView etPwd2;
    private ImageView tvLook2;
    private Button btOK;
    String verifyCode;
    String verifyCodeType;
    private String oldMobile;
    private String oldModuleType;

    public static void start(Context context,String oldMobile, String verifyCode, String verifyCodeType,String oldModuleType) {
        Intent intent = new Intent(context, UserEditPasswordTwoActivity.class);
        intent.putExtra("verifyCode", verifyCode);
        intent.putExtra("verifyCodeType", verifyCodeType);
        intent.putExtra("oldModuleType",oldModuleType);
        intent.putExtra("oldMobile",oldMobile);
        context.startActivity(intent);
    }

    @Override
    protected int getLayout() {
        return R.layout.user_edit_password_two_activity;
    }

    @Override
    protected void bindView(@Nullable Bundle bundle) {
        verifyCode = getIntent().getStringExtra("verifyCode");
        verifyCodeType = getIntent().getStringExtra("verifyCodeType");
        oldMobile = getIntent().getStringExtra("oldMobile");
        oldModuleType = getIntent().getStringExtra("oldModuleType");

        AppToolber appToolber = findViewById(R.id.appToolber);
        String home = getIntent().getStringExtra("home");
        if (!TextUtils.isEmpty(home)) {
            appToolber.getTvLeft().setVisibility(View.INVISIBLE);
        }

        etPwd = findViewById(R.id.etPwd);
        tvLook = findViewById(R.id.tvLook);
        etPwd2 = findViewById(R.id.etPwd2);
        tvLook2 = findViewById(R.id.tvLook2);
        btOK = findViewById(R.id.btOK);
        tvLook.setOnClickListener(this);
        tvLook2.setOnClickListener(this);
        btOK.setOnClickListener(this);

        etPwd.setTransformationMethod(PasswordTransformationMethod.getInstance());
        etPwd2.setTransformationMethod(PasswordTransformationMethod.getInstance());

        etPwd.addTextChangedListener(this);
        etPwd2.addTextChangedListener(this);
    }

    @Override
    protected void initData() {

    }

    @Override
    public boolean onKeyDown(int keyCode, KeyEvent event) {
        if (keyCode == KeyEvent.KEYCODE_BACK) {
            String home = getIntent().getStringExtra("home");
            if (!TextUtils.isEmpty(home)) {
                return true;
            }
            DialogBuilder dialogBuilder = new DialogBuilder();
            dialogBuilder.setTitle("提示");
            dialogBuilder.setMessage("确定放弃修改密码？");
            dialogBuilder.setOkListener((dialogInterface, i) -> {
                dialogInterface.dismiss();
                finish();
            });
            showDialog(dialogBuilder);
            return true;
        }
        return super.onKeyDown(keyCode, event);
    }

    @LiveDataMatch(tag = "修改密码成功")
    public void onSaveSuccess(String toast) {
        DialogBuilder dialogBuilder = new DialogBuilder();
        dialogBuilder.setMessage("密码修改成功，请重新登录！");
        dialogBuilder.setCancelable(false);
        dialogBuilder.setOkListener((dialogInterface, i) -> {
            dialogInterface.dismiss();
            finish();
            AppMainContext appMainContext = AppCacheManager.getApplication();
            appMainContext.onLoseToken("", "");

        });
        this.showDialog(dialogBuilder);
    }

    @Override
    public void beforeTextChanged(CharSequence s, int start, int count, int after) {

    }

    @Override
    public void onTextChanged(CharSequence s, int start, int before, int count) {

    }

    @Override
    public void afterTextChanged(Editable s) {
        if (TextUtils.isEmpty(etPwd.getText().toString()) || TextUtils.isEmpty(etPwd2.getText().toString())) {
            btOK.setEnabled(false);
        } else {
            btOK.setEnabled(true);
        }
    }

    @Override
    public void onClick(View v) {
        if (tvLook == v) {
            TransformationMethod mTransformations = etPwd.getTransformationMethod();
            if (mTransformations == null || mTransformations instanceof PasswordTransformationMethod) {
                //显示密码
                etPwd.setTransformationMethod(HideReturnsTransformationMethod.getInstance());
                etPwd.setSelection(etPwd.getText().toString().length());
                tvLook.setImageResource(R.drawable.user_register_look_ok);
            } else {
                //隐藏密码
                etPwd.setTransformationMethod(PasswordTransformationMethod.getInstance());
                etPwd.setSelection(etPwd.getText().toString().length());
                tvLook.setImageResource(R.drawable.user_register_look_no);
            }
        } else if (tvLook2 == v) {
            TransformationMethod mTransformations = etPwd2.getTransformationMethod();
            if (mTransformations == null || mTransformations instanceof PasswordTransformationMethod) {
                //显示密码
                etPwd2.setTransformationMethod(HideReturnsTransformationMethod.getInstance());
                etPwd2.setSelection(etPwd2.getText().toString().length());
                tvLook2.setImageResource(R.drawable.user_register_look_ok);
            } else {
                //隐藏密码
                etPwd2.setTransformationMethod(PasswordTransformationMethod.getInstance());
                etPwd2.setSelection(etPwd2.getText().toString().length());
                tvLook2.setImageResource(R.drawable.user_register_look_no);
            }
        } else if (btOK == v) {
            if (!TextUtils.equals(etPwd.getText().toString(), etPwd2.getText().toString())) {
                showToast("两次密码不一致");
                return;
            }
            if (!PwdUtilKt.checkPwdRobustness(etPwd.getText().toString())) {
                PwdUtilKt.showCheckPwdDialog(this);
                return;
            }
            ReqPassword req = new ReqPassword();
            req.setModuleType(ReqSendCode.MODULE_TYPE_C);
            req.setNewPwd(etPwd.getText().toString());
            req.setVerifyCod(verifyCode);
            req.setVerifyCodeType(verifyCodeType);
            req.setOldMobile(oldMobile);
            req.setOldModuleType(oldModuleType);
            this.getViewModel().savePassword(req.buildEidtPassword());
        }
    }
}
