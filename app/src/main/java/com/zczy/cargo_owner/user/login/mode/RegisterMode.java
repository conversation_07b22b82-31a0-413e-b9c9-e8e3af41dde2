package com.zczy.cargo_owner.user.login.mode;

import android.text.TextUtils;

import com.sfh.lib.exception.HandleException;
import com.sfh.lib.rx.IResult;
import com.sfh.lib.rx.IResultSuccess;
import com.zczy.cargo_owner.user.login.entity.ERegisterCheckCode;
import com.zczy.cargo_owner.user.login.request.ReqRegister;
import com.zczy.cargo_owner.user.login.request.ReqRegisterCheckCode;
import com.zczy.cargo_owner.user.setting.model.ReqGetVideoPath;
import com.zczy.cargo_owner.user.setting.model.RespGetVideoPath;
import com.zczy.comm.CommServer;
import com.zczy.comm.data.SMSCodeModel;
import com.zczy.comm.data.entity.ECity;
import com.zczy.comm.data.entity.EShowVerifyCode;
import com.zczy.comm.data.request.ReqSendCode;
import com.zczy.comm.data.request.ReqShowVerifyCode;
import com.zczy.comm.http.entity.BaseRsp;
import com.zczy.comm.http.entity.ResultData;

import java.util.List;

import io.reactivex.Observable;
import io.reactivex.ObservableEmitter;
import io.reactivex.ObservableOnSubscribe;
import io.reactivex.disposables.Disposable;

public class RegisterMode extends SMSCodeModel implements IResult<BaseRsp<RegisterMode.RegisterResponse>> {


    @Override
    protected void onCleared() {
        super.onCleared();
    }

    /***
     * 注册2
     */
    public void register(ReqRegister req) {
        this.execute(true,req,this);
    }

    @Override
    public void onSuccess(BaseRsp<RegisterResponse> baseRsp) throws Exception {
        if (baseRsp.success() && TextUtils.equals("1", baseRsp.getData().getDealSuccess())) {
            setValue("onRegisterSuccess");
        } else {
            showDialogToast(baseRsp.getMsg());
        }
    }

    @Override
    public void onFail(HandleException e) {

        showDialogToast(e.getMsg());
    }

    @Override
    public void onSendCode(boolean success, String type) {
        setValue("onSendCodeRelust", success, type);
    }

    @Override
    public void onShowImageVerifyCode(ReqSendCode req) {
        setValue("onShowImageVerifyCode", req);
    }

    @Override
    public void onCheckSuccess() {
        setValue("onCheckVerifyCodeSuccess");
    }

    public static class RegisterResponse extends ResultData {
        String dealSuccess;

        public String getDealSuccess() {
            return dealSuccess;
        }
    }

    //获取视频链接
    public void getVideoPath(ReqGetVideoPath reqGetVideoPath) {
        this.execute(true, reqGetVideoPath, new IResult<BaseRsp<RespGetVideoPath>>() {
            @Override
            public void onFail(HandleException e) {
                showDialogToast(e.getMessage());
            }

            @Override
            public void onSuccess(BaseRsp<RespGetVideoPath> data) throws Exception {
                if (data.success()) {
                    setValue("ongetVideoPathSuccess", data.getData());
                } else {
                    showDialogToast(data.getData().getResultMsg());
                }
            }
        });
    }

    /***
     * 发送验证码是否显示图片验证码
     * @param req
     */
    @Override
    public void showImageVerifyCode(final ReqSendCode req) {

        Observable<BaseRsp<ResultData>> observable = Observable.create (new ObservableOnSubscribe<BaseRsp<ResultData>>() {

            @Override
            public void subscribe(ObservableEmitter<BaseRsp<ResultData>> emitter) throws Exception {

                BaseRsp result;
                //1.发送验证码是否显示图片验证码
                BaseRsp<EShowVerifyCode> step1 = new ReqShowVerifyCode(req.getMobile ()).sendRequest ();
                result = step1;
                if (step1.success () && step1.getData ().noShow ()) {
                    //2.直接发送短信
                    result = req.sendRequest ();
                }
                emitter.onNext (result);
                emitter.onComplete ();
            }
        });
        this.execute (false, observable, new IResultSuccess<BaseRsp<ResultData>>() {

            @Override
            public void onSuccess(BaseRsp<ResultData> baseRsp) throws Exception {

                if (baseRsp.success ()) {
                    ResultData resultData = baseRsp.getData ();
                    if (resultData instanceof EShowVerifyCode) {
                        if (((EShowVerifyCode) resultData).show ()) {
                            //显示图片验证码
                            onShowImageVerifyCode (req);
                        }
                        return;
                    }

                    //发送验证码 成功
                    onSendCode (true, req.getType ());

                } else {
                    showDialogToast (baseRsp.getMsg ());
                }
            }
        });
    }

    /***
     * 发送验证码
     * @param req
     */
    @Override
    public void sendVerifyCode(final ReqSendCode req) {

        this.execute (false, req, new IResult<BaseRsp<ResultData>> () {

            @Override
            public void onSuccess(BaseRsp<ResultData> baseRsp) throws Exception {

                if (baseRsp.success ()) {
                    onSendCode (true, req.getType ());
                } else {
                    showDialogToast (baseRsp.getMsg ());
                    onSendCode (false, req.getType ());
                }
            }

            @Override
            public void onFail(HandleException e) {

                showDialogToast (e.getMsg ());
                onSendCode (false, req.getType ());
            }
        });
    }


    /***
     *  验证验证码接口 ZCZY-10873 供应链会员中台-用户部分
     * @param req
     */
    public void checkVerifyCode(final ReqRegisterCheckCode req) {

        this.execute (false, req, new IResultSuccess<BaseRsp<ERegisterCheckCode>> () {

            @Override
            public void onSuccess(BaseRsp<ERegisterCheckCode> baseRsp) throws Exception {

                if (baseRsp.success ()) {
                    if (baseRsp.getData().isSaas()){
                        setValue("onPhoneExist", req.getMobile());

                    }else {
                        setValue("onCheckVerifyCodeSuccess");
                    }
                } else {
                    showDialogToast (baseRsp.getMsg ());
                }
            }
        });
    }

    public void getCityInfo() {
        //  获取数据
        Disposable disposable =  CommServer.getCacheServer().getCity(new IResult<List<ECity>> () {
            @Override
            public void onSuccess(List<ECity> eCities) {
                setValue("onCityInfoSuccess", eCities);
            }

            @Override
            public void onFail(HandleException e) {

            }
        });
        this.putDisposable(disposable);
    }

}
