package com.zczy.cargo_owner.user.info

import android.content.Context
import android.content.Intent
import android.os.Bundle
import android.text.TextUtils
import android.view.View
import com.sfh.lib.mvvm.annotation.LiveDataMatch
import com.sfh.lib.mvvm.service.BaseViewModel
import com.sfh.lib.rx.ui.UtilRxView
import com.zczy.cargo_owner.R
import com.zczy.cargo_owner.deliver.addorder.container.BaseActivityV1
import com.zczy.cargo_owner.user.info.model.ReqBindMobile
import com.zczy.cargo_owner.user.login.mode.ForgetModel
import com.zczy.comm.Const
import com.zczy.comm.data.request.ReqSendCode
import com.zczy.comm.utils.PhoneUtil
import com.zczy.comm.utils.VerificationCodeUtil
import com.zczy.comm.widget.ImageCodeDialog
import kotlinx.android.synthetic.main.user_edit_phone_activity.iv_server_phone
import kotlinx.android.synthetic.main.user_refine_phone_activity.btNext
import kotlinx.android.synthetic.main.user_refine_phone_activity.etCode
import kotlinx.android.synthetic.main.user_refine_phone_activity.etPhone
import kotlinx.android.synthetic.main.user_refine_phone_activity.llVoice
import kotlinx.android.synthetic.main.user_refine_phone_activity.tvTime
import kotlinx.android.synthetic.main.user_refine_phone_activity.tvVoiceTime

/**
 *@Desc 完善手机号
 *@User ssp
 *@Date 2023/6/5-11:15
 */
class UserRefinePhoneActivity : BaseActivityV1<BaseViewModel>() {

    private var util: VerificationCodeUtil? = null
    private var codeType = ReqSendCode.TYPE_SMS

    companion object {

        @JvmStatic
        fun jumpPage(context: Context?) {
            val intent = Intent(context, UserRefinePhoneActivity::class.java)
            context?.startActivity(intent)
        }
    }

    override val layout: Int
        get() = R.layout.user_refine_phone_activity


    override fun bindView(bundle: Bundle?) {
        bindClickEvent(btNext)
        bindClickEvent(iv_server_phone)
        UtilRxView.textChangeEvents(etPhone, 300) {
            if (it === this.etPhone.text) {
                val phone: String = it.toString()
                if (!TextUtils.isEmpty(phone) && phone.length >= 11) {
                    val code = etCode.text.toString()
                    this.etCode.requestFocus()
                    this.etCode.setSelection(if (TextUtils.isEmpty(code)) 0 else code.length)
                    util?.setAbled(true)
                } else {
                    util?.setAbled(false)
                }
            }

            val phone = this.etPhone.text.toString()
            val code = this.etCode.text.toString()
            this.btNext.isEnabled = !(TextUtils.isEmpty(phone) || TextUtils.isEmpty(code))
        }
        UtilRxView.textChangeEvents(etCode, 300) {
            if (it === this.etPhone.text) {
                val phone: String = it.toString()
                if (!TextUtils.isEmpty(phone) && phone.length >= 11) {
                    val code = etCode.text.toString()
                    this.etCode.requestFocus()
                    this.etCode.setSelection(if (TextUtils.isEmpty(code)) 0 else code.length)
                    util?.setAbled(true)
                } else {
                    util?.setAbled(false)
                }
            }

            val phone = this.etPhone.text.toString()
            val code = this.etCode.text.toString()
            this.btNext.isEnabled = !(TextUtils.isEmpty(phone) || TextUtils.isEmpty(code))
        }

        util = VerificationCodeUtil(
            ReqSendCode.MODULE_TYPE_A,
            object : VerificationCodeUtil.IOnCallback {
                override fun getPhone(): String {
                    return etPhone.text.toString()
                }

                override fun onClickCode(req: ReqSendCode) {
                    getViewModel(ForgetModel::class.java).checkMobileExist(
                        req = ReqCheckMobileExist(
                            checkType = ReqCheckMobileExist.checkType1,
                            mobile = phone
                        ), req
                    )
                }

                override fun showToast(toast: CharSequence) {}
            }).build(tvTime, tvVoiceTime, llVoice)
    }

    override fun initData() {

    }

    override fun onSingleClick(v: View) {
        super.onSingleClick(v)
        when (v.id) {
            R.id.iv_server_phone -> {
                PhoneUtil.callPhone(this@UserRefinePhoneActivity, Const.PHONE_SERVER_400)
            }

            R.id.btNext -> {
                val phone = etPhone.text.toString()
                val code = etCode.text.toString()
                if (TextUtils.isEmpty(phone)) {
                    showToast("请输入手机号码")
                    return
                }
                if (TextUtils.isEmpty(code)) {
                    showToast("请输入验证码")
                    return
                }
                getViewModel(ForgetModel::class.java).checkMobileExist(
                    req = ReqCheckMobileExist(
                        checkType = ReqCheckMobileExist.checkType1,
                        mobile = phone
                    ), null
                )
            }
        }
    }


    @LiveDataMatch(tag = "显示图片验证码")
    open fun onShowImageVerifyCode(req: ReqSendCode) {
        val phone = etPhone.text.toString()
        ImageCodeDialog(this, phone) { _: ImageCodeDialog?, code: String? ->
            req.setImageCode(code)
            getViewModel(ForgetModel::class.java).sendVerifyCode(req)
        }.show()
    }

    @LiveDataMatch
    open fun onSendCode(success: Boolean, type: String) {
        util?.onSendCodeResult(success, type)
    }

    @LiveDataMatch
    open fun checkMobileExistSuccess(req: ReqSendCode?) {
        if (req != null) {
            codeType = req.type
            getViewModel(ForgetModel::class.java).showImageVerifyCode(req)
            return
        }
        val phone = etPhone.text.toString()
        val code = etCode.text.toString()
        getViewModel(ForgetModel::class.java).bindMobile(
            ReqBindMobile(
                mobile = phone,
                verifyCode = code,
                verifyCodeType = codeType,
                moduleType = ReqSendCode.MODULE_TYPE_A
            )
        )
    }

    @LiveDataMatch
    open fun bindMobileSuccess() {
        finish()
    }
}