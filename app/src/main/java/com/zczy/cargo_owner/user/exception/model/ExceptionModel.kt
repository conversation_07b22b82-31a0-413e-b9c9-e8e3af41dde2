package com.zczy.cargo_owner.user.exception.model

import com.sfh.lib.exception.HandleException
import com.sfh.lib.mvvm.service.BaseViewModel
import com.sfh.lib.rx.IResult
import com.zczy.cargo_owner.user.exception.bean.WaybillDetails
import com.zczy.cargo_owner.user.exception.req.*
import com.zczy.comm.CommServer
import com.zczy.comm.file.IFileServer.OnFileUploaderListener
import com.zczy.comm.http.entity.BaseRsp
import com.zczy.comm.http.entity.PageList
import com.zczy.comm.http.entity.ResultData
import java.io.File

/**
 * 功能描述: 运单异常管理
 * <AUTHOR>
 * @date 2023/4/3-13:58
 */

class ExceptionModel : BaseViewModel() {
    fun queryExceptionOrderAuditList(req: ReqWayBillList) {
        this.execute(req, object : IResult<BaseRsp<PageList<WaybillException>>> {
            override fun onFail(e: HandleException) {
                setValue("queryExceptionListSuccess")
            }

            @Throws(Exception::class)
            override fun onSuccess(pageListBaseRsp: BaseRsp<PageList<WaybillException>>) {
                if (pageListBaseRsp.success()) {
                    setValue("queryExceptionListSuccess", pageListBaseRsp.data)
                } else {
                    setValue("queryExceptionListSuccess")
                }
            }
        })
    }

    fun uploadFiles(files: List<String>) {
        for (file in files) {
            upLoadPic(file)
        }
    }

    /**
     * 图片上传
     *
     * @param file
     */
    fun upLoadPic(file: String) {
        showLoading(true)
        val fileServer = CommServer.getFileServer()
        val old = File(file)
        if (old.exists()) {
            val disposable = fileServer.update(old, object : OnFileUploaderListener {
                override fun onSuccess(tag: File, url: String) {
                    hideLoading()
                    setValue("upLoadPicSuccess", tag, url)
                }

                override fun onFailure(tag: File, error: String) {
                    hideLoading()
                    showToast(error)
                }
            }, true)
            putDisposable(disposable)
        }
    }

    /**
     * 查询运单详情接口
     */
    fun queryWaybillDeatils(req: ReqWayDetails) {
        this.execute<BaseRsp<WaybillDetails>>(true, req, object : IResult<BaseRsp<WaybillDetails>> {
            override fun onFail(e: HandleException) {
                showDialogToast(e.message)
            }

            @Throws(Exception::class)
            override fun onSuccess(waybillDetailsBaseRsp: BaseRsp<WaybillDetails>) {
                setValue("onWaybillDetailsSuccess", waybillDetailsBaseRsp)
            }
        })
    }

    /**
     * 提交证明材料
     */
    fun exceptionOrderSubmit(reqWaybillSubmit: ReqWaybillSubmit) {
        this.execute<BaseRsp<ResultData>>(false, reqWaybillSubmit, object : IResult<BaseRsp<ResultData>> {
            override fun onFail(e: HandleException) {
                showDialogToast(e.message)
            }

            @Throws(Exception::class)
            override fun onSuccess(baseRsp: BaseRsp<ResultData>) {
                if (baseRsp.success()) {
                    setValue("submitSuccess", baseRsp)
                } else {
                    showDialogToast(baseRsp.msg)
                }
            }
        })
    }

    /**
     * 证明材料催审核
     * @param req
     */
    fun urgeExceptionOrderExamine(req: ReqUrgeExceptionOrderExamine) {
        this.execute<BaseRsp<ResultData>>(false, req, object : IResult<BaseRsp<ResultData>> {
            override fun onFail(e: HandleException) {
                showDialogToast(e.message)
            }

            @Throws(Exception::class)
            override fun onSuccess(baseRsp: BaseRsp<ResultData>) {
                if (baseRsp.success()) {
                    setValue("urgeExceptionOrderExamineSuccess", baseRsp)
                } else {
                    showDialogToast(baseRsp.msg)
                }
            }
        })
    }

    /**
     * 注释：证明材料催审核_待处理
     * 时间：2024/10/11 8:41
     * 作者：王家辉
     * */
    fun urgeNoDealExceptionOrderExamine(req: ReqUrgeNoDealExceptionOrderExamine) {
        this.execute<BaseRsp<ResultData>>(false, req, object : IResult<BaseRsp<ResultData>> {
            override fun onFail(e: HandleException) {
                showDialogToast(e.message)
            }

            @Throws(Exception::class)
            override fun onSuccess(baseRsp: BaseRsp<ResultData>) {
                if (baseRsp.success()) {
                    setValue("urgeNoDealExceptionOrderExamineSuccess", baseRsp)
                } else {
                    showDialogToast(baseRsp.msg)
                }
            }
        })
    }
}