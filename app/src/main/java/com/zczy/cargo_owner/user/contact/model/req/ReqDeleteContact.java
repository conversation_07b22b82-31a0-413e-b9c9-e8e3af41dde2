package com.zczy.cargo_owner.user.contact.model.req;

import com.zczy.cargo_owner.user.contact.model.resp.RespContactListData;
import com.zczy.cargo_owner.user.contact.model.resp.RespDeleteContactData;
import com.zczy.comm.http.entity.BaseNewRequest;
import com.zczy.comm.http.entity.BaseRsp;

public class ReqDeleteContact extends BaseNewRequest<BaseRsp<RespDeleteContactData>> {
    public ReqDeleteContact() {
        super("mms-app/consignorTelPhone/deleteConsignorTelphone");
    }

    public long id;//通讯录id
}
