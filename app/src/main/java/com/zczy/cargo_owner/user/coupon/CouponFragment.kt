package com.zczy.cargo_owner.user.coupon

import android.graphics.Color
import android.os.Bundle
import androidx.annotation.IntDef
import android.view.View
import com.chad.library.adapter.base.BaseQuickAdapter
import com.chad.library.adapter.base.listener.OnItemClickListener
import com.sfh.lib.mvvm.annotation.LiveDataMatch
import com.zczy.cargo_owner.R
import com.zczy.cargo_owner.order.settlement.SettlementApplicationListActivity
import com.zczy.cargo_owner.user.coupon.req.RspUserCoupon
import com.zczy.comm.http.entity.PageList
import com.zczy.comm.ui.BaseFragment
import com.zczy.comm.utils.ex.isTrue
import com.zczy.comm.widget.pulltorefresh.CommEmptyView
import com.zczy.comm.widget.pulltorefresh.SwipeRefreshMoreLayout

/**
 * PS: 个人中心 优惠卷  #1：未使用 2：使用记录 3：已过期
 */
class CouponFragment : BaseFragment<CouponModel>() {
    companion object {
        const val TYPE_UN_USED = 1 // 未使用
        const val TYPE_USED = 2 // 已使用
        const val TYPE_EXPIRED = 3 // 过期
    }

    interface OnRefresh {
        fun refresh()
    }

    @IntDef(TYPE_UN_USED, TYPE_USED, TYPE_EXPIRED)
    @Retention(AnnotationRetention.SOURCE)
    annotation class Type

    @Type
    var type = TYPE_UN_USED
    var mOnRefresh: OnRefresh? = null

    private lateinit var swipeRefreshMoreLayout: SwipeRefreshMoreLayout

    override fun getLayout(): Int = R.layout.order_common_fragment

    override fun bindView(view: View, bundle: Bundle?) {
        swipeRefreshMoreLayout = view.findViewById(R.id.swipe_refresh_more_layout)

        val adapter = CouponListAdapter()
        adapter.itemViewType = type

        val emptyView = CommEmptyView.creatorDef(context)
        swipeRefreshMoreLayout.setAdapter(adapter, true)
        swipeRefreshMoreLayout.setEmptyView(emptyView)
        swipeRefreshMoreLayout.setRecyclerViewBgColor(Color.WHITE)
        swipeRefreshMoreLayout.addOnItemListener(object : OnItemClickListener() {
            override fun onSimpleItemClick(
                adapter: BaseQuickAdapter<*, *>,
                view: View,
                position: Int
            ) {
                val rspUserCoupon = adapter.getItem(position) as RspUserCoupon

                if (type == TYPE_UN_USED) {

                    if (!rspUserCoupon.isUse.isTrue) {
                        //使用时间未到场景
                        return
                    }
                    SettlementApplicationListActivity.start(context, 0, "")
                }
            }
        })

        swipeRefreshMoreLayout.setOnLoadListener2 {
            viewModel?.page(it, type)
            if (mOnRefresh != null && it <= 1){
                mOnRefresh?.refresh()
            }
        }
    }

    override fun initData() {
        swipeRefreshMoreLayout.onAutoRefresh()
    }


    @LiveDataMatch
    open fun onQueryUserCouponListSuccess(data: PageList<RspUserCoupon>?) {
        swipeRefreshMoreLayout.onRefreshCompale(data)
    }

}