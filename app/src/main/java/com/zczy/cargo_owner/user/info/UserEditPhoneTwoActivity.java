package com.zczy.cargo_owner.user.info;

import android.content.Context;
import android.content.Intent;
import android.graphics.Color;
import android.os.Bundle;
import android.text.Editable;
import android.text.TextUtils;
import android.text.TextWatcher;
import android.view.View;
import android.widget.Button;
import android.widget.LinearLayout;

import com.sfh.lib.mvvm.annotation.LiveDataMatch;
import com.sfh.lib.ui.AbstractLifecycleActivity;
import com.sfh.lib.ui.dialog.DialogBuilder;
import com.zczy.cargo_owner.R;
import com.zczy.cargo_owner.user.info.model.UserEditPhoneModel;
import com.zczy.cargo_owner.user.login.activity.LoginAccountActivity;
import com.zczy.comm.Const;
import com.zczy.comm.data.request.ReqCheckCode;
import com.zczy.comm.data.request.ReqSendCode;
import com.zczy.comm.permission.PermissionCallBack;
import com.zczy.comm.permission.PermissionUtil;
import com.zczy.comm.ui.UtilStatus;
import com.zczy.comm.utils.PhoneUtil;
import com.zczy.comm.utils.VerificationCodeUtil;
import com.zczy.comm.widget.EditTextCloseView;
import com.zczy.comm.widget.ImageCodeDialog;
import com.zczy.comm.widget.RxTimeCountView;

/***
 * 修改手机号码2
 */
public class UserEditPhoneTwoActivity extends AbstractLifecycleActivity<UserEditPhoneModel> implements View.OnClickListener, TextWatcher {

    public static void start(Context context, String oldMobile, String oldVerifyCode, String oldVerifyCodeType, String oldModuleType) {
        Intent intent = new Intent(context, UserEditPhoneTwoActivity.class);
        intent.putExtra("oldMobile", oldMobile);
        intent.putExtra("oldVerifyCode", oldVerifyCode);
        intent.putExtra("oldVerifyCodeType", oldVerifyCodeType);
        intent.putExtra("oldModuleType", oldModuleType);
        context.startActivity(intent);
    }


    private EditTextCloseView etPhone;
    private EditTextCloseView etCode;
    private RxTimeCountView tvSendCode;
    private LinearLayout lyVoice;
    private RxTimeCountView tvVoiceTime;
    private Button btMext;
    /*
     * 传入旧手机的相关参数给服务端判断
     * */
    private String oldMobile;
    private String oldVerifyCode;
    private String oldVerifyCodeType;
    private String oldModuleType;
    /***
     * 验证码类型
     */
    private String codeType = ReqSendCode.TYPE_SMS;
    private VerificationCodeUtil codeUtil;

    @Override
    protected void onCreate(Bundle savedInstanceState) {
        super.onCreate(savedInstanceState);
        setContentView(R.layout.user_edit_phone_two_activity);
        UtilStatus.initStatus(this, Color.WHITE);
        etPhone = findViewById(R.id.etPhone);
        etCode = findViewById(R.id.etCode);
        tvSendCode = findViewById(R.id.tvSendCode);
        lyVoice = findViewById(R.id.lyVoice);
        tvVoiceTime = findViewById(R.id.tvVoiceTime);
        btMext = findViewById(R.id.btMext);
        btMext.setOnClickListener(this);
        oldMobile = getIntent().getStringExtra("oldMobile");
        oldVerifyCode = getIntent().getStringExtra("oldVerifyCode");
        oldVerifyCodeType = getIntent().getStringExtra("oldVerifyCodeType");
        oldModuleType = getIntent().getStringExtra("oldModuleType");

        etPhone.addTextChangedListener(this);
        etCode.addTextChangedListener(this);

        codeUtil = new VerificationCodeUtil(ReqSendCode.MODULE_TYPE_D, new VerificationCodeUtil.IOnCallback() {
            @Override
            public String getPhone() {
                return etPhone.getText().toString();
            }

            @Override
            public void onClickCode(ReqSendCode req) {
                codeType = req.getType();
                //非平台会员,才发送验证码
                req.setPlateFormType("0");
                getViewModel(UserEditPhoneModel.class).showImageVerifyCode(req);
            }

            @Override
            public void showToast(CharSequence toast) {
                UserEditPhoneTwoActivity.this.showDialogToast(toast);
            }
        }).build(tvSendCode, tvVoiceTime, lyVoice);
        findViewById(R.id.iv_server_phone).setOnClickListener(v -> PhoneUtil.callPhone(UserEditPhoneTwoActivity.this, Const.PHONE_SERVER_400));
    }

    @LiveDataMatch(tag = "提示")
    public void goLogin(String msg) {
        DialogBuilder dialogBuilder = new DialogBuilder();
        dialogBuilder.setTitle("提示");
        dialogBuilder.setMessage(msg);
        dialogBuilder.setOKText("前往登录");
        dialogBuilder.setOkListener((dialogInterface, i) -> {
            dialogInterface.dismiss();
            LoginAccountActivity.startPhone(UserEditPhoneTwoActivity.this, etPhone.getText().toString());
            finish();
        });
        dialogBuilder.setCancelText("更新手机号");
        dialogBuilder.setCancelListener((dialogInterface, i) -> {
            dialogInterface.dismiss();
            etPhone.setText("");
        });
        showDialog(dialogBuilder);
    }

    @LiveDataMatch
    public void onSendCode(boolean success, String type) {

        codeUtil.onSendCodeResult(success, type);
    }

    @LiveDataMatch
    public void onShowImageVerifyCode(final ReqSendCode req) {

        final String phone = etPhone.getText().toString();
        new ImageCodeDialog(this, phone, new ImageCodeDialog.CodeCallback() {
            @Override
            public void onClickCode(ImageCodeDialog dialog, String code) {

                req.setImageCode(code);
                getViewModel().sendVerifyCode(req);
            }
        }).show();
    }

    @LiveDataMatch(tag = "修改手机号码成功")
    public void onEditSuccess() {
        DialogBuilder builder = new DialogBuilder();
        builder.setMessage("修改手机号码成功");
        builder.setHideCancel(true);
        builder.setCancelable(false);
        builder.setOkListener((DialogBuilder.DialogInterface dialogInterface, int i) -> {
            dialogInterface.dismiss();
            finish();
        });
        showDialog(builder);
    }

    @Override
    public void onClick(View v) {
        String phone = etPhone.getText().toString();
        if (TextUtils.isEmpty(phone)) {
            showDialogToast("请输入手机号");
            return;
        }
        String code = etCode.getText().toString();
        if (TextUtils.isEmpty(code)) {
            showDialogToast("请输入验证码");
            return;
        }
        if (code.length() < 6) {
            showDialogToast("请输入正确的验证码");
            return;
        }

        ReqCheckCode req = new ReqCheckCode();
        req.setMobile(phone);
        req.setVerifyCode(code);
        req.setVerifyCodeType(codeType);
        req.setModuleType(ReqCheckCode.MODULE_TYPE_D);
        this.getViewModel().saveNewPhone(oldMobile, oldVerifyCode, oldVerifyCodeType, oldModuleType, phone, req);
    }

    @Override
    public void beforeTextChanged(CharSequence s, int start, int count, int after) {

    }

    @Override
    public void onTextChanged(CharSequence s, int start, int before, int count) {

    }

    @Override
    public void afterTextChanged(Editable s) {
        boolean enabled = TextUtils.isEmpty(etPhone.getText().toString()) || TextUtils.isEmpty(etCode.getText().toString());
        btMext.setEnabled(!enabled);
    }
}
