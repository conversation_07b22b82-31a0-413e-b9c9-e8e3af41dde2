package com.zczy.cargo_owner.user;


import android.app.AlertDialog;
import android.content.Context;
import android.os.Bundle;
import androidx.annotation.NonNull;

import android.text.TextUtils;
import android.widget.TextView;

import com.zczy.cargo_owner.R;
import com.zczy.cargo_owner.user.info.model.EVipCustomer;
import com.zczy.comm.SpannableHepler;

public class VipDialog extends AlertDialog {

    private EVipCustomer vipCustomer;

    public VipDialog(Context context, EVipCustomer data) {
        super(context, R.style.dialog_Fullscreen2);
        this.vipCustomer = data;
    }

    @Override
    protected void onCreate(Bundle savedInstanceState) {
        super.onCreate(savedInstanceState);
        setContentView(R.layout.user_vip_dialog);

        findViewById(R.id.iv_close).setOnClickListener(v -> dismiss());
        findViewById(R.id.tv_close).setOnClickListener(v -> dismiss());

        TextView tv_title = findViewById(R.id.tv_title);
        if (TextUtils.equals(vipCustomer.getVipFlag(), "0")) {
            // 非vip
            tv_title.setText("尊敬的用户");
        } else {
            // vip
            tv_title.setText(String.format("尊敬的V%s用户", vipCustomer.getVipLevel()));
        }


        TextView tv_content = findViewById(R.id.tv_content);

        SpannableHepler spannableHepler = new SpannableHepler();

        if (TextUtils.equals("1", vipCustomer.getExpiringFlag()) || TextUtils.equals("2", vipCustomer.getLevelState())) {
            //天数不足 或 2-降级
            spannableHepler.append("根据平台数据统计，您的V" + vipCustomer.getVipLevel() + "有效期已不足")
                    .append(new SpannableHepler.Txt("30", "#ffff602e"))
                    .append(String.format("天，到期后将不再享受V%s专属价格优惠、绿色服务通道等服务。\n", vipCustomer.getVipLevel()))
                    .append(new SpannableHepler.Txt("如需保持当前级别，您可以：\n\n", "#ffff602e"));


        } else if (TextUtils.equals("1", vipCustomer.getLevelState())) {
            // 1-升级
            //根据平台数据统计，您本月已接近Vx-1级，升级即享Vx-1专属价格优惠、绿色服务通道等多重服务!
            spannableHepler.append(String.format("根据平台数据统计，您本月已接近V%s级，升级即享", vipCustomer.getRecentVipLevel()))
                    .append(new SpannableHepler.Txt("V" + vipCustomer.getRecentVipLevel(), "#ffF19500"))
                    .append("专属价格优惠、绿色服务通道等多重服务!\n")
                    .append(new SpannableHepler.Txt("如需快速升级，您可以：\n\n", "#ffF19500"));
        }
        spannableHepler
                .append(new SpannableHepler.Txt("◆", "#ffF19500")).append(" 增加发单量\n\n")
                .append(new SpannableHepler.Txt("◆", "#ffF19500")).append(" 增加开放货源\n\n")
                .append(new SpannableHepler.Txt("◆", "#ffF19500")).append(" 提高油气品配置率\n\n")
                .append(new SpannableHepler.Txt("◆", "#ffF19500")).append(" 提高货物保障服务使用率\n\n")
                .append(new SpannableHepler.Txt("◆", "#ffF19500")).append(" 使用平台数字供应链产品\n\n");
        tv_content.setText(spannableHepler.builder());
    }


}
