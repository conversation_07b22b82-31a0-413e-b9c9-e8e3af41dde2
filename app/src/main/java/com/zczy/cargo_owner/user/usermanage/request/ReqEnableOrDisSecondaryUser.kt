package com.zczy.cargo_owner.user.usermanage.request

import com.zczy.comm.http.entity.BaseNewRequest
import com.zczy.comm.http.entity.BaseRsp
import com.zczy.comm.http.entity.ResultData

/**
 * 注释：启用停用子账号
 * 时间：2024/6/19 0019 9:29
 * 作者：郭翰林
 */
class ReqEnableOrDisSecondaryUser(
    var childId: String = "",
    var status: String = ""
) :
    BaseNewRequest<BaseRsp<ResultData>>("mms-app/platform/user/enableOrDisSecondaryUser")