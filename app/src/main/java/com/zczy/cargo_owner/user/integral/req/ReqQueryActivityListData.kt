package com.zczy.cargo_owner.user.integral.req

import com.zczy.comm.http.entity.BaseNewRequest
import com.zczy.comm.http.entity.BaseRsp
import com.zczy.comm.http.entity.PageList

/**
 * PS: 查询app端我的活动列表数据
 * http://wiki.zczy56.com/pages/viewpage.action?pageId=12355717
 * Created by sdx on 2019/3/5.
 */
data class ReqQueryActivityListData(
        var nowPage: Int = 1,
        var pageSize: Int = 10,
        var queryType: String = "1" // 查询类型（1--当前活动列表、2--历史记录 ）
) : BaseNewRequest<BaseRsp<PageList<RspActivityListItem>>>("mms-app/activity/queryActivityListData")

data class RspActivityListItem(
        var activityId: String = "", // 活动ID
        var activityIdName: String = "", // 活动ID名称
        var activityName: String = "", // 活动名称
        var themeUrl: String = "", // 活动主题图片路径
        var haveTop: String = "", // 是否置顶 1 : 是 0:否
        var topTime: String = "", // 置顶时间
        var jumpUrl: String = "", // 跳转
        var startTime: String = "", // 活动开始时间
        var endTime: String = ""  // 活动结束时间
)