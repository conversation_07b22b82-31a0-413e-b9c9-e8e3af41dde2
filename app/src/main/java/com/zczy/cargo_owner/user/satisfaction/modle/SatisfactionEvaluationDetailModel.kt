package com.zczy.cargo_owner.user.satisfaction.modle

import com.sfh.lib.mvvm.service.BaseViewModel
import com.zczy.cargo_owner.user.satisfaction.req.ReqSatisfactionEvaluationDetail

/**
 *    author : Ssp
 *    date   : 2019/7/5 13:38
 *    desc   : 客服满意度评价详情
 */
class SatisfactionEvaluationDetailModel : BaseViewModel() {
    fun queryData(evaluateId: String) {
        var reqSatisfactionEvaluationDetail = ReqSatisfactionEvaluationDetail()
        reqSatisfactionEvaluationDetail.evaluateId = evaluateId
        execute(true,
                reqSatisfactionEvaluationDetail
        ) { t ->
            if (t.success()) {
                setValue("onSuccess", t.data)
            } else {
                showDialogToast(t.msg)
            }
        }
    }
}