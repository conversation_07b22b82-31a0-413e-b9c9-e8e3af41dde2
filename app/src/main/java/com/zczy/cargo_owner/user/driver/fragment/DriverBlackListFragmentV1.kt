package com.zczy.cargo_owner.user.driver.fragment

import android.graphics.Color
import android.os.Bundle
import android.view.KeyEvent
import android.view.View
import android.view.inputmethod.EditorInfo
import android.widget.TextView.OnEditorActionListener
import com.sfh.lib.event.RxBusEvent
import com.sfh.lib.mvvm.annotation.LiveDataMatch
import com.sfh.lib.ui.dialog.DialogBuilder
import com.zczy.cargo_owner.R
import com.zczy.cargo_owner.user.driver.DriverBlackAddActivityV1
import com.zczy.cargo_owner.user.driver.DriverBlackRecordListActivity
import com.zczy.cargo_owner.user.driver.DriverBlackRemoveActivityV1
import com.zczy.cargo_owner.user.driver.adapter.DriverBlacklistAdapterV1
import com.zczy.cargo_owner.user.driver.model.DriverBlacklistModel
import com.zczy.cargo_owner.user.driver.model.EDriverBlack
import com.zczy.cargo_owner.user.driver.model.ReqPageDriverBlack
import com.zczy.cargo_owner.user.driver.model.RspQueryLogisticsOrderState
import com.zczy.cargo_owner.user.driver.model.RxAddRelationVehicleBlack
import com.zczy.comm.http.entity.PageList
import com.zczy.comm.ui.BaseFragment
import com.zczy.comm.utils.ex.isTrue
import com.zczy.comm.widget.pulltorefresh.CommEmptyView
import kotlinx.android.synthetic.main.driver_black_list_fragment_v1.et_search
import kotlinx.android.synthetic.main.driver_black_list_fragment_v1.swipe_refresh
import kotlinx.android.synthetic.main.driver_black_list_fragment_v1.tvAdd
import kotlinx.android.synthetic.main.driver_black_list_fragment_v1.tvDelete
import kotlinx.android.synthetic.main.driver_black_list_fragment_v1.tvLimit
import kotlinx.android.synthetic.main.driver_black_list_fragment_v1.tvNormal

/**
 *@Desc 黑名单管理
 *@User ssp
 *@Date 2023/7/18-19:22
 */
class DriverBlackListFragmentV1 : BaseFragment<DriverBlacklistModel>() {
    private val mAdapterV1 = DriverBlacklistAdapterV1()
    private var isNormal = "0"

    companion object {
        @JvmStatic
        fun newInstance(): DriverBlackListFragmentV1 {
            return DriverBlackListFragmentV1()
        }
    }

    override fun getLayout(): Int {
        return R.layout.driver_black_list_fragment_v1
    }

    override fun initData() {
        viewModel?.queryLogisticsOrderState()
    }

    override fun bindView(view: View, bundle: Bundle?) {
        et_search.setOnEditorActionListener(OnEditorActionListener { _, actionId, keyEvent ->
            if (actionId == EditorInfo.IME_ACTION_SEARCH || keyEvent != null && keyEvent.keyCode == KeyEvent.KEYCODE_ENTER) {
                swipe_refresh.onAutoRefresh()
                return@OnEditorActionListener true
            }
            false
        })
        swipe_refresh.apply {
            setAdapter(mAdapterV1, true)
            setEmptyView(CommEmptyView.creatorDef(<EMAIL>))
            addItemDecorationSize(7)
            setOnLoadListener2 { nowPage ->
                getViewModel(DriverBlacklistModel::class.java).page(
                    req = ReqPageDriverBlack(
                        nowPage = nowPage,
                        queryParam = et_search.text.toString().ifEmpty {
                            null
                        },
                        blackStatus = isNormal
                    )
                )
            }
            addOnItemChildClickListener { adapter, v, position ->
                val item = mAdapterV1.data[position]
                when (v.id) {
                    R.id.tv_del -> {
                        DriverBlackRemoveActivityV1.jumpPage(<EMAIL>, item)
                    }

                    R.id.tv_check -> {
                        DriverBlackRecordListActivity.jumpPage(
                            context = <EMAIL>,
                            targetId = item.id,
                            targetType = DriverBlackRecordListActivity.TARGET_TYPE_1,
                        )
                    }
                }
            }
        }
        bindClickEvent(tvLimit)
        bindClickEvent(tvAdd)
        bindClickEvent(tvNormal)
        bindClickEvent(tvDelete)
        handOff()
    }

    override fun onSingleClick(v: View) {
        super.onSingleClick(v)
        when (v.id) {
            R.id.tvNormal -> {
                //正常
                isNormal = "0"
                handOff()
            }

            R.id.tvDelete -> {
                //移除
                isNormal = "1"
                handOff()
            }

            R.id.tvAdd -> {
                //添加承运人
                DriverBlackAddActivityV1.jumpPage(<EMAIL>)
            }

            R.id.tvLimit -> {
                when (tvLimit.text.toString()) {
                    "限制物流企业摘单" -> {
                        viewModel?.restrictOrRestoreLogisticsOrder(
                            state = "1"
                        )
                    }

                    "恢复物流企业摘单" -> {
                        viewModel?.restrictOrRestoreLogisticsOrder(
                            state = "0"
                        )
                    }
                }
            }
        }
    }

    private fun handOff() {
        if (isNormal.isTrue) {
            //当前移除状态
            tvNormal.isSelected = false
            tvNormal.setTextColor(Color.parseColor("#666666"))
            tvDelete.isSelected = true
            tvDelete.setTextColor(Color.parseColor("#5086FC"))
        } else {
            tvNormal.isSelected = true
            tvNormal.setTextColor(Color.parseColor("#5086FC"))
            tvDelete.isSelected = false
            tvDelete.setTextColor(Color.parseColor("#666666"))
        }
        swipe_refresh.onAutoRefresh()
    }

    @LiveDataMatch
    open fun restrictOrRestoreLogisticsOrderSuccess() {
        viewModel?.queryLogisticsOrderState()
    }

    @LiveDataMatch
    open fun queryLogisticsOrderStateSuccess(data: RspQueryLogisticsOrderState) {
        when (data.state) {
            "1" -> {
                //状态为限制
                tvLimit.text = "恢复物流企业摘单"
            }

            else -> {
                //状态为恢复
                tvLimit.text = "限制物流企业摘单"
            }
        }
    }

    @LiveDataMatch
    open fun onRefresh() {
        //删除成功，添加成功
        swipe_refresh.onAutoRefresh()
    }

    @RxBusEvent(from = "添加黑名单成功")
    open fun onAddSuccess(data: RxAddRelationVehicleBlack) {
        if (data.success) {
            swipe_refresh.onAutoRefresh()
        }
    }

    @LiveDataMatch
    open fun onPageSuccess(pageList: PageList<EDriverBlack>) {
        swipe_refresh.onRefreshCompale(pageList)
    }

    @LiveDataMatch
    open fun onPageError() {
        swipe_refresh.onLoadMoreFail()
    }
}
