package com.zczy.cargo_owner.user.login.activity

import android.content.Context
import android.content.Intent
import android.os.Bundle
import android.view.View
import android.widget.TextView
import com.sfh.lib.mvvm.service.BaseViewModel
import com.sfh.lib.ui.AbstractLifecycleActivity
import com.zczy.cargo_owner.R

/**
 * @description 登录报错提示框
 * <AUTHOR> 李罡喆
 * @company 中储智运科技股份有限公司
 * @copyright （版权）中储南京智慧物流科技有限公司所有
 * @since 2020/5/14
 */
class UserLoginOtherErrorDialogActivity : AbstractLifecycleActivity<BaseViewModel>(), View.OnClickListener {

    private val tvOK by lazy { findViewById<TextView>(R.id.tv_ok) }
    private val tvContent by lazy { findViewById<TextView>(R.id.tv_content) }

    companion object {
        @JvmStatic
        fun startContentUI(context: Context, msg: String?) {
            val intent = Intent(context, UserLoginOtherErrorDialogActivity::class.java)
            intent.putExtra("msg", msg)
            context.startActivity(intent)
        }
    }

    override fun onCreate(savedInstanceState: Bundle?) {
        super.onCreate(savedInstanceState)
        setContentView(R.layout.user_login_other_error_dialog_activity)
        val msg = intent.getStringExtra("msg") ?: ""
        tvContent.text = msg
        initView()
    }

    private fun initView() {
        tvOK.setOnClickListener(this)
    }

    override fun onClick(v: View) {
        when (v.id) {
            R.id.tv_ok -> {
                finish()
            }
        }
    }
}