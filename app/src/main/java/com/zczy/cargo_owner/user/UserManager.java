package com.zczy.cargo_owner.user;

import com.sfh.lib.AppCacheManager;
import com.sfh.lib.ui.AbstractLifecycleActivity;
import com.sfh.lib.ui.dialog.DialogBuilder;
import com.zczy.cargo_owner.libcomm.config.ConstConfig;
import com.zczy.cargo_owner.user.login.activity.LoginAccountActivity;
import com.zczy.comm.CommServer;

public class UserManager {
    public static void isLogin(AbstractLifecycleActivity activity, CheckCallback checkCallback) {
        boolean isLogin = CommServer.getUserServer().isLogin();
        if (isLogin) {
            checkCallback.result(true);
        } else {
            DialogBuilder dialogBuilder = new DialogBuilder();
            dialogBuilder.setHideCancel(true);
            dialogBuilder.setMessage("请登录");
            dialogBuilder.setOkListener(new DialogBuilder.DialogInterface.OnClickListener() {
                @Override
                public void onClick(DialogBuilder.DialogInterface dialog, int which) {
                    LoginAccountActivity.start(activity);
                    dialog.dismiss();
                }
            });
            activity.showDialog(dialogBuilder);
        }

    }

    public static boolean isLogin() {
        return CommServer.getUserServer().isLogin();
    }

    public static void loginOut() {
        AppCacheManager.removeCache(ConstConfig.LOGIN_KEY);
    }

    public interface CheckCallback {
        void result(boolean isLogin);
    }
}
