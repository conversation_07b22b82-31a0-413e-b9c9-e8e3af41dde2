package com.zczy.cargo_owner.user.exception.req;

import com.zczy.cargo_owner.user.exception.bean.WaybillDetails;
import com.zczy.comm.http.entity.BaseNewRequest;
import com.zczy.comm.http.entity.BaseRsp;

/**
 * 功能描述:
 *
 * <AUTHOR>
 * @company 中储智运科技股份有限公司
 * @copyright （版权）中储南京智慧物流科技有限公司所有
 * @date 2019/2/25
 */
public class ReqWayDetails extends BaseNewRequest<BaseRsp<WaybillDetails>> {


    public ReqWayDetails() {
        super("oms-app/order/consignor/exception/exceptionOrderDetail");
    }


    //1：待处理2：已处理

    private String id;

    public String getId() {
        return id;
    }

    public void setId(String id) {
        this.id = id;
    }
}

