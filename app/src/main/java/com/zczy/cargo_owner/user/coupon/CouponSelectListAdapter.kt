package com.zczy.cargo_owner.user.coupon

import android.content.Context
import android.graphics.Color
import androidx.constraintlayout.widget.ConstraintLayout
import android.text.TextUtils
import android.util.SparseArray
import android.widget.ImageView
import android.widget.Toast
import com.chad.library.adapter.base.BaseQuickAdapter
import com.chad.library.adapter.base.BaseViewHolder
import com.zczy.cargo_owner.R
import com.zczy.cargo_owner.user.coupon.req.RspUserCoupon


/**
 * PS: 选择优惠卷 适配器
 */
class CouponSelectListAdapter : BaseQuickAdapter<RspUserCoupon, BaseViewHolder>(R.layout.user_coupon_main_select_item) {

    var select : RspUserCoupon? = null

    var color_5086FC = Color.parseColor("#5086FC")
    override fun convert(helper: BaseViewHolder, item: RspUserCoupon) {
        // 未使用
        helper.setText(R.id.tv_coupon_type_name, item.formatCouponTypeName())   // 优惠券类型名称
        helper.setText(R.id.tv_validity_time, item.validityTime) // 优惠券有效期
        helper.setText(R.id.tv_coupon_description, item.instructions)  //使用说明栏

        //使用说明栏,设置不同使用背景图片
        val imageView = helper.getView<ImageView>(R.id.iv_coupon_bg)
        val layoutParam = ConstraintLayout.LayoutParams(imageView.layoutParams as ConstraintLayout.LayoutParams)
        if (!TextUtils.isEmpty(item.instructions)) {
            helper.setGone(R.id.cl_coupon_bottom, true)
            layoutParam.dimensionRatio = "H,345:120" //设置背景宽高比
        } else {
            helper.setGone(R.id.cl_coupon_bottom, false)
            layoutParam.dimensionRatio = "H,345:100"
        }
        imageView.layoutParams = layoutParam


        // 优惠券类型ID 1 增值卷 2 抵用卷 3 任务类型-满赠卷 4 任务类型-满减卷
        when (item.couponTypeId) {
            // 增值卷 任务类型-满赠卷 任务类型-满减卷
            "1", "3", "4" -> {
                helper
                    // 优惠券面额
                    .setText(R.id.tv_coupon_money, item.formatCouponMoney())
                    // 满多少可用
                    .setText(R.id.tv_min_money, item.formatMinMoney())
            }
            // 抵用卷
            "2" -> {
                helper
                    // 优惠券面额
                    .setText(R.id.tv_coupon_money, item.formatDiscountRatio())
                    // 满多少可用
                    .setText(R.id.tv_min_money, item.discountMoneyTopName)
            }

            else -> {
                helper
                    // 优惠券面额
                    .setText(R.id.tv_coupon_money, item.formatCouponMoney())
                    // 满多少可用
                    .setText(R.id.tv_min_money, item.formatMinMoney())
            }
        }

        helper.setTextColor(R.id.tv_coupon_money, color_5086FC)
        helper.setTextColor(R.id.tv_coupon_type_name, color_5086FC)

        helper.setImageResource(
            R.id.iv_coupon_bg,
            if (!TextUtils.isEmpty(item.instructions)) R.drawable.order_coupon_main_item_unused_big_bg else R.drawable.order_coupon_main_item_unused_bg
        )

        //选择
        val ivSelect = helper.getView<ImageView>(R.id.iv_select)
        ivSelect.isSelected =  select != null && TextUtils.equals(select?.userCouponId,item.userCouponId)
    }


    fun defealtSelect(data:RspUserCoupon){
        this.select = data;
    }

    fun select(context: Context, position: Int) {
        val data = data[position]
        if (select != null && TextUtils.equals(select?.userCouponId,data.userCouponId)){
            select = null;
        }else{
            select = data;
        }
        notifyDataSetChanged()
    }

    fun getSelectToList(): ArrayList<RspUserCoupon> {
        val list = ArrayList<RspUserCoupon>()
        select?.let {
            list.add(it)
        }
        return list
    }
}