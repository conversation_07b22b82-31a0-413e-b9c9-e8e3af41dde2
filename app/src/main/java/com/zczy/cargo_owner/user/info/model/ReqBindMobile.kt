package com.zczy.cargo_owner.user.info.model

import com.zczy.comm.http.entity.BaseNewRequest
import com.zczy.comm.http.entity.BaseRsp
import com.zczy.comm.http.entity.ResultData

/**
 *@Desc 绑定手机号
 *@User ssp
 *@Date 2023/6/5-13:38
 */
class ReqBindMobile(
    var verifyCode: String = "", //验证码
    var mobile: String = "", // 手机号
    var verifyCodeType: String = "", // *短信验证码类型 1.短信 2.语音
    var moduleType: String = "", //
) : BaseNewRequest<BaseRsp<ResultData>>("mms-app/mms/upgrade/bindMobile")