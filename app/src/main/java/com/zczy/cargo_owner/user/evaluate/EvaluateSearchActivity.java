package com.zczy.cargo_owner.user.evaluate;

import android.content.Context;
import android.content.Intent;
import android.graphics.Color;
import android.os.Bundle;

import androidx.annotation.Nullable;

import android.text.TextUtils;
import android.view.View;

import com.chad.library.adapter.base.BaseQuickAdapter;
import com.chad.library.adapter.base.listener.OnItemClickListener;
import com.sfh.lib.mvvm.annotation.LiveDataMatch;
import com.sfh.lib.ui.AbstractLifecycleActivity;
import com.zczy.cargo_owner.R;
import com.zczy.cargo_owner.user.evaluate.adapter.EvaluateAdapter;
import com.zczy.cargo_owner.user.evaluate.wight.SearchLayout;
import com.zczy.comm.http.entity.PageList;
import com.zczy.comm.ui.UtilStatus;
import com.zczy.comm.utils.ResUtil;
import com.zczy.comm.widget.pulltorefresh.CommEmptyView;
import com.zczy.comm.widget.pulltorefresh.OnLoadingListener;
import com.zczy.comm.widget.pulltorefresh.SwipeRefreshMoreLayout;
import com.zczy.cargo_owner.user.evaluate.bean.EvaluationBean;
import com.zczy.cargo_owner.user.evaluate.model.EvaluateModel;
import com.zczy.cargo_owner.user.evaluate.req.ReqEvaluationList;

/**
 * 功能描述: 评价管理搜索
 *
 * <AUTHOR>
 * @company 中储智运科技股份有限公司
 * @copyright （版权）中储南京智慧物流科技有限公司所有
 * @date 2019/3/5
 */
public class EvaluateSearchActivity extends AbstractLifecycleActivity<EvaluateModel> implements OnLoadingListener {

    private SearchLayout mSearchLayout;
    private SwipeRefreshMoreLayout mSwipeRefreshMoreLayout;
    private String mSearchContent;

    public static void start(Context context) {
        Intent intent = new Intent(context, EvaluateSearchActivity.class);
        context.startActivity(intent);
    }

    @Override
    protected void onCreate(@Nullable Bundle savedInstanceState) {
        super.onCreate(savedInstanceState);
        setContentView(R.layout.evaluate_search_activity);
        UtilStatus.initStatus(this, Color.WHITE);
        initView();
        initSearch();

    }

    private void initView() {
        mSearchLayout = (SearchLayout) findViewById(R.id.search_layout);
        mSwipeRefreshMoreLayout = (SwipeRefreshMoreLayout) findViewById(R.id.swipe_refresh_more_layout);
        EvaluateAdapter evaluateAdapter = new EvaluateAdapter();
        mSwipeRefreshMoreLayout.setAdapter(evaluateAdapter, true);
        mSwipeRefreshMoreLayout.addItemDecorationSize(ResUtil.dp2px(7));
        View emptyView = CommEmptyView.creator(this, R.drawable.evalutate_no_information, "没有找到相关信息");
        mSwipeRefreshMoreLayout.setEmptyView(emptyView);
        mSwipeRefreshMoreLayout.setOnLoadListener(this);
        mSwipeRefreshMoreLayout.addOnItemListener(new OnItemClickListener() {
            @Override
            public void onSimpleItemClick(BaseQuickAdapter adapter, View view, int position) {
            }

            @Override
            public void onItemChildClick(BaseQuickAdapter adapter, View view, int position) {
                super.onItemChildClick(adapter, view, position);
                if (view.getId() == R.id.tv_evalutate) {
                    EvaluationBean evaluationBean = (EvaluationBean) adapter.getItem(position);
                    String carrierHaveEvaluate = evaluationBean.getConsignorHaveEvaluate();
                    if (TextUtils.equals(carrierHaveEvaluate, "0")) {
                        // 去评价
                        EvaluateActivityV1.start(EvaluateSearchActivity.this, evaluationBean.getOrderId());
                    } else if (TextUtils.equals(carrierHaveEvaluate, "1")) {
                        // 评价详情
                        EvaluateDetailActivityV1.start(EvaluateSearchActivity.this, evaluationBean.getOrderId());
                    }
                }
            }
        });
    }

    private void initSearch() {
        mSearchLayout = findViewById(R.id.search_layout);
        mSearchLayout.setSearchEtHintText("货物名称／启运地／目的地");
        mSearchLayout.setOnSearchListener(searchListener);
    }

    private SearchLayout.SeachContentListener searchListener = new SearchLayout.SeachContentListener() {
        @Override
        public void doSearchListener(String searchContent) {
            mSearchContent = searchContent;
            mSwipeRefreshMoreLayout.onAutoRefresh();
        }

        @Override
        public void doCancelSearch() {
            finish();
        }
    };

    @Override
    public void onRefreshUI(int nowPage) {
        ReqEvaluationList evaluationList = new ReqEvaluationList();
        evaluationList.setNowPage(String.valueOf(nowPage));
        evaluationList.setPageSize("10");
        evaluationList.setRemark(mSearchContent);
        getViewModel().getEvaluateList(evaluationList);
    }

    @Override
    public void onLoadMoreUI(int nowPage) {
        ReqEvaluationList evaluationList = new ReqEvaluationList();
        evaluationList.setNowPage(String.valueOf(nowPage));
        evaluationList.setPageSize("10");
        evaluationList.setRemark(mSearchContent);
        getViewModel().getEvaluateList(evaluationList);
    }

    @LiveDataMatch
    public void getEvaluateListSuccess(PageList<EvaluationBean> data) {
        if (data == null) {
            mSwipeRefreshMoreLayout.onLoadMoreFail();
        } else {
            mSwipeRefreshMoreLayout.onRefreshCompale(data);
        }
    }
}

