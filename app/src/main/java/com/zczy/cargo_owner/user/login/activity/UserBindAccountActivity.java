package com.zczy.cargo_owner.user.login.activity;

import android.content.Context;
import android.content.DialogInterface;
import android.content.Intent;
import android.graphics.Color;
import android.os.Bundle;
import android.text.Editable;
import android.text.TextUtils;
import android.text.TextWatcher;
import android.util.SparseArray;
import android.view.KeyEvent;
import android.view.View;
import android.widget.Button;
import android.widget.EditText;
import android.widget.LinearLayout;

import com.jakewharton.rxbinding2.view.RxView;
import com.sfh.lib.AppCacheManager;
import com.sfh.lib.mvvm.annotation.LiveDataMatch;
import com.sfh.lib.ui.AbstractLifecycleActivity;
import com.sfh.lib.ui.dialog.DialogBuilder;
import com.sfh.lib.utils.UtilSoftKeyboard;
//import com.wbtech.ums.UmsAgent;
import com.zczy.MainPluginServer;
import com.zczy.cargo_owner.R;
import com.zczy.cargo_owner.home.HomeActivity;
import com.zczy.cargo_owner.libcomm.config.HttpConfig;
import com.zczy.cargo_owner.user.certification.CertificationUtils;
import com.zczy.cargo_owner.user.login.authenticationdialog.AuthenticationDialog;
import com.zczy.cargo_owner.user.login.mode.BindAccountModel;
import com.zczy.cargo_owner.user.login.mode.LoginAccountModel;
import com.zczy.cargo_owner.user.login.mode.LoginCodeModel;
import com.zczy.cargo_owner.user.login.request.ReqLogin;
import com.zczy.cargo_owner.user.login.request.ReqWxLoginStatus;
import com.zczy.cargo_owner.user.setting.UserEditPasswordctivity;
import com.zczy.cargo_owner.wight.AgreementView;
import com.zczy.comm.CommServer;

import com.zczy.comm.ZczyApplication;
import com.zczy.comm.data.entity.ELogin;
import com.zczy.comm.data.request.ReqSendCode;
import com.zczy.comm.ui.UtilStatus;
import com.zczy.comm.utils.VerificationCodeUtil;
import com.zczy.comm.widget.AppToolber;
import com.zczy.comm.widget.ImageCodeDialog;
import com.zczy.comm.widget.RxTimeCountView;
import com.zczy.comm.x5.X5WebActivity;
import com.zczy.cargo_owner.user.login.entity.WxLoginStatus;

import java.util.concurrent.TimeUnit;

import io.reactivex.disposables.Disposable;
import io.reactivex.functions.Consumer;

/**
 * 注册1
 *
 * <AUTHOR>
 */
public class UserBindAccountActivity extends AbstractLifecycleActivity<BindAccountModel> implements TextWatcher {


    public static void start(Context context, String openId, String mobile) {
        Intent intent = new Intent(context, UserBindAccountActivity.class);
        intent.putExtra("openId", openId);
        intent.putExtra("mobile", mobile);
        context.startActivity(intent);
    }

    private EditText etPhone;
    private Button btNext;
    //验证码
    private EditText etCode;
    private RxTimeCountView tvTime;
    private LinearLayout lyVoice;
    private RxTimeCountView tvVoiceTime;
    private VerificationCodeUtil util;
    private String openId;
    private AgreementView agreementView;
    @Override
    protected void onCreate(Bundle savedInstanceState) {
        super.onCreate(savedInstanceState);
        setContentView(R.layout.user_bind_account_activity);
        UtilStatus.initStatus(this, Color.WHITE);
        etPhone = findViewById(R.id.etPhone);
        btNext = findViewById(R.id.btNext);
        etCode = findViewById(R.id.etCode);
        tvTime = findViewById(R.id.tvTime);
        lyVoice = findViewById(R.id.lyVoice);
        tvVoiceTime = findViewById(R.id.tvVoiceTime);
        etPhone.addTextChangedListener(this);
        etCode.addTextChangedListener(this);
        agreementView = findViewById(R.id.agreementView);

        this.setListener();
        openId = getIntent().getStringExtra("openId");
        String mobile = getIntent().getStringExtra("mobile");
        if (!TextUtils.isEmpty(mobile)) {
            etPhone.setText(mobile);
            etPhone.setEnabled(false);
        }
        AppToolber appToolber = findViewById(R.id.appToolber);
        appToolber.setRightOnClickListener((View v) -> {
            new MainPluginServer().openLogin(UserBindAccountActivity.this);
            finish();
        });

        SparseArray<AgreementView.AgreementTxt> agreementTxtSparseArray = new SparseArray<>();
        agreementTxtSparseArray.put("中储智运平台服务协议".hashCode(), new AgreementView.AgreementTxt("中储智运平台服务协议", HttpConfig.getWebUrl("/form_h5/documents/app_documents/app_consignor/service.html?sceneCode=133&plateType=1&time="+System.currentTimeMillis())));
        agreementTxtSparseArray.put("中储智运网络货运平台交易规则".hashCode(), new AgreementView.AgreementTxt("中储智运网络货运平台交易规则", HttpConfig.getWebUrl("/form_h5/documents/app_documents/app_consignor/tradeRule.html?sceneCode=133&plateType=2&time="+System.currentTimeMillis())));
        agreementTxtSparseArray.put("隐私政策".hashCode(), new AgreementView.AgreementTxt("隐私政策", HttpConfig.getWebUrl("/form_h5/documents/app_documents/app_consignor/privacy.html?sceneCode=133&plateType=4&time="+System.currentTimeMillis())));
        agreementTxtSparseArray.put("用户授权协议".hashCode(), new AgreementView.AgreementTxt("用户授权协议", HttpConfig.getWebUrl("/form_h5/documents/app_documents/app_consignor/authorization.html?sceneCode=133&plateType=3&time="+System.currentTimeMillis())));

        agreementView.newData(agreementTxtSparseArray);
    }

    private void setListener() {
        util = new VerificationCodeUtil(ReqSendCode.MODULE_TYPE_A, new VerificationCodeUtil.IOnCallback() {
            @Override
            public String getPhone() {
                return etPhone.getText().toString();
            }

            @Override
            public void onClickCode(ReqSendCode req) {
                ReqWxLoginStatus reqWxLoginStatus = new ReqWxLoginStatus(openId, etPhone.getText().toString().trim());
                getViewModel(BindAccountModel.class).getWxLoginStatus(reqWxLoginStatus);
            }

            @Override
            public void showToast(CharSequence toast) {

                showDialogToast(toast);
            }
        }).build(tvTime, tvVoiceTime, lyVoice);


        Disposable cilck = RxView.clicks(btNext).throttleFirst(1, TimeUnit.SECONDS).subscribe(new Consumer<Object>() {
            @Override
            public void accept(Object o) throws Exception {
                //下一步
                String account = etPhone.getText().toString();
                String pwd = etCode.getText().toString();

                if (pwd.length() < 6) {
                    showDialogToast("请输入6位验证码");
                    return;
                }
                if (!agreementView.getCheckBox().isChecked()) {
                    showToast("请先勾选同意后再绑定！");
                    return;
                }
                UtilSoftKeyboard.hide(etPhone);
                ReqLogin.CodeBuilder builder = new ReqLogin.CodeBuilder();
                builder.setLoginName(account);
                builder.setLoginCode(pwd);
                builder.setLoginWx("1");// 微信登录传1
                //验证码类型
                builder.setCodeType(true);
                getViewModel(BindAccountModel.class).login(builder.bulder());
            }
        });
        this.putDisposable(cilck);
    }

    @LiveDataMatch(tag = "微信授权成功")
    public void onWxLoginStatus(WxLoginStatus status) {
        //0:未注册，1：注册可以自己登录，2：未解绑；3：号码已存在角色不正确
        String code = status.getCode();
        if (TextUtils.equals(code, "0")) {
            DialogBuilder builder = new DialogBuilder();
            builder.setTitle("提示");
            builder.setCancelText("取消");
            builder.setOKText("去注册");
            builder.setMessage(status.getResultMsg());
            builder.setOkListener((dialog, which) -> {
                RegisterStep1Activity.start(this, etPhone.getText().toString().trim());
                dialog.dismiss();
            });
            showDialog(builder);
        } else if (TextUtils.equals(code, "2") || TextUtils.equals(code, "3")) {
            showToast(status.getResultMsg());
        } else if (TextUtils.equals(code, "1")) {
            this.util.onSendCodeResult(true, ReqSendCode.TYPE_SMS);
        }
    }

    @LiveDataMatch(tag = "发送验证码结果")
    public void onSendCodeRelust(boolean success, String type) {
        this.util.onSendCodeResult(success, type);
    }

    @LiveDataMatch(tag = " 验证验证码")
    public void onCheckVerifyCodeSuccess() {
        String phone = etPhone.getText().toString();
        String code = etCode.getText().toString();
        finish();
    }

    @LiveDataMatch(tag = "显示图片验证码")
    public void onShowImageVerifyCode(final ReqSendCode req) {
        final String phone = etPhone.getText().toString();
        new ImageCodeDialog(this, phone, new ImageCodeDialog.CodeCallback() {
            @Override
            public void onClickCode(ImageCodeDialog dialog, String code) {
                req.setImageCode(code);
                getViewModel(BindAccountModel.class).sendVerifyCode(req);
            }
        }).show();
    }

    @Override
    public void beforeTextChanged(CharSequence s, int start, int count, int after) {

    }

    @Override
    public void onTextChanged(CharSequence s, int start, int before, int count) {

    }

    @Override
    public void afterTextChanged(Editable s) {
        if (s == this.etPhone.getText()) {
            String phone = s.toString();
            if (!TextUtils.isEmpty(phone) && phone.length() >= 11) {
                String code = etCode.getText().toString();
                this.etCode.requestFocus();
                this.etCode.setSelection(TextUtils.isEmpty(code) ? 0 : code.length() - 1);
                this.util.setAbled(true);
            } else {
                this.util.setAbled(false);
            }
        }

        String phone = this.etPhone.getText().toString();
        String code = this.etCode.getText().toString();
        if (TextUtils.isEmpty(phone) || TextUtils.isEmpty(code)) {
            this.btNext.setEnabled(false);
        } else {
            this.btNext.setEnabled(true);
        }
    }

    @LiveDataMatch(tag = "登录成功 or 新设备认证提示")
    public void onLoginSuccess(ELogin login) {
        AppCacheManager.putCache("login_name_phone", etPhone.getText().toString());
        if (TextUtils.equals("1",login.getWheterChangePassword())){
            UserEditPasswordctivity.startUI(this);
        }else{
            HomeActivity.start(this);
        }
        this.finish();
    }


    AuthenticationDialog authenticationDialog;

    @LiveDataMatch(tag = "新设备认证短信发送成功")
    public void onSendAuthenticationCodeSuccess(ELogin login) {
        //新设备认证提示
        if (this.authenticationDialog == null) {
            this.authenticationDialog = new AuthenticationDialog(this).setListener(new AuthenticationDialog.Listener() {
                @Override
                public void onReSendCode(ELogin data) {
                    // 重新发送验证码
                    getViewModel(LoginAccountModel.class).sendAuthenticationSMS(data);
                }

                @Override
                public void onCommit(ELogin data, String code) {
                    // 认证
                    getViewModel(LoginAccountModel.class).checkAuthenticationCode(data, code);
                }
            });
        }
        this.authenticationDialog.setLogin(login);
        if (!this.authenticationDialog.isShowing()) {
            this.authenticationDialog.show();
        }
    }

    @LiveDataMatch(tag = "登录ZCZY-10873 供应链会员中台-用户部分")
    public void onLoginSuccessToast(ELogin login){
        new SelectPersonDialog(this, login.getNotUserTypeMsg(), new DialogInterface.OnClickListener() {
            @Override
            public void onClick(DialogInterface dialog, int which) {
                dialog.dismiss();
                if (which == 1){
                    //确认认证
                    getViewModel(BindAccountModel.class).switchUserType();
                }else {
                    //退出登录
                    finish();
                    ZczyApplication application = AppCacheManager.getApplication();
                    application.onLoseToken("","");
                }
            }
        }).show();

    }


    @LiveDataMatch
    public void onSwitchUserTypeSuccess(){
        //确认认证
        HomeActivity.start(this);
        CertificationUtils.hzCertification(UserBindAccountActivity.this);
        finish();
    }
    @LiveDataMatch
    public void loginError(String msg) {
        UserLoginOtherErrorDialogActivity.startContentUI(this, msg);
    }
}
