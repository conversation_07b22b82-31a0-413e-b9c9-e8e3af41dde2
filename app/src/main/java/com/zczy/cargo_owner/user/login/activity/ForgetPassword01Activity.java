package com.zczy.cargo_owner.user.login.activity;

import android.content.Context;
import android.content.Intent;
import android.net.Uri;
import android.os.Bundle;
import androidx.annotation.Nullable;
import android.text.Editable;
import android.text.TextUtils;
import android.text.TextWatcher;
import android.view.KeyEvent;
import android.view.View;
import android.widget.Button;
import android.widget.LinearLayout;

import com.sfh.lib.mvvm.annotation.LiveDataMatch;
import com.sfh.lib.ui.dialog.DialogBuilder;
import com.zczy.cargo_owner.R;
import com.zczy.cargo_owner.user.login.mode.ForgetModel;
import com.zczy.comm.Const;
import com.zczy.comm.data.request.ReqCheckCode;
import com.zczy.comm.data.request.ReqSendCode;
import com.zczy.comm.permission.PermissionCallBack;
import com.zczy.comm.permission.PermissionUtil;
import com.zczy.comm.ui.BaseActivity;
import com.zczy.comm.utils.PhoneUtil;
import com.zczy.comm.utils.VerificationCodeUtil;
import com.zczy.comm.widget.AppToolber;
import com.zczy.comm.widget.EditTextCloseView;
import com.zczy.comm.widget.ImageCodeDialog;
import com.zczy.comm.widget.RxTimeCountView;

public class ForgetPassword01Activity extends BaseActivity<ForgetModel> implements View.OnClickListener, TextWatcher {

    /**
     * 请输入您的手机号码
     */
    private EditTextCloseView etPhone;
    /**
     * 请输入短信验证码
     */
    private EditTextCloseView etCode;
    /**
     * 获取验证码
     */
    private RxTimeCountView tvTime;
    /**
     * 试试语音验证
     */
    private RxTimeCountView tvVoiceTime;
    private LinearLayout llVoice;
    /**
     * 确认提交
     */
    private Button btNext;
    private AppToolber mAppToolber;

    private VerificationCodeUtil util;

    private String codeType = ReqSendCode.TYPE_SMS;


    public static void start(Context context, String phone,boolean switchAccount) {
        Intent intent = new Intent(context, ForgetPassword01Activity.class);
        intent.putExtra("phone", phone);
        intent.putExtra("switchAccount", switchAccount);
        context.startActivity(intent);
    }

    @Override
    protected int getLayout() {
        return R.layout.user_forget_activity;
    }

    @Override
    protected void bindView(@Nullable Bundle bundle) {
        etPhone = findViewById(R.id.etPhone);
        etCode = findViewById(R.id.etCode);
        tvTime = findViewById(R.id.tvTime);
        tvVoiceTime = findViewById(R.id.tvVoiceTime);
        llVoice = findViewById(R.id.llVoice);
        btNext = findViewById(R.id.btNext);
        mAppToolber = findViewById(R.id.appToolber);
        findViewById(R.id.iv_clickphone).setOnClickListener(new View.OnClickListener() {
            @Override
            public void onClick(View v) {

                PhoneUtil.callPhone(ForgetPassword01Activity.this, Const.PHONE_SERVER_400);

            }
        });


        btNext.setOnClickListener(this);

        etPhone.addTextChangedListener(this);
        etCode.addTextChangedListener(this);

        util = new VerificationCodeUtil(ReqSendCode.MODULE_TYPE_C, new VerificationCodeUtil.IOnCallback() {
            @Override
            public String getPhone() {
                return etPhone.getText().toString();
            }

            @Override
            public void onClickCode(ReqSendCode req) {
                codeType = req.getType();
                getViewModel(ForgetModel.class).showImageVerifyCode(req);
            }

            @Override
            public void showToast(CharSequence toast) {

            }
        }).build(tvTime, tvVoiceTime, llVoice);

        mAppToolber.setLeftOnClickListener(this);
        etPhone.setText(getIntent().getStringExtra("phone"));
    }

    @Override
    protected void initData() {

    }

    @Override
    public boolean onKeyDown(int keyCode, KeyEvent event) {
        if (keyCode == KeyEvent.KEYCODE_BACK) {
            DialogBuilder dialogBuilder = new DialogBuilder();
            dialogBuilder.setTitle("提示");
            dialogBuilder.setMessage("确定放弃找回密码？");
            dialogBuilder.setOkListener(new DialogBuilder.DialogInterface.OnClickListener() {
                @Override
                public void onClick(DialogBuilder.DialogInterface dialogInterface, int i) {
                    dialogInterface.dismiss();
                    finish();
                }
            });
            showDialog(dialogBuilder);
            return true;
        }
        return super.onKeyDown(keyCode, event);
    }

    @Override
    public void onClick(View v) {
        if (v == mAppToolber.getTvLeft()) {
            DialogBuilder dialogBuilder = new DialogBuilder();
            dialogBuilder.setTitle("提示");
            dialogBuilder.setMessage("确定放弃找回密码？");
            dialogBuilder.setOkListener(new DialogBuilder.DialogInterface.OnClickListener() {
                @Override
                public void onClick(DialogBuilder.DialogInterface dialogInterface, int i) {
                    dialogInterface.dismiss();
                    finish();
                }
            });
            showDialog(dialogBuilder);
        } else if (v == btNext) {
            String phone = etPhone.getText().toString();
            String code = etCode.getText().toString();
            if (TextUtils.isEmpty(phone)) {
                showToast("请输入手机号码");
                return;
            }
            if (TextUtils.isEmpty(code)) {
                showToast("请输入验证码");
                return;
            }

            ReqCheckCode reqCheckCode = new ReqCheckCode();
            reqCheckCode.setMobile(phone);
            reqCheckCode.setModuleType(ReqCheckCode.MODULE_TYPE_C);
            reqCheckCode.setVerifyCode(code);
            reqCheckCode.setVerifyCodeType(codeType);
            getViewModel(ForgetModel.class).checkVerifyCode(reqCheckCode);

        }

    }

    @LiveDataMatch
    public void onCheckSuccess() {
        String phone = etPhone.getText().toString();
        String code = etCode.getText().toString();
        boolean switchAccount = getIntent().getBooleanExtra("switchAccount", false);
        ForgetPassword02Activity.startUI(this, phone, code, codeType,switchAccount);
        finish();
    }


    @Override
    public void beforeTextChanged(CharSequence s, int start, int count, int after) {

    }

    @Override
    public void onTextChanged(CharSequence s, int start, int before, int count) {

    }

    @Override
    public void afterTextChanged(Editable s) {

        if (s == this.etPhone.getText()) {
            String phone = s.toString();
            if (!TextUtils.isEmpty(phone) && phone.length() >= 11) {
                String code = etCode.getText().toString();
                this.etCode.requestFocus();
                this.etCode.setSelection(TextUtils.isEmpty(code) ? 0 : code.length());
                this.util.setAbled(true);
            } else {
                this.util.setAbled(false);
            }
        }

        String phone = this.etPhone.getText().toString();
        String code = this.etCode.getText().toString();
        if (TextUtils.isEmpty(phone) || TextUtils.isEmpty(code)) {
            this.btNext.setEnabled(false);
        } else {
            this.btNext.setEnabled(true);
        }
    }

    @LiveDataMatch(tag = "显示图片验证码")
    public void onShowImageVerifyCode(ReqSendCode req) {
        final String phone = etPhone.getText().toString();
        new ImageCodeDialog(this, phone, (ImageCodeDialog dialog, String code) -> {
            req.setImageCode(code);
            getViewModel(ForgetModel.class).sendVerifyCode(req);
        }).show();
    }


    @LiveDataMatch
    public void onSendCode(boolean success, String type) {
        util.onSendCodeResult(success, type);
    }

    @LiveDataMatch
    public void onPhoneSuccess(String phone) {
        PhoneUtil.callPhone(ForgetPassword01Activity.this,phone);
//        PermissionUtil.call(this, new PermissionCallBack() {
//            @Override
//            public void onHasPermission() {
//                Intent intent = new Intent(Intent.ACTION_DIAL, Uri.parse("tel:" + phone));
//                intent.setFlags(Intent.FLAG_ACTIVITY_NEW_TASK);
//                startActivity(intent);
//            }
//        });
    }
}
