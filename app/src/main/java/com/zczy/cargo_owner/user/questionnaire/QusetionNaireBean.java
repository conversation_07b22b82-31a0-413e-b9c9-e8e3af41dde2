package com.zczy.cargo_owner.user.questionnaire;

import java.io.Serializable;

/**
 * 功能描述:
 *
 * <AUTHOR>
 * @company 中储智运科技股份有限公司
 * @copyright （版权）中储南京智慧物流科技有限公司所有
 * @date 2019/3/5
 */
public class QusetionNaireBean implements Serializable {


    /**
     * questionId：问卷ID
     * title：问卷名称
     * startTimeStr：问卷开始时间
     * endTimeStr：问卷结束时间
     * userState：参与状态 0-未参与 1-己参与
     */

    private long questionId;
    private String title;
    private String startTimeStr;
    private String endTimeStr;
    private String userState;

    private String activityId;
    private String activityName;
    private String activityIdName;
    private String startTime;
    private String endTime;
    private String url;

    public String getActivityId() {
        return activityId;
    }

    public void setActivityId(String activityId) {
        this.activityId = activityId;
    }

    public String getActivityName() {
        return activityName;
    }

    public void setActivityName(String activityName) {
        this.activityName = activityName;
    }

    public String getActivityIdName() {
        return activityIdName;
    }

    public void setActivityIdName(String activityIdName) {
        this.activityIdName = activityIdName;
    }

    public String getStartTime() {
        return startTime;
    }

    public void setStartTime(String startTime) {
        this.startTime = startTime;
    }

    public String getEndTime() {
        return endTime;
    }

    public void setEndTime(String endTime) {
        this.endTime = endTime;
    }

    public String getUrl() {
        return url+"&source=1";
    }

    public void setUrl(String url) {
        this.url = url;
    }

    public long getQuestionId() {
        return questionId;
    }

    public void setQuestionId(long questionId) {
        this.questionId = questionId;
    }

    public String getTitle() {
        return title;
    }

    public void setTitle(String title) {
        this.title = title;
    }

    public String getStartTimeStr() {
        return startTimeStr;
    }

    public void setStartTimeStr(String startTimeStr) {
        this.startTimeStr = startTimeStr;
    }

    public String getEndTimeStr() {
        return endTimeStr;
    }

    public void setEndTimeStr(String endTimeStr) {
        this.endTimeStr = endTimeStr;
    }

    public String getUserState() {
        return userState;
    }

    public void setUserState(String userState) {
        this.userState = userState;
    }
}

