package com.zczy.cargo_owner.user.info.model;

import com.zczy.comm.CommServer;
import com.zczy.comm.data.entity.ELogin;
import com.zczy.comm.http.entity.BaseNewRequest;
import com.zczy.comm.http.entity.BaseRsp;
import com.zczy.comm.http.entity.ResultData;

/**
 * 功能描述: web二维码扫码登录
 *
 * <AUTHOR> 孙飞虎
 * @company 中储智运科技股份有限公司
 * @copyright （版权）中储南京智慧物流科技有限公司所有
 * @date 2018/11/7
 */
public class ReqWebLogin extends BaseNewRequest<BaseRsp<ResultData>> {

    /**
     * 需要3.0通信保护协议加密
     */
    String ssoToken;

    /**
     * 1:货主；2：承运方
     */
    String appNameType = "1";

    String keyCode;

    public ReqWebLogin(String keyCode) {

        super ("mms-app/qrCodeController/toQrCodeIndex");
        this.keyCode = keyCode;
    }

    @Override
    public Object buildParam() {
        ELogin login = CommServer.getUserServer().getLogin();

        if (login != null) {
            // ssoTokenId 单点登陆tokenId
            ssoToken = login.getSsoTokenId();
        }
        return super.buildParam();
    }
}
