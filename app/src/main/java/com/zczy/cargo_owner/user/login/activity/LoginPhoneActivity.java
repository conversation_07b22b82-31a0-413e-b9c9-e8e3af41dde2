package com.zczy.cargo_owner.user.login.activity;

import android.content.Context;
import android.content.DialogInterface;
import android.content.Intent;
import android.os.Bundle;
import androidx.annotation.Nullable;
import android.text.Editable;
import android.text.TextUtils;
import android.text.TextWatcher;
import android.util.SparseArray;
import android.view.View;
import android.view.animation.Animation;
import android.view.animation.AnimationUtils;
import android.widget.Button;
import android.widget.CheckBox;
import android.widget.EditText;
import android.widget.ImageView;
import android.widget.LinearLayout;
import android.widget.TextView;

import com.jaeger.library.StatusBarUtil;
import com.jakewharton.rxbinding2.view.RxView;
import com.sfh.lib.AppCacheManager;
import com.sfh.lib.event.RxBusEvent;
import com.sfh.lib.mvvm.annotation.LiveDataMatch;
import com.sfh.lib.ui.dialog.DialogBuilder;
import com.sfh.lib.utils.UtilTool;
import com.zczy.cargo_owner.home.onlinecall.OnLineCallActivity;
import com.zczy.cargo_owner.libcomm.config.HttpConfig;
import com.zczy.cargo_owner.libcomm.widget.CheckSelfPermissionDialog;
import com.zczy.cargo_owner.user.certification.CertificationUtils;
import com.zczy.cargo_owner.user.setting.UserEditPasswordctivity;
import com.zczy.cargo_owner.wight.AgreementView;
import com.zczy.cargo_owner.wxapi.RxWXLoginResultData;
import com.zczy.cargo_owner.R;
import com.zczy.cargo_owner.home.HomeActivity;
import com.zczy.cargo_owner.user.login.authenticationdialog.AuthenticationDialog;
import com.zczy.cargo_owner.user.login.entity.WxAuthSession;
import com.zczy.cargo_owner.user.login.mode.LoginCodeModel;
import com.zczy.cargo_owner.user.login.request.ReqLogin;
import com.zczy.comm.Const;
import com.zczy.comm.ZczyApplication;
import com.zczy.comm.data.entity.ELogin;
import com.zczy.comm.data.request.ReqSendCode;
import com.zczy.comm.permission.PermissionCallBack;
import com.zczy.comm.ui.BaseActivity;
import com.zczy.comm.utils.VerificationCodeUtil;
import com.zczy.comm.widget.AppToolber;
import com.zczy.comm.widget.ImageCodeDialog;
import com.zczy.comm.widget.RxTimeCountView;
import com.zczy.comm.x5.X5WebActivity;

import java.util.concurrent.TimeUnit;

import io.reactivex.disposables.Disposable;

public class LoginPhoneActivity extends BaseActivity<LoginCodeModel> implements TextWatcher {

    private EditText etAccount;
    private EditText etPwd;
    private Button btLogin;
    private RxTimeCountView tvTime;
    private LinearLayout lyVoice;
    private RxTimeCountView tvVoiceTime;
    private VerificationCodeUtil codeUtil;
    private TextView tvVersion;
    private ImageView tvCall;
    private AppToolber appToolber;
    private String mCodeType = ReqSendCode.TYPE_SMS;
    private AgreementView agreementView;

    public static void start(Context context) {
        Intent intent = new Intent(context, LoginPhoneActivity.class);
        context.startActivity(intent);
    }

    public static void start(Context context, String phone) {
        Intent intent = new Intent(context, LoginPhoneActivity.class);
        intent.putExtra("phone", phone);
        context.startActivity(intent);
    }

    @Override
    protected int getLayout() {
        return R.layout.user_login_phone_activity;
    }

    @Override
    protected void initStatus() {
        View cl_top = findViewById(R.id.cl_top);
        StatusBarUtil.setTranslucentForImageViewInFragment(this, 0, cl_top);
    }

    @Override
    protected void bindView(@Nullable Bundle bundle) {


        agreementView = findViewById(R.id.agreementView);
        this.etAccount = this.findViewById(R.id.etAccount);
        this.etPwd = this.findViewById(R.id.etPwd);
        this.btLogin = this.findViewById(R.id.btLogin);
        this.tvTime = this.findViewById(R.id.tvTime);
        this.lyVoice = this.findViewById(R.id.lyVoice);
        this.tvVoiceTime = this.findViewById(R.id.tvVoiceTime);
        tvVersion = findViewById(R.id.tv_version);
        tvCall = findViewById(R.id.tv_call);
        this.appToolber = findViewById(R.id.appToolber);
        this.appToolber.setRightOnClickListener((View v) -> {
            RegisterStep1Activity.start(LoginPhoneActivity.this, "", etAccount.getText().toString());
        });
        findViewById(R.id.tv_regiest).setOnClickListener(v -> {
            //注册
            RegisterStep1Activity.start(LoginPhoneActivity.this, etAccount.getText().toString());
        });
        findViewById(R.id.tvChangeLogin).setOnClickListener(v -> {
            LoginAccountActivity.start(LoginPhoneActivity.this);
            finish();
        });

        this.etAccount.addTextChangedListener(this);
        this.etPwd.addTextChangedListener(this);
        tvCall.setOnClickListener(v ->
                OnLineCallActivity.start(LoginPhoneActivity.this, "")
        );
        // 登录
        Disposable cilck = RxView.clicks(this.btLogin).throttleFirst(1, TimeUnit.SECONDS).subscribe((Object object) -> {

            if (!agreementView.getCheckBox().isChecked()) {
                showToast("请先勾选同意后再登录");
                Animation shake = AnimationUtils.loadAnimation(getApplicationContext(), R.anim.translate);
                agreementView.startAnimation(shake);
                return;
            }

            //权限申请
//            checkSelfPermission(() -> {
                String account = etAccount.getText().toString();
                String pwd = etPwd.getText().toString();

                ReqLogin.CodeBuilder builder = new ReqLogin.CodeBuilder();
                builder.setLoginName(account);
                builder.setLoginCode(pwd);
                //验证码类型
                builder.setCodeType(TextUtils.equals(ReqSendCode.TYPE_SMS, mCodeType));
                getViewModel(LoginCodeModel.class).login(builder.bulder());
//            });

        });
        this.putDisposable(cilck);

        //验证辅助
        this.codeUtil = new VerificationCodeUtil(ReqSendCode.MODULE_TYPE_I, new VerificationCodeUtil.IOnCallback() {
            @Override
            public String getPhone() {
                return etAccount.getText().toString();
            }

            @Override
            public void onClickCode(ReqSendCode req) {
                mCodeType = req.getType();
                getViewModel(LoginCodeModel.class).showImageVerifyCode(req);
            }

            @Override
            public void showToast(CharSequence toast) {

                DialogBuilder dialogBuilder = new DialogBuilder();
                dialogBuilder.setMessage("提示");
                dialogBuilder.setMessage(toast);
                dialogBuilder.setHideCancel(true);
                showDialog(dialogBuilder);
            }
        }).build(this.tvTime, this.tvVoiceTime, this.lyVoice);

        String phone = getIntent().getStringExtra("phone");
        if (!TextUtils.isEmpty(phone)) {
            this.etAccount.setText(phone);
        } else {
            String login_phone = AppCacheManager.getCache("login_phone", String.class);
            if (!TextUtils.isEmpty(login_phone)) {
                etAccount.setText(login_phone);
            }
        }
        findViewById(R.id.iv_weixin_login).setOnClickListener(v -> {
            // 微信登陸
            if (!agreementView.getCheckBox().isChecked()) {
                showToast("请先勾选同意后再登录");
                Animation shake = AnimationUtils.loadAnimation(getApplicationContext(), R.anim.translate);
                agreementView.startAnimation(shake);
                return;
            }
            getViewModel(LoginCodeModel.class).weChatLogin(LoginPhoneActivity.this);
        });

        SparseArray<AgreementView.AgreementTxt> agreementTxtSparseArray = new SparseArray<>();
        agreementTxtSparseArray.put("中储智运平台服务协议".hashCode(), new AgreementView.AgreementTxt("中储智运平台服务协议", HttpConfig.getWebUrl("/form_h5/documents/app_documents/app_consignor/service.html?sceneCode=133&plateType=1&time=" + System.currentTimeMillis())));
        agreementTxtSparseArray.put("中储智运网络货运平台交易规则".hashCode(), new AgreementView.AgreementTxt("中储智运网络货运平台交易规则", HttpConfig.getWebUrl("/form_h5/documents/app_documents/app_consignor/tradeRule.html?sceneCode=133&plateType=2&time=" + System.currentTimeMillis())));
        agreementTxtSparseArray.put("隐私政策".hashCode(), new AgreementView.AgreementTxt("隐私政策", HttpConfig.getWebUrl("/form_h5/documents/app_documents/app_consignor/privacy.html?sceneCode=133&plateType=4&time=" + System.currentTimeMillis())));
        agreementTxtSparseArray.put("用户授权协议".hashCode(), new AgreementView.AgreementTxt("用户授权协议", HttpConfig.getWebUrl("/form_h5/documents/app_documents/app_consignor/authorization.html?sceneCode=133&plateType=3&time=" + System.currentTimeMillis())));

        agreementView.newData(agreementTxtSparseArray);
    }

    @Override
    protected void initData() {
        String versionCode = UtilTool.getVersion(this);
        if (!TextUtils.isEmpty(versionCode)) {
            tvVersion.setText("V" + versionCode);
        } else {
            tvVersion.setVisibility(View.GONE);
        }
    }


    @RxBusEvent(from = "微信登录成功")
    public void weiXinAuthorSuccess(RxWXLoginResultData resultData) {
        if (resultData.query) {
            getViewModel(LoginCodeModel.class).getWxOauthSession(resultData.code);
        } else {
            showToast("授权失败");
        }
    }

    @LiveDataMatch
    public void onReqWxAuthSession(WxAuthSession session) {
        UserBindAccountActivity.start(this, session.getOpenId(), "");
    }


    @LiveDataMatch(tag = "图片验证码结果")
    public void onSendCodeRelust(boolean success, String type) {
        this.codeUtil.onSendCodeResult(success, type);

    }

    @LiveDataMatch(tag = "显示图片验证码")
    public void onShowImageVerifyCode(ReqSendCode req) {
        final String phone = etAccount.getText().toString();
        new ImageCodeDialog(this, phone, (ImageCodeDialog dialog, String code) -> {

            req.setImageCode(code);
            getViewModel(LoginCodeModel.class).sendVerifyCode(req);
        }).show();
    }


    AuthenticationDialog authenticationDialog;

    @LiveDataMatch(tag = "新设备认证短信发送成功")
    public void onSendAuthenticationCodeSuccess(ELogin login) {
        //新设备认证提示
        if (this.authenticationDialog == null) {
            this.authenticationDialog = new AuthenticationDialog(this).setListener(new AuthenticationDialog.Listener() {
                @Override
                public void onReSendCode(ELogin data) {
                    // 重新发送验证码
                    getViewModel(LoginCodeModel.class).sendAuthenticationSMS(data);
                }

                @Override
                public void onCommit(ELogin data, String code) {
                    // 认证
                    getViewModel(LoginCodeModel.class).checkAuthenticationCode(data, code);
                }
            });
        }
        this.authenticationDialog.setLogin(login);
        if (!this.authenticationDialog.isShowing()) {
            this.authenticationDialog.show();
        }
    }

    @LiveDataMatch(tag = "登录成功 or 新设备认证提示")
    public void onLoginSuccess(ELogin login) {
        AppCacheManager.putCache("login_phone", etAccount.getText().toString());
        if (TextUtils.equals("1", login.getReadProtocol())) {
            View view = View.inflate(this, R.layout.user_login_dialog, null);
            CheckBox checkBox = view.findViewById(R.id.cbSelect);

            view.findViewById(R.id.tvAgreement).setOnClickListener(v -> X5WebActivity.startContentUI(this, "中储智运平台服务协议", HttpConfig.getWebUrl("/form_h5/documents/app_documents/app_consignor/service.html?sceneCode=133&plateType=1&time=" + System.currentTimeMillis())));
            view.findViewById(R.id.tvRule).setOnClickListener(v -> X5WebActivity.startContentUI(this, "中储智运网络货运平台交易规则", HttpConfig.getWebUrl("/form_h5/documents/app_documents/app_consignor/tradeRule.html?sceneCode=133&plateType=2&time=" + System.currentTimeMillis())));

            DialogBuilder builder = new DialogBuilder();
            builder.setView(view);
            builder.setTitle("提示");
            builder.setCancelListener((dialogInterface, i) -> {
                dialogInterface.dismiss();
                AppCacheManager.removeCache(Const.LOGIN_KEY);
            });
            builder.setOkListener((dialogInterface, i) -> {

                if (!checkBox.isChecked()) {
                    showToast("请选择阅读并同意");
                    return;
                }
                dialogInterface.dismiss();
                getViewModel(LoginCodeModel.class).updateUserSpecialBackUp();
                gotoHome(login);
            });
            showDialog(builder);
        } else {
            gotoHome(login);
        }
    }

    @LiveDataMatch(tag = "登录ZCZY-10873 供应链会员中台-用户部分")
    public void onLoginSuccessToast(ELogin login){
        new SelectPersonDialog(this, login.getNotUserTypeMsg(), new DialogInterface.OnClickListener() {
            @Override
            public void onClick(DialogInterface dialog, int which) {
                dialog.dismiss();
                if (which == 1){
                    //确认认证
                    getViewModel(LoginCodeModel.class).switchUserType();
                }else {
                    //退出登录
                    finish();
                    ZczyApplication application = AppCacheManager.getApplication();
                    application.onLoseToken("","");
                }
            }
        }).show();

    }

    @LiveDataMatch
    public void onSwitchUserTypeSuccess(){
        //确认认证
        HomeActivity.start(this);
        CertificationUtils.hzCertification(LoginPhoneActivity.this);
        finish();
    }

    @LiveDataMatch
    public void loginError(String msg) {
        UserLoginOtherErrorDialogActivity.startContentUI(this, msg);
    }

    private void gotoHome(ELogin login) {
        if (TextUtils.equals("1", login.getWheterChangePassword())) {
            //需要修改密码
            UserEditPasswordctivity.startUI(this);
        } else {
            HomeActivity.start(this);
            this.finish();
        }
    }

    @LiveDataMatch(tag = "去注册")
    public void onRegiester() {
        RegisterStep1Activity.start(this, this.etAccount.getText().toString());
    }

    @Override
    public void beforeTextChanged(CharSequence s, int start, int count, int after) {

    }

    @Override
    public void onTextChanged(CharSequence s, int start, int before, int count) {

    }

    @Override
    public void afterTextChanged(Editable s) {

        if (s == this.etAccount.getText()) {
            String phone = s.toString();
            if (!TextUtils.isEmpty(phone) && phone.length() >= 11) {
                String code = etPwd.getText().toString();
                this.etPwd.requestFocus();
                this.etPwd.setSelection(TextUtils.isEmpty(code) ? 0 : code.length() - 1);
                this.codeUtil.setAbled(true);
            } else {
                this.codeUtil.setAbled(false);
            }
        }

        String phone = this.etAccount.getText().toString();
        String code = this.etPwd.getText().toString();
        if (TextUtils.isEmpty(phone) || TextUtils.isEmpty(code)) {
            this.btLogin.setEnabled(false);
        } else {
            this.btLogin.setEnabled(true);
        }
    }
    private boolean mCheckSelfPermission = false;

    private void checkSelfPermission(Runnable nextCallback) {
        nextCallback.run();
        if (mCheckSelfPermission){
            //已经申请一次 or 已经授权
            nextCallback.run();
        }else {
            mCheckSelfPermission = true;

            CheckSelfPermissionDialog.storagePhoneStatePermissionDialog(LoginPhoneActivity.this, new PermissionCallBack() {
                @Override
                public void onHasPermission() {
                    //授权 成功
                    nextCallback.run();
                }

                @Override
                public void onFailedPermission() {
                    //授权 失败
                    nextCallback.run();
                }
            });
        }
    }
}
