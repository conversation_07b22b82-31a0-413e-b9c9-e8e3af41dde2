package com.zczy.cargo_owner.user.setting;

import android.content.Context;
import android.content.Intent;
import android.os.Bundle;
import android.text.TextUtils;
import android.view.View;
import android.widget.ImageView;
import android.widget.TextView;

import androidx.annotation.Nullable;

import com.sfh.lib.mvvm.annotation.LiveDataMatch;
import com.sfh.lib.ui.dialog.DialogBuilder;
import com.zczy.cargo_owner.R;
import com.zczy.cargo_owner.libcomm.config.HttpConfig;
import com.zczy.cargo_owner.user.setting.model.SilentProtocolModel;
import com.zczy.certification.carrier.req.QueryPersonalInfoByUserId;
import com.zczy.comm.CommServer;
import com.zczy.comm.data.entity.ELogin;
import com.zczy.comm.data.role.IRelation;
import com.zczy.comm.http.entity.BaseRsp;
import com.zczy.comm.http.entity.ResultData;
import com.zczy.comm.ui.BaseActivity;
import com.zczy.comm.utils.imgloader.ImgUtil;
import com.zczy.comm.utils.imgloader.Options;
import com.zczy.comm.widget.inputv2.InputViewEdit;

import java.util.Objects;

public class PersonDownLoadTwoActivity extends BaseActivity<SilentProtocolModel> implements View.OnClickListener {
    ELogin login = CommServer.getUserServer().getLogin();
    IRelation relation = login.getRelation();
    private ImageView ivHead;
    private TextView userNameTv;
    private TextView userMobileTv;
    private TextView contentTv;
    private TextView tv_commit;
    private InputViewEdit input_name_producer;
    private String source;
    private String customerName;
    private String mobile;

    public static void start(Context context) {
        Intent intent = new Intent(context, PersonDownLoadTwoActivity.class);
        context.startActivity(intent);
    }

    @Override
    protected int getLayout() {
        return R.layout.person_download_two_activty;
    }

    @Override
    protected void bindView(@Nullable Bundle bundle) {
        ivHead = findViewById(R.id.ivHead);
        userNameTv = findViewById(R.id.userNameTv);
        userMobileTv = findViewById(R.id.userMobileTv);
        contentTv = findViewById(R.id.contentTv);
        tv_commit = findViewById(R.id.tv_commit);
        input_name_producer = findViewById(R.id.input_name_producer);
        input_name_producer.setOnClickListener(this);
        tv_commit.setOnClickListener(this);
    }

    @Override
    protected void initData() {
        getViewModel().queryPersonalInfoByUserId();

    }

    @Override
    public void onClick(View v) {
        int id = v.getId();
        if (id == R.id.tv_commit) {
            if (input_name_producer.getContent().isEmpty() || !input_name_producer.getContent().contains("@")){
                showToast("邮箱格式不正确，请重新输入！");
                return;
            }else {
                getViewModel().submitPersonalDownLoad(source,input_name_producer.getContent(),customerName,mobile,source);
            }
        }
    }

    @LiveDataMatch
    public void queryPersonalInfoByUserIdSuccess(QueryPersonalInfoByUserId data) {
        source = data.getContent();
                customerName = data.getCustomerName();
                mobile = data.getMobile();
        if (TextUtils.isEmpty(data.getHeadUrl())) {
            ivHead.setImageResource(R.drawable.person_down);
        } else {
            ImgUtil.loadUrl(ivHead, HttpConfig.getUrlImage(Objects.requireNonNull(data.getHeadUrl())), Options.creator().setCircle(true).setError(R.drawable.person_down).setPlaceholder(R.drawable.person_down));
        }
        userNameTv.setText(data.getCustomerName());
        userMobileTv.setText(data.getMobile());
        contentTv.setText(data.getContent());
    }


    @LiveDataMatch
    public void SubmitPersonalDownLoadSuccess( BaseRsp<ResultData> rspBase) {
        DialogBuilder dialogBuilder =  new DialogBuilder();
        dialogBuilder.setHideCancel(true);
        dialogBuilder.setMessage("我们将在1个工作日内将链接发送至您的邮箱" +
                input_name_producer.getContent()+ "，链接有效期为1天");
        dialogBuilder.setOKTextColor("确定", R.color.text_blue);
        dialogBuilder.setOkListener(new DialogBuilder.DialogInterface.OnClickListener() {
            @Override
            public void onClick(DialogBuilder.DialogInterface dialog, int which) {
                dialog.dismiss();
                finish();
            }
        });
        showDialog(dialogBuilder);
    }

}
