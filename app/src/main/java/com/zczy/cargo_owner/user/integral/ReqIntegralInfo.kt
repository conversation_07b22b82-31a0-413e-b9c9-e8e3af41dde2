package com.zczy.cargo_owner.user.integral

import com.zczy.comm.http.entity.BaseNewRequest
import com.zczy.comm.http.entity.BaseRsp
import com.zczy.comm.http.entity.ResultData

/**
 * 功能描述:查询积分总积分数列表
 *
 * <AUTHOR> 孙飞虎
 * @company 中储智运科技股份有限公司
 * @copyright （版权）中储南京智慧物流科技有限公司所有
 * @date 2018/12/7
 */
class ReqIntegralInfo : BaseNewRequest<BaseRsp<IntegralScore>>("ims-app/integral/index") {
    var rows = 10
    var page = 1
}

class IntegralScore : ResultData() {
    /***会员总积分数 */
    var totalIntegral: String = ""
    var willExpireIntegral: String = ""
}
