package com.zczy.cargo_owner.user.contact;

import android.app.Activity;
import android.app.Dialog;
import android.content.Context;
import android.content.Intent;
import android.graphics.Color;
import android.os.Bundle;
import android.provider.ContactsContract;
import androidx.annotation.Nullable;

import android.text.Editable;
import android.text.TextUtils;
import android.text.TextWatcher;
import android.view.Gravity;
import android.view.View;
import android.view.ViewGroup;
import android.view.Window;
import android.widget.AdapterView;
import android.widget.EditText;
import android.widget.FrameLayout;
import android.widget.ImageView;
import android.widget.ListView;
import android.widget.SimpleAdapter;
import android.widget.TextView;
import android.widget.Toast;

import com.bigkoo.pickerview.builder.TimePickerBuilder;
import com.bigkoo.pickerview.listener.OnTimeSelectChangeListener;
import com.bigkoo.pickerview.listener.OnTimeSelectListener;
import com.bigkoo.pickerview.view.TimePickerView;
import com.sfh.lib.mvvm.annotation.LiveDataMatch;
import com.sfh.lib.ui.AbstractLifecycleActivity;
import com.zczy.cargo_owner.R;
import com.zczy.cargo_owner.libcomm.utils.UriExKt;
import com.zczy.cargo_owner.libcomm.widget.CheckSelfPermissionDialog;
import com.zczy.cargo_owner.user.contact.model.CPageList;
import com.zczy.cargo_owner.user.contact.model.ContactModel;
import com.zczy.cargo_owner.user.contact.model.req.ReqDeleteContact;
import com.zczy.cargo_owner.user.contact.model.req.ReqEditContact;
import com.zczy.cargo_owner.user.contact.model.req.ReqTypeMap;
import com.zczy.cargo_owner.user.contact.model.resp.RespContactListData;
import com.zczy.cargo_owner.user.contact.model.resp.RespTypeMap;
import com.zczy.comm.permission.PermissionCallBack;
import com.zczy.comm.permission.PermissionUtil;
import com.zczy.comm.ui.UtilStatus;
import com.zczy.comm.utils.ex.StringUtil;
import com.zczy.comm.widget.AppToolber;

import java.text.SimpleDateFormat;
import java.util.ArrayList;
import java.util.Date;
import java.util.HashMap;
import java.util.List;

public class EditContactActivity extends AbstractLifecycleActivity<ContactModel> {

    private EditText et_input_name;
    private EditText et_input_phone;
    private TextView tv_choose_type;
    private TextView tv_time_start;
    private TextView tv_time_end;
    private EditText et_mark;
    private TextView tv_mark_count;
    private ImageView iv_phone_contact;
    private TextView tv_delete_contact;

    private static int REQUEST_PHONE = 0x42;
    private TimePickerView pvTime;
    private ArrayList<HashMap<String, String>> dataList;

    private AppToolber appToolber;
    private RespContactListData respContactListData;
    private HashMap<String, String> map;

    public static void start(Context context, RespContactListData respContactListData) {
        Intent intent = new Intent(context, EditContactActivity.class);
        intent.putExtra("respContactListData", respContactListData);
        context.startActivity(intent);
    }

    @Override
    protected void onCreate(Bundle savedInstanceState) {
        super.onCreate(savedInstanceState);
        setContentView(R.layout.activity_edit);
        UtilStatus.initStatus(this, Color.WHITE);
        respContactListData = (RespContactListData) getIntent().getSerializableExtra("respContactListData");
        initView();
        initData();
    }

    private void initData() {
        if (null != respContactListData) {
            //1:挂单  2:装卸货  3:回单  4:轨迹  5:结算  6:财务  7:其他
            String type = "";
            switch (respContactListData.contactsType) {
                case "1":
                    type = "挂单";
                    break;
                case "2":
                    type = "装卸货";
                    break;
                case "3":
                    type = "回单";
                    break;
                case "4":
                    type = "轨迹";
                    break;
                case "5":
                    type = "结算";
                    break;
                case "6":
                    type = "财务";
                    break;
                default:
                    type = "其他";
                    break;
            }

            et_input_name.setText(respContactListData.fullName);
            et_input_phone.setText(respContactListData.mobile);
            tv_choose_type.setText(respContactListData.contactsTypeDesc);
            tv_time_start.setText(respContactListData.contactStartTime);
            tv_time_end.setText(respContactListData.contactEndTime);
            et_mark.setText(respContactListData.remark);
        }
    }

    private void initView() {
        appToolber = findViewById(R.id.appToolber);
        et_input_name = findViewById(R.id.et_input_name);
        et_input_phone = findViewById(R.id.et_input_phone);
        tv_choose_type = findViewById(R.id.tv_choose_type);
        tv_time_start = findViewById(R.id.tv_time_start);
        tv_time_end = findViewById(R.id.tv_time_end);
        et_mark = findViewById(R.id.et_mark);
        tv_mark_count = findViewById(R.id.tv_mark_count);
        iv_phone_contact = findViewById(R.id.iv_phone_contact);
        tv_delete_contact = findViewById(R.id.tv_delete_contact);

        appToolber.setRightOnClickListener(new View.OnClickListener() {
            @Override
            public void onClick(View v) {
                if (TextUtils.isEmpty(et_input_name.getText().toString())) {
                    Toast.makeText(EditContactActivity.this, "请输入联系人", Toast.LENGTH_SHORT).show();
                } else if (TextUtils.isEmpty(et_input_phone.getText().toString())) {
                    Toast.makeText(EditContactActivity.this, "请输入联系电话", Toast.LENGTH_SHORT).show();
                } else if (!TextUtils.isEmpty(et_input_phone.getText().toString()) && !isPhoneNumber(et_input_phone.getText().toString())) {
                    Toast.makeText(EditContactActivity.this, "请输入正确的联系电话号码", Toast.LENGTH_SHORT).show();
                } else if (TextUtils.isEmpty(tv_choose_type.getText().toString())) {
                    Toast.makeText(EditContactActivity.this, "请选择联系人类型", Toast.LENGTH_SHORT).show();
                } else {
                    //1:挂单  2:装卸货  3:回单  4:轨迹  5:结算  6:财务  7:其他
                    String type = "";
                    switch (tv_choose_type.getText().toString()) {
                        case "挂单":
                            type = "1";
                            break;
                        case "装卸货":
                            type = "2";
                            break;
                        case "回单":
                            type = "3";
                            break;
                        case "轨迹":
                            type = "4";
                            break;
                        case "结算":
                            type = "5";
                            break;
                        case "财务":
                            type = "6";
                            break;
                        default:
                            type = "7";
                            break;
                    }

                    ReqEditContact reqEditContact = new ReqEditContact();
                    if (null != respContactListData) {
                        reqEditContact.id = respContactListData.id;
                    }
                    reqEditContact.fullName = et_input_name.getText().toString();
                    reqEditContact.mobile = et_input_phone.getText().toString();
                    if (null != map) {
                        reqEditContact.contactsType = map.get("type");
                    }else{
                        reqEditContact.contactsType = respContactListData.contactsType;
                    }

                    reqEditContact.contactStartTime = tv_time_start.getText().toString();
                    reqEditContact.contactEndTime = tv_time_end.getText().toString();
                    reqEditContact.remark = et_mark.getText().toString();
                    getViewModel(ContactModel.class).editContact(reqEditContact);
                }
            }
        });

        tv_delete_contact.setOnClickListener(new View.OnClickListener() {
            @Override
            public void onClick(View v) {
                if (null != respContactListData) {
                }
                ReqDeleteContact reqDeleteContact = new ReqDeleteContact();
                if (null != respContactListData) {
                    reqDeleteContact.id = respContactListData.id;
                }
                getViewModel(ContactModel.class).deleteContact(reqDeleteContact);
            }
        });

        tv_choose_type.setOnClickListener(new View.OnClickListener() {
            @Override
            public void onClick(View v) {
                getViewModel(ContactModel.class).queryTypeMap(new ReqTypeMap());
            }
        });

        tv_time_start.setOnClickListener(new View.OnClickListener() {
            @Override
            public void onClick(View v) {
                initTimePicker(tv_time_start);
            }
        });

        tv_time_end.setOnClickListener(new View.OnClickListener() {
            @Override
            public void onClick(View v) {
                initTimePicker(tv_time_end);
            }
        });

        iv_phone_contact.setOnClickListener(new View.OnClickListener() {
            @Override
            public void onClick(View v) {
                CheckSelfPermissionDialog.cameraPermissionDialog(EditContactActivity.this, new PermissionCallBack() {
                    @Override
                    public void onHasPermission() {
                        PermissionUtil.contacts(EditContactActivity.this, new PermissionCallBack() {
                            @Override
                            public void onHasPermission() {
                                Intent intent = new Intent(Intent.ACTION_PICK, ContactsContract.CommonDataKinds.Phone.CONTENT_URI);
                                intent.addCategory(Intent.CATEGORY_DEFAULT);
                                startActivityForResult(intent, REQUEST_PHONE);
                            }
                        });
                    }});

            }
        });

        initMarkArea(100);
    }

    private void initMarkArea(int textNum) {
        et_mark.addTextChangedListener(new TextWatcher() {
            @Override
            public void beforeTextChanged(CharSequence s, int start, int count, int after) {

            }

            @Override
            public void onTextChanged(CharSequence s, int start, int before, int count) {
                String str = s.toString();
                if (str.length() > textNum) {
                    et_mark.setText(str.substring(0, textNum));
                    et_mark.requestFocus();
                    et_mark.setSelection(et_mark.getText().length());
                    tv_mark_count.setText(new StringBuilder().append("(").append(textNum).append("/").append(textNum).append(")").toString());
                } else {
                    tv_mark_count.setText(new StringBuilder().append("(").append(str.length()).append("/").append(textNum).append(")").toString());
                }
            }

            @Override
            public void afterTextChanged(Editable s) {
                String text = s.toString();
                // 检查文本中是否包含 < 或 >
                if (text.contains("<") || text.contains(">")) {
                    // 如果包含，则移除
                    text = text.replaceAll("<", "").replaceAll(">", "");
                    et_mark.setText(text);
                    et_mark.setSelection(text.length()); // 将光标移动到末尾
                }
            }
        });
    }

    private void initTimePicker(TextView tvTimeView) {//Dialog 模式下，在底部弹出
        pvTime = new TimePickerBuilder(this, new OnTimeSelectListener() {
            @Override
            public void onTimeSelect(Date date, View v) {
                tvTimeView.setText(getTime(date));
            }
        })
                .setTimeSelectChangeListener(new OnTimeSelectChangeListener() {
                    @Override
                    public void onTimeSelectChanged(Date date) {

                    }
                })
                .setType(new boolean[]{false, false, false, true, true, true})
                .isDialog(true) //默认设置false ，内部实现将DecorView 作为它的父控件。
                .addOnCancelClickListener(new View.OnClickListener() {
                    @Override
                    public void onClick(View view) {

                    }
                })
                .setItemVisibleCount(5) //若设置偶数，实际值会加1（比如设置6，则最大可见条目为7）
                .setLineSpacingMultiplier(3.0f)
                .isAlphaGradient(true)
                .build();

        Dialog mDialog = pvTime.getDialog();
        if (mDialog != null) {

            FrameLayout.LayoutParams params = new FrameLayout.LayoutParams(
                    ViewGroup.LayoutParams.MATCH_PARENT,
                    ViewGroup.LayoutParams.WRAP_CONTENT,
                    Gravity.BOTTOM);

            params.leftMargin = 0;
            params.rightMargin = 0;
            pvTime.getDialogContainerLayout().setLayoutParams(params);

            Window dialogWindow = mDialog.getWindow();
            if (dialogWindow != null) {
                dialogWindow.setWindowAnimations(com.bigkoo.pickerview.R.style.picker_view_slide_anim);//修改动画样式
                dialogWindow.setGravity(Gravity.BOTTOM);//改成Bottom,底部显示
                dialogWindow.setDimAmount(0.3f);
            }
        }
        mDialog.show();
    }

    private String getTime(Date date) {
//        SimpleDateFormat format = new SimpleDateFormat("yyyy-MM-dd HH:mm:ss");
        SimpleDateFormat format = new SimpleDateFormat("HH:mm:ss");
        return format.format(date);
    }

    private void showTypeChooseDialog(List<RespTypeMap> records) {
        final Dialog dialog = new Dialog(this, R.style.BottomDialogTheme);
        View view = View.inflate(this, R.layout.type_list, null);
        dialog.setContentView(view);
        Window window = dialog.getWindow();
        window.setGravity(Gravity.BOTTOM);
        window.setLayout(ViewGroup.LayoutParams.MATCH_PARENT, ViewGroup.LayoutParams.WRAP_CONTENT);
        dialog.show();

        dialog.findViewById(R.id.iv_cancel).setOnClickListener(new View.OnClickListener() {
            @Override
            public void onClick(View view) {
                dialog.dismiss();
            }
        });

        ListView type_list = dialog.findViewById(R.id.type_list);

        SimpleAdapter simpleAdapter = new SimpleAdapter(this,
                getData(records),
                R.layout.item_simple_layout,
                new String[]{"type_name", "choose_img"},
                new int[]{R.id.type_name, R.id.choose_img});

        type_list.setAdapter(simpleAdapter);

        type_list.setOnItemClickListener(new AdapterView.OnItemClickListener() {
            @Override
            public void onItemClick(AdapterView<?> parent, View view, int position, long id) {
                map = dataList.get(position);
                tv_choose_type.setText(map.get("type_name").toString());
                dialog.dismiss();
            }
        });
    }

    public static boolean isPhoneNumber(String phoneNum) {
//        if (TextUtils.isEmpty(phoneNo)) {
//            return false;
//        }
//        if (phoneNo.length() == 11) {
//            for (int i = 0; i < 11; i++) {
//                if (!PhoneNumberUtils.isISODigit(phoneNo.charAt(i))) {
//                    return false;
//                }
//            }
//            Pattern p = Pattern.compile("^((13[^4,\\D])" + "|(134[^9,\\D])" +
//                    "|(14[5,7])" +
//                    "|(15[^4,\\D])" +
//                    "|(17[3,6-8])" +
//                    "|(18[0-9]))\\d{8}$");
//            Matcher m = p.matcher(phoneNo);
//            return m.matches();
//        }
//        return false;

        String telRegex = "^((1[3,5,7,8][0-9])|(15[^4,\\D])|(18[0,5-9]))\\d{8}$";
        if (TextUtils.isEmpty(phoneNum)) {
            return false;
        } else {
            return phoneNum.matches(telRegex);
        }
    }

    private List<HashMap<String, String>> getData(List<RespTypeMap> records) {
        dataList = new ArrayList<>();

        for (RespTypeMap respTypeMap : records) {
            HashMap map = new HashMap<String, Object>();
            map.put("type_name", respTypeMap.description);
            map.put("type", respTypeMap.value);
            map.put("choose_img", R.drawable.icon_choose_type);
            dataList.add(map);
        }

//        HashMap map = new HashMap<String, Object>();
//        map.put("type_name", "挂单");
//        map.put("choose_img", R.drawable.icon_choose_type);
//        dataList.add(map);
//
//        HashMap map1 = new HashMap<String, Object>();
//        map1.put("type_name", "装卸货");
//        map1.put("choose_img", R.drawable.icon_choose_type);
//        dataList.add(map1);
//
//        HashMap map2 = new HashMap<String, Object>();
//        map2.put("type_name", "回单");
//        map2.put("choose_img", R.drawable.icon_choose_type);
//        dataList.add(map2);
//
//        HashMap map3 = new HashMap<String, Object>();
//        map3.put("type_name", "轨迹");
//        map3.put("choose_img", R.drawable.icon_choose_type);
//        dataList.add(map3);
//
//        HashMap map4 = new HashMap<String, Object>();
//        map4.put("type_name", "结算");
//        map4.put("choose_img", R.drawable.icon_choose_type);
//        dataList.add(map4);
//
//        HashMap map5 = new HashMap<String, Object>();
//        map5.put("type_name", "财务");
//        map5.put("choose_img", R.drawable.icon_choose_type);
//        dataList.add(map5);
//
//        HashMap map6 = new HashMap<String, Object>();
//        map6.put("type_name", "其他");
//        map6.put("choose_img", R.drawable.icon_choose_type);
//        dataList.add(map6);

        return dataList;
    }

    @Override
    protected void onActivityResult(int requestCode, int resultCode, @Nullable @org.jetbrains.annotations.Nullable Intent data) {
        super.onActivityResult(requestCode, resultCode, data);
        if (resultCode == Activity.RESULT_OK || requestCode == REQUEST_PHONE) {
            if (data != null) {
                String[] contact = UriExKt.getPhoneContacts(data.getData(), EditContactActivity.this);
                if (contact != null) {
                    String name = contact[0];
                    String phone = StringUtil.formatPhoneNum(contact[1]);
                    et_input_name.setText(name);
                    et_input_phone.setText(phone);
                } else {
                    showDialogToast("获取联系人信息失败！");
                }
            }
        }
    }

    @LiveDataMatch
    public void onEditContactSuccess() {
        Toast.makeText(this, "保存成功", Toast.LENGTH_SHORT).show();
        finish();
    }

    @LiveDataMatch
    public void onDeleteContactSuccess() {
        Toast.makeText(this, "删除成功", Toast.LENGTH_SHORT).show();
        finish();
    }

    @LiveDataMatch
    public void onQueryTypeMapSuccess(CPageList<RespTypeMap> data) {
        showTypeChooseDialog(data.records);
    }
}