package com.zczy.cargo_owner.user.exception;

import android.annotation.SuppressLint;
import android.app.Activity;
import android.content.Intent;
import android.graphics.Color;
import android.os.Bundle;

import androidx.annotation.Nullable;

import android.text.Editable;
import android.text.InputFilter;
import android.text.TextWatcher;
import android.widget.EditText;
import android.widget.ImageView;
import android.widget.LinearLayout;
import android.widget.TextView;

import com.sfh.lib.event.RxBusEventManager;
import com.sfh.lib.mvvm.annotation.LiveDataMatch;
import com.sfh.lib.ui.AbstractLifecycleActivity;
import com.sfh.lib.ui.dialog.DialogBuilder;
import com.zczy.cargo_owner.R;
import com.zczy.cargo_owner.libcomm.config.HttpConfig;
import com.zczy.cargo_owner.libcomm.widget.CheckSelfPermissionDialog;
import com.zczy.cargo_owner.user.exception.bean.WaybillDetails;
import com.zczy.cargo_owner.user.exception.model.ExceptionModel;
import com.zczy.cargo_owner.user.exception.req.ReqWayDetails;
import com.zczy.cargo_owner.user.exception.req.ReqWaybillSubmit;
import com.zczy.comm.CommServer;
import com.zczy.comm.data.entity.EImage;
import com.zczy.comm.data.entity.ELogin;
import com.zczy.comm.data.entity.EProcessFile;
import com.zczy.comm.http.entity.BaseRsp;
import com.zczy.comm.http.entity.ResultData;
import com.zczy.comm.permission.PermissionCallBack;
import com.zczy.comm.permission.PermissionUtil;
import com.zczy.comm.ui.UtilStatus;
import com.zczy.comm.utils.ex.CollectionUtil;
import com.zczy.comm.utils.ex.StringUtil;
import com.zczy.comm.utils.ex.ViewUtil;
import com.zczy.comm.utils.imageselector.ImagePreviewActivity;
import com.zczy.comm.utils.imageselector.ImageSelectProgressView;
import com.zczy.comm.utils.imageselector.ImageSelector;
import com.zczy.comm.widget.AppToolber;
import com.zczy.comm.widget.inputv2.InputViewClick;

import java.io.File;
import java.util.ArrayList;
import java.util.List;

import kotlin.collections.CollectionsKt;

/**
 * 运单异常
 * 提交证明材料,重新提交材料页面
 */

public class WaybillProveInfoSubmitActivity extends AbstractLifecycleActivity<ExceptionModel> {


    private AppToolber mAppToolber;
    private InputViewClick mTvGoodsNumber;
    /**
     * 材料审核状态
     */
    private TextView mTvVerifyTip;
    private InputViewClick mTvMaterialSubmitParty;
    private InputViewClick mTvStartAddress;
    private InputViewClick mTvEndAddress;
    private InputViewClick mTvCarrierName;
    private InputViewClick mTvCarrierNumber;
    private InputViewClick mTvPlateNumber;
    private InputViewClick mTvExceptionType;
    /**
     * 异常原因
     */
    private TextView mTvHandlingOpinionsTip;
    private TextView mTvHandlingOpinions;
    private ImageSelectProgressView mImageSelectView;
    private EditText mNoteTv;
    /**
     * (0/150)
     */
    private TextView mSizeTv;
    private InputViewClick inputHandleType;
    private LinearLayout mNoteLayout;
    /**
     * 提交
     */
    private TextView mTvSubmit;
    private String monitorId;
    private static final int REQUEST_PHOTO = 0x32;
    private String orderId;
    private String submitterType;

    public static void startUI(Activity activity, String monitorId, int requestCode) {
        Intent intent = new Intent(activity, WaybillProveInfoSubmitActivity.class);
        intent.putExtra("monitorId", monitorId);
        activity.startActivityForResult(intent, requestCode);
    }

    @Override
    protected void onCreate(@Nullable Bundle savedInstanceState) {
        super.onCreate(savedInstanceState);
        setContentView(R.layout.waybill_proveinfo_activity);
        UtilStatus.initStatus(this, Color.WHITE);
        initView();
        initListener();
        monitorId = getIntent().getStringExtra("monitorId");
        ReqWayDetails reqWayDetails = new ReqWayDetails();
        reqWayDetails.setId(monitorId);
        getViewModel().queryWaybillDeatils(reqWayDetails);
    }

    private void initView() {
        mAppToolber = (AppToolber) findViewById(R.id.appToolber);
        mTvGoodsNumber = (InputViewClick) findViewById(R.id.tv_goods_number);
        mTvVerifyTip = (TextView) findViewById(R.id.tv_verify_tip);
        mTvMaterialSubmitParty = (InputViewClick) findViewById(R.id.tv_material_submit_party);
        mTvStartAddress = (InputViewClick) findViewById(R.id.tv_start_address);
        mTvEndAddress = (InputViewClick) findViewById(R.id.tv_end_address);
        mTvCarrierName = (InputViewClick) findViewById(R.id.tv_carrier_name);
        mTvCarrierNumber = (InputViewClick) findViewById(R.id.tv_carrier_number);
        mTvPlateNumber = (InputViewClick) findViewById(R.id.tv_plate_number);
        mTvExceptionType = (InputViewClick) findViewById(R.id.tv_exception_type);
        mTvHandlingOpinionsTip = (TextView) findViewById(R.id.tv_handling_opinions_tip);
        mTvHandlingOpinions = (TextView) findViewById(R.id.tv_handling_opinions);
        mImageSelectView = findViewById(R.id.image_select_view);
        mNoteTv = (EditText) findViewById(R.id.noteTv);
        mSizeTv = (TextView) findViewById(R.id.sizeTv);
        mNoteLayout = (LinearLayout) findViewById(R.id.noteLayout);
        mTvSubmit = (TextView) findViewById(R.id.tv_submit);
        inputHandleType = findViewById(R.id.inputHandleType);
    }

    private void initListener() {
        mNoteTv.setFilters(new InputFilter[]{new InputFilter.LengthFilter(200)});
        mNoteTv.addTextChangedListener(new TextWatcher() {
            @Override
            public void beforeTextChanged(CharSequence s, int start, int count, int after) {
            }

            @Override
            public void onTextChanged(CharSequence s, int start, int before, int count) {
            }

            @SuppressLint("SetTextI18n")
            @Override
            public void afterTextChanged(Editable s) {
                if (s.length() > 200) {
                    return;
                }
                mSizeTv.setText("(" + s.length() + "/200)");
            }
        });
        mTvSubmit.setOnClickListener(view -> {

            List<String> imgList = CollectionsKt.map(mImageSelectView.getDataList(), eProcessFile -> eProcessFile.getImagUrl());
            ELogin login = CommServer.getUserServer().getLogin();
            ReqWaybillSubmit reqWaybillSubmit = new ReqWaybillSubmit();
            reqWaybillSubmit.setId(monitorId);
            reqWaybillSubmit.setChildId(login.getUserId());
            reqWaybillSubmit.setFileUrl(CollectionUtil.toCommaString(imgList));
            reqWaybillSubmit.setSubmitterType(submitterType);
            reqWaybillSubmit.setRemarks(mNoteTv.getText().toString().trim());
            reqWaybillSubmit.setUploadType("3");
            getViewModel().exceptionOrderSubmit(reqWaybillSubmit);
        });

        this.mImageSelectView.setOnItemSelectListener(new ImageSelectProgressView.OnItemSelectListener() {

            @Override
            public void onSelectImageClick(int surplus) {
                CheckSelfPermissionDialog.cameraPermissionDialog(WaybillProveInfoSubmitActivity.this, new PermissionCallBack() {
                    @Override
                    public void onHasPermission() {
                        PermissionUtil.openAlbum(WaybillProveInfoSubmitActivity.this,
                                new PermissionCallBack() {
                                    @Override
                                    public void onHasPermission() {
                                        ImageSelector.open(WaybillProveInfoSubmitActivity.this, surplus, true, REQUEST_PHOTO);
                                    }
                                });
                    }
                });

            }

            @Override
            public void onUpImageClick(String file) {
                getViewModel().upLoadPic(file);
            }

            @Override
            public void onLookImageClick(List<EProcessFile> file, int position) {
                //查看大图
                List<EImage> list = new ArrayList<>(file.size());
                for (EProcessFile processFile : file) {
                    EImage image = new EImage();
                    image.setNetUrl(HttpConfig.getUrlImage(processFile.getImagUrl()));
                    list.add(image);
                }
                ImagePreviewActivity.start(WaybillProveInfoSubmitActivity.this, list, position);
            }

            @Override
            public void onDelateClick(int position) {
                DialogBuilder dialogBuilder = new DialogBuilder();
                dialogBuilder.setMessage("确定删除当前图片吗？");
                dialogBuilder.setOkListener((DialogBuilder.DialogInterface dialogInterface, int i) -> {
                    dialogInterface.dismiss();
                    mImageSelectView.deleteImage(position);
                });
                showDialog(dialogBuilder);
            }
        });
        this.mImageSelectView.setShowSize(10, 4);
        this.mImageSelectView.setDelete(true);
    }


    @Override
    public void onActivityResult(int requestCode, int resultCode, Intent data) {
        super.onActivityResult(requestCode, resultCode, data);
        if (resultCode == Activity.RESULT_OK) {
            switch (requestCode) {
                case REQUEST_PHOTO:
                    List<String> file = ImageSelector.obtainPathResult(data);
                    mImageSelectView.onUpLoadStart(file);
                    getViewModel().uploadFiles(file);
                    break;
            }
        }
    }

    // 证明材料提交成功
    @LiveDataMatch
    public void submitSuccess(BaseRsp<ResultData> baseRsp) {
        RxBusEventManager.postEvent("重新提交证明材料成功");
        finish();
    }

    // 图片上传成功
    @LiveDataMatch
    public void upLoadPicSuccess(File tag, String url) {
        mImageSelectView.onUpLoadFileSuccess(tag.getAbsolutePath(), url);
    }

    @LiveDataMatch(tag = "查询运单详情成功")
    public void onWaybillDetailsSuccess(BaseRsp<WaybillDetails> data) {
        if (data.getData() == null) {
            return;
        }
        WaybillDetails.ExamineDtoBean examineDto = data.getData().getExamineDto();
        List<WaybillDetails.RootArrayBean> rootArrayBeans = data.getData().getRootArray();
        List<EProcessFile> imageList = new ArrayList<>();
        if (rootArrayBeans != null) {
            for (WaybillDetails.RootArrayBean rootArrayBean : rootArrayBeans) {
                EProcessFile rImage = new EProcessFile();
                rImage.setImagUrl(rootArrayBean.getUrl());
                imageList.add(rImage);
            }
        }
        if (examineDto != null) {
            orderId = examineDto.getOrderId();
            mTvGoodsNumber.setContent(orderId);
            ImageView imgArrow = mTvGoodsNumber.getImgArrow();
            ViewUtil.setVisible(imgArrow, StringUtil.isTrue(examineDto.getLTLOrderFlag()));
            imgArrow.setImageResource(R.drawable.deliver_order_nil_tag);
            mTvStartAddress.setContent(examineDto.getDespatch());
            mTvEndAddress.setContent(examineDto.getDeliver());
            // 承运人
            mTvCarrierName.setContent(examineDto.getDriverUserName());
            // 发货人手机号码
            mTvCarrierNumber.setContent(examineDto.getDriverMobile());
            // 发货人
            mTvPlateNumber.setContent(examineDto.getPlateNumber());
            //异常类别
            mTvExceptionType.setContent(examineDto.getExceptionType());
            //处理方式
            inputHandleType.setContent(examineDto.getHandleOpinion());
            ViewUtil.setVisible(inputHandleType, StringUtil.isTrue(examineDto.getShowHandleOpinion()));
            // 异常原因
            mTvHandlingOpinions.setText(examineDto.getMessage());
            // 备注信息
            mNoteTv.setText(examineDto.getRemarks());
            submitterType = examineDto.getSubmitterType();
        }
    }

}
