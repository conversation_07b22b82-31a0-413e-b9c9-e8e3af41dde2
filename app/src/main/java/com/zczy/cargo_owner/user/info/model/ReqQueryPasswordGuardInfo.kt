package com.zczy.cargo_owner.user.info.model

import com.zczy.comm.http.entity.BaseNewRequest
import com.zczy.comm.http.entity.BaseRsp
import com.zczy.comm.http.entity.ResultData

/**
 *  MMS-13637 用户账号密码登录防护策略
 *
 */
class ReqQueryPasswordGuardInfo :
    BaseNewRequest<BaseRsp<RspPasswordGuardInfo>>("/mms-app/member/queryPasswordGuardInfo")

data class RspPasswordGuardInfo(
    var popUpFlag: Int = 0,//0：不强制 1：强制修改 2：只提醒，不强制修改
    var isDefaultPassword: Boolean = false, //是否默认密码
    var isWeakPassword: Boolean = false, //是否弱密码
    var isTimeOutExpired: Boolean = false,//是否密码过期
) : ResultData(){
    fun openUI():Boolean{
        //目前弹窗只需判断：popUpFlag，0不弹出，1弹出且不可关闭，2弹出可关闭
        return popUpFlag != 0
    }
}

//APP修改密码
class ReqUpdatePasswordV2(
    var newPwd: String? = "",//新密码
    var affirmPwd: String? = "",//确认密码
) : BaseNewRequest<BaseRsp<ResultData>>("/mms-app/member/updatePasswordV2")