package com.zczy.cargo_owner.user.city;

import android.text.TextUtils;

import com.zczy.comm.http.entity.ResultData;

/**
 * 功能描述: 定位转换城市信息
 *
 * <AUTHOR> 孙飞虎
 * @company 中储智运科技股份有限公司
 * @copyright （版权）中储南京智慧物流科技有限公司所有
 * @date 2018/10/29
 */
public class ELocationCity extends ResultData {

    String areaCode;
    String proNm;
    String proCode;
    String cityCode;
    String cityNm;
    String areaNm;

    //[本地传值]经度
    String longitude;

    //[本地传值]纬度
    String latitude;

    public String getLongitude() {
        return longitude;
    }

    public void setLongitude(String longitude) {
        this.longitude = longitude;
    }

    public String getLatitude() {
        return latitude;
    }

    public void setLatitude(String latitude) {
        this.latitude = latitude;
    }

    public String getAreaCode() {
        return areaCode;
    }

    public String getProNm() {
        return proNm;
    }

    public String getProCode() {
        return proCode;
    }

    public String getCityCode() {
        return cityCode;
    }

    public String getCityNm() {
        return cityNm;
    }

    public String getAreaNm() {
        return areaNm;
    }

    public void setAreaCode(String areaCode) {
        this.areaCode = areaCode;
    }

    public void setProNm(String proNm) {
        this.proNm = proNm;
    }

    public void setProCode(String proCode) {
        this.proCode = proCode;
    }

    public void setCityCode(String cityCode) {
        this.cityCode = cityCode;
    }

    public void setCityNm(String cityNm) {
        this.cityNm = cityNm;
    }

    public void setAreaNm(String areaNm) {
        this.areaNm = areaNm;
    }

    public String getName(){
        StringBuilder builder = new StringBuilder(20);
        if (!TextUtils.isEmpty(proNm)){
            builder.append(proNm).append(" ");
        }
        if (!TextUtils.isEmpty(cityNm)){
            builder.append(cityNm).append(" ");
        }
        if (!TextUtils.isEmpty(areaNm)){
            builder.append(areaNm);
        }
        return builder.toString();
    }
}
