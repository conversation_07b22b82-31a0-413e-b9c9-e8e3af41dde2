package com.zczy.cargo_owner.user.assure

import android.app.Activity
import android.content.Context
import android.os.Bundle
import androidx.fragment.app.Fragment
import android.view.View
import com.chad.library.adapter.base.BaseQuickAdapter
import com.chad.library.adapter.base.listener.OnItemClickListener
import com.sfh.lib.mvvm.annotation.LiveDataMatch
import com.zczy.comm.http.entity.PageList
import com.zczy.comm.ui.BaseFragment
import com.zczy.comm.utils.dp2px
import com.zczy.comm.widget.pulltorefresh.CommEmptyView
import com.zczy.comm.widget.pulltorefresh.OnLoadingListener
import kotlinx.android.synthetic.main.order_common_fragment_new.*


/**
 * PS: 逾期运单管理 待处理
 * Created by sdx on 2019-06-24.
 */
class AssureListHuozhuFragment : BaseFragment<AssureModel>() {

    private val mAdapter = AssureAdapter()

    companion object {
        @JvmStatic
        fun newInstance(context: Context): AssureListHuozhuFragment {
            val args = Bundle()
            return androidx.fragment.app.Fragment.instantiate(context, AssureListHuozhuFragment::class.java.name, args)
                    as AssureListHuozhuFragment
        }
    }

    override fun getLayout(): Int {
        return com.zczy.cargo_owner.R.layout.order_common_fragment_new
    }

    override fun bindView(view: View, bundle: Bundle?) {
        val emptyView = CommEmptyView.creatorDef(context)
        swipe_refresh_more_layout.apply {
            setAdapter(mAdapter, true)
            setEmptyView(emptyView)
            addItemDecorationSize(dp2px(7f))
            addOnItemListener(onItemClickListener)
            setOnLoadListener(object : OnLoadingListener {
                override fun onRefreshUI(nowPage: Int) {
                    viewModel?.quesyList(nowPage,"1")
                }

                override fun onLoadMoreUI(nowPage: Int) {
                    viewModel?.quesyList(nowPage,"1")
                }
            })
        }
    }

    override fun initData() {
        swipe_refresh_more_layout.onAutoRefresh()
    }

    private val onItemClickListener = object : OnItemClickListener() {
        override fun onSimpleItemClick(adapter: BaseQuickAdapter<*, *>, view: View, position: Int) {
            val data = adapter.getItem(position) as Assure?
            AssureDetailActivity.start(context as Activity?, data!!.getOrderId(),"0")
        }

        override fun onItemChildClick(adapter: BaseQuickAdapter<*, *>, view: View, position: Int) {
            super.onItemChildClick(adapter, view, position)

        }
    }

    @LiveDataMatch
    open fun onPageList(data: PageList<Assure>?) {
        swipe_refresh_more_layout.onRefreshCompale(data)
    }

}