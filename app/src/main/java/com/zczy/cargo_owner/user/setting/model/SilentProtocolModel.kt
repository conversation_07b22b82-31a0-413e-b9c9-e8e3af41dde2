package com.zczy.cargo_owner.user.setting.model
import com.sfh.lib.exception.HandleException
import com.sfh.lib.mvvm.service.BaseViewModel
import com.sfh.lib.rx.IResult
import com.zczy.certification.carrier.req.QueryPersonalInfoByUserId
import com.zczy.certification.carrier.req.ReqQueryPersonalInfoByUserId
import com.zczy.certification.carrier.req.ReqSubmitPersonalDownLoad
import com.zczy.comm.CommServer
import com.zczy.comm.http.entity.BaseRsp
import com.zczy.comm.http.entity.ResultData

/**
 * @description
 * @date 15:21 5/25/20
 * <AUTHOR>
 * @since 1.0
 **/
class SilentProtocolModel : BaseViewModel() {


    fun queryPersonalInfoByUserId() {
        execute(ReqQueryPersonalInfoByUserId(userId = CommServer.getUserServer().login.userId), object : IResult<BaseRsp<QueryPersonalInfoByUserId>> {
            override fun onSuccess(p0: BaseRsp<QueryPersonalInfoByUserId>) {
                if (p0.success()) {
                    setValue("queryPersonalInfoByUserIdSuccess", p0.data)
                }
            }
            override fun onFail(p0: HandleException) {
                showDialogToast(p0.msg)
            }
        })
    }

    fun submitPersonalDownLoad(source:String,email: String,customerName: String,mobile:String,content:String) {
        execute(ReqSubmitPersonalDownLoad(
            source = source,
            email = email,
            customerName = customerName,
            mobile = mobile,
            content = content
        ), object : IResult<BaseRsp<ResultData>> {
            override fun onSuccess(p0: BaseRsp<ResultData>) {
                if (p0.success()) {
                    setValue("SubmitPersonalDownLoadSuccess", p0)
                }
            }
            override fun onFail(p0: HandleException) {
                showDialogToast(p0.msg)
            }
        })
    }


}