package com.zczy.cargo_owner.user.overdue.adapter

import com.chad.library.adapter.base.BaseQuickAdapter
import com.chad.library.adapter.base.BaseViewHolder
import com.zczy.cargo_owner.R
import com.zczy.cargo_owner.user.overdue.req.RspQueryUserExpireLicenseB
import com.zczy.cargo_owner.user.overdue.req.showLicenseTypeDes
import com.zczy.cargo_owner.user.overdue.req.showTitle1
import com.zczy.cargo_owner.user.overdue.widget.ComCertificationForDriver

/**
 * 功能描述: 证件过期
 * <AUTHOR>
 * @date 2022/10/13-17:13
 */

class CysExpiredCertificateManagementAdapter : BaseQuickAdapter<RspQueryUserExpireLicenseB, BaseViewHolder>(R.layout.cys_expired_certificate_management_item) {

    override fun convert(helper: BaseViewHolder?, item: RspQueryUserExpireLicenseB?) {
        helper?.apply {
            item?.let {
                val view = getView<ComCertificationForDriver>(R.id.comCertificationForDriverView)
                view.setTvTitle1(it.licenseName)
                    .setIvStatusVisible(true)
                    .setImgVisible2(false)
                    .setCl2Bg()
                    .setLicenseStatus()
                    .setTvDoubt(false)
                    .setGoFinishVisible(false)
                    .setTvDescribe(it.showLicenseTypeDes())
                    .setClTop1Visible(item.showTitle1())
                    .setTvTitle(item.title)
                    .setTvIsItRequiredText(null)
                    .setEmptyViewVisible(item.showTitle1())

                // 0：待上传；1：审核中；  2：审核成功；  3：审核失败
                when (it.licenseState) {
                    "1" -> {
                        view.setImageV3(R.drawable.certification_wait_audit)
                            .setImgVisible3(false)
                            .setTvRightTextVisible(false)
                    }
                    "0" -> {
                        view.setImageV3(R.drawable.certification_wait_perfect)
                            .setImgVisible3(true)
                            .setTvRightTextVisible(true)
                            .setTvRightText("去完善")
                    }
                    "3" -> {
                        view.setImageV3(R.drawable.certification_carrier_no_pass)
                            .setImgVisible3(true)
                            .setTvRightTextVisible(true)
                            .setTvRightText("重新上传")
                            .setViewBg2()
                    }
                    else -> {
                        view.setImageV3(R.drawable.certification_carrier_pass)
                            .setImgVisible3(false)
                            .setTvRightTextVisible(false)
                    }
                }
            }
        }
    }
}