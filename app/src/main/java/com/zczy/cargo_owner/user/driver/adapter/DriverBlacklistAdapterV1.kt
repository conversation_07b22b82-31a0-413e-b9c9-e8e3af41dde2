package com.zczy.cargo_owner.user.driver.adapter

import android.graphics.Color
import com.chad.library.adapter.base.BaseQuickAdapter
import com.chad.library.adapter.base.BaseViewHolder
import com.zczy.cargo_owner.R
import com.zczy.cargo_owner.user.driver.model.EDriverBlack

/**
 *@Desc 黑名单司机
 *@User ssp
 *@Date 2023/7/19-10:31
 */
class DriverBlacklistAdapterV1 : BaseQuickAdapter<EDriverBlack, BaseViewHolder>(R.layout.driver_black_list_adapter_item_v1) {
    override fun convert(helper: BaseViewHolder, item: EDriverBlack) {
        helper.setText(R.id.tv_name, item.memberName)
            .setText(R.id.tv_phone, item.mobile)
            .addOnClickListener(R.id.tv_del)
            .addOnClickListener(R.id.tv_check)
        when (item.blackStatus) {
            "1" -> {
                helper.setImageResource(R.id.iv_status, R.drawable.ic_remove)
                    .setGone(R.id.tv_del, false)
            }

            else -> {
                helper.setImageResource(R.id.iv_status, R.drawable.ic_normal)
                    .setText(R.id.tv_del, "移除")
                    .setGone(R.id.tv_del, true)
            }
        }
        when (item.userType) {
            "2" -> {
                //司机
                helper.setBackgroundRes(R.id.tvUserType, R.drawable.e0f1ff_2corners)
                    .setText(R.id.tvUserType, "个体司机")
                    .setTextColor(R.id.tvUserType, Color.parseColor("#178ADD"))
            }

            "3" -> {
                //物流企业
                helper.setBackgroundRes(R.id.tvUserType, R.drawable.ffebca_2corners)
                    .setText(R.id.tvUserType, "物流企业")
                    .setTextColor(R.id.tvUserType, Color.parseColor("#D98700"))
            }

            "10" -> {
                //车老板
                helper.setBackgroundRes(R.id.tvUserType, R.drawable.e0f5e2_2corners)
                    .setText(R.id.tvUserType, "车老板")
                    .setTextColor(R.id.tvUserType, Color.parseColor("#629B84"))
            }
        }
    }
}
