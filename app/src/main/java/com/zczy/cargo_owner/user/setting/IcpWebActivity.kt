package com.zczy.cargo_owner.user.setting

import android.content.Context
import android.content.Intent
import android.os.Bundle
import com.sfh.lib.mvvm.service.BaseViewModel
import com.zczy.cargo_owner.R
import com.zczy.comm.X5BaseJavascriptInterface
import com.zczy.comm.ui.BaseActivity
import kotlinx.android.synthetic.main.icp_web_activity.*


/*=============================================================================================
 * 功能描述:
 *---------------------------------------------------------------------------------------------
 *  涉及业务 & 更新说明:
 *
 *
 *--------------------------------------------------------------------------------------------
 *  <AUTHOR>  on  2024/5/8
 *  @Company    中储智运科技股份有限公司
 *  @Copyright （版权）中储南京智慧物流科技有限公司所有                                                                                        *
 *=============================================================================================*/
class IcpWebActivity : BaseActivity<BaseViewModel>() {

    private val eUrl by lazy { intent.getStringExtra(EXTRA_URL) }

    companion object {
        private const val EXTRA_URL = "extra_url"

        @JvmStatic
        fun start(context: Context, url: String) {
            val intent = Intent(context, IcpWebActivity::class.java)
            intent.putExtra(EXTRA_URL, url)
            context.startActivity(intent)
        }
    }

    override fun getLayout(): Int = R.layout.icp_web_activity

    override fun bindView(bundle: Bundle?) {
        initHardwareAccelerate()
        initWebView()
    }

    /**
     * 启用硬件加速
     */
    private fun initHardwareAccelerate() {
        try {
            if (android.os.Build.VERSION.SDK_INT >= 11) {
                window.setFlags(
                    android.view.WindowManager.LayoutParams.FLAG_HARDWARE_ACCELERATED,
                    android.view.WindowManager.LayoutParams.FLAG_HARDWARE_ACCELERATED
                )
            }
        } catch (e: Exception) {
            e.printStackTrace()
        }
    }

    private fun initWebView() {
        val webViewSettings = X5WebView.settings
        val baseAgent = webViewSettings.userAgentString
        webViewSettings.setUserAgent("$baseAgent;app/ANDROID")
        X5WebView.addJavascriptInterface(jsUserInfoInterface, "android")
    }

    override fun initData() {
        X5WebView.loadUrl(eUrl)
    }

    override fun onDestroy() {
        //释放资源
        jsUserInfoInterface.destroy()
        if (X5WebView != null) {
            X5WebView.destroy()
        }
        super.onDestroy()
    }

    private val jsUserInfoInterface = object : X5BaseJavascriptInterface(this) {

    }
}