package com.zczy.cargo_owner.user.integral

import android.text.TextUtils
import android.widget.ImageView
import android.widget.RelativeLayout
import android.widget.TextView
import com.chad.library.adapter.base.BaseMultiItemQuickAdapter
import com.chad.library.adapter.base.BaseViewHolder
import com.zczy.cargo_owner.R
import com.zczy.comm.utils.getResColor
import com.zczy.cargo_owner.user.integral.req.SignCycle

class UserSignInAdapter :
        BaseMultiItemQuickAdapter<SignCycle, BaseViewHolder>(null) {
    init {
        addItemType(TYPE_COMM, R.layout.user_singin_adapter) // 通用样式
        addItemType(TYPE_END, R.layout.user_singin_adapter2) // 通用样式
    }

    override fun convert(helper: BaseViewHolder, item: SignCycle) {
        when (helper.itemViewType) {
            TYPE_COMM -> {
                convertComm(helper, item)
            }
            TYPE_END -> {
                convertFriend(helper, item)
            }
        }
    }

    /** 通用样式 */
    private fun convertComm(helper: BaseViewHolder, item: SignCycle) {
        helper.setText(R.id.tv_day_time, item.navText)
        helper.setText(R.id.tv_integral, item.integral + "积分")
//        val ivIntegral = helper.getView<ImageView>(R.id.iv_integral)
        val rlRootView = helper.getView<RelativeLayout>(R.id.rl_root_view)
        val tvDayTime = helper.getView<TextView>(R.id.tv_day_time)
        val tvIntegral = helper.getView<TextView>(R.id.tv_integral)
        if (TextUtils.equals(item.isSigned, "1")) {
            rlRootView.setBackgroundResource(R.drawable.base_ui_shape_ff6a69_solid_6radius)
            tvDayTime.setTextColor(getResColor(R.color.white))
            tvIntegral.setTextColor(getResColor(R.color.white))
        } else {
            rlRootView.setBackgroundResource(R.drawable.base_ui_shape_eff0f3_solid_6radius)
            tvDayTime.setTextColor(getResColor(R.color.text_66))
            tvIntegral.setTextColor(getResColor(R.color.text_99))
        }
    }

    private fun convertFriend(helper: BaseViewHolder, item: SignCycle) {
        helper.setText(R.id.tv_day_time, item.navText)
        helper.setText(R.id.tv_integral, item.integral + "积分")
//        val ivIntegral = helper.getView<ImageView>(R.id.iv_integral)
        val rlRootView = helper.getView<RelativeLayout>(R.id.rl_root_view)
        val tvDayTime = helper.getView<TextView>(R.id.tv_day_time)
        val tvIntegral = helper.getView<TextView>(R.id.tv_integral)
        if (TextUtils.equals(item.isSigned, "1")) {
            rlRootView.setBackgroundResource(R.drawable.base_ui_shape_ff6a69_solid_6radius)
            tvDayTime.setTextColor(getResColor(R.color.white))
            tvIntegral.setTextColor(getResColor(R.color.white))
        } else {
            rlRootView.setBackgroundResource(R.drawable.base_ui_shape_eff0f3_solid_6radius)
            tvDayTime.setTextColor(getResColor(R.color.text_66))
            tvIntegral.setTextColor(getResColor(R.color.text_99))
        }
    }

    companion object {
        const val TYPE_COMM = 0 // 通用样式
        const val TYPE_END = 1 // informType = 2 好友申请
    }
}