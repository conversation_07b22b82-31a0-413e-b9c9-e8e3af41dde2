package com.zczy.cargo_owner.user.satisfaction.modle

import com.sfh.lib.exception.HandleException
import com.sfh.lib.mvvm.service.BaseViewModel
import com.sfh.lib.rx.IResult
import com.zczy.cargo_owner.user.satisfaction.req.ReqSatisfactionEvaluation
import com.zczy.comm.http.entity.BaseRsp
import com.zczy.comm.http.entity.PageList
import com.zczy.cargo_owner.user.satisfaction.rsp.RspSatisfactionEvaluation

/**
 *    author : Ssp
 *    date   : 2019/7/5 13:39
 *    desc   : 客服满意度评价
 */
class SatisfactionEvaluationModel : BaseViewModel() {

    fun queryData() {
        execute(ReqSatisfactionEvaluation(), object : IResult<BaseRsp<PageList<RspSatisfactionEvaluation>>> {
            override fun onSuccess(t: BaseRsp<PageList<RspSatisfactionEvaluation>>) {
                if (t.success()) {
                    setValue("onSuccess", t.data)
                } else {
                    setValue("onError")
                }
            }

            override fun onFail(e: HandleException) {
                setValue("onError")
            }
        })
    }
}