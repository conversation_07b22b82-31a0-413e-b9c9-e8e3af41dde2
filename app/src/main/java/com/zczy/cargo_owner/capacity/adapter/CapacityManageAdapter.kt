package com.zczy.cargo_owner.capacity.adapter

import com.chad.library.adapter.base.BaseQuickAdapter
import com.chad.library.adapter.base.BaseViewHolder
import com.zczy.cargo_owner.R
import com.zczy.cargo_owner.capacity.req.RspQueryConsignorSpecifyVehicleConfigList
import com.zczy.comm.utils.ex.isTrue
import kotlinx.android.synthetic.main.capacity_manage_item.view.ivCheck

/**
 * 类描述：原有运力管理列表
 * 作者：ssp
 * 创建时间：2024/5/13
 */

class CapacityManageAdapter : BaseQuickAdapter<RspQueryConsignorSpecifyVehicleConfigList, BaseViewHolder>(R.layout.capacity_manage_item) {
    override fun convert(helper: BaseViewHolder, item: RspQueryConsignorSpecifyVehicleConfigList) {
        helper.setText(R.id.tv1, item.plateNumber)
        helper.itemView.ivCheck.isSelected = item.configState.isTrue
        helper.addOnClickListener(R.id.ivCheck)
    }
}