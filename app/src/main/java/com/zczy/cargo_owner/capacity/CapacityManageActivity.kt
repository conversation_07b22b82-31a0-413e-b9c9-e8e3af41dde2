package com.zczy.cargo_owner.capacity

import android.content.Context
import android.content.Intent
import android.os.Bundle
import android.view.KeyEvent
import android.view.View
import android.view.inputmethod.EditorInfo
import android.widget.TextView
import androidx.recyclerview.widget.LinearLayoutManager
import com.sfh.lib.event.RxBusEvent
import com.sfh.lib.mvvm.annotation.LiveDataMatch
import com.sfh.lib.mvvm.service.BaseViewModel
import com.zczy.cargo_owner.R
import com.zczy.cargo_owner.capacity.adapter.CapacityManageAdapter
import com.zczy.cargo_owner.capacity.model.CapacityManageModel
import com.zczy.cargo_owner.capacity.req.ReqQueryConsignorSpecifyVehicleConfigList
import com.zczy.cargo_owner.capacity.req.ReqUpdateConsignorSpecifyVehicleConfigState
import com.zczy.cargo_owner.capacity.req.RspQueryConsignorSpecifyVehicleConfigList
import com.zczy.cargo_owner.capacity.req.RxBusAddConsignorEvent
import com.zczy.cargo_owner.libcomm.widget.SearchLayout
import com.zczy.comm.http.entity.PageList
import com.zczy.comm.ui.BaseActivity
import com.zczy.comm.utils.ex.isNull
import kotlinx.android.synthetic.main.capacity_manage_activty.*

/**
 * 类描述：原有运力管理
 * 作者：ssp
 * 创建时间：2024/5/13
 */
class CapacityManageActivity : BaseActivity<BaseViewModel>() {
    private val mAdapter = CapacityManageAdapter()

    companion object {

        @JvmStatic
        fun jumpPage(context: Context?) {
            val intent = Intent(context, CapacityManageActivity::class.java)
            context?.startActivity(intent)
        }
    }

    override fun getLayout(): Int {
        return R.layout.capacity_manage_activty
    }

    override fun bindView(bundle: Bundle?) {
        appToolbar.setRightOnClickListener {
            CapacityAddActivity.jumpPage(context = this@CapacityManageActivity)
        }
        searchLayout.setListener(object : SearchLayout.SearchListener {
            override fun onSearch(content: String) {
                swipeRefreshMoreLayout.onAutoRefresh()
            }
        })
        swipeRefreshMoreLayout.apply {
            setAdapter(mAdapter, true)
            setLayoutManager(LinearLayoutManager(this@CapacityManageActivity))
            addOnItemChildClickListener { _, view, position ->
                val item = mAdapter.data[position]
                when (view.id) {
                    R.id.ivCheck -> {
                        getViewModel(CapacityManageModel::class.java).updateConsignorSpecifyVehicleConfigState(
                            req = ReqUpdateConsignorSpecifyVehicleConfigState(
                                configId = item.configId,
                                configState = when (item.configState) {
                                    "1" -> {
                                        "0"
                                    }

                                    else -> {
                                        "1"
                                    }
                                }
                            )
                        ) {
                            runOnUiThread {
                                item.configState = when (item.configState) {
                                    "1" -> {
                                        "0"
                                    }

                                    else -> {
                                        "1"
                                    }
                                }
                                mAdapter.notifyItemChanged(position)
                            }
                        }
                    }
                }
            }
            setOnLoadListener2 {
                getViewModel(CapacityManageModel::class.java).queryList(
                    req = ReqQueryConsignorSpecifyVehicleConfigList(
                        nowPage = it,
                        plateNumber = searchLayout.getEditContent().ifEmpty { null }
                    )
                )
            }
            onAutoRefresh()
        }
    }

    override fun initData() {

    }

    @LiveDataMatch
    open fun onQueryListSuccess(data: PageList<RspQueryConsignorSpecifyVehicleConfigList>?) {
        if (data.isNull) {
            swipeRefreshMoreLayout.onLoadMoreFail()
        } else {
            swipeRefreshMoreLayout.onRefreshCompale(data)
        }
    }

    @RxBusEvent(from = "新增")
    open fun onAddSuccess(data: RxBusAddConsignorEvent) {
        if (data.success) {
            swipeRefreshMoreLayout.onAutoRefresh()
        }
    }
}