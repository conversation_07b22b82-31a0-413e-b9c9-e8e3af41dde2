package com.zczy.cargo_owner.capacity

import android.content.Context
import android.content.Intent
import android.os.Bundle
import android.view.View
import android.widget.TextView
import androidx.recyclerview.widget.LinearLayoutManager
import com.sfh.lib.event.RxBusEventManager
import com.sfh.lib.mvvm.service.BaseViewModel
import com.zczy.cargo_owner.R
import com.zczy.cargo_owner.capacity.adapter.CapacityAddAdapter
import com.zczy.cargo_owner.capacity.model.CapacityManageModel
import com.zczy.cargo_owner.capacity.req.ReqAddConsignorSpecifyVehicleConfig
import com.zczy.cargo_owner.capacity.req.RspCapacityAdd
import com.zczy.cargo_owner.capacity.req.RxBusAddConsignorEvent
import com.zczy.comm.ui.BaseActivity
import com.zczy.comm.utils.ex.toCommaString
import com.zczy.comm.utils.ex.yes
import kotlinx.android.synthetic.main.capacity_add_activty.*

/**
 * 类描述：
 * 作者：ssp
 * 创建时间：2024/5/13
 */
class CapacityAddActivity : BaseActivity<BaseViewModel>() {
    private val mAdapter = CapacityAddAdapter()

    companion object {

        @JvmStatic
        fun jumpPage(context: Context?) {
            val intent = Intent(context, CapacityAddActivity::class.java)
            context?.startActivity(intent)
        }
    }

    override fun getLayout(): Int {
        return R.layout.capacity_add_activty
    }

    override fun bindView(bundle: Bundle?) {
        mAdapter.apply {
            bindToRecyclerView(recyclerView)
            setOnItemChildClickListener { _, view, position ->
                when (view.id) {
                    R.id.tvDelete -> {
                        //删除
                        remove(position)
                    }
                }
            }
        }
        recyclerView.apply {
            layoutManager = LinearLayoutManager(this@CapacityAddActivity)
            adapter = mAdapter
        }
        mAdapter.addData(RspCapacityAdd())
        val footerView = layoutInflater.inflate(R.layout.capacity_add_footer_view, null)
        footerView.findViewById<TextView>(R.id.tvAdd).setOnClickListener {
            //增加条目
            val size = mAdapter.data.size
            if (size>=20){
                showDialogToast("最多同时添加20条数据")
                return@setOnClickListener
            }
            mAdapter.addData(RspCapacityAdd())
        }
        mAdapter.addFooterView(footerView)
        bindClickEvent(tvOk)
    }

    override fun onSingleClick(v: View) {
        super.onSingleClick(v)
        when (v.id) {

            R.id.tvOk -> {
                //提交
                checkAll().yes {
                    getViewModel(CapacityManageModel::class.java).addConsignorSpecifyVehicleConfig(
                        req = ReqAddConsignorSpecifyVehicleConfig(
                            plateNumbers = mAdapter.data.toCommaString { it.plateNumber?:"" }
                        )
                    ) {
                        runOnUiThread {
                            RxBusEventManager.postEvent(RxBusAddConsignorEvent(success = true))
                            finish()
                        }
                    }
                }
            }
        }
    }

    override fun initData() {

    }

    private fun checkAll(): Boolean {
        mAdapter.data.forEachIndexed { index, s ->
            if (s.plateNumber.isNullOrEmpty()) {
                showDialogToast("车牌号不能为空！")
                recyclerView.scrollToPosition(index)
                return false
            }
        }
        return true
    }
}