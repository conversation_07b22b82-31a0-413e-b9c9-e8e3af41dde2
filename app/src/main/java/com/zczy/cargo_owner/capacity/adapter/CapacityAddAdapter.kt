package com.zczy.cargo_owner.capacity.adapter

import android.text.Editable
import android.text.TextWatcher
import com.chad.library.adapter.base.BaseQuickAdapter
import com.chad.library.adapter.base.BaseViewHolder
import com.zczy.cargo_owner.R
import com.zczy.cargo_owner.capacity.req.RspCapacityAdd
import kotlinx.android.synthetic.main.capacity_add_item.view.etContent

/**
 * 类描述：原有运力管理新增
 * 作者：ssp
 * 创建时间：2024/5/13
 */

class CapacityAddAdapter : BaseQuickAdapter<RspCapacityAdd, BaseViewHolder>(R.layout.capacity_add_item) {

    override fun convert(helper: BaseViewHolder, item: RspCapacityAdd) {
        val editText = helper.itemView.etContent
        editText.setText(item.plateNumber ?: "")
        editText.tag = item
        editText.addTextChangedListener(object : TextWatcher {
            override fun beforeTextChanged(p0: CharSequence?, p1: Int, p2: Int, p3: Int) {

            }

            override fun onTextChanged(p0: CharSequence, p1: Int, p2: Int, p3: Int) {
                val tag = editText.etContent.tag
                if (tag is RspCapacityAdd) {
                    tag.plateNumber = p0.toString()
                }
            }

            override fun afterTextChanged(p0: Editable?) {
            }
        })
        helper.addOnClickListener(R.id.tvDelete)
    }
}