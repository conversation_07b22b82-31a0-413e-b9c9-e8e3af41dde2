package com.zczy.cargo_owner.offline.model

import android.content.Context
import com.sfh.lib.AppCacheManager
import com.sfh.lib.exception.HandleException
import com.sfh.lib.http.down.HttpDownHelper
import com.sfh.lib.mvvm.service.BaseViewModel
import com.sfh.lib.rx.IResult
import com.sfh.lib.rx.IResultSuccess
import com.sfh.lib.ui.dialog.DialogBuilder
import com.zczy.cargo_owner.deliver.main.req.ReqQueryCarrierBiddingOrderList2
import com.zczy.cargo_owner.deliver.main.req.ReqQueryConsignorDepositInfo2
import com.zczy.cargo_owner.deliver.main.req.RspQueryCarrierBiddingOrderItem
import com.zczy.cargo_owner.order.detail.bean.ContractToPdf
import com.zczy.cargo_owner.order.detail.req.ReqContractOfflineToPdf
import com.zczy.cargo_owner.order.util.DownloadUtil
import com.zczy.comm.http.entity.BaseRsp
import com.zczy.comm.http.entity.PageList
import com.zczy.comm.http.entity.ResultData
import io.reactivex.Observable
import java.io.File

/**
 * 功能描述: 运输计划
 * <AUTHOR>
 * @date 2021/10/21-11:34
 */

class OfflineModel : BaseViewModel() {

    fun queryOfflineListData(req: OfflineReq) {
        showLoading(true)
        execute(req, object : IResult<BaseRsp<PageList<OfflineResp>>> {
            override fun onSuccess(it: BaseRsp<PageList<OfflineResp>>) {
                hideLoading()
                if (it.success()) {
                    setValue("onQueryOfflineListDataSuccess", it.data)
                } else {
                    showDialogToast(it.msg)
                }
            }

            override fun onFail(it: HandleException) {
                hideLoading()
                showToast(it.msg)
            }
        })
    }

    fun deleteOrder(req: DeleteOrderReq) {
        showLoading(true)
        execute(req, object : IResult<BaseRsp<ResultData>> {
            override fun onSuccess(it: BaseRsp<ResultData>) {
                hideLoading()
                if (it.success()) {
                    setValue("deleteOrderSuccess", it.data)
                } else {
                    showDialogToast(it.msg)
                }
            }

            override fun onFail(it: HandleException) {
                hideLoading()
                showToast(it.msg)
            }
        })
    }


    /**
     * 查询下载合同地址
     * @return
     */
    fun addContract(orderId :String) {
        this.execute<BaseRsp<ContractToPdf>>(true, ReqContractOfflineToPdf(orderId), object :
            IResult<BaseRsp<ContractToPdf>> {
            @Throws(Exception::class)
            override fun onSuccess(rspBase: BaseRsp<ContractToPdf>) {
                if (rspBase.success()) {
                    setValue("addContractSuccess", rspBase.data)
                } else {
                    showDialogToast(rspBase.msg)
                }
            }
            override fun onFail(e: HandleException) {
                showDialogToast(e.msg)
            }
        })
    }

    fun loadFile(url: String, fileName: String,context: Context) {

        DownloadUtil.downloadFile(context,url,fileName,"pdf")

        /*this.execute(true, Observable.just(url).map<String> { s ->
            var file = File(AppCacheManager.getFileCache(), fileName)
            file = HttpDownHelper.Builder(s).setTagFile(file).start()
            file.absolutePath
        }, object : IResult<String> {
            @Throws(Exception::class)
            override fun onSuccess(path: String) {
                setValue("downLoadSuccess", path)
            }

            override fun onFail(e: HandleException) {
                showDialogToast(e.message)
            }
        })*/
    }

    fun cancleOrder(req: CancleOrderReq) {
        showLoading(true)
        execute(req, object : IResult<BaseRsp<ResultData>> {
            override fun onSuccess(it: BaseRsp<ResultData>) {
                hideLoading()
                if (it.success()) {
                    setValue("cancleOrderSuccess", it.data)
                } else {
                    showDialogToast(it.msg)
                }
            }

            override fun onFail(it: HandleException) {
                hideLoading()
                showToast(it.msg)
            }
        })
    }
    fun queryList2(nowPage: Int, orderId: String) {
        execute(
            ReqQueryCarrierBiddingOrderList2(nowPage = nowPage, orderId = orderId),
            object : IResult<BaseRsp<PageList<RspQueryCarrierBiddingOrderItem>>> {
                override fun onSuccess(baseRsp: BaseRsp<PageList<RspQueryCarrierBiddingOrderItem>>) {
                    if (baseRsp.success()) {
                        setValue("onQueryListSuccess2", baseRsp.data)
                    } else {
                        setValue("onQueryListSuccess2", null)
                        showDialogToast(baseRsp.msg)
                    }
                }

                override fun onFail(e: HandleException) {
                    showDialogToast(e.msg)
                    setValue("onQueryListSuccess2", null)
                }
            })
    }

    // 选择承运方
    fun queryConsignorDepositInfo2(expectId: String, orderId: String, seeMoney: String) {
        execute(true,
            ReqQueryConsignorDepositInfo2(
                expectId = expectId,
                orderId = orderId,
                seeMoney = seeMoney
            ),
            IResultSuccess<BaseRsp<ResultData>> { baseRsp ->
                if (baseRsp.success()) {
                    val dialogBuilder = DialogBuilder()
                    dialogBuilder.setHideCancel(true)
                        .setMessage(baseRsp.msg)
                        .setOkListener { dialogInterface, _ ->
                            dialogInterface.dismiss()
                            setValue("onConsignorSelectCarrier")
                        }
                    showDialog(dialogBuilder)
                } else {
                    showDialogToast(baseRsp.msg)
                }
            })
    }



    fun agreeCancelOrderTender(req: AgreeCancelOrderTenderReq) {
        showLoading(true)
        execute(req, object : IResult<BaseRsp<ResultData>> {
            override fun onSuccess(it: BaseRsp<ResultData>) {
                hideLoading()
                if (it.success()) {
                    showToast(it.msg)
                    setValue("agreeCancelOrderTenderSuccess", it.data)
                } else {
                    showDialogToast(it.msg)
                }
            }

            override fun onFail(it: HandleException) {
                hideLoading()
                showToast(it.msg)
            }
        })
    }
}