package com.zczy.cargo_owner.offline.req

import com.zczy.cargo_owner.deliver.addorder.req.normal.Rsp55GetSuccessImportNum
import com.zczy.comm.http.entity.BaseNewRequest
import com.zczy.comm.http.entity.BaseRsp

/**
 * 功能描述: 线下专区实时获取已成功导入单子条数
 * <AUTHOR>
 * @date 2023/3/7-9:40
 */

class Req56GetSuccessImportNum : BaseNewRequest<BaseRsp<Rsp55GetSuccessImportNum>>("oms-app/order/addConsignorTenderOrder/getSuccessImportNum")
