package com.zczy.cargo_owner.offline.publish

import android.annotation.SuppressLint
import android.app.Activity
import android.content.Context
import android.content.Intent
import android.graphics.Color
import android.os.Bundle
import android.text.InputType
import android.text.SpannableString
import android.text.SpannableStringBuilder
import android.text.Spanned
import android.text.TextUtils
import android.text.style.AbsoluteSizeSpan
import android.text.style.ForegroundColorSpan
import android.view.View
import com.sfh.lib.event.RxBusEventManager
import com.sfh.lib.mvvm.annotation.LiveDataMatch
import com.sfh.lib.mvvm.service.BaseViewModel
import com.sfh.lib.rx.ui.UtilRxView
import com.sfh.lib.ui.dialog.DialogBuilder
import com.sfh.lib.utils.UtilLog
import com.zczy.cargo_owner.R
import com.zczy.cargo_owner.deliver.addorder.JumpNewGoodsData
import com.zczy.cargo_owner.deliver.addorder.PolicyTipsData
import com.zczy.cargo_owner.deliver.addorder.bean.AppointCarrierCommInfo
import com.zczy.cargo_owner.deliver.addorder.bean.OrderReceiptInfo
import com.zczy.cargo_owner.deliver.addorder.bean.getAllWeight
import com.zczy.cargo_owner.deliver.addorder.bean.normal.oilGesEmpty
import com.zczy.cargo_owner.deliver.addorder.dialog.DeliverNewGoodsLossDialog
import com.zczy.cargo_owner.deliver.addorder.dialog.DeliverNewGoodsModelDialog
import com.zczy.cargo_owner.deliver.addorder.dialog.DeliverNewGoodsNormalDialog
import com.zczy.cargo_owner.deliver.addorder.dialog.DeliverNewGoodsPresetPublishDialog
import com.zczy.cargo_owner.deliver.addorder.dialog.DeliverNewGoodsReceiptDialog
import com.zczy.cargo_owner.deliver.addorder.dialog.DeliverNewGoodsSafeHintDialog
import com.zczy.cargo_owner.deliver.addorder.dialog.DeliverNewGoodsTimeDialogV2
import com.zczy.cargo_owner.deliver.addorder.dialog.DeliverOrderPreferredDialog
import com.zczy.cargo_owner.deliver.addorder.dialog.DeliverOrderSuccessImportNumDialog
import com.zczy.cargo_owner.deliver.addorder.req.RspGetPendingOrderIds
import com.zczy.cargo_owner.deliver.addorder.req.batch.RspAddResult
import com.zczy.cargo_owner.deliver.addorder.req.comm.OrderCommonRuleItem
import com.zczy.cargo_owner.deliver.addorder.req.comm.OrderCommonTonRuleItem
import com.zczy.cargo_owner.deliver.addorder.req.comm.Req37QueryOilRewardInfoByPublish
import com.zczy.cargo_owner.deliver.addorder.req.comm.ReqQueryFlyingOrderControl
import com.zczy.cargo_owner.deliver.addorder.req.comm.ReqQueryTonRuleByCargoName
import com.zczy.cargo_owner.deliver.addorder.req.comm.Rsp23QueryMobileOrderCommonInfo
import com.zczy.cargo_owner.deliver.addorder.req.comm.Rsp33QueryCargoDespatchOrDeliveryInfo
import com.zczy.cargo_owner.deliver.addorder.req.comm.Rsp37QueryOilRewardInfoByPublish
import com.zczy.cargo_owner.deliver.addorder.req.comm.RspCheckAdvanceInfo
import com.zczy.cargo_owner.deliver.addorder.req.comm.RspOilGasConfigInfo
import com.zczy.cargo_owner.deliver.addorder.req.comm.RspQueryOrderPolicyInfo
import com.zczy.cargo_owner.deliver.addorder.req.comm.RspQueryOrderReceipt
import com.zczy.cargo_owner.deliver.addorder.req.comm.RspQueryTonRuleByCargoName
import com.zczy.cargo_owner.deliver.addorder.req.comm.formatRuleNameJSONArray
import com.zczy.cargo_owner.deliver.addorder.req.comm.getDeliverNewGoodsTimeData
import com.zczy.cargo_owner.deliver.addorder.req.comm.setData
import com.zczy.cargo_owner.deliver.addorder.req.fast.ex.getVehicleType
import com.zczy.cargo_owner.deliver.addorder.req.normal.JumpNormalData
import com.zczy.cargo_owner.deliver.addorder.req.normal.ReqAddOrderForSeniorConsignor2
import com.zczy.cargo_owner.deliver.addorder.req.normal.RspQueryMobileDepositInfoForOrder
import com.zczy.cargo_owner.deliver.addorder.req.normal.ex.checkFreightType
import com.zczy.cargo_owner.deliver.addorder.req.normal.ex.checkOrderMode
import com.zczy.cargo_owner.deliver.addorder.req.normal.ex.formatDriverMoney
import com.zczy.cargo_owner.deliver.addorder.req.normal.ex.formatOwnerMoney
import com.zczy.cargo_owner.deliver.addorder.req.normal.ex.formatRealBlockMoney
import com.zczy.cargo_owner.deliver.addorder.req.normal.ex.formatRealTotalMoney
import com.zczy.cargo_owner.deliver.addorder.req.normal.ex.getAddressEnd
import com.zczy.cargo_owner.deliver.addorder.req.normal.ex.getAddressStart
import com.zczy.cargo_owner.deliver.addorder.req.normal.ex.getAppointCarrierList
import com.zczy.cargo_owner.deliver.addorder.req.normal.ex.getAppointCarrierSocialList
import com.zczy.cargo_owner.deliver.addorder.req.normal.ex.getExpectTimeCalender
import com.zczy.cargo_owner.deliver.addorder.req.normal.ex.getGoodsDetail
import com.zczy.cargo_owner.deliver.addorder.req.normal.ex.getReceiptAddress
import com.zczy.cargo_owner.deliver.addorder.req.normal.ex.getReceiveDateCalender
import com.zczy.cargo_owner.deliver.addorder.req.normal.ex.getReceiveDateCalenderV2
import com.zczy.cargo_owner.deliver.addorder.req.normal.ex.getVehicleType
import com.zczy.cargo_owner.deliver.addorder.req.normal.ex.lookCarrierRankFlag
import com.zczy.cargo_owner.deliver.addorder.req.normal.ex.monitorFlag
import com.zczy.cargo_owner.deliver.addorder.req.normal.ex.orderCargoType
import com.zczy.cargo_owner.deliver.addorder.req.normal.ex.plus
import com.zczy.cargo_owner.deliver.addorder.req.normal.ex.priorSelectFlag
import com.zczy.cargo_owner.deliver.addorder.req.normal.ex.setAddressEnd
import com.zczy.cargo_owner.deliver.addorder.req.normal.ex.setAppointCarrierList
import com.zczy.cargo_owner.deliver.addorder.req.normal.ex.setDespatchTime
import com.zczy.cargo_owner.deliver.addorder.req.normal.ex.setDetailsData
import com.zczy.cargo_owner.deliver.addorder.req.normal.ex.setDetailsDataV1
import com.zczy.cargo_owner.deliver.addorder.req.normal.ex.setJumpData
import com.zczy.cargo_owner.deliver.addorder.req.normal.ex.setOidData
import com.zczy.cargo_owner.deliver.addorder.req.normal.ex.setReceiptAddress
import com.zczy.cargo_owner.deliver.addorder.req.normal.ex.setReceiptInfo
import com.zczy.cargo_owner.deliver.addorder.req.normal.ex.setRsp23QueryMobileOrderCommonInfo
import com.zczy.cargo_owner.deliver.addorder.req.normal.ex.setRspCheckAdvanceInfo
import com.zczy.cargo_owner.deliver.addorder.req.normal.ex.setVehicleType
import com.zczy.cargo_owner.deliver.addorder.req.normal.ex.urgentFlag
import com.zczy.cargo_owner.deliver.addorder.req.normal.ex2.checkDraft
import com.zczy.cargo_owner.deliver.addorder.req.normal.ex2.checkEnableOilFixedCredit
import com.zczy.cargo_owner.deliver.addorder.req.normal.ex2.checkEnablePolicyFlag
import com.zczy.cargo_owner.deliver.addorder.req.normal.ex2.checkEnablePolicyFlagV1
import com.zczy.cargo_owner.deliver.addorder.req.normal.ex2.checkEnableTotalAmount
import com.zczy.cargo_owner.deliver.addorder.req.normal.ex2.checkInit
import com.zczy.cargo_owner.deliver.addorder.req.normal.ex2.checkNew
import com.zczy.cargo_owner.deliver.addorder.ui.DeliverAddOrderFastTrackFragmentV2
import com.zczy.cargo_owner.deliver.addorder.ui.DeliverAddOrderMarkActivityV2
import com.zczy.cargo_owner.deliver.addorder.ui.DeliverAddOrderProjectNameActivity
import com.zczy.cargo_owner.deliver.addorder.ui.DeliverAddOrderZDYBHActivityV2
import com.zczy.cargo_owner.deliver.addorder.ui.DeliverAddOrderZDYBHActivityV3
import com.zczy.cargo_owner.deliver.addorder.ui.DeliverAddOrderZXHYQActivityV2
import com.zczy.cargo_owner.deliver.addorder.ui.DeliverGoodsDetailsActivity
import com.zczy.cargo_owner.deliver.addorder.ui.DeliverVehicleTypeActivity
import com.zczy.cargo_owner.deliver.addorder.widget.AddOrderAddressView
import com.zczy.cargo_owner.deliver.addorder.widget.AddOrderPolicyView
import com.zczy.cargo_owner.deliver.addorder.widget.AddOrderSocialView
import com.zczy.cargo_owner.deliver.addorder.widget.AddOrderTimeView
import com.zczy.cargo_owner.deliver.addorder.widget.AddOrderTotalAmountView
import com.zczy.cargo_owner.deliver.addorder.widget.InputViewEditV2
import com.zczy.cargo_owner.deliver.addorder.widget.OilCardView
import com.zczy.cargo_owner.deliver.addorder.widget.OilCardViewDeDaV2
import com.zczy.cargo_owner.deliver.address.consignor.DeliverAddressMainActivity
import com.zczy.cargo_owner.deliver.address.receipt.DeliverReceiptMainActivity
import com.zczy.cargo_owner.deliver.carrier.DeliverAppointCarrierActivity
import com.zczy.cargo_owner.libcomm.config.HttpConfig
import com.zczy.cargo_owner.libcomm.event.EventNewGoodsSuccess
import com.zczy.cargo_owner.offline.check.handleCheckResult
import com.zczy.cargo_owner.offline.model.DeliverAddOrderNormalModelV3
import com.zczy.cargo_owner.order.confirm.ReturnedOrderConfirmListActivity
import com.zczy.cargo_owner.order.express.OrderExpressMainActivity
import com.zczy.cargo_owner.order.settlement.SettlementApplicationListActivity
import com.zczy.comm.SpannableHepler
import com.zczy.comm.data.EAgreement
import com.zczy.comm.pluginserver.AMainServer
import com.zczy.comm.ui.FlyingSingleDialog
import com.zczy.comm.ui.OnFlyingSingleClickListener
import com.zczy.comm.utils.CommUtils
import com.zczy.comm.utils.PhoneUtil
import com.zczy.comm.utils.ex.isTrue
import com.zczy.comm.utils.ex.setVisible
import com.zczy.comm.utils.ex.toCommaString
import com.zczy.comm.utils.json.toJson
import com.zczy.comm.utils.json.toJsonObject
import com.zczy.comm.widget.dialog.ChooseDialogV1
import com.zczy.comm.widget.dialog.MenuDialogV1
import com.zczy.comm.widget.inputv2.InputViewCheckV2
import com.zczy.comm.widget.inputv2.InputViewClick
import com.zczy.comm.widget.inputv2.InputViewEdit
import com.zczy.comm.widget.pickerview.picker.TimePicker
import kotlinx.android.synthetic.main.deliver_add_order_normal_fragment_v3.add_order_policy_view
import kotlinx.android.synthetic.main.deliver_add_order_normal_fragment_v3.add_order_preferred_view
import kotlinx.android.synthetic.main.deliver_add_order_normal_fragment_v3.add_order_social_view
import kotlinx.android.synthetic.main.deliver_add_order_normal_fragment_v3.add_order_time_view
import kotlinx.android.synthetic.main.deliver_add_order_normal_fragment_v3.address_view
import kotlinx.android.synthetic.main.deliver_add_order_normal_fragment_v3.agreementView
import kotlinx.android.synthetic.main.deliver_add_order_normal_fragment_v3.btn_commit
import kotlinx.android.synthetic.main.deliver_add_order_normal_fragment_v3.btn_save
import kotlinx.android.synthetic.main.deliver_add_order_normal_fragment_v3.btn_save_and_commit
import kotlinx.android.synthetic.main.deliver_add_order_normal_fragment_v3.check_deliver_process
import kotlinx.android.synthetic.main.deliver_add_order_normal_fragment_v3.check_freight_type
import kotlinx.android.synthetic.main.deliver_add_order_normal_fragment_v3.check_order_mode
import kotlinx.android.synthetic.main.deliver_add_order_normal_fragment_v3.edit_contact_name
import kotlinx.android.synthetic.main.deliver_add_order_normal_fragment_v3.edit_contact_phone
import kotlinx.android.synthetic.main.deliver_add_order_normal_fragment_v3.et_order_num
import kotlinx.android.synthetic.main.deliver_add_order_normal_fragment_v3.inputExpectedFreightRate
import kotlinx.android.synthetic.main.deliver_add_order_normal_fragment_v3.input_advance_ratio
import kotlinx.android.synthetic.main.deliver_add_order_normal_fragment_v3.input_appoint_carrier
import kotlinx.android.synthetic.main.deliver_add_order_normal_fragment_v3.input_balance
import kotlinx.android.synthetic.main.deliver_add_order_normal_fragment_v3.input_goods_detail
import kotlinx.android.synthetic.main.deliver_add_order_normal_fragment_v3.input_jszxdh
import kotlinx.android.synthetic.main.deliver_add_order_normal_fragment_v3.input_lanbiaojia
import kotlinx.android.synthetic.main.deliver_add_order_normal_fragment_v3.input_monitor
import kotlinx.android.synthetic.main.deliver_add_order_normal_fragment_v3.input_offer_time_end
import kotlinx.android.synthetic.main.deliver_add_order_normal_fragment_v3.input_order_loss
import kotlinx.android.synthetic.main.deliver_add_order_normal_fragment_v3.input_order_mark
import kotlinx.android.synthetic.main.deliver_add_order_normal_fragment_v3.input_project_name
import kotlinx.android.synthetic.main.deliver_add_order_normal_fragment_v3.input_receipt
import kotlinx.android.synthetic.main.deliver_add_order_normal_fragment_v3.input_receipt_address
import kotlinx.android.synthetic.main.deliver_add_order_normal_fragment_v3.input_receipt_money
import kotlinx.android.synthetic.main.deliver_add_order_normal_fragment_v3.input_receive_time
import kotlinx.android.synthetic.main.deliver_add_order_normal_fragment_v3.input_rule
import kotlinx.android.synthetic.main.deliver_add_order_normal_fragment_v3.input_vehicle_type
import kotlinx.android.synthetic.main.deliver_add_order_normal_fragment_v3.input_waybill_type
import kotlinx.android.synthetic.main.deliver_add_order_normal_fragment_v3.input_zdybh
import kotlinx.android.synthetic.main.deliver_add_order_normal_fragment_v3.input_zdzxdh
import kotlinx.android.synthetic.main.deliver_add_order_normal_fragment_v3.input_zxhyq
import kotlinx.android.synthetic.main.deliver_add_order_normal_fragment_v3.look_ranking
import kotlinx.android.synthetic.main.deliver_add_order_normal_fragment_v3.oilCardView
import kotlinx.android.synthetic.main.deliver_add_order_normal_fragment_v3.oilCardViewDeDa
import kotlinx.android.synthetic.main.deliver_add_order_normal_fragment_v3.order_cargo_money_view
import kotlinx.android.synthetic.main.deliver_add_order_normal_fragment_v3.order_total_amount_view
import kotlinx.android.synthetic.main.deliver_add_order_normal_fragment_v3.tvExpectedFreightRate
import kotlinx.android.synthetic.main.deliver_add_order_normal_fragment_v3.tv_add_order
import kotlinx.android.synthetic.main.deliver_add_order_normal_fragment_v3.tv_subtraction_order
import kotlinx.android.synthetic.main.deliver_add_order_normal_fragment_v3.worry_or_not

/**
 * 功能描述: 线下专区发货
 * <AUTHOR>
 * @date 2023/4/4-15:14
 */
open class DeliverAddOrderNormalFragmentV3 :
    DeliverAddOrderNormalViewV2<DeliverAddOrderNormalModelV3>() {

    override val mData2 = ReqAddOrderForSeniorConsignor2()

    override val mJumpData by lazy {
        arguments?.getString(EXTRA_JUMP_DATA)?.toJsonObject(JumpNewGoodsData::class.java)
            ?: JumpNewGoodsData()
    }
    val outOrderNumber by lazy { arguments?.getString(OUT_ORDER_NUMBER) }

    // 初始化 信息
    override var mOrderCommonInfo = Rsp23QueryMobileOrderCommonInfo()
    override var mRspOilGasConfigInfo: RspOilGasConfigInfo = RspOilGasConfigInfo()

    /** 跳转过来的初始化数据 */
    override var initJump: Boolean = false

    /** 货物是否 可以购买 保险 true 不能买*/
    override var policyTipsData: PolicyTipsData = PolicyTipsData()
    override var mRspQueryTonRuleByCargoName = RspQueryTonRuleByCargoName() //亏涨吨集合

    private val mDeliverOrderSuccessImportNumDialog: DeliverOrderSuccessImportNumDialog =
        DeliverOrderSuccessImportNumDialog()

    override fun getLayout(): Int = R.layout.deliver_add_order_normal_fragment_v3

    override fun bindView(view: View, bundle: Bundle?) {
        mData2.orderInfo.batchPublishNum = "1"
        mData2.orderInfo.outOrderNumber = outOrderNumber
        initStepView()
        bindClickEvent(btn_save)
        bindClickEvent(btn_save_and_commit)
        bindClickEvent(btn_commit)
    }

    override fun initData() {
        UtilLog.e(TAG, "initData, mJumpData = ${mJumpData.toJson()}")
        if (mJumpData.orderId.isNotEmpty()) {
            // 发货是带数据的
            viewModel?.jumpToMobileSeniorConsignorUpdateOrder2(mJumpData)
        } else {
            // 发新货
            viewModel?.doNewGoodsInitRequestV3()
        }
    }

    override fun onSingleClick(v: View) {
        super.onSingleClick(v)
        when (v.id) {
            // 保存
            R.id.btn_save -> {
                val dialogBuilder = DialogBuilder()
                dialogBuilder.message =
                    "此运单为线下专区运单，成交后请与承运方线下交易，平台不承担该交易相关责任！"
                dialogBuilder.title = "温馨提示"
                dialogBuilder.okListener =
                    DialogBuilder.DialogInterface.OnClickListener { p0, _ ->
                        p0.dismiss()
                        val map = mData2.checkDraft(mOrderCommonInfo)
                        if (handleCheckResult(map)) {
                            viewModel?.saveDrafts(mData2, mJumpData.pageState)
                        }
                    }
                showDialog(dialogBuilder)
            }
            // 保存并发布
            R.id.btn_save_and_commit -> {
                val dialogBuilder = DialogBuilder()
                dialogBuilder.message =
                    "此运单为线下专区运单，成交后请与承运方线下交易，平台不承担该交易相关责任！"
                dialogBuilder.title = "温馨提示"
                dialogBuilder.okListener =
                    DialogBuilder.DialogInterface.OnClickListener { p0, _ ->
                        p0.dismiss()
                        val req = mData2.copy(orderInfo = mData2.orderInfo.copy())
                        val map = mData2.checkNew(mOrderCommonInfo)
                        if (handleCheckResult(map)) {
                            viewModel?.commit(req, mJumpData.pageState)
                            if (!mData2.orderInfo.batchPublishNum.isTrue) {
                                viewModel?.getSuccessImportNum()
                            }
                        }
                    }
                showDialog(dialogBuilder)
            }
            // 发布
            R.id.btn_commit -> {
                queryFlyingOrderControl {
                    val dialogBuilder = DialogBuilder()
                    dialogBuilder.message =
                        "此运单为线下专区运单，成交后请与承运方线下交易，平台不承担该交易相关责任！"
                    dialogBuilder.title = "温馨提示"
                    dialogBuilder.okListener =
                        DialogBuilder.DialogInterface.OnClickListener { p0, _ ->
                            p0.dismiss()
                            val req = mData2.copy(orderInfo = mData2.orderInfo.copy())
                            val map = mData2.checkNew(mOrderCommonInfo)
                            if (handleCheckResult(map)) {
                                viewModel?.commit(req, mJumpData.pageState)
                                if (!mData2.orderInfo.batchPublishNum.isTrue) {
                                    viewModel?.getSuccessImportNum()
                                }
                            }
                        }
                    showDialog(dialogBuilder)
                }
            }
        }
    }

    override fun onActivityResult(requestCode: Int, resultCode: Int, data: Intent?) {
        super.onActivityResult(requestCode, resultCode, data)
        if (resultCode == Activity.RESULT_OK) {
            when (requestCode) {
                // 普_从
                REQUEST_ADDRESS_START -> {
                    mData2.plus(DeliverAddressMainActivity.obtainData(data))
                    refreshAddress()
                    address_view.setStartWarning(false)
                }
                // 到
                REQUEST_ADDRESS_END -> {
                    mData2.setAddressEnd(DeliverAddressMainActivity.obtainData(data))
                    refreshAddress()
                    address_view.setEndWarning(false)
                }
                // 货物明细
                REQUEST_GOODS_DETAIL -> {
                    mData2.setDetailsData(DeliverGoodsDetailsActivity.obtainData(data))
                    order_total_amount_view.setEditEnable(true)
                    input_goods_detail.setWarning(false)
                    refreshGoodsDetail()
                    refreshTotalAmount()
                    refreshCargoMoney()
                    refreshAdvance()
                    refreshSdOilCard()
                    refreshSdOilCardDeDa()
                    // 查询亏涨吨规则
                    getTonRule()
                }
                // 车长车型
                REQUEST_VEHICLE_TYPE -> {
                    mData2.setVehicleType(DeliverVehicleTypeActivity.obtainData(data))
                    refreshTypeLength()
                    input_vehicle_type.setWarning(false)
                }
                // 指定承运人
                REQUEST_SHIPPER_CARRIER -> {
                    mData2.setAppointCarrierList(
                        data1 = DeliverAppointCarrierActivity.obtainData(data),
                        //                            data2 = DeliverAppointCarrierActivity.obtainGroupData(data),
                        data3 = DeliverAppointCarrierActivity.obtainSocialData(data),
                        orderCommonInfo = mOrderCommonInfo
                    )

                    refreshAppointCarrier()
                    refreshPreferredView() //优选专区
                    input_appoint_carrier.setWarning(false)
                }
                // 回单地址
                REQUEST_RECEIPT_ADDRESS -> {
                    mData2.setReceiptAddress(DeliverReceiptMainActivity.obtainData(data))
                    refreshReceipt()
                    input_receipt_address.setWarning(false)
                }
                // 装卸货要求
                REQUEST_ZXHYQ -> {
                    val other = DeliverAddOrderZXHYQActivityV2.obtainOtherV1(data)
                    mData2.loadFeeInfo = other
                    mData2.orderInfo.prompt = DeliverAddOrderZXHYQActivityV2.obtainOtherV2(data)
                    refreshZXHYQ()
                }
                // 自定义编号
                REQUEST_ZDYBH -> {
//                    if (mOrderCommonInfo.selfCommentSelectFlag.isTrue) {
//                        mData2.orderInfo.selfComment =
//                            DeliverAddOrderZDYBHActivityV3.obtainSelfComment(data)
//                    } else {
                        mData2.orderInfo.selfComment =
                            DeliverAddOrderZDYBHActivityV2.obtainSelfComment(data)
//                    }
                    refreshZDYBH()
                }
                // 运单标识
                REQUEST_ORDER_MARK -> {
                    mData2.orderInfo.orderMark = DeliverAddOrderMarkActivityV2.obtainOrderMark(data)
                    refreshOrderMark()
                }
                // 项目名称
                REQUEST_PROJECT_NAME -> {
                    mData2.orderInfo.projectName =
                        DeliverAddOrderProjectNameActivity.obtainProjectName(data)
                    refreshProjectName()
                }
            }
        }
    }

    @SuppressLint("SetTextI18n")
    private fun initStepView() {
        // 从 到
        address_view.listener = object : AddOrderAddressView.Listener {
            override fun onClickStart() {
                DeliverAddressMainActivity.start(
                    this@DeliverAddOrderNormalFragmentV3,
                    DeliverAddressMainActivity.TYPE_SENDER,
                    mData2.getAddressStart(),
                    REQUEST_ADDRESS_START,
                    true
                )
            }

            override fun onClickEnd() {
                DeliverAddressMainActivity.start(
                    this@DeliverAddOrderNormalFragmentV3,
                    DeliverAddressMainActivity.TYPE_RECEIVER,
                    mData2.getAddressEnd(),
                    REQUEST_ADDRESS_END,
                    true
                )
            }
        }
        // 项目名称
        input_waybill_type.setListener(object : InputViewClick.Listener() {
            override fun onClick(viewId: Int, view: InputViewClick, content: String) {
                MenuDialogV1.instance(listOf("大件", "小包", "普货"))
                    .setClick { s, _ ->
                        mData2.orderCargoType(s)
                        refreshOrderType()
                    }
                    .show(this@DeliverAddOrderNormalFragmentV3)
            }
        })
        // 货物明细
        input_goods_detail.setListener(object : InputViewClick.Listener() {
            override fun onClick(viewId: Int, view: InputViewClick, content: String) {
                DeliverGoodsDetailsActivity.start(
                    fragment = this@DeliverAddOrderNormalFragmentV3,
                    data = mData2.getGoodsDetail(),
                    maxNormalOrderWeight = mOrderCommonInfo.maxOrderWeight,
                    maxNormalOrderVolume = mOrderCommonInfo.maxOrderVolume,
                    unitShowFlag = mOrderCommonInfo.cargoStandardUnitShowFlag,
                    requestCode = REQUEST_GOODS_DETAIL,
                    offlineZone = true
                )
            }
        })
        // 车型车长
        input_vehicle_type.setListener(object : InputViewClick.Listener() {
            override fun onClick(viewId: Int, view: InputViewClick, content: String) {
                DeliverVehicleTypeActivity.start(
                    fragment = this@DeliverAddOrderNormalFragmentV3,
                    data = mData2.getVehicleType(),
                    showVehicleTypeList = mOrderCommonInfo.showRecommendVehicle.isTrue,
                    vehicleTypeList = mOrderCommonInfo.recommendVehicleList,
                    requestCode = REQUEST_VEHICLE_TYPE
                )
            }
        })
        // 装货时间
        add_order_time_view.listener = object : AddOrderTimeView.Listener {
            override fun onClickTime(startTime: String, endTime: String) {
                mData2.setDespatchTime(startTime, endTime)
                refreshDespatchTime()
            }

            override fun onClickInfo() {
                DeliverNewGoodsTimeDialogV2()
                    .setData(mOrderCommonInfo.getDeliverNewGoodsTimeData())
                    .show(this@DeliverAddOrderNormalFragmentV3)
            }
        }
        // 收货时间
        input_receive_time.setListener(object : InputViewClick.Listener() {
            override fun onClick(viewId: Int, view: InputViewClick, content: String) {
                DeliverNewGoodsNormalDialog.chooseTimeDialog(
                    context = context,
                    title = "收货时间",
                    data = mData2.getReceiveDateCalenderV2(),
                    timePickerType = TimePicker.TYPE_DATE or TimePicker.TYPE_HOUR,
                )
                {
                    mData2.orderInfo.receiveDate = it
                    refreshReceiveDate()
                }
            }
        })
        // 定价模式
        check_order_mode.setListener(object : InputViewCheckV2.Listener() {
            override fun onClickCheck(
                viewId: Int,
                view: InputViewCheckV2,
                check: Int,
                radioStr: String
            ): Boolean {
                mData2.checkOrderMode(check, mOrderCommonInfo)
                refreshCargoMoneyGuaranteeLeft("")
                refreshOrderModel()
                refreshTotalAmount()
                refreshCargoMoney()
                refreshLabiaojia() // 拦标价
                refreshRule() //自动成交规则
                refreshExpectTime()
                refreshSdOilCard()
                refreshSdOilCardDeDa()
                refreshInputExpectedFreightRate()
                // 查询货主赠保额度
                if (mData2.checkEnablePolicyFlag(mOrderCommonInfo).isEmpty()) {
                    viewModel?.queryOrderPolicyInfoChangeModel(
                        mData2.cargoList,
                        mOrderCommonInfo.giftMoney,
                        mData2.orderInfo.cargoMoney,
                        mData2.formatRealTotalMoney(),
                        mData2.cargoList.getAllWeight(),
                        mData2.orderInfo.changePolicyToCarrier,
                        mData2.orderAddressInfo
                    )
                }
                return true
            }

            override fun onClickTitleRight(viewId: Int, view: InputViewCheckV2) {
                super.onClickTitleRight(viewId, view)
                DeliverNewGoodsModelDialog().show(this@DeliverAddOrderNormalFragmentV3)
            }
        })
        // 拦标价
        val sb = SpannableStringBuilder()
        sb.append("拦标价(元) ")
        val span = SpannableString("含税")
        span.setSpan(
            ForegroundColorSpan(Color.parseColor("#666666")),
            0,
            span.length,
            Spanned.SPAN_INCLUSIVE_EXCLUSIVE
        )
        span.setSpan(AbsoluteSizeSpan(12, true), 0, span.length, Spanned.SPAN_EXCLUSIVE_EXCLUSIVE)
        sb.append(span)
        CommUtils.setEditTextInputType(input_lanbiaojia.editText, 4)
        input_lanbiaojia.setTitleSB(sb)
        input_lanbiaojia.setListener(object : InputViewEditV2.Listener() {
            override fun onTextChanged(viewId: Int, view: InputViewEditV2, s: String) {
                mData2.orderInfo.blockMoney = s
                input_lanbiaojia.setWarning(false)
                refreshOilRewardInfo()
            }

            override fun onClickTitleRight(viewId: Int, view: InputViewEditV2) {
                super.onClickTitleRight(viewId, view)
                val dialog = DialogBuilder()
                    .setHideCancel(true)
                    .setTitle("提示")
                    .setOkListener { dialogInterface, _ ->
                        dialogInterface.dismiss()
                    }
                    .setOKText("我知道了")
                    .setMessage("所有报价不能高于拦标价")
                showDialog(dialog)
            }
        })
        // 运费计算方式
        check_freight_type.setListener(object : InputViewCheckV2.Listener() {
            override fun onClickCheck(
                viewId: Int,
                view: InputViewCheckV2,
                check: Int,
                radioStr: String
            ): Boolean {
                mData2.checkFreightType(check)
                refreshTotalAmount()
                return true
            }
        })

        // 是否监控收发货流程
        check_deliver_process.setListener(object : InputViewClick.Listener() {
            override fun onClick(viewId: Int, view: InputViewClick, content: String) {
                MenuDialogV1.instance(listOf("否", "仅监控收货流程", "监控收发货流程"))
                    .setTitle("请选择")
                    .setClick { s, _ ->
                        mData2.monitorFlag(s)
                        refreshOrderProcess()
                    }
                    .show(this@DeliverAddOrderNormalFragmentV3)
            }
        })

        // 是否允许承运人查看自己的排名
        look_ranking.setListener(object : InputViewCheckV2.Listener() {
            override fun onClickCheck(
                viewId: Int,
                view: InputViewCheckV2,
                check: Int,
                radioStr: String
            ): Boolean {
                mData2.lookCarrierRankFlag(check)
                return true
            }
        })


        // 是否加急
        worry_or_not.setListener(object : InputViewCheckV2.Listener() {
            override fun onClickCheck(
                viewId: Int,
                view: InputViewCheckV2,
                check: Int,
                radioStr: String
            ): Boolean {
                mData2.urgentFlag(check)
                return true
            }
        })

        // 结算依据
        input_balance.setListener(object : InputViewClick.Listener() {
            override fun onClick(viewId: Int, view: InputViewClick, content: String) {
                DeliverNewGoodsNormalDialog
                    .settleBasisTypeDialog(
                        fragment = this@DeliverAddOrderNormalFragmentV3,
                    )
                    {
                        mData2.orderInfo.settleBasisType = it
                        refreshSettleBasisType()
                    }
            }
        })
        input_rule.setListener(object : InputViewClick.Listener() {
            override fun onClick(viewId: Int, view: InputViewClick, content: String) {
                DeliverNewGoodsNormalDialog.ruleDialog(
                    this@DeliverAddOrderNormalFragmentV3,
                    mOrderCommonInfo.formatRuleNameJSONArray(),
                    OrderCommonRuleItem(mData2.orderInfo.ruleId, mData2.orderInfo.ruleName)
                )
                {
                    if (it.ruleName == "无" && it.ruleId == "-101") {
                        mData2.orderInfo.ruleId = ""
                        mData2.orderInfo.ruleName = ""
                    } else {
                        mData2.orderInfo.ruleId = it.ruleId
                        mData2.orderInfo.ruleName = it.ruleName
                    }
                    refreshRule()
                }
            }
        })
        // 运费报价
        order_total_amount_view.listener = object : AddOrderTotalAmountView.Listener {
            override fun onClick() {
                handleCheckResult(mData2.checkEnableTotalAmount(mOrderCommonInfo))
            }

            override fun onTextChanged(s: String): String {
                mData2.orderInfo.totalAmount = s
                refreshSdOilCard()
                refreshSdOilCardDeDa()
                // 查询货主赠保额度
                val policyMoney = order_cargo_money_view.getPolicyMoney()
                if (!TextUtils.isEmpty(policyMoney)) {
                    queryPolicyInfo(policyMoney)
                } else {
                    refreshCargoMoney()
                }
                refreshOilRewardInfo()
                return if (mData2.orderInfo.freightType == "1") {
                    mData2.formatRealTotalMoney()
                } else {
                    ""
                }
            }

            override fun onClickView1(s: String): String {
                //货主价
                mData2.orderInfo.totalAmount = s
                //货主不含税运费
                mData2.orderInfo.consignorNoTaxMoney =
                    mData2.formatDriverMoney(mOrderCommonInfo.settleRate.toString())
                refreshCargoMoney()
                refreshOilRewardInfo()
                return mData2.orderInfo.consignorNoTaxMoney
            }

            override fun onClickView2(s: String): String {
                //货主不含税运费
                mData2.orderInfo.consignorNoTaxMoney = s
                //货主价
                mData2.orderInfo.totalAmount =
                    mData2.formatOwnerMoney(mOrderCommonInfo.settleRate.toString())
                refreshCargoMoney()
                refreshOilRewardInfo()
                return mData2.orderInfo.totalAmount
            }
        }
        // 报价结束时间
        input_offer_time_end.setListener(object : InputViewClick.Listener() {
            override fun onClick(viewId: Int, view: InputViewClick, content: String) {
                DeliverNewGoodsNormalDialog
                    .chooseTimeDialog(context, "报价结束时间", mData2.getExpectTimeCalender())
                    {
                        mData2.orderInfo.expectTime = it
                        refreshExpectTime()
                    }
            }
        })
        // 整车货值
        order_cargo_money_view.visibility = View.GONE
        // 购买保障
        add_order_policy_view.listener = object : AddOrderPolicyView.Listener {
            override fun canClickPolicy(policy: Boolean): Boolean {
                if (policy) {
                    if (!handleCheckResult(mData2.checkEnablePolicyFlag(mOrderCommonInfo))) {
                        return false
                    }
                    viewModel?.queryOrderPolicyInfoDialog(
                        mData2.cargoList,
                        mOrderCommonInfo.giftMoney,
                        mData2.orderInfo.cargoMoney,
                        mData2.formatRealTotalMoney(),
                        mData2.cargoList.getAllWeight(),
                        mData2.orderInfo.changePolicyToCarrier,
                        mData2.orderAddressInfo
                    )
                } else {
                    if (mData2.orderInfo.specifyFlag.isTrue) {
                        mData2.orderInfo.policyFlag = "0"
                        mData2.orderInfo.changePolicyToCarrier = "0"
                        refreshCargoMoneyGuaranteeLeft("")
                        refreshPolicyFlag()
                        return false
                    }
                    mData2.orderInfo.policyFlag = "0"
                    mData2.orderInfo.changePolicyToCarrier = "0"
                    refreshCargoMoneyGuaranteeLeft("")
                    refreshPolicyFlag()
                    return false
                }
                return false
            }

            override fun onDataChange(data: AddOrderPolicyView.ViewData) {
                mData2.orderInfo.policyFlag = data.policyFlag
                mData2.orderInfo.changePolicyToCarrier = data.changePolicyToCarrier
                mData2.orderInfo.agreeChangePolicyToCarrier = data.agreeChangePolicyToCarrier
                if (!policyTipsData.policyTipsFlagByCargoName && mData2.orderInfo.changePolicyToCarrier.isTrue) {
                    viewModel?.queryOrderPolicyInfoChangeModelV1(
                        mData2.cargoList,
                        mOrderCommonInfo.giftMoney,
                        mData2.orderInfo.cargoMoney,
                        mData2.formatRealTotalMoney(),
                        mData2.cargoList.getAllWeight(),
                        mData2.orderInfo.changePolicyToCarrier,
                        mData2.orderAddressInfo
                    )
                }
            }

            override fun onClickView1(): Boolean {
                if (!handleCheckResult(mData2.checkEnablePolicyFlagV1())) {
                    return false
                }
                viewModel?.queryOrderPolicyInfoChangeModel(
                    mData2.cargoList,
                    mOrderCommonInfo.giftMoney,
                    mData2.orderInfo.cargoMoney,
                    mData2.formatRealTotalMoney(),
                    mData2.cargoList.getAllWeight(),
                    mData2.orderInfo.changePolicyToCarrier,
                    mData2.orderAddressInfo
                )
                return true
            }

            override fun onClickView2(): Boolean {
                return true
            }
        }
        // 指定承运方
        input_appoint_carrier.setVisible(true)
        input_appoint_carrier.setListener(object : InputViewClick.Listener() {
            override fun onClick(viewId: Int, view: InputViewClick, content: String) {
                val appointCarrierCommInfo = AppointCarrierCommInfo()
                appointCarrierCommInfo.supportSocialAppointFlag =
                    mOrderCommonInfo.supportSocialAppointFlag.isTrue
                appointCarrierCommInfo.appointCarrierSwitch =
                    mOrderCommonInfo.appointCarrierSwitch.isTrue
                appointCarrierCommInfo.appointCarrierMaxMumber =
                    mOrderCommonInfo.appointCarrierMaxMumber
                appointCarrierCommInfo.appointGroupMaxMumber =
                    mOrderCommonInfo.appointGroupMaxMumber
                appointCarrierCommInfo.appointStaffMaxMumber =
                    mOrderCommonInfo.appointStaffMaxMumber
                appointCarrierCommInfo.selectAppointFlag = getCheckState()

                // 增加字段 orderType：订单类型：1 普通货，2 批量货
                val appointCarrierList = mData2.getAppointCarrierList()
                //                val appointCarrierGroupList = mData2.getAppointCarrierGroupList()
                val appointCarrierSocialList = mData2.getAppointCarrierSocialList()
                DeliverAppointCarrierActivity.start(
                    fragment = this@DeliverAddOrderNormalFragmentV3,
                    data1 = appointCarrierList,
                    //                        data2 = appointCarrierGroupList,
                    data3 = appointCarrierSocialList,
                    orderType = "1",
                    requestCode = REQUEST_SHIPPER_CARRIER,
                    showSocial = appointCarrierCommInfo,
                    showStaff = true
                )
            }
        })

        //是否优选专区
        add_order_preferred_view.setListeners(object : InputViewCheckV2.Listener() {
            override fun onClickCheck(
                viewId: Int,
                view: InputViewCheckV2,
                check: Int,
                radioStr: String
            ): Boolean {
                mData2.priorSelectFlag(check)
                return true
            }

            override fun onClickTitleRight(viewId: Int, view: InputViewCheckV2) {
                super.onClickTitleRight(viewId, view)
                DeliverOrderPreferredDialog().show(this@DeliverAddOrderNormalFragmentV3)
            }
        })

        //切换是否全平台运力选项 清空之前选择
        add_order_social_view.setListeners(object : InputViewCheckV2.Listener() {
            override fun onClickCheck(
                viewId: Int,
                view: InputViewCheckV2,
                check: Int,
                radioStr: String
            ): Boolean {
                input_appoint_carrier.content = ""
                when (check) {
                    InputViewCheckV2.LEFT -> {
                        input_appoint_carrier.setContentHint("必填")
                        mData2.orderInfo.isSelectSocial = "1"
                    }

                    InputViewCheckV2.RIGHT -> {
                        input_appoint_carrier.setContentHint("选填")
                        mData2.orderInfo.isSelectSocial = "0"
                    }
                }
                //清空之前的选择
                mData2.setAppointCarrierList(
                    data1 = emptyList(),
                    //                        data2 = emptyList(),
                    data3 = emptyList(),
                    orderCommonInfo = mOrderCommonInfo
                )
                refreshAdvance()
                refreshPreferredView() //优选专区
                return true
            }
        })
        add_order_social_view.setOnCheckListener(object : AddOrderSocialView.OnCheckListener {
            override fun onChecked(boolean: Boolean) {
                if (boolean) {
                    input_appoint_carrier.setContentHint("必填")
                } else {
                    input_appoint_carrier.setContentHint("选填")
                }
            }
        })

        // 预付比例
        input_advance_ratio.setListener(object : InputViewClick.Listener() {
            override fun onClick(viewId: Int, view: InputViewClick, content: String) {
                viewModel?.checkAdvanceInfo(mData2)
            }
        })
        // 是否押回单
        input_receipt.setListener(object : InputViewCheckV2.Listener() {
            override fun onClickCheck(
                viewId: Int,
                view: InputViewCheckV2,
                check: Int,
                radioStr: String
            ): Boolean {
                when (check) {
                    InputViewCheckV2.LEFT -> {
                        // 查询押回单信息
                        viewModel?.queryOrderReceipt()
                        return false
                    }

                    InputViewCheckV2.RIGHT -> {
                        // 清空数据
                        mData2.orderInfo.receiptFlag = "0"
                        mData2.orderReceiptInfo = OrderReceiptInfo()
                        refreshReceipt()
                        input_receipt.setWarning(false)
                        return false
                    }
                }
                return true
            }

            override fun onClickTitleRight(viewId: Int, view: InputViewCheckV2) {
                super.onClickTitleRight(viewId, view)
                DeliverNewGoodsReceiptDialog().show(this@DeliverAddOrderNormalFragmentV3)
            }
        })
        // 回单押金金额
        input_receipt_money.setInputType(InputType.TYPE_CLASS_NUMBER)
        input_receipt_money.setListener(object : InputViewEdit.Listener() {
            override fun onTextChanged(viewId: Int, view: InputViewEdit, s: String) {
                mData2.orderReceiptInfo.receiptMoney = s
                input_receipt_money.setWarning(false)
                refreshReceipt()
            }
        })
        // 回单收件地址
        input_receipt_address.setListener(object : InputViewClick.Listener() {
            override fun onClick(viewId: Int, view: InputViewClick, content: String) {
                DeliverReceiptMainActivity.start(
                    this@DeliverAddOrderNormalFragmentV3,
                    mData2.getReceiptAddress(), REQUEST_RECEIPT_ADDRESS, true
                )
            }
        })
        // 是否支持含油卡
        // 油卡
        oilCardView.listener = object : OilCardView.Listener {
            override fun onClickFixed(): Boolean {
                if (!handleCheckResult(mData2.checkEnableOilFixedCredit(mOrderCommonInfo))) {
                    return false
                }
                return true
            }

            override fun onDataChange(data: OilCardView.ViewData) {
                mData2.setOidData(data)
                refreshOilRewardInfo()
            }

            override fun onFillFixedAmount(fillFixedAmount: String) {
                mData2.orderInfo.oilFixedCredit = fillFixedAmount
                if (fillFixedAmount.isEmpty()) {
                    return
                }
                val realTotalMoney = mData2.formatRealTotalMoney().toDoubleOrNull() ?: 0.0
                if (!fillFixedAmount.matches(Regex("^[1-9]\\d*|0\$"))) {
                    showToast("固定额度必须为大于0的正整数")
                    oilCardView.setWarning1(true)
                    return
                }
            }

            override fun onGasFixedAmount(fillFixedAmount: String) {
                mData2.orderInfo.gasFixedCredit = fillFixedAmount
                if (fillFixedAmount.isEmpty()) {
                    return
                }
                val realTotalMoney = mData2.formatRealTotalMoney().toDoubleOrNull() ?: 0.0
                if (!fillFixedAmount.matches(Regex("^[1-9]\\d*|0\$"))) {
                    showToast("固定额度必须为大于0的正整数")
                    oilCardView.setWarning1(true)
                    return
                }
            }

        }
        // 是否支持含油卡
        // 油卡
        oilCardViewDeDa.listenerDeDa = object : OilCardViewDeDaV2.Listener {
            override fun onDataChange(data: OilCardViewDeDaV2.ViewData) {
                mData2.setOidData(data)
            }
        }
        // 紧急联系人
        edit_contact_name.setListener(object : InputViewEdit.Listener() {
            override fun onTextChanged(viewId: Int, view: InputViewEdit, s: String) {
                mData2.orderInfo.contactName = s
                edit_contact_name.setWarning(false)
            }
        })
        // 紧急联系人电话
        edit_contact_phone.setInputType(InputType.TYPE_CLASS_NUMBER)
        edit_contact_phone.setListener(object : InputViewEdit.Listener() {
            override fun onTextChanged(viewId: Int, view: InputViewEdit, s: String) {
                mData2.orderInfo.contactPhone = s
                edit_contact_phone.setWarning(false)
            }
        })
        // 摘单咨询电话
        input_zdzxdh.setInputType(InputType.TYPE_CLASS_NUMBER)
        input_zdzxdh.setListener(object : InputViewEdit.Listener() {
            override fun onTextChanged(viewId: Int, view: InputViewEdit, s: String) {
                mData2.orderInfo.delistConsultMobile = s
                input_zdzxdh.setWarning(false)
            }
        })
        // 结算咨询电话
        input_jszxdh.setInputType(InputType.TYPE_CLASS_NUMBER)
        input_jszxdh.setListener(object : InputViewEdit.Listener() {
            override fun onTextChanged(viewId: Int, view: InputViewEdit, s: String) {
                mData2.orderInfo.settleConsultMobile = s
            }
        })
        // 装卸货要求
        input_zxhyq.setListener(object : InputViewClick.Listener() {
            override fun onClick(viewId: Int, view: InputViewClick, content: String) {
                DeliverAddOrderZXHYQActivityV2.start(
                    fragment = this@DeliverAddOrderNormalFragmentV3,
                    allData = mOrderCommonInfo.promptTypeList ?: arrayListOf(),
                    extraData = mData2.loadFeeInfo.toJson(),
                    promptData = mData2.orderInfo.prompt,
                    requestCode = REQUEST_ZXHYQ
                )
            }
        })
        // 自定义编号
        input_zdybh.setListener(object : InputViewClick.Listener() {
            override fun onClick(viewId: Int, view: InputViewClick, content: String) {
//                if (mOrderCommonInfo.selfCommentSelectFlag.isTrue) {
//                    DeliverAddOrderZDYBHActivityV3.start(
//                        fragment = this@DeliverAddOrderNormalFragmentV3,
//                        requestCode = REQUEST_ZDYBH
//                    )
//                } else {
                    DeliverAddOrderZDYBHActivityV2.start(
                        fragment = this@DeliverAddOrderNormalFragmentV3,
                        selfComment = mData2.orderInfo.selfComment,
                        requestCode = REQUEST_ZDYBH
                    )
//                }
            }
        })
        // 运单标识
        input_order_mark.setListener(object : InputViewClick.Listener() {
            override fun onClick(viewId: Int, view: InputViewClick, content: String) {
                DeliverAddOrderMarkActivityV2.start(
                    this@DeliverAddOrderNormalFragmentV3,
                    mData2.orderInfo.orderMark, REQUEST_ORDER_MARK
                )
            }
        })
        // 项目名称
        input_project_name.setListener(object : InputViewClick.Listener() {
            override fun onClick(viewId: Int, view: InputViewClick, content: String) {
                DeliverAddOrderProjectNameActivity.start(
                    this@DeliverAddOrderNormalFragmentV3,
                    mData2.orderInfo.projectName, REQUEST_PROJECT_NAME
                )
            }
        })
        //批量发单输入
        UtilRxView.afterTextChangeEvents(et_order_num, 1500) {
            val batchPublishLimit = mOrderCommonInfo.batchPublishLimit.toIntOrNull() ?: 20
            val num = et_order_num.text.toString().toIntOrNull() ?: 1
            if (num > batchPublishLimit) {
                showDialogToast("批量发单每次限制${batchPublishLimit}单")
                return@afterTextChangeEvents
            }
            if (num - 1 <= 0) {
                showDialogToast("批量发单每次最少一单")
                return@afterTextChangeEvents
            }
            mData2.orderInfo.batchPublishNum = it.toString()
        }
        // 批量发单加
        tv_add_order.setOnClickListener {
            val batchPublishLimit = mOrderCommonInfo.batchPublishLimit.toIntOrNull() ?: 20
            val num = et_order_num.text.toString().toIntOrNull() ?: 1
            if (num + 1 > batchPublishLimit) {
                showDialogToast("批量发单每次限制${batchPublishLimit}单")
                return@setOnClickListener
            }
            et_order_num.setText((num + 1).toString())
            mData2.orderInfo.batchPublishNum = (num + 1).toString()
        }
        // 批量发单减
        tv_subtraction_order.setOnClickListener {
            val num = et_order_num.text.toString().toIntOrNull() ?: 1
            if (num - 1 <= 0) {
                showDialogToast("批量发单每次最少一单")
                return@setOnClickListener
            }
            et_order_num.setText((num - 1).toString())
            mData2.orderInfo.batchPublishNum = (num - 1).toString()
        }

        //亏涨吨规则
        input_order_loss.setListener(object : InputViewClick.Listener() {
            override fun onClick(viewId: Int, view: InputViewClick, content: String) {
                // 选择亏涨吨
                val deliverNewGoodsLossDialog = DeliverNewGoodsLossDialog()
                deliverNewGoodsLossDialog.setOnSelectTonRuleListener(object :
                    DeliverNewGoodsLossDialog.OnSelectTonRuleListener {
                    override fun onSelectTonRule(data: OrderCommonTonRuleItem?) {
                        if (data == null) {
                            mData2.orderInfo.tonRuleId = ""
                            input_order_loss.content = "请选择"
                        }
                        data?.let {
                            mData2.orderInfo.tonRuleId = it.tonRuleId
                            input_order_loss.content = it.tonRuleName
                        }
                    }
                })
                deliverNewGoodsLossDialog.setTonRuleId(mData2.orderInfo.tonRuleId)
                mRspQueryTonRuleByCargoName.tonRuleList?.let {
                    deliverNewGoodsLossDialog.setTonRuleData(it)
                }
                deliverNewGoodsLossDialog.show(this@DeliverAddOrderNormalFragmentV3)
            }
        })

        //定制化监控
        input_monitor.setListener(object : InputViewCheckV2.Listener() {
            override fun onClickCheck(
                viewId: Int,
                view: InputViewCheckV2,
                check: Int,
                radioStr: String
            ): Boolean {
                when (check) {
                    InputViewCheckV2.LEFT -> {
                        mData2.orderInfo.orderMonitor = "1"
                    }

                    InputViewCheckV2.RIGHT -> {
                        mData2.orderInfo.orderMonitor = "0"
                    }
                }
                return true
            }

        })
        //承运方期望运价推送规则
        tvExpectedFreightRate.setVisible(false)
        inputExpectedFreightRate.setListener(object : InputViewClick.Listener() {
            override fun onClick(viewId: Int, view: InputViewClick, content: String) {
                val list = arrayListOf<String>()
                list.add("实时推送")
                list.add("统一推送")
                ChooseDialogV1.instance(list)
                    .setTitle("请选择货物包装")
                    .setChoose(view.content)
                    .setClick { s, _ ->
                        inputExpectedFreightRate.content = s
                        when (s) {
                            "实时推送" -> {
                                mData2.orderInfo.publishAutoPushCarrierPrice = "1"
                                tvExpectedFreightRate.setVisible(false)
                            }

                            "统一推送" -> {
                                mData2.orderInfo.publishAutoPushCarrierPrice = "0"
                                tvExpectedFreightRate.text =
                                    "承运方报价将在报价结束时间结束后统一推送"
                                tvExpectedFreightRate.setVisible(true)
                            }
                        }
                    }
                    .show(this@DeliverAddOrderNormalFragmentV3)
            }
        })
    }

    private fun refreshOilRewardInfo() {
        when (mOrderCommonInfo.oilConfig) {
            "0" -> {
                return
            }
        }
        when (mData2.orderInfo.orderModel) {
            "1" -> {
                //议价单
                if (mData2.formatRealBlockMoney().isEmpty()) {
                    refreshOilGasPreferential("")
                    return
                }
                if (mData2.orderInfo.oilGesEmpty()) {
                    refreshOilGasPreferential("")
                    return
                }
                val totalMoney = when (mData2.orderInfo.orderModel) {
                    "1" -> {
                        mData2.formatRealBlockMoney()
                    }

                    else -> {
                        mData2.formatRealTotalMoney()
                    }
                }.toDoubleOrNull() ?: 0.00
                if (totalMoney == 0.00) {
                    refreshOilGasPreferential("")
                    return
                }
                viewModel?.queryOilRewardInfoByPublish(
                    req = Req37QueryOilRewardInfoByPublish().setData(
                        orderInfo = mData2.orderInfo,
                        orderAddressInfo = mData2.orderAddressInfo,
                        mTotalMoney = totalMoney.toString()
                    )
                )
            }

            else -> {
                //抢单
                if (mData2.formatRealTotalMoney().isEmpty()) {
                    refreshOilGasPreferential("")
                    return
                }
                if (mData2.orderInfo.oilGesEmpty()) {
                    refreshOilGasPreferential("")
                    return
                }
                val totalMoney = when (mData2.orderInfo.orderModel) {
                    "1" -> {
                        mData2.formatRealBlockMoney()
                    }

                    else -> {
                        mData2.formatRealTotalMoney()
                    }
                }.toDoubleOrNull() ?: 0.00
                if (totalMoney == 0.00) {
                    refreshOilGasPreferential("")
                    return
                }
                viewModel?.queryOilRewardInfoByPublish(
                    req = Req37QueryOilRewardInfoByPublish().setData(
                        orderInfo = mData2.orderInfo,
                        orderAddressInfo = mData2.orderAddressInfo,
                        mTotalMoney = totalMoney.toString()
                    )
                )
            }
        }
    }

    private fun queryPolicyInfo(
        s: String
    ) {
        viewModel?.queryOrderPolicyInfo(
            mData2.cargoList,
            mOrderCommonInfo.giftMoney,
            s,
            mData2.formatRealTotalMoney(),
            mData2.cargoList.getAllWeight(),
            mData2.orderInfo.changePolicyToCarrier,
            mData2.orderAddressInfo
        )
    }

    @LiveDataMatch
    open fun onQueryOrderPolicyInfoV1(data: RspQueryOrderPolicyInfo) {
        if (data.orderPolicyInfo.lossRatioOverLimitFlag.isTrue) {
            showToast("当前货物赔付率较高，请您加强风险控制！")
        }
    }

    @LiveDataMatch
    open fun onReq23QueryMobileOrderCommonInfo2(data: Rsp23QueryMobileOrderCommonInfo) {
        mData2.setRsp23QueryMobileOrderCommonInfo(data)
        mOrderCommonInfo = data
        refreshInitData()
        handleCheckResult(mData2.checkInit(), false)
        if (!TextUtils.isEmpty(outOrderNumber)) {
            viewModel?.queryCargoDespatchOrDeliveryInfo33(outOrderNumber ?: "")
        }
        getTonRule()

        setAgreement()
    }

    @LiveDataMatch
    open fun onJumpToMobileSeniorConsignorUpdateOrder(data: JumpNormalData) {
        mData2.setJumpData(data, mJumpData)
        mData2.orderInfo.agreeChangePolicyToCarrier = true
        policyTipsData.policyTipsFlagByCargoName = data.policyTipsFlag.isTrue
        policyTipsData.autoBuyFlag = data.autoBuyFlag
        policyTipsData.policyMode = data.policyMode
        policyTipsData.openMonthlyServiceFlag = data.openMonthlyServiceFlag
        policyTipsData.monthlyServiceMsg = data.monthlyServiceMsg
        policyTipsData.monthlyPolicyMode = data.monthlyPolicyMode
        policyTipsData.policyTipsFlag = data.policyTipsFlag
        mOrderCommonInfo = data.initData ?: Rsp23QueryMobileOrderCommonInfo()
        refreshDraftsData(data.jumpData?.policyInfo?.guaranteeLeft)
        handleCheckResult(mData2.checkNew(mOrderCommonInfo), false)
        getTonRule()
        setAgreement()

    }

    private fun setAgreement() {
        agreementView.queryAgreement(viewModel, true, {
            //本地固定协议
            val ag = EAgreement()
            ag.type = EAgreement.Query.LOCAL
            ag.hookShow = true
            ag.contentDescAlias = mOrderCommonInfo.contractName
            ag.url =
                HttpConfig.getWebUrl("form_h5/contract/index.html?_t=" + System.currentTimeMillis() + "#/consignor")
            it.add(ag)
        })
    }

    private fun DeliverAddOrderNormalFragmentV3.getTonRule() {
        if (mOrderCommonInfo.whetherShowTonRule.isTrue) {
            viewModel?.queryTonRuleByCargoName(
                req = ReqQueryTonRuleByCargoName(
                    cargoNameStr = mData2.cargoList.toCommaString { it.cargoName })
            )
        }
    }

    // 10 查询押回单信息
    @LiveDataMatch
    open fun onQueryOrderReceiptSuccess(data: RspQueryOrderReceipt) {
        mData2.setReceiptInfo(data.receiptInfo)
        refreshReceipt()
        input_receipt_money.setWarning(false)
        input_receipt_address.setWarning(false)
    }

    // 10 查询押回单信息 金额 list
    @LiveDataMatch
    open fun onQueryOrderReceiptListSuccess(data: RspQueryOrderReceipt) {
        ChooseDialogV1.instance(data.receiptInfo.receiptMoneyList)
            .setTitle("请选择回单金额")
            .setChoose(mData2.orderReceiptInfo.receiptMoney)
            .setClick { s, _ ->
                mData2.orderReceiptInfo.receiptMoney = s
                refreshReceipt()
                input_receipt_money.setWarning(false)
            }
            .show(this@DeliverAddOrderNormalFragmentV3)
    }

    // 17
    @LiveDataMatch
    open fun onQueryOrderPolicyInfo(data: RspQueryOrderPolicyInfo, cargoMoney: String) {
        if (data.specialCargo == "1" && mData2.orderInfo.orderModel == "1") {
            refreshCargoMoneyGuaranteeLeft("")
        } else {
            refreshCargoMoneyGuaranteeLeft(data.orderPolicyInfo.guaranteeLeft)
        }
    }

    // 17
    @LiveDataMatch
    open fun onQueryOrderPolicyInfoDialog(data: RspQueryOrderPolicyInfo) {
        if (data.supportPolicyFlag.isTrue) {
            add_order_policy_view.setVisible(true)
            DeliverNewGoodsSafeHintDialog()
                .setData(
                    getViewModel(DeliverAddOrderNormalModelV3::class.java),
                    data,
                    mData2.orderInfo.orderModel
                ) {
                    mData2.orderInfo.policyFlag = "1"
                    mData2.orderInfo.changePolicyToCarrier = "0"
                    val policyMoney = order_cargo_money_view.getPolicyMoney()
                    if (!TextUtils.isEmpty(policyMoney)) {
                        queryPolicyInfo(policyMoney)
                    }
                    refreshPolicyFlag()
                    add_order_policy_view.setWarning(false)
                }
                .show(this@DeliverAddOrderNormalFragmentV3)
        } else {
            showDialogToast("不支持购买货物保障服务")
            mData2.orderInfo.policyFlag = "0"
            mData2.orderInfo.changePolicyToCarrier = "0"
            refreshCargoMoneyGuaranteeLeft("")
            add_order_policy_view.setVisible(false)
            add_order_policy_view.setWarning(false)
        }
    }

    // 16.PC端和手机端接口：校验增值服务信息
    @LiveDataMatch
    open fun onCheckAdvanceInfo(data: RspCheckAdvanceInfo) {
        if (data.advanceState.isTrue) {
            input_advance_ratio.setVisible(true)
            //            check_advance.check = InputViewCheckV2.LEFT
            ChooseDialogV1.instance(data.advanceRatioList.map { it.ratio })
                .setTitle("请选择预付比例")
                .setChoose(input_advance_ratio.content)
                .setClick { s, _ ->
                    input_advance_ratio.content = s
                    mData2.setRspCheckAdvanceInfo(settleNumber = data.settleNumber, ratio = s)
                    input_advance_ratio.setWarning(false)
                }
                .show(this@DeliverAddOrderNormalFragmentV3)
        } else {
            input_advance_ratio.setVisible(false)
            input_advance_ratio.content = ""
            showDialogToast(data.resultMsg)
        }
    }

    //    //  22.手机端接口：根据货物名称查询是否展示保险提示信息标志
    //    @LiveDataMatch
    //    open fun onQueryPolicyTipsFlagByCargoName(data: RspQueryPolicyTipsFlagByCargoName) {
    //        // 保险提示信息标志：0 不展示，1 展示
    //        if (data.policyTipsFlag.isTrue) {
    //            policyTipsData.policyTipsFlagByCargoName = true
    //            mData2.orderInfo.policyFlag = "0"
    //            mData2.orderInfo.changePolicyToCarrier = "0"
    //            mData2.orderInfo.agreeChangePolicyToCarrier = true
    //        } else {
    //            policyTipsData.policyTipsFlagByCargoName = false
    //        }
    //        policyTipsData.autoBuyFlag = data.autoBuyFlag
    //        policyTipsData.policyMode = data.policyMode
    //        refreshPolicyFlag()
    //        refreshPolicyHint()
    //    }

    private var lastTime = 0L

    // 2.手机端接口：新增普通货运单-抢单/议价2种模式时:查询已认证货主保证金相关信息
    @LiveDataMatch
    open fun onQueryMobileDepositInfoForOrder(
        data: RspQueryMobileDepositInfoForOrder,
        type: String
    ) {
        val commitFun = {
            // 是否预挂单发布
            if (data.orderPresetPublishFlag.isTrue) {
                DeliverNewGoodsPresetPublishDialog()
                    .setData(data.alertMsgArray) {
                        val currentTimeMillis = System.currentTimeMillis()
                        if (currentTimeMillis - lastTime < 1000) {
                            return@setData
                        }
                        lastTime = currentTimeMillis
                        // 发布
                        val req = mData2.copy(orderInfo = mData2.orderInfo.copy())
                        req.orderInfo.orderPresetPublishFlag = data.orderPresetPublishFlag
                        if (req.orderInfo.cargoValueOverLimit == "1") {
                            req.orderInfo.policyFlag = "0"
                            req.orderInfo.changePolicyToCarrier = "0"
                        }

                        when (type) {
                            "commit" -> {
                                viewModel?.commit(req, mJumpData.pageState)
                            }

                            "save_and_commit" -> {
                                viewModel?.saveAndCommit(req, mJumpData.pageState)
                            }
                        }
                        if (!mData2.orderInfo.batchPublishNum.isTrue) {
                            viewModel?.getSuccessImportNum()
                        }
                    }
                    .show(this)
            } else {
                val dialogBuilder = DialogBuilder()
                    .setMessage(data.alertMsgText)
                    .setOkListener { dialog, _ ->
                        dialog.dismiss()
                        val currentTimeMillis = System.currentTimeMillis()
                        if (currentTimeMillis - lastTime < 1000) {
                            return@setOkListener
                        }
                        lastTime = currentTimeMillis
                        // 发布
                        val req = mData2.copy(orderInfo = mData2.orderInfo.copy())
                        req.orderInfo.orderPresetPublishFlag = data.orderPresetPublishFlag
                        if (req.orderInfo.cargoValueOverLimit == "1") {
                            req.orderInfo.policyFlag = "0"
                            req.orderInfo.changePolicyToCarrier = "0"
                        }

                        when (type) {
                            "commit" -> {
                                viewModel?.commit(req, mJumpData.pageState)
                            }

                            "save_and_commit" -> {
                                viewModel?.saveAndCommit(req, mJumpData.pageState)
                            }
                        }
                        if (!mData2.orderInfo.batchPublishNum.isTrue) {
                            viewModel?.getSuccessImportNum()
                        }
                    }
                showDialog(dialogBuilder)
            }
        }
        // 账期截止时间-错误标志
        if (data.expiryDateFlag) {
            showDialog(DialogBuilder().setMessage(data.expiryDateMsg))
            return
        }
        // 账期额度-错误标志
        if (data.advanceFlag) {
            showDialog(DialogBuilder()
                .setViewListener { dialog, _ ->
                    dialog.dismiss()
                    PhoneUtil.callPhone(context, data.advanceMobile)
                }
                .setMessage(data.advanceMsg))
            return
        }
        // 预警额度-错误标志
        if (data.warningFlag) {
            val dialogBuilder = DialogBuilder()
                .setMessage(data.warningMsg)
                .setViewListener { dialog, _ ->
                    dialog.dismiss()
                    PhoneUtil.callPhone(context, data.advanceMobile)
                }
                .setOkListener { dialog, _ ->
                    dialog.dismiss()
                    mData2.orderInfo.cargoValueOverLimit = data.cargoValueOverLimit
                    if (data.cargoValueOverLimit == "1") {
                        val dialogBuilder = DialogBuilder()
                            .setTitle("保障服务确认")
                            .setHideCancel(true)
                            .setMessage("此单货值超出货物保障限额，需联系客服************或客户经理线下购买货物保障服务。")
                            .setOkListener { dialog, _ ->
                                dialog.dismiss()
                                commitFun()
                            }
                        showDialog(dialogBuilder)
                    } else {
                        commitFun()
                    }
                }
            showDialog(dialogBuilder)
            return
        }
        mData2.orderInfo.cargoValueOverLimit = data.cargoValueOverLimit
        if (data.cargoValueOverLimit == "1") {
            val dialogBuilder = DialogBuilder()
                .setTitle("保障服务确认")
                .setHideCancel(true)
                .setMessage("此单货值超出货物保障限额，需联系客服************或客户经理线下购买货物保障服务。")
                .setOkListener { dialog, _ ->
                    dialog.dismiss()
                    commitFun()
                }
            showDialog(dialogBuilder)
            return
        }
        // 正常发布流程
        commitFun()
    }

    /**
     * 注释：查询飞单管控
     * 时间：2025/4/2 0002 19:24
     * 作者：郭翰林
     */
    private fun queryFlyingOrderControl(runnable: Runnable?) {
        getViewModel(BaseViewModel::class.java).execute(
            ReqQueryFlyingOrderControl()
        ) { rsp ->
            activity?.runOnUiThread {
                if (rsp.success()) {
                    if (rsp.data?.policyHit == "0") {
                        runnable?.run()
                    } else {
                        //飞单管控弹窗
                        val dialog = FlyingSingleDialog()
                        dialog.setOnFlyingSingleClickListener(object : OnFlyingSingleClickListener {
                            override fun onSubmit() {
                                if (rsp.data?.policyTimeUnit == "1") {
                                    runnable?.run()
                                }
                            }

                            override fun onCustomerService() {
                                //  点击客服
                                AMainServer.getPluginServer().showLineServerPhone(activity)
                            }
                        })
                        dialog.isCancelable = false
                        dialog.needTime = rsp.data?.policyTimeUnit == "1"
                        dialog.second = rsp.data?.policyTime?.toInt() ?: 30
                        dialog.content = rsp.data?.resultMsg ?: ""
                        dialog.show(this)
                    }
                }
            }
        }
    }

    @LiveDataMatch
    open fun onCommitSuccess() {
        showToast("发货成功")
        RxBusEventManager.postEvent(EventNewGoodsSuccess())
        AMainServer.getPluginServer()
            .changeMenu(<EMAIL>, AMainServer.JUMP_WAYBILL)
    }

    @LiveDataMatch
    open fun onReq33QueryCargoDespatchOrDeliveryInfo(data: Rsp33QueryCargoDespatchOrDeliveryInfo?) {
        data?.let {
            //悠然运输计划相关查询
            it.cargoDespatchOrDeliveryInfo?.apply {
                //1.货物信息
                mData2.setDetailsDataV1(cargoInfo)
                //2.设置从
                mData2.plus(despatchInfo)
                //3.设置到
                mData2.setAddressEnd(deliverInfo)
                refreshAddress()
                address_view.setEndWarning(false)
            }
        }
    }

    @LiveDataMatch
    open fun onCommitNoMoney(result: RspAddResult) {
        // 发货失败，余额不足
        val sh = SpannableHepler()
        result.alertMsgArray.forEach { text ->
            sh.append(SpannableHepler.Txt(text, "#333333"))
        }
        val dialog = DialogBuilder()
        dialog.title = "您的可用余额不足"
        dialog.isHideCancel = false
        dialog.message = sh.builder()
        showDialog(dialog)
    }

    @LiveDataMatch
    open fun onGetPendingOrderIdsSuccess(data: RspGetPendingOrderIds) {

        when (data.code) {
            "3027" -> {
                val dialogBuilder = DialogBuilder()
                dialogBuilder.message = data.msg
                dialogBuilder.cancelText = "关闭"
                dialogBuilder.title = "提示"
                dialogBuilder.setOKText("立即处理")
                dialogBuilder.setOkListener { dialog, _ ->
                    dialog.dismiss()
                    OrderExpressMainActivity.start(
                        context = context,
                        index = when (data.dealFlag) {
                            "0" -> {
                                0
                            }

                            else -> {
                                1
                            }
                        },
                        orderIds = data.orderIds
                    )
                }
                showDialog(dialogBuilder)
            }

            "3028" -> {
                val dialogBuilder = DialogBuilder()
                dialogBuilder.message = data.msg
                dialogBuilder.cancelText = "关闭"
                dialogBuilder.title = "提示"
                dialogBuilder.setOKText("立即处理")
                dialogBuilder.setOkListener { dialog, _ ->
                    dialog.dismiss()
                    ReturnedOrderConfirmListActivity.start(
                        context = context,
                        index = when (data.dealFlag) {
                            "0" -> {
                                0
                            }

                            else -> {
                                1
                            }
                        },
                        orderIds = data.orderIds
                    )
                }
                showDialog(dialogBuilder)
            }

            "3029" -> {
                val dialogBuilder = DialogBuilder()
                dialogBuilder.message = data.msg
                dialogBuilder.cancelText = "关闭"
                dialogBuilder.title = "提示"
                dialogBuilder.setOKText("立即处理")
                dialogBuilder.setOkListener { dialog, _ ->
                    dialog.dismiss()
                    SettlementApplicationListActivity.start(
                        context = context,
                        index = 0,
                        orderIds = data.orderIds
                    )
                }
                showDialog(dialogBuilder)
            }
        }
    }

    @LiveDataMatch
    open fun onQueryOilRewardInfoByPublish(data: Rsp37QueryOilRewardInfoByPublish) {
        if (data.haveReward.isTrue) {
            //有油品优惠活动
            if (mData2.orderInfo.supportSdOilCardFlag.isTrue) {
                //支持油品
                refreshOilGasPreferential(data.money)
            } else {
                val dialogBuilder = DialogBuilder()
                dialogBuilder.message =
                    "选择配置油/气品最高可获得" + data.money + "元现金奖励用于抵扣发单运费，是否继续配油/气？"
                dialogBuilder.cancelText = "放弃奖励"
                dialogBuilder.setOKText("配置油/气")
                dialogBuilder.cancelListener =
                    DialogBuilder.DialogInterface.OnClickListener { p, _ ->
                        //放弃奖励
                        p?.dismiss()
                        mData2.orderInfo.supportSdOilCardFlag = "0"
                        mData2.orderInfo.oilCalculateType = "0"
                        mData2.orderInfo.abandonOilReward = true
                        refreshSdOilCard()
                    }
                dialogBuilder.okListener = DialogBuilder.DialogInterface.OnClickListener { p, _ ->
                    //配置油/气
                    p?.dismiss()
                    mData2.orderInfo.supportSdOilCardFlag = "1"
                    mData2.orderInfo.oilCalculateType = "1"
                    refreshSdOilCard()
                    refreshOilGasPreferential(data.money)
                }
                if (!mData2.orderInfo.abandonOilReward) {
                    showDialog(dialogBuilder)
                }
            }
        } else {
            refreshOilGasPreferential("")
        }
    }

    @LiveDataMatch
    open fun onSaveDraftsSuccess() {
        showToast("保存草稿成功")
        RxBusEventManager.postEvent(EventNewGoodsSuccess())
    }

    @LiveDataMatch()
    open fun onGetSuccessImportNum(orderNum: String) {
        if (!mDeliverOrderSuccessImportNumDialog.isAdded) {
            mDeliverOrderSuccessImportNumDialog.show(this@DeliverAddOrderNormalFragmentV3)
        }
        mDeliverOrderSuccessImportNumDialog.setNum(orderNum, mData2.orderInfo.batchPublishNum)
        if (TextUtils.equals("-1", orderNum)) {
            RxBusEventManager.postEvent(EventNewGoodsSuccess())
            AMainServer.getPluginServer()
                .changeMenu(<EMAIL>, AMainServer.JUMP_WAYBILL)
        }
    }

    companion object {
        private const val EXTRA_JUMP_DATA = "extra_jump_data"

        /** 跳转选择 起点 地址选择 */
        private const val REQUEST_ADDRESS_START = 0x31

        /** 跳转选择 终点 地址选择 */
        private const val REQUEST_ADDRESS_END = 0x32

        /** 跳转选择 货物明细 */
        private const val REQUEST_GOODS_DETAIL = 0x33

        /** 跳转选择 车长车型 */
        private const val REQUEST_VEHICLE_TYPE = 0x34

        /** 跳转选择 承运人 */
        private const val REQUEST_SHIPPER_CARRIER = 0x35

        /** 跳转选择 回单地址 */
        private const val REQUEST_RECEIPT_ADDRESS = 0x36

        /** 跳转选择 装卸货要求 */
        private const val REQUEST_ZXHYQ = 0x38

        /** 跳转选择 自定义编号 */
        private const val REQUEST_ZDYBH = 0x39

        /** 跳转选择 运单标识 */
        private const val REQUEST_ORDER_MARK = 0x5a

        /** 跳转选择 项目名称 */
        private const val REQUEST_PROJECT_NAME = 0x6a

        private const val OUT_ORDER_NUMBER = "outOrderNumber"
        fun instance(
            context: Context?,
            outOrderNumber: String,
            jumpNewGoodsData: JumpNewGoodsData? = null
        ): DeliverAddOrderNormalFragmentV3 {
            val data = Bundle()
            data.putString(EXTRA_JUMP_DATA, jumpNewGoodsData.toJson())
            data.putString(OUT_ORDER_NUMBER, outOrderNumber)
            var f = DeliverAddOrderNormalFragmentV3()
            f.arguments = data
            return f
        }
    }
}