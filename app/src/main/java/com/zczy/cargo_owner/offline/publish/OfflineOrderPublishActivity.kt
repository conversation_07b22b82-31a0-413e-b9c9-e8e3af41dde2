package com.zczy.cargo_owner.offline.publish

import android.app.Activity
import android.content.Context
import android.content.Intent
import android.os.Bundle
import androidx.fragment.app.FragmentTransaction
import android.view.View
import com.sfh.lib.event.RxBusEvent
import com.zczy.cargo_owner.R
import com.zczy.cargo_owner.deliver.addorder.dialog.DeliverNewGoodsNormalDialog
import com.zczy.cargo_owner.libcomm.event.EventNewGoodsSuccess
import com.zczy.cargo_owner.deliver.drafts.ui.DeliverDraftsMainFragment
import com.zczy.cargo_owner.deliver.drafts.ui.DeliverDraftsSearchActivity
import com.zczy.cargo_owner.libcomm.widget.toolbar.CommTabToolbar
import com.zczy.cargo_owner.offline.model.OfflineModel
import com.zczy.comm.ui.BaseActivity
import kotlinx.android.synthetic.main.activity_publish_offline.*

class OfflineOrderPublishActivity : BaseActivity<OfflineModel>() {
    private var showingIndex = 0
    private var draftsShowingOrderType = "0"

    private var newGoodsFragment: DeliverAddOrderNormalFragmentV3? = null
    private var draftsFragment: DeliverDraftsNormalFragmentV2? = null

    override fun getLayout(): Int = R.layout.activity_publish_offline

    override fun initData() {}

    override fun bindView(bundle: Bundle?) {
        initToolbar()
        switchTab(0)
    }

    override fun onBackPressed() {
        if (showingIndex == 0) {
            showDialog(DeliverNewGoodsNormalDialog.cancelDialog {
                finish()
            })
        } else {
            super.onBackPressed()
        }
    }

    private fun initToolbar() {
        comm_tab_toolbar.apply {
            setLeftBtn()
            setRightBtn()
            setRightBtnVisible(false)
            setData(listOf("发新货", "草稿箱"))
            mListener = object : CommTabToolbar.Listener() {
                override fun onClickLeft(v: View) {
                    if (showingIndex == 0) {
                        showDialog(DeliverNewGoodsNormalDialog.cancelDialog {
                            finish()
                        })
                    } else {
                        finish()
                    }
                }

                override fun onClickRight(v: View) {
                    DeliverDraftsSearchActivity.start(
                        this@OfflineOrderPublishActivity,
                        draftsShowingOrderType
                    )
                }

                override fun onChoose(index: Int, title: String) {
                    switchTab(index)
                }
            }
        }
    }

    private fun switchTab(tabIndex: Int) {
        showingIndex = tabIndex
        val fm = supportFragmentManager
        val transaction = fm.beginTransaction()
        hideFragments(transaction)
        when (tabIndex) {
            0 -> {
                var f = newGoodsFragment
                if (f == null) {
                    f = DeliverAddOrderNormalFragmentV3.instance(
                        context = this,
                        outOrderNumber = ""
                    )
                    newGoodsFragment = f
                    transaction.add(R.id.fl_container, f)
                    transaction.hide(f)
                }
                transaction.show(f)
            }
            1 -> {
                var f = draftsFragment

                if (f == null) {
                    f = DeliverDraftsNormalFragmentV2.instance(
                        this,
                        comeType = DeliverDraftsMainFragment.COME_TYPE_ADD
                    )
                    draftsFragment = f
                    transaction.add(R.id.fl_container, f)
                    transaction.hide(f)
                }
                transaction.show(f)
            }
        }
        transaction.commit()
    }

    private fun hideFragments(transaction: androidx.fragment.app.FragmentTransaction) {
        newGoodsFragment?.apply {
            transaction.hide(this)
        }
        draftsFragment?.apply {
            transaction.hide(this)
        }
    }

    @RxBusEvent(from = "发新货成功")
    open fun onEventNewGoodsSuccess(data: EventNewGoodsSuccess) {
        setResult(Activity.RESULT_OK)
        finish()
    }

    companion object {
        @JvmStatic
        fun start(context: Context?) {
            context ?: return
            val intent = Intent(context, OfflineOrderPublishActivity::class.java)
            context.startActivity(intent)
        }
    }
}