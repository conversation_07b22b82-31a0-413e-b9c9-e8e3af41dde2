package com.zczy.cargo_owner.offline.check

import android.view.View
import com.sfh.lib.ui.dialog.DialogBuilder
import com.zczy.cargo_owner.R
import com.zczy.cargo_owner.deliver.addorder.req.normal.ex2.AddOrderNormalCheckMenu
import com.zczy.cargo_owner.offline.publish.DeliverAddOrderNormalViewV2
import com.zczy.comm.utils.ex.setVisible
import kotlinx.android.synthetic.main.deliver_add_order_normal_fragment_v3.add_order_policy_view
import kotlinx.android.synthetic.main.deliver_add_order_normal_fragment_v3.add_order_time_view
import kotlinx.android.synthetic.main.deliver_add_order_normal_fragment_v3.address_view
import kotlinx.android.synthetic.main.deliver_add_order_normal_fragment_v3.check_deliver_process
import kotlinx.android.synthetic.main.deliver_add_order_normal_fragment_v3.check_freight_type
import kotlinx.android.synthetic.main.deliver_add_order_normal_fragment_v3.check_order_mode
import kotlinx.android.synthetic.main.deliver_add_order_normal_fragment_v3.cl_subtraction_order
import kotlinx.android.synthetic.main.deliver_add_order_normal_fragment_v3.img_warning_subtraction_order
import kotlinx.android.synthetic.main.deliver_add_order_normal_fragment_v3.inputExpectedFreightRate
import kotlinx.android.synthetic.main.deliver_add_order_normal_fragment_v3.input_advance_ratio
import kotlinx.android.synthetic.main.deliver_add_order_normal_fragment_v3.input_appoint_carrier
import kotlinx.android.synthetic.main.deliver_add_order_normal_fragment_v3.input_balance
import kotlinx.android.synthetic.main.deliver_add_order_normal_fragment_v3.input_goods_detail
import kotlinx.android.synthetic.main.deliver_add_order_normal_fragment_v3.input_lanbiaojia
import kotlinx.android.synthetic.main.deliver_add_order_normal_fragment_v3.input_offer_time_end
import kotlinx.android.synthetic.main.deliver_add_order_normal_fragment_v3.input_receipt
import kotlinx.android.synthetic.main.deliver_add_order_normal_fragment_v3.input_receipt_address
import kotlinx.android.synthetic.main.deliver_add_order_normal_fragment_v3.input_receipt_money
import kotlinx.android.synthetic.main.deliver_add_order_normal_fragment_v3.input_receive_time
import kotlinx.android.synthetic.main.deliver_add_order_normal_fragment_v3.input_vehicle_type
import kotlinx.android.synthetic.main.deliver_add_order_normal_fragment_v3.input_waybill_type
import kotlinx.android.synthetic.main.deliver_add_order_normal_fragment_v3.oilCardView
import kotlinx.android.synthetic.main.deliver_add_order_normal_fragment_v3.order_cargo_money_view
import kotlinx.android.synthetic.main.deliver_add_order_normal_fragment_v3.order_total_amount_view
import kotlinx.android.synthetic.main.deliver_add_order_normal_fragment_v3.scroll_view

/**
 * 处理 检查 结果
 * 错误的 标红，移动到第一项，提示第一项 error msg
 * @param map
 * @param needScroll 是否 移动 并展示 dialog msg
 */
fun DeliverAddOrderNormalViewV2<*>.handleCheckResult(
    map: Map<AddOrderNormalCheckMenu, String>,
    needScroll: Boolean = true,
    needRed: Boolean = true
): Boolean {
    var firstFlag = true
    val scrollF: (Boolean, View, String) -> Boolean = { flag, view, str ->
        if (flag && needScroll) {
            scroll_view.scrollTo(0, view.top)
            val dialog = DialogBuilder()
            dialog.title = "提示"
            dialog.isHideCancel = true
            dialog.message = str
            showDialog(dialog)
        }
        false
    }
    for ((key, value) in map) {
        when (key) {
            AddOrderNormalCheckMenu.普_从 -> {
                if (needRed) {
                    address_view.setStartWarning(true)
                }
                firstFlag = scrollF(firstFlag, address_view, value)
            }

            AddOrderNormalCheckMenu.普_到 -> {
                if (needRed) {
                    address_view.setEndWarning(true)
                }
                firstFlag = scrollF(firstFlag, address_view, value)
            }

            AddOrderNormalCheckMenu.普_货物明细 -> {
                if (needRed) {
                    input_goods_detail.setWarning(true)
                }
                firstFlag = scrollF(firstFlag, input_goods_detail, value)
            }

            AddOrderNormalCheckMenu.普_车长车型 -> {
                if (needRed) {
                    input_vehicle_type.setWarning(true)
                }
                firstFlag = scrollF(firstFlag, input_vehicle_type, value)
            }

            AddOrderNormalCheckMenu.普_装货时间,
            AddOrderNormalCheckMenu.普_装货时间_校验 -> {
                if (needRed) {
                    add_order_time_view.setWarning(true)
                }
                firstFlag = scrollF(firstFlag, add_order_time_view, value)
            }

            AddOrderNormalCheckMenu.普_到货时间,
            AddOrderNormalCheckMenu.普_到货时间_校验 -> {
                if (needRed) {
                    input_receive_time.setWarning(true)
                }
                firstFlag = scrollF(firstFlag, input_receive_time, value)
            }

            AddOrderNormalCheckMenu.普_定价模式 -> {
                if (needRed) {
                    check_order_mode.setWarning(true)
                }
                firstFlag = scrollF(firstFlag, check_order_mode, value)
            }

            AddOrderNormalCheckMenu.普_拦标价 -> {
                if (needRed) {
                    input_lanbiaojia.setWarning(true)
                }
                firstFlag = scrollF(firstFlag, input_lanbiaojia, value)
            }

            AddOrderNormalCheckMenu.普_运费计算模式 -> {
                if (needRed) {
                    check_freight_type.setWarning(true)
                }
                firstFlag = scrollF(firstFlag, check_freight_type, value)
            }

            AddOrderNormalCheckMenu.普_结算依据 -> {
                if (needRed) {
                    input_balance.setWarning(true)
                }
                firstFlag = scrollF(firstFlag, input_balance, value)
            }

            AddOrderNormalCheckMenu.普_运费报价 -> {
                if (needRed) {
                    order_total_amount_view.setWarning(true)
                }
                firstFlag = scrollF(firstFlag, order_total_amount_view, value)
            }

            AddOrderNormalCheckMenu.普_货主不含税运费 -> {
                if (needRed) {
                    order_total_amount_view.setDriverPriceWarning(true)
                }
                firstFlag = scrollF(firstFlag, order_total_amount_view, value)
            }

            AddOrderNormalCheckMenu.普_报价结束时间,
            AddOrderNormalCheckMenu.普_报价时间_校验 -> {
                if (needRed) {
                    input_offer_time_end.setWarning(true)
                }
                firstFlag = scrollF(firstFlag, input_offer_time_end, value)
            }

            AddOrderNormalCheckMenu.普_整车货值 -> {
                if (needRed) {
                    order_cargo_money_view.setWarning(true)
                }
                firstFlag = scrollF(firstFlag, order_cargo_money_view, value)
            }

            AddOrderNormalCheckMenu.普_购买货物保障服务 -> {
                if (needRed) {
                    add_order_policy_view.setWarning(true)
                }
                firstFlag = scrollF(firstFlag, add_order_policy_view, value)
            }

            AddOrderNormalCheckMenu.普_购买货物保障服务_条约 -> {
                if (needRed) {
                    add_order_policy_view.setWarning(true)
                }
                firstFlag = scrollF(firstFlag, add_order_policy_view, value)
            }

            AddOrderNormalCheckMenu.普_VIP运力池承运方 -> {
                if (needRed) {
                    input_appoint_carrier.setWarning(true)
                }
                firstFlag = scrollF(firstFlag, input_appoint_carrier, value)
            }

            AddOrderNormalCheckMenu.普_是否推荐运力 -> {
                if (needRed) {
                    input_appoint_carrier.setWarning(true)
                }
                firstFlag = scrollF(firstFlag, input_appoint_carrier, value)
            }

            AddOrderNormalCheckMenu.普_预付比例 -> {
                if (needRed) {
                    input_advance_ratio.setWarning(true)
                }
                firstFlag = scrollF(firstFlag, input_advance_ratio, value)
            }

            AddOrderNormalCheckMenu.普_是否押回单 -> {
                if (needRed) {
                    input_receipt.setWarning(true)
                }
                firstFlag = scrollF(firstFlag, input_receipt, value)
            }

            AddOrderNormalCheckMenu.普_运单类型 -> {
                if (needRed) {
                    input_receipt.setWarning(true)
                }
                firstFlag = scrollF(firstFlag, input_waybill_type, value)
            }

            AddOrderNormalCheckMenu.普_是否监控发货流程 -> {
                if (needRed) {
                    input_receipt.setWarning(true)
                }
                firstFlag = scrollF(firstFlag, check_deliver_process, value)
            }

            AddOrderNormalCheckMenu.普_回单押金 -> {
                if (needRed) {
                    input_receipt_money.setWarning(true)
                }
                firstFlag = scrollF(firstFlag, input_receipt_money, value)
            }

            AddOrderNormalCheckMenu.普_押回单收件地址 -> {
                if (needRed) {
                    input_receipt_address.setWarning(true)
                }
                firstFlag = scrollF(firstFlag, input_receipt_address, value)
            }

            AddOrderNormalCheckMenu.普_是否包含油品 -> {
                if (needRed) {
                    oilCardView.setWarning1(true)
                }
                firstFlag = scrollF(firstFlag, oilCardView, value)
            }

            AddOrderNormalCheckMenu.普_油品计算方式 -> {
                if (needRed) {
                    oilCardView.setWarning1(true)
                }
                firstFlag = scrollF(firstFlag, oilCardView, value)
            }

            AddOrderNormalCheckMenu.普_油品比例 -> {
                if (needRed) {
                    oilCardView.setWarning1(true)
                }
                firstFlag = scrollF(firstFlag, oilCardView, value)
            }

            AddOrderNormalCheckMenu.普_油品固定额度 -> {
                if (needRed) {
                    oilCardView.setWarning1(true)
                }
                firstFlag = scrollF(firstFlag, oilCardView, value)
            }
//            AddOrderNormalCheckMenu.普_紧急联系人 -> {
//                if (needRed) {
//                    edit_contact_name.setWarning(true)
//                }
//                firstFlag = scrollF(firstFlag, edit_contact_name, value)
//            }
//            AddOrderNormalCheckMenu.普_紧急联系人手机 -> {
//                if (needRed) {
//                    edit_contact_phone.setWarning(true)
//                }
//                firstFlag = scrollF(firstFlag, edit_contact_phone, value)
//            }
            AddOrderNormalCheckMenu.普_发单数量校验 -> {
                if (needRed) {
                    img_warning_subtraction_order.setVisible(true)
                    cl_subtraction_order.setBackgroundResource(R.color.red_warning)
                }
                firstFlag = scrollF(firstFlag, cl_subtraction_order, value)
            }

            AddOrderNormalCheckMenu.普_承运方期望运价推送规则 -> {
                if (needRed) {
                    inputExpectedFreightRate.setWarning(true)
                }
                firstFlag = scrollF(firstFlag, inputExpectedFreightRate, value)
            }

            else -> {}
        }
    }
    return map.isEmpty()
}