package com.zczy.cargo_owner.offline.fragments

import android.os.Bundle
import android.view.View
import com.sfh.lib.mvvm.annotation.LiveDataMatch
import com.zczy.cargo_owner.R
import com.zczy.cargo_owner.deliver.addorder.JumpNewGoodsData
import com.zczy.cargo_owner.deliver.bean.DeliverMyOrderListData
import com.zczy.cargo_owner.home.dialog.X5VideoWebNoToolBarActivity
import com.zczy.cargo_owner.libcomm.config.HttpConfig
import com.zczy.cargo_owner.offline.OfflineDeliverChooseActivity
import com.zczy.cargo_owner.offline.OfflineWaybillDetailActivity
import com.zczy.cargo_owner.offline.publish.OfflineOrderPublishActivity
import com.zczy.cargo_owner.offline.adapter.OfflineAdapter
import com.zczy.cargo_owner.offline.callback.OnBtnClickListener
import com.zczy.cargo_owner.offline.model.*
import com.zczy.cargo_owner.offline.publish.DeliverDraftsEditActivityV2
import com.zczy.cargo_owner.offline.publish.DeliverDraftsNormalFragmentV2
import com.zczy.cargo_owner.order.transport.WaybillTrackingActivityV1
import com.zczy.comm.http.entity.PageList
import com.zczy.comm.http.entity.ResultData
import com.zczy.comm.ui.BaseFragment
import com.zczy.comm.widget.pulltorefresh.CommEmptyView
import kotlinx.android.synthetic.main.fragment_page_b.swipeRefreshMoreLayout
import kotlinx.android.synthetic.main.fragment_page_d.*

class PageBFragment : BaseFragment<OfflineModel>() {
    override fun getLayout(): Int {
        return R.layout.fragment_page_b
    }

    override fun initData() {

    }

    override fun bindView(view: View, bundle: Bundle?) {
        swipeRefreshMoreLayout.setAdapter(OfflineAdapter(OnBtnClickListener { btnName, orderId, truckNum, specifyFlag ->
            when (btnName) {
                "取消" -> {
                    getViewModel(OfflineModel::class.java).cancleOrder(CancleOrderReq(orderId))
                }
                "删除" -> {
                    getViewModel(OfflineModel::class.java).deleteOrder(DeleteOrderReq(orderId))
                }
                "再来一单" -> {
                    DeliverDraftsEditActivityV2.start(
                        this@PageBFragment,
                        JumpNewGoodsData(
                            orderId = orderId,
                            specifyFlag = specifyFlag.toString(),
                            pageState = JumpNewGoodsData.PageState.重新发布,
                            orderType = "0"
                        ),
                        "编辑货源",
                        DeliverDraftsNormalFragmentV2.REQUEST_EDIT
                    )
                }
                "选择承运方" -> {
                    val data = DeliverMyOrderListData()
                    data.orderId = orderId
                    OfflineDeliverChooseActivity.start(this, data, 10000)
                }
                "在途跟踪", "查看轨迹" -> {
                    context?.let { WaybillTrackingActivityV1.startUI(it, orderId, truckNum, "1") }
                }

                "同意取消运单" -> {
                    getViewModel(OfflineModel::class.java).agreeCancelOrderTender(AgreeCancelOrderTenderReq(orderId))
                }
            }
        }), true)
        swipeRefreshMoreLayout.addItemDecorationSize(20)
        swipeRefreshMoreLayout.setEmptyView(CommEmptyView.creatorDef(activity))

        swipeRefreshMoreLayout.setOnLoadListener2 { nowPage ->
            getListData(nowPage)
        }

        swipeRefreshMoreLayout.addOnItemChildClickListener { adapter, view, position ->

        }

        swipeRefreshMoreLayout.addOnItemListener { adapter, view, position ->
            val offlineResp = adapter.data[position] as OfflineResp
            OfflineWaybillDetailActivity.start(this@PageBFragment,offlineResp.orderId,10000)
        }
        swipeRefreshMoreLayout.onAutoRefresh()
    }

    private fun getListData(nowPage: Int) {
        val req = OfflineReq()
        req.nowPage = nowPage
        req.queryType = 1
        getViewModel(OfflineModel::class.java).queryOfflineListData(req)
    }

    @LiveDataMatch
    open fun onQueryOfflineListDataSuccess(data: PageList<OfflineResp>) {
        swipeRefreshMoreLayout.onRefreshCompale(data)
    }

    @LiveDataMatch
    open fun cancleOrderSuccess(data: ResultData) {
        swipeRefreshMoreLayout.onAutoRefresh()
    }

    @LiveDataMatch
    open fun deleteOrderSuccess(data: ResultData) {
        swipeRefreshMoreLayout.onAutoRefresh()
    }

    @LiveDataMatch
    open fun agreeCancelOrderTenderSuccess(data: ResultData) {
        swipeRefreshMoreLayout.onAutoRefresh()
    }

}