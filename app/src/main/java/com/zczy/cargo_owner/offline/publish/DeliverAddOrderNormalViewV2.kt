package com.zczy.cargo_owner.offline.publish

import android.text.TextUtils
import com.sfh.lib.mvvm.service.BaseViewModel
import com.zczy.cargo_owner.deliver.addorder.JumpNewGoodsData
import com.zczy.cargo_owner.deliver.addorder.PolicyTipsData
import com.zczy.cargo_owner.deliver.addorder.bean.OrderReceiptInfo
import com.zczy.cargo_owner.deliver.addorder.bean.formatAddress
import com.zczy.cargo_owner.deliver.addorder.bean.formatDetailStr
import com.zczy.cargo_owner.deliver.addorder.bean.formatStr
import com.zczy.cargo_owner.deliver.addorder.bean.normal.showLatestSettleBasisType
import com.zczy.cargo_owner.deliver.addorder.req.comm.OrderCommonTonRuleItem
import com.zczy.cargo_owner.deliver.addorder.req.comm.Rsp23QueryMobileOrderCommonInfo
import com.zczy.cargo_owner.deliver.addorder.req.comm.RspOilGasConfigInfo
import com.zczy.cargo_owner.deliver.addorder.req.comm.RspQueryTonRuleByCargoName
import com.zczy.cargo_owner.deliver.addorder.req.normal.ReqAddOrderForSeniorConsignor2
import com.zczy.cargo_owner.deliver.addorder.req.normal.ex.formatRealTotalMoney
import com.zczy.cargo_owner.deliver.addorder.req.normal.ex.getAppointCarrierList
import com.zczy.cargo_owner.deliver.addorder.req.normal.ex.getAppointCarrierSocialList
import com.zczy.cargo_owner.deliver.addorder.req.normal.ex.getVehicleType
import com.zczy.cargo_owner.deliver.addorder.req.normal.ex.setAppointCarrierList
import com.zczy.cargo_owner.deliver.addorder.req.normal.ex.setRspCheckAdvanceInfo
import com.zczy.cargo_owner.deliver.addorder.req.normal.ex2.checkEnableTotalAmount
import com.zczy.cargo_owner.deliver.addorder.req.normal.refreshPriorSelectFlag
import com.zczy.cargo_owner.deliver.bean.formatDetailStr
import com.zczy.comm.ui.BaseFragment
import com.zczy.comm.utils.ex.isTrue
import com.zczy.comm.utils.ex.setVisible
import com.zczy.comm.widget.inputv2.InputViewCheckV2
import kotlinx.android.synthetic.main.deliver_add_order_normal_fragment_v3.add_order_preferred_view
import kotlinx.android.synthetic.main.deliver_add_order_normal_fragment_v3.add_order_social_view
import kotlinx.android.synthetic.main.deliver_add_order_normal_fragment_v3.add_order_time_view
import kotlinx.android.synthetic.main.deliver_add_order_normal_fragment_v3.address_view
import kotlinx.android.synthetic.main.deliver_add_order_normal_fragment_v3.check_deliver_process
import kotlinx.android.synthetic.main.deliver_add_order_normal_fragment_v3.check_freight_type
import kotlinx.android.synthetic.main.deliver_add_order_normal_fragment_v3.check_order_mode
import kotlinx.android.synthetic.main.deliver_add_order_normal_fragment_v3.edit_contact_name
import kotlinx.android.synthetic.main.deliver_add_order_normal_fragment_v3.edit_contact_phone
import kotlinx.android.synthetic.main.deliver_add_order_normal_fragment_v3.inputExpectedFreightRate
import kotlinx.android.synthetic.main.deliver_add_order_normal_fragment_v3.input_advance_ratio
import kotlinx.android.synthetic.main.deliver_add_order_normal_fragment_v3.input_appoint_carrier
import kotlinx.android.synthetic.main.deliver_add_order_normal_fragment_v3.input_balance
import kotlinx.android.synthetic.main.deliver_add_order_normal_fragment_v3.input_goods_detail
import kotlinx.android.synthetic.main.deliver_add_order_normal_fragment_v3.input_jszxdh
import kotlinx.android.synthetic.main.deliver_add_order_normal_fragment_v3.input_lanbiaojia
import kotlinx.android.synthetic.main.deliver_add_order_normal_fragment_v3.input_monitor
import kotlinx.android.synthetic.main.deliver_add_order_normal_fragment_v3.input_offer_time_end
import kotlinx.android.synthetic.main.deliver_add_order_normal_fragment_v3.input_order_loss
import kotlinx.android.synthetic.main.deliver_add_order_normal_fragment_v3.input_order_mark
import kotlinx.android.synthetic.main.deliver_add_order_normal_fragment_v3.input_project_name
import kotlinx.android.synthetic.main.deliver_add_order_normal_fragment_v3.input_receipt
import kotlinx.android.synthetic.main.deliver_add_order_normal_fragment_v3.input_receipt_address
import kotlinx.android.synthetic.main.deliver_add_order_normal_fragment_v3.input_receipt_money
import kotlinx.android.synthetic.main.deliver_add_order_normal_fragment_v3.input_receive_time
import kotlinx.android.synthetic.main.deliver_add_order_normal_fragment_v3.input_rule
import kotlinx.android.synthetic.main.deliver_add_order_normal_fragment_v3.input_vehicle_type
import kotlinx.android.synthetic.main.deliver_add_order_normal_fragment_v3.input_waybill_type
import kotlinx.android.synthetic.main.deliver_add_order_normal_fragment_v3.input_zdybh
import kotlinx.android.synthetic.main.deliver_add_order_normal_fragment_v3.input_zdzxdh
import kotlinx.android.synthetic.main.deliver_add_order_normal_fragment_v3.input_zxhyq
import kotlinx.android.synthetic.main.deliver_add_order_normal_fragment_v3.oilCardView
import kotlinx.android.synthetic.main.deliver_add_order_normal_fragment_v3.oilCardViewDeDa
import kotlinx.android.synthetic.main.deliver_add_order_normal_fragment_v3.order_total_amount_view
import kotlinx.android.synthetic.main.deliver_add_order_normal_fragment_v3.policy_hint_view
import kotlinx.android.synthetic.main.deliver_add_order_normal_fragment_v3.tvExpectedFreightRate

/**
 * PS: 发货 - 发新货 - 发普通货
 * Created by sdx on 2019/2/1.
 */
abstract class DeliverAddOrderNormalViewV2<VM : BaseViewModel> : BaseFragment<VM>() {

    /** 主请求体 */
    protected abstract val mData2: ReqAddOrderForSeniorConsignor2

    /** 发货 来源 数据 */
    protected abstract val mJumpData: JumpNewGoodsData

    /** 初始化 配置 数据 */
    protected abstract var mOrderCommonInfo: Rsp23QueryMobileOrderCommonInfo

    /** 油气品默认值查询 */
    protected abstract var mRspOilGasConfigInfo: RspOilGasConfigInfo

    /** 保险相关数据 */
    //    protected abstract var mOrderPolicyInfo: OrderPolicyInfo?

    /** 跳转过来的初始化数据 */
    protected abstract var initJump: Boolean

    /**  */
    protected abstract var policyTipsData: PolicyTipsData
    protected abstract var mRspQueryTonRuleByCargoName: RspQueryTonRuleByCargoName

    /** 刷新 发新货 初始化 */
    protected fun refreshInitData() {
        refreshAddress() // 地址
        refreshContact() // 紧急联系人
        refreshSettleBasisType() // 结算依据
        refreshLabiaojia() // 拦标价
        refreshRule()
        refreshTonRule()
        //        refreshReceipt() // 押回单
        refreshAdvance() // 预付款
        refreshAppointCarrier() // 指定承运人
        refreshPreferredView() //优选专区
        refreshSocialView()
        refreshSdOilCard() // 油卡
        refreshSdOilCardDeDa() // 油卡
        refreshMoble()
        refreshTotalAmount() // 运费报价
        refreshOrderType() //运单类型
        refreshProjectName() //项目名称
        refreshOrderProcess() //是否监控收发货流程

        if (mData2.orderInfo.despatchStart.isNotEmpty()) {
            refreshDespatchTime() //最早到场装货时间,最晚到场装货时间
        }
        if (mData2.orderInfo.receiveDate.isNotEmpty()) {
            refreshReceiveDate() //收货时间
        }
    }

    /** 刷新 草稿箱 发货  */
    protected fun refreshDraftsData(guaranteeLeft: String?) {
        refreshAllView(guaranteeLeft)
    }

    /** 刷新 所有界面 */
    private fun refreshAllView(guaranteeLeft: String?) {
        refreshPolicyHint() // 保险提示信息标志
        refreshAddress() // 普_从 到
        refreshGoodsDetail() // 货物明细
        refreshTypeLength() // 车型车长
        refreshDespatchTime() // 装货时间
        refreshReceiveDate() // 收货时间
        refreshExpectTime() // 报价结束时间
        refreshOrderModel() // 定价模式
        refreshLabiaojia() // 拦标价
        refreshRule()
        refreshFreightType() // 运费计算方式
        refreshRule()
        refreshTonRule()
        refreshSettleBasisType() // 结算依据
        refreshTotalAmount() // 运费报价
        refreshCargoMoney() // 整车货值
        refreshCargoMoneyGuaranteeLeft(guaranteeLeft ?: "")
        refreshPolicyFlag() // 购买货物保障服务
        refreshAppointCarrier() // 指定承运方
        refreshPreferredView() //优选专区
        refreshSocialView()
        refreshAdvance() // 是否预付款
        //        refreshReceipt()// 是否押回单
        refreshSdOilCard() // 是否支持油卡
        refreshSdOilCardDeDa() // 是否支持油卡
        refreshContact() // 紧急联系人 紧急联系人手机号
        refreshMoble()
        refreshZXHYQ() // 装卸货要求
        refreshZDYBH() // 自定义编号
        refreshOrderMark() //运单标识
        refreshMonitor() //定制化监控
        refreshOrderType() //运单类型
        refreshProjectName() //项目名称
        refreshOrderProcess() //是否监控收发货流程
    }

    /** 刷新 险提示信息标志 */
    protected fun refreshPolicyHint() {
        // 保险提示信息标志：0 不展示，1 展示
        if (policyTipsData.policyTipsFlagByCargoName) {
            policy_hint_view.setVisible(true)
            //            add_order_policy_view.setVisible(false)
        } else {
            policy_hint_view.setVisible(false)
            //            add_order_policy_view.setVisible(true)
        }
    }

    /** 刷新 从 到 */
    protected fun refreshAddress() {
        address_view.setAddress(mData2.orderAddressInfo)
    }

    /** 刷新 货物明细 */
    protected fun refreshGoodsDetail() {
        input_goods_detail.content = mData2.cargoList.formatDetailStr()
    }

    /** 刷新 车长车型 */
    protected fun refreshTypeLength() {
        input_vehicle_type.content = mData2.getVehicleType().formatDetailStr()
    }

    /** 刷新 装货时间 */
    protected fun refreshDespatchTime() {
        add_order_time_view.setTime(mData2.orderInfo.despatchStart, mData2.orderInfo.despatchEnd)
        add_order_time_view.setWarning(false)
    }

    /** 刷新 收货时间 */
    protected fun refreshReceiveDate() {
        input_receive_time.content = mData2.orderInfo.receiveDate
        input_receive_time.setWarning(false)
    }

    /** 刷新 报价结束时间 */
    protected fun refreshExpectTime() {
        if (mData2.orderInfo.orderModel == "1") {
            input_offer_time_end.setVisible(true)
            input_offer_time_end.content = mData2.orderInfo.expectTime
        } else {
            input_offer_time_end.setVisible(false)
            input_offer_time_end.content = ""
        }
        input_offer_time_end.setWarning(false)
    }

    /** 刷新 定价模式 */
    protected fun refreshOrderModel() {
        when (mData2.orderInfo.orderModel) {
            "0" -> { // 0 抢单
                check_order_mode.check = InputViewCheckV2.LEFT
                input_offer_time_end.setVisible(false)
                order_total_amount_view.setVisible(true)
            }

            "1" -> { // 1 议价
                check_order_mode.check = InputViewCheckV2.RIGHT
                input_offer_time_end.content = mData2.orderInfo.expectTime
                input_offer_time_end.setVisible(true)
                order_total_amount_view.setVisible(false)
            }

            else -> {
                check_order_mode.check = InputViewCheckV2.NONE
            }
        }
        check_order_mode.setWarning(false)
    }

    /** 刷新 拦标价 */
    protected fun refreshLabiaojia() {
        input_lanbiaojia.setVisible(mData2.orderInfo.orderModel == "1")
        input_lanbiaojia.content = mData2.orderInfo.blockMoney
        input_lanbiaojia.setWarning(false)
    }

    /** 刷新 运费计算方式 */
    protected fun refreshFreightType() {
        when (mData2.orderInfo.freightType) {
            "0" -> { // 0 包车价
                check_freight_type.check = InputViewCheckV2.LEFT
            }

            "1" -> { // 1 单价
                check_freight_type.check = InputViewCheckV2.RIGHT
            }

            else -> check_freight_type.check = InputViewCheckV2.NONE
        }
    }

    /** 刷新 结算依据 */
    protected fun refreshSettleBasisType() {
        // 货主是否支持结算调整：0 否，1 是
        if (mData2.orderInfo.settleBasisType.isEmpty()) {
            mData2.orderInfo.settleBasisType = mOrderCommonInfo.latestSettleBasisType
        }
        input_balance.setVisible(true)
        input_balance.content = mData2.orderInfo.showLatestSettleBasisType()
        input_balance.setWarning(false)
    }

    protected fun refreshRule() {
        input_rule.setVisible(mData2.orderInfo.orderModel == "1" && mOrderCommonInfo.supportAutoDealFlag == "1")
        input_rule.content = mData2.orderInfo.ruleName
        input_rule.setWarning(false)
    }

    private fun refreshTonRule() {
        input_order_loss.setVisible(TextUtils.equals("1", mOrderCommonInfo.whetherShowTonRule))

        when (mOrderCommonInfo.whetherShowTonRule) {
            "1" -> {
                mRspQueryTonRuleByCargoName.tonRuleList?.let {
                    val tonRuleId = mData2.orderInfo.tonRuleId
                    if (!TextUtils.isEmpty(tonRuleId)) {
                        val hasTonRuleId = hasTonRuleId(it, tonRuleId)
                        if (hasTonRuleId == null) {
                            it.forEach { tonRuleIdItem ->
                                if (TextUtils.equals(tonRuleIdItem.tonRuleDefaultFlag, "1")) {
                                    input_order_loss.content = tonRuleIdItem.tonRuleName
                                    mData2.orderInfo.tonRuleId = tonRuleIdItem.tonRuleId
                                }
                            }
                        } else {
                            //存在配置
                            input_order_loss.content = hasTonRuleId.tonRuleName
                            mData2.orderInfo.tonRuleId = hasTonRuleId.tonRuleId
                        }
                    } else {
                        it.forEach { tonRuleIdItem ->
                            if (TextUtils.equals(tonRuleIdItem.tonRuleDefaultFlag, "1")) {
                                input_order_loss.content = tonRuleIdItem.tonRuleName
                                mData2.orderInfo.tonRuleId = tonRuleIdItem.tonRuleId
                            }
                        }
                    }
                }
            }

            else -> {
                mData2.orderInfo.tonRuleId = ""
            }
        }

    }

    private fun hasTonRuleId(
        tonRuleNameJSONArray: MutableList<OrderCommonTonRuleItem>,
        tonRuleId: String
    ): OrderCommonTonRuleItem? {
        val filter = tonRuleNameJSONArray.filter { TextUtils.equals(it.tonRuleId, tonRuleId) }
        return if (filter.isEmpty()) {
            //不包含该配置
            null
        } else {
            //包含该配置
            filter[0]
        }
    }

    /** 刷新 运费报价 */
    protected fun refreshTotalAmount() {
        val realTotalMoney = if (mData2.orderInfo.freightType == "1") {
            mData2.formatRealTotalMoney()
        } else {
            ""
        }
        order_total_amount_view.setWarning(false)
        order_total_amount_view.setDriverPriceWarning(false)
        order_total_amount_view.setData(
            freightType = mData2.orderInfo.freightType,
            totalAmount = mData2.orderInfo.totalAmount,
            cargoList = mData2.cargoList,
            realTotalMoney = realTotalMoney,
            orderModelParam = mData2.orderInfo.orderModel,
            supportConsignorNoTaxMoneyFlagParam = mOrderCommonInfo.supportConsignorNoTaxMoneyFlag,
            consignorNoTaxMoney = mData2.orderInfo.consignorNoTaxMoney,
        )
        val map = mData2.checkEnableTotalAmount(mOrderCommonInfo)
        order_total_amount_view.setEditEnable(map.isEmpty())
    }

    /** 刷新 整车货值 */
    protected fun refreshCargoMoney() {
        //        val map = mData2.checkEnableCargoMoney(mOrderCommonInfo)
        //        order_cargo_money_view.setEditEnable(map.isEmpty())
        //        order_cargo_money_view.setData(mData2.orderInfo.cargoMoney, "")
    }

    /** 刷新 整车货值 */
    protected fun refreshCargoMoneyGuaranteeLeft(guaranteeLeft: String) {
        //        order_cargo_money_view.setGuaranteeLeft(guaranteeLeft)
    }

    /** 刷新 购买货物保证服务 */
    protected fun refreshPolicyFlag() {
        //        add_order_policy_view.setWarning(false)
        //        add_order_policy_view.refreshNormalData(
        //            policyTipsData = policyTipsData,
        //            orderInfo = mData2.orderInfo,
        //            orderCommonInfo = mOrderCommonInfo
        //        )
    }

    private fun refreshSocialView() {
        add_order_social_view.refreshNormalData(mData2.orderInfo, mOrderCommonInfo)
    }

    // 指定承运人
    protected fun refreshAppointCarrier() {
        // 货主是否支持指定承运方：0 否，1 是
        if (mOrderCommonInfo.appointCarrierSwitch.isTrue || mOrderCommonInfo.supportSocialAppointFlag.isTrue) {
            input_appoint_carrier.setVisible(true)
            //            + mData2.getAppointCarrierGroupList().formatStr() zczy-4051 运力组和承运方合并
            input_appoint_carrier.content =
                mData2.getAppointCarrierList().formatStr() + mData2.getAppointCarrierSocialList()
                    .formatStr()
        } else {
            input_appoint_carrier.setVisible(false)
            mData2.setAppointCarrierList(
                data1 = emptyList(),
                //                    data2 = emptyList(),
                data3 = emptyList(),
                orderCommonInfo = mOrderCommonInfo
            )
        }
    }

    protected fun refreshPreferredView() {
        mData2.refreshPriorSelectFlag()
        add_order_preferred_view.refreshNormalData(mData2.orderInfo, mOrderCommonInfo)
    }

    protected fun getCheckState(): Boolean {
        return add_order_social_view.getCheckState()
    }

    // 预付款
    protected fun refreshAdvance() {
        // 货主是否支持指定承运方：0 否，1 是
        if (mOrderCommonInfo.appointCarrierSwitch.isTrue && mData2.orderInfo.isSelectSocial.isTrue) {
            //            check_advance.setVisible(true)
            input_advance_ratio.setVisible(true)
            when (mData2.orderInfo.advanceFlag) { // 是否预付：0 否,1 是
                "0" -> {
                    //                    check_advance.check = InputViewCheckV2.RIGHT
                    input_advance_ratio.setVisible(false)
                }

                "1" -> {
                    //                    check_advance.check = InputViewCheckV2.LEFT
                    input_advance_ratio.setVisible(true)
                    input_advance_ratio.content = mData2.orderAdvanceInfo.advanceRatio
                }

                else -> {
                    //                    check_advance.check = InputViewCheckV2.NONE
                    input_advance_ratio.setVisible(false)
                }
            }
        } else {
            //            check_advance.setVisible(false)
            input_advance_ratio.setVisible(false)
            mData2.setRspCheckAdvanceInfo("", "")
        }
    }

    // 押回单回单
    protected fun refreshReceipt() {
        // 货主是否开启押回单功能：0 否，1 是
        if (mOrderCommonInfo.whetherShowBackOrderChoose.isTrue) {
            input_receipt.setVisible(true)
            when (mData2.orderInfo.receiptFlag) { // 是否押回单：0 否,1 是
                "0" -> {
                    input_receipt.check = InputViewCheckV2.RIGHT
                    input_receipt_money.setVisible(false)
                    input_receipt_address.setVisible(false)
                }

                "1" -> {
                    input_receipt.check = InputViewCheckV2.LEFT
                    input_receipt_money.setVisible(true)
                    input_receipt_address.setVisible(true)
                    input_receipt_money.content = mData2.orderReceiptInfo.receiptMoney
                    input_receipt_address.content = mData2.orderReceiptInfo.formatAddress()
                }

                else -> {
                    input_receipt.check = InputViewCheckV2.NONE
                }
            }
        } else {
            input_receipt.setVisible(false)
            mData2.orderInfo.receiptFlag = "0"
            mData2.orderReceiptInfo = OrderReceiptInfo()
        }
    }

    // 是否支持油卡
    protected fun refreshSdOilCard() {
        mData2.orderInfo.apply {
            if (TextUtils.equals("1", mOrderCommonInfo.whetherDedaUserId)) {
                oilCardView.setVisible(false)
                return
            }
            oilCardView.refreshNormalData(
                orderInfo = this,
                orderCommonInfo = mOrderCommonInfo,
                rspOilGasConfigInfo = mRspOilGasConfigInfo
            )
        }
    }

    protected fun refreshOilGasPreferential(money: String?) {
        oilCardView.refreshOilGasPreferential(money)
    }

    // 是否支持油卡(德达)
    protected fun refreshSdOilCardDeDa() {
        mData2.orderInfo.apply {
            if (!TextUtils.equals("1", mOrderCommonInfo.whetherDedaUserId)) {
                oilCardViewDeDa.setVisible(false)
                return
            }
            oilCardViewDeDa.refreshViewV2()
        }
    }

    // 紧急联系人
    protected fun refreshContact() {
        edit_contact_name.content = mData2.orderInfo.contactName
        edit_contact_phone.content = mData2.orderInfo.contactPhone
    }

    // 紧急联系人
    protected fun refreshMoble() {
        input_zdzxdh.content = mData2.orderInfo.delistConsultMobile
        input_jszxdh.content = mData2.orderInfo.settleConsultMobile
    }

    // 装卸货要求
    protected fun refreshZXHYQ() {
        input_zxhyq.content = mData2.orderInfo.prompt
    }

    // 自定义编号
    protected fun refreshZDYBH() {
        input_zdybh.content = mData2.orderInfo.selfComment
    }

    // 运单标识
    protected fun refreshOrderMark() {
        input_order_mark.content = mData2.orderInfo.orderMark
    }

    // 运单类型
    protected fun refreshOrderType() {
        when (mData2.orderInfo.orderCargoType) {
            "0" -> {
                input_waybill_type.content = "普货"
            }

            "2" -> {
                input_waybill_type.content = "小包"
            }

            "1" -> {
                input_waybill_type.content = "大件"
            }
        }
    }

    // 项目名称
    protected fun refreshProjectName() {
        input_project_name.content = mData2.orderInfo.projectName
    }

    // 是否监控收发货流程
    protected fun refreshOrderProcess() {
        when (mData2.orderInfo.monitorFlag) {
            "0" -> {
                check_deliver_process.content = "否"
            }

            "1" -> {
                check_deliver_process.content = "监控收发货流程"
            }

            "2" -> {
                check_deliver_process.content = "仅监控收货流程"
            }
        }
    }

    // 定制化监控
    private fun refreshMonitor() {
        when (mData2.orderInfo.orderMonitor) {
            "1" -> {
                input_monitor.check = InputViewCheckV2.LEFT
            }

            else -> {
                input_monitor.check = InputViewCheckV2.RIGHT
            }
        }
    }

    fun refreshInputExpectedFreightRate() {
        if (TextUtils.equals(
                "2",
                mOrderCommonInfo.autoPushCarrierPrice
            ) && mData2.orderInfo.orderModel.isTrue
        ) {
            inputExpectedFreightRate.setVisible(true)
            tvExpectedFreightRate.setVisible(true)
            inputExpectedFreightRate.content = "实时推送"
            mData2.orderInfo.publishAutoPushCarrierPrice = "1"
        } else {
            inputExpectedFreightRate.setVisible(false)
            tvExpectedFreightRate.setVisible(false)
        }
    }
}