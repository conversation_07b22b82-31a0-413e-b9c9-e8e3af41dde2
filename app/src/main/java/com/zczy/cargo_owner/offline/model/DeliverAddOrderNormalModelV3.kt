package com.zczy.cargo_owner.offline.model

import android.text.TextUtils
import com.sfh.lib.exception.HandleException
import com.sfh.lib.mvvm.service.BaseViewModel
import com.sfh.lib.rx.IResult
import com.sfh.lib.rx.IResultSuccessNoFail
import com.zczy.cargo_owner.deliver.addorder.JumpNewGoodsData
import com.zczy.cargo_owner.deliver.addorder.bean.CargoInfo
import com.zczy.cargo_owner.deliver.addorder.bean.OrderAddressInfo
import com.zczy.cargo_owner.deliver.addorder.req.ReqGetPendingOrderIds
import com.zczy.cargo_owner.deliver.addorder.req.batch.RspAddResult
import com.zczy.cargo_owner.deliver.addorder.req.comm.*
import com.zczy.cargo_owner.deliver.addorder.req.normal.*
import com.zczy.cargo_owner.offline.req.Req56GetSuccessImportNum
import com.zczy.comm.http.entity.BaseRsp
import com.zczy.comm.utils.ex.isTrue
import com.zczy.comm.utils.ex.toCommaString
import io.reactivex.Observable
import io.reactivex.Observer
import io.reactivex.android.schedulers.AndroidSchedulers
import io.reactivex.disposables.Disposable
import io.reactivex.functions.BiFunction
import io.reactivex.schedulers.Schedulers
import java.util.concurrent.TimeUnit

/**
 * PS:
 * Created by sdx on 2019/2/13.
 */
class DeliverAddOrderNormalModelV3 : BaseViewModel() {

    /**
     * 发新货 普通货 初始化  网络请求
     */
    fun doNewGoodsInitRequestV3() {
        execute(
            true,
            Req23QueryMobileOrderCommonInfo2()
        ) { t ->
            if (t.success()) {
                setValue("onReq23QueryMobileOrderCommonInfo2", t.data)
            } else {
                showDialogToast(t.msg)
            }
        }
    }

    fun queryCargoDespatchOrDeliveryInfo33(outOrderNumber: String) {
        execute(
            true,
            Req33QueryCargoDespatchOrDeliveryInfo(outOrderNumber = outOrderNumber)
        ) { t ->
            if (t.success()) {
                setValue("onReq33QueryCargoDespatchOrDeliveryInfo", t.data)
            } else {
                showDialogToast(t.msg)
            }
        }
    }

    // 普通货草稿箱：跳转到已认证货主修改普通货运单页面
    fun jumpToMobileSeniorConsignorUpdateOrder2(mJumpData: JumpNewGoodsData) {
        val jumpNormalData = JumpNormalData()
        Observable.zip(
            Req23QueryMobileOrderCommonInfo2().task,
            ReqJumpToMobileSeniorConsignorUpdateOrder2().setJumpData(mJumpData).task
        ) { t1, t2 ->
            when {
                !t1.success() -> {
                    throw HandleException(t1.code.toIntOrNull() ?: 0, t1.msg)
                }

                !t2.success() -> {
                    throw HandleException(t2.code.toIntOrNull() ?: 0, t2.msg)
                }
            }
            jumpNormalData.initData = t1.data
            jumpNormalData.jumpData = t2.data
            true
        }
            .map {
                true
            }
            .subscribeOn(Schedulers.io())
            .observeOn(AndroidSchedulers.mainThread())
            .doOnSubscribe { showLoading(true) }
            .subscribe(object : Observer<Boolean> {
                override fun onSubscribe(d: Disposable) {
                    putDisposable(d)
                }

                override fun onNext(t: Boolean) {
                    setValue("onJumpToMobileSeniorConsignorUpdateOrder", jumpNormalData)
                }

                override fun onError(e: Throwable) {
                    showDialogToast(e.message)
                    hideLoading()
                }

                override fun onComplete() {
                    hideLoading()
                }
            })
    }

    // 10 查询押回单信息
    fun queryOrderReceipt() {
        execute(
            true,
            Req10QueryOrderReceipt()
        ) { baseRsp ->
            hideLoading()
            if (baseRsp.success()) {
                setValue("onQueryOrderReceiptSuccess", baseRsp.data)
            } else {
                showDialogToast(baseRsp.msg)
            }
        }
    }

    // 10 查询押回单信息 金额list
    fun queryOrderReceiptList() {
        execute(
            true,
            Req10QueryOrderReceipt()
        ) { baseRsp ->
            hideLoading()
            if (baseRsp.success()) {
                setValue("onQueryOrderReceiptListSuccess", baseRsp.data)
            } else {
                showDialogToast(baseRsp.msg)
            }
        }
    }

    // 17 普通货：根据货物名称，审核状态，货值，赠保额度：查询保障服务相关信息
    fun queryOrderPolicyInfo(
        cargoList: List<CargoInfo>,
        giftMoney: String,
        cargoMoney: String,
        totalMoney: String,
        weight: String,
        changePolicyToCarrier: String,
        orderAddressInfo: OrderAddressInfo
    ) {
        execute(
            Req17QueryOrderPolicyInfo(
                giftMoney = giftMoney,
                cargoNameStr = cargoList.toCommaString { it.cargoName },
                cargoMoney = cargoMoney,
                totalMoney = totalMoney,
                weight = weight,
                policyType = when (changePolicyToCarrier) {
                    "1" -> {
                        "5"
                    }

                    else -> {
                        "1"
                    }
                },
                changePolicyToCarrier = changePolicyToCarrier,
                despatchPro = orderAddressInfo.despatchPro,
                despatchCity = orderAddressInfo.despatchCity,
                despatchDis = orderAddressInfo.despatchDis,
                despatchPlace = orderAddressInfo.despatchPlace,
                despatchCoordinateX = orderAddressInfo.despatchCoordinateX,
                despatchCoordinateY = orderAddressInfo.despatchCoordinateY,
                deliverPro = orderAddressInfo.deliverPro,
                deliverCity = orderAddressInfo.deliverCity,
                deliverDis = orderAddressInfo.deliverDis,
                deliverPlace = orderAddressInfo.deliverPlace,
                deliverCoordinateX = orderAddressInfo.deliverCoordinateX,
                deliverCoordinateY = orderAddressInfo.deliverCoordinateY
            )
        ) { baseRsp ->
            if (baseRsp.success()) {
                setValue("onQueryOrderPolicyInfo", baseRsp.data, cargoMoney)
            } else {
                showDialogToast(baseRsp.msg)
            }
        }
    }

    //转嫁查询气泡提示
    fun queryOrderPolicyInfoChangeModelV1(
        cargoList: List<CargoInfo>,
        giftMoney: String,
        cargoMoney: String,
        totalMoney: String,
        weight: String,
        changePolicyToCarrier: String,
        orderAddressInfo: OrderAddressInfo
    ) {
        execute(
            Req17QueryOrderPolicyInfo(
                giftMoney = giftMoney,
                cargoNameStr = cargoList.toCommaString { it.cargoName },
                cargoMoney = cargoMoney,
                totalMoney = totalMoney,
                weight = weight,
                policyType = when (changePolicyToCarrier) {
                    "1" -> {
                        "5"
                    }

                    else -> {
                        "1"
                    }
                },
                changePolicyToCarrier = changePolicyToCarrier,
                despatchPro = orderAddressInfo.despatchPro,
                despatchCity = orderAddressInfo.despatchCity,
                despatchDis = orderAddressInfo.despatchDis,
                despatchPlace = orderAddressInfo.despatchPlace,
                despatchCoordinateX = orderAddressInfo.despatchCoordinateX,
                despatchCoordinateY = orderAddressInfo.despatchCoordinateY,
                deliverPro = orderAddressInfo.deliverPro,
                deliverCity = orderAddressInfo.deliverCity,
                deliverDis = orderAddressInfo.deliverDis,
                deliverPlace = orderAddressInfo.deliverPlace,
                deliverCoordinateX = orderAddressInfo.deliverCoordinateX,
                deliverCoordinateY = orderAddressInfo.deliverCoordinateY
            ),
            IResultSuccessNoFail { baseRsp ->
                if (baseRsp.success()) {
                    setValue("onQueryOrderPolicyInfoV1", baseRsp.data)
                }
            })
    }

    // 17 普通货：根据货物名称，审核状态，货值，赠保额度：查询保障服务相关信息
    fun queryOrderPolicyInfoChangeModel(
        cargoList: List<CargoInfo>,
        giftMoney: String,
        cargoMoney: String,
        totalMoney: String,
        weight: String,
        changePolicyToCarrier: String,
        orderAddressInfo: OrderAddressInfo
    ) {
        execute(
            Req17QueryOrderPolicyInfo(
                giftMoney = giftMoney,
                cargoNameStr = cargoList.toCommaString { it.cargoName },
                cargoMoney = cargoMoney,
                totalMoney = totalMoney,
                weight = weight,
                policyType = when (changePolicyToCarrier) {
                    "1" -> {
                        "5"
                    }

                    else -> {
                        "1"
                    }
                },
                changePolicyToCarrier = changePolicyToCarrier,
                despatchPro = orderAddressInfo.despatchPro,
                despatchCity = orderAddressInfo.despatchCity,
                despatchDis = orderAddressInfo.despatchDis,
                despatchPlace = orderAddressInfo.despatchPlace,
                despatchCoordinateX = orderAddressInfo.despatchCoordinateX,
                despatchCoordinateY = orderAddressInfo.despatchCoordinateY,
                deliverPro = orderAddressInfo.deliverPro,
                deliverCity = orderAddressInfo.deliverCity,
                deliverDis = orderAddressInfo.deliverDis,
                deliverPlace = orderAddressInfo.deliverPlace,
                deliverCoordinateX = orderAddressInfo.deliverCoordinateX,
                deliverCoordinateY = orderAddressInfo.deliverCoordinateY
            ),
            IResultSuccessNoFail<BaseRsp<RspQueryOrderPolicyInfo>> { baseRsp ->
                if (baseRsp.success()) {
                    setValue("onQueryOrderPolicyInfo", baseRsp.data, cargoMoney)
                }
            })
    }

    // 17 普通货：根据货物名称，审核状态，货值，赠保额度：查询保障服务相关信息
    fun queryOrderPolicyInfoDialog(
        cargoList: List<CargoInfo>,
        giftMoney: String,
        cargoMoney: String,
        totalMoney: String,
        weight: String,
        changePolicyToCarrier: String,
        orderAddressInfo: OrderAddressInfo
    ) {
        execute(
            true,
            Req17QueryOrderPolicyInfo(
                giftMoney = giftMoney,
                cargoNameStr = cargoList.toCommaString { it.cargoName },
                cargoMoney = cargoMoney,
                totalMoney = totalMoney,
                weight = weight,
                policyType = when (changePolicyToCarrier) {
                    "1" -> {
                        "5"
                    }

                    else -> {
                        "1"
                    }
                },
                changePolicyToCarrier = changePolicyToCarrier,
                despatchPro = orderAddressInfo.despatchPro,
                despatchCity = orderAddressInfo.despatchCity,
                despatchDis = orderAddressInfo.despatchDis,
                despatchPlace = orderAddressInfo.despatchPlace,
                despatchCoordinateX = orderAddressInfo.despatchCoordinateX,
                despatchCoordinateY = orderAddressInfo.despatchCoordinateY,
                deliverPro = orderAddressInfo.deliverPro,
                deliverCity = orderAddressInfo.deliverCity,
                deliverDis = orderAddressInfo.deliverDis,
                deliverPlace = orderAddressInfo.deliverPlace,
                deliverCoordinateX = orderAddressInfo.deliverCoordinateX,
                deliverCoordinateY = orderAddressInfo.deliverCoordinateY
            )
        ) { baseRsp ->
            if (baseRsp.success()) {
                setValue("onQueryOrderPolicyInfoDialog", baseRsp.data)
            } else {
                showDialogToast(baseRsp.msg)
            }
        }
    }

    // 16.PC端和手机端接口：校验增值服务信息
    fun checkAdvanceInfo(data: ReqAddOrderForSeniorConsignor2) {
        execute(true,
            Req16CheckAdvanceInfo().apply { setReqAddOrderForSeniorConsignor(data) }
        ) { baseRsp ->
            hideLoading()
            if (baseRsp.success()) {
                setValue("onCheckAdvanceInfo", baseRsp.data)
            } else {
                showDialogToast(baseRsp.msg)
            }
        }
    }

    //    // 22 根据货物名称查询是否展示保险提示信息标志
    //    fun queryPolicyTipsFlagByCargoName(cargoNameStr: String) {
    //        execute(
    //            true,
    //            Req22QueryPolicyTipsFlagByCargoName(cargoNameStr = cargoNameStr)
    //        ) { baseRsp ->
    //            hideLoading()
    //            if (baseRsp.success()) {
    //                setValue("onQueryPolicyTipsFlagByCargoName", baseRsp.data)
    //            } else {
    //                showDialogToast(baseRsp.msg)
    //            }
    //        }
    //    }

    fun commit(mData: ReqAddOrderForSeniorConsignor2, state: JumpNewGoodsData.PageState) {
        /**
         * 操作方式
         * add                  新增页面保存运单
         * publish              新增页面发布运单
         *
         * update               编辑页面保存运单
         * editpublish          编辑页面发布运单
         *
         * reupdate             重新发布页面保存运单
         * republish            重新发布页面发布运单
         *
         * import               导入发布运单
         *
         * addpublish           新增页面保存并发布运单
         * editaddpublish       编辑页面保存并发布运单
         * readdpublish         重新发布页面保存并发布运单
         */
        when (state) {
            JumpNewGoodsData.PageState.新增 -> {
                mData.orderInfo.operation = "publish"
            }

            JumpNewGoodsData.PageState.编辑 -> {
                mData.orderInfo.operation = "editpublish"
            }

            JumpNewGoodsData.PageState.重新发布 -> {
                mData.orderInfo.operation = "republish"
            }

            else -> {}
        }

        execute(
            false,
            mData,
            object : IResult<BaseRsp<RspAddResult>> {
                override fun onSuccess(baseRsp: BaseRsp<RspAddResult>) {
                    hideLoading()
                    if (baseRsp.success()) {
                        if (mData.orderInfo.batchPublishNum.isTrue) {
                            setValue("onCommitSuccess")
                        } else {
                            getSuccessImportNum()
                        }
                    } else {
                        when (val code = baseRsp.code) {
                            "3027" -> {
                                getPendingOrderIds("1", baseRsp.msg, code)
                            }

                            "3028" -> {
                                getPendingOrderIds("2", baseRsp.msg, code)
                            }

                            "3029" -> {
                                getPendingOrderIds("3", baseRsp.msg, code)
                            }

                            "2222" -> {
                                setValue("onCommitNoMoney", baseRsp.data)
                            }

                            else -> {
                                showDialogToast(baseRsp.msg)
                            }
                        }
                    }
                }

                override fun onFail(p0: HandleException?) {
                    if (TextUtils.equals("10005", p0?.msg)) {
                        setValue("onCommitSuccess")
                    }
                }
            })
    }

    fun getPendingOrderIds(type: String, msg: String, code: String) {
        execute(
            ReqGetPendingOrderIds(type = type)
        ) { response ->
            if (response.success()) {
                response.data?.msg = msg
                response.data?.code = code
                setValue("onGetPendingOrderIdsSuccess", response.data)
            }
        }
    }

    //  保存草稿
    fun saveDrafts(mData: ReqAddOrderForSeniorConsignor2, state: JumpNewGoodsData.PageState) {
        /**
         * 操作方式
         * add                  新增页面保存运单
         * publish              新增页面发布运单
         *
         * update               编辑页面保存运单
         * editpublish          编辑页面发布运单
         *
         * reupdate             重新发布页面保存运单
         * republish            重新发布页面发布运单
         *
         * import               导入发布运单
         *
         * addpublish           新增页面保存并发布运单
         * editaddpublish       编辑页面保存并发布运单
         * readdpublish         重新发布页面保存并发布运单
         */
        when (state) {
            JumpNewGoodsData.PageState.新增 -> {
                mData.orderInfo.operation = "add"
            }

            JumpNewGoodsData.PageState.编辑 -> {
                mData.orderInfo.operation = "update"
            }

            JumpNewGoodsData.PageState.重新发布 -> {
                mData.orderInfo.operation = "reupdate"
            }

            else -> {}
        }
        execute(
            false,
            mData
        ) { baseRsp ->
            hideLoading()
            if (baseRsp.success()) {
                setValue("onSaveDraftsSuccess")
            } else {
                showDialogToast(baseRsp.msg)
            }
        }
    }

    //  保存并发布
    fun saveAndCommit(mData: ReqAddOrderForSeniorConsignor2, state: JumpNewGoodsData.PageState) {
        /**
         * 操作方式
         * add                  新增页面保存运单
         * publish              新增页面发布运单
         *
         * update               编辑页面保存运单
         * editpublish          编辑页面发布运单
         *
         * reupdate             重新发布页面保存运单
         * republish            重新发布页面发布运单
         *
         * import               导入发布运单
         *
         * addpublish           新增页面保存并发布运单
         * editaddpublish       编辑页面保存并发布运单
         * readdpublish         重新发布页面保存并发布运单
         */
        when (state) {
            JumpNewGoodsData.PageState.新增 -> {
                mData.orderInfo.operation = "addpublish"
            }

            JumpNewGoodsData.PageState.编辑 -> {
                mData.orderInfo.operation = "editaddpublish"
            }

            JumpNewGoodsData.PageState.重新发布 -> {
                mData.orderInfo.operation = "readdpublish"
            }

            else -> {}
        }
        execute(
            false,
            mData,
            object : IResult<BaseRsp<RspAddResult>> {
                override fun onSuccess(baseRsp: BaseRsp<RspAddResult>) {
                    if (baseRsp.success()) {
                        if (mData.orderInfo.batchPublishNum.isTrue) {
                            setValue("onSaveDraftsSuccess")
                        } else {
                            getSuccessImportNum()
                        }
                    } else {
                        showDialogToast(baseRsp.msg)
                    }
                }

                override fun onFail(p0: HandleException?) {
                    if (TextUtils.equals("10005", p0?.msg)) {
                        setValue("onCommitSuccess")
                    }
                }
            }
        )
    }

    fun queryTonRuleByCargoName(req: ReqQueryTonRuleByCargoName) {
        execute(req) {
            if (it.success()) {
                setValue("queryTonRuleByCargoNameSuccess", it.data)
            } else {
                showToast(it.msg)
            }
        }
    }

    fun queryOilRewardInfoByPublish(req: Req37QueryOilRewardInfoByPublish) {
        execute(req) {
            if (it.success()) {
                setValue("onQueryOilRewardInfoByPublish", it.data)
            } else {
                showToast(it.msg)
            }
        }
    }

    fun getSuccessImportNum() {
        var observable: Disposable? = null
        observable = Observable.interval(1000, TimeUnit.MILLISECONDS)
            .subscribeOn(Schedulers.io())
            .observeOn(AndroidSchedulers.mainThread())
            .subscribe {
                execute(Req56GetSuccessImportNum(), object : IResult<BaseRsp<Rsp55GetSuccessImportNum>> {
                    override fun onSuccess(sendRequest: BaseRsp<Rsp55GetSuccessImportNum>) {
                        if (sendRequest.success()) {
                            val data = sendRequest.data
                            if (TextUtils.equals(data?.finishFlag, "0")) {
                                //发布未结束
                                setValue("onGetSuccessImportNum", data?.successImportNum)
                            } else {
                                //发布结束包括异常结束
                                observable?.dispose()
                                setValue("onGetSuccessImportNum", "-1")
                            }
                        } else {
                            //发布结束包括异常结束
                            setValue("onGetSuccessImportNum", "-1")
                            observable?.dispose()
                        }
                    }

                    override fun onFail(p0: HandleException?) {
                        //发布结束包括异常结束
                        setValue("onGetSuccessImportNum", "-1")
                        observable?.dispose()
                    }

                })
            }
    }
}