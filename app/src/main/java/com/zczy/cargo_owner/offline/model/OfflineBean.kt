package com.zczy.cargo_owner.offline.model

import com.zczy.comm.http.entity.BaseNewRequest
import com.zczy.comm.http.entity.BaseRsp
import com.zczy.comm.http.entity.PageList
import com.zczy.comm.http.entity.ResultData

class OfflineReq(
    var title: String = "",
    var queryType: Int = 1,// 1 我的发布  2 待发货 3 待收货 4 已完成 5 全部 6 草稿箱
    var nowPage: Int = 1,
    var pageSize: Int = 10
) : BaseNewRequest<BaseRsp<PageList<OfflineResp>>>("oms-app/order/tender/consignor/queryOrderTenderListForApp")

class OfflineResp : ResultData() {
    var despatchPlace: String? = null
    var specifyCarrierJsonArray: List<SpecifyCarrierJsonArrayDTO>? = null
    var orderId: String? = null
    var allCargoName: String? = null
    var pbConsignorDisplayUnitMoney: Double? = null
    var source: Int? = null
    var policyFlag: Int? = null
    var title: String? = null
    var deliverPlace: String? = null
    var displayUnitMoney: String? = null
    var freightType: Int? = null
    var republishFlag: Int? = null
    var validityPeriod: Long? = null
    var querySource: Int? = null
    var specifyFlag: String? = null
    var deliverDis: String? = null
    var buttonObj: List<ButtonObjDTO>? = null
    var selfComment: String? = null
    var deliverCity: String? = null
    var createdTime: String? = null
    var displayMoney: String? = null
    var despatchDis: String? = null
    var carrierUserId: String? = null
    var dealFlag: Int? = null
    var orderState: Int? = null
    var blockMoney: String? = null
    var orderCurrentState: String? = null
    var vehicleType: String? = null
    var orderMark: String? = null
    var pbConsignorDisplayMoney: Double? = null
    var carrierMobile: String? = null
    var expectPeriod: Long? = null
    var validityTime: String? = null
    var orderCurrentStateId: String? = null
    var expectTime: String? = null
    var vehicleNameJsonArray: List<VehicleNameJsonArrayDTO>? = null
    var weight: Double? = null
    var plateNumber: String? = null
    var orderModel: Int? = null
    var carrierCustomerName: String? = null
    var queryType: Int? = null
    var cargoCategory: Int? = null
    var vehicleLength: String? = null
    var receiptFlag: Int? = null
    var despatchPro: String? = null
    var deliverPro: String? = null
    var vehicleNames: String? = null
    var monitorFlag: Int? = null
    var despatchCity: String? = null
    var consignorState: Int? = null

    class SpecifyCarrierJsonArrayDTO

    class ButtonObjDTO {
        var btnName: String? = null
        var btnState: Boolean? = null
    }

    class VehicleNameJsonArrayDTO {
        var driverUserName: String? = null
        var plateNumber: String? = null
    }
}
