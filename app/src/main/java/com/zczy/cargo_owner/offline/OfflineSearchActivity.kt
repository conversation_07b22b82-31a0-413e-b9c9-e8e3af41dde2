package com.zczy.cargo_owner.offline

import android.app.Activity
import android.content.Context
import android.content.Intent
import android.graphics.Color
import android.os.Bundle
import android.text.TextUtils
import android.view.View
import android.view.inputmethod.InputMethodManager
import com.google.gson.Gson
import com.google.gson.reflect.TypeToken
import com.sfh.lib.AppCacheManager
import com.sfh.lib.mvvm.annotation.LiveDataMatch
import com.zczy.cargo_owner.R
import com.zczy.cargo_owner.deliver.addorder.JumpNewGoodsData
import com.zczy.cargo_owner.deliver.bean.DeliverMyOrderListData
import com.zczy.cargo_owner.home.dialog.X5VideoWebNoToolBarActivity
import com.zczy.cargo_owner.libcomm.config.HttpConfig
import com.zczy.cargo_owner.offline.adapter.OfflineAdapter
import com.zczy.cargo_owner.offline.callback.OnBtnClickListener
import com.zczy.cargo_owner.offline.model.*
import com.zczy.cargo_owner.offline.publish.DeliverDraftsEditActivityV2
import com.zczy.cargo_owner.offline.publish.DeliverDraftsNormalFragmentV2
import com.zczy.cargo_owner.order.transport.WaybillTrackingActivityV1
import com.zczy.comm.http.entity.PageList
import com.zczy.comm.http.entity.ResultData
import com.zczy.comm.ui.BaseActivity
import com.zczy.comm.utils.dp2px
import com.zczy.comm.utils.json.toJson
import com.zczy.comm.widget.pulltorefresh.CommEmptyView
import kotlinx.android.synthetic.main.activity_offline_search.*


class OfflineSearchActivity : BaseActivity<OfflineModel>() {

    private var mTitle: String = ""
    var historyList = ArrayList<String>()

    companion object {
        @JvmStatic
        fun jumpUi(context: Context) {
            val intent = Intent(context, OfflineSearchActivity::class.java)
            context.startActivity(intent)
        }
    }

    override fun getLayout(): Int {
        return R.layout.activity_offline_search
    }

    override fun bindView(bundle: Bundle?) {
        iv_back.setOnClickListener {
            finish()
        }

        iv_history_delete.setOnClickListener {
            historyList.clear()
            refreshHistory()
        }

        tv_search.setOnClickListener {
            getListData(1, et_search_title.text.toString())
        }

        swipeRefreshMoreLayout.setAdapter(OfflineAdapter(OnBtnClickListener { btnName, orderId, truckNum, specifyFlag ->
            when (btnName) {
                "取消" -> {
                    getViewModel(OfflineModel::class.java).cancleOrder(CancleOrderReq(orderId))
                }
                "删除" -> {
                    getViewModel(OfflineModel::class.java).deleteOrder(DeleteOrderReq(orderId))
                }
                "再来一单" -> {
                    DeliverDraftsEditActivityV2.start(
                        this@OfflineSearchActivity,
                        JumpNewGoodsData(
                            orderId = orderId,
                            specifyFlag = specifyFlag.toString(),
                            pageState = JumpNewGoodsData.PageState.重新发布,
                            orderType = "0"
                        ),
                        "编辑货源",
                        DeliverDraftsNormalFragmentV2.REQUEST_EDIT
                    )
                }
                "选择承运方" -> {
                    val data = DeliverMyOrderListData()
                    data.orderId = orderId
                    OfflineDeliverChooseActivity.start(this, data, 10000)
                }
                "在途跟踪", "轨迹回放" -> {
                    WaybillTrackingActivityV1.startUI(this, orderId, truckNum, "1")
                }
                "同意取消运单" -> {
                    getViewModel(OfflineModel::class.java).agreeCancelOrderTender(AgreeCancelOrderTenderReq(orderId))
                }
            }
        }), true)
        swipeRefreshMoreLayout.addItemDecorationSize(dp2px(10f))
        swipeRefreshMoreLayout.setEmptyView(CommEmptyView.creatorDef(this))

        swipeRefreshMoreLayout.setOnLoadListener2 { nowPage ->
            getListData(nowPage, et_search_title.text.toString())
        }

        swipeRefreshMoreLayout.addOnItemChildClickListener { adapter, view, position ->

        }

        swipeRefreshMoreLayout.addOnItemListener { adapter, view, position ->
            val offlineResp = adapter.data[position] as OfflineResp
            OfflineWaybillDetailActivity.start(this@OfflineSearchActivity, offlineResp.orderId, 10000)
        }
    }

    private fun getListData(nowPage: Int, title: String) {
        mTitle = title
        val req = OfflineReq()
        req.nowPage = nowPage
        req.queryType = 5
        req.title = title
        getViewModel(OfflineModel::class.java).queryOfflineListData(req)

        val imm = getSystemService(Context.INPUT_METHOD_SERVICE) as InputMethodManager
        imm.hideSoftInputFromWindow(et_search_title.windowToken, 0)

    }

    @LiveDataMatch
    open fun onQueryOfflineListDataSuccess(data: PageList<OfflineResp>) {
        swipeRefreshMoreLayout.visibility = View.VISIBLE
        swipeRefreshMoreLayout.onRefreshCompale(data)

        if (!historyList.contains(mTitle)) {
            historyList.add(0, mTitle)
            refreshHistory()
        } else {
            historyList.remove(mTitle)
            historyList.add(0, mTitle)
            refreshHistory()
        }
    }

    override fun onActivityResult(requestCode: Int, resultCode: Int, data: Intent?) {
        super.onActivityResult(requestCode, resultCode, data)
        if (requestCode == 10000 && resultCode == Activity.RESULT_OK) {
            this.swipeRefreshMoreLayout.onAutoRefresh()
        }
    }

    @LiveDataMatch
    open fun cancleOrderSuccess(data: ResultData) {
        swipeRefreshMoreLayout.onAutoRefresh()
    }

    @LiveDataMatch
    open fun deleteOrderSuccess(data: ResultData) {
        swipeRefreshMoreLayout.onAutoRefresh()
    }

    override fun initData() {
        val listType = object : TypeToken<ArrayList<String>>() {}.type
        val historyStr = AppCacheManager.getCache("search_history", String::class.java)
        historyList = if (TextUtils.isEmpty(historyStr)) {
            Gson().fromJson(
                "[]",
                listType
            )
        } else {
            Gson().fromJson(
                historyStr,
                listType
            )
        }

        refreshHistory()
    }

    private fun refreshHistory() {
        AppCacheManager.putCache("search_history", historyList.toJson())
        flow_view.setTextSize(12)
        flow_view.setTextColor(Color.parseColor("#666666"))
        flow_view.setBackgroundResource(R.drawable.file_eff0f3_input_circle)
        flow_view.setHorizontalSpacing(10)
        flow_view.setVerticalSpacing(10)
        flow_view.setTextPaddingH(12)
        flow_view.setTextPaddingV(8)
        flow_view.setViews(
            historyList
        ) { content ->
            getListData(1, content)
        }
    }
}