package com.zczy.cargo_owner.tickling.model

import com.sfh.lib.exception.HandleException
import com.sfh.lib.mvvm.service.BaseViewModel
import com.sfh.lib.rx.IResult
import com.zczy.cargo_owner.tickling.req.Req1QueryProblemFeedbackPage
import com.zczy.cargo_owner.tickling.req.RspProblemListData
import com.zczy.comm.http.entity.BaseRsp
import com.zczy.comm.http.entity.PageList

/**
 * PS:
 * Created by sdx on 2018/10/23.
 */
class UserTicklingModel : BaseViewModel() {
    fun getNetInfo(nowPage: Int) {
        this.execute(Req1QueryProblemFeedbackPage(nowPage = nowPage),
                object : IResult<BaseRsp<PageList<RspProblemListData>>> {
                    override fun onSuccess(t: BaseRsp<PageList<RspProblemListData>>) {
                        if (t.success()) {
                            setValue("onGetNetInfoSuccess", t.data)
                        } else {
                            showDialogToast(t.msg)
                            setValue("onGetNetInfoSuccess", null)
                        }
                    }

                    override fun onFail(e: HandleException) {
                        showDialogToast(e.msg)
                        setValue("onGetNetInfoSuccess", null)
                    }
                })
    }
}
