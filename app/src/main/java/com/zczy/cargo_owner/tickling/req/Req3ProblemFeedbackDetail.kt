package com.zczy.cargo_owner.tickling.req

import com.zczy.comm.http.entity.BaseNewRequest
import com.zczy.comm.http.entity.BaseRsp
import com.zczy.comm.http.entity.ResultData

/**
 * PS:3.查询问题反馈详情
 * Created by sdx on 2019/2/25.
 */
data class Req3ProblemFeedbackDetail(
    var feedbackId: String = "" // 问题反馈id
) : BaseNewRequest<BaseRsp<RspProblemFeedbackDetail>>("wo-app/problemFeedback/problemFeedbackDetail")

data class RspProblemFeedbackDetail(
    var feedbackId: String = "", // 唯一id
    var problemId: String = "", // 问题编号
    var memberNM: String = "", // 会员名称
    var createdTimeStr: String = "", // 创建时间
    var contacter: String = "", // 联系人
    var contacterPhone: String = "", // 联系人电话
    var orderId: String = "", // 运单号
    var problemDescription: String = "", // 问题描述
    var state: String = "", // 状态。1：已提交，2：处理中，3：已完成
    var registerMobile: String = "", // 注册手机号
    var userNm: String = "", // 用户名
    var pfUrls: String = "", // 问题反馈图片url，逗号分隔
    var problemType: String = "", // 问题类型
    var problemTypeName: String = "", // 问题类型名称
) : ResultData()


fun RspProblemFeedbackDetail.formatState(): String {
    return when (state) {
        "1" -> {
            "已提交"
        }

        "2" -> {
            "处理中"
        }

        "3" -> {
            "已完成"
        }

        else -> {
            ""
        }
    }
}
