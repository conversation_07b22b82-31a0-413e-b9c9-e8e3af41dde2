package com.zczy.cargo_owner.tickling;

import android.app.Activity;
import android.content.Intent;
import android.graphics.Color;
import android.os.Bundle;
import android.text.InputFilter;
import android.text.InputType;
import android.text.SpannableString;
import android.text.SpannableStringBuilder;
import android.text.Spanned;
import android.text.TextUtils;
import android.text.method.LinkMovementMethod;
import android.text.style.ClickableSpan;
import android.text.style.ForegroundColorSpan;
import android.view.View;
import android.widget.Button;
import android.widget.EditText;
import android.widget.TextView;

import androidx.annotation.NonNull;
import androidx.annotation.Nullable;
import androidx.fragment.app.Fragment;

import com.jakewharton.rxbinding2.widget.RxTextView;
import com.sfh.lib.event.RxBusEventManager;
import com.sfh.lib.mvvm.annotation.LiveDataMatch;
import com.sfh.lib.ui.dialog.DialogBuilder;
import com.zczy.cargo_owner.R;
import com.zczy.cargo_owner.home.onlinecall.OnLineCallActivity;
import com.zczy.cargo_owner.libcomm.config.HttpConfig;
import com.zczy.cargo_owner.libcomm.widget.CheckSelfPermissionDialog;
import com.zczy.cargo_owner.order.confirm.bean.RxAgreeAdjust;
import com.zczy.cargo_owner.tickling.model.UserTicklingAddViewModel;
import com.zczy.cargo_owner.tickling.req.Req2AddProblemFeedback;
import com.zczy.cargo_owner.tickling.req.ReqQueryProblemFeedbackProblemTypeEnum;
import com.zczy.cargo_owner.tickling.req.RspQueryProblemFeedbackProblemTypeEnum;
import com.zczy.cargo_owner.tickling.req.RspQueryProblemFeedbackProblemTypeEnumList;
import com.zczy.cargo_owner.tickling.req.RspQueryPromblemFeedbackOrderList;
import com.zczy.comm.CommServer;
import com.zczy.comm.data.IUserServer;
import com.zczy.comm.data.entity.EImage;
import com.zczy.comm.data.entity.ELogin;
import com.zczy.comm.data.entity.EProcessFile;
import com.zczy.comm.permission.PermissionCallBack;
import com.zczy.comm.permission.PermissionUtil;
import com.zczy.comm.ui.BaseActivity;
import com.zczy.comm.utils.ex.CollectionUtil;
import com.zczy.comm.utils.imageselector.ImagePreviewActivity;
import com.zczy.comm.utils.imageselector.ImageSelectProgressView;
import com.zczy.comm.utils.imageselector.ImageSelector;
import com.zczy.comm.widget.dialog.ChooseDialogV1;
import com.zczy.comm.widget.inputv2.InputViewClick;
import com.zczy.comm.widget.inputv2.InputViewEdit;

import java.io.File;
import java.util.ArrayList;
import java.util.List;

import io.reactivex.Observable;
import io.reactivex.disposables.Disposable;
import kotlin.collections.CollectionsKt;
import kotlin.jvm.functions.Function1;

/**
 * PS:个人中心 问题反馈 新增
 *
 * <AUTHOR>
 * @date 2018/10/23
 */
public class UserTicklingAddActivity extends BaseActivity<UserTicklingAddViewModel> implements View.OnClickListener {

    private static final int REQUEST_PHOTO = 0x32;
    private static final int REQUEST_ORDER = 0x33;

    private InputViewEdit inputName;
    private InputViewEdit inputPhone;
    private InputViewClick inputOrder;
    private TextView tvTitleNote;
    private EditText edDescription;
    private TextView tvSize;
    private ImageSelectProgressView imageSelectView;
    private TextView tvCall;
    private Button btnCommit;
    private InputViewClick input_question_type;
    private String label;

    private RspQueryPromblemFeedbackOrderList mOrder = null;
    private String from = null; // 3 货主回单打回 2 逾期运单列表 否则传空字符串
    private String orderId = null;
    private String name = null;
    private String phone = null;
    private String problemType = null;

    public static void start(Activity activity, String orderId, String label, String from, int requestCode) {
        Intent intent = new Intent(activity, UserTicklingAddActivity.class);
        intent.putExtra("orderId", orderId);
        intent.putExtra("label", label);
        intent.putExtra("from", from);
        activity.startActivityForResult(intent, requestCode);
    }

    public static void start(Fragment fragment, String name, String phone, String orderId, String label, String from, int requestCode) {
        Intent intent = new Intent(fragment.getContext(), UserTicklingAddActivity.class);
        intent.putExtra("name", name);
        intent.putExtra("phone", phone);
        intent.putExtra("orderId", orderId);
        intent.putExtra("label", label);
        intent.putExtra("from", from);
        fragment.startActivityForResult(intent, requestCode);
    }

    @Override
    protected int getLayout() {
        return R.layout.user_tickling_add_activity;
    }

    @Override
    protected void bindView(@Nullable Bundle bundle) {
        initView();
    }

    private void initView() {
        inputName = findViewById(R.id.input_name);
        inputPhone = findViewById(R.id.input_phone);
        inputOrder = findViewById(R.id.input_order);
        imageSelectView = findViewById(R.id.image_select_view);
        tvTitleNote = findViewById(R.id.tv_title_note);
        edDescription = findViewById(R.id.ed_description);
        tvSize = findViewById(R.id.tv_more_size);
        tvCall = findViewById(R.id.tv_call);
        btnCommit = findViewById(R.id.btn_commit);
        input_question_type = findViewById(R.id.input_question_type);

        inputOrder.setListener(new InputViewClick.Listener() {
            @Override
            public void onClick(int i, @NonNull InputViewClick inputViewClick, @NonNull String s) {
                UserTicklingChooseOrderActivity.start(UserTicklingAddActivity.this, mOrder, REQUEST_ORDER);
            }
        });

        input_question_type.setListener(new InputViewClick.Listener() {
            @Override
            public void onClick(int i, @NonNull InputViewClick inputViewClick, @NonNull String s) {
                getViewModel(UserTicklingAddViewModel.class).queryProblemFeedbackProblemTypeEnum(new ReqQueryProblemFeedbackProblemTypeEnum());
            }
        });

        edDescription.setFilters(new InputFilter[]{new InputFilter.LengthFilter(200)});
        inputPhone.setInputType(InputType.TYPE_CLASS_NUMBER);
        Observable<CharSequence> oName = RxTextView.textChanges(inputName.getEditText());
        Observable<CharSequence> oPhone = RxTextView.textChanges(inputPhone.getEditText());
        Observable<CharSequence> oDescription = RxTextView.textChanges(edDescription);

        Disposable subscribe = Observable
                .combineLatest(oName, oPhone, oDescription,
                        (char1, char2, char3) -> {
                            tvSize.setText(char3.length() + "/200");
                            return TextUtils.isEmpty(char1) || TextUtils.isEmpty(char2) || TextUtils.isEmpty(char3);
                        })
                .subscribe(aBoolean -> btnCommit.setEnabled(!aBoolean));
        putDisposable(subscribe);

        SpannableStringBuilder builder = new SpannableStringBuilder("问题描述");
        SpannableString span = new SpannableString("*");
        span.setSpan(new ForegroundColorSpan(Color.RED), 0, span.length(), Spanned.SPAN_INCLUSIVE_EXCLUSIVE);
        builder.append(span);
        tvTitleNote.setText(builder);

        this.imageSelectView.setOnItemSelectListener(new ImageSelectProgressView.OnItemSelectListener() {

            @Override
            public void onSelectImageClick(int surplus) {
                CheckSelfPermissionDialog.cameraPermissionDialog(UserTicklingAddActivity.this, new PermissionCallBack() {
                    @Override
                    public void onHasPermission() {
                        PermissionUtil.openAlbum(UserTicklingAddActivity.this,
                                new PermissionCallBack() {
                                    @Override
                                    public void onHasPermission() {
                                        ImageSelector.open(UserTicklingAddActivity.this, surplus, true, REQUEST_PHOTO);
                                    }
                                });
                    }
                });

            }

            @Override
            public void onUpImageClick(String file) {
                getViewModel().upFile(file);
            }

            @Override
            public void onLookImageClick(List<EProcessFile> file, int position) {
                //查看大图
                List<EImage> list = new ArrayList<>(file.size());
                for (EProcessFile processFile : file) {
                    EImage image = new EImage();
                    image.setNetUrl(HttpConfig.getUrlImage(processFile.getImagUrl()));
                    list.add(image);
                }
                ImagePreviewActivity.start(UserTicklingAddActivity.this, list, position);
            }

            @Override
            public void onDelateClick(int position) {
                DialogBuilder dialogBuilder = new DialogBuilder();
                dialogBuilder.setMessage("确定删除当前图片吗？");
                dialogBuilder.setOkListener((DialogBuilder.DialogInterface dialogInterface, int i) -> {
                    dialogInterface.dismiss();
                    imageSelectView.deleteImage(position);
                });
                showDialog(dialogBuilder);
            }
        });
        this.imageSelectView.setShowSize(4, 4);
        this.imageSelectView.setDelete(true);

        btnCommit.setOnClickListener(this);

        SpannableStringBuilder sb = new SpannableStringBuilder("紧急问题可直接 ");
        SpannableString str = new SpannableString("联系在线客服");
        str.setSpan(new ClickableSpan() {
            @Override
            public void onClick(@NonNull View widget) {
                OnLineCallActivity.start(UserTicklingAddActivity.this, "");

            }
        }, 0, str.length(), Spanned.SPAN_EXCLUSIVE_EXCLUSIVE);
        sb.append(" ");
        sb.append(str);
        tvCall.setText(sb);
        //不设置 没有点击事件
        tvCall.setMovementMethod(LinkMovementMethod.getInstance());
    }

    @Override
    protected void initData() {
        IUserServer userServer = CommServer.getUserServer();
        if (userServer != null) {
            ELogin login = userServer.getLogin();
            if (login != null) {
                String memberName = login.getMemberName();
                String mobile = login.getMobile();
                inputName.setContent(memberName);
                inputPhone.setContent(mobile);
            }
        }
        label = getIntent().getStringExtra("label");
        from = getIntent().getStringExtra("from");
        orderId = getIntent().getStringExtra("orderId");
        name = getIntent().getStringExtra("name");
        phone = getIntent().getStringExtra("phone");
        inputName.setContent(name != null ? name : "");
        inputPhone.setContent(phone != null ? phone : "");
        inputOrder.setContent(orderId != null ? orderId : "");
        if ("2".equals(from) || "3".equals(from)) {
            inputOrder.setClickable(false);
            inputOrder.setEnabled(false);
            input_question_type.setVisibility(View.VISIBLE);
        }
    }

    @Override
    public void onActivityResult(int requestCode, int resultCode, Intent data) {
        super.onActivityResult(requestCode, resultCode, data);
        if (resultCode == Activity.RESULT_OK) {
            switch (requestCode) {
                case REQUEST_PHOTO:
                    List<String> file = ImageSelector.obtainPathResult(data);
                    if (file != null) {
                        imageSelectView.onUpLoadStart(file);
                        getViewModel().upFile(file);
                    }
                    break;
                case REQUEST_ORDER:
                    mOrder = UserTicklingChooseOrderActivity.obtainData(data);
                    if (mOrder != null) {
                        inputOrder.setContent(mOrder.getOrderId());
                    }
                    break;
                default:
                    break;
            }
        }
    }

    @Override
    public void onClick(View v) {
        switch (v.getId()) {
            case R.id.btn_commit:
                commit();
                break;
            default:
                break;
        }
    }

    private void commit() {
        String name = inputName.getContent();
        String phone = inputPhone.getContent();
        String order = inputOrder.getContent();
        List<String> imgList = CollectionsKt.map(imageSelectView.getDataList(), new Function1<EProcessFile, String>() {
            @Override
            public String invoke(EProcessFile eProcessFile) {
                return eProcessFile.getImagUrl();
            }
        });
        String description = edDescription.getText().toString().trim();
        if (input_question_type.getVisibility() == View.VISIBLE && TextUtils.isEmpty(input_question_type.getContent())) {
            showToast("请选择问题分类");
            return;
        }
        Req2AddProblemFeedback req = new Req2AddProblemFeedback();
        // 问题描述
        req.setProblemDescription(description);
        // 运单号
        req.setOrderId(order);
        // 联系人
        req.setContacter(name);
        // 联系人电话
        req.setContacterPhone(phone);
        // 问题反馈图片url，逗号分隔
        req.setPfUrls(CollectionUtil.toCommaString(imgList));
        // 回单打回标签
        req.setTaggedValue(label);
        req.setProblemType(problemType);
        req.setSource(from);
        getViewModel().doCommit(req);
    }

    @LiveDataMatch(tag = "上传文件成功")
    public void onFileSuccess(File tag, String url) {
        imageSelectView.onUpLoadFileSuccess(tag.getAbsolutePath(), url);
    }

    @LiveDataMatch(tag = "上传文件失败")
    public void onFileFailure(File tag, String error) {
        imageSelectView.onUpLoadFileError(tag.getAbsolutePath());
    }

    @LiveDataMatch
    public void doCommitSuccess() {
        setResult(RESULT_OK);
        RxAgreeAdjust rxAgreeAdjust = new RxAgreeAdjust();
        rxAgreeAdjust.setSuccess(true);
        RxBusEventManager.postEvent(rxAgreeAdjust);
        finish();
    }


    @LiveDataMatch
    public void queryProblemFeedbackProblemTypeEnumSuccess(RspQueryProblemFeedbackProblemTypeEnum data) {
        if (data == null || data.getList() == null) {
            return;
        }
        List<RspQueryProblemFeedbackProblemTypeEnumList> list = data.getList();
        ChooseDialogV1.instance(list).setTitle("问题分类").setFlatMap(RspQueryProblemFeedbackProblemTypeEnumList::getValue).setClick((data1, integer) -> {
            input_question_type.setContent(data1.getValue());
            problemType = data1.getKey();
            return null;
        }).show(this);
    }
}
