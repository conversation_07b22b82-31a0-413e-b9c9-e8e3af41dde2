package com.zczy.cargo_owner.tickling.req

import com.zczy.comm.http.entity.BaseNewRequest
import com.zczy.comm.http.entity.BaseRsp
import com.zczy.comm.http.entity.PageList
import com.zczy.comm.http.entity.ResultData

/**
 * PS:.查询运单列表接口
 * http://wiki.zczy56.com/pages/viewpage.action?pageId=12356020
 * Created by sdx on 2019/2/25.
 */
data class ReqQueryPromblemFeedbackOrderList(
        var currentPage: Int = 1, //
        var pageSize: Int = 10, //
        var orderId: String = "" // 运单号
) : BaseNewRequest<BaseRsp<PageList<RspQueryPromblemFeedbackOrderList>>>("wo-app/problemFeedback/queryPromblemFeedbackOrderList")

data class RspQueryPromblemFeedbackOrderList(
        var orderId: String = "", // 运单号
        var cargoCategory: String = "", // 货物类别：1：重货,2 泡货
        var orderModel: String = "", // 订单类型：0 抢单,1 议价
        var freightType: String = "", // 费用类型：0 包车价,1 单价
        var invoiceFlag: String = "", // 是否需要发票：0 否,1 是
        var consignorCompany: String = "", // 货主公司名称
        var despatchCity: String = "", // 启运地市
        var despatchPlace: String = "", // 启运地详细地址
        var deliverCity: String = "", // 目的地市
        var deliverPlace: String = "", // 目的地详细地址
        var goodsDescription: String = "", // 货物描述
        var money: String = "", // 展示金额

        var despatchDis: String = "", // 起运地区
        var deliverDis: String = "", // 目的地区
        var despatchPro: String = "", //
        var deliverPro: String = "" //

) : ResultData()


fun RspQueryPromblemFeedbackOrderList.formatStartAddress(): String {
    val sb = StringBuilder()
    if (despatchCity != "市辖区") {
        sb.append(despatchCity)
    } else {
        sb.append(despatchPro)
    }
    if (sb.isNotEmpty()) {
        sb.append(" ")
    }
    sb.append(despatchDis)
    return sb.toString()
}

fun RspQueryPromblemFeedbackOrderList.formatEndAddress(): String {
    val sb = StringBuilder()
    if (deliverCity != "市辖区") {
        sb.append(deliverCity)
    } else {
        sb.append(deliverPro)
    }
    if (sb.isNotEmpty()) {
        sb.append(" ")
    }
    sb.append(deliverDis)
    return sb.toString()
}

fun RspQueryPromblemFeedbackOrderList.formatFreightType(): String {
    return when (freightType) {
        "0" -> "包车价"
        "1" -> "单价"
        else -> ""
    }
}