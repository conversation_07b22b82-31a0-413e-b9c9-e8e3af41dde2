package com.zczy.cargo_owner.tickling.adapter

import android.text.TextUtils
import com.chad.library.adapter.base.BaseQuickAdapter
import com.chad.library.adapter.base.BaseViewHolder
import com.zczy.cargo_owner.R
import com.zczy.cargo_owner.tickling.req.RspProblemListData
import com.zczy.cargo_owner.tickling.req.formatState

/**
 * PS:
 * Created by sdx on 2018/10/23.
 */
class UserTicklingAdapter
    : BaseQuickAdapter<RspProblemListData, BaseViewHolder>(R.layout.user_tickling_recycle_item) {

    override fun convert(helper: BaseViewHolder, item: RspProblemListData) {
        helper
            // 问题编号
            .setText(R.id.tv_code, "问题编号：${item.problemId}")
            // 状态
            .setText(R.id.tv_state, item.formatState())
            // 名字
            .setText(R.id.tv_name, "姓名：${item.contacter}")
            .setGone(R.id.tv_name, !TextUtils.isEmpty(item.contacter))
            // 手机号
            .setText(R.id.tv_phone, "手机号：${item.contacterPhone}")
    }
}
