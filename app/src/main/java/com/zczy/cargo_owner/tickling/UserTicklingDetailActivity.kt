package com.zczy.cargo_owner.tickling

import android.app.Activity
import android.content.Intent
import android.os.Bundle
import androidx.constraintlayout.widget.Group
import androidx.recyclerview.widget.GridLayoutManager
import androidx.recyclerview.widget.RecyclerView
import android.view.View
import android.widget.TextView
import com.chad.library.adapter.base.BaseQuickAdapter
import com.chad.library.adapter.base.listener.OnItemClickListener
import com.sfh.lib.mvvm.annotation.LiveDataMatch
import com.zczy.cargo_owner.R
import com.zczy.cargo_owner.libcomm.config.HttpConfig
import com.zczy.cargo_owner.tickling.adapter.UserTicklingDetailImgAdapter
import com.zczy.cargo_owner.tickling.model.UserTicklingDetailModel
import com.zczy.cargo_owner.tickling.req.RspProblemFeedbackDetail
import com.zczy.cargo_owner.tickling.req.formatState
import com.zczy.comm.data.entity.EImage
import com.zczy.comm.ui.BaseActivity
import com.zczy.comm.utils.imageselector.ImagePreviewActivity
import com.zczy.comm.utils.setVisible
import kotlinx.android.synthetic.main.user_tickling_detail_activity.tv_question_type
import kotlinx.android.synthetic.main.user_tickling_detail_activity.tv_question_type_title

/**
 * PS:个人中心 问题反馈 详情
 * Created by sdx on 2018/10/23.
 */
open class UserTicklingDetailActivity : BaseActivity<UserTicklingDetailModel>() {


    private val tvCode: TextView by lazy { findViewById<TextView>(R.id.tv_code) }
    private val tvState: TextView by lazy { findViewById<TextView>(R.id.tv_state) }
    private val tvName: TextView by lazy { findViewById<TextView>(R.id.tv_name) }
    private val tvPhone: TextView by lazy { findViewById<TextView>(R.id.tv_phone) }
    private val tvOrder: TextView by lazy { findViewById<TextView>(R.id.tv_order) }
    private val imgRecycler: androidx.recyclerview.widget.RecyclerView by lazy { findViewById<androidx.recyclerview.widget.RecyclerView>(R.id.recyclerView_img) }
    private val tvDescribe: TextView by lazy { findViewById<TextView>(R.id.tv_describe) }
    private val imgGroup: Group by lazy { findViewById<Group>(R.id.group_img) }

    private val code: String by lazy { intent.getStringExtra(EXTRA_CODE) ?: "" }
    private val imgAdapter: UserTicklingDetailImgAdapter = UserTicklingDetailImgAdapter()

    companion object {

        private const val EXTRA_CODE = "extra_code"

        @JvmStatic
        fun start(activity: Activity, feedbackId: String) {
            val intent = Intent(activity, UserTicklingDetailActivity::class.java)
            intent.putExtra(EXTRA_CODE, feedbackId)
            activity.startActivity(intent)
        }
    }


    override fun getLayout(): Int = R.layout.user_tickling_detail_activity

    override fun bindView(bundle: Bundle?) {
        initList()
    }

    override fun initData() {
        viewModel?.getNetInfo(code)
    }

    private fun initList() {
        imgRecycler.layoutManager =
            androidx.recyclerview.widget.GridLayoutManager(this, 4)
        imgRecycler.setHasFixedSize(true)
        //设置点击事件
        imgRecycler.addOnItemTouchListener(ticklingTouchListener)
        // empty view
        imgRecycler.adapter = imgAdapter
    }

    private val ticklingTouchListener = object : OnItemClickListener() {
        override fun onSimpleItemClick(adapter: BaseQuickAdapter<*, *>, view: View, position: Int) {
            val item = adapter.getItem(position)
            if (item is String) {
                val map = adapter.data.map {
                    val u = it as? String ?: ""
                    EImage(netUrl = HttpConfig.getUrlImage(u))
                }
                ImagePreviewActivity.start(this@UserTicklingDetailActivity, map, position)
            }
        }
    }

    @LiveDataMatch
    open fun onGetNetInfoSuccess(data: RspProblemFeedbackDetail) {
        tvCode.text = "问题编号：${data.problemId}"
        // 状态
        tvState.text = data.formatState()
        // 姓名
        tvName.text = data.contacter
        // 手机号
        tvPhone.text = data.contacterPhone
        // 关联订单
        tvOrder.text = data.orderId
        // 图片
        val list = data.pfUrls.split(",").filter { it.isNotEmpty() }
        if (list.isEmpty()) {
            imgGroup.visibility = View.GONE
        } else {
            imgGroup.visibility = View.VISIBLE
            imgAdapter.setNewData(list)
        }
        // 问题描述
        tvDescribe.text = data.problemDescription
        // 问题分类
        tv_question_type.text = data.problemTypeName
        tv_question_type_title.setVisible(data.problemTypeName.isNotEmpty())
        tv_question_type.setVisible(data.problemTypeName.isNotEmpty())
    }


}
