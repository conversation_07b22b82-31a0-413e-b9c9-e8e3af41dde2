package com.zczy.cargo_owner.tickling.model

import com.sfh.lib.mvvm.service.BaseViewModel
import com.sfh.lib.rx.IResultSuccess
import com.zczy.cargo_owner.tickling.req.Req3ProblemFeedbackDetail
import com.zczy.cargo_owner.tickling.req.RspProblemFeedbackDetail
import com.zczy.comm.http.entity.BaseRsp

/**
 * PS:
 * Created by sdx on 2018/10/23.
 */
class UserTicklingDetailModel : BaseViewModel() {
    fun getNetInfo(feedbackId: String) {
        this.execute(true,
                Req3ProblemFeedbackDetail(feedbackId = feedbackId),
                IResultSuccess<BaseRsp<RspProblemFeedbackDetail>> { t ->
                    if (t.success()) {
                        setValue("onGetNetInfoSuccess", t.data)
                    } else {
                        showDialogToast(t.msg)
                    }
                })
    }
}
