package com.zczy.cargo_owner.tickling

import android.app.Activity
import android.content.Context
import android.content.Intent
import android.os.Bundle
import android.text.TextUtils
import android.view.KeyEvent
import android.view.View
import android.view.inputmethod.EditorInfo
import android.view.inputmethod.InputMethodManager
import android.widget.EditText
import android.widget.TextView
import com.chad.library.adapter.base.BaseQuickAdapter
import com.chad.library.adapter.base.listener.OnItemClickListener
import com.jakewharton.rxbinding2.widget.RxTextView
import com.sfh.lib.mvvm.annotation.LiveDataMatch
import com.zczy.cargo_owner.R
import com.zczy.cargo_owner.tickling.adapter.UserTicklingChooseOrderAdapter
import com.zczy.cargo_owner.tickling.model.UserTicklingChooseOrderModel
import com.zczy.cargo_owner.tickling.req.RspQueryPromblemFeedbackOrderList
import com.zczy.comm.http.entity.PageList
import com.zczy.comm.ui.BaseActivity
import com.zczy.comm.utils.dp2px
import com.zczy.comm.utils.json.toJson
import com.zczy.comm.utils.json.toJsonObject
import com.zczy.comm.widget.pulltorefresh.CommEmptyView
import com.zczy.comm.widget.pulltorefresh.SwipeRefreshMoreLayout


/**
 * PS:个人中心 问题反馈 选择订单
 * Created by sdx on 2018/10/23.
 */
open class UserTicklingChooseOrderActivity
    : BaseActivity<UserTicklingChooseOrderModel>() {

    private val swipeToLoadLayout by lazy { findViewById<SwipeRefreshMoreLayout>(R.id.swipe_refresh_more_layout) }

    private val rlViewSearch by lazy { findViewById<View>(R.id.rl_search_view) }
    private val tvBtnSearch by lazy { findViewById<TextView>(R.id.tv_search) }
    private val layoutSearch by lazy { findViewById<View>(R.id.layout_search) }
    private val btnViewSearch by lazy { findViewById<View>(R.id.tv_search) }

    private val editSearch by lazy { findViewById<EditText>(R.id.edit_search) }
    private val btnClear by lazy { findViewById<View>(R.id.btn_clear) }

    private val btnCancel by lazy { findViewById<View>(R.id.btn_cancel) }

    private val eData by lazy { obtainData(intent) }

    companion object {

        private const val EXTRA_DATA_JSON = "extra_data_json"

        @JvmStatic
        fun start(
            activity: Activity,
            selectData: RspQueryPromblemFeedbackOrderList?,
            requestCode: Int
        ) {
            val intent = Intent(activity, UserTicklingChooseOrderActivity::class.java)
            intent.putExtra(EXTRA_DATA_JSON, selectData.toJson())
            activity.startActivityForResult(intent, requestCode)
        }

        @JvmStatic
        fun obtainData(intent: Intent?): RspQueryPromblemFeedbackOrderList {
            return intent?.getStringExtra(EXTRA_DATA_JSON)
                ?.toJsonObject(RspQueryPromblemFeedbackOrderList::class.java)
                ?: RspQueryPromblemFeedbackOrderList()
        }
    }

    override fun getLayout(): Int {
        return R.layout.user_tickling_choose_order_activity
    }

    override fun bindView(bundle: Bundle?) {
        initList()
        initEdit()

        bindClickEvent(layoutSearch)
        bindClickEvent(btnViewSearch)
        bindClickEvent(btnClear)
        bindClickEvent(btnCancel)
    }

    override fun initData() {
        swipeToLoadLayout.onAutoRefresh()
    }

    override fun onSingleClick(v: View) {
        super.onSingleClick(v)
        when (v.id) {
            R.id.layout_search -> {
                tvBtnSearch.text = ""
                showSearchView(false)
            }
            R.id.tv_search -> {
                showSearchView(true)
            }
            R.id.btn_clear -> {
                editSearch.setText("")
            }
            R.id.btn_cancel -> {
                tvBtnSearch.text = ""
                showSearchView(false)
            }
        }
    }

    private fun initList() {
        //设置点击事件
        val ticklingAdapter = UserTicklingChooseOrderAdapter()
        ticklingAdapter.selectData = eData
        // empty view
        val view = CommEmptyView.creatorDef(this)
        swipeToLoadLayout.setEmptyView(view)
        swipeToLoadLayout.addItemDecorationSize(dp2px(7f))
        swipeToLoadLayout.setAdapter(ticklingAdapter, true)
        swipeToLoadLayout.setOnLoadListener2 { nowPage ->
            //            override fun onRefreshUI(nowPage: Int) {
//                viewModel?.getNetInfo(nowPage)
//            }
//
//            override fun onLoadMoreUI(nowPage: Int) {
//                viewModel?.getNetInfo(nowPage)
//            }

            /***刷新 */
            viewModel?.getNetInfo(nowPage)

        }
        swipeToLoadLayout.addOnItemListener(ticklingTouchListener)
    }

    private fun initEdit() {
        RxTextView.textChanges(editSearch)
            .map(CharSequence::toString)
            .subscribe {
                btnClear.visibility = if (it.isEmpty()) View.GONE else View.VISIBLE
            }
            .apply {
                putDisposable(this)
            }

        editSearch.setOnEditorActionListener(object : TextView.OnEditorActionListener {
            override fun onEditorAction(v: TextView, actionId: Int, event: KeyEvent?): Boolean {
                if (actionId == EditorInfo.IME_ACTION_SEARCH) {
                    val trim = editSearch.text.toString().trim()
                    if (trim.isEmpty()) {
                        showDialogToast("请输入运单号")
                    } else {
                        showSearchView(false)
                        tvBtnSearch.text = trim
                        viewModel?.getNetInfo(1, trim)
                    }
                    return true
                }
                return false
            }
        })
    }

    private fun showSearchView(show: Boolean) {
        if (show) {
            layoutSearch.visibility = View.VISIBLE
            rlViewSearch.visibility = View.GONE
            val inputManager =
                editSearch.context.applicationContext.getSystemService(Context.INPUT_METHOD_SERVICE) as InputMethodManager
            editSearch.requestFocus()
            inputManager.showSoftInput(editSearch, 0)
        } else {
            rlViewSearch.visibility = View.VISIBLE
            layoutSearch.visibility = View.GONE
            editSearch.setText("")
            val inputManager =
                editSearch.context.applicationContext.getSystemService(Context.INPUT_METHOD_SERVICE) as InputMethodManager
            inputManager.hideSoftInputFromWindow(
                editSearch.windowToken,
                InputMethodManager.HIDE_NOT_ALWAYS
            )
            initData()
        }
    }

    private val ticklingTouchListener = object : OnItemClickListener() {
        override fun onSimpleItemClick(adapter: BaseQuickAdapter<*, *>, view: View, position: Int) {
            val item = adapter.getItem(position)
            if (item is RspQueryPromblemFeedbackOrderList) {
                intent.putExtra(EXTRA_DATA_JSON, item.toJson())
                setResult(Activity.RESULT_OK, intent)
                finish()
            }
        }
    }

    @LiveDataMatch
    open fun onGetNetInfoSuccess(page: PageList<RspQueryPromblemFeedbackOrderList>?) {
        swipeToLoadLayout.onRefreshCompale(page)
    }
}
