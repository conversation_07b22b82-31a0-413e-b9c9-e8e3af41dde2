package com.zczy.cargo_owner.tickling.model

import com.sfh.lib.mvvm.service.BaseViewModel
import com.sfh.lib.rx.IResultSuccess
import com.zczy.cargo_owner.tickling.req.Req2AddProblemFeedback
import com.zczy.cargo_owner.tickling.req.ReqQueryProblemFeedbackProblemTypeEnum
import com.zczy.comm.CommServer
import com.zczy.comm.file.IFileServer
import com.zczy.comm.http.entity.BaseRsp
import com.zczy.comm.http.entity.ResultData
import java.io.File

/**
 * PS:
 * Created by sdx on 2018/10/24.
 */
class UserTicklingAddViewModel : BaseViewModel() {
    fun doCommit(req: Req2AddProblemFeedback) {
        execute(true,
            req,
            IResultSuccess<BaseRsp<ResultData>> {
                if (it.success()) {
                    showToast("提交成功")
                    setValue("doCommitSuccess")
                } else {
                    showDialogToast(it.msg)
                }
            })
    }

    /**
     * 查询问题反馈的问题类型枚举值
     */
    fun queryProblemFeedbackProblemTypeEnum(req: ReqQueryProblemFeedbackProblemTypeEnum) {
        execute(req) {
            if (it.success()) {
                setValue("queryProblemFeedbackProblemTypeEnumSuccess", it.data)
            } else {
                showDialogToast(it.msg)
            }
        }
    }

    /***
     * 上传文件
     * @param file
     */
    fun upFile(file: List<String>) {
        for (p in file) {
            this.upFile(p)
        }
    }

    fun upFile(file: String) {
        val fileServer = CommServer.getFileServer()

        val disposable = fileServer.update(File(file), object : IFileServer.OnFileUploaderListener {
            override fun onSuccess(tag: File, url: String) {
                setValue("onFileSuccess", tag, url)
            }

            override fun onFailure(tag: File, error: String) {
                showToast(error)
                setValue("onFileFailure", tag, error)
            }
        })
        this.putDisposable(disposable)
    }
}
