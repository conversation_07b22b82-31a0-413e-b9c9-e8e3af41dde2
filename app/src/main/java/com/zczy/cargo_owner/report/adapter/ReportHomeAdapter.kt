package com.zczy.cargo_owner.report.adapter

import com.chad.library.adapter.base.BaseQuickAdapter
import com.chad.library.adapter.base.BaseViewHolder
import com.zczy.cargo_owner.R
import com.zczy.cargo_owner.report.fragment.ReportHomeFragment.Companion.TAB_TYPE_1
import com.zczy.cargo_owner.report.fragment.ReportHomeFragment.Companion.TAB_TYPE_2
import com.zczy.cargo_owner.report.fragment.ReportHomeFragment.Companion.TAB_TYPE_3
import com.zczy.cargo_owner.report.fragment.ReportHomeFragment.Companion.TAB_TYPE_4
import com.zczy.cargo_owner.report.req.RpsReportHome
import com.zczy.cargo_owner.report.req.showMoney
import com.zczy.cargo_owner.report.req.showOrderCount
import com.zczy.cargo_owner.report.req.showWeightCount

/**
 * 功能描述: 统计报表
 * <AUTHOR>
 * @date 2022/10/19-14:06
 */

class ReportHomeAdapter(val tabType: String? = TAB_TYPE_1) : BaseQuickAdapter<RpsReportHome, BaseViewHolder>(R.layout.report_home_item) {

    override fun convert(helper: BaseViewHolder?, item: RpsReportHome?) {
        helper?.let {
            item?.apply {
                it.setText(R.id.tv_start, orderAddressStart)
                    .setText(R.id.tv_end, orderAddressEnd)
                    .setText(R.id.tvOrderCount, showOrderCount())
                    .setText(
                        R.id.tvPrice, when (tabType) {
                            TAB_TYPE_2 -> {
                                "在途运费：" + showMoney()
                            }
                            TAB_TYPE_3 -> {
                                "冻结运费：" + showMoney()
                            }
                            TAB_TYPE_4 -> {
                                "货主结算金额：" + showMoney()
                            }
                            else -> {
                                "报价：" + showMoney()
                            }
                        }
                    )
                    .setText(
                        R.id.tvWeight, when (tabType) {
                            TAB_TYPE_2 -> {
                                "确认发货数量：" + showWeightCount()
                            }
                            TAB_TYPE_3 -> {
                                "确认收货数量：" + showWeightCount()
                            }
                            TAB_TYPE_4 -> {
                                "回单确认数量：" + showWeightCount()
                            }
                            else -> {
                                "货物总量：" + showWeightCount()
                            }
                        }
                    )
                    .setGone(R.id.tv_settlement_price, tabType == TAB_TYPE_4)
                    .setText(R.id.tv_settlement_price, "预计最终结算价格（含税）：" + deductSettleMoneyCount + "元")
            }
        }
    }
}