package com.zczy.cargo_owner.wight

import android.os.Bundle
import android.view.View
import android.widget.TextView
import com.zczy.cargo_owner.R
import com.zczy.comm.ui.BaseDialog

/** 功能描述:
 *
 * <AUTHOR> 贾发展
 * @company 中储智运科技股份有限公司
 * @copyright （版权）中储南京智慧物流科技有限公司所有
 * @date 2019/7/4
 */
class WeiXinLoginDialog : BaseDialog() {

    companion object {
        @JvmStatic
        fun instance(): WeiXinLoginDialog {
            val trainDialog = WeiXinLoginDialog()
            return trainDialog
        }
    }

    override fun bindView(view: View, bundle: Bundle?) {

        val tv_weixin_cn = view.findViewById<TextView>(R.id.tv_weixin_cn)
        val tv_weixin_en = view.findViewById<TextView>(R.id.tv_weixin_en)
        val tvLeftClick = view.findViewById<TextView>(R.id.tvLeftClick)
        tv_weixin_cn.setOnClickListener {
            listener?.onWeiXinCn()
            dismiss()
        }
        tv_weixin_en.setOnClickListener {
            listener?.onWeiXinEn()
            dismiss()
        }
        tvLeftClick.setOnClickListener { dismiss() }
    }

    override fun getDialogTag(): String {

        return "WeiXinLoginDialog"
    }

    override fun getDialogLayout(): Int {
        return R.layout.weixin_login_dialog
    }

    var listener: WeiXinLoginListener? = null

    public interface WeiXinLoginListener {
        fun onWeiXinCn()
        fun onWeiXinEn()
    }

    public fun setOnWeiXinLoginClickListener(winXinLoginListener: WeiXinLoginListener): WeiXinLoginDialog {
        this.listener = winXinLoginListener
        return this
    }


    override fun getDialogType(): DialogType {
        return DialogType.bottom
    }
}