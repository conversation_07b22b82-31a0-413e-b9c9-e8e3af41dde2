package com.zczy.cargo_owner.wight;

import android.content.Context;
import android.graphics.Color;
import androidx.annotation.Nullable;
import android.text.Spannable;
import android.text.SpannableString;
import android.text.SpannableStringBuilder;
import android.text.TextPaint;
import android.text.method.LinkMovementMethod;
import android.text.style.ClickableSpan;
import android.util.AttributeSet;
import android.util.SparseArray;
import android.view.View;
import android.widget.CheckBox;
import android.widget.LinearLayout;
import android.widget.TextView;

import com.zczy.cargo_owner.R;
import com.zczy.comm.x5.X5WebActivity;

/***
 * 协议View 控件
 */
public class AgreementView extends LinearLayout {

    /***
     * 协议
     */
    public static class AgreementTxt {
        public AgreementTxt(String title, String url) {
            this.title = title;
            this.url = url;
        }

        public String title;
        public String url;
        public TextClick click;
        public boolean user = true;
    }

    public AgreementView(Context context) {
        this(context, null);
    }

    public AgreementView(Context context, @Nullable AttributeSet attrs) {
        super(context, attrs);
        init();
    }

    CheckBox checkBox;
    TextView tvAgreement;
    SparseArray<AgreementTxt> agreementTxts;

    private void init() {

        setOrientation(HORIZONTAL);
        checkBox = new CheckBox(this.getContext());
        checkBox.setButtonDrawable(R.drawable.base_ui_selector_check_blue);
        tvAgreement = new TextView(this.getContext());
        tvAgreement.setTextSize(12);
        tvAgreement.setTextColor(Color.parseColor("#999999"));
        tvAgreement.setPadding(10, 0, 0, 0);
        this.addView(checkBox);
        this.addView(tvAgreement);

    }

    /***
     * 显示数据
     * @param agreementTxts
     * @return
     */
    public void newData(SparseArray<AgreementTxt> agreementTxts) {
        this.agreementTxts = agreementTxts;
        if (this.agreementTxts == null) {
            this.agreementTxts = new SparseArray<>(0);
        }
        this.showUI(this.agreementTxts);
    }

    public void hiderAgreement(int key) {
        AgreementTxt agreementTxt;
        if (this.agreementTxts != null && ((agreementTxt = this.agreementTxts.get(key)) != null)) {
            agreementTxt.user = false;
            this.showUI(agreementTxts);
        }
    }

    public void showAgreement(int key) {
        AgreementTxt agreementTxt;
        if (this.agreementTxts != null && ((agreementTxt = this.agreementTxts.get(key)) != null)) {
            agreementTxt.user = true;
            this.showUI(agreementTxts);
        }
    }

    public void putAgreement(int key, AgreementTxt value) {
        if (this.agreementTxts == null) {
            this.agreementTxts = new SparseArray<>();
        }
        this.agreementTxts.put(key, value);
        this.showUI(agreementTxts);
    }

    public void moveAgreement(int key) {
        if (this.agreementTxts != null && (this.agreementTxts.get(key) != null)) {
            this.agreementTxts.delete(key);
            this.showUI(agreementTxts);
        }
    }

    public SparseArray<AgreementTxt> getData() {
        return agreementTxts;
    }

    public CheckBox getCheckBox() {
        return checkBox;
    }

    public void notifyAgreementView() {
        if (this.agreementTxts == null) {
            this.agreementTxts = new SparseArray<>(0);
        }
        this.showUI(this.agreementTxts);
    }

    private void showUI(SparseArray<AgreementTxt> showData) {
        //协议
        SpannableStringBuilder builder = new SpannableStringBuilder("我已阅读并同意");

        final int size = showData.size();

        for (int i = 0; i < size; i++) {

            AgreementTxt item = showData.valueAt(i);
            String content = "《" + item.title + "》";
            SpannableString spannable = new SpannableString(content);
            if (item.click == null) {
                item.click = new TextClick(getContext(), item.title, item.url);
            }
            spannable.setSpan(item.click, 0, content.length(), Spannable.SPAN_EXCLUSIVE_EXCLUSIVE);
            builder.append(spannable);
        }
        //这个一定要记得设置，不然点击不生效。
        tvAgreement.setMovementMethod(LinkMovementMethod.getInstance());
        tvAgreement.setText(builder);
    }

    /**** 超链接文字点击事件*/
    public static class TextClick extends ClickableSpan {
        String url;
        String title;
        Context context;

        public TextClick(Context context, String title, String url) {
            this.url = url;
            this.context = context;
            this.title = title;
        }

        @Override
        public void onClick(View view) {
            X5WebActivity.startContentUI(context, title, url);
        }

        @Override
        public void updateDrawState(TextPaint ds) {
            super.updateDrawState(ds);
            ds.setColor(Color.parseColor("#5086FC"));   //设置字体颜
            ds.setUnderlineText(false); //设置没有下划线
        }
    }
}
