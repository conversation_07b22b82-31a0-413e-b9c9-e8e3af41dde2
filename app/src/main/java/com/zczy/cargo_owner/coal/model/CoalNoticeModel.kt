package com.zczy.cargo_owner.coal.model

import com.sfh.lib.mvvm.service.BaseViewModel
import com.zczy.cargo_owner.coal.req.ReqCoalNoticeDelete
import com.zczy.cargo_owner.coal.req.ReqCoalNoticeList

/**
 *  user: ssp
 *  time: 2021/5/21 14:04
 *  desc: 煤矿公告管理
 */

class CoalNoticeModel : BaseViewModel() {

    fun queryList(pageNum: Int, targetType: String) {
        execute(ReqCoalNoticeList(pageNum = pageNum, targetType = targetType)) {
            if (it.success()) {
                setValue("onQueryListSuccess", it.data)
            } else {
                setValue("onQueryListError", it.msg)
            }
        }
    }

    fun deleteItem(noticeId: String) {
        execute(ReqCoalNoticeDelete(noticeId = noticeId, targetType = "1")) {
            if (it.success()) {
                setValue("onDeleteSuccess", it.msg)
            } else {
                showDialogToast(it.msg)
            }
        }
    }
}