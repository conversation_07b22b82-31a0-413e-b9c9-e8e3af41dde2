package com.zczy.cargo_owner.coal.model

import com.sfh.lib.mvvm.service.BaseViewModel
import com.zczy.cargo_owner.coal.req.ReqCoalNoticeAddEdit
import com.zczy.cargo_owner.coal.req.ReqCoalNoticeDetail
import com.zczy.cargo_owner.coal.req.ReqCoalNoticeEdit
import com.zczy.cargo_owner.coal.req.success

/**
 *  user: ssp
 *  time: 2021/5/21 14:04
 *  desc: 煤矿公告管理
 */

class CoalNoticeAddEditModel : BaseViewModel() {

    fun add(req: ReqCoalNoticeAddEdit) {
        execute(req) {
            if (it.success()) {
                setValue("onAddSuccess", it.msg)
            } else {
                showDialogToast(it.msg)
            }
        }
    }

    fun edit() {
        execute(ReqCoalNoticeEdit()) {
            if (it.success()) {
                setValue("onEditSuccess", it.msg)
            } else {
                showDialogToast(it.msg)
            }
        }
    }

    fun queryDetail(noticeId: String) {
        execute(ReqCoalNoticeDetail(noticeId = noticeId)) {
            if (it.success()) {
                val data = it.data
                data?.let { bean ->
                    if (bean.isNullOrEmpty()) {
                        return@let
                    }
                    setValue("onQueryDetailSuccess", bean[0])
                }
            } else {
                showDialogToast(it.msg)
            }
        }
    }
}