package com.zczy.cargo_owner.coal

import android.content.Context
import android.content.Intent
import android.os.Bundle
import com.sfh.lib.event.RxBusEvent
import com.sfh.lib.mvvm.annotation.LiveDataMatch
import com.zczy.cargo_owner.R
import com.zczy.cargo_owner.coal.adapter.CoalNoticeAdapter
import com.zczy.cargo_owner.coal.model.CoalNoticeModel
import com.zczy.cargo_owner.coal.req.RspCoalNoticeList
import com.zczy.cargo_owner.coal.req.RxCoalNoticeAddEditSuccess
import com.zczy.comm.http.entity.PageList
import com.zczy.comm.ui.BaseActivity
import com.zczy.comm.utils.dp2px
import com.zczy.comm.widget.pulltorefresh.CommEmptyView
import kotlinx.android.synthetic.main.production_capacity_activity.*

/**
 *  user: ssp
 *  time: 2021/5/21 10:59
 *  desc: 煤矿公告管理
 */

class CoalNoticeActivity : BaseActivity<CoalNoticeModel>() {

    private val mAdapter: CoalNoticeAdapter = CoalNoticeAdapter()

    companion object {

        @JvmStatic
        fun jumpUi(context: Context) {
            val intent = Intent(context, CoalNoticeActivity::class.java)
            context.startActivity(intent)
        }
    }

    override fun getLayout(): Int {
        return R.layout.coal_notice_activity
    }

    override fun initData() {
    }

    override fun bindView(bundle: Bundle?) {
        swipe_refresh.apply {
            addItemDecorationSize(dp2px(7f))
            setAdapter(mAdapter, true)
            setEmptyView(CommEmptyView.creatorDef(context))
            addOnItemChildClickListener { adapter, view, position ->
                when (view?.id) {
                    R.id.tvEdit -> {
                        // 编辑
                        val item = adapter?.getItem(position) as RspCoalNoticeList
                        CoalNoticeAddEditActivity.jumpUi(this@CoalNoticeActivity, true, item.noticeId)
                    }
                    R.id.tvDelete -> {
                        // 删除
                        val item = adapter?.getItem(position) as RspCoalNoticeList
                        viewModel?.deleteItem(item.noticeId)
                    }
                }
            }
            setOnLoadListener2 { nowPage -> viewModel?.queryList(nowPage, "1") }
            onAutoRefresh()
        }
        tvAdd.setOnClickListener {
            CoalNoticeAddEditActivity.jumpUi(this@CoalNoticeActivity, false, "")
        }
    }

    @LiveDataMatch
    open fun onQueryListSuccess(data: PageList<RspCoalNoticeList>?) {
        swipe_refresh.onRefreshCompale(data)
    }

    @LiveDataMatch
    open fun onQueryListError(data: String?) {
        swipe_refresh.onLoadMoreFail()
        showToast(data ?: "")
    }

    @LiveDataMatch
    open fun onDeleteSuccess(data: String?) {
        swipe_refresh.onAutoRefresh()
        showToast(data ?: "")
    }

    @RxBusEvent(from = "新增编辑页面")
    open fun onAddEditSuccess(rxCoalNoticeAddEditSuccess: RxCoalNoticeAddEditSuccess) {
        if (rxCoalNoticeAddEditSuccess.success) {
            swipe_refresh.onAutoRefresh()
        }
    }
}