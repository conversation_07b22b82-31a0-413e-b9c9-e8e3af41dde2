package com.zczy.cargo_owner.freight.req

import com.zczy.comm.http.entity.BaseNewRequest
import com.zczy.comm.http.entity.BaseRsp
import com.zczy.comm.http.entity.ResultData

/**
 *  desc: 申请提款-多渠道
 *  user: 宋双朋
 *  time: 2025/7/16 14:24
 */
class ReqQueryCapitalTypeAndMessage(
    var orderIds: String? = null, // 运单id
    var subSidiaryId: String? = null, // 结算主体
) : BaseNewRequest<BaseRsp<RspQueryCapitalTypeAndMessage>>("oms-app/consignor/drawMoney/queryCapitalTypeAndMessage")

class RspQueryCapitalTypeAndMessage(
    val customerSignState: String? = null, // 客户是否电子签认证完成 0：未完成，1：完成
    val mobile: String? = null, // 当前用户手机号
    val userList: MutableList<RspQueryCapitalTypeAndMessageUserInfo>? = null, // 拥有签署静默签的用户集合
    val needSignCustomer: String? = null, // 是否存在待签署电签单 1有 0 没有
    val signCustomerMobile: String? = null, // 指定签署人手机号
    val signCustomerUserName: String? = null, // 指定签署人姓名
    val modelSize: String? = null, // 渠道个数
    val applyMoney: String? = null, // 申请提款的钱
    val model: MutableList<RspQueryCapitalTypeAndMessageChannelInfo>? = null, // 渠道信息
    val loanLeftMoney: String? = null // 可用额度
) : ResultData()

data class RspQueryCapitalTypeAndMessageUserInfo(
    val mobile: String? = null, // 手机号
    val userRealName: String? = null // 真实名称
)

data class RspQueryCapitalTypeAndMessageChannelInfo(
    val capitalType: String? = null,// 1 工行 2 京东数科 3 交通银行 4 珠海华润 5 浙商银行 6 华夏银行
    val capitalTypeName: String? = null, // 1 工行 2 京东数科 3 交通银行 4 珠海华润 5 浙商银行 6 华夏银行
    val loanLeftMoney: String? = null, // 可用额度
)