package com.zczy.cargo_owner.freight.dialog

import android.content.Context
import android.graphics.Rect
import android.graphics.drawable.ColorDrawable
import android.os.Build
import android.view.LayoutInflater
import android.view.View
import android.view.ViewGroup
import android.widget.PopupWindow
import androidx.fragment.app.FragmentActivity
import com.zczy.cargo_owner.R
import com.zczy.cargo_owner.freight.req.ContractSigningEnum
import com.zczy.cargo_owner.freight.req.FreightFilterData
import com.zczy.cargo_owner.freight.req.YearEnum
import com.zczy.comm.widget.dialog.ChooseDialogV1
import com.zczy.comm.widget.inputv2.InputViewClick
import kotlinx.android.synthetic.main.freight_with_draw_dialog_1.view.inputView1
import kotlinx.android.synthetic.main.freight_with_draw_dialog_1.view.inputView2
import kotlinx.android.synthetic.main.freight_with_draw_dialog_1.view.tvClear
import kotlinx.android.synthetic.main.freight_with_draw_dialog_1.view.tvSure

/**
 *  desc: 申请提款-待申请-更多筛选
 *  user: 宋双朋
 *  time: 2025/7/16 15:59
 */
class FreightWithDrawDialog1(
    val mContext: Context?,
    var fData: FreightFilterData,
    var onSureBlock: (fData: FreightFilterData) -> Unit,
    var onClearBlock: (fData: FreightFilterData) -> Unit,
) : PopupWindow() {
    private var mItemView: View? = null

    init {
        mItemView = LayoutInflater.from(mContext).inflate(R.layout.freight_with_draw_dialog_1, null, false)
        contentView = mItemView
        mItemView?.setOnClickListener { dismiss() }
        val background = ColorDrawable()
        background.alpha = 100
        this.setBackgroundDrawable(background)
        this.isFocusable = true
        this.width = ViewGroup.LayoutParams.MATCH_PARENT
        this.height = ViewGroup.LayoutParams.MATCH_PARENT
        this.isOutsideTouchable = true
        initData()
        mItemView?.inputView1?.setListener(object : InputViewClick.Listener() {
            override fun onClick(viewId: Int, view: InputViewClick, content: String) {
                if (mContext is FragmentActivity) {
                    ChooseDialogV1.instance(YearEnum.getAllYears())
                        .setFlatMap { fTitle }
                        .setClick { s, _ ->
                            view.content = s.fTitle
                            fData.mYearEnum = s
                        }
                        .show(<EMAIL>)
                }
            }

        })
        mItemView?.inputView2?.setListener(object : InputViewClick.Listener() {
            override fun onClick(viewId: Int, view: InputViewClick, content: String) {
                if (mContext is FragmentActivity) {
                    ChooseDialogV1.instance(ContractSigningEnum.getAllSign())
                        .setFlatMap { fTitle }
                        .setClick { s, _ ->
                            view.content = s.fTitle
                            fData.mContractSigningEnum = s
                        }
                        .show(<EMAIL>)
                }
            }

        })
        mItemView?.tvClear?.setOnClickListener {
            fData = FreightFilterData()
            initData()
            onClearBlock(fData)
        }
        mItemView?.tvSure?.setOnClickListener {
            onSureBlock(fData)
            dismiss()
        }
    }

    fun initData() {
        mItemView?.inputView1?.content = fData.mYearEnum?.fTitle ?: ""
        mItemView?.inputView2?.content = fData.mContractSigningEnum?.fTitle ?: ""
    }

    fun show(anchor: View) {
        if (Build.VERSION.SDK_INT >= 24) {
            val visibleFrame = Rect()
            anchor.getGlobalVisibleRect(visibleFrame)
            val h = anchor.resources.displayMetrics.heightPixels - visibleFrame.bottom
            super.setHeight(h)
        }
        super.showAsDropDown(anchor)
    }

}