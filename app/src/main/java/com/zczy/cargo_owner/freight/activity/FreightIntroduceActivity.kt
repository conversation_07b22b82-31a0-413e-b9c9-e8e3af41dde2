package com.zczy.cargo_owner.freight.activity

import android.content.Context
import android.content.Intent
import android.os.Bundle
import android.view.View
import androidx.recyclerview.widget.LinearLayoutManager
import com.chad.library.adapter.base.BaseQuickAdapter
import com.sfh.lib.event.RxBusEvent
import com.sfh.lib.mvvm.annotation.LiveDataMatch
import com.sfh.lib.mvvm.service.BaseViewModel
import com.sfh.lib.ui.dialog.DialogBuilder
import com.zczy.cargo_owner.R
import com.zczy.cargo_owner.freight.adapter.FreightIntroduceAdapter
import com.zczy.cargo_owner.freight.model.FreightWithDrawModel
import com.zczy.cargo_owner.freight.req.ReqToLoanOpenApply
import com.zczy.cargo_owner.freight.req.RspFindApplyState
import com.zczy.cargo_owner.freight.req.RxIntroduceListRefresh
import com.zczy.cargo_owner.freight.req.SapBankApplyStateDtos
import com.zczy.cargo_owner.libcomm.config.HttpConfig
import com.zczy.comm.ui.BaseActivity
import com.zczy.comm.utils.dp2px
import com.zczy.comm.widget.itemdecoration.SpaceItemDecoration
import com.zczy.comm.x5.X5WebActivity
import kotlinx.android.synthetic.main.freight_introduce_activty.*

/**
 *  desc: 产品介绍
 *  user: zzf
 *  time: 2025/7/17 15:48
 */
class FreightIntroduceActivity : BaseActivity<BaseViewModel>(), BaseQuickAdapter.OnItemChildClickListener {
    private val mAdapter = FreightIntroduceAdapter()

    companion object {

        @JvmStatic
        fun jumpPage(context: Context?) {
            val intent = Intent(context, FreightIntroduceActivity::class.java)
            context?.startActivity(intent)
        }
    }

    override fun getLayout(): Int {
        return R.layout.freight_introduce_activty
    }

    override fun bindView(bundle: Bundle?) {
        mAdapter.apply {
            bindToRecyclerView(recyclerView)
            onItemChildClickListener = this@FreightIntroduceActivity
        }
        recyclerView.apply {
            layoutManager =
                LinearLayoutManager(this@FreightIntroduceActivity)
            adapter = mAdapter
            addItemDecoration(SpaceItemDecoration(dp2px(0f)))
        }
    }


    override fun initData() {
        getViewModel(FreightWithDrawModel::class.java).findApplyStateList()
    }


    @LiveDataMatch
    open fun findApplyStateListSuccess(data: RspFindApplyState) {
        // 只保留bankApplyFlag为"1"的数据
        val filteredList = data.sapBankApplyStateDtos?.filter { it.bankApplyFlag == "1" }
        mAdapter.setNewData(filteredList)
    }

    @RxBusEvent(from = "")
    open fun onEventNewGoodsSuccess(data: RxIntroduceListRefresh) {
        getViewModel(FreightWithDrawModel::class.java).findApplyStateList()
    }

    override fun onItemChildClick(adapter: BaseQuickAdapter<*, *>, view: View, position: Int) {
        val item = adapter.getItem(position)
        if (item is SapBankApplyStateDtos) {
            when (view.id) {
                R.id.btnApply -> {
                    // 申请、查看状态，提款
                    when (item.bankCode) {
                        "CZB" -> { // 浙商
                            when (item.applyBankState) {
                                "1" -> {
                                    //申请
                                    if (!item.isAgreementChecked) {
                                        showToast("请先勾选协议")
                                        return
                                    }
                                    getViewModel(FreightWithDrawModel::class.java).execute(
                                        true,
                                        ReqToLoanOpenApply(capitalType = "3")
                                    ) {
                                        if (it.success()) {
                                            FreightZSAccessActivity.start(this@FreightIntroduceActivity)
                                        } else {
                                            val dialog = DialogBuilder()
                                                .setHideCancel(true)
                                                .setTitle("申请提示")
                                                .setOkListener { dialogInterface, _ ->
                                                    dialogInterface.dismiss()
                                                }
                                                .setOKText("我知道了")
                                                .setMessage(it.msg)
                                            showDialog(dialog)
                                        }

                                    }
                                }

                                "9" -> {
                                    //提款
                                    FreightZSDrawMonryActivity.jumpPage(this@FreightIntroduceActivity, item)
                                }

                                else -> {
                                    //查看状态
                                    FreightZSStateActivity.start(this@FreightIntroduceActivity, item)
                                }
                            }
                        }

                        "BC" -> { // 交行
                            when (item.applyBankState) {
                                "1" -> {
                                    //申请
                                    if (!item.isAgreementChecked) {
                                        showToast("请先勾选协议")
                                        return
                                    }
                                    FreightBOCAccessActivity.start(this@FreightIntroduceActivity)
                                }

                                "8" -> {
                                    //提款
                                    FreightJHDrawMonryActivity.jumpPage(this@FreightIntroduceActivity, item)
                                }

                                else -> {
                                    //查看状态
                                    FreightBOCStateActivity.start(this@FreightIntroduceActivity, item)
                                }
                            }
                        }
                    }

                }

                R.id.cbAgree -> {
                    // 协议勾选状态切换
                    item.isAgreementChecked = !item.isAgreementChecked
                    adapter.notifyItemChanged(position)
                }

                R.id.llAgreementText -> {
                    //协议
                    X5WebActivity.startNoTitleContentUI(this, HttpConfig.getWebUrl() + "form_h5/h5_mobile/index.html?_t=" + System.currentTimeMillis() + "#/infoService")
                }
            }
        }
    }

}