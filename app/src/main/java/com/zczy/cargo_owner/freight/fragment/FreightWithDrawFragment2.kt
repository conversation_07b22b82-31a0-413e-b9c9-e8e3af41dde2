package com.zczy.cargo_owner.freight.fragment

import android.os.Bundle
import android.view.View
import androidx.recyclerview.widget.LinearLayoutManager
import com.sfh.lib.mvvm.annotation.LiveDataMatch
import com.sfh.lib.mvvm.service.BaseViewModel
import com.zczy.cargo_owner.R
import com.zczy.cargo_owner.freight.adapter.FreightWithDrawAdapter2
import com.zczy.cargo_owner.freight.dialog.FreightWithDrawDialog2
import com.zczy.cargo_owner.freight.model.FreightWithDrawModel
import com.zczy.cargo_owner.freight.req.FreightFilterData
import com.zczy.cargo_owner.freight.req.FundingPartyEnum
import com.zczy.cargo_owner.freight.req.QueryDrawMoneyPageList
import com.zczy.cargo_owner.freight.req.ReqQueryConsignorSub
import com.zczy.cargo_owner.freight.req.ReqQueryDrawMoneyList
import com.zczy.cargo_owner.freight.req.RspQueryConsignorSub
import com.zczy.cargo_owner.freight.req.RspQueryDrawMoneyList
import com.zczy.comm.http.entity.PageList
import com.zczy.comm.ui.BaseFragment
import com.zczy.comm.widget.dialog.ChooseDialogV1
import kotlinx.android.synthetic.main.freight_with_draw_fragment_2.swipeRefreshMoreLayout
import kotlinx.android.synthetic.main.freight_with_draw_fragment_2.tvFilter1
import kotlinx.android.synthetic.main.freight_with_draw_fragment_2.tvFilter2
import kotlinx.android.synthetic.main.freight_with_draw_fragment_2.tvFilter3

/**
 *  desc: 申请提款-多渠道
 *  user: 宋双朋
 *  time: 2025/7/16 14:08
 */
class FreightWithDrawFragment2 : BaseFragment<BaseViewModel>() {
    private val mAdapter = FreightWithDrawAdapter2()
    private var mRspQueryConsignorSub: RspQueryConsignorSub? = null // 结算主体
    private var mFundingPartyEnum: FundingPartyEnum? = null // 资金方
    private var mfData: FreightFilterData = FreightFilterData()

    companion object {

        fun newInstance(): FreightWithDrawFragment2 {
            val fragment = FreightWithDrawFragment2()
            val bundle = Bundle()
            fragment.arguments = bundle
            return fragment
        }
    }

    override fun getLayout(): Int {
        return R.layout.freight_with_draw_fragment_2
    }

    override fun initData() {

    }

    override fun bindView(view: View, bundle: Bundle?) {
        bindClickEvent(tvFilter1)
        bindClickEvent(tvFilter2)
        bindClickEvent(tvFilter3)
        swipeRefreshMoreLayout.apply {
            setLayoutManager(LinearLayoutManager(<EMAIL>))
            setAdapter(mAdapter, true)
            setEmptyView(R.layout.invoice_apply_empty_item_2)
            setOnLoadListener2 { nowPage ->
                refreshData(nowPage = nowPage)
            }
            addOnItemChildClickListener { _, view, position ->
                val item = mAdapter.data[position]
                when (view.id) {

                }
            }
            onAutoRefresh()
        }
    }

    private fun refreshData(nowPage: Int) {
        getViewModel(FreightWithDrawModel::class.java).queryList(
            req = ReqQueryDrawMoneyList(
                nowPage = nowPage,
                drawMoneySubmitType = "1",
                periodFlag = mfData.mYearEnum?.fCode,
                moneySource = mFundingPartyEnum?.fCode,
                subsidiaryId = mRspQueryConsignorSub?.upSettlePartId,
                signedType = mfData.mContractSigningEnum?.fCode,
                settleApplyExtractState = mfData.mLoanStatusEnum?.fCode,
                settleApplyTime = "5",
                settleApplyTimeS = mfData.time1,
                settleApplyTimeE = mfData.time2,
                loanTimeS = mfData.time3,
                loanTimeE = mfData.time4,
                settleApplyExtractMoneyS = mfData.etLoanAmountStart,
                settleApplyExtractMoneyE = mfData.etLoanAmountEnd,
            )
        )
    }

    override fun onSingleClick(v: View) {
        super.onSingleClick(v)
        when (v.id) {
            R.id.tvFilter1 -> {
                //资金方
                ChooseDialogV1.instance(FundingPartyEnum.getAllFundingParty())
                    .setFlatMap { fTitle }
                    .setClick { s, _ ->
                        mFundingPartyEnum = s
                        tvFilter1.text = s.fTitle
                        refreshData(nowPage = 1)
                    }
                    .show(this@FreightWithDrawFragment2)
            }

            R.id.tvFilter2 -> {
                //结算主体
                getViewModel(BaseViewModel::class.java).execute(ReqQueryConsignorSub()) {
                    if (it.success()) {
                        it.data?.dataList?.let { list ->
                            ChooseDialogV1.instance(list)
                                .setFlatMap { upSettlePartName ?: "" }
                                .setClick { s, _ ->
                                    mRspQueryConsignorSub = s
                                    tvFilter2.text = s.upSettlePartName
                                    refreshData(nowPage = 1)
                                }
                                .show(this@FreightWithDrawFragment2)
                        }
                    }
                }
            }

            R.id.tvFilter3 -> {
                //更多
                FreightWithDrawDialog2(
                    mContext = <EMAIL>,
                    fData = mfData,
                    onSureBlock = { fData: FreightFilterData ->
                        mfData = fData
                        refreshData(nowPage = 1)
                    }
                ).show(tvFilter1)
            }
        }
    }

    @LiveDataMatch
    open fun queryListSuccess(data: QueryDrawMoneyPageList<RspQueryDrawMoneyList>) {
        swipeRefreshMoreLayout.onRefreshCompale(data)
    }

    @LiveDataMatch
    open fun queryListError() {
        swipeRefreshMoreLayout.onLoadMoreFail()
    }
}