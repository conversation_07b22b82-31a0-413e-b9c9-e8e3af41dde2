package com.zczy.cargo_owner.freight.req

import com.zczy.comm.http.entity.BaseNewRequest
import com.zczy.comm.http.entity.BaseRsp
import com.zczy.comm.http.entity.ResultData

/**
 *  desc: 申请提款-确认提款接口
 *  user: 宋双朋
 *  time: 2025/7/16 14:24
 */
class ReqSubmitOrderLoanApply(
    var orderIds: String? = null, // 运单id
    var subSidiaryId: String? = null, // 结算主体
    var capitalType: String? = null, // 融资渠道 3 交通银行 5 浙商银行
) : BaseNewRequest<BaseRsp<ResultData>>("oms-app/consignor/drawMoney/submitOrderLoanApply")
