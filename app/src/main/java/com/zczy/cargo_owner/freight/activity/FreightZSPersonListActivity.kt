package com.zczy.cargo_owner.freight.activity

import android.content.Context
import android.content.Intent
import android.os.Bundle
import com.sfh.lib.event.RxBusEvent
import com.sfh.lib.exception.HandleException
import com.sfh.lib.rx.IResult
import com.sfh.lib.ui.dialog.DialogBuilder
import com.zczy.cargo_owner.R
import com.zczy.cargo_owner.freight.adapter.FreightPersonAdapter
import com.zczy.cargo_owner.freight.model.FreightWithDrawModel
import com.zczy.cargo_owner.freight.req.ReqDeleteCzOperatorInfo
import com.zczy.cargo_owner.freight.req.ReqQueryCzOperatorInfoList
import com.zczy.cargo_owner.freight.req.RspCzOperatorInfo
import com.zczy.comm.http.entity.BaseRsp
import com.zczy.comm.http.entity.PageList
import com.zczy.comm.ui.BaseActivity
import kotlinx.android.synthetic.main.freight_zs_person_list_activty.*

/**
 *  desc: 浙商- 维护授权操作员
 *  user:孙飞虎
 *  time: 2025/7/16 14:05
 */
class FreightZSPersonListActivity : BaseActivity<FreightWithDrawModel>() {

    companion object {

        @JvmStatic
        fun start(context: Context?) {
            val intent = Intent(context, FreightZSPersonListActivity::class.java)
            context?.startActivity(intent)
        }
    }

    override fun getLayout(): Int {
        return R.layout.freight_zs_person_list_activty
    }

    override fun bindView(bundle: Bundle?) {
        swipe_refresh_more_layout?.let {
            it.setAdapter(FreightPersonAdapter(), false)
            it.setOnLoadListener2 {
                getViewModel(FreightWithDrawModel::class.java).execute(ReqQueryCzOperatorInfoList(),object :IResult<BaseRsp<PageList<RspCzOperatorInfo>>>{
                    override fun onSuccess(t: BaseRsp<PageList<RspCzOperatorInfo>>) {
                        if (t.success()){
                            swipe_refresh_more_layout.onRefreshCompale(t.data)
                        }else{
                            swipe_refresh_more_layout.onLoadMoreFail()
                            showToast(t.msg)
                        }
                    }
                    override fun onFail(e: HandleException) {
                        swipe_refresh_more_layout.onLoadMoreFail()
                        showToast(e.msg)
                    }

                })
            }
            it.addOnItemChildClickListener { adapter, view, position ->
                var item = adapter.getItem(position) as RspCzOperatorInfo
                when (view.id) {
                    R.id.btn_edit_info -> {
                        FreightZSPersonEditActivity.start(this@FreightZSPersonListActivity,item)
                    }
                    R.id.btn_delete_info -> {
                        var dailog = DialogBuilder()
                            .setTitle("提示")
                            .setMessage("确定删除吗？")
                        dailog.setOkListener { dialog, which ->
                            dialog.dismiss()
                            getViewModel(FreightWithDrawModel::class.java)
                                .execute(false, ReqDeleteCzOperatorInfo(id = item.id)) {
                                    if (it.success()) {
                                        showToast(it.msg)
                                        swipe_refresh_more_layout?.onAutoRefresh()
                                    } else {
                                        showDialogToast(it.msg)
                                    }
                                }
                        }
                        showDialog(dailog)
                    }
                }

            }

            it.onAutoRefresh()
        }

        btn_submit.setOnClickListener {
            FreightZSPersonAddActivity.start(this@FreightZSPersonListActivity)
        }
    }

    override fun initData() {


    }
    @RxBusEvent(from = "刷新列表")
    open fun onAutoRefresh(data: RspCzOperatorInfo){
        swipe_refresh_more_layout?.onAutoRefresh()
    }

}