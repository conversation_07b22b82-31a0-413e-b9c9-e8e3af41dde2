package com.zczy.cargo_owner.freight.model

data class Industry(
    val label: String,
    val value: String
)

data class CompanyScale(
    val label: String,
    val value: String
)

data class Certificate(
    val label: String,
    val value: String
)

data class Leader(
    val label: String,
    val value: String
)

data class CompanyQuality(
    val label: String,
    val value: String
)

object DataProvider {

    // 行业数据
    fun getIndustry(): List<Industry> {
        return listOf(
            Industry("机制纸及纸板制造", "C2221"),
            Industry("手工纸制造", "C2222"),
            Industry("加工纸制造", "C2223"),
            Industry("纸和纸板容器制造", "C2231"),
            Industry("其他纸制品制造", "C2239"),
            Industry("其他电子设备制造", "C3991"),
            Industry("电工仪器仪表制造", "C4012"),
            Industry("金属表面处理及热处理加工", "C3361"),
            Industry("黑色金属铸造", "C3391"),
            Industry("有色金属铸造", "C3392"),
            Industry("锻件及粉末冶金制品制造", "C3393"),
            Industry("其他未列明金属制品制造", "C3399"),
            Industry("金属切削机床制造", "C3421"),
            Industry("金属成形机床制造", "C3422"),
            Industry("铸造机械制造", "C3423"),
            Industry("金属切割及焊接设备制造", "C3424"),
            Industry("机床功能部件及附件制造", "C3425"),
            Industry("其他金属加工机械制造", "C3429"),
            Industry("其他电气机械及器材制造", "C3890"),
            Industry("通用仪器仪表制造", "C4010"),
            Industry("专用仪器仪表制造", "C4020"),
            Industry("其他仪器仪表制造业", "C4090"),
            Industry("金属废料和碎屑加工处理", "C4210"),
            Industry("非金属废料和碎屑加工处理", "C4220"),
            Industry("电力生产", "D4410"),
            Industry("电力供应", "D4420"),
            Industry("热力生产和供应", "D4430"),
            Industry("燃气生产和供应业", "D4510"),
            Industry("沿海货物运输", "G5522"),
            Industry("内河货物运输", "G5523"),
            Industry("货运港口", "G5532"),
            Industry("多式联运", "G5811"),
            Industry("货物运输代理", "G5821"),
            Industry("通用仓储", "G5921"),
            Industry("低温仓储", "G5931"),
            Industry("油气仓储", "G5941"),
            Industry("危险化学品仓储", "G5942"),
            Industry("其他危险品仓储", "G5949"),
            Industry("谷物仓储", "G5951"),
            Industry("棉花仓储", "G5952"),
            Industry("其他农产品仓储", "G5959"),
            Industry("其他仓储业", "G5991"),
            Industry("物流园区", "G5992"),
            Industry("农、林、牧、渔产品批发", "F5110"),
            Industry("食品、饮料及烟草制品批发", "F5120"),
            Industry("矿产品、建材及化工产品批发", "F5160"),
            Industry("机械设备、五金产品及电子产品批发", "F5170"),
            Industry("贸易经纪与代理", "F5180"),
            Industry("其他批发业", "F5190"),
            Industry("铁路货物运输", "G5320"),
            Industry("道路货物运输", "G5430"),
            Industry("水上货物运输", "G5520"),
            Industry("多式联运", "G5810"),
            Industry("运输代理业", "G5820"),
            Industry("谷物、棉花等农产品仓储", "G5950"),
            Industry("其他仓储业", "G5990")
        )
    }

    // 企业规模数据
    fun getCompanyScale(): List<CompanyScale> {
        return listOf(
            CompanyScale("大型", "01"),
            CompanyScale("中型", "02"),
            CompanyScale("小型", "03"),
            CompanyScale("微型", "04"),
            CompanyScale("无需划型客户", "05"),
            CompanyScale("大暂无法划型客户型", "06"),
            CompanyScale("特殊类型客户", "09")
        )
    }

    // 证件类型数据
    fun getCertificates(): List<Certificate> {
        return listOf(
            Certificate("身份证", "0"),
            Certificate("护照", "1"),
            Certificate("军官证", "2"),
            Certificate("士兵证", "3"),
            Certificate("港澳台居民往来通行证", "4"),
            Certificate("临时身份证", "5"),
            Certificate("户口本", "6"),
            Certificate("其他", "7"),
            Certificate("警官证", "9"),
            Certificate("外国人永久居留证", "12")
        )
    }

    // 是否实际控制人数据
    fun getLeader(): List<Leader> {
        return listOf(
            Leader("是", "1"),
            Leader("否", "2")
        )
    }

    // 企业性质数据
    fun getCompanyQuality(): List<CompanyQuality> {
        return listOf(
            CompanyQuality("中央政府、省、直辖市政府", "A01"),
            CompanyQuality("副省级、财政收入前50名地级市政府（含同级别市辖区）", "A02"),
            CompanyQuality("重点城市国家级新区市辖区政府、其他地级市政府（含同级别市辖区）、前10名百强县政府", "A03"),
            CompanyQuality("其他重点城市市辖区政府、其他百强县政府", "A04"),
            CompanyQuality("除上述外其他地区政府", "A05"),
            CompanyQuality("国家级全额拨款事业单位", "B01"),
            CompanyQuality("三甲医院、省（部）级全额拨款事业单位", "B02"),
            CompanyQuality("三乙医院，市级全额拨款事业单位", "B03"),
            CompanyQuality("其他医院、其他全额拨款事业单位", "B04"),
            CompanyQuality("军级及以上部队单位及其下属医院、学校", "C01"),
            CompanyQuality("副军级部队单位及其下属医院、学校", "C02"),
            CompanyQuality("正师级部队单位", "C03"),
            CompanyQuality("其他编制部队单位", "C04"),
            CompanyQuality("央企集团总部及其一级子公司、工行及各分支机构或直属中心", "D01"),
            CompanyQuality("央企二级子公司、500强企业、省级国有企业总部", "D02"),
            CompanyQuality("央企三级子公司及控股子公司、上市公司（非新三板）、省级国有企业一级子公司、市级国有企业", "D03"),
            CompanyQuality("其他国有控股企业、民营行业龙头企业", "D04"),
            CompanyQuality("其他企业", "D05")
        )
    }
}
