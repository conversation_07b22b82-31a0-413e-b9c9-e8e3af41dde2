package com.zczy.cargo_owner.freight.activity

import android.content.Context
import android.content.Intent
import android.os.Bundle
import android.text.InputType
import android.text.TextUtils
import com.zczy.cargo_owner.R
import com.zczy.cargo_owner.freight.model.DataProvider
import com.zczy.cargo_owner.freight.model.FreightWithDrawModel
import com.zczy.cargo_owner.freight.req.*
import com.zczy.comm.CommServer
import com.zczy.comm.TimePickerUtilV1
import com.zczy.comm.ui.BaseActivity
import com.zczy.comm.utils.ex.YYYY_MM_DD
import com.zczy.comm.utils.ex.getFormatTime
import com.zczy.comm.utils.imageselector.ImageSelectorNew
import com.zczy.comm.utils.imageselector.OnFileCallbackListener
import com.zczy.comm.widget.dialog.ChooseDialogV1
import com.zczy.comm.widget.inputv2.InputViewClick
import com.zczy.comm.widget.inputv2.InputViewImage
import com.zczy.comm.widget.pickerview.picker.TimePicker
import io.reactivex.Observable
import kotlinx.android.synthetic.main.freight_zs_access_activty.*
import java.io.File

/**
 *  desc: 浙商准入申请
 *  user:孙飞虎
 *  time: 2025/7/16 14:05
 */
class FreightZSAccessActivity : BaseActivity<FreightWithDrawModel>() {

    companion object {

        @JvmStatic
        fun start(context: Context?) {
            val intent = Intent(context, FreightZSAccessActivity::class.java)
            context?.startActivity(intent)
        }
    }

    var postReq = ReqApplySap()

    override fun getLayout(): Int {
        return R.layout.freight_zs_access_activty
    }

    override fun bindView(bundle: Bundle?) {

        // 操作员证件到期日期
        input_tv_operator_id_expiry.setListener(object : InputViewClick.Listener() {
            override fun onClick(viewId: Int, view: InputViewClick, content: String) {
                TimePickerUtilV1.showComm4(
                    context = this@FreightZSAccessActivity, title = "请选择", null,
                    TimePicker.TYPE_DATE
                ) {
                    postReq.enterpriseEstablishDate = it.getFormatTime(YYYY_MM_DD)
                    input_tv_operator_id_expiry.content = postReq.enterpriseEstablishDate ?: ""
                }
            }
        })

        //法定代表人证件类型
        input_tv_id_type.setListener(object : InputViewClick.Listener() {
            override fun onClick(viewId: Int, view: InputViewClick, content: String) {
                ChooseDialogV1.instance(DataProvider.getCertificates())
                    .setFlatMap { label }
                    .setClick { item, i ->
                        postReq.legalDeputyCertificate = item.value
                        input_tv_id_type.content = item.label
                    }.show(this@FreightZSAccessActivity)
            }
        })
        //操作员证件类型
        input_tv_operator_id_type.setListener(object : InputViewClick.Listener() {
            override fun onClick(viewId: Int, view: InputViewClick, content: String) {
                ChooseDialogV1.instance(DataProvider.getCertificates())
                    .setFlatMap { label }
                    .setClick { item, i ->
                        postReq.operatorCertificateType = item.value
                        input_tv_operator_id_type.content = item.label
                    }.show(this@FreightZSAccessActivity)
            }
        })
        //手机号码
        input_tv_operator_phone.setInputType(InputType.TYPE_CLASS_PHONE)

        //法定代表身份证正面
        input_tv_id_front.setListener(object : InputViewImage.Listener() {
            override fun onClickImg(viewId: Int, view: InputViewImage) {
                ImageSelectorNew.open(this@FreightZSAccessActivity, 1, true, true, object :
                    OnFileCallbackListener {
                    override fun onResult(result: MutableList<String>?) {

                        getViewModel(FreightWithDrawModel::class.java)
                            .execute(
                                true,
                                Observable.just(1).map {
                                    return@map CommServer.getFileServer()
                                        .update2(File(result?.get(0)))
                                }
                            ) {
                                if (it.success()) {
                                    postReq.legalDeputyCertificateFrontUrl = it.data?.sigleUrl ?: ""
                                    input_tv_id_front.setImgUrl(it.data?.sigleUrl ?: "")

                                } else {
                                    showDialogToast(it.msg)
                                }
                            }
                    }

                    override fun onCancel() {
                    }
                })
            }
        })
        //法定代表身份证反面
        input_tv_id_back.setListener(object : InputViewImage.Listener() {
            override fun onClickImg(viewId: Int, view: InputViewImage) {
                ImageSelectorNew.open(this@FreightZSAccessActivity, 1, true, true, object :
                    OnFileCallbackListener {
                    override fun onResult(result: MutableList<String>?) {

                        getViewModel(FreightWithDrawModel::class.java)
                            .execute(
                                true,
                                Observable.just(1).map {
                                    return@map CommServer.getFileServer()
                                        .update2(File(result?.get(0)))
                                }
                            ) {
                                if (it.success()) {
                                    postReq.legalDeputyCertificateBackUrl = it.data?.sigleUrl ?: ""
                                    input_tv_id_back.setImgUrl(it.data?.sigleUrl ?: "")

                                } else {
                                    showDialogToast(it.msg)
                                }
                            }
                    }

                    override fun onCancel() {
                    }
                })
            }
        })
        btn_submit.setOnClickListener {
            postApply()
        }
    }

    fun postApply() {
        if (TextUtils.isEmpty(input_tv_business_scope.content)) {
            showToast("请输入通讯地址")
            return;
        }
        postReq.contactAddress = input_tv_business_scope.content

        if (TextUtils.isEmpty(input_tv_legal_representative.content)) {
            showToast("请输入法定代表人")
            return;
        }
        postReq.legalDeputy = input_tv_legal_representative.content

        if (TextUtils.isEmpty(input_tv_id_type.content)) {
            showToast("请选择法定代表人证件类型")
            return;
        }

        if (TextUtils.isEmpty(input_tv_id_number.content)) {
            showToast("请输入法定代表人证件")
            return;
        }
        postReq.legalDeputyCertificateNo = input_tv_id_number.content

        //法定代表人身份证正面
        if (TextUtils.isEmpty(postReq.legalDeputyCertificateFrontUrl)) {
            showToast("请上传法定代表人身份证正面")
            return;
        }
        //法定代表人身份证反面
        if (TextUtils.isEmpty(postReq.legalDeputyCertificateBackUrl)) {
            showToast("请上传法定代表人身份证反面")
            return;
        }
        //操作员名称
        if (TextUtils.isEmpty(input_tv_operator_name.content)) {
            showToast("请输入操作员名称")
            return;
        }
        postReq.operatorName = input_tv_operator_name.content
        //操作员证件类型
        if (TextUtils.isEmpty(input_tv_operator_id_type.content)) {
            showToast("请选择操作员证件类型")
            return;
        }
        //操作员证件号码
        if (TextUtils.isEmpty(input_tv_operator_id_number.content)) {
            showToast("请输入操作员证件号码")
            return;
        }
        postReq.operatorCertificateNo = input_tv_operator_id_number.content

        //操作员证件到期日期
        if (TextUtils.isEmpty(input_tv_operator_id_expiry.content)) {
            showToast("请选择操作员证件到期日期")
            return;
        }
        postReq.operatorCertificateEndDate = input_tv_operator_id_expiry.content

        //操作员手机号
        if (TextUtils.isEmpty(input_tv_operator_phone.content) || input_tv_operator_phone.content.length != 11) {
            showToast("请输入正确操作员手机号")
            return;
        }
        postReq.operatorMobile = input_tv_operator_phone.content

        CommServer.getUserServer().login?.let {
            postReq.userName = it.userName
            postReq.userId = it.userId
        }
        getViewModel(FreightWithDrawModel::class.java).execute(
            false, postReq
        ) {
            if (it.success()) {
                showDialogToast("申请提交成功")
                FreightZSFaceImageActivity.start(this@FreightZSAccessActivity)
                postEvent(RxIntroduceListRefresh())
                finish()
            } else {
                showDialogToast(it.msg)
            }
        }

    }

    override fun initData() {

        getViewModel(FreightWithDrawModel::class.java).execute(
            true,
            ReqToLoanOpenApply(capitalType = "3")
        ) {
            if (it.success()) {
                input_tv_code.content = it.data?.unifiedSocialCreditCode ?: ""//  社会统一信用代码
                input_tv_address.content = it.data?.enterpriseRegisteredAddress ?: ""// 注册地址

                input_tv_legal_representative.content = it.data?.legalDeputy ?: ""//法定代表人
                input_tv_legal_representative.editText.isEnabled =
                    TextUtils.isEmpty(it.data?.legalDeputy)//为空可以输入
                input_tv_id_number.content = it.data?.legalIdCardNo ?: ""//法人证件号码
                input_tv_id_number.editText.isEnabled =
                    TextUtils.isEmpty(it.data?.legalIdCardNo)//为空可以输入

                postReq.capitalType = "5"
                postReq.customerId = it.data?.customerId ?: ""
                postReq.unifiedSocialCreditCode = it.data?.unifiedSocialCreditCode ?: ""
                postReq.enterpriseRegisteredAddress = it.data?.enterpriseRegisteredAddress ?: ""
                postReq.legalDeputy = it.data?.legalDeputy ?: ""
                postReq.legalDeputyMobile = it.data?.legalDeputyMobile ?: ""
                postReq.legalDeputyCertificateNo = it.data?.legalIdCardNo ?: ""
                postReq.enterpriseEntryDate = it.data?.enterpriseEntryDate ?: ""
                postReq.fristTradeDate = it.data?.fristOrderDate ?: ""
                postReq.enterpriseBusinessScope = it.data?.businessScope ?: ""
                postReq.economicType = it.data?.economicType ?: ""
                postReq.customerName = it.data?.customerName ?: ""
            } else {
                showDialogToast(it.msg)
            }
        }
    }

}