package com.zczy.cargo_owner.freight.req

import com.zczy.comm.http.entity.BaseNewRequest
import com.zczy.comm.http.entity.BaseRsp
import com.zczy.comm.http.entity.ResultData

/**
 *  提交准入申请
 *  http://wiki.zczy56.com/pages/viewpage.action?pageId=91586783
 */
class ReqApplySap(
    var capitalType: String = "", // 3交行 5 浙商
    var customerId: String? = null, // 客户ID
    var unifiedSocialCreditCode: String? = null, // 社会统一信用代码
    var enterpriseRegisteredAddress: String? = null, // 注册地址
    var legalDeputy: String? = null, // 法定代表人
    var enterpriseEntryDate: String? = null, // 入驻日期（货主平台注册日期）
    var legalDeputyCertificate: String? = null, // 法定代表人证件类型
    var legalDeputyCertificateNo: String? = null, // 法定代表人证件号码
    var legalDeputyMobile: String? = null, // 法定代表人电话/手机号
    var legalDeputyActualControllerFlag: String? = null, // 法定代表人是否实际控制人 ：1.是 2.否
    var enterpriseIndustry: String? = null, // 所属行业
    var enterpriseEstablishDate: String? = null, // 成立日期
    var enterpriseStaffCount: String? = null, // 员工数
    var enterpriseScale: String? = null, // 企业规模
    var enterpriseBusinessScope: String? = null, // 经营范围
    var economicType: String? = null, // 企业性质
    var customerName: String? = null, // 客户名称
    var fristTradeDate: String? = null, // 交易日期 （货主平台第一单挂单日期）
    var userId: String? = null, // 用户userid
    var userName: String? = null, // 用户名称


    var contactAddress: String = "", // 通讯地址
    var legalDeputyCertificateFrontUrl: String = "", // 法定代表人身份证正面照片OSS地址
    var legalDeputyCertificateBackUrl: String = "", // 法定代表人身份证反面照片OSS地址
    var operatorName: String = "", // 操作员名称
    var operatorCertificateType: String = "", // 操作员证件类型，若为身份证，则传"0"
    var operatorCertificateNo: String = "", // 操作员证件号码
    var operatorCertificateEndDate: String = "", // 操作员证件到期日
    var operatorMobile: String = "" // 操作员手机号
) : BaseNewRequest<BaseRsp<RspApplySap>>("ams-app/loan/apply/applySap")

data class RspApplySap(
   var data:String? = null,
) : ResultData()
