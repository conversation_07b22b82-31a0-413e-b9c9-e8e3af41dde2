package com.zczy.cargo_owner.freight.activity

import android.content.Context
import android.content.Intent
import android.os.Bundle
import android.view.View
import android.widget.LinearLayout
import com.sfh.lib.mvvm.service.BaseViewModel
import com.zczy.cargo_owner.R
import com.zczy.cargo_owner.freight.req.*
import com.zczy.comm.SpannableHepler
import com.zczy.comm.ui.BaseActivity
import com.zczy.comm.utils.toJson
import com.zczy.comm.utils.toJsonObject
import kotlinx.android.synthetic.main.freight_zs_state_activty.*

/**
 *  (浙商)准入申请状态-融资申请状态
 *  user:孙飞虎
 */
class FreightZSStateActivity : BaseActivity<BaseViewModel>() {

    companion object {
        @JvmStatic
        fun start(context: Context?, data: SapBankApplyStateDtos) {
            val intent = Intent(context, FreightZSStateActivity::class.java)
            intent.putExtra("data", data.toJson())
            context?.startActivity(intent)
        }
    }

    override fun getLayout(): Int {
        return R.layout.freight_zs_state_activty
    }

    override fun bindView(bundle: Bundle?) {
    }


    override fun initData() {

        var json = intent.getStringExtra("data")
        var data = json.toJsonObject(SapBankApplyStateDtos::class.java)
//        浙商：
//        1待准入申请，
//        2准入申请中
//        3准入审核中
//        4准入拒绝 ，展示准入“重新申请”的按钮
//        5融资待申请（可以点授信申请按钮）
//        6融资申请中
//        7融资审核中
//        8融资审核失败
//        9融资申请完成

//        applyBankState为2准入申请中，提示“您的准入申请正在处理中，请耐心等待结果!”；
//        applyBankState为3准入审核中，提示“您的准入申请浙商银行审核中，请耐心等待结果!”；
//        applyBankState为4准入拒绝，提示“您的准入申请审批未通过！可发起重新申请 。”，同时展示“重新申请”按钮；
//        applyBankState为5融资待申请，展示“授信申请”按钮；
//        applyBankState为6融资申请中，提示“您的授信正在申请中，如需继续完成申请，请点击访问浙商银行小程序!”；
//        applyBankState为7融资审核中，提示“您的授信申请浙商银行审核中，请耐心等待结果!”
//        applyBankState为8融资失败，提示“您的授信申请审批未通过！可发起重新申请 。”，同时展示“重新申请”按钮；
//        applyBankState为9融资成功，展示额度信息、额度到期日信息。


        iv_step.setImageResource(
            when (data?.applyBankState) {
                "1","2","3","4" -> R.drawable.icon_boc_state_1
                else -> R.drawable.icon_boc_state_2
            }
        )
        when(data?.applyBankState){
            "2" -> {
                iv.setImageResource(R.drawable.icon_boc_state1)
                tv_toast.text = "您的准申请正在处理中，请耐心等待结果！"
                tv_onclick.visibility = View.GONE
            }
            "3" -> {
                iv.setImageResource(R.drawable.icon_boc_state1)
                tv_toast.text = SpannableHepler(" 您好！准入申请").append(SpannableHepler.Txt("浙商银行审核中","#FF5E1D")).append("，请耐心等待结果!").builder()
                tv_onclick.visibility = View.GONE
            }
            "4" -> {
                iv.setImageResource(R.drawable.evalutate_no_information)
                tv_toast.text = "很抱歉，您暂未获得浙商银行准入资格！"+data?.banklTimePoint+"后可重新申请"
                tv_onclick.visibility = View.VISIBLE
                tv_onclick.setOnClickListener {
                    FreightZSAccessActivity.start(this@FreightZSStateActivity)
                    finish()
                }
            }
            "5" -> {
                iv.setImageResource(R.drawable.enterprise_realname_success)
                tv_toast.text = "恭喜您！您的准入申请已通过，请完成授信申请！"
                tv_onclick.text = "授信申请"
                tv_onclick.visibility = View.VISIBLE
                tv_onclick.setOnClickListener {
                    // 点击“授信申请”按钮弹窗显
                    FreightZSFaceImageActivity.start(this@FreightZSStateActivity)
                    finish()
                }
                tv_edit.visibility = View.VISIBLE
                tv_edit.setOnClickListener {
                    //修改授权操作员
                    FreightZSPersonListActivity.start(this@FreightZSStateActivity)
                }
            }
            "6" -> {
                iv.setImageResource(R.drawable.icon_boc_state1)
                tv_toast.text = "您的授信正在申请中，如需继续完成申请，请点击访问浙商银行小程序!"
                tv_onclick.visibility = LinearLayout.VISIBLE
                tv_onclick.text = "访问浙商银行小程序"
                tv_onclick.setOnClickListener {
                    // 点击“访问浙商银行小程序”按钮
                    FreightZSFaceImageActivity.start(this@FreightZSStateActivity)
                }
            }
            "7" -> {
                iv.setImageResource(R.drawable.icon_boc_state1)
                tv_toast.text =  SpannableHepler("您的授信申请").append(SpannableHepler.Txt("交通银行审核中","#FF5E1D")).append("，请耐心等待结果!").builder()
                tv_onclick.visibility = View.GONE
            }
            "8" -> {
                iv.setImageResource(R.drawable.evalutate_no_information)
                tv_toast.text = "您的授信申请审批未通过！可发起重新申请。"
                tv_onclick.visibility = View.VISIBLE
                tv_onclick.setOnClickListener {
                    // 点击“重新申请”按钮
                    FreightZSAccessActivity.start(this@FreightZSStateActivity)
                    finish()
                }
            }
        }
    }

}