package com.zczy.cargo_owner.freight.req

import com.zczy.comm.http.entity.BaseNewRequest
import com.zczy.comm.http.entity.BaseRsp
import com.zczy.comm.http.entity.ResultData

/**
 *  申请运费贷准入，跳转至准入页面
 *  http://wiki.zczy56.com/pages/viewpage.action?pageId=91586783
 */
class ReqToLoanOpenApply(
    var capitalType: String = ""//3交行 5 浙商
) : BaseNewRequest<BaseRsp<RspToLoanOpenApply>>("ams-app/loan/apply/toLoanOpenApply")

data class RspToLoanOpenApply(
    var customerId: String? = "",// 客户ID
    var unifiedSocialCreditCode: String? = "",//  社会统一信用代码
    var enterpriseRegisteredAddress: String? = "",// 注册地址
    var legalDeputy: String? = "",// 法定代表人
    var enterpriseEntryDate: String? = "",// 入驻日期（货主平台注册日期）
    var legalDeputyMobile: String? = "",// 法定代表人电话/手机号
    var businessScope: String? = "",// 经营范围
    var economicType: String? = "",// 企业性质
    var customerName: String? = "",// 客户名称
    var legalIdCardNo: String? = "",// 法人证件号码	String
    var fristOrderDate: String? = "",// 首单挂单日期
) : ResultData()
