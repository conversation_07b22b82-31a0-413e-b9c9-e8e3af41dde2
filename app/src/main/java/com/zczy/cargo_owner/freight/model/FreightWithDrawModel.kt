package com.zczy.cargo_owner.freight.model

import com.sfh.lib.exception.HandleException
import com.sfh.lib.mvvm.service.BaseViewModel
import com.sfh.lib.rx.IResult
import com.zczy.cargo_owner.freight.req.QueryDrawMoneyPageList
import com.zczy.cargo_owner.freight.req.ReqConsignorMoneyList
import com.zczy.cargo_owner.freight.req.ReqFindApplyState
import com.zczy.cargo_owner.freight.req.ReqQueryCapitalTypeAndMessage
import com.zczy.cargo_owner.freight.req.ReqQueryDrawMoneyList
import com.zczy.cargo_owner.freight.req.ReqSubmitSignatureContract
import com.zczy.cargo_owner.freight.req.RspConsignorMoneyList
import com.zczy.cargo_owner.freight.req.RspFindApplyState
import com.zczy.cargo_owner.freight.req.RspQueryCapitalTypeAndMessage
import com.zczy.cargo_owner.freight.req.RspQueryDrawMoneyList
import com.zczy.cargo_owner.freight.req.RspSubmitSignatureContract
import com.zczy.comm.http.entity.BaseRsp
import com.zczy.comm.http.entity.PageList

/**
 *  desc: 申请提款-多渠道
 *  user: 宋双朋
 *  time: 2025/7/16 14:16
 */
class FreightWithDrawModel : BaseViewModel() {
    fun queryList(req: ReqQueryDrawMoneyList) {
        execute(req, object : IResult<BaseRsp<QueryDrawMoneyPageList<RspQueryDrawMoneyList>>> {
            override fun onSuccess(t: BaseRsp<QueryDrawMoneyPageList<RspQueryDrawMoneyList>>) {
                if (t.success()) {
                    setValue("queryListSuccess", t.data)
                } else {
                    showToast(t.msg)
                    setValue("queryListError")
                }
            }

            override fun onFail(e: HandleException) {
                showToast(e.msg)
                setValue("queryListError")
            }
        })
    }

    fun consignorMoneyList(req: ReqConsignorMoneyList) {
        execute(req, object : IResult<BaseRsp<PageList<RspConsignorMoneyList>>> {
            override fun onSuccess(t: BaseRsp<PageList<RspConsignorMoneyList>>) {
                if (t.success()) {
                    setValue("consignorMoneyListSuccess", t.data)
                } else {
                    showToast(t.msg)
                    setValue("consignorMoneyListError")
                }
            }

            override fun onFail(e: HandleException) {
                showToast(e.msg)
                setValue("consignorMoneyListError")
            }
        })
    }

    /**
     * 产品介绍
     */
    fun findApplyStateList() {
        execute(ReqFindApplyState(), object : IResult<BaseRsp<RspFindApplyState>> {
            override fun onSuccess(t: BaseRsp<RspFindApplyState>) {
                if (t.success()) {
                    setValue("findApplyStateListSuccess", t.data)
                } else {
                    showDialogToast(t.msg)
                }
            }

            override fun onFail(e: HandleException) {
                showDialogToast(e.msg)
            }
        })
    }

    /**
     * 提款前查询资金方和提示信息
     */
    fun queryCapitalTypeAndMessage(req: ReqQueryCapitalTypeAndMessage) {
        execute(req, object : IResult<BaseRsp<RspQueryCapitalTypeAndMessage>> {
            override fun onSuccess(t: BaseRsp<RspQueryCapitalTypeAndMessage>) {
                if (t.success()) {
                    setValue("queryCapitalTypeAndMessageSuccess", t.data)
                } else {
                    when (t.data?.resultCode) {
                        "301" -> {
                            //当resultCode 为301时 需要去签署运费贷合同
                            setValue("queryCapitalTypeAndMessageSuccess", t.data)
                        }

                        else -> {
                            showDialogToast(t.msg)
                        }
                    }
                }
            }

            override fun onFail(e: HandleException) {
                showDialogToast(e.msg)
            }
        })
    }

    /**
     * 签署运费贷合同
     */
    fun submitSignatureContract(req: ReqSubmitSignatureContract) {
        execute(req, object : IResult<BaseRsp<RspSubmitSignatureContract>> {
            override fun onSuccess(t: BaseRsp<RspSubmitSignatureContract>) {
                if (t.success()) {
                    setValue("submitSignatureContractSuccess", t.data)
                } else {
                    showDialogToast(t.msg)
                }
            }

            override fun onFail(e: HandleException) {
                showDialogToast(e.msg)
            }
        })
    }
}