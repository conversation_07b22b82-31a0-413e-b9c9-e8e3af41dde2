package com.zczy.cargo_owner.freight.activity

import android.content.Context
import android.content.Intent
import android.os.Bundle
import android.view.View
import android.widget.LinearLayout
import com.sfh.lib.mvvm.service.BaseViewModel
import com.zczy.cargo_owner.R
import com.zczy.cargo_owner.freight.req.*
import com.zczy.comm.SpannableHepler
import com.zczy.comm.ui.BaseActivity
import com.zczy.comm.utils.toJson
import com.zczy.comm.utils.toJsonObject
import kotlinx.android.synthetic.main.freight_boc_state_activty.*

/**
 *  （交行）准入申请状态-融资申请状态
 *  user:孙飞虎
 */
class FreightBOCStateActivity : BaseActivity<BaseViewModel>() {

    companion object {
        @JvmStatic
        fun start(context: Context?, data: SapBankApplyStateDtos) {
            val intent = Intent(context, FreightBOCStateActivity::class.java)
            intent.putExtra("data", data.toJson())
            context?.startActivity(intent)
        }
    }

    override fun getLayout(): Int {
        return R.layout.freight_boc_state_activty
    }

    override fun bindView(bundle: Bundle?) {
    }


    override fun initData() {

        var json = intent.getStringExtra("data")
        var data = json.toJsonObject(SapBankApplyStateDtos::class.java)
//        applyBankState为1显示“立即申请”按钮；
//        applyBankState为2准入申请中，提示“您的准入申请正在处理中，请耐心等待结果!”；
//        applyBankState为3准入审核中，提示“您的准入申请浙商银行审核中，请耐心等待结果!”；
//        applyBankState为4准入拒绝，提示“您的准入申请审批未通过！可发起重新申请 。”，同时展示“重新申请”按钮；
//        applyBankState为5融资待申请，展示“授信申请”按钮；
//        applyBankState为6融资申请中，提示“您的授信正在申请中，如需继续完成申请，请点击访问浙商银行小程序!”；
//        applyBankState为7融资审核中，提示“您的授信申请浙商银行审核中，请耐心等待结果!”
//        applyBankState为8融资失败，提示“您的授信申请审批未通过！可发起重新申请 。”，同时展示“重新申请”按钮；
//        applyBankState为9融资成功，展示额度信息、额度到期日信息。

        iv_step.setImageResource(
            when (data?.applyBankState) {
                "1","2","3","4" -> R.drawable.icon_boc_state_1
                else -> R.drawable.icon_boc_state_2
            }
        )
        when(data?.applyBankState){
            "2" -> {
                iv.setImageResource(R.drawable.icon_boc_state1)
                tv_toast.text = "您的准申请正在处理中，请耐心等待结果！"
                tv_onclick.visibility = View.GONE
            }
            "3" -> {
                iv.setImageResource(R.drawable.icon_boc_state1)
                tv_toast.text =
                    SpannableHepler(" 您好！准入申请").append(SpannableHepler.Txt("交通银行审核中","#FF5E1D")).append("，请耐心等待结果!").builder()
                tv_onclick.visibility = View.GONE
            }
            "4" -> {
                iv.setImageResource(R.drawable.evalutate_no_information)
                tv_toast.text = "很抱歉，您暂未获得交通银行准入资格！"+data?.banklTimePoint+"后可重新申请"
                tv_onclick.visibility = View.VISIBLE
                tv_onclick.setOnClickListener {
                    FreightBOCAccessActivity.start(this@FreightBOCStateActivity)
                    finish()
                }
            }
            "5" -> {
                iv.setImageResource(R.drawable.enterprise_realname_success)
                tv_toast.text = "恭喜您！您的准入申请已通过，请完成融资签约！"
                tv_onclick.text = "去完成融资签约"
                tv_onclick.visibility = View.VISIBLE
                tv_onclick.setOnClickListener {
                    //TODO 点击“融资签约”按钮弹窗显示输入交行对公结算户账号
                }
            }
            "6" -> {
                iv.setImageResource(R.drawable.icon_boc_state1)
                tv_toast.text = "您的授信正在申请中，如需继续完成申请，请点击访问交通银行小程序!"
                tv_onclick.visibility = LinearLayout.GONE
            }
            "7" -> {
                iv.setImageResource(R.drawable.icon_boc_state1)
                SpannableHepler(" 您的授信申请").append(SpannableHepler.Txt("交通银行审核中","#FF5E1D")).append("，请耐心等待结果!").builder()
                tv_onclick.visibility = View.GONE
            }
            "8" -> {
                iv.setImageResource(R.drawable.evalutate_no_information)
                tv_toast.text = "您的授信申请审批未通过！可发起重新申请 。"
                tv_onclick.visibility = View.VISIBLE
                tv_onclick.setOnClickListener {
                    FreightBOCAccessActivity.start(this@FreightBOCStateActivity)
                    finish()
                }
            }
        }
    }

}