package com.zczy.cargo_owner.freight.adapter

import com.chad.library.adapter.base.BaseQuickAdapter
import com.chad.library.adapter.base.BaseViewHolder
import com.zczy.cargo_owner.R
import com.zczy.cargo_owner.freight.req.RspConsignorMoneyList
import com.zczy.cargo_owner.freight.req.RspQueryDrawMoneyList
import com.zczy.cargo_owner.freight.req.showLoadingLocation

/**
 *  desc: 放款管理
 *  user: 宋双朋
 *  time: 2025/7/16 14:20
 */
class FreightLoanManagementAdapter : BaseQuickAdapter<RspConsignorMoneyList, BaseViewHolder>(R.layout.freight_loan_management_item) {
    override fun convert(helper: BaseViewHolder, item: RspConsignorMoneyList) {
        // 设置申请提款编号
        helper.setText(R.id.tv_withdraw_number_value, item.applyReplyNo ?: "")
            // 设置运单号
            .setText(R.id.tv_waybill_number_value, item.orderId ?: "")
            // 设置发货运费(含税)
            .setText(R.id.tv_freight_amount_value, item.deliverMoney ?: "")
            // 设置结算金额
            .setText(R.id.tv_settlement_amount_value, item.settleMoney ?: "")
            // 设置放款金额
            .setText(R.id.tv_loan_amount_value, item.loanMoney ?: "")
            // 设置退款金额
            .setText(R.id.tv_refund_amount_value, item.loanBackMoney ?: "")
            // 设置优惠奖励(销售折让)
            .setText(R.id.tv_discount_reward_value, item.rebateMoney ?: "")
            // 设置结算用款金额
            .setText(R.id.tv_settlement_payment_amount_value, item.loanUseMoney)
            // 设置放款时间
            .setText(R.id.tv_loan_time_value, item.loanTime ?: "")
            // 设置退款状态
            .setText(R.id.tv_refund_status_value, item.loanBackState ?: "")
            // 设置退款时间
            .setText(R.id.tv_refund_time_value, item.loanBackTime)
            // 设置资金方
            .setText(R.id.tv_funder_value, item.capitalType ?: "")
    }
}