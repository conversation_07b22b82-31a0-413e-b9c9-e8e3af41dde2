package com.zczy.cargo_owner.freight.dialog

import android.content.Context
import android.graphics.Rect
import android.graphics.drawable.ColorDrawable
import android.os.Build
import android.view.LayoutInflater
import android.view.View
import android.view.ViewGroup
import android.widget.PopupWindow
import androidx.fragment.app.FragmentActivity
import com.zczy.cargo_owner.R
import com.zczy.cargo_owner.freight.req.FreightFilterData
import com.zczy.cargo_owner.libcomm.utils.DateUtils
import com.zczy.cargo_owner.order.confirm.bean.isNotNullOrNotEmpty
import com.zczy.comm.TimePickerUtilV1
import com.zczy.comm.utils.ex.getFormatTime
import com.zczy.comm.widget.inputv2.InputViewClick
import com.zczy.comm.widget.pickerview.picker.TimePicker
import kotlinx.android.synthetic.main.freight_with_draw_dialog_4.view.etLoanAmountEnd1
import kotlinx.android.synthetic.main.freight_with_draw_dialog_4.view.etLoanAmountEnd2
import kotlinx.android.synthetic.main.freight_with_draw_dialog_4.view.etLoanAmountEnd3
import kotlinx.android.synthetic.main.freight_with_draw_dialog_4.view.etLoanAmountStart1
import kotlinx.android.synthetic.main.freight_with_draw_dialog_4.view.etLoanAmountStart2
import kotlinx.android.synthetic.main.freight_with_draw_dialog_4.view.etLoanAmountStart3
import kotlinx.android.synthetic.main.freight_with_draw_dialog_4.view.inputView1
import kotlinx.android.synthetic.main.freight_with_draw_dialog_4.view.inputView2
import kotlinx.android.synthetic.main.freight_with_draw_dialog_4.view.inputView3
import kotlinx.android.synthetic.main.freight_with_draw_dialog_4.view.tvClear
import kotlinx.android.synthetic.main.freight_with_draw_dialog_4.view.tvSure

/**
 *  desc: 信息服务费-更多筛选
 *  user: 宋双朋
 *  time: 2025/7/16 15:59
 */
class FreightWithDrawDialog4(
    val mContext: Context?,
    var fData: FreightFilterData,
    var onSureBlock: (
        fData: FreightFilterData,
    ) -> Unit,
) : PopupWindow() {
    private var mItemView: View? = null

    init {
        mItemView = LayoutInflater.from(mContext).inflate(R.layout.freight_with_draw_dialog_4, null, false)
        contentView = mItemView
        mItemView?.setOnClickListener { dismiss() }
        val background = ColorDrawable()
        background.alpha = 100
        this.setBackgroundDrawable(background)
        this.isFocusable = true
        this.width = ViewGroup.LayoutParams.MATCH_PARENT
        this.height = ViewGroup.LayoutParams.MATCH_PARENT
        this.isOutsideTouchable = true
        initData()
        mItemView?.inputView3?.setListener(object : InputViewClick.Listener() {
            override fun onClick(viewId: Int, view: InputViewClick, content: String) {
                if (mContext is FragmentActivity) {
                    TimePickerUtilV1.showStartEnd(
                        context = mContext,
                        selectedDate = null,
                        timePickerType = TimePicker.TYPE_DATE
                    ) { date1, date2 ->
                        fData.time1 = date1.getFormatTime(DateUtils.dateFormatYMD3)
                        fData.time2 = date2.getFormatTime(DateUtils.dateFormatYMD3)
                        view.content = "${fData.time1}至${fData.time2}"
                    }
                }
            }

        })
        mItemView?.tvClear?.setOnClickListener {
            fData = FreightFilterData()
            initData()
            onSureBlock(fData)
        }
        mItemView?.tvSure?.setOnClickListener {
            fData.orderId = mItemView?.inputView1?.content
            fData.etLoanAmountStart = mItemView?.etLoanAmountStart1?.text.toString().trim()
            fData.etLoanAmountEnd = mItemView?.etLoanAmountEnd1?.text.toString().trim()
            fData.etLoanAmountStart1 = mItemView?.etLoanAmountStart2?.text.toString().trim()
            fData.etLoanAmountEnd1 = mItemView?.etLoanAmountEnd2?.text.toString().trim()
            fData.etLoanAmountStart2 = mItemView?.etLoanAmountStart3?.text.toString().trim()
            fData.etLoanAmountEnd2 = mItemView?.etLoanAmountEnd3?.text.toString().trim()
            onSureBlock(fData)
            dismiss()
        }
    }

    private fun initData() {
        mItemView?.inputView1?.content = fData.orderId ?: ""
        mItemView?.inputView2?.content = fData.settleApplyExtractNum ?: ""
        if (fData.time1.isNotNullOrNotEmpty() && fData.time2.isNotNullOrNotEmpty()) {
            mItemView?.inputView3?.content = "${fData.time1}至${fData.time2}"
        }
        mItemView?.etLoanAmountStart1?.setText(fData.etLoanAmountStart)
        mItemView?.etLoanAmountEnd1?.setText(fData.etLoanAmountEnd)
        mItemView?.etLoanAmountStart2?.setText(fData.etLoanAmountStart1)
        mItemView?.etLoanAmountEnd2?.setText(fData.etLoanAmountEnd1)
        mItemView?.etLoanAmountStart3?.setText(fData.etLoanAmountStart1)
        mItemView?.etLoanAmountEnd3?.setText(fData.etLoanAmountEnd1)
    }

    fun show(anchor: View) {
        if (Build.VERSION.SDK_INT >= 24) {
            val visibleFrame = Rect()
            anchor.getGlobalVisibleRect(visibleFrame)
            val h = anchor.resources.displayMetrics.heightPixels - visibleFrame.bottom
            super.setHeight(h)
        }
        super.showAsDropDown(anchor)
    }
}