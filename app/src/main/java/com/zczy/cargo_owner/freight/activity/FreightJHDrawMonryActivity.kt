package com.zczy.cargo_owner.freight.activity

import android.annotation.SuppressLint
import android.content.Context
import android.content.Intent
import android.os.Bundle
import android.text.TextUtils
import com.sfh.lib.mvvm.service.BaseViewModel
import com.sfh.lib.ui.dialog.DialogBuilder
import com.zczy.cargo_owner.R
import com.zczy.cargo_owner.freight.model.FreightWithDrawModel
import com.zczy.cargo_owner.freight.req.ReqToLoanOpenApply
import com.zczy.cargo_owner.freight.req.SapBankApplyStateDtos
import com.zczy.cargo_owner.libcomm.config.HttpConfig
import com.zczy.comm.ui.BaseActivity
import com.zczy.comm.utils.json.toJson
import com.zczy.comm.utils.json.toJsonObject
import com.zczy.comm.utils.setVisible
import com.zczy.comm.x5.X5WebActivity
import kotlinx.android.synthetic.main.freight_jh_draw_activty.*

/**
 *  desc: 到期提醒--交行
 *  user: zzf
 *  time: 2025/7/18 10:48
 */
class FreightJHDrawMonryActivity : BaseActivity<BaseViewModel>() {

    private val mData by lazy {
        intent.getStringExtra(EXTRA_DATA_JSON)?.toJsonObject(SapBankApplyStateDtos::class.java)
            ?: SapBankApplyStateDtos()
    }

    companion object {
        private const val EXTRA_DATA_JSON = "extra_data_json"

        @JvmStatic
        fun jumpPage(context: Context?, data: SapBankApplyStateDtos) {
            val intent = Intent(context, FreightJHDrawMonryActivity::class.java)
            intent.putExtra(EXTRA_DATA_JSON, data.toJson())
            context?.startActivity(intent)
        }
    }

    override fun getLayout(): Int {
        return R.layout.freight_jh_draw_activty
    }

    override fun bindView(bundle: Bundle?) {
        tv_apply.setOnClickListener {
            // 申请提款
            FreightWithDrawActivity.jumpPage(this@FreightJHDrawMonryActivity)
        }
        btnApply.setOnClickListener {
            // 准入申请
            if (!cbAgree.isChecked) {
                showToast("请先勾选协议")
                return@setOnClickListener
            }

            getViewModel(FreightWithDrawModel::class.java).execute(
                true,
                ReqToLoanOpenApply(capitalType = "3")
            ) {
                if (it.success()) {
                    FreightBOCAccessActivity.start(this@FreightJHDrawMonryActivity)
                } else {
                    val dialog = DialogBuilder()
                        .setHideCancel(true)
                        .setTitle("申请提示")
                        .setOkListener { dialogInterface, _ ->
                            dialogInterface.dismiss()
                        }
                        .setOKText("我知道了")
                        .setMessage(it.msg)
                    showDialog(dialog)
                }

            }

        }
        tvAgreement.setOnClickListener {
            //协议
            X5WebActivity.startNoTitleContentUI(this, HttpConfig.getWebUrl() + "form_h5/h5_mobile/index.html?_t=" + System.currentTimeMillis() + "#/infoService")
        }
    }


    @SuppressLint("SetTextI18n")
    override fun initData() {
        if (!TextUtils.isEmpty(mData.bankExpireBeforeMsg)) {
            // 到期前提示
            ll.setVisible(true)
            cl.setVisible(false)
            tv_money.text = mData.actualBankLeftMoney
            tv_else_money.text = "总额度 " + mData.totalBankMoney + "  已用额度 " + mData.totalBankUseMoney
            tv_tips1.text = mData.bankExpireBeforeMsg
        } else if (!TextUtils.isEmpty(mData.bankExpireAfterMsg)) {
            // 到期后提示
            ll.setVisible(false)
            cl.setVisible(true)
            tv_tips1.setVisible(false)
            tv_tips.text = mData.bankExpireAfterMsg
            tvRateValue.text = mData.bankRate
            tvCreditLimit.text = mData.creditQuotaDesc
            tvRepayType.text = mData.repaymentMethod
            tvLoanPeriod.text = mData.financingPeriod
        } else {
            ll.setVisible(true)
            cl.setVisible(false)
            tv_tips1.setVisible(false)
            tv_money.text = mData.actualBankLeftMoney
            tv_else_money.text = "总额度 " + mData.totalBankMoney + "  已用额度 " + mData.totalBankUseMoney
            tv_tips1.text = mData.bankExpireBeforeMsg
        }
    }


}