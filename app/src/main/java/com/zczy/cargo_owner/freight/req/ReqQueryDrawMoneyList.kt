package com.zczy.cargo_owner.freight.req

import com.zczy.comm.http.entity.BaseNewRequest
import com.zczy.comm.http.entity.BaseRsp
import com.zczy.comm.http.entity.PageList

/**
 *  desc: 申请提款-多渠道
 *  user: 宋双朋
 *  time: 2025/7/16 14:24
 */
class ReqQueryDrawMoneyList(
    var periodFlag: String? = null, // 历史记录年份 1 近三个月 其他年份传对应年份值
    var drawMoneySubmitType: String? = null, // 0 待申请 1 已申请
    var nowPage: Int = 1, // 当前页
    var pageSize: Int = 10, // 每页条数
    var orderId: String? = null, // 订单号
    var allCargoName: String? = null, // 货物名称
    var subsidiaryId: String? = null, // 结算主体
    var moneySource: String? = null, // 资金方1:工行贷;2:京东数科;3:交通银行;4:华润银行;5:浙商银行;6:华夏银行
    var signedType: String? = null, // 合同电签状态 0 未电签 1 已电签
    var settleApplyExtractState: String? = null, // 申请提款状态 1:已申请:2:已放款3:拒绝放款;4:放款中;5:放款失败
    var settleApplyTime: String? = null, // 申请日期 今天:1 最近7天:2 最近30天:3 最近90天:4 自定义时间：5
    var settleApplyTimeE: String? = null, // 申请时间结束
    var settleApplyTimeS: String? = null, // 申请时间开始
    var loanTime: String? = null, // 借款到期日
    var loanTimeE: String? = null, // 借款到期日 结束
    var loanTimeS: String? = null, // 借款到期日 开始
    var settleApplyExtractMoneyS: String? = null, // 申请提款金额 开始
    var settleApplyExtractMoneyE: String? = null // 申请提款金额 结束
) : BaseNewRequest<BaseRsp<QueryDrawMoneyPageList<RspQueryDrawMoneyList>>>("oms-app/consignor/drawMoney/queryDrawMoneyList")

class RspQueryDrawMoneyListV2(
    val jtMoney: String? = null, //交通银行可用额度
    val zsMoney: String? = null, //浙商银行可用额度
)

class RspQueryDrawMoneyList(
    val reachUnloadingLocation: String? = null, // 是否到达卸货地 1：到达 0:没到达
    val settleApplyExtractState: String? = null, // 提款申请状态 1:已申请:2:已放款3:拒绝放款;4:放款中;5:放款失败
    val settleApplyExtractStateStr: String? = null, // 提款申请状态 中文
    val estimateReceiveTime: String? = null, // 预计到达时间
    val signedStr: String? = null, // 合同电签状态
    val moneySource: String? = null, // 资金方1:工行贷;2:京东数科;3:交通银行;4:华润银行;5:浙商银行;6:华夏银行
    val moneySourceStr: String? = null, // 资金方 中文
    val orderCurrentState: String? = null, // 运单状态
    val startTime: String? = null, // 确认发货时间
    val deliverMoney: String? = null, // 发货运费
    val allCargoName: String? = null, // 货物名称
    val orderId: String? = null, // 运单号
    val settleApplyExtractNum: String? = null, // 申请提款编号
    val settleApplyExtractMoney: String? = null, // 申请提款金额
    val consignorState: String? = null, // 5 摘单  6 确认发货
    val pbConsignorMoney: String? = null, // 发单金额
    val settleApplyTime: String? = null, // 申请日期
    val loanEndTime: String? = null, // 借款到期日
)

class QueryDrawMoneyPageList<T>(
    val amsInfo: RspQueryDrawMoneyListV2? = null,//银行信息
    val showAlertFlag: String? = null,// 是否弹框提示
    val showAlertMsg: String? = null, // 提示话
) : PageList<T>()

fun RspQueryDrawMoneyList.showLoadingLocation(): String {
    return when (reachUnloadingLocation) {
        "1" -> {
            "到达"
        }

        "2" -> {
            "未到达"
        }

        else -> {
            ""
        }
    }
}

class FreightFilterData(
    var orderId: String? = null,
    var settleApplyExtractNum: String? = null,
    var time1: String? = null,
    var time2: String? = null,
    var time3: String? = null,
    var time4: String? = null,
    var etLoanAmountStart: String? = null,
    var etLoanAmountEnd: String? = null,
    var etLoanAmountStart1: String? = null,
    var etLoanAmountEnd1: String? = null,
    var etLoanAmountStart2: String? = null,
    var etLoanAmountEnd2: String? = null,
    var mYearEnum: YearEnum? = null,
    var mContractSigningEnum: ContractSigningEnum? = null,
    var mLoanStatusEnum: LoanStatusEnum? = null,
    var mFundingPartyEnum: FundingPartyEnum? = null, // 资金方
    var mRspQueryConsignorSub: RspQueryConsignorSub? = null, // 结算主体
)