package com.zczy.cargo_owner.freight.dialog

import android.annotation.SuppressLint
import android.content.Context
import android.os.Bundle
import android.view.View
import com.zczy.cargo_owner.R
import com.zczy.cargo_owner.freight.req.RspQueryCapitalTypeAndMessage
import com.zczy.cargo_owner.order.confirm.bean.isNotNullOrNotEmpty
import com.zczy.comm.ui.BaseDialog
import kotlinx.android.synthetic.main.freight_sign_dialog_1.signView2
import kotlinx.android.synthetic.main.freight_sign_dialog_1.signView3
import kotlinx.android.synthetic.main.freight_sign_dialog_1.signView4
import kotlinx.android.synthetic.main.freight_sign_dialog_1.signView5
import kotlinx.android.synthetic.main.freight_sign_dialog_1.signView6

/**
 *  desc: 签署运费贷信息技术服务协议
 *  user: 宋双朋
 *  time: 2025/7/16 15:59
 */
class FreightSignDialog1(
    val mContext: Context?,
    var fData: RspQueryCapitalTypeAndMessage,
    var onSureBlock: () -> Unit,
) : BaseDialog() {

    @SuppressLint("SetTextI18n")
    override fun bindView(view: View, bundle: Bundle?) {
        signView5.setOnClickListener {
            //重新发起
            onSureBlock()
            dismiss()
        }
        signView6.setOnClickListener {
            //我知道了
            dismiss()
        }
        if (fData.signCustomerMobile.isNotNullOrNotEmpty() && fData.signCustomerUserName.isNotNullOrNotEmpty()) {
            signView2.text = "已指定签署人：${fData.signCustomerMobile} ${fData.signCustomerUserName}"
        } else {
            signView2.text = "已指定签署人："
        }
        signView3.text = when (fData.customerSignState) {
            "0" -> {
                "电签认证状态：未认证"
            }

            "1" -> {
                "电签认证状态：已认证"
            }

            else -> {
                "电签认证状态："
            }
        }
        signView4.text = when (fData.customerSignState) {
            "0" -> {
                "协议签署状态：未签署"
            }

            "1" -> {
                "协议签署状态：已签署"
            }

            else -> {
                "协议签署状态："
            }
        }
        when (fData.resultCode) {
            "301" -> {
            }

            "302" -> {
            }
        }
    }

    override fun getDialogTag(): String {
        return "FreightSignDialog1"
    }

    override fun getDialogLayout(): Int {
        return R.layout.freight_sign_dialog_1
    }

}