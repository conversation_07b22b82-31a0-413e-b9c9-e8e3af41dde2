package com.zczy.cargo_owner.transport

import android.content.Context
import android.content.Intent
import android.os.Bundle
import com.sfh.lib.mvvm.annotation.LiveDataMatch
import com.zczy.cargo_owner.R
import com.zczy.cargo_owner.transport.adapter.TransportPlanUpdateAdapter
import com.zczy.cargo_owner.transport.model.TransportPlanModel
import com.zczy.cargo_owner.transport.req.RspTransportPlanUpdate
import com.zczy.comm.http.entity.PageList
import com.zczy.comm.ui.BaseActivity
import com.zczy.comm.utils.dp2px
import com.zczy.comm.widget.pulltorefresh.CommEmptyView
import kotlinx.android.synthetic.main.transport_plan_activity.*

/**
 * 功能描述: 更新记录
 * <AUTHOR>
 * @date 2021/10/21-11:06
 */

class TransportPlanUpdateActivity : BaseActivity<TransportPlanModel>() {

    private val mAdapter = TransportPlanUpdateAdapter()
    private val planId by lazy { intent.getStringExtra(PLAN_ID) }

    companion object {

        private const val PLAN_ID = "outOrderNumber"

        @JvmStatic
        fun jumpPage(context: Context, planId: String) {
            val intent = Intent(context, TransportPlanUpdateActivity::class.java)
            intent.putExtra(PLAN_ID, planId)
            context.startActivity(intent)
        }
    }

    override fun getLayout(): Int {
        return R.layout.transport_plan_update_activity
    }

    override fun bindView(bundle: Bundle?) {
        swipe_refresh.apply {
            addItemDecorationSize(dp2px(7f))
            setAdapter(mAdapter, true)
            setEmptyView(CommEmptyView.creatorDef(context))
            setOnLoadListener2 { viewModel?.queryUpdateList(outOrderNumber = planId?:"") }
            onAutoRefresh()
        }
    }

    override fun initData() {

    }

    @LiveDataMatch(tag = "列表接口返回")
    open fun onPageListSuccess(data: PageList<RspTransportPlanUpdate>?) {
        swipe_refresh.onRefreshCompale(data)
    }

    @LiveDataMatch(tag = "列表接口返回")
    open fun onPageListError(msg: String?) {
        swipe_refresh.onLoadMoreFail()
        showToast(msg)
    }
}