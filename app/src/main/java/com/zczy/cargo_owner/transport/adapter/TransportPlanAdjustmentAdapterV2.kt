package com.zczy.cargo_owner.transport.adapter

import android.annotation.SuppressLint
import android.text.TextUtils
import android.widget.ImageView
import com.chad.library.adapter.base.BaseQuickAdapter
import com.chad.library.adapter.base.BaseViewHolder
import com.zczy.cargo_owner.R

/**
 * 功能描述: 运输计划详情
 * <AUTHOR>
 * @date 2021/10/25-13:26
 */

class TransportPlanAdjustmentAdapterV2 :
    BaseQuickAdapter<String, BaseViewHolder>(R.layout.transport_plan_adjustment_item_v2) {
    var checked = String()
    override fun convert(helper: BaseViewHolder?, item: String) {

        helper?.apply {
            setText(R.id.tvOrderId, item)
            addOnClickListener(R.id.ivCheck)
            helper.getView<ImageView>(R.id.ivCheck).isSelected =
                TextUtils.equals(item, checked)
        }
    }


    @SuppressLint("NotifyDataSetChanged")
    fun setChecked(position: Int) {
        checked = mData[position]
        notifyItemChanged(position, checked)
    }

    fun getCheckedData(): String {
        return checked
    }

    fun clear() {
        checked = String()
    }
}