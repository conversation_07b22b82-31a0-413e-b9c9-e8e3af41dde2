package com.zczy.cargo_owner.transport

import android.annotation.SuppressLint
import android.content.Context
import android.content.Intent
import android.os.Bundle
import androidx.recyclerview.widget.LinearLayoutManager
import com.sfh.lib.event.RxBusEvent
import com.sfh.lib.mvvm.annotation.LiveDataMatch
import com.zczy.cargo_owner.R
import com.zczy.cargo_owner.order.detail.WaybillDetailActivity
import com.zczy.cargo_owner.transport.adapter.TransportPlanDetailAdapter
import com.zczy.cargo_owner.transport.model.TransportPlanModel
import com.zczy.cargo_owner.transport.req.*
import com.zczy.comm.ui.BaseActivity
import com.zczy.comm.utils.dp2px
import com.zczy.comm.utils.ex.setVisible
import com.zczy.comm.widget.itemdecoration.SpaceItemDecoration
import kotlinx.android.synthetic.main.transport_plan_detail_activity.*

/**
 * 功能描述: 运输计划详情
 * <AUTHOR>
 * @date 2021/10/21-11:06
 */

class TransportPlanDetailActivity : BaseActivity<TransportPlanModel>() {

    private val mAdapter = TransportPlanDetailAdapter()
    private val planId by lazy { intent.getStringExtra(PLAN_ID) }

    companion object {

        private const val PLAN_ID = "outOrderNumber"

        @JvmStatic
        fun jumpPage(context: Context, planId: String) {
            val intent = Intent(context, TransportPlanDetailActivity::class.java)
            intent.putExtra(PLAN_ID, planId)
            context.startActivity(intent)
        }
    }

    override fun getLayout(): Int {
        return R.layout.transport_plan_detail_activity
    }

    override fun bindView(bundle: Bundle?) {
        swipe_refresh.apply {
            addItemDecoration(SpaceItemDecoration(dp2px(2f)))
            layoutManager =
                androidx.recyclerview.widget.LinearLayoutManager(context)
            adapter = mAdapter
        }
        mAdapter.apply {
            bindToRecyclerView(swipe_refresh)
            setOnItemChildClickListener { adapter, view, position ->
                when (view.id) {
                    R.id.tvOrderIdV1 -> {
                        val item = adapter.getItem(position) as RspTransportDetailV1
                        WaybillDetailActivity.start(this@TransportPlanDetailActivity, item.orderId)
                    }
                }
            }
        }
        tvUpdateList.setOnClickListener {
            TransportPlanUpdateActivity.jumpPage(this@TransportPlanDetailActivity, planId?:"")
        }
        adjustment.setOnClickListener {
            TransportPlanAdjustmentActivity.jumpPage(
                this@TransportPlanDetailActivity,
                planId = planId?:""
            )
        }
    }

    override fun initData() {
        viewModel?.queryDetail(outOrderNumber = planId?:"")
    }

    @SuppressLint("SetTextI18n")
    @LiveDataMatch(tag = "运输计划详情")
    open fun onDetailSuccess(data: RspTransportDetail?) {
        data?.apply {
            tv_code.text = outOrderNumber
            if (despatchAddress.isNullOrEmpty() || deliverAddress.isNullOrEmpty()) {
                iv_arrow.setVisible(false)
            } else {
                iv_arrow.setVisible(true)
            }
            tv_start_address.text = despatchAddress ?: ""
            tv_end_address.text = deliverAddress ?: ""
            tv_company.text = company
            tv_cargo_name_v1.text = cargoName
            tv_plan_num_v1.text = weightV1() + unitV1()
            tvDismantleNum_v1.text = splitWeightV1() + unitV1()
            ivStatus.setBackgroundResource(
                when (orderState) {
                    "1" -> {
                        adjustment.setVisible(true)
                        R.drawable.trans_port_effective
                    }
                    "2" -> {
                        adjustment.setVisible(false)
                        R.drawable.transport_plan_completed
                    }
                    "3" -> {
                        adjustment.setVisible(false)
                        R.drawable.transport_plan_delete
                    }
                    else -> {
                        adjustment.setVisible(true)
                        R.color.transparent
                    }
                }
            )
            mAdapter.setNewData(rootArray)
        }
    }

    @RxBusEvent(from = "调整运单关系成功")
    open fun onUpdateSuccess(data: RxBusTransportEvent) {
        if (data.success) {
            viewModel?.queryDetail(planId?:"")
        }
    }
}