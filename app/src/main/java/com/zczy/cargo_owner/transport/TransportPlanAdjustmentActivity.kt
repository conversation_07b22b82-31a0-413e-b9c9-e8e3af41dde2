package com.zczy.cargo_owner.transport

import android.content.Context
import android.content.Intent
import android.os.Bundle
import com.sfh.lib.event.RxBusEventManager
import com.sfh.lib.mvvm.annotation.LiveDataMatch
import com.zczy.cargo_owner.R
import com.zczy.cargo_owner.transport.model.TransportPlanModel
import com.zczy.cargo_owner.transport.req.*
import com.zczy.cargo_owner.transport.widget.TransportPlanAdjustViewV1
import com.zczy.cargo_owner.transport.widget.TransportPlanAdjustViewV2
import com.zczy.comm.http.entity.PageList
import com.zczy.comm.ui.BaseActivity
import com.zczy.comm.utils.ex.setVisible
import kotlinx.android.synthetic.main.transport_plan_adjustment_activity.*

/**
 * 功能描述: 运输计划详情
 * <AUTHOR>
 * @date 2021/10/21-11:06
 */

class TransportPlanAdjustmentActivity : BaseActivity<TransportPlanModel>() {

    private val planId by lazy { intent.getStringExtra(PLAN_ID) }

    companion object {

        private const val PLAN_ID = "outOrderNumber"

        @JvmStatic
        fun jumpPage(context: Context, planId: String) {
            val intent = Intent(context, TransportPlanAdjustmentActivity::class.java)
            intent.putExtra(PLAN_ID, planId)
            context.startActivity(intent)
        }
    }

    override fun getLayout(): Int {
        return R.layout.transport_plan_adjustment_activity
    }

    override fun bindView(bundle: Bundle?) {
        tpV1.setListener(object : TransportPlanAdjustViewV1.Listener {
            override fun onRight(data: MutableList<RspTransportAdjustV1>) {
                //下一步
                tpV1.setVisible(false)
                clSearch.setVisible(false)
                tpV2.setVisible(true)
                val reqTransportPlanAdjustV2 = ReqTransportPlanAdjustV2(outOrderNumber = planId?:"")
                viewModel?.queryAdjustListV2(reqTransportPlanAdjustV2)
            }

        })
        tpV2.setListener(object : TransportPlanAdjustViewV2.Listener {
            override fun onLeft() {
                //上一步
                tpV1.setVisible(true)
                clSearch.setVisible(true)
                tpV2.setVisible(false)
            }

            override fun onSure(bean: String) {
                //确定调整
                val reqTransportPlanUpdateOrderRelations = ReqTransportPlanUpdateOrderRelations()
                reqTransportPlanUpdateOrderRelations.outOrderNumber = bean
                reqTransportPlanUpdateOrderRelations.orginOutOrderNumber = planId?:""
                val orderIds = StringBuilder()
                val data = tpV1.getData();
                data.forEachIndexed { index, rspTransportAdjustV1 ->
                    if (index == data.size - 1) {
                        orderIds.append(rspTransportAdjustV1.orderId)
                    } else {
                        orderIds.append(rspTransportAdjustV1.orderId).append(",")
                    }
                }
                reqTransportPlanUpdateOrderRelations.orderIds = orderIds.toString()
                viewModel?.updateOrderRelations(reqTransportPlanUpdateOrderRelations)
            }
        })
        btn_search.setOnClickListener {
            val text = search_title.text.toString().trim()
            tpV1.filterData(text)
        }
    }

    override fun initData() {
        viewModel?.queryAdjustListV1(req = ReqTransportPlanAdjustV1(outOrderNumber = planId?:""))
        tpV1.setData(planId?:"")
    }


    @LiveDataMatch(tag = "获取第三方编号下的运单")
    open fun onPageListV2Success(data: RspTransportAdjustV2?) {
        tpV2.setData(data?.outOrderNumbers)
    }

    @LiveDataMatch(tag = "获取第三方编号列表")
    open fun onPageListV1Success(data: PageList<RspTransportAdjustV1>?) {
        tpV1.setData(data)
    }

    @LiveDataMatch(tag = "确定调整操作")
    open fun onUpdateSuccess() {
        RxBusEventManager.postEvent(RxBusTransportEvent(success = true))
        finish()
    }

}