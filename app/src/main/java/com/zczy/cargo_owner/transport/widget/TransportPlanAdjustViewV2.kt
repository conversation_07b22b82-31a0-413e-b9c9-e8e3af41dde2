package com.zczy.cargo_owner.transport.widget

import android.content.Context
import androidx.constraintlayout.widget.ConstraintLayout
import androidx.recyclerview.widget.LinearLayoutManager
import android.util.AttributeSet
import android.view.LayoutInflater
import com.zczy.cargo_owner.R
import com.zczy.cargo_owner.transport.adapter.TransportPlanAdjustmentAdapterV2
import com.zczy.cargo_owner.transport.req.RspTransportAdjustV2
import com.zczy.comm.utils.dp2px
import com.zczy.comm.widget.itemdecoration.SpaceItemDecoration
import kotlinx.android.synthetic.main.transport_plan_adjustment_view_v1.view.*

class TransportPlanAdjustViewV2 :
    ConstraintLayout {

    private val mAdapter = TransportPlanAdjustmentAdapterV2()
    private var listener: Listener? = null

    constructor(context: Context) : super(context)

    constructor(context: Context, attrs: AttributeSet?) : super(context, attrs)

    constructor(context: Context, attrs: AttributeSet?, defStyleAttr: Int) : super(
        context,
        attrs,
        defStyleAttr
    )

    init {
        LayoutInflater.from(context).inflate(R.layout.transport_plan_adjustment_view_v2, this)
        initView()
    }

    private fun initView() {
        swipe_refresh.apply {
            addItemDecoration(SpaceItemDecoration(dp2px(2f)))
            layoutManager =
                androidx.recyclerview.widget.LinearLayoutManager(context)
            adapter = mAdapter
        }
        mAdapter.apply {
            bindToRecyclerView(swipe_refresh)
            setOnItemChildClickListener { _, _, position ->
                mAdapter.setChecked(
                    position
                )
            }
        }
        tvLeft.setOnClickListener {
            //上一步
            clear()
            listener?.onLeft()
        }
        tvRight.setOnClickListener {
            // 确定调整
            listener?.onSure(mAdapter.getCheckedData())
        }
    }

    private fun clear() {
        mAdapter.clear()
    }

    fun setData(data: MutableList<String>?) {
        data?.let {
            mAdapter.setNewData(it)
        }
    }

    fun setListener(listener: Listener) {
        this.listener = listener
    }

    interface Listener {
        fun onLeft()//上一步
        fun onSure(planId: String)//确定调整
    }
}