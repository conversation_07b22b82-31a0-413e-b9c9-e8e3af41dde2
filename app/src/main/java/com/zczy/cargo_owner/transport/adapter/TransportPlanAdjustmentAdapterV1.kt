package com.zczy.cargo_owner.transport.adapter

import android.annotation.SuppressLint
import android.text.TextUtils
import android.widget.ImageView
import com.chad.library.adapter.base.BaseQuickAdapter
import com.chad.library.adapter.base.BaseViewHolder
import com.zczy.cargo_owner.R
import com.zczy.cargo_owner.transport.req.RspTransportAdjustV1

/**
 * 功能描述: 运输计划详情
 * <AUTHOR>
 * @date 2021/10/25-13:26
 */

class TransportPlanAdjustmentAdapterV1 :
    BaseQuickAdapter<RspTransportAdjustV1, BaseViewHolder>(R.layout.transport_plan_adjustment_item_v1) {

    val checkedData: MutableList<RspTransportAdjustV1> = mutableListOf()
    override fun convert(helper: BaseViewHolder?, item: RspTransportAdjustV1) {

        helper?.apply {
            setText(R.id.tvOrderIdV1, item.orderId)
            setText(R.id.tvPickOrderV1, item.delistTime)
            setText(R.id.tvPhoneV1, item.driverMobile)
            setText(R.id.tvLicensePlateV1, item.plateNumber)
            setText(R.id.tvDeliverGoodsTimeV1, item.startTime)
            setText(R.id.tvReceivingTimeV1, item.endTime)
            val find = checkedData.contains(item)
            helper.getView<ImageView>(R.id.ivCheck).isSelected = find
            addOnClickListener(R.id.ivCheck)
            addOnClickListener(R.id.tvOrderIdV1)
        }
    }


    @SuppressLint("NotifyDataSetChanged")
    fun setChecked(position: Int) {
        val item = mData[position]
        val contains = checkedData.contains(item)
        if (contains) {
            checkedData.remove(item)
        } else {
            checkedData.add(item)
        }
        notifyItemChanged(position, item)
    }

    @SuppressLint("NotifyDataSetChanged")
    fun checkedAll() {
        checkedData.addAll(mData.filter { ob1 ->
            checkedData.find { ob2 ->
                ob1.orderId == ob2.orderId
            } == null
        })
        notifyDataSetChanged()
    }

    @SuppressLint("NotifyDataSetChanged")
    fun unCheckedAll() {
        //全反选
        checkedData.clear()
        notifyDataSetChanged()
    }

    @JvmName("getCheckedData1")
    fun getCheckedData(): MutableList<RspTransportAdjustV1> {
        return checkedData
    }
}