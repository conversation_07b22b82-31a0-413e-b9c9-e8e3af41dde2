package com.zczy.cargo_owner.transport.req

import android.text.TextUtils
import com.zczy.comm.http.entity.BaseNewRequest
import com.zczy.comm.http.entity.BaseRsp
import com.zczy.comm.http.entity.PageList
import com.zczy.comm.http.entity.ResultData

/**
 * 功能描述: 运输计划
 * <AUTHOR>
 * @date 2021/10/26-16:22
 */

class ReqTransportPlan(
    var searchWord: String = "",
    var nowPage: Int = 1,
    var pageSize: Int = 10
) : BaseNewRequest<BaseRsp<PageList<RspTransportPlan>>>("oms-app/plan/youran/youranOrderPlanList")

class RspTransportPlan : ResultData() {
    val outOrderNumber: String? = null  //第三方编号
    val orderState: String? = null  //订单状态 1有效 2完成 3删除
    val consignorUserId: String? = null  //货主id
    val consignorChildId: String? = null  //货主子账号
    val title: String? = null  //标题
    val allCargoName: String? = null  //货物名称
    val weight: String? = null  //总重
    val cargoCategory: String? = null  //货物类别：1：重货，2：泡货
    val splitWeight: String? = null  //已拆重量
    val orderNum: String? = null  //    运单数量
    val onLoadWeight: String? = null  //   在途运输量val  cargoCategory	货物类别：1：重货，2：泡货	Integer
    val despatchCompanyName: String? = null//起运地
    val deliverCompanyName: String? = null//目的地址
}

fun RspTransportPlan.weightV1(): String {
    return if (weight.isNullOrEmpty()) {
        "--"
    } else {
        weight
    }
}

fun RspTransportPlan.splitWeightV1(): String {
    return if (splitWeight.isNullOrEmpty()) {
        "--"
    } else {
        splitWeight
    }
}

fun RspTransportPlan.orderNumV1(): String {
    return if (orderNum.isNullOrEmpty()) {
        "--"
    } else {
        orderNum
    }
}

fun RspTransportPlan.onLoadWeightV1(): String {
    return if (onLoadWeight.isNullOrEmpty()) {
        "--"
    } else {
        onLoadWeight
    }
}

fun RspTransportPlan.unitV1(): String {
    return if (TextUtils.equals("2", cargoCategory)) {
        "方"
    } else {
        "吨"
    }
}