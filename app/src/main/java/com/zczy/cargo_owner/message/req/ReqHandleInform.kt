package com.zczy.cargo_owner.message.req

import com.zczy.cargo_owner.message.rsp.RspHandleInform
import com.zczy.comm.http.entity.BaseNewRequest
import com.zczy.comm.http.entity.BaseRsp


/**
 * PS: 好友申请同意和拒绝,车老板司机双向关联消息(合并)
 * Created by sdx on 2018/11/26.
 */
data class ReqHandleInform(
        var informId: String, // 消息ID 是否可空  否
        var dealState: String, // 处理状态(1:同意，2:拒绝) 否
        var businessId: String = "", // 业务ID 是否可空 是
        var checkedCYR: String = "", // 承运人是否勾选电子合同(0:未勾选,1:勾选) 是否可空 是
        var operateType: String // 操作类型(0:好友消息处理，1:车老板司机关联消息处理) 是否可空 否
) : BaseNewRequest<BaseRsp<RspHandleInform>>("mms-app/tips/handleInform")
