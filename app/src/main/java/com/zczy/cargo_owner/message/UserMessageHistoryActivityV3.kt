package com.zczy.cargo_owner.message

import android.app.Activity
import android.content.Intent
import android.os.Bundle
import android.text.TextUtils
import android.view.View
import android.widget.ImageView
import android.widget.TextView
import com.sfh.lib.mvvm.annotation.LiveDataMatch
import com.sfh.lib.ui.dialog.DialogBuilder
import com.zczy.cargo_owner.R
import com.zczy.cargo_owner.message.adapter.UserMessageListAdapterV3
import com.zczy.cargo_owner.message.adapter.UserMessageListListenerV3
import com.zczy.cargo_owner.message.model.UserMessageHistoryModelV3
import com.zczy.cargo_owner.message.model.UserMessageType
import com.zczy.cargo_owner.message.req.ReqBatchDelete
import com.zczy.cargo_owner.message.req.ReqHandleInform
import com.zczy.cargo_owner.message.req.RspTipsInfoNew
import com.zczy.comm.http.entity.PageList
import com.zczy.comm.http.entity.ResultData
import com.zczy.comm.ui.BaseActivity
import com.zczy.comm.utils.dp2px
import com.zczy.comm.utils.ex.YYYY_MM
import com.zczy.comm.utils.ex.setVisible
import com.zczy.comm.utils.ex.toCalendar
import com.zczy.comm.widget.pickerview.TimePickerUtil
import com.zczy.comm.widget.pulltorefresh.CommEmptyView
import com.zczy.comm.widget.pulltorefresh.SwipeRefreshMoreLayout
import kotlinx.android.synthetic.main.user_message_history_activity_v3.*
import java.util.*

/**
 * PS: 个人中心 消息 消息历史 页面
 * Created by sdx on 2018/11/7.
 */
open class UserMessageHistoryActivityV3 : BaseActivity<UserMessageHistoryModelV3>() {

    private val img_back by lazy { findViewById<ImageView>(R.id.img_back) }
    private val view_month by lazy { findViewById<View>(R.id.view_month) }
    private val tv_month by lazy { findViewById<TextView>(R.id.tv_month) }
    private val tv_all_read by lazy { findViewById<TextView>(R.id.tv_all_read) }
    private val swipe_refresh_message_history by lazy { findViewById<SwipeRefreshMoreLayout>(R.id.swipe_refresh_message_history) }

    private val type: UserMessageType by lazy {
        val intExtra = intent.getIntExtra(EXTRA_MESSAGE_TYPE_INT, UserMessageType.系统消息.type)
        UserMessageType.get(intExtra)
    }

    private val adapter = UserMessageListAdapterV3()
    private var edit: Boolean = true

    override fun getLayout(): Int = R.layout.user_message_history_activity_v3

    override fun bindView(bundle: Bundle?) {
        bindClickEvent(img_back)
        bindClickEvent(view_month)
        bindClickEvent(tv_all_read)
        bindClickEvent(tv_edit)
        bindClickEvent(tv_delete)
        bindClickEvent(tv_select_all)
        tv_all_read.setVisible(false)

        initList()
    }

    override fun initData() {
        val instance = Calendar.getInstance().also {
            it.add(Calendar.MONTH, -1)
        }
        val year = instance.get(Calendar.YEAR)
        val month = instance.get(Calendar.MONTH) + 1
        tv_month.text = "$year-$month"
        swipe_refresh_message_history.onAutoRefresh()
    }

    private fun initList() {
        swipe_refresh_message_history.run {
            setAdapter(adapter, true)
            addItemDecorationSize(dp2px(8F))
            setEmptyView(CommEmptyView.creatorDef(this@UserMessageHistoryActivityV3))
            addOnItemListener(UserMessageListListenerV3 { item, allow ->
                if (allow) {
                    val dialogBuilder = DialogBuilder()
                    dialogBuilder.message = "确认接受该用户的好友请求吗？"
                    dialogBuilder.okListener = DialogBuilder.DialogInterface.OnClickListener { p0, _ ->
                        p0.dismiss()
                        val req = ReqHandleInform(
                            informId = item.messageId,
                            dealState = "1",
                            operateType = "0"
                        )
                        viewModel?.handleInform(req)
                    }
                    showDialog(dialogBuilder)
                } else {
                    val dialogBuilder = DialogBuilder()
                    dialogBuilder.message = "确认拒绝该用户的好友请求吗？"
                    dialogBuilder.okListener = DialogBuilder.DialogInterface.OnClickListener { p0, _ ->
                        p0.dismiss()
                        val req = ReqHandleInform(
                            informId = item.messageId,
                            dealState = "2",
                            operateType = "0"
                        )
                        viewModel?.handleInform(req)
                    }
                    showDialog(dialogBuilder)
                }
            })
            setOnLoadListener2 {
                val selectTime = tv_month.text.toString()
                    .toCalendar(YYYY_MM)
                    ?: Calendar.getInstance().also { cal ->
                        cal.add(Calendar.MONTH, -1)
                    }
                val year = selectTime.get(Calendar.YEAR)
                val month = selectTime.get(Calendar.MONTH) + 1
                viewModel?.tipsHistoryInfo(it, type, year.toString(), month = month.toString())

            }
        }
    }

    override fun onSingleClick(v: View) {
        super.onSingleClick(v)
        when (v.id) {
            // 返回
            R.id.img_back -> {
                finish()
            }
            // title 月份 筛选
            R.id.view_month -> {
                val selectTime = tv_month.text.toString().toCalendar(YYYY_MM)
                    ?: Calendar.getInstance().also {
                        it.add(Calendar.MONTH, -1)
                    }
                TimePickerUtil.showMonth(this, "筛选信息时间", selectTime.timeInMillis) { year, month ->
                    tv_month.text = "$year-$month"
                    swipe_refresh_message_history.onAutoRefresh()
                }
            }
            // 全部已读
            R.id.tv_all_read -> {
                val dialogBuilder = DialogBuilder()
                dialogBuilder.message = "确认标记全部消息为已读吗？"
                dialogBuilder.okListener = DialogBuilder.DialogInterface.OnClickListener { p0, _ ->
                    p0.dismiss()
                    val selectTime = tv_month.text.toString().toCalendar(YYYY_MM)
                        ?: Calendar.getInstance().also {
                            it.add(Calendar.MONTH, -1)
                        }
                    val year = selectTime.get(Calendar.YEAR)
                    val month = selectTime.get(Calendar.MONTH) + 1
                    viewModel?.readTipsByType(type, year.toString(), month.toString())
                }
                showDialog(dialogBuilder)
            }

            R.id.tv_edit -> {
                if (edit) {
                    tv_edit.text = "取消"
                    cl_bottom.visibility = View.VISIBLE
                    adapter.data.forEach {
                        it.canEdit = true
                    }
                    adapter.notifyDataSetChanged()
                } else {
                    tv_edit.text = "编辑"
                    cl_bottom.visibility = View.GONE
                    adapter.data.forEach {
                        it.canEdit = false
                        it.isSelected = false
                    }
                    adapter.notifyDataSetChanged()
                }
                edit = !edit
            }
            R.id.tv_delete -> {
                var informIds = ""
                adapter.data.forEach {
                    if (it.isSelected) {
                        informIds += "${it.messageId},"
                    }
                }
                if (TextUtils.isEmpty(informIds)) {
                    showToast("请选择要删除的消息")
                    return
                }
                val req = ReqBatchDelete(
                    operateType = "2",
                    informIds = informIds
                )
                val dialogBuilder = DialogBuilder()
                dialogBuilder.message = "确定删除消息吗？"
                dialogBuilder.okListener = DialogBuilder.DialogInterface.OnClickListener { p0, _ ->
                    p0.dismiss()
                    viewModel?.batchDelete(req)
                }
                showDialog(dialogBuilder)
            }
            R.id.tv_select_all -> {
                adapter.data.forEach {
                    it.isSelected = true
                }
                adapter.notifyDataSetChanged()
            }
        }
    }

    @LiveDataMatch
    open fun onTipsHistoryInfo(data: PageList<RspTipsInfoNew>?) {
        swipe_refresh_message_history.onRefreshCompale(data)
        tv_all_read.setVisible(adapter.data.isNotEmpty())
    }

    @LiveDataMatch
    open fun onReadTipsByTypeSuccess() {
        setResult(Activity.RESULT_OK)
        swipe_refresh_message_history.onAutoRefresh()
    }

    @LiveDataMatch
    open fun onHandleInformSuccess(req: ReqHandleInform) {
        adapter.data
            .find {
                it.messageId == req.informId
            }
            ?.run {
                messageStatus = "1"
                reviewStatus = req.dealState
            }
        adapter.notifyDataSetChanged()
    }

    @LiveDataMatch
    open fun onHandleBatchDeletSeuccess(rsp: ResultData) {
        if (rsp.success()) {
            showToast(rsp.resultMsg)
            adapter.notifyDataSetChanged()
        } else {
            showDialogToast(rsp.resultMsg)
        }
    }


    companion object {
        private const val EXTRA_MESSAGE_TYPE_INT = "extra_message_type_int"

        @JvmStatic
        fun start(activity: Activity?, type: UserMessageType) {
            activity ?: return
            val intent = Intent(activity, UserMessageHistoryActivityV3::class.java)
            intent.putExtra(EXTRA_MESSAGE_TYPE_INT, type.type)
            activity.startActivity(intent)
        }
    }
}
