package com.zczy.cargo_owner.message.fragment

import android.text.TextUtils
import android.view.View
import com.sfh.lib.mvvm.service.BaseViewModel
import com.sfh.lib.rx.IResultSuccess
import com.sfh.lib.rx.ui.UtilRxView
import com.sfh.lib.ui.AbstractLifecycleFragment
import com.zczy.cargo_owner.R
import com.zczy.cargo_owner.message.UserMessageDetailActivity
import com.zczy.cargo_owner.message.model.MessageType
import com.zczy.cargo_owner.message.rsp.RspTipsDetail
import com.zczy.cargo_owner.order.change.OrderChangeHandleActivity
import com.zczy.cargo_owner.order.detail.WaybillDetailActivity
import com.zczy.cargo_owner.order.overdue.OrderOverdueMainActivity
import com.zczy.cargo_owner.user.questionnaire.QuestionnaireX5WebActivity
import com.zczy.comm.CommServer
import com.zczy.comm.data.entity.EImage
import com.zczy.comm.utils.ex.setVisible
import com.zczy.comm.utils.json.toJsonObject
import com.zczy.comm.x5.X5WebActivity
import io.reactivex.disposables.Disposable
import kotlinx.android.synthetic.main.user_message_detail_look_fragment.view.*

/**
 * 类描述：消息详情展示-去查看
 * 作者：ssp
 * 创建时间：2024/4/24
 */

class MessageDetailLookFragment : AbstractLifecycleFragment<BaseViewModel>(), IResultSuccess<Any?> {
    private var detail: RspTipsDetail = RspTipsDetail()
    private var disposable: Disposable? = null

    override fun getLayout(): Int {
        return R.layout.user_message_detail_look_fragment
    }

    override fun initData(view: View?) {
        initView()
    }

    private fun initView() {
        val json = arguments?.getString(UserMessageDetailActivity.DATA)
        if (!TextUtils.isEmpty(json)) {
            detail = json.toJsonObject(RspTipsDetail::class.java) ?: RspTipsDetail()
            mRoot.tv_title.text = detail.informAppTitle
            mRoot.tv_message.text = detail.informAppContent
            //系统消息 推送活动或http连接，抽奖,问卷调查
            when {
                TextUtils.equals(MessageType.QUESTIONNAIRE_SURVEY, detail.informTemplateId) -> {
                    mRoot.tv_option.setVisible(true)
                    mRoot.tv_option.text = "去查看"
                }

                TextUtils.equals(
                    MessageType.SYSTEM_CODE,
                    detail.informTemplateId
                ) && !TextUtils.isEmpty(detail.businessId) -> {
                    mRoot.tv_option.setVisible(true)
                    mRoot.tv_option.text = "去查看"
                }
                TextUtils.equals(MessageType.MESSAGE_TYPE_29, detail.informTemplateId) || TextUtils.equals(MessageType.MESSAGE_TYPE_30, detail.informTemplateId) -> {
                    mRoot.tv_option.setVisible(true)
                    mRoot.tv_option.text = "去查看"
                }
                TextUtils.equals(MessageType.MESSAGE_TYPE_789, detail.informTemplateId) -> {
                    mRoot.tv_option.setVisible(true)
                    mRoot.tv_option.text = "查看详情"
                }

            }
            mRoot.imageSelectView.maxCount = 1
            mRoot.imageSelectView.canSelect = false
            mRoot.imageSelectView.canDelete = false
            if (detail.msgUrl.isNullOrEmpty()) {
                mRoot.imageSelectView.setVisible(false)
            } else {
                mRoot.imageSelectView.setVisible(true)
                mRoot.imageSelectView.imgList = arrayListOf(EImage(imageId = detail.msgUrl ?: ""))
            }
        }
        disposable = UtilRxView.clicks(mRoot.tv_option, 1000, this)
    }

    override fun onDestroyView() {
        super.onDestroyView()
        disposable?.dispose()
    }

    @Throws(Exception::class)
    override fun onSuccess(o: Any?) {
        when (detail.informTemplateId) {
            MessageType.QUESTIONNAIRE_SURVEY -> {
                val login = CommServer.getUserServer().login
                if (login != null) {
                    //问卷调查
                    QuestionnaireX5WebActivity.start(
                        context = context,
                        url = detail.businessId + "&userId=" + detail.userId + "&userType=" + login.userType
                    )
                }
            }

            MessageType.SYSTEM_CODE -> {
                if (TextUtils.isEmpty(detail.businessId)) {
                    return
                }
                if (detail.businessId.startsWith("H5-")) {
                    val http = detail.businessId.substring("H5-".length)
                    X5WebActivity.startContentUI(activity, http)
                }
            }

            //交易关闭运单、强制结算运单
            MessageType.TRANSACTION_CLOSED, MessageType.MANDATORY_SETTLEMENT -> {
                OrderOverdueMainActivity.start(activity)
            }
            MessageType.MESSAGE_TYPE_789 -> {
                context?.let { OrderChangeHandleActivity.start(it, detail.businessId) }
            }
            MessageType.MESSAGE_TYPE_29, MessageType.MESSAGE_TYPE_30 -> {
                context?.let { WaybillDetailActivity.start(it, detail.businessId) }
            }
        }
    }
}
