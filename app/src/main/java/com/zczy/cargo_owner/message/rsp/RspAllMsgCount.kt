package com.zczy.cargo_owner.message.rsp

import com.zczy.comm.http.entity.ResultData

/**
 * PS:消息分类的未读数量
 * Created by sdx on 2018/11/8.
 */
data class RspAllMsgCount(
        var recommendMsgNum: String = "0", // 推荐消息数量
        var balanceMsgNum: String = "0", // 余额消息数量
        var friendMsgNum: String = "0", //  好友消息数量
        var waybillMsgNum: String = "0", // 运单消息数量
        var systemMsgNum: String = "0", // 系统消息数量
        var bidMsgNum: String = "0" // 议价消息
) : ResultData()

/**
 * 主页的未读消息数量
 */
fun RspAllMsgCount.getAllUnReadCount(): Int {
    val num1 = recommendMsgNum.toIntOrNull() ?: 0
    val num2 = balanceMsgNum.toIntOrNull() ?: 0
    val num3 = bidMsgNum.toIntOrNull() ?: 0
    val num4 = waybillMsgNum.toIntOrNull() ?: 0
    val num5 = systemMsgNum.toIntOrNull() ?: 0

    return num1 + num2 + num3 + num4 + num5
}