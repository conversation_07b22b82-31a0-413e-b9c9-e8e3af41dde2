package com.zczy.cargo_owner.message.req;

import com.zczy.comm.http.entity.BaseNewRequest;
import com.zczy.comm.http.entity.BaseRsp;
import com.zczy.comm.http.entity.ResultData;

/**
 * 功能描述:货主同意/拒绝回单协助
 *
 * <AUTHOR> 孙飞虎
 * @company 中储智运科技股份有限公司
 * @copyright （版权）中储南京智慧物流科技有限公司所有
 * @date 2019/6/4
 */
public class ReqUpdateConsignorBackOrderState  extends BaseNewRequest<BaseRsp<ResultData>> {

    //订单id
    String orderId;

    //   消息id
    String  pushId;

    //0:默认;1:同意;2:拒绝
    String state;


    public ReqUpdateConsignorBackOrderState( String orderId, String  pushId, String state) {
        super("oms-app/order/consignor/updateConsignorBackOrderState");
        this.orderId = orderId;
        this.pushId = pushId;
        this.state = state;
    }
}
