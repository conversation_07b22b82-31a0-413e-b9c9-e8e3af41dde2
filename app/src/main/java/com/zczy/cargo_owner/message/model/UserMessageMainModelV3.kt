package com.zczy.cargo_owner.message.model

import com.sfh.lib.exception.HandleException
import com.sfh.lib.mvvm.service.BaseViewModel
import com.sfh.lib.rx.IResult
import com.zczy.cargo_owner.R
import com.zczy.cargo_owner.message.adapter.UserMessageMainAdapterV3
import com.zczy.cargo_owner.message.entity.MessageMenuItem
import com.zczy.cargo_owner.message.entity.clearAllCount
import com.zczy.cargo_owner.message.entity.setRspAllMsgCountNew
import com.zczy.cargo_owner.message.req.ReqAllMsgCountNew
import com.zczy.cargo_owner.message.req.ReqReadTipsByType
import com.zczy.cargo_owner.message.req.RspAllMsgCountNew
import com.zczy.comm.http.entity.BaseRsp

/**
 * PS:个人中心  消息 模块 主页
 * Created by sdx on 2018/11/7.
 */
open class UserMessageMainModelV3 : BaseViewModel() {

    val listData by lazy {
        mutableListOf(
            // TYPE_0
            MessageMenuItem(
                name = "异常提醒",
                informType = UserMessageType.异常提醒,
                resId = R.drawable.user_message_menu_1_v3,
                adapterType = UserMessageMainAdapterV3.TYPE_0
            ),
            MessageMenuItem(
                name = "资金信息",
                informType = UserMessageType.资金消息,
                resId = R.drawable.user_message_menu_2_v3,
                adapterType = UserMessageMainAdapterV3.TYPE_0
            ),
            MessageMenuItem(
                name = "活动奖励",
                informType = UserMessageType.活动奖励,
                resId = R.drawable.user_message_menu_3_v3,
                adapterType = UserMessageMainAdapterV3.TYPE_0
            ),
            MessageMenuItem(
                name = "变更消息",
                informType = UserMessageType.变更消息,
                resId = R.drawable.user_message_menu_4_v3,
                adapterType = UserMessageMainAdapterV3.TYPE_0
            ),
            // TYPE_1
            MessageMenuItem(
                name = "账号信息",
                informType = UserMessageType.账号信息,
                resId = R.drawable.user_message_list_1_v3,
                adapterType = UserMessageMainAdapterV3.TYPE_1
            ),
            MessageMenuItem(
                name = "运单摘牌",
                informType = UserMessageType.运单摘牌,
                resId = R.drawable.user_message_list_3_v3,
                adapterType = UserMessageMainAdapterV3.TYPE_1
            ),
            MessageMenuItem(
                name = "运输信息",
                informType = UserMessageType.运输信息,
                resId = R.drawable.user_message_list_2_v3,
                adapterType = UserMessageMainAdapterV3.TYPE_1
            ),
            MessageMenuItem(
                name = "增值服务",
                informType = UserMessageType.增值服务,
                resId = R.drawable.user_message_list_6_v3,
                adapterType = UserMessageMainAdapterV3.TYPE_1
            ),
            MessageMenuItem(
                name = "回单消息",
                informType = UserMessageType.回单消息,
                resId = R.drawable.user_message_list_4_v3,
                adapterType = UserMessageMainAdapterV3.TYPE_1
            ),
            MessageMenuItem(
                name = "结算金额超限预警",
                informType = UserMessageType.结算金额超限预警,
                resId = R.drawable.user_message_list_4_v13,
                adapterType = UserMessageMainAdapterV3.TYPE_1
            ),
            MessageMenuItem(
                name = "议价消息",
                informType = UserMessageType.议价消息,
                resId = R.drawable.user_message_list_5_v3,
                adapterType = UserMessageMainAdapterV3.TYPE_1
            ),
            MessageMenuItem(
                name = "重卡租赁",
                informType = UserMessageType.重卡租赁,
                resId = R.drawable.user_message_list_7_v3,
                adapterType = UserMessageMainAdapterV3.TYPE_1
            ),
            MessageMenuItem(
                name = "应急物流",
                informType = UserMessageType.应急物流,
                resId = R.drawable.user_message_list_8_v3,
                adapterType = UserMessageMainAdapterV3.TYPE_1
            ),
            MessageMenuItem(
                name = "系统消息",
                informType = UserMessageType.系统消息_展示,
                resId = R.drawable.user_message_list_10_v3,
                adapterType = UserMessageMainAdapterV3.TYPE_1
            ),
            MessageMenuItem(
                name = "其他消息",
                informType = UserMessageType.历史,
                resId = R.drawable.user_message_list_9_v3,
                adapterType = UserMessageMainAdapterV3.TYPE_1
            )
        )
    }

    /**
     * 消息分类的未读数量
     */
    fun getAllMsgCount() {
        execute(ReqAllMsgCountNew(),
            object : IResult<BaseRsp<RspAllMsgCountNew>> {
                override fun onSuccess(rsp: BaseRsp<RspAllMsgCountNew>) {
                    if (rsp.success()) {
                        listData.setRspAllMsgCountNew(rsp.data)
                        setValue("onGetAllMsgCountSuccess")
                    } else {
                        showDialogToast(rsp.msg)
                        setValue("onGetAllMsgCountSuccess")
                    }
                }

                override fun onFail(rsp: HandleException) {
                    showDialogToast(rsp.msg)
                    setValue("onGetAllMsgCountSuccess")
                }
            })
    }

    fun readTipsByType() {
        execute(
            true,
            ReqReadTipsByType(
                allRead = "1"
            )
        ) { rsp ->
            if (rsp.success()) {
                listData.clearAllCount()
                setValue("onGetAllMsgCountSuccess")
            } else {
                showDialogToast(rsp.msg)
            }
        }
    }
}