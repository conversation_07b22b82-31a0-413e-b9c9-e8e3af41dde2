package com.zczy.cargo_owner.message.entity

import androidx.annotation.DrawableRes
import com.chad.library.adapter.base.entity.MultiItemEntity
import com.zczy.cargo_owner.message.model.UserMessageType
import com.zczy.cargo_owner.message.req.MessageListData
import com.zczy.cargo_owner.message.req.RspAllMsgCountNew

/**
 * PS:
 * <AUTHOR> by sdx on 2019-08-13.
 */
data class MessageMenuItem(
        var name: String = "",
        var count: String = "",
        var message: String = "",
        var time: String = "",
        var informType: UserMessageType,
        @DrawableRes
        var resId: Int = -1,
        var adapterType: Int = -1,
        var sortedId: String = ""
) : MultiItemEntity {
    override fun getItemType(): Int = adapterType
}

fun MessageMenuItem.formatMessage(): String {
    return if (message.isEmpty()) {
        "近一个月暂无消息"
    } else {
        message
    }
}

fun MessageMenuItem.formatCount(): String {
    return if (count.toIntOrNull() ?: 0 > 99) {
        "..."
    } else {
        count
    }
}

fun MutableList<MessageMenuItem>.setRspAllMsgCountNew(rsp: RspAllMsgCountNew?) {
    rsp ?: return
    fun MessageMenuItem.setMessageListData(listData: MessageListData?) {
        name = listData?.name ?: ""
        count = listData?.count ?: "0"
        time = listData?.date ?: ""
        message = listData?.message ?: ""
        sortedId = listData?.sortedId ?: ""
    }
    forEach {
        when (it.informType) {
            UserMessageType.异常提醒 -> {
                it.setMessageListData(rsp.abnormalNum)
            }
            UserMessageType.资金消息 -> {
                it.setMessageListData(rsp.moneyNum)
            }
            UserMessageType.活动奖励 -> {
                it.setMessageListData(rsp.activityNum)
            }
            UserMessageType.变更消息 -> {
                it.setMessageListData(rsp.changeNum)
            }
            UserMessageType.账号信息 -> {
                it.setMessageListData(rsp.auditNum)
            }
            UserMessageType.运输信息 -> {
                it.setMessageListData(rsp.publishNum)
            }
            UserMessageType.运单摘牌 -> {
                it.setMessageListData(rsp.delistingNum)
            }
            UserMessageType.回单消息 -> {
                it.setMessageListData(rsp.receiptNum)
            }
            UserMessageType.议价消息 -> {
                it.setMessageListData(rsp.bargainNum)
            }
            UserMessageType.增值服务 -> {
                it.setMessageListData(rsp.prepaidNum)
            }
            UserMessageType.重卡租赁 -> {
                it.setMessageListData(rsp.heavyNum)
            }
            UserMessageType.应急物流 -> {
                it.setMessageListData(rsp.emergencyNum)
            }
            UserMessageType.系统消息_展示 -> {
                it.setMessageListData(rsp.systemNum)
            }
            UserMessageType.历史 -> {
                it.setMessageListData(rsp.otherNum)
            }
            UserMessageType.结算金额超限预警 -> {
                it.setMessageListData(rsp.overLimitWarningNum)
            }
            else -> {
            }
        }
    }
    val sortedBy = sortedBy { it.sortedId.toIntOrNull() ?: Int.MAX_VALUE }
    clear()
    addAll(sortedBy)
}

fun List<MessageMenuItem>.clearAllCount() {
    forEach {
        it.count = "0"
    }
}
