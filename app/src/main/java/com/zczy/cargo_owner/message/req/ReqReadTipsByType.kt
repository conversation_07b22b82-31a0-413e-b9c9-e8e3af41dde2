package com.zczy.cargo_owner.message.req

import com.zczy.cargo_owner.message.rsp.RspReadTipsByType
import com.zczy.comm.http.entity.BaseNewRequest
import com.zczy.comm.http.entity.BaseRsp

/**
 * PS: 根据消息类型更新消息为已读
 * http://wiki.zczy56.com/pages/viewpage.action?pageId=10551299
 * Created by sdx on 2018/11/21.
 */
data class ReqReadTipsByType(
    var informType: String = "",// 多个类型用逗号分隔传到informType
    /** 年份 */
    var year: String = "",
    /** 月份 */
    var month: String = "",
    /** sysType 系统类型：0:汽运 1:水运 2:多式联运（不传时默认查询汽运系统的消息）*/
    var sysType: String = "0",
    /** 老的消息操作0；1：新的消息操作，默认老的 */
    var msgType: String = "1",
    /** 是否全读，1：是全部已读 */
    var allRead: String = "0"
) : BaseNewRequest<BaseRsp<RspReadTipsByType?>>("mms-app/tips/readTipsByType")