package com.zczy.cargo_owner.message;

import android.app.Notification;
import android.app.NotificationChannel;
import android.app.NotificationManager;
import android.app.PendingIntent;
import android.content.Context;
import android.content.Intent;

import androidx.core.app.NotificationCompat;
import android.text.TextUtils;
import android.util.Log;

import com.igexin.sdk.GTIntentService;
import com.igexin.sdk.PushManager;
import com.igexin.sdk.message.GTCmdMessage;
import com.igexin.sdk.message.GTNotificationMessage;
import com.igexin.sdk.message.GTTransmitMessage;
import com.sfh.lib.AppCacheManager;
import com.sfh.lib.event.RxBusEventManager;
import com.zczy.cargo_owner.AppMainContext;
import com.zczy.cargo_owner.R;
import com.zczy.cargo_owner.message.entity.JPsuhMessage;
import com.zczy.cargo_owner.message.entity.RxBusMessageSizeRefresh;
import com.zczy.cargo_owner.message.entity.RxBusUserInfo;
import com.zczy.cargo_owner.message.model.MessageType;
import com.zczy.comm.Const;
import com.zczy.comm.ZczyApplication;
import com.zczy.comm.utils.json.JsonUtil;

/**
 * 功能描述:
 *
 * <AUTHOR> 孙飞虎
 * @company 中储智运科技股份有限公司
 * @copyright （版权）中储南京智慧物流科技有限公司所有
 * @date 2018/12/3
 */
public class PushIntentService extends GTIntentService {
    public final static String TAG = PushIntentService.class.getName();
    private static final int SERVICE_NOTIFICATION_ID = 0x889;

    @Override
    public void onReceiveMessageData(Context context, GTTransmitMessage msg) {
        // 透传消息的处理，详看SDK demo
        String appid = msg.getAppid();
        String taskid = msg.getTaskId();
        String messageid = msg.getMessageId();
        byte[] payload = msg.getPayload();
        String pkg = msg.getPkgName();
        String cid = msg.getClientId();

        // 第三方回执调用接口，actionid范围为90000-90999，可根据业务场景执行
        boolean result = PushManager.getInstance().sendFeedbackMessage(context, taskid, messageid, 90001);

        Log.d(TAG, "PushIntentService onReceiveMessageData -> " + "appid = " + appid + "\ntaskid = " + taskid + "\nmessageid = " + messageid + "\npkg = " + pkg
                + "\ncid = " + cid);

        if (payload == null) {
            Log.d(TAG, "PushIntentService receiver payload = null");
        } else {
            String data = new String(payload);
            Log.d(TAG, "PushIntentService  receiver payload = " + data);

            JPsuhMessage pushMessage = JsonUtil.toJsonObject(data, JPsuhMessage.class);
            this.actionNotification(context, pushMessage);
        }
    }

    /***
     * 处理消息
     * @param context
     * @param data
     */
    public void actionNotification(Context context, JPsuhMessage data) {

        if (TextUtils.isEmpty(data.getContentId())) {
            return;
        }

        //通知栏
        this.notification(context, data);

        // 首页刷新未读消息数量
        RxBusEventManager.postEvent(new RxBusMessageSizeRefresh());

        //语音播报的开关
        boolean open = AppCacheManager.getCache(Const.VOICE_OPEN_STATUS, Boolean.class, true);
        if (open) {
            //使用科大讯飞播放语音
            AppMainContext.readText(context, data.getContent());
        }

        //锁定消息
        if (TextUtils.equals(MessageType.MESSAGE_MEMBER_LOCAKED, data.getMsgTemplateId())) {
            //调往登陆页面
            ZczyApplication application = AppCacheManager.getApplication();
            application.onLoseToken("", data.getTitle());
            return;
        }

        //问卷调查消息
        if (TextUtils.equals(MessageType.QUESTIONNAIRE_SURVEY, data.getMsgTemplateId())) {
            //  刷新服务大厅问卷调查数量
            // ApplicationEntity.rxBusPostUI(new IPstServiceHall.RxEventQuestionnaireSize());
            return;
        }
        if (TextUtils.equals(MessageType.OWNER_MONEY_PLAN, data.getMsgTemplateId())) {
            // 推送资金计划消息
            Intent intent = new Intent("com.zczy.cargo.OwnerMoney");
            intent.putExtra("OwnerMoney", data.getContent());
            sendBroadcast(intent);
        }
        // 会员信息审核通过 - 会员信息审核失败 - 会员帐号恢复正常 - 车辆审核未通过 -车辆审核通过
        if (MessageType.USER.contains(data.getMsgTemplateId())) {
            //  刷新个人信息
            RxBusEventManager.postEvent(new RxBusUserInfo());
        }

    }

    /***
     * 根据获取到的消息，打开对于的详情页面
     * @param context
     * @param data
     */
    private void notification(Context context, JPsuhMessage data) {

        boolean open = AppCacheManager.getCache(Const.MESSAGE_OPEN_STATUS, Boolean.class, true);
        if (!open) {
            //消息提醒 关闭则不通知
            return;
        }
        NotificationManager mNotificationManager = (NotificationManager) context.getSystemService(
                Context.NOTIFICATION_SERVICE);
        NotificationCompat.Builder builder = new NotificationCompat.Builder(context, context.getPackageName())
                //设置通知时间，此事件用于通知栏排序
                .setWhen(System.currentTimeMillis())
                //设置优先级，低优先级可能被隐藏
                .setPriority(NotificationCompat.PRIORITY_HIGH)
                //设置通知栏能否被清楚，true不能被清除，false可以被清除
                .setOngoing(false)
                .setAutoCancel(true)
                //设置通知栏的小图标,必需设置，否则crash
                .setSmallIcon(R.drawable.ic_launcher)
                .setContentTitle(data.getTitle())
                .setContentText(data.getContent());

        if (android.os.Build.VERSION.SDK_INT >= android.os.Build.VERSION_CODES.O) {
            NotificationChannel channel = new NotificationChannel(context.getPackageName(), "推送", NotificationManager.IMPORTANCE_HIGH);
            mNotificationManager.createNotificationChannel(channel);
        }

        // 其他全部进详情-消息详情中处理
        Intent intent = new Intent(context, UserMessageDetailActivity.class);
        intent.putExtra(UserMessageDetailActivity.EXTRA_MESSAGE_ID_STRING, data.getContentId());
        intent.setFlags(Intent.FLAG_ACTIVITY_NEW_TASK);
        PendingIntent pendingIntent = PendingIntent.getActivity(context, data.getContentId().hashCode(), intent, PendingIntent.FLAG_IMMUTABLE);
        builder.setContentIntent(pendingIntent);

        Notification notification = builder.build();
        mNotificationManager.notify(SERVICE_NOTIFICATION_ID, notification);
    }


    /***
     * 设置JPushAlias 推送信息是标识
     * @param appContext
     * @param userid
     */
    public static void initJPushAlias(Context appContext, boolean bind, String userid) {

        if (bind) {
            // 设置别名
            boolean ok = PushManager.getInstance().bindAlias(appContext, userid);
            Log.d(TAG, "PushIntentService bindAlias userid=" + userid + " ok = " + ok);

        } else {
            //解绑别名
            boolean ok = PushManager.getInstance().unBindAlias(appContext.getApplicationContext(), userid, false);
            Log.d(TAG, "PushIntentService initJPushAlias  unBindAlias userid=" + userid + " ok = " + ok);
            //清空通知
            NotificationManager nm = (NotificationManager) appContext.getSystemService(Context.NOTIFICATION_SERVICE);
            nm.cancel(SERVICE_NOTIFICATION_ID);
        }

    }

    @Override
    public void onReceiveServicePid(Context context, int i) {
        Log.d(TAG, "PushIntentService onReceiveServicePid ->   " + i);
    }

    @Override
    public void onReceiveClientId(Context context, String clientid) {
        Log.d(TAG, "PushIntentService onReceiveClientId -> " + "clientid = " + clientid);
    }

    @Override
    public void onReceiveOnlineState(Context context, boolean online) {
        Log.d(TAG, "PushIntentService onReceiveOnlineState -> " + (online ? "online" : "offline"));
    }

    @Override
    public void onReceiveCommandResult(Context context, GTCmdMessage cmdMessage) {
        Log.d(TAG, "PushIntentService onReceiveCommandResult -> " + cmdMessage);

    }

    @Override
    public void onNotificationMessageArrived(Context context, GTNotificationMessage message) {
        Log.d(TAG, "PushIntentService onNotificationMessageArrived -> " + "appid = " + message.getAppid() + "\ntaskid = " + message.getTaskId() + "\nmessageid = "
                + message.getMessageId() + "\npkg = " + message.getPkgName() + "\ncid = " + message.getClientId() + "\ntitle = "
                + message.getTitle() + "\ncontent = " + message.getContent());
    }

    @Override
    public void onNotificationMessageClicked(Context context, GTNotificationMessage message) {
        Log.d(TAG, "PushIntentService onNotificationMessageClicked -> " + "appid = " + message.getAppid() + "\ntaskid = " + message.getTaskId() + "\nmessageid = "
                + message.getMessageId() + "\npkg = " + message.getPkgName() + "\ncid = " + message.getClientId() + "\ntitle = "
                + message.getTitle() + "\ncontent = " + message.getContent());
    }
}
