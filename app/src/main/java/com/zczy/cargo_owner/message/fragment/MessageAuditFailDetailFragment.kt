package com.zczy.cargo_owner.message.fragment

import android.view.View
import com.sfh.lib.mvvm.service.BaseViewModel
import com.sfh.lib.rx.IResultSuccess
import com.sfh.lib.rx.ui.UtilRxView
import com.sfh.lib.ui.AbstractLifecycleFragment
import com.zczy.cargo_owner.R
import com.zczy.cargo_owner.message.UserMessageDetailActivity
import com.zczy.cargo_owner.message.rsp.RspTipsDetail
import com.zczy.cargo_owner.user.certification.CertificationUtils
import com.zczy.comm.data.entity.EImage
import com.zczy.comm.utils.ex.setVisible
import com.zczy.comm.utils.json.toJsonObject
import io.reactivex.disposables.Disposable
import kotlinx.android.synthetic.main.user_message_detail_audit_fail_fragment.view.*

/**
 * 类描述：会员信息审核失败
 * 作者：ssp
 * 创建时间：2024/4/24
 */
class MessageAuditFailDetailFragment : AbstractLifecycleFragment<BaseViewModel>(), IResultSuccess<Any?> {
    private var disposable: Disposable? = null
    private fun initView() {
        val json = arguments?.getString(UserMessageDetailActivity.DATA)
        json?.let {
            val detail = it.toJsonObject(RspTipsDetail::class.java) ?: RspTipsDetail()
            mRoot.tv_title.text = detail.informAppTitle
            mRoot.tv_message.text = detail.informAppContent
            mRoot.tv_fail.text = ""
            mRoot.imageSelectView.maxCount = 1
            mRoot.imageSelectView.canSelect = false
            mRoot.imageSelectView.canDelete = false
            if (detail.msgUrl.isNullOrEmpty()) {
                mRoot.imageSelectView.setVisible(false)
            } else {
                mRoot.imageSelectView.setVisible(true)
                mRoot.imageSelectView.imgList = arrayListOf(EImage(imageId = detail.msgUrl ?: ""))
            }
        }
        disposable = UtilRxView.clicks(mRoot.tv_option, 1000, this)
    }

    override fun onDestroyView() {
        super.onDestroyView()
        disposable?.dispose()
    }

    override fun getLayout(): Int {
        return R.layout.user_message_detail_audit_fail_fragment
    }

    override fun initData(view: View?) {
        initView()
    }

    @Throws(Exception::class)
    override fun onSuccess(o: Any?) {
        //  重新提交注册信息
        CertificationUtils.hzCertification(context)
    }
}
