package com.zczy.cargo_owner.message.fragment

import android.annotation.SuppressLint
import android.text.TextUtils
import android.view.View
import com.sfh.lib.mvvm.service.BaseViewModel
import com.sfh.lib.rx.IResultSuccess
import com.sfh.lib.rx.ui.UtilRxView
import com.sfh.lib.ui.AbstractLifecycleFragment
import com.zczy.cargo_owner.R
import com.zczy.cargo_owner.message.UserMessageDetailActivity
import com.zczy.cargo_owner.message.model.MessageType
import com.zczy.cargo_owner.message.rsp.RspTipsDetail
import com.zczy.cargo_owner.user.exception.WaybillExceptionActivity
import com.zczy.comm.data.entity.EImage
import com.zczy.comm.utils.ex.setVisible
import com.zczy.comm.utils.json.toJsonObject
import io.reactivex.disposables.Disposable
import kotlinx.android.synthetic.main.user_message_detail_order_exception_fragment.view.*

/**
 * 类描述：运单监控异常消息
 * 作者：ssp
 * 创建时间：2024/4/24
 */

class MessageOrderExceptionDetailFragment : AbstractLifecycleFragment<BaseViewModel>(), IResultSuccess<Any?> {
    private var disposable: Disposable? = null

    override fun getLayout(): Int {
        return R.layout.user_message_detail_order_exception_fragment
    }

    @SuppressLint("SetTextI18n")
    override fun initData(view: View) {
        initView()
    }

    @SuppressLint("SetTextI18n")
    private fun initView() {
        val json = arguments?.getString(UserMessageDetailActivity.DATA)
        if (!TextUtils.isEmpty(json)) {
            val detail = json.toJsonObject(RspTipsDetail::class.java)
            mRoot.tv_title.text = detail?.informAppTitle
            mRoot.tv_message.text = detail?.informAppContent
            mRoot.tv_order.text = "运单号：${detail?.businessId}"
            mRoot.tv_option.setVisible(false)
            when (detail?.informTemplateId) {
                MessageType.ORDER_EXCEPCTION_ADD -> {
                    mRoot.tv_option.text = "提交证明材料"
                    mRoot.tv_option.setVisible(true)
                }

                MessageType.ORDER_EXCEPCTION_AGAIN -> {
                    mRoot.tv_option.text = "重新提交证明材料"
                    mRoot.tv_option.setVisible(true)
                }
            }
            mRoot.imageSelectView.maxCount = 1
            mRoot.imageSelectView.canSelect = false
            mRoot.imageSelectView.canDelete = false
            if (detail?.msgUrl.isNullOrEmpty()) {
                mRoot.imageSelectView.setVisible(false)
            } else {
                mRoot.imageSelectView.setVisible(true)
                mRoot.imageSelectView.imgList = arrayListOf(EImage(imageId = detail?.msgUrl ?: ""))
            }
        }
        disposable = UtilRxView.clicks(mRoot.tv_option, 1000, this)
    }

    override fun onDestroyView() {
        super.onDestroyView()
        if (disposable != null) {
            disposable?.dispose()
        }
    }

    @Throws(Exception::class)
    override fun onSuccess(o: Any?) {
        //  进入运单异常监控列表
        WaybillExceptionActivity.startUI(context)
    }
}
