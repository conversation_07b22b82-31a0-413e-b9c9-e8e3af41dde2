package com.zczy.cargo_owner.message

import android.app.Activity
import android.content.Context
import android.content.Intent
import android.os.Bundle
import android.text.TextUtils
import android.view.LayoutInflater
import android.view.View
import android.view.ViewGroup
import android.widget.TextView
import com.sfh.lib.event.RxBusEventManager
import com.sfh.lib.mvvm.annotation.LiveDataMatch
import com.sfh.lib.ui.dialog.DialogBuilder
//import com.wbtech.ums.UmsAgent
import com.zczy.cargo_owner.R
import com.zczy.cargo_owner.message.adapter.UserMessageListAdapterV3
import com.zczy.cargo_owner.message.adapter.UserMessageListListenerV3
import com.zczy.cargo_owner.message.entity.EventRefreshMessageList
import com.zczy.cargo_owner.message.model.UserMessageListModelV3
import com.zczy.cargo_owner.message.model.UserMessageType
import com.zczy.cargo_owner.message.req.ReqBatchDelete
import com.zczy.cargo_owner.message.req.ReqHandleInform
import com.zczy.cargo_owner.message.req.RspTipsInfoNew
import com.zczy.comm.CommServer
import com.zczy.comm.http.entity.PageList
import com.zczy.comm.http.entity.ResultData
import com.zczy.comm.ui.BaseActivity
import com.zczy.comm.utils.dp2px
import com.zczy.comm.utils.ex.setVisible
import com.zczy.comm.widget.AppToolber
import com.zczy.comm.widget.pulltorefresh.SwipeRefreshMoreLayout
import kotlinx.android.synthetic.main.user_message_list_activity.*

/**
 * PS: 个人中心  消息 消息列表 页面
 * Created by sdx on 2018/11/7.
 */
open class UserMessageListActivityV3 : BaseActivity<UserMessageListModelV3>() {

    private val swipeRefreshMoreLayout by lazy { findViewById<SwipeRefreshMoreLayout>(R.id.swipe_refresh_more_layout) }
    private val btnHistory by lazy { findViewById<View>(R.id.btn_history) }

    private val adapter = UserMessageListAdapterV3()
    private var edit: Boolean = true
    private val title: String by lazy {
        intent.getStringExtra(EXTRA_TITLE_STRING) ?: "通知"
    }
    private val type: UserMessageType by lazy {
        val intExtra = intent.getIntExtra(EXTRA_MESSAGE_TYPE_INT, UserMessageType.系统消息.type)
        UserMessageType.get(intExtra)
    }

    override fun getLayout(): Int = R.layout.user_message_list_activity

    override fun bindView(bundle: Bundle?) {
        initToolBar()
        initList()
        btnHistory.setOnClickListener {
            UserMessageHistoryActivityV3.start(this@UserMessageListActivityV3, type)
        }
    }

    override fun initData() {
        swipeRefreshMoreLayout.onAutoRefresh()
    }

    private fun initToolBar() {
        // 标记已读点击
        view_title.text = title
        tv_all_read.setOnClickListener {
            val dialogBuilder = DialogBuilder()
            dialogBuilder.message = "确认标记全部消息为已读吗？"
            dialogBuilder.okListener = DialogBuilder.DialogInterface.OnClickListener { p0, _ ->
                p0.dismiss()
                viewModel?.readTipsByType(type)
//                UmsAgent.onEvent(this@UserMessageListActivityV3, "message&allreaded", CommServer.getUserServer().login?.userId)
            }
            showDialog(dialogBuilder)
        }
        img_back.setOnClickListener {
            finish()
        }

        tv_edit.setOnClickListener {
            if (edit) {
                tv_edit.text = "取消"
                btnHistory.setVisible(false)
                cl_bottom.visibility = View.VISIBLE
                adapter.data.forEach {
                    it.canEdit = true
                }
                adapter.notifyDataSetChanged()
            } else {
                tv_edit.text = "编辑"
                btnHistory.setVisible(adapter.data.isNotEmpty())
                cl_bottom.visibility = View.GONE
                adapter.data.forEach {
                    it.canEdit = false
                    it.isSelected = false
                }
                adapter.notifyDataSetChanged()
            }
            edit = !edit
        }
        tv_select_all.setOnClickListener {
            adapter.data.forEach {
                it.isSelected = true
            }
            adapter.notifyDataSetChanged()
        }

        tv_delete.setOnClickListener {
            var informIds = ""
            adapter.data.forEach {
                if (it.isSelected) {
                    informIds += "${it.messageId},"
                }
            }
            if (TextUtils.isEmpty(informIds)) {
                showToast("请选择要删除的消息")
                return@setOnClickListener
            }
            val req = ReqBatchDelete(
                operateType = "2",
                informIds = informIds
            )
            val dialogBuilder = DialogBuilder()
            dialogBuilder.message = "确定删除消息吗？"
            dialogBuilder.okListener = DialogBuilder.DialogInterface.OnClickListener { p0, _ ->
                p0.dismiss()
                viewModel?.batchDelete(req)
            }
            showDialog(dialogBuilder)

        }
        tv_all_read.setVisible(false)
        btnHistory.setVisible(false)
    }

    private fun initList() {
        val emptyView = LayoutInflater.from(this).inflate(R.layout.message_recycler_empty, null)
        val lp = ViewGroup.LayoutParams(ViewGroup.LayoutParams.MATCH_PARENT, ViewGroup.LayoutParams.MATCH_PARENT)
        emptyView.layoutParams = lp
        emptyView.findViewById<TextView>(R.id.tv_message)?.text = "近一个月没有相关消息"
        emptyView.findViewById<TextView>(R.id.tv_history).setOnClickListener {
            UserMessageHistoryActivityV3.start(this@UserMessageListActivityV3, type)
        }
        swipeRefreshMoreLayout.run {
            setAdapter(adapter, true)
            addItemDecorationSize(dp2px(8F))
            setEmptyView(emptyView)
            addOnItemListener(UserMessageListListenerV3 { item, allow ->
                if (allow) {
                    val dialogBuilder = DialogBuilder()
                    dialogBuilder.message = "确认接受该用户的好友请求吗？"
                    dialogBuilder.okListener = DialogBuilder.DialogInterface.OnClickListener { p0, _ ->
                        p0.dismiss()
                        val req = ReqHandleInform(
                            informId = item.messageId,
                            dealState = "1",
                            operateType = "0"
                        )
                        viewModel?.handleInform(req)
                    }
                    showDialog(dialogBuilder)
                } else {
                    val dialogBuilder = DialogBuilder()
                    dialogBuilder.message = "确认拒绝该用户的好友请求吗？"
                    dialogBuilder.okListener = DialogBuilder.DialogInterface.OnClickListener { p0, _ ->
                        p0.dismiss()
                        val req = ReqHandleInform(
                            informId = item.messageId,
                            dealState = "2",
                            operateType = "0"
                        )
                        viewModel?.handleInform(req)
                    }
                    showDialog(dialogBuilder)
                }
            })
            setOnLoadListener2 {
                viewModel?.tipsInfo(it, type)
            }
        }
    }


    @LiveDataMatch
    open fun onTipsInfo(data: PageList<RspTipsInfoNew>?) {
        swipeRefreshMoreLayout.onRefreshCompale(data)
        tv_all_read.setVisible(adapter.data.isNotEmpty())
        btnHistory.setVisible(adapter.data.isNotEmpty())
    }

    @LiveDataMatch
    open fun onReadTipsByTypeSuccess() {
        RxBusEventManager.postEvent(EventRefreshMessageList())
        swipeRefreshMoreLayout.onAutoRefresh()
    }

    @LiveDataMatch
    open fun onHandleInformSuccess(req: ReqHandleInform) {
        adapter.data
            .find {
                it.messageId == req.informId
            }
            ?.run {
                messageStatus = "1"
                reviewStatus = req.dealState
            }
        adapter.notifyDataSetChanged()
    }

    @LiveDataMatch
    open fun onHandleBatchDeletSeuccess(rsp: ResultData) {
        if (rsp.success()) {
            showToast(rsp.resultMsg)
            adapter.notifyDataSetChanged()
        } else {
            showDialogToast(rsp.resultMsg)
        }
    }

    companion object {
        private const val EXTRA_TITLE_STRING = "extra_title_string"
        private const val EXTRA_MESSAGE_TYPE_INT = "extra_message_type_int"

        @JvmStatic
        fun start(activity: Activity?, title: String, type: UserMessageType) {
            activity ?: return
            val intent = Intent(activity, UserMessageListActivityV3::class.java)
            intent.putExtra(EXTRA_TITLE_STRING, title)
            intent.putExtra(EXTRA_MESSAGE_TYPE_INT, type.type)
            activity.startActivity(intent)
        }

        @JvmStatic
        fun buildIntent(context: Context, title: String, type: UserMessageType): Intent {
            val intent = Intent(context, UserMessageListActivityV3::class.java)
            intent.putExtra(EXTRA_TITLE_STRING, title)
            intent.putExtra(EXTRA_MESSAGE_TYPE_INT, type.type)
            return intent
        }
    }
}
