package com.zczy.cargo_owner.message.rsp

import com.zczy.comm.http.entity.ResultData

/**
 * PS: 消息返回 对象
 * Created by sdx on 2018/11/21.
 * {
 *   "informAppId": 38,
 *   "informId": 81,
 *   "informAppTitle": "1534",
 *   "informAppContent": "6548",
 *   "readType": "1",
 *   "informType": "6",
 *   "sendType": "1",
 *   "userId": "1281012010787537917",
 *   "childUserId": null,
 *   "informTemplateId": "5",
 *   "readTime": "2018-11-21 10:52:07",
 *   "businessId": null,
 *   "pushAppFlag": "0",
 *   "pushSMSFlag": "0",
 *   "dealState": "0",
 *   "dealTime": null,
 *   "notReadToSendSMSFlag": null,
 *   "createBy": null,
 *   "createTime": "2018-11-21 09:34:06",
 *   "lastUpDateBy": "1281012010787537917",
 *   "lastUpDateTime": "2018-11-21 10:52:07",
 *   "deleteFlag": "0"
 * }
 */
data class RspTipsDetail(
    var informAppId: String = "",// 38,
    var informId: String = "",// 81,
    var informAppTitle: String = "",// "1534",
    var informAppContent: String = "",// "6548",
    var readType: String = "",// "1",
    var informType: String = "",// "6",
    var sendType: String = "",// "1",
    var userId: String = "",// "1281012010787537917",
    var childUserId: String = "",// null,
    var informTemplateId: String = "",// "5",
    var readTime: String = "",// "2018-11-21 10:52:07",
    var businessId: String = "",// null,
    var pushAppFlag: String = "",// "0",
    var pushSMSFlag: String = "",// "0",
    var dealState: String = "",// "0",
    var dealTime: String = "",// null,
    var notReadToSendSMSFlag: String = "",// null,
    var createBy: String = "",// null,
    var createTime: String = "",// "2018-11-21 09:34:06",
    var lastUpDateBy: String = "",// "1281012010787537917",
    var lastUpDateTime: String = "",// "2018-11-21 10:52:07",
    var deleteFlag: String = "",// "0"
    var msgUrl: String? = null,// 图片地址
) : ResultData()