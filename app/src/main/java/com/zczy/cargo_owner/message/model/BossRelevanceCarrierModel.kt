package com.zczy.cargo_owner.message.model

import com.sfh.lib.mvvm.service.BaseViewModel
import com.zczy.cargo_owner.message.req.ReqUpdateConsignorBackOrderState
import com.zczy.comm.http.entity.BaseRsp
import com.zczy.comm.http.entity.ResultData

/**
 * 类描述：协助补充回单照片消息
 * 作者：ssp
 * 创建时间：2024/4/24
 */
class BossRelevanceCarrierModel : BaseViewModel() {
    fun handleInform(req: ReqUpdateConsignorBackOrderState) {
        this.execute<BaseRsp<ResultData>>(
            false,
            req
        ) { resultDataBaseRsp ->
            showDialogToast(resultDataBaseRsp.msg)
            if (resultDataBaseRsp.success()) {
                setValue("onSuccess")
            }
        }
    }
}
