<?xml version="1.0" encoding="utf-8"?>
<androidx.constraintlayout.widget.ConstraintLayout xmlns:android="http://schemas.android.com/apk/res/android"
    xmlns:app="http://schemas.android.com/apk/res-auto"
    xmlns:tools="http://schemas.android.com/tools"
    android:layout_width="match_parent"
    android:layout_height="match_parent"
    tools:background="#aa000000">

    <androidx.constraintlayout.widget.ConstraintLayout
        android:layout_width="288dp"
        android:layout_height="wrap_content"
        android:layout_marginBottom="180dp"
        android:background="@drawable/file_lease_white_circle"
        app:layout_constraintBottom_toBottomOf="parent"
        app:layout_constraintLeft_toLeftOf="parent"
        app:layout_constraintRight_toRightOf="parent">

        <TextView
            android:id="@+id/tv_content"
            android:layout_width="wrap_content"
            android:layout_height="wrap_content"
            android:layout_marginTop="24dp"
            android:gravity="center"
            android:paddingLeft="19dp"
            android:paddingRight="19dp"
            android:textColor="#ff333333"
            android:textSize="16sp"
            app:layout_constraintLeft_toLeftOf="parent"
            app:layout_constraintRight_toRightOf="parent"
            app:layout_constraintTop_toTopOf="parent"
            tools:text="该手机尚未注册，是否注册？" />

        <TextView
            android:id="@+id/tv_ok"
            android:layout_width="match_parent"
            android:layout_height="46dp"
            android:gravity="center"
            android:text="确定"
            android:textColor="#ff5086fc"
            android:textSize="16sp"
            app:layout_constraintBottom_toBottomOf="parent"
            app:layout_constraintTop_toBottomOf="@id/line" />

        <View
            android:id="@+id/line"
            android:layout_width="match_parent"
            android:layout_height="@dimen/divider_05"
            android:layout_marginTop="21dp"
            android:background="@drawable/base_file_line_gray"
            app:layout_constraintLeft_toLeftOf="parent"
            app:layout_constraintRight_toRightOf="parent"
            app:layout_constraintTop_toBottomOf="@id/tv_content" />

    </androidx.constraintlayout.widget.ConstraintLayout>

</androidx.constraintlayout.widget.ConstraintLayout>
