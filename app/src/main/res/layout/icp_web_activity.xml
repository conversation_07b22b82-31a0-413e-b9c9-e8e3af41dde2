<LinearLayout xmlns:android="http://schemas.android.com/apk/res/android"
    android:layout_width="match_parent"
    android:layout_height="match_parent"
    xmlns:app="http://schemas.android.com/apk/res-auto"
    android:layout_gravity="center"
    android:gravity="center"
    android:orientation="vertical">

    <com.zczy.comm.widget.AppToolber
        android:id="@+id/appToolber"
        android:layout_width="match_parent"
        android:layout_height="55dp"
        android:background="#ffffff"
        app:leftIcon="@drawable/base_back_black"
        app:titleColor="#333333"
        app:titleSize="17sp"
        app:titleTxt="" />

    <com.zczy.comm.x5.X5WebView
        android:id="@+id/X5WebView"
        android:layout_width="match_parent"
        android:layout_height="match_parent"
        android:orientation="vertical" />
</LinearLayout>
