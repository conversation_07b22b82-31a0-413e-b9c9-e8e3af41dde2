<?xml version="1.0" encoding="utf-8"?>
<RelativeLayout xmlns:android="http://schemas.android.com/apk/res/android"
    android:id="@+id/rl_root_view"
    android:layout_width="wrap_content"
    android:layout_height="wrap_content"
    android:background="@drawable/base_ui_shape_eff0f3_solid_6radius"
    android:orientation="vertical">

    <TextView
        android:id="@+id/tv_day_time"
        android:layout_width="wrap_content"
        android:layout_height="wrap_content"
        android:layout_marginStart="15dp"
        android:layout_marginTop="5dp"
        android:text="第2天"
        android:textColor="@color/text_66"
        android:textSize="15sp" />


    <TextView
        android:id="@+id/tv_integral"
        android:layout_width="wrap_content"
        android:layout_height="wrap_content"
        android:layout_below="@+id/tv_day_time"
        android:layout_marginStart="15dp"
        android:layout_marginTop="3dp"
        android:layout_marginBottom="5dp"
        android:text="15积分"
        android:textColor="@color/text_99"
        android:textSize="12sp" />

    <ImageView
        android:id="@+id/iv_integral"
        android:layout_width="50dp"
        android:layout_height="50dp"
        android:layout_alignParentEnd="true"
        android:layout_centerVertical="true"
        android:layout_marginEnd="15dp"
        android:background="@drawable/integral_icon" />
</RelativeLayout>
