<?xml version="1.0" encoding="utf-8"?>
<androidx.constraintlayout.widget.ConstraintLayout xmlns:android="http://schemas.android.com/apk/res/android"
    xmlns:app="http://schemas.android.com/apk/res-auto"
    xmlns:tools="http://schemas.android.com/tools"
    android:layout_width="match_parent"
    android:layout_height="wrap_content"
    android:layout_marginTop="7dp"
    android:background="#FFFFFF"
    android:orientation="horizontal"
    android:paddingHorizontal="15dp"
    android:paddingVertical="10dp">

    <ImageView
        android:id="@+id/ivCheck"
        android:layout_width="wrap_content"
        android:layout_height="wrap_content"
        android:layout_marginTop="3dp"
        android:background="@drawable/base_check_blue_select_selector_v2"
        app:layout_constraintStart_toStartOf="parent"
        app:layout_constraintTop_toTopOf="parent" />

    <LinearLayout
        android:id="@+id/view1"
        android:layout_width="0dp"
        android:layout_height="wrap_content"
        android:layout_marginStart="5dp"
        android:layout_weight="1"
        android:orientation="vertical"
        app:layout_constrainedWidth="true"
        app:layout_constraintEnd_toStartOf="@id/tvMoney"
        app:layout_constraintStart_toEndOf="@id/ivCheck"
        app:layout_constraintTop_toTopOf="parent">

        <LinearLayout
            android:layout_width="match_parent"
            android:layout_height="wrap_content"
            android:orientation="horizontal">

            <TextView
                android:layout_width="wrap_content"
                android:layout_height="wrap_content"
                android:text="结算时间："
                android:textColor="#ff666666"
                android:textSize="13dp" />

            <TextView
                android:id="@+id/tvTime"
                android:layout_width="wrap_content"
                android:layout_height="wrap_content"
                android:textColor="#ff666666"
                android:textSize="13dp"
                tools:text="2024-02-02 19:19:11" />

        </LinearLayout>

        <LinearLayout
            android:layout_width="match_parent"
            android:layout_height="wrap_content"
            android:layout_marginTop="5dp"
            android:orientation="horizontal">

            <TextView
                android:layout_width="wrap_content"
                android:layout_height="wrap_content"
                android:text="货物信息："
                android:textColor="#ff666666"
                android:textSize="13dp" />

            <TextView
                android:id="@+id/tvCargoName"
                android:layout_width="wrap_content"
                android:layout_height="wrap_content"
                android:textColor="#ff666666"
                android:textSize="13dp"
                tools:text="方木10吨方木10吨方木10吨方木10吨方木10吨方木10吨" />

        </LinearLayout>

        <LinearLayout
            android:layout_width="match_parent"
            android:layout_height="wrap_content"
            android:layout_marginTop="5dp"
            android:orientation="horizontal">

            <TextView
                android:layout_width="wrap_content"
                android:layout_height="wrap_content"
                android:text="发货单位："
                android:textColor="#ff666666"
                android:textSize="13dp" />

            <TextView
                android:id="@+id/tvCompany1"
                android:layout_width="wrap_content"
                android:layout_height="wrap_content"
                android:textColor="#ff666666"
                android:textSize="13dp"
                tools:text="中储大厦中储大厦中储大厦中储大厦中储大厦" />

        </LinearLayout>

        <LinearLayout
            android:layout_width="match_parent"
            android:layout_height="wrap_content"
            android:layout_marginTop="5dp"
            android:orientation="horizontal">

            <TextView
                android:layout_width="wrap_content"
                android:layout_height="wrap_content"
                android:text="收货单位："
                android:textColor="#ff666666"
                android:textSize="13dp" />

            <TextView
                android:id="@+id/tvCompany2"
                android:layout_width="wrap_content"
                android:layout_height="wrap_content"
                android:textColor="#ff666666"
                android:textSize="13dp"
                tools:text="南京中储智运南京中储智运南京中储智运南京中储智运南京中储智运南京中储智运南京中储智运" />

        </LinearLayout>

        <LinearLayout
            android:layout_width="match_parent"
            android:layout_height="wrap_content"
            android:layout_marginTop="5dp"
            android:orientation="horizontal">

            <TextView
                android:layout_width="wrap_content"
                android:layout_height="wrap_content"
                android:text="车牌号："
                android:textColor="#ff666666"
                android:textSize="13dp" />

            <TextView
                android:id="@+id/tvPlateNumber"
                android:layout_width="wrap_content"
                android:layout_height="wrap_content"
                android:layout_marginStart="15dp"
                android:textColor="#ff666666"
                android:textSize="13dp"
                tools:text="苏A11112" />

        </LinearLayout>

        <LinearLayout
            android:id="@+id/viewException"
            android:layout_width="match_parent"
            android:layout_height="wrap_content"
            android:layout_marginTop="5dp"
            android:orientation="horizontal"
            android:visibility="gone"
            tools:visibility="visible">

            <TextView
                android:layout_width="wrap_content"
                android:layout_height="wrap_content"
                android:text="异常原因："
                android:textColor="#ffff3f3f"
                android:textSize="13dp" />


            <TextView
                android:id="@+id/tvExceptionReason"
                android:layout_width="wrap_content"
                android:layout_height="wrap_content"
                android:text="存在欠款，暂不可申请发票"
                android:textColor="#ffff3f3f"
                android:textSize="13dp" />

        </LinearLayout>

    </LinearLayout>

    <TextView
        android:id="@+id/tvMoney"
        android:layout_width="wrap_content"
        android:layout_height="wrap_content"
        android:gravity="end"
        android:maxWidth="100dp"
        android:textColor="#ffff3f3f"
        android:textSize="13dp"
        app:layout_constraintEnd_toEndOf="parent"
        app:layout_constraintTop_toTopOf="parent"
        tools:text="1000000000元" />

    <TextView
        android:id="@+id/tvRate"
        android:layout_width="wrap_content"
        android:layout_height="wrap_content"
        android:layout_marginTop="5dp"
        android:textColor="#ffff3f3f"
        android:textSize="13dp"
        app:layout_constraintEnd_toEndOf="parent"
        app:layout_constraintTop_toBottomOf="@id/tvMoney"
        tools:text="9%" />

    <TextView
        android:id="@+id/tvDetail"
        android:layout_width="wrap_content"
        android:layout_height="wrap_content"
        android:drawableEnd="@drawable/invoice_arrow_right_black"
        android:drawablePadding="2dp"
        android:text="详情"
        android:textColor="#ff666666"
        android:textSize="14dp"
        app:layout_constraintBottom_toBottomOf="@id/view1"
        app:layout_constraintEnd_toEndOf="parent" />

</androidx.constraintlayout.widget.ConstraintLayout>