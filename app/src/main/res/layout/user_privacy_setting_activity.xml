<?xml version="1.0" encoding="utf-8"?>
<LinearLayout xmlns:android="http://schemas.android.com/apk/res/android"
    xmlns:app="http://schemas.android.com/apk/res-auto"
    xmlns:tools="http://schemas.android.com/tools"
    android:layout_width="match_parent"
    android:layout_height="match_parent"
    android:background="#ffeff0f3"
    android:orientation="vertical">

    <com.zczy.comm.widget.AppToolber
        android:id="@+id/appToolber"
        android:layout_width="match_parent"
        android:layout_height="55dp"
        android:background="#ffffff"
        app:leftIcon="@drawable/base_back_black"
        app:titleColor="#333333"
        app:titleSize="17sp"
        app:titleTxt="隐私设置" />


    <LinearLayout

        android:layout_width="match_parent"
        android:layout_height="43dp"
        android:layout_marginTop="7dp"
        android:background="@color/white"
        android:gravity="center"
        android:paddingLeft="14dp"
        android:paddingRight="14dp"
        app:layout_constraintTop_toBottomOf="@+id/appToolber">

        <TextView
            android:layout_width="wrap_content"
            android:layout_height="wrap_content"
            android:layout_weight="1"
            android:text="开启地理位置定位"
            android:textColor="#ff333333"
            android:textSize="16sp" />

        <TextView
            android:id="@+id/tv_lbs"
            android:layout_width="wrap_content"
            android:gravity="center"
            android:layout_height="match_parent"
            android:drawablePadding="5dp"
            android:drawableRight="@drawable/user_set_jt"
            android:text="去设置"
            android:textColor="#ff666666"
            android:textSize="16sp" />

    </LinearLayout>

    <LinearLayout

        android:layout_width="match_parent"
        android:layout_height="43dp"
        android:layout_marginTop="7dp"
        android:background="@color/white"
        android:gravity="center"
        android:paddingLeft="14dp"
        android:paddingRight="14dp"
        app:layout_constraintTop_toBottomOf="@+id/appToolber">

        <TextView
            android:layout_width="wrap_content"
            android:layout_height="wrap_content"
            android:layout_weight="1"
            android:text="允许访问相机"
            android:textColor="#ff333333"
            android:textSize="16sp" />

        <TextView
            android:id="@+id/tv_caren"
            android:layout_width="wrap_content"
            android:gravity="center"
            android:layout_height="match_parent"
            android:drawablePadding="5dp"
            android:drawableRight="@drawable/user_set_jt"
            android:text="去设置"
            android:textColor="#ff666666"
            android:textSize="16sp" />

    </LinearLayout>
    <LinearLayout

        android:layout_width="match_parent"
        android:layout_height="43dp"
        android:layout_marginTop="7dp"
        android:background="@color/white"
        android:gravity="center"
        android:paddingLeft="14dp"
        android:paddingRight="14dp"
        app:layout_constraintTop_toBottomOf="@+id/appToolber">

        <TextView
            android:layout_width="wrap_content"
            android:layout_height="wrap_content"
            android:layout_weight="1"
            android:text="允许访问电话"
            android:textColor="#ff333333"
            android:textSize="16sp" />

        <TextView
            android:id="@+id/tv_call"
            android:layout_width="wrap_content"
            android:gravity="center"
            android:layout_height="match_parent"
            android:drawablePadding="5dp"
            android:drawableRight="@drawable/user_set_jt"
            android:text="去设置"
            android:textColor="#ff666666"
            android:textSize="16sp" />

    </LinearLayout>
    <LinearLayout

        android:layout_width="match_parent"
        android:layout_height="43dp"
        android:layout_marginTop="7dp"
        android:background="@color/white"
        android:gravity="center"
        android:paddingLeft="14dp"
        android:paddingRight="14dp"
        app:layout_constraintTop_toBottomOf="@+id/appToolber">

        <TextView
            android:layout_width="wrap_content"
            android:layout_height="wrap_content"
            android:layout_weight="1"
            android:text="允许访问存储"
            android:textColor="#ff333333"
            android:textSize="16sp" />

        <TextView
            android:id="@+id/tv_sdcard"
            android:layout_width="wrap_content"
            android:gravity="center"
            android:layout_height="match_parent"
            android:drawablePadding="5dp"
            android:drawableRight="@drawable/user_set_jt"
            android:text="去设置"
            android:textColor="#ff666666"
            android:textSize="16sp" />

    </LinearLayout>

    <LinearLayout
        android:layout_width="match_parent"
        android:layout_height="43dp"
        android:layout_marginTop="7dp"
        android:background="@color/white"
        android:gravity="center"
        android:paddingLeft="14dp"
        android:paddingRight="14dp"
        app:layout_constraintTop_toBottomOf="@+id/appToolber">

        <TextView
            android:layout_width="wrap_content"
            android:layout_height="wrap_content"
            android:layout_weight="1"
            android:text="允许访问通讯录"
            android:textColor="#ff333333"
            android:textSize="16sp" />

        <TextView
            android:id="@+id/tv_txl"
            android:layout_width="wrap_content"
            android:gravity="center"
            android:layout_height="match_parent"
            android:drawablePadding="5dp"
            android:drawableRight="@drawable/user_set_jt"
            android:text="去设置"
            android:textColor="#ff666666"
            android:textSize="16sp" />

    </LinearLayout>

    <LinearLayout
        android:layout_width="match_parent"
        android:layout_height="43dp"
        android:layout_marginTop="7dp"
        android:background="@color/white"
        android:gravity="center"
        android:paddingLeft="14dp"
        android:paddingRight="14dp"
        app:layout_constraintTop_toBottomOf="@+id/appToolber">

        <TextView
            android:layout_width="wrap_content"
            android:layout_height="wrap_content"
            android:layout_weight="1"
            android:text="允许访问短信"
            android:textColor="#ff333333"
            android:textSize="16sp" />

        <TextView
            android:id="@+id/tv_sms"
            android:layout_width="wrap_content"
            android:gravity="center"
            android:layout_height="match_parent"
            android:drawablePadding="5dp"
            android:drawableRight="@drawable/user_set_jt"
            android:text="去设置"
            android:textColor="#ff666666"
            android:textSize="16sp" />

    </LinearLayout>
    <androidx.constraintlayout.widget.ConstraintLayout
        android:id="@+id/ly_push"
        android:layout_width="match_parent"
        android:layout_height="wrap_content"
        android:layout_marginTop="7dp"
        android:background="@color/color_ffffff"
        android:paddingLeft="14dp"
        android:paddingTop="10dp"
        android:paddingRight="14dp"
        android:paddingBottom="10dp"
        android:visibility="gone"
        tools:visibility="visible">

        <TextView
            android:id="@+id/tv_push"
            android:layout_width="wrap_content"
            android:layout_height="wrap_content"
            android:text="个性化推荐"
            android:textColor="@color/color_333333"
            android:textSize="@dimen/text_16"
            app:layout_constraintLeft_toLeftOf="parent"
            app:layout_constraintTop_toTopOf="parent" />


        <TextView
            android:layout_width="wrap_content"
            android:layout_height="wrap_content"
            android:text="提高货源推荐的准确性,保障推荐货源质量，提高找货效率"
            android:textColor="@color/color_999999"
            android:textSize="@dimen/text_12"
            app:layout_constraintLeft_toLeftOf="parent"
            app:layout_constraintTop_toBottomOf="@+id/tv_push" />

        <CheckBox
            android:id="@+id/rb_push"
            android:layout_width="wrap_content"
            android:layout_height="wrap_content"
            android:button="@drawable/user_set_select_on_off"
            android:checked="true"
            app:layout_constraintBottom_toBottomOf="parent"
            app:layout_constraintRight_toRightOf="parent"
            app:layout_constraintTop_toTopOf="parent" />
    </androidx.constraintlayout.widget.ConstraintLayout>

    <LinearLayout
        android:layout_width="match_parent"
        android:layout_height="43dp"
        android:layout_marginTop="7dp"
        android:background="@color/white"
        android:gravity="center"
        android:paddingLeft="14dp"
        android:paddingRight="14dp">

        <TextView
            android:layout_width="wrap_content"
            android:layout_height="wrap_content"
            android:layout_weight="1"
            android:text="开启通知栏信息展示"
            android:textColor="#ff333333"
            android:textSize="16sp" />

        <TextView
            android:id="@+id/tv_notification"
            android:layout_width="wrap_content"
            android:gravity="center"
            android:layout_height="match_parent"
            android:drawablePadding="5dp"
            android:drawableRight="@drawable/user_set_jt"
            android:text="去设置"
            android:textColor="#ff666666"
            android:textSize="16sp" />

    </LinearLayout>
</LinearLayout>