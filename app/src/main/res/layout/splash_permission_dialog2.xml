<?xml version="1.0" encoding="utf-8"?>
<LinearLayout xmlns:android="http://schemas.android.com/apk/res/android"
    xmlns:app="http://schemas.android.com/apk/res-auto"
    android:layout_width="match_parent"
    android:layout_height="match_parent"
    android:background="#ffffff"
    android:gravity="center|top"
    android:orientation="vertical">

    <TextView
        android:id="@+id/tv_title"
        android:layout_width="match_parent"
        android:layout_height="wrap_content"

        android:drawableTop="@drawable/ic_launcher"
        android:drawablePadding="7dp"
        android:gravity="center"
        android:paddingTop="50dp"
        android:paddingBottom="15dp"
        android:text="声明与政策"
        android:textColor="#333333"
        android:textSize="14dp" />

    <androidx.core.widget.NestedScrollView
        android:layout_width="match_parent"
        android:layout_height="wrap_content"
        android:layout_weight="1">

        <TextView
            android:id="@+id/tv_content"
            android:layout_width="match_parent"
            android:layout_height="wrap_content"
            android:padding="20dp"
            android:text="使用设备标识码进行统计、账户安全风控和服务推送等"
            android:textColor="#ff333333"
            android:textSize="12dp" />
    </androidx.core.widget.NestedScrollView>


    <TextView
        android:id="@+id/btn_accept"
        android:layout_width="match_parent"
        android:layout_height="45dp"
        android:layout_marginLeft="20dp"
        android:layout_marginTop="10dp"
        android:layout_marginRight="20dp"
        android:background="@drawable/base_ui_shape_blue_solid_6radius"
        android:gravity="center"
        android:text="同意"
        android:textColor="#ffffff"
        android:textSize="17dp" />

    <TextView
        android:id="@+id/btn_cancel"
        android:layout_width="match_parent"
        android:layout_height="45dp"
        android:layout_marginLeft="20dp"
        android:layout_marginTop="10dp"
        android:layout_marginRight="20dp"
        android:layout_marginBottom="27dp"
        android:background="@drawable/base_ui_selector_blue_or_99_stroke_5radius"
        android:gravity="center"
        android:text="不同意"
        android:textColor="#999999"
        android:textSize="17dp" />
</LinearLayout>
