<?xml version="1.0" encoding="utf-8"?>
<manifest xmlns:android="http://schemas.android.com/apk/res/android"
    xmlns:tools="http://schemas.android.com/tools"
    package="com.zczy.cargo_owner">

    <queries>
        <intent>
            <action android:name="com.getui.sdk.action" />
        </intent>
    </queries>
    <application
        android:name=".AppMainContext"
        android:allowBackup="true"
        android:allowNativeHeapPointerTagging="false"
        android:icon="@drawable/ic_launcher"
        android:label="@string/app_name"
        android:maxAspectRatio="2.4"
        android:persistent="true"
        android:preserveLegacyExternalStorage="true"
        android:requestLegacyExternalStorage="true"
        android:resizeableActivity="true"
        android:supportsRtl="true"
        android:theme="@style/AppTheme"
        android:usesCleartextTraffic="true"
        tools:replace="android:name,android:allowBackup,android:theme">

        >
        <!--用来注册登记 APP 的 AppId 信息-->
        <meta-data
            android:name="ZX_APPID"
            android:value="${ZX_APPID}" />
        <meta-data
            android:name="ScopedStorage"
            android:value="true" />

        <!--禁用Sentry自动初始化-->
        <meta-data
            android:name="io.sentry.auto-init"
            android:value="false" />

        <provider
            android:name="androidx.core.content.FileProvider"
            android:authorities="@string/file_authority"
            android:exported="false"
            android:grantUriPermissions="true">
            <meta-data
                android:name="android.support.FILE_PROVIDER_PATHS"
                android:resource="@xml/file_provider_paths" />
        </provider>
        <receiver
            android:name=".home.onlinecall.MyBroadcastReceiver"
            android:exported="true">
            <intent-filter>
                <action android:name="com.hollycrm.hollyphone.ACTION_LOGIN_ERROR" />
                <action android:name="com.hollycrm.hollyphone.ACTION_CALL_ERROR" />
            </intent-filter>
        </receiver>
        <meta-data
            android:name="com.amap.api.v2.apikey"
            android:value="5b7793a64e48c773481303af20ff94da" /> <!-- 个推 -->
        <!-- 个推 -->
        <service
            android:name="com.zczy.cargo_owner.message.PushIntentService"
            android:enabled="true"
            android:exported="true"
            android:process=":pushservice" />
        <service
            android:name=".message.ZczyHzPushService"
            android:enabled="true"
            android:exported="true"
            android:process=":pushservice" /> <!-- 主页 -->
        <activity
            android:name=".message.ZczyPushHelpActivity"
            android:excludeFromRecents="true"
            android:exported="true"
            android:process=":pushservice"
            android:taskAffinity="com.igexin.sdk.PushActivityTask"
            android:theme="@android:style/Theme.Translucent.NoTitleBar" />

        <activity
            android:name=".splash.MainActivity"
            android:exported="true"
            android:screenOrientation="portrait"
            android:theme="@style/AppSplash">
            <intent-filter>
                <action android:name="android.intent.action.MAIN" />

                <category android:name="android.intent.category.LAUNCHER" />
            </intent-filter>
            <intent-filter>

                <!-- 个推 category 为必须设置项 设置为 android.intent.category.DEFAULT 即可 -->
                <category android:name="android.intent.category.DEFAULT" />

                <action android:name="android.intent.action.oppopush" />
            </intent-filter>
        </activity> <!-- 首页 -->
        <activity
            android:name=".home.HomeActivity"
            android:configChanges="keyboardHidden|orientation|screenSize"
            android:launchMode="singleTask"
            android:screenOrientation="portrait" /> <!-- 保险商城 -->

        <!--子用户管理页-->
        <activity
            android:name=".user.usermanage.SubUserManageActivity"
            android:configChanges="keyboardHidden|orientation|screenSize"
            android:launchMode="singleTask"
            android:screenOrientation="portrait" />

        <!--子用户详情页-->
        <activity
            android:name=".user.usermanage.SubUserDetailActivity"
            android:configChanges="keyboardHidden|orientation|screenSize"
            android:launchMode="singleTask"
            android:screenOrientation="portrait" />


        <!--子用户编辑页-->
        <activity
            android:name=".user.usermanage.SubUserEditActivity"
            android:configChanges="keyboardHidden|orientation|screenSize"
            android:launchMode="singleTask"
            android:screenOrientation="portrait" />

        <activity
            android:name=".splash.WebActivityJuhe"
            android:configChanges="keyboardHidden|orientation|screenSize"
            android:launchMode="singleTask"
            android:screenOrientation="portrait" />
        <activity
            android:name=".user.InsuranceWebActivity"
            android:configChanges="keyboardHidden|orientation|screenSize"
            android:launchMode="singleTask"
            android:screenOrientation="portrait" />
        <activity
            android:name=".splash.LeadActivity"
            android:screenOrientation="portrait"
            android:theme="@android:style/Theme.NoTitleBar.Fullscreen" />
        <activity android:name=".user.setting.PrivacySettingActivity" />
        <activity android:name=".user.setting.UserSettingActivity" />
        <activity
            android:name=".user.setting.UserSwitchAccountActivity"
            android:label="切换账号" />
        <activity
            android:name=".user.setting.UserSwitchAccountLoginActivity"
            android:label="切换账号-账号登录" />
        <activity
            android:name=".user.setting.UserSwitchPhoneLoginActivity"
            android:label="切换账号-手机号登录" />
        <activity android:name=".user.setting.PersonDownLoadActivity" />
        <activity android:name=".user.setting.PersonDownLoadTwoActivity" />
        <activity android:name=".user.setting.safe.UserSafeSettingActivity" />
        <activity android:name=".user.setting.safe.UserEditSafePasswordctivity" />
        <activity android:name=".user.setting.safe.UserEditSafePasswordTwoctivity" />
        <activity android:name=".user.setting.UserEditPasswordctivity" />
        <activity android:name=".user.setting.UserEditPasswordTwoActivity" /> <!-- 账号登录 -->
        <activity android:name=".user.login.activity.LoginAccountActivity" />
        <activity android:name=".user.login.activity.ForgetPassword02Activity" />
        <activity android:name=".user.login.activity.ForgetPassword01Activity" />
        <activity android:name=".user.login.activity.LoginPhoneActivity" />
        <activity android:name=".user.login.activity.RegisterStep1Activity" />
        <activity android:name=".user.login.activity.RegisterStep2Activity" />
        <activity android:name=".user.login.activity.RegisterSuccActivity" />
        <activity android:name=".user.login.activity.UserBindAccountActivity" /> <!-- 登录 -->
        <activity android:name=".user.login.activity.UserRegisterPhoneExitActivity" />
        <activity
            android:name=".wxapi.WXEntryActivity"
            android:exported="true"
            android:launchMode="singleTop"
            android:theme="@android:style/Theme.NoDisplay" /> <!-- 车老板认证 -->
        <activity android:name=".user.certification.HzCertificationAuditActivity" />
        <activity android:name=".user.certification.HzCertificationStartActivity" />
        <activity android:name=".user.certification.HzCertificationFailedActivity" />
        <activity android:name=".user.certification.HzCertificationDeatilsActivity" />
        <activity android:name=".user.certification.HzCertificationRejectActivity" />
        <activity android:name=".user.certification.ExpireInfoActivity" /> <!-- 个人中心  消息 消息列表 页面 -->
        <activity android:name=".message.UserMessageDetailActivity" />
        <activity android:name=".message.UserMessageMainActivityV3" />
        <activity android:name=".message.UserMessageListActivityV3" />
        <activity android:name=".message.UserMessageHistoryActivityV3" /> <!-- 个人中心 问题反馈 -->
        <activity android:name=".tickling.UserTicklingActivity" /> <!-- 个人中心 问题反馈 新增 -->
        <activity android:name=".tickling.UserTicklingAddActivity" /> <!-- 个人中心 问题反馈 选择订单 -->
        <activity android:name=".tickling.UserTicklingChooseOrderActivity" /> <!-- 个人中心 问题反馈 详情 -->
        <activity android:name=".tickling.UserTicklingDetailActivity" /> <!-- 运单异常 -->
        <activity android:name=".user.exception.WaybillProveInfoDetailsActivity" />
        <activity android:name=".user.exception.WaybillExceptionActivity" />
        <activity android:name=".user.exception.WaybillProveInfoSubmitActivity" /> <!-- 评价管理 -->
        <activity
            android:name=".user.evaluate.EvaluateActivityV1"
            android:label="我要评价" />
        <activity
            android:name=".user.evaluate.EvaluateDetailActivityV1"
            android:label="评价详情" />
        <activity android:name=".user.evaluate.EvaluateManagerActivity" />
        <activity android:name=".user.evaluate.EvaluateSearchActivity" />
        <activity android:name=".user.info.UserInfoActivity" />
        <activity android:name=".user.info.UserRefinePhoneActivity" />
        <activity android:name=".user.city.UserCitySearchActivity" />
        <activity android:name=".user.city.UserCityActivity" />
        <activity android:name=".user.info.UserEditPhoneOneActivity" />
        <activity android:name=".user.info.UserEditPhoneTwoActivity" /> <!-- 货物保障服务列表 -->
        <activity android:name=".user.assure.AssureListActivity" /> <!-- 货物保障服务详情 -->
        <activity android:name=".user.assure.AssureDetailActivity" />
        <activity android:name=".user.setting.AboutActivty" /> <!-- 问卷调查 -->
        <activity android:name=".user.questionnaire.QuestionnaireSurveyActivity" />
        <activity android:name=".user.questionnaire.QuestionnaireX5WebActivity" /> <!-- 邀请注册 -->
        <activity android:name=".user.invitation.InvitationRegistrationActivity" />
        <activity android:name=".user.invitation.MyInvitationActivity" />
        <activity android:name=".home.onlinecall.OnLineCallActivity" /> <!-- 客服滿意度 -->
        <activity android:name=".user.satisfaction.SatisfactionEvaluationActionActivity" />
        <activity android:name=".user.satisfaction.SatisfactionEvaluationActivity" />
        <activity android:name=".user.satisfaction.SatisfactionEvaluationDetailActivity" /> <!-- 扫描登录web -->
        <activity android:name=".user.UserScanLoginActivity" />
        <activity
            android:name=".home.fragment.ShareDialogActivity"
            android:launchMode="singleTask"
            android:theme="@style/MyDialogStyleBottom" />
        <activity android:name=".user.integral.UserIntegralWebActivity" />
        <activity android:name=".user.integral.UserIntegralListActivity" />
        <activity android:name=".user.integral.UserIntegralActivity" />
        <activity android:name=".user.integral.UsersIntegralActivity" />
        <activity android:name=".user.setting.ProtocolActivity" />
        <activity android:name=".production.ProductionCapacityActivity" />
        <activity android:name=".production.ProductionCapacityAddEditActivity" />
        <activity android:name=".production.ShippingAddressActivity" />
        <activity android:name=".shipment.ShipmentPlanActivity" />
        <activity android:name=".shipment.ShipmentPlanAddEditActivity" />
        <activity android:name=".coal.CoalNoticeActivity" />
        <activity android:name=".coal.CoalNoticeAddEditActivity" />
        <activity android:name=".cement.CementNoticeActivity" />
        <activity android:name=".cement.CementNoticeAddEditActivity" />
        <activity android:name=".home.view.webpage.WebActivity" />
        <activity
            android:name=".home.dialog.VideoWebView"
            android:configChanges="orientation|screenSize|keyboardHidden"
            android:hardwareAccelerated="true" />
        <activity
            android:name=".home.dialog.X5VideoWebNoToolBarActivity"
            android:configChanges="orientation|screenSize|keyboardHidden"
            android:hardwareAccelerated="true" />
        <activity
            android:name=".user.X5ServiceCenterWebActivity"
            android:label="服务中心" />
        <activity
            android:name=".user.login.activity.UserLoginOtherErrorDialogActivity"
            android:launchMode="singleTask"
            android:theme="@style/MyDialogStyleBottom" />
        <activity
            android:name=".user.login.activity.UserLoginErrorDialogActivity"
            android:launchMode="singleTask"
            android:theme="@style/MyDialogStyleBottom" />
        <activity
            android:name=".home.MoneyPlanDialogActivity"
            android:launchMode="singleTop"
            android:theme="@style/MyDialogStyleBottom" /> <!-- 查看附件 -->
        <activity
            android:name=".user.CarOwnerFileWebviewActivity"
            android:launchMode="singleTask"
            android:screenOrientation="portrait" />

        <activity
            android:name=".offline.OfflineActivity"
            android:launchMode="singleTask"
            android:screenOrientation="portrait" />
        <activity
            android:name=".offline.OfflineWaybillDetailActivity"
            android:launchMode="singleTask"
            android:screenOrientation="portrait" />
        <activity
            android:name=".offline.OfflineSearchActivity"
            android:launchMode="singleTask"
            android:screenOrientation="portrait" />
        <activity
            android:name=".offline.publish.OfflineOrderPublishActivity"
            android:launchMode="singleTask"
            android:screenOrientation="portrait" />
        <activity
            android:name=".offline.OfflineDeliverChooseActivity"
            android:launchMode="singleTask"
            android:screenOrientation="portrait" />
        <activity
            android:name=".offline.publish.DeliverDraftsEditActivityV2"
            android:launchMode="singleTask"
            android:screenOrientation="portrait" />

        <activity android:name=".user.contact.EditContactActivity" />
        <activity android:name=".user.contact.AddContactActivity" />
        <activity android:name=".user.contact.ContactListActivity" />
        <!--        运输计划-->
        <activity android:name=".transport.TransportPlanActivity" />
        <activity android:name=".transport.TransportPlanAdjustmentActivity" />
        <activity android:name=".transport.TransportPlanDetailActivity" />
        <activity android:name=".transport.TransportPlanUpdateActivity" />
        <activity
            android:name=".home.view.HomeMoneyActivity"
            android:launchMode="singleTask"
            android:theme="@style/custom_dialog2" />
        <activity
            android:name=".home.view.TransparentX5WebActivity"
            android:launchMode="singleTask"
            android:theme="@style/TransparentTheme" />

        <receiver
            android:name=".home.MyBroadcastReceiver"
            android:exported="true">
            <intent-filter>
                <action android:name="com.zczy.cargo.OwnerMoney" />
            </intent-filter>
        </receiver>

        <activity
            android:name=".user.driver.DriverBlackAddActivityV1"
            android:label="添加司机黑名单"
            android:launchMode="singleTask"
            android:screenOrientation="portrait" />
        <activity
            android:name=".user.driver.DriverBlackRemoveActivityV1"
            android:label="移除司机黑名单"
            android:launchMode="singleTask"
            android:screenOrientation="portrait" />
        <activity
            android:name=".user.driver.DriverBlackAddActivityV2"
            android:label="添加车辆黑名单"
            android:launchMode="singleTask"
            android:screenOrientation="portrait" />

        <activity
            android:name=".user.driver.DriverBlackRemoveActivityV2"
            android:label="移除车辆黑名单"
            android:launchMode="singleTask"
            android:screenOrientation="portrait" />
        <activity
            android:name=".user.driver.DriverBlackListActivity"
            android:label="黑名单管理"
            android:launchMode="singleTask" />
        <activity
            android:name=".user.driver.DriverBlackRecordListActivity"
            android:label="移除记录"
            android:launchMode="singleTask" />
        <activity
            android:name=".claim.ClaimApplicationManagerActivity"
            android:launchMode="singleTask" />
        <activity
            android:name=".claim.ClaimApplicationManagerAddActivity"
            android:launchMode="singleTask" />
        <activity
            android:name=".claim.ClaimChooseOrderActivity"
            android:launchMode="singleTask" />
        <activity
            android:name=".claim.ClaimApplicationManagerDetailActivity"
            android:launchMode="singleTask" />
        <activity
            android:name=".report.ReportHomeActivity"
            android:label="统计报表"
            android:launchMode="singleTask" />
        <activity
            android:name=".user.overdue.CysExpiredCertificateManagementActivity"
            android:label="证件过期管理"
            android:launchMode="singleTask" />
        <activity
            android:name=".user.overdue.CysExpiredCertificateUpdateActivity"
            android:label="证件过期上传页"
            android:launchMode="singleTask" />
        <activity
            android:name=".user.overdue.ImagePreviewActivityV2"
            android:label="图片 查看大图 界面"
            android:launchMode="singleTask" />
        <activity
            android:name=".defaultapply.DefaultApplyExemptionConfirmActivity"
            android:label="违约申请免确认管理"
            android:launchMode="singleTask" />
        <activity
            android:name=".defaultapply.DefaultApplyExemptionConfirmApplyActivity"
            android:label="违约申请免确认申请"
            android:launchMode="singleTask" />
        <activity
            android:name=".user.coupon.CouponListActivity"
            android:label="优惠券列表"
            android:launchMode="singleTask" />
        <activity
            android:name=".user.coupon.CouponSelectListActivity"
            android:label="优惠券选择列表"
            android:launchMode="singleTask" />
        <activity
            android:name=".user.setting.IcpWebActivity"
            android:launchMode="singleTask" />
        <activity
            android:name=".capacity.CapacityAddActivity"
            android:label="新增原有运力"
            android:launchMode="singleTask" />
        <activity
            android:name=".capacity.CapacityManageActivity"
            android:label="原有运力管理"
            android:launchMode="singleTask" />
        <activity
            android:name=".invoice.activity.InvoiceCenterActivity"
            android:label="发票中心"
            android:launchMode="singleTask" />
        <activity
            android:name=".invoice.activity.InvoiceRecordActivity"
            android:label="申请记录"
            android:launchMode="singleTask" />
        <activity
            android:name=".invoice.activity.InvoiceRecordFilterActivity"
            android:label="申请记录-筛选"
            android:launchMode="singleTask" />
        <activity
            android:name=".invoice.activity.InvoiceRecordDetailActivity"
            android:label="申请详情"
            android:launchMode="singleTask" />
        <activity
            android:name=".invoice.activity.InvoiceManagerActivity"
            android:label="发票管家"
            android:launchMode="singleTask" />
        <activity
            android:name=".invoice.activity.InvoiceManageFilterActivity"
            android:label="发票管家-筛选"
            android:launchMode="singleTask" />
        <activity
            android:name=".invoice.activity.InvoiceMaterialActivity"
            android:label="开票资料"
            android:launchMode="singleTask" />
        <activity
            android:name=".invoice.activity.InvoiceApplyActivity"
            android:label="开票申请"
            android:launchMode="singleTask" />
        <activity
            android:name=".invoice.activity.InvoiceReApplyActivity"
            android:label="开票申请-重新申请"
            android:launchMode="singleTask" />
        <activity
            android:name=".invoice.activity.InvoiceNpsActivity"
            android:label="问卷调查"
            android:launchMode="singleTask" />
        <activity
            android:name=".invoice.activity.InvoiceFilterActivity"
            android:label="开票筛选"
            android:launchMode="singleTask" />
        <activity
            android:name=".invoice.activity.InvoiceReApplyFilterActivity"
            android:label="开票筛选-重新申请开票"
            android:launchMode="singleTask" />
        <activity
            android:name=".invoice.activity.InvoiceWebActivity"
            android:label="发票预览"
            android:launchMode="singleTask" />
        <activity
            android:name=".user.info.UserForceEditPasswordctivity"
            android:label="强制更新密码"
            android:launchMode="singleTask" />

        <!-- 预约指导外呼 -->
        <activity
            android:name=".appointment.AppointmentGuideListActivity"
            android:label="预约指导外呼"
            android:launchMode="singleTask"
            android:screenOrientation="portrait" />
        <activity
            android:name=".appointment.AppointmentGuideAddActivity"
            android:label="新增预约指导外呼"
            android:launchMode="singleTask"
            android:screenOrientation="portrait" />
        <activity
            android:name=".appointment.AppointmentGuideFilterActivity"
            android:label="筛选"
            android:launchMode="singleTask"
            android:screenOrientation="portrait" />

        <!-- 预约指导外呼 -->
        <activity
            android:name=".appointment.AppointmentGuideResultActivity"
            android:label="预约指导外呼"
            android:launchMode="singleTask"
            android:screenOrientation="portrait" />
        <activity
            android:name=".freight.activity.FreightWithDrawActivity"
            android:label="申请提款-多渠道"
            android:launchMode="singleTask" />
        <activity
            android:name=".freight.activity.FreightLoanManagementActivity"
            android:label="放款管理"
            android:launchMode="singleTask" />
        <activity
            android:name=".freight.activity.FreightInformationServiceFeeActivity"
            android:label="信息服务费"
            android:launchMode="singleTask" />
        <activity
            android:name=".freight.activity.FreightIntroduceActivity"
            android:label="产品介绍"
            android:launchMode="singleTask" />
        <activity
            android:name=".freight.activity.FreightZSDrawMonryActivity"
            android:label="到期提醒--浙商"
            android:launchMode="singleTask" />
        <activity
            android:name=".freight.activity.FreightJHDrawMonryActivity"
            android:label="到期提醒--交行"
            android:launchMode="singleTask" />
        <activity
            android:name=".freight.activity.FreightPassHomeActivity"
            android:label="运费通-首页"
            android:launchMode="singleTask" />
        <activity
            android:name=".freight.activity.X5FreightSignWebActivity"
            android:label="签署运费贷合同"
            android:launchMode="singleTask" />
        <activity
            android:name=".freight.activity.FreightBOCAccessActivity"
            android:label="交行准入申请"
            android:launchMode="singleTask" />
        <activity
            android:name=".freight.activity.FreightBOCStateActivity"
            android:label="交行准入申请状态-融资申请状态"
            android:launchMode="singleTask" />
        <activity
            android:name=".freight.activity.FreightBOCFaceImageActivity"
            android:label="交行准入二维码"
            android:launchMode="singleTask" />
        <activity
            android:name=".freight.activity.FreightZSAccessActivity"
            android:label="浙商准入申请"
            android:launchMode="singleTask" />
        <activity
            android:name=".freight.activity.FreightZSFaceImageActivity"
            android:label="浙商准入二维码"
            android:launchMode="singleTask" />
        <activity
            android:name=".freight.activity.FreightZSStateActivity"
            android:label="浙商准入申请状态-融资申请状态"
            android:launchMode="singleTask" />
        <activity
            android:name=".freight.activity.FreightZSPersonListActivity"
            android:label="浙商-操作人列表"
            android:launchMode="singleTask" />
        <activity
            android:name=".freight.activity.FreightZSPersonEditActivity"
            android:label="浙商-操作人修改"
            android:launchMode="singleTask" />
        <activity
            android:name=".freight.activity.FreightZSPersonAddActivity"
            android:label="浙商-操作人新增"
            android:launchMode="singleTask" />
        <activity
            android:name=".user.h5face.X5WebH5FaceActivity"
            android:exported="true"
            android:launchMode="singleTask">
            <intent-filter>
                <action android:name="android.intent.action.VIEW" />

                <category android:name="android.intent.category.DEFAULT" />
                <category android:name="android.intent.category.BROWSABLE" />

                <data
                    android:host="hz"
                    android:path="/contractFace"
                    android:scheme="zczy" />
            </intent-filter>
        </activity>
    </application>

</manifest>